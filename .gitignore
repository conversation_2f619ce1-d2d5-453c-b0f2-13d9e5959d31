# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
build
.next
out
server/public
vite.config.ts.*

# Testing
coverage
.nyc_output
.coverage
playwright-report
test-results

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.sublime-workspace

# Temporary files
.tmp
.temp
.eslintcache
.stylelintcache
*.tsbuildinfo
*.tar.gz

# Database
*.sqlite
*.sqlite3
*.db