import axios from 'axios';

// Create an axios instance with default config
export const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include tenant ID
api.interceptors.request.use((config) => {
  // Get tenant ID from localStorage or other state management
  const tenantId = localStorage.getItem('tenantId');
  
  if (tenantId) {
    config.headers['x-tenant-id'] = tenantId;
  }
  
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle session expiration
    if (error.response?.status === 401) {
      // Redirect to login page or refresh token
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// Tenant Domain API
export const tenantDomainApi = {
  // Get all domains for the current tenant
  getDomains: () => api.get('/tenant/domains'),
  
  // Get a specific domain
  getDomain: (id: string) => api.get(`/tenant/domains/${id}`),
  
  // Register a new domain
  registerDomain: (domain: string, isDefault: boolean = false) => 
    api.post('/tenant/domains', { domain, isDefault }),
  
  // Set up tracking domain
  setupTrackingDomain: (id: string, trackingSubdomain: string) => 
    api.post(`/tenant/domains/${id}/tracking`, { trackingSubdomain }),
  
  // Check verification status
  checkVerificationStatus: (id: string) => 
    api.get(`/tenant/domains/${id}/verification`),
  
  // Check DNS records
  checkDNSRecords: (id: string) => 
    api.get(`/tenant/domains/${id}/dns-check`),
  
  // Rotate webhook secret
  rotateWebhookSecret: (id: string) => 
    api.post(`/tenant/domains/${id}/rotate-secret`),
  
  // Delete a domain
  deleteDomain: (id: string) => 
    api.delete(`/tenant/domains/${id}`)
};

// Email Tracking API
export const emailTrackingApi = {
  // Get all email tracking records
  getTrackingRecords: (params?: { 
    contactId?: string; 
    status?: string; 
    page?: number; 
    limit?: number;
  }) => api.get('/email-tracking', { params }),
  
  // Get a specific tracking record
  getTrackingRecord: (id: string) => 
    api.get(`/email-tracking/${id}`),
  
  // Create a tracking record
  createTracking: (data: {
    contactId?: string;
    sequenceId?: string;
    sequenceStepId?: string;
    messageId: string;
    subject: string;
    recipient: string;
    sender: string;
    trackingEnabled?: boolean;
    linkTrackingEnabled?: boolean;
    attachmentTrackingEnabled?: boolean;
    customFields?: Record<string, any>;
  }) => api.post('/email-tracking', data),
  
  // Generate a reply draft
  generateReplyDraft: (id: string) => 
    api.post(`/email-tracking/${id}/reply-draft`)
};

// Email Config API
export const emailConfigApi = {
  // Get email configuration
  getEmailConfig: () => api.get('/email-config'),
  
  // Save email configuration
  saveEmailConfig: (data: {
    fromName: string;
    fromEmail: string;
    replyTo?: string;
    signature?: string;
    isEnabled: boolean;
    provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses' | 'gmail' | 'outlook' | 'resend' | 'other';
    providerSettings?: Record<string, any>;
  }) => api.post('/email-config', data),
  
  // Test email configuration
  testEmailConfig: (email: string) => 
    api.post('/email-config/test', { email })
};

// Hook for tenant ID
export const useTenantId = () => {
  return localStorage.getItem('tenantId');
};

export default api;
