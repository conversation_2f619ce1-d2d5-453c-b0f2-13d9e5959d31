'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useSubscription, UseSubscriptionResult } from '@/hooks/use-subscription';

// Create the context
const SubscriptionContext = createContext<UseSubscriptionResult | undefined>(undefined);

// Provider component
export function SubscriptionProvider({ children }: { children: ReactNode }) {
  const subscription = useSubscription();
  
  return (
    <SubscriptionContext.Provider value={subscription}>
      {children}
    </SubscriptionContext.Provider>
  );
}

// Hook to use the subscription context
export function useSubscriptionContext(): UseSubscriptionResult {
  const context = useContext(SubscriptionContext);
  
  if (context === undefined) {
    throw new Error('useSubscriptionContext must be used within a SubscriptionProvider');
  }
  
  return context;
}
