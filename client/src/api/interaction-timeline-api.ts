import { apiClient } from './api-client';

// Define interaction type
export interface Interaction {
  id: string;
  type: 'email' | 'call' | 'meeting' | 'chat' | 'social' | 'note' | 'task' | 'other';
  source: string;
  sourceId?: string;
  timestamp: string;
  summary: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  direction?: 'inbound' | 'outbound';
  participants?: Array<{
    id?: string;
    email?: string;
    name?: string;
    role?: string;
  }>;
  content?: {
    text?: string;
    html?: string;
    attachments?: Array<{
      name: string;
      type: string;
      url?: string;
    }>;
  };
  nextAction?: {
    type: 'email' | 'call' | 'meeting' | 'task' | 'other';
    description: string;
    dueDate?: string;
    priority?: 'low' | 'medium' | 'high';
  };
  aiGenerated: boolean;
  aiConfidence?: number;
}

// Define filter options
export interface FilterOptions {
  types?: string[];
  sources?: string[];
  sentiment?: 'positive' | 'neutral' | 'negative';
  search?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  skip?: number;
}

/**
 * Fetch interactions for a contact
 */
export const fetchInteractions = async (
  contactId: string,
  filters: FilterOptions = {}
): Promise<Interaction[]> => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    
    if (filters.limit) queryParams.append('limit', filters.limit.toString());
    if (filters.skip) queryParams.append('skip', filters.skip.toString());
    if (filters.startDate) queryParams.append('startDate', filters.startDate);
    if (filters.endDate) queryParams.append('endDate', filters.endDate);
    if (filters.types && filters.types.length > 0) queryParams.append('types', filters.types.join(','));
    if (filters.sources && filters.sources.length > 0) queryParams.append('sources', filters.sources.join(','));
    if (filters.sentiment) queryParams.append('sentiment', filters.sentiment);
    if (filters.search) queryParams.append('search', filters.search);
    
    const queryString = queryParams.toString();
    const url = `/contacts/${contactId}/interactions${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching interactions:', error);
    throw error;
  }
};

/**
 * Add a new interaction
 */
export const addInteraction = async (
  contactId: string,
  interaction: Omit<Interaction, 'id'>
): Promise<Interaction> => {
  try {
    const response = await apiClient.post(`/contacts/${contactId}/interactions`, interaction);
    return response.data;
  } catch (error) {
    console.error('Error adding interaction:', error);
    throw error;
  }
};

/**
 * Update an existing interaction
 */
export const updateInteraction = async (
  contactId: string,
  interactionId: string,
  updates: Partial<Interaction>
): Promise<Interaction> => {
  try {
    const response = await apiClient.put(`/contacts/${contactId}/interactions/${interactionId}`, updates);
    return response.data;
  } catch (error) {
    console.error('Error updating interaction:', error);
    throw error;
  }
};

/**
 * Delete an interaction
 */
export const deleteInteraction = async (
  contactId: string,
  interactionId: string
): Promise<void> => {
  try {
    await apiClient.delete(`/contacts/${contactId}/interactions/${interactionId}`);
  } catch (error) {
    console.error('Error deleting interaction:', error);
    throw error;
  }
};

/**
 * Sync interactions from external sources
 */
export const syncInteractions = async (
  contactId: string,
  options: {
    sources?: ('email' | 'calendar' | 'telephony' | 'social')[];
    startDate?: string;
    endDate?: string;
    limit?: number;
  } = {}
): Promise<{ added: number; updated: number; errors: number }> => {
  try {
    const response = await apiClient.post(`/contacts/${contactId}/interactions/sync`, options);
    return response.data;
  } catch (error) {
    console.error('Error syncing interactions:', error);
    throw error;
  }
};
