import { apiClient } from './api-client';

/**
 * Interface for Chart Specification
 */
export interface ChartSpec {
  type: string;
  data: any;
  options: Record<string, any>;
  title?: string;
  subtitle?: string;
  interactive?: boolean;
  drilldown?: boolean;
}

/**
 * Interface for Module Integration
 */
export interface ModuleIntegration {
  module: 'workflow' | 'insights' | 'proposal_generator' | 'objection_handler' | 'follow_up_coach' | 'win_loss_analyzer' | 'meeting_prep';
  action: string;
  params: Record<string, any>;
}

/**
 * Interface for Recommended Play
 */
export interface RecommendedPlay {
  id?: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  workflow_template?: string;
  action_url?: string;
  module_integration?: ModuleIntegration;
}

/**
 * Interface for Insight
 */
export interface Insight {
  _id: string;
  tenant_id: string;
  question: string;
  question_hash: string;
  dataset_ref: string;
  narrative: string;
  chart_spec: ChartSpec;
  why_it_matters: string[];
  recommended_plays: RecommendedPlay[];
  sql_query?: string;
  graph_query?: string;
  feature_weights?: Record<string, number>;
  created_at: string;
  updated_at: string;
  expires_at: string;
  refresh_count: number;
  last_refresh_at?: string;
  feedback?: {
    helpful: boolean;
    comment?: string;
    submitted_by: string;
    submitted_at: string;
  }[];
}

/**
 * Interface for Generate Insight Request
 */
export interface GenerateInsightRequest {
  question: string;
  refresh?: boolean;
  filters?: Record<string, any>;
}

/**
 * Interface for Generate Insight Response
 */
export interface GenerateInsightResponse {
  success: boolean;
  insight?: Insight;
  error?: string;
  source: 'cache' | 'ai_service' | 'voyage_fallback' | 'error';
}

/**
 * Interface for Submit Feedback Request
 */
export interface SubmitFeedbackRequest {
  helpful: boolean;
  comment?: string;
}

/**
 * Generate insight from natural language question
 */
export const generateInsight = async (data: GenerateInsightRequest): Promise<GenerateInsightResponse> => {
  const response = await apiClient.post<GenerateInsightResponse>('/api/insights/generate', data);
  return response.data;
};

/**
 * Get cached insights
 */
export const getCachedInsights = async (options: {
  limit?: number;
  skip?: number;
} = {}): Promise<{ insights: Insight[]; total: number }> => {
  const { limit, skip } = options;

  const params: Record<string, any> = {};
  if (limit) params.limit = limit;
  if (skip) params.skip = skip;

  const response = await apiClient.get<{ insights: Insight[]; total: number }>('/api/insights/cached', { params });
  return response.data;
};

/**
 * Get insight by ID
 */
export const getInsight = async (id: string): Promise<Insight> => {
  const response = await apiClient.get<Insight>(`/api/insights/${id}`);
  return response.data;
};

/**
 * Submit feedback for an insight
 */
export const submitFeedback = async (id: string, data: SubmitFeedbackRequest): Promise<Insight> => {
  const response = await apiClient.post<Insight>(`/api/insights/${id}/feedback`, data);
  return response.data;
};
