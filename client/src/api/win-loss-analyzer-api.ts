import { apiRequest } from '../api/api-utils';

/**
 * Get all win/loss analyses
 */
export async function getWinLossAnalyses(params: {
  outcome?: 'won' | 'lost';
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  startDate?: Date;
  endDate?: Date;
  minValue?: number;
  maxValue?: number;
  search?: string;
} = {}) {
  // Convert dates to ISO strings for API
  const apiParams: Record<string, any> = { ...params };

  if (apiParams.startDate instanceof Date) {
    apiParams.startDate = apiParams.startDate.toISOString();
  }

  if (apiParams.endDate instanceof Date) {
    apiParams.endDate = apiParams.endDate.toISOString();
  }

  return apiRequest({
    url: '/api/win-loss/analyses',
    method: 'GET',
    data: apiParams
  });
}

/**
 * Get a win/loss analysis by ID
 */
export async function getWinLossAnalysisById(id: string) {
  return apiRequest({
    url: `/api/win-loss/analyses/${id}`,
    method: 'GET'
  });
}

/**
 * Create a new win/loss analysis
 */
export async function createWinLossAnalysis(data: {
  title: string;
  description?: string;
  opportunityId: string;
  contactId?: string;
  companyId?: string;
  outcome: 'won' | 'lost';
  value: number;
  currency: string;
  closedDate: Date;
  stage: string;
  salesCycle: number;
  keyFactors: Array<{
    factor: string;
    impact: 'positive' | 'negative';
    weight: number;
    description?: string;
  }>;
  competitorInfo?: {
    name?: string;
    strengths?: string[];
    weaknesses?: string[];
    pricingDifference?: number;
  };
  feedback?: string;
  learnings: string[];
  recommendations: string[];
  isAIGenerated?: boolean;
  tags?: string[];
  customFields?: Record<string, any>;
}) {
  // Convert dates to ISO strings
  const apiData: Record<string, any> = { ...data };

  if (apiData.closedDate instanceof Date) {
    apiData.closedDate = apiData.closedDate.toISOString();
  }

  return apiRequest({
    url: '/api/win-loss/analyses',
    method: 'POST',
    data: apiData
  });
}

/**
 * Update a win/loss analysis
 */
export async function updateWinLossAnalysis(
  id: string,
  data: {
    title?: string;
    description?: string;
    outcome?: 'won' | 'lost';
    value?: number;
    currency?: string;
    closedDate?: Date;
    stage?: string;
    salesCycle?: number;
    keyFactors?: Array<{
      factor: string;
      impact: 'positive' | 'negative';
      weight: number;
      description?: string;
    }>;
    competitorInfo?: {
      name?: string;
      strengths?: string[];
      weaknesses?: string[];
      pricingDifference?: number;
    };
    feedback?: string;
    learnings?: string[];
    recommendations?: string[];
    tags?: string[];
    customFields?: Record<string, any>;
  }
) {
  // Convert dates to ISO strings if present
  const apiData: Record<string, any> = { ...data };

  if (apiData.closedDate instanceof Date) {
    apiData.closedDate = apiData.closedDate.toISOString();
  }

  return apiRequest({
    url: `/api/win-loss/analyses/${id}`,
    method: 'PUT',
    data: apiData
  });
}

/**
 * Delete a win/loss analysis
 */
export async function deleteWinLossAnalysis(id: string) {
  return apiRequest({
    url: `/api/win-loss/analyses/${id}`,
    method: 'DELETE'
  });
}

/**
 * Generate a win/loss analysis for an opportunity
 */
export async function generateWinLossAnalysis(opportunityId: string) {
  return apiRequest({
    url: '/api/win-loss/analyses/generate',
    method: 'POST',
    data: { opportunityId }
  });
}

/**
 * Get win/loss factors
 */
export async function getWinLossFactors(params: {
  category?: string;
  impact?: 'positive' | 'negative' | 'neutral';
  isActive?: boolean;
  search?: string;
} = {}) {
  return apiRequest({
    url: '/api/win-loss/factors',
    method: 'GET',
    data: params
  });
}

/**
 * Get win/loss statistics
 */
export async function getWinLossStatistics(params: {
  startDate?: Date;
  endDate?: Date;
  companyId?: string;
} = {}) {
  // Convert dates to ISO strings for API
  const apiParams: Record<string, any> = { ...params };

  if (apiParams.startDate instanceof Date) {
    apiParams.startDate = apiParams.startDate.toISOString();
  }

  if (apiParams.endDate instanceof Date) {
    apiParams.endDate = apiParams.endDate.toISOString();
  }

  return apiRequest({
    url: '/api/win-loss/statistics',
    method: 'GET',
    data: apiParams
  });
}
