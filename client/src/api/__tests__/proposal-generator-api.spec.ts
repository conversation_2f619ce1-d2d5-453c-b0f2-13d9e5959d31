import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  generateProposalWithAI, 
  generateProposalDocument,
  createProposalShareableLink,
  exportProposalToEmail,
  exportProposalToSocial,
  updateProposalClauses,
  getProposalAnalytics
} from '../proposal-generator-api';
import { apiRequest } from '../api-utils';

// Mock the apiRequest function
vi.mock('../api-utils', () => ({
  apiRequest: vi.fn()
}));

describe('proposal-generator-api', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
    
    // Default mock implementation
    (apiRequest as any).mockResolvedValue({});
  });

  describe('generateProposalWithAI', () => {
    it('calls the correct endpoint with correct data', async () => {
      const options = {
        prompt: 'Create a proposal for a new website',
        model: 'claude-3-opus-20240229',
        opportunityId: 'opp123',
        companyId: 'comp456',
        contactIds: ['contact789'],
        includeSections: {
          executiveSummary: true,
          solution: true,
          timeline: true,
          pricing: true,
          team: false,
          testimonials: false,
          terms: true
        }
      };

      await generateProposalWithAI(options);

      expect(apiRequest).toHaveBeenCalledWith({
        url: '/api/proposals/ai/generate',
        method: 'POST',
        data: options
      });
    });
  });

  describe('generateProposalDocument', () => {
    it('calls the correct endpoint with correct data', async () => {
      const id = 'proposal123';
      const options = {
        format: 'pdf' as const,
        includeCompanyLogo: true,
        includeSignaturePage: true,
        includeAttachments: true
      };

      await generateProposalDocument(id, options);

      expect(apiRequest).toHaveBeenCalledWith({
        url: `/api/proposals/${id}/document`,
        method: 'POST',
        data: options
      });
    });
  });

  describe('createProposalShareableLink', () => {
    it('calls the correct endpoint with correct data', async () => {
      const id = 'proposal123';
      const options = {
        expiresAt: new Date('2023-12-31')
      };

      await createProposalShareableLink(id, options);

      expect(apiRequest).toHaveBeenCalledWith({
        url: `/api/proposals/${id}/share`,
        method: 'POST',
        data: { proposalId: id, ...options }
      });
    });

    it('works with default options', async () => {
      const id = 'proposal123';

      await createProposalShareableLink(id);

      expect(apiRequest).toHaveBeenCalledWith({
        url: `/api/proposals/${id}/share`,
        method: 'POST',
        data: { proposalId: id }
      });
    });
  });

  describe('exportProposalToEmail', () => {
    it('calls the correct endpoint with correct data', async () => {
      const id = 'proposal123';
      const options = {
        to: ['<EMAIL>'],
        cc: ['<EMAIL>'],
        subject: 'Your Proposal',
        message: 'Please review the attached proposal',
        format: 'pdf' as const
      };

      await exportProposalToEmail(id, options);

      expect(apiRequest).toHaveBeenCalledWith({
        url: `/api/proposals/${id}/export/email`,
        method: 'POST',
        data: { proposalId: id, ...options }
      });
    });
  });

  describe('exportProposalToSocial', () => {
    it('calls the correct endpoint with correct data', async () => {
      const id = 'proposal123';
      const options = {
        platform: 'linkedin' as const,
        message: 'Check out our new proposal!'
      };

      await exportProposalToSocial(id, options);

      expect(apiRequest).toHaveBeenCalledWith({
        url: `/api/proposals/${id}/export/social`,
        method: 'POST',
        data: { proposalId: id, ...options }
      });
    });
  });

  describe('updateProposalClauses', () => {
    it('calls the correct endpoint with correct data', async () => {
      const id = 'proposal123';
      const clauses = ['clause1', 'clause2', 'clause3'];

      await updateProposalClauses(id, clauses);

      expect(apiRequest).toHaveBeenCalledWith({
        url: `/api/proposals/${id}/clauses`,
        method: 'PUT',
        data: { proposalId: id, clauses }
      });
    });
  });

  describe('getProposalAnalytics', () => {
    it('calls the correct endpoint', async () => {
      const id = 'proposal123';

      await getProposalAnalytics(id);

      expect(apiRequest).toHaveBeenCalledWith({
        url: `/api/proposals/${id}/analytics`,
        method: 'GET'
      });
    });
  });
});
