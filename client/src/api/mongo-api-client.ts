/**
 * MongoDB API Client
 *
 * This client provides methods for interacting with the MongoDB-backed API endpoints.
 * It handles CRUD operations for contacts, companies, opportunities, and other entities.
 */

import axios from 'axios';
import {
  Contact,
  Company,
  Opportunity,
  Activity,
  Proposal
} from '@/types/core';

// Define interfaces for objection handler responses
interface ObjectionHandlerResponse {
  objectionClass: string;
  confidence: number;
  responses: Array<{
    id: string;
    text: string;
    confidence: number;
    evidence?: Array<{
      id: string;
      title: string;
      type: string;
      description: string;
      url?: string;
    }>;
  }>;
}

interface RealTimeObjectionResponse {
  responseId: string;
  objectionClass: string;
  response: string;
  rebuttals: Array<{
    id: string;
    text: string;
    winRate: number;
    evidence?: string[];
  }>;
  supportingEvidence: Array<{
    id: string;
    title: string;
    type: string;
    description: string;
    url?: string;
  }>;
}

interface VoiceObjectionResponse {
  transcription: string;
  response: RealTimeObjectionResponse;
}

// Define interfaces for proposal generator responses
interface ProposalAIGenerationResponse {
  id: string;
  title: string;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  aiModel: string;
  aiConfidence: number;
  generatedAt: Date;
}

interface ProposalDocumentGenerationResponse {
  documentId: string;
  url: string;
  format: string;
  expiresAt: Date;
  size?: number;
}

interface ProposalShareResponse {
  id: string;
  proposalId: string;
  token: string;
  url: string;
  expiresAt?: Date;
  createdBy: string;
  createdAt: Date;
  accessCount: number;
  lastAccessedAt?: Date;
}

interface ProposalAnalytics {
  proposalId: string;
  views: number;
  uniqueViews: number;
  downloads: number;
  shares: number;
  averageViewDuration: number;
  events: Array<{
    id: string;
    type: 'view' | 'download' | 'share' | 'accept' | 'reject' | 'comment';
    timestamp: string;
    data?: Record<string, any>;
  }>;
}

// Base API URL
const API_BASE_URL = '/api';

// Create axios instance with common configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Contacts API
const contactsApi = {
  getAll: async (): Promise<Contact[]> => {
    const response = await apiClient.get('/contacts');
    return response.data.contacts;
  },

  getById: async (id: string): Promise<Contact> => {
    const response = await apiClient.get(`/contacts/${id}`);
    return response.data.contact;
  },

  create: async (data: Partial<Contact>): Promise<Contact> => {
    const response = await apiClient.post('/contacts', data);
    return response.data.contact;
  },

  update: async (id: string, data: Partial<Contact>): Promise<Contact> => {
    const response = await apiClient.patch(`/contacts/${id}`, data);
    return response.data.contact;
  },

  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/contacts/${id}`);
  },
};

// Companies API
const companiesApi = {
  getAll: async (): Promise<Company[]> => {
    const response = await apiClient.get('/companies');
    return response.data.companies;
  },

  getById: async (id: string): Promise<Company> => {
    const response = await apiClient.get(`/companies/${id}`);
    return response.data.company;
  },

  create: async (data: Partial<Company>): Promise<Company> => {
    const response = await apiClient.post('/companies', data);
    return response.data.company;
  },

  update: async (id: string, data: Partial<Company>): Promise<Company> => {
    const response = await apiClient.patch(`/companies/${id}`, data);
    return response.data.company;
  },

  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/companies/${id}`);
  },
};

// Opportunities API
const opportunitiesApi = {
  getAll: async (): Promise<Opportunity[]> => {
    const response = await apiClient.get('/opportunities');
    return response.data.opportunities;
  },

  getById: async (id: string): Promise<Opportunity> => {
    const response = await apiClient.get(`/opportunities/${id}`);
    return response.data.opportunity;
  },

  getByContactId: async (contactId: string): Promise<Opportunity[]> => {
    const response = await apiClient.get(`/opportunities?contactId=${contactId}`);
    return response.data.opportunities;
  },

  getByCompanyId: async (companyId: string): Promise<Opportunity[]> => {
    const response = await apiClient.get(`/opportunities?companyId=${companyId}`);
    return response.data.opportunities;
  },

  create: async (data: Partial<Opportunity>): Promise<Opportunity> => {
    const response = await apiClient.post('/opportunities', data);
    return response.data.opportunity;
  },

  update: async (id: string, data: Partial<Opportunity>): Promise<Opportunity> => {
    const response = await apiClient.patch(`/opportunities/${id}`, data);
    return response.data.opportunity;
  },

  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/opportunities/${id}`);
  },
};

// Activities API
const activitiesApi = {
  getAll: async (): Promise<Activity[]> => {
    const response = await apiClient.get('/activities');
    return response.data.activities;
  },

  getById: async (id: string): Promise<Activity> => {
    const response = await apiClient.get(`/activities/${id}`);
    return response.data.activity;
  },

  getByContactId: async (contactId: string): Promise<Activity[]> => {
    const response = await apiClient.get(`/activities?contactId=${contactId}`);
    return response.data.activities;
  },

  create: async (data: Partial<Activity>): Promise<Activity> => {
    const response = await apiClient.post('/activities', data);
    return response.data.activity;
  },

  update: async (id: string, data: Partial<Activity>): Promise<Activity> => {
    const response = await apiClient.patch(`/activities/${id}`, data);
    return response.data.activity;
  },

  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/activities/${id}`);
  },
};

// Proposals API
const proposalsApi = {
  getAll: async (): Promise<Proposal[]> => {
    const response = await apiClient.get('/proposals');
    return response.data.proposals;
  },

  getById: async (id: string): Promise<Proposal> => {
    const response = await apiClient.get(`/proposals/${id}`);
    return response.data.proposal;
  },

  getByOpportunityId: async (opportunityId: string): Promise<Proposal[]> => {
    const response = await apiClient.get(`/proposals?opportunityId=${opportunityId}`);
    return response.data.proposals;
  },

  create: async (data: Partial<Proposal>): Promise<Proposal> => {
    const response = await apiClient.post('/proposals', data);
    return response.data.proposal;
  },

  update: async (id: string, data: Partial<Proposal>): Promise<Proposal> => {
    const response = await apiClient.patch(`/proposals/${id}`, data);
    return response.data.proposal;
  },

  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/proposals/${id}`);
  },
};

// Contacts API - Additional methods
const contactsApiExtended = {
  ...contactsApi,

  generatePersona: async (contactId: string): Promise<any> => {
    const response = await apiClient.post(`/contacts/${contactId}/persona`);
    return response.data;
  },

  enrich: async (contactId: string): Promise<any> => {
    const response = await apiClient.post(`/contacts/${contactId}/enrich`);
    return response.data;
  }
};

// Opportunities API - Additional methods
const opportunitiesApiExtended = {
  ...opportunitiesApi,

  getDealBrief: async (opportunityId: string): Promise<any> => {
    const response = await apiClient.get(`/opportunities/${opportunityId}/deal-brief`);
    return response.data;
  },

  getStageAnalysis: async (opportunityId: string): Promise<any> => {
    const response = await apiClient.get(`/opportunities/${opportunityId}/stage-analysis`);
    return response.data;
  },

  getStageTransitions: async (opportunityId: string): Promise<any> => {
    const response = await apiClient.get(`/opportunities/${opportunityId}/stage-transitions`);
    return response.data;
  }
};

// Pipeline API
const pipelineApi = {
  getOpportunityInsights: async (opportunityId: string): Promise<any> => {
    const response = await apiClient.get(`/pipeline/opportunities/${opportunityId}/insights`);
    return response.data;
  },

  runChecks: async (type: 'all' | 'opportunities' | 'contacts'): Promise<any> => {
    const response = await apiClient.post(`/pipeline/run-checks`, { type });
    return response.data;
  }
};

// Insights API
const insightsApi = {
  markAsRead: async (insightId: string): Promise<any> => {
    const response = await apiClient.post(`/insights/${insightId}/mark-read`);
    return response.data;
  }
};

// Stage Transitions API
const stageTransitionsApi = {
  applyTransition: async (transitionId: string, approved: boolean): Promise<any> => {
    const response = await apiClient.post(`/stage-transitions/${transitionId}/apply`, { approved });
    return response.data;
  },

  analyzeAll: async (autoApprove: boolean): Promise<any> => {
    const response = await apiClient.post(`/stage-transitions/analyze-all`, { autoApprove });
    return response.data;
  }
};

// Objection Handler API
const objectionHandlerApi = {
  handleObjection: async (objectionText: string, options?: any): Promise<ObjectionHandlerResponse> => {
    const response = await apiClient.post('/objection-handler', {
      objectionText,
      ...options
    });
    return response.data.response;
  },

  handleRealTimeObjection: async (objectionText: string, options?: any): Promise<RealTimeObjectionResponse> => {
    const response = await apiClient.post('/objection-handler/real-time', {
      objectionText,
      ...options
    });
    return response.data.response;
  },

  handleVoiceObjection: async (audioData: Blob, options?: any): Promise<VoiceObjectionResponse> => {
    const formData = new FormData();
    formData.append('audio', audioData);

    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    const response = await apiClient.post('/objection-handler/voice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data.response;
  }
};

// Proposal Generator API
const proposalGeneratorApi = {
  generateProposal: async (opportunityId: string, options?: any): Promise<Proposal> => {
    const response = await apiClient.post('/proposal-generator', {
      opportunityId,
      ...options
    });
    return response.data.proposal;
  },

  generateProposalWithAI: async (options: any): Promise<ProposalAIGenerationResponse> => {
    const response = await apiClient.post('/proposal-generator/ai', options);
    return response.data.proposal;
  },

  generateProposalDocument: async (proposalId: string, format: string, options?: any): Promise<ProposalDocumentGenerationResponse> => {
    const response = await apiClient.post(`/proposal-generator/${proposalId}/document`, {
      format,
      options
    });
    return response.data.document;
  },

  shareProposal: async (proposalId: string, options?: any): Promise<ProposalShareResponse> => {
    const response = await apiClient.post(`/proposal-generator/${proposalId}/share`, options);
    return response.data.share;
  },

  getProposalAnalytics: async (proposalId: string, options?: any): Promise<ProposalAnalytics> => {
    const response = await apiClient.get(`/proposal-generator/${proposalId}/analytics`, {
      params: options
    });
    return response.data;
  }
};

import { interactionsApi } from './interactions-api';

// Export the MongoDB API client
export const mongoApiClient = {
  contacts: contactsApiExtended,
  companies: companiesApi,
  opportunities: opportunitiesApiExtended,
  activities: activitiesApi,
  proposals: proposalsApi,
  pipeline: pipelineApi,
  insights: insightsApi,
  stageTransitions: stageTransitionsApi,
  objectionHandler: objectionHandlerApi,
  proposalGenerator: proposalGeneratorApi,
  interactions: interactionsApi
};
