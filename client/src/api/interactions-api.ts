/**
 * Interactions API client
 */
import axios from 'axios';
import type { Interaction, NextAction, InteractionInsights } from '@/components/smart-interaction-timeline/SmartInteractionTimeline.d';

// Create axios instance
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to include credentials
api.interceptors.request.use(config => {
  config.withCredentials = true;
  return config;
});

/**
 * Interactions API client
 */
export const interactionsApi = {
  /**
   * Get all interactions for a contact
   */
  getAll: async (contactId: string, params?: { limit?: number, offset?: number }) => {
    const response = await api.get(`/contacts/${contactId}/interactions`, { params });
    return response.data;
  },

  /**
   * Get interaction by ID
   */
  getById: async (contactId: string, interactionId: string) => {
    const response = await api.get(`/contacts/${contactId}/interactions/${interactionId}`);
    return response.data;
  },

  /**
   * Create a new interaction
   */
  create: async (contactId: string, interactionData: Partial<Interaction>) => {
    const response = await api.post(`/contacts/${contactId}/interactions`, interactionData);
    return response.data;
  },

  /**
   * Update an interaction
   */
  update: async (contactId: string, interactionId: string, interactionData: Partial<Interaction>) => {
    const response = await api.patch(`/contacts/${contactId}/interactions/${interactionId}`, interactionData);
    return response.data;
  },

  /**
   * Delete an interaction
   */
  delete: async (contactId: string, interactionId: string) => {
    const response = await api.delete(`/contacts/${contactId}/interactions/${interactionId}`);
    return response.data;
  },

  /**
   * Generate a summary for an interaction
   */
  generateSummary: async (content: string) => {
    const response = await api.post('/interactions/generate-summary', { content });
    return response.data;
  },

  /**
   * Get insights for a contact
   */
  getInsights: async (contactId: string) => {
    const response = await api.get(`/contacts/${contactId}/interactions/insights`);
    return response.data as InteractionInsights;
  },

  /**
   * Generate insights for a contact
   */
  generateInsights: async (contactId: string) => {
    const response = await api.post(`/contacts/${contactId}/interactions/generate-insights`);
    return response.data as InteractionInsights;
  },

  /**
   * Update next action for an interaction
   */
  updateNextAction: async (interactionId: string, nextAction: Partial<NextAction>) => {
    const response = await api.patch(`/interactions/${interactionId}/next-action`, nextAction);
    return response.data;
  },

  /**
   * Generate next actions for a contact
   */
  generateNextActions: async (contactId: string) => {
    const response = await api.post(`/contacts/${contactId}/interactions/generate-next-actions`);
    return response.data;
  }
};
