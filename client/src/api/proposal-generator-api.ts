import { apiRequest } from '../api/api-utils';
import {
  ProposalTemplate,
  ProposalSection,
  Proposal,
  ProposalGenerationRequest,
  ProposalGenerationResponse,
  ProposalExportFormat,
  ProposalExportRequest,
  ProposalExportResponse,
  ProposalShareRequest,
  ProposalShareResponse,
  ProposalEmailRequest,
  ProposalEmailResponse,
  ProposalSocialExportRequest,
  ProposalSocialExportResponse,
  ProposalClausesUpdateRequest,
  ProposalClausesUpdateResponse,
  IndustrySpecificClause,
  ProposalAIGenerationOptions,
  ProposalAIGenerationResponse,
  ProposalDownloadOptions,
  ProposalAnalytics
} from '@shared/types';

/**
 * Get all proposal templates
 */
export async function getProposalTemplates(params: {
  category?: string;
  isDefault?: boolean;
  createdBy?: string;
  search?: string;
} = {}) {
  return apiRequest({
    url: '/api/proposal-templates',
    method: 'GET',
    data: params
  });
}

/**
 * Get proposal template by ID
 */
export async function getProposalTemplateById(id: string) {
  return apiRequest({
    url: `/api/proposal-templates/${id}`,
    method: 'GET'
  });
}

/**
 * Create a new proposal template
 */
export async function createProposalTemplate(data: {
  name: string;
  description?: string;
  category: string;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    isRequired: boolean;
    order: number;
  }>;
  variables?: string[];
  tags?: string[];
  isDefault?: boolean;
  customFields?: Record<string, any>;
}) {
  return apiRequest({
    url: '/api/proposal-templates',
    method: 'POST',
    data
  });
}

/**
 * Update a proposal template
 */
export async function updateProposalTemplate(
  id: string,
  data: {
    name?: string;
    description?: string;
    category?: string;
    content?: string;
    sections?: Array<{
      id: string;
      name: string;
      type: string;
      content: string;
      isRequired: boolean;
      order: number;
    }>;
    variables?: string[];
    tags?: string[];
    isDefault?: boolean;
    customFields?: Record<string, any>;
  }
) {
  return apiRequest({
    url: `/api/proposal-templates/${id}`,
    method: 'PUT',
    data
  });
}

/**
 * Delete a proposal template
 */
export async function deleteProposalTemplate(id: string) {
  return apiRequest({
    url: `/api/proposal-templates/${id}`,
    method: 'DELETE'
  });
}

/**
 * Record template usage
 */
export async function recordTemplateUsage(
  id: string,
  wasSuccessful: boolean = false
) {
  return apiRequest({
    url: `/api/proposal-templates/${id}/usage`,
    method: 'POST',
    data: { wasSuccessful }
  });
}

/**
 * Get all proposals
 */
export async function getProposals(params: {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  status?: string;
  createdBy?: string;
  search?: string;
} = {}) {
  return apiRequest({
    url: '/api/proposals',
    method: 'GET',
    data: params
  });
}

/**
 * Get proposal by ID
 */
export async function getProposalById(id: string) {
  return apiRequest({
    url: `/api/proposals/${id}`,
    method: 'GET'
  });
}

/**
 * Create a new proposal
 */
export async function createProposal(data: {
  name: string;
  description?: string;
  opportunityId: string;
  contactId?: string;
  companyId?: string;
  templateId?: string;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  value: number;
  currency?: string;
  validUntil?: Date;
  tags?: string[];
  notes?: string;
  customFields?: Record<string, any>;
}) {
  return apiRequest({
    url: '/api/proposals',
    method: 'POST',
    data
  });
}

/**
 * Update a proposal
 */
export async function updateProposal(
  id: string,
  data: {
    name?: string;
    description?: string;
    content?: string;
    sections?: Array<{
      id: string;
      name: string;
      type: string;
      content: string;
      order: number;
    }>;
    status?: string;
    value?: number;
    currency?: string;
    validUntil?: Date;
    sentAt?: Date;
    viewedAt?: Date;
    acceptedAt?: Date;
    rejectedAt?: Date;
    documentUrl?: string;
    documentId?: string;
    tags?: string[];
    notes?: string;
    customFields?: Record<string, any>;
  }
) {
  return apiRequest({
    url: `/api/proposals/${id}`,
    method: 'PUT',
    data
  });
}

/**
 * Delete a proposal
 */
export async function deleteProposal(id: string) {
  return apiRequest({
    url: `/api/proposals/${id}`,
    method: 'DELETE'
  });
}

/**
 * Generate a proposal using AI
 */
export async function generateProposal(params: ProposalGenerationRequest): Promise<ProposalGenerationResponse> {
  return apiRequest({
    url: '/api/proposals/generate',
    method: 'POST',
    data: params
  });
}

/**
 * Generate a document from a proposal in the specified format
 */
export async function generateProposalDocument(
  id: string,
  options: ProposalDownloadOptions
): Promise<ProposalExportResponse> {
  return apiRequest({
    url: `/api/proposals/${id}/document`,
    method: 'POST',
    data: options
  });
}

/**
 * Send a proposal to the client
 */
export async function sendProposal(id: string) {
  return apiRequest({
    url: `/api/proposals/${id}/send`,
    method: 'POST'
  });
}

/**
 * Mark a proposal as viewed
 */
export async function markProposalAsViewed(id: string) {
  return apiRequest({
    url: `/api/proposals/${id}/viewed`,
    method: 'POST'
  });
}

/**
 * Mark a proposal as accepted
 */
export async function markProposalAsAccepted(id: string) {
  return apiRequest({
    url: `/api/proposals/${id}/accept`,
    method: 'POST'
  });
}

/**
 * Mark a proposal as rejected
 */
export async function markProposalAsRejected(id: string, reason?: string) {
  return apiRequest({
    url: `/api/proposals/${id}/reject`,
    method: 'POST',
    data: { reason }
  });
}

/**
 * Create a shareable link for a proposal
 */
export async function createProposalShareableLink(
  id: string,
  options: Partial<ProposalShareRequest> = {}
): Promise<ProposalShareResponse> {
  return apiRequest({
    url: `/api/proposals/${id}/share`,
    method: 'POST',
    data: { proposalId: id, ...options }
  });
}

/**
 * Export proposal to email
 */
export async function exportProposalToEmail(
  id: string,
  options: Omit<ProposalEmailRequest, 'proposalId'>
): Promise<ProposalEmailResponse> {
  return apiRequest({
    url: `/api/proposals/${id}/export/email`,
    method: 'POST',
    data: { proposalId: id, ...options }
  });
}

/**
 * Export proposal to social media
 */
export async function exportProposalToSocial(
  id: string,
  options: Omit<ProposalSocialExportRequest, 'proposalId'>
): Promise<ProposalSocialExportResponse> {
  return apiRequest({
    url: `/api/proposals/${id}/export/social`,
    method: 'POST',
    data: { proposalId: id, ...options }
  });
}

/**
 * Update proposal legal clauses
 */
export async function updateProposalClauses(
  id: string,
  clauses: string[]
): Promise<ProposalClausesUpdateResponse> {
  return apiRequest({
    url: `/api/proposals/${id}/clauses`,
    method: 'PUT',
    data: { proposalId: id, clauses }
  });
}

/**
 * Get industry-specific clauses for a company
 */
export async function getIndustrySpecificClauses(
  companyId: string
): Promise<IndustrySpecificClause[]> {
  return apiRequest({
    url: `/api/companies/${companyId}/clauses`,
    method: 'GET'
  });
}

/**
 * Generate proposal content with AI
 */
export async function generateProposalWithAI(
  options: ProposalAIGenerationOptions
): Promise<ProposalAIGenerationResponse> {
  return apiRequest({
    url: '/api/proposals/ai/generate',
    method: 'POST',
    data: options
  });
}

/**
 * Generate a specific proposal section with AI
 */
export async function generateProposalSectionWithAI(
  sectionType: string,
  prompt: string,
  model: string = 'claude-3-opus-20240229',
  context: {
    opportunityId?: string;
    companyId?: string;
    contactIds?: string[];
  } = {}
): Promise<{ title: string; content: string; type: string }> {
  return apiRequest({
    url: '/api/proposals/ai/generate-section',
    method: 'POST',
    data: {
      sectionType,
      prompt,
      model,
      ...context
    }
  });
}

/**
 * Get proposal analytics
 */
export async function getProposalAnalytics(
  id: string
): Promise<ProposalAnalytics> {
  return apiRequest({
    url: `/api/proposals/${id}/analytics`,
    method: 'GET'
  });
}
