import { apiRequest } from '../api/api-utils';

/**
 * Get all follow-ups
 */
export async function getFollowUps(params: {
  status?: 'pending' | 'completed' | 'cancelled';
  type?: 'email' | 'call' | 'meeting' | 'task' | 'other';
  priority?: 'low' | 'medium' | 'high';
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  startDate?: Date;
  endDate?: Date;
  search?: string;
} = {}) {
  // Convert dates to ISO strings for API
  const apiParams: Record<string, any> = { ...params };

  if (apiParams.startDate instanceof Date) {
    apiParams.startDate = apiParams.startDate.toISOString();
  }

  if (apiParams.endDate instanceof Date) {
    apiParams.endDate = apiParams.endDate.toISOString();
  }

  return apiRequest({
    url: '/api/follow-ups',
    method: 'GET',
    data: apiParams
  });
}

/**
 * Get a follow-up by ID
 */
export async function getFollowUpById(id: string) {
  return apiRequest({
    url: `/api/follow-ups/${id}`,
    method: 'GET'
  });
}

/**
 * Create a new follow-up
 */
export async function createFollowUp(data: {
  title: string;
  description?: string;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  activityId?: string;
  scheduledDate: Date;
  completedDate?: Date;
  status?: 'pending' | 'completed' | 'cancelled';
  type?: 'email' | 'call' | 'meeting' | 'task' | 'other';
  priority?: 'low' | 'medium' | 'high';
  template?: string;
  content?: string;
  reminderDate?: Date;
  isAIGenerated?: boolean;
  tags?: string[];
  customFields?: Record<string, any>;
}) {
  // Convert dates to ISO strings
  const apiData: Record<string, any> = { ...data };

  if (apiData.scheduledDate instanceof Date) {
    apiData.scheduledDate = apiData.scheduledDate.toISOString();
  }

  if (apiData.completedDate instanceof Date) {
    apiData.completedDate = apiData.completedDate.toISOString();
  }

  if (apiData.reminderDate instanceof Date) {
    apiData.reminderDate = apiData.reminderDate.toISOString();
  }

  return apiRequest({
    url: '/api/follow-ups',
    method: 'POST',
    data: apiData
  });
}

/**
 * Update a follow-up
 */
export async function updateFollowUp(
  id: string,
  data: {
    title?: string;
    description?: string;
    scheduledDate?: Date;
    completedDate?: Date;
    status?: 'pending' | 'completed' | 'cancelled';
    type?: 'email' | 'call' | 'meeting' | 'task' | 'other';
    priority?: 'low' | 'medium' | 'high';
    template?: string;
    content?: string;
    reminderDate?: Date;
    tags?: string[];
    customFields?: Record<string, any>;
  }
) {
  // Convert dates to ISO strings
  const apiData: Record<string, any> = { ...data };

  if (apiData.scheduledDate instanceof Date) {
    apiData.scheduledDate = apiData.scheduledDate.toISOString();
  }

  if (apiData.completedDate instanceof Date) {
    apiData.completedDate = apiData.completedDate.toISOString();
  }

  if (apiData.reminderDate instanceof Date) {
    apiData.reminderDate = apiData.reminderDate.toISOString();
  }

  return apiRequest({
    url: `/api/follow-ups/${id}`,
    method: 'PUT',
    data: apiData
  });
}

/**
 * Delete a follow-up
 */
export async function deleteFollowUp(id: string) {
  return apiRequest({
    url: `/api/follow-ups/${id}`,
    method: 'DELETE'
  });
}

/**
 * Generate follow-up recommendations
 */
export async function generateFollowUpRecommendations(params: {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  activityId?: string;
  count?: number;
}) {
  return apiRequest({
    url: '/api/follow-ups/generate',
    method: 'POST',
    data: params
  });
}
