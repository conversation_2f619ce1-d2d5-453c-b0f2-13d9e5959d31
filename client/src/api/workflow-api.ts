import { apiClient } from './api-client';

/**
 * Interface for Workflow Trigger
 */
export interface WorkflowTrigger {
  type: string;
  config: Record<string, any>;
  description: string;
}

/**
 * Interface for Workflow Action
 */
export interface WorkflowAction {
  type: string;
  config: Record<string, any>;
  description: string;
}

/**
 * Interface for Workflow Condition
 */
export interface WorkflowCondition {
  type: 'if' | 'wait_until' | 'for_each';
  config: Record<string, any>;
  description: string;
}

/**
 * Interface for Workflow Node
 */
export interface WorkflowNode {
  id: string;
  type: 'trigger' | 'action' | 'condition';
  position: { x: number; y: number };
  data: WorkflowTrigger | WorkflowAction | WorkflowCondition;
}

/**
 * Interface for Workflow Edge
 */
export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  condition?: Record<string, any>;
}

/**
 * Interface for Workflow
 */
export interface Workflow {
  _id: string;
  tenant_id: string;
  name: string;
  description?: string;
  dsl_yaml: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  version: number;
  status: 'draft' | 'active' | 'paused' | 'archived';
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  original_prompt?: string;
  tags?: string[];
  simulation_results?: {
    run_at: string;
    expected_volume: number;
    edge_cases: string[];
    performance_metrics: Record<string, any>;
  };
}

/**
 * Interface for Workflow Version
 */
export interface WorkflowVersion {
  _id: string;
  workflow_id: string;
  tenant_id: string;
  version: number;
  dsl_yaml: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  created_by: string;
  created_at: string;
  comment?: string;
}

/**
 * Interface for Workflow Run
 */
export interface WorkflowRun {
  _id: string;
  workflow_id: string;
  tenant_id: string;
  trigger_event: {
    type: string;
    data: Record<string, any>;
  };
  start_ts: string;
  end_ts?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  error_log?: string;
  step_executions: {
    node_id: string;
    started_at: string;
    completed_at?: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
    error?: string;
    result?: Record<string, any>;
    duration_ms?: number;
  }[];
  context_data?: Record<string, any>;
  created_by?: string;
  created_at: string;
  updated_at: string;
  duration_ms?: number;
  is_simulation: boolean;
  affected_records?: {
    type: string;
    id: string;
    action: string;
  }[];
}

/**
 * Interface for Create Workflow Request
 */
export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  dsl_yaml?: string;
  original_prompt?: string;
  tags?: string[];
}

/**
 * Interface for Update Workflow Request
 */
export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  dsl_yaml?: string;
  nodes?: WorkflowNode[];
  edges?: WorkflowEdge[];
  status?: 'draft' | 'active' | 'paused' | 'archived';
  tags?: string[];
}

/**
 * Interface for NL Parser Request
 */
export interface NLParserRequest {
  prompt: string;
}

/**
 * Interface for NL Parser Response
 */
export interface NLParserResponse {
  success: boolean;
  dsl_yaml?: string;
  nodes?: WorkflowNode[];
  edges?: WorkflowEdge[];
  error?: string;
  source: 'ai_service' | 'voyage_fallback' | 'error';
}

/**
 * Interface for Compile Workflow Request
 */
export interface CompileWorkflowRequest {
  dsl_yaml: string;
}

/**
 * Interface for Compile Workflow Response
 */
export interface CompileWorkflowResponse {
  dsl_yaml: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  errors: string[];
  warnings: string[];
}

/**
 * Interface for Simulate Workflow Request
 */
export interface SimulateWorkflowRequest {
  trigger_event: {
    type: string;
    data: Record<string, any>;
  };
}

/**
 * Interface for Execute Workflow Request
 */
export interface ExecuteWorkflowRequest {
  trigger_event: {
    type: string;
    data: Record<string, any>;
  };
}

/**
 * Create a new workflow
 */
export const createWorkflow = async (data: CreateWorkflowRequest): Promise<Workflow> => {
  const response = await apiClient.post<Workflow>('/api/workflows', data);
  return response.data;
};

/**
 * Get workflow by ID
 */
export const getWorkflow = async (id: string): Promise<Workflow> => {
  const response = await apiClient.get<Workflow>(`/api/workflows/${id}`);
  return response.data;
};

/**
 * List workflows
 */
export const listWorkflows = async (options: {
  status?: 'draft' | 'active' | 'paused' | 'archived';
  tags?: string[];
  limit?: number;
  skip?: number;
} = {}): Promise<{ workflows: Workflow[]; total: number }> => {
  const { status, tags, limit, skip } = options;

  const params: Record<string, any> = {};
  if (status) params.status = status;
  if (tags) params.tags = tags.join(',');
  if (limit) params.limit = limit;
  if (skip) params.skip = skip;

  const response = await apiClient.get<{ workflows: Workflow[]; total: number }>('/api/workflows', { params });
  return response.data;
};

/**
 * Update workflow
 */
export const updateWorkflow = async (id: string, data: UpdateWorkflowRequest): Promise<Workflow> => {
  const response = await apiClient.put<Workflow>(`/api/workflows/${id}`, data);
  return response.data;
};

/**
 * Delete workflow
 */
export const deleteWorkflow = async (id: string): Promise<boolean> => {
  const response = await apiClient.delete<{ success: boolean }>(`/api/workflows/${id}`);
  return response.data.success;
};

/**
 * Get workflow versions
 */
export const getWorkflowVersions = async (id: string): Promise<WorkflowVersion[]> => {
  const response = await apiClient.get<WorkflowVersion[]>(`/api/workflows/${id}/versions`);
  return response.data;
};

/**
 * Simulate workflow
 */
export const simulateWorkflow = async (id: string, data: SimulateWorkflowRequest): Promise<WorkflowRun> => {
  const response = await apiClient.post<WorkflowRun>(`/api/workflows/${id}/simulate`, data);
  return response.data;
};

/**
 * Execute workflow
 */
export const executeWorkflow = async (id: string, data: ExecuteWorkflowRequest): Promise<WorkflowRun> => {
  const response = await apiClient.post<WorkflowRun>(`/api/workflows/${id}/execute`, data);
  return response.data;
};

/**
 * Get workflow runs
 */
export const getWorkflowRuns = async (id: string, options: {
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  is_simulation?: boolean;
  limit?: number;
  skip?: number;
} = {}): Promise<{ runs: WorkflowRun[]; total: number }> => {
  const { status, is_simulation, limit, skip } = options;

  const params: Record<string, any> = {};
  if (status) params.status = status;
  if (is_simulation !== undefined) params.is_simulation = is_simulation;
  if (limit) params.limit = limit;
  if (skip) params.skip = skip;

  const response = await apiClient.get<{ runs: WorkflowRun[]; total: number }>(`/api/workflows/${id}/runs`, { params });
  return response.data;
};

/**
 * Parse natural language to workflow DSL
 */
export const parseWorkflow = async (data: NLParserRequest): Promise<NLParserResponse> => {
  const response = await apiClient.post<NLParserResponse>('/api/workflows/parse', data);
  return response.data;
};

/**
 * Compile workflow DSL to graph
 */
export const compileWorkflow = async (data: CompileWorkflowRequest): Promise<CompileWorkflowResponse> => {
  const response = await apiClient.post<CompileWorkflowResponse>('/api/workflows/compile', data);
  return response.data;
};
