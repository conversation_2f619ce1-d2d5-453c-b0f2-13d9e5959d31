import { apiRequest } from './api-utils';
import { 
  AskBIRequest, 
  AskBIResponse,
  RunAttributionRequest,
  RunAttributionResponse,
  GetAttributionResultsRequest,
  GetAttributionResultsResponse,
  BudgetOptimizationRequest,
  BudgetOptimizationResponse,
  TrackEventRequest,
  TrackEventResponse,
  GetDatasetsRequest,
  GetDatasetsResponse,
  CBICacheType
} from '@/types/analytics-reporting';

/**
 * Ask a question and get insights
 */
export async function askQuestion(request: Omit<AskBIRequest, 'tenant_id'>): Promise<AskBIResponse> {
  return apiRequest<AskBIResponse>({
    url: '/api/cbi/ask',
    method: 'POST',
    data: request,
  });
}

/**
 * Get recent insights
 */
export async function getRecentInsights(limit: number = 10): Promise<CBICacheType[]> {
  const response = await apiRequest<{ success: boolean; insights: CBICacheType[] }>({
    url: `/api/cbi/recent?limit=${limit}`,
    method: 'GET',
  });

  return response.insights;
}

/**
 * Delete an insight
 */
export async function deleteInsight(id: string): Promise<{ success: boolean; message: string }> {
  return apiRequest<{ success: boolean; message: string }>({
    url: `/api/cbi/${id}`,
    method: 'DELETE',
  });
}

/**
 * Run attribution model
 */
export async function runAttribution(request: Omit<RunAttributionRequest, 'tenant_id'>): Promise<RunAttributionResponse> {
  return apiRequest<RunAttributionResponse>({
    url: '/api/attribution/run',
    method: 'POST',
    data: request,
  });
}

/**
 * Get attribution results
 */
export async function getAttributionResults(request: Omit<GetAttributionResultsRequest, 'tenant_id'>): Promise<GetAttributionResultsResponse> {
  const { window, model_type, group_by, limit, sort_by, sort_order } = request;
  
  let url = `/api/attribution/results?window=${window}&model_type=${model_type}`;
  
  if (group_by) url += `&group_by=${group_by}`;
  if (limit) url += `&limit=${limit}`;
  if (sort_by) url += `&sort_by=${sort_by}`;
  if (sort_order) url += `&sort_order=${sort_order}`;
  
  return apiRequest<GetAttributionResultsResponse>({
    url,
    method: 'GET',
  });
}

/**
 * Optimize budget allocation
 */
export async function optimizeBudget(request: Omit<BudgetOptimizationRequest, 'tenant_id'>): Promise<BudgetOptimizationResponse> {
  return apiRequest<BudgetOptimizationResponse>({
    url: '/api/attribution/optimize-budget',
    method: 'POST',
    data: request,
  });
}

/**
 * Track an event
 */
export async function trackEvent(request: Omit<TrackEventRequest, 'tenant_id'>): Promise<TrackEventResponse> {
  return apiRequest<TrackEventResponse>({
    url: '/api/events/track',
    method: 'POST',
    data: request,
  });
}

/**
 * Get events
 */
export async function getEvents(options: {
  visitor_id?: string;
  contact_id?: string;
  channel?: string;
  event_type?: string;
  start_date?: string;
  end_date?: string;
  limit?: number;
  offset?: number;
}): Promise<{ success: boolean; events: any[]; count: number; limit: number; offset: number }> {
  const { visitor_id, contact_id, channel, event_type, start_date, end_date, limit, offset } = options;
  
  let url = '/api/events?';
  
  if (visitor_id) url += `&visitor_id=${visitor_id}`;
  if (contact_id) url += `&contact_id=${contact_id}`;
  if (channel) url += `&channel=${channel}`;
  if (event_type) url += `&event_type=${event_type}`;
  if (start_date) url += `&start_date=${start_date}`;
  if (end_date) url += `&end_date=${end_date}`;
  if (limit) url += `&limit=${limit}`;
  if (offset) url += `&offset=${offset}`;
  
  return apiRequest<{ success: boolean; events: any[]; count: number; limit: number; offset: number }>({
    url,
    method: 'GET',
  });
}

/**
 * Get event counts
 */
export async function getEventCounts(options: {
  group_by: 'channel' | 'event_type' | 'campaign' | 'source' | 'medium' | 'day' | 'week' | 'month';
  start_date?: string;
  end_date?: string;
}): Promise<{ success: boolean; counts: { _id: string; count: number }[] }> {
  const { group_by, start_date, end_date } = options;
  
  let url = `/api/events/counts?group_by=${group_by}`;
  
  if (start_date) url += `&start_date=${start_date}`;
  if (end_date) url += `&end_date=${end_date}`;
  
  return apiRequest<{ success: boolean; counts: { _id: string; count: number }[] }>({
    url,
    method: 'GET',
  });
}

/**
 * Get datasets
 */
export async function getDatasets(options: Omit<GetDatasetsRequest, 'tenant_id'> = {}): Promise<GetDatasetsResponse> {
  const { is_active } = options;
  
  let url = '/api/datasets';
  
  if (is_active !== undefined) url += `?is_active=${is_active}`;
  
  return apiRequest<GetDatasetsResponse>({
    url,
    method: 'GET',
  });
}

/**
 * Get dataset by ID
 */
export async function getDatasetById(id: string): Promise<{ success: boolean; dataset: any }> {
  return apiRequest<{ success: boolean; dataset: any }>({
    url: `/api/datasets/${id}`,
    method: 'GET',
  });
}

/**
 * Create or update dataset
 */
export async function createOrUpdateDataset(data: any): Promise<{ success: boolean; dataset_id: string; message: string }> {
  return apiRequest<{ success: boolean; dataset_id: string; message: string }>({
    url: '/api/datasets',
    method: 'POST',
    data,
  });
}
