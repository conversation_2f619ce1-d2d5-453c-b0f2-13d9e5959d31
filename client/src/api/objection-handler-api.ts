import { apiRequest } from '../api/api-utils';

/**
 * Get all objection categories
 */
export async function getObjectionCategories() {
  return apiRequest({
    url: '/api/objections/categories',
    method: 'GET'
  });
}

/**
 * Get all objections
 */
export async function getObjections(params: {
  category?: string;
  isCommon?: boolean;
  createdBy?: string;
  search?: string;
} = {}) {
  return apiRequest({
    url: '/api/objections',
    method: 'GET',
    data: params
  });
}

/**
 * Get objection by ID
 */
export async function getObjectionById(id: string) {
  return apiRequest({
    url: `/api/objections/${id}`,
    method: 'GET'
  });
}

/**
 * Create a new objection
 */
export async function createObjection(data: {
  name: string;
  category: string;
  description: string;
  tags?: string[];
  isCommon?: boolean;
  customFields?: Record<string, any>;
}) {
  return apiRequest({
    url: '/api/objections',
    method: 'POST',
    data
  });
}

/**
 * Update an objection
 */
export async function updateObjection(
  id: string,
  data: {
    name?: string;
    category?: string;
    description?: string;
    tags?: string[];
    isCommon?: boolean;
    customFields?: Record<string, any>;
  }
) {
  return apiRequest({
    url: `/api/objections/${id}`,
    method: 'PUT',
    data
  });
}

/**
 * Delete an objection
 */
export async function deleteObjection(id: string) {
  return apiRequest({
    url: `/api/objections/${id}`,
    method: 'DELETE'
  });
}

/**
 * Get responses for an objection
 */
export async function getResponsesForObjection(
  id: string,
  params: {
    effectiveness?: number;
    isAIGenerated?: boolean;
    createdBy?: string;
    opportunityId?: string;
    contactId?: string;
    companyId?: string;
  } = {}
) {
  return apiRequest({
    url: `/api/objections/${id}/responses`,
    method: 'GET',
    data: params
  });
}

/**
 * Create a response for an objection
 */
export async function createResponse(
  objectionId: string,
  data: {
    response: string;
    context: string;
    effectiveness?: number;
    opportunityId?: string;
    contactId?: string;
    companyId?: string;
    isAIGenerated?: boolean;
    customFields?: Record<string, any>;
  }
) {
  return apiRequest({
    url: `/api/objections/${objectionId}/responses`,
    method: 'POST',
    data
  });
}

/**
 * Update a response
 */
export async function updateResponse(
  id: string,
  data: {
    response?: string;
    context?: string;
    effectiveness?: number;
    usedCount?: number;
    successCount?: number;
    customFields?: Record<string, any>;
  }
) {
  return apiRequest({
    url: `/api/objection-responses/${id}`,
    method: 'PUT',
    data
  });
}

/**
 * Delete a response
 */
export async function deleteResponse(id: string) {
  return apiRequest({
    url: `/api/objection-responses/${id}`,
    method: 'DELETE'
  });
}

/**
 * Record response usage
 */
export async function recordResponseUsage(
  id: string,
  wasSuccessful: boolean = false
) {
  return apiRequest({
    url: `/api/objection-responses/${id}/usage`,
    method: 'POST',
    data: { wasSuccessful }
  });
}

/**
 * Generate AI response for an objection
 */
export async function generateAIResponse(
  objectionId: string,
  params: {
    opportunityId?: string;
    contactId?: string;
    companyId?: string;
  } = {}
) {
  return apiRequest({
    url: `/api/objections/${objectionId}/generate-response`,
    method: 'POST',
    data: params
  });
}

/**
 * Classify an objection
 */
export async function classifyObjection(
  objectionText: string,
  params: {
    opportunityId?: string;
    contactId?: string;
    companyId?: string;
  } = {}
) {
  return apiRequest({
    url: '/api/objections/classify',
    method: 'POST',
    data: {
      objectionText,
      ...params
    }
  });
}

/**
 * Handle a real-time objection
 */
export async function handleRealTimeObjection(
  objectionText: string,
  params: {
    opportunityId?: string;
    contactId?: string;
    companyId?: string;
  } = {}
) {
  return apiRequest({
    url: '/api/objections/handle-realtime',
    method: 'POST',
    data: {
      objectionText,
      ...params
    }
  });
}
