/**
 * API utilities for making requests
 */
import axios from 'axios';

// Create axios instance
export const apiClient = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Handle API errors
 * @param error Error object
 * @returns Error message
 */
export function handleApiError(error: any): string {
  if (axios.isAxiosError(error)) {
    const errorMessage = error.response?.data?.message || error.message;
    console.error('API Error:', errorMessage);
    return errorMessage;
  }

  console.error('Unexpected error:', error);
  return 'An unexpected error occurred';
}

/**
 * Make a GET request
 * @param url API endpoint
 * @param params Query parameters
 * @returns Promise with response data
 */
export async function apiGet<T>(url: string, params?: any): Promise<T> {
  try {
    const response = await apiClient.get<T>(url, { params });
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
}

/**
 * Make a POST request
 * @param url API endpoint
 * @param data Request body
 * @returns Promise with response data
 */
export async function apiPost<T>(url: string, data?: any): Promise<T> {
  try {
    const response = await apiClient.post<T>(url, data);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
}

/**
 * Make a PATCH request
 * @param url API endpoint
 * @param data Request body
 * @returns Promise with response data
 */
export async function apiPatch<T>(url: string, data?: any): Promise<T> {
  try {
    const response = await apiClient.patch<T>(url, data);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
}

/**
 * Make a DELETE request
 * @param url API endpoint
 * @returns Promise with response data
 */
export async function apiDelete<T>(url: string): Promise<T> {
  try {
    const response = await apiClient.delete<T>(url);
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
}

/**
 * Make a request with options
 * @param options Request options
 * @returns Promise with response data
 */
export async function apiRequest<T = any>({
  url,
  method = 'GET',
  data,
  headers,
}: {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data?: any;
  headers?: Record<string, string>;
}): Promise<T> {
  try {
    const response = await apiClient.request<T>({
      url,
      method,
      data,
      headers,
    });
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
}
