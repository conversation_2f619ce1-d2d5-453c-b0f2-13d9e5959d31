import { apiRequest } from '../api/api-utils';

/**
 * Generate a meeting prep document
 */
export async function generateMeetingPrep(params: {
  contactId?: string;
  companyId?: string;
  opportunityId?: string;
}) {
  const { contactId, companyId, opportunityId } = params;

  if (!contactId && !companyId && !opportunityId) {
    throw new Error('At least one of contactId, companyId, or opportunityId must be provided');
  }

  return apiRequest({
    url: '/api/meeting-prep',
    method: 'POST',
    data: {
      contactId,
      companyId,
      opportunityId
    }
  });
}

/**
 * Generate a URL to add meeting prep to a calendar agenda
 */
export async function generateAddToAgendaUrl(
  id: string,
  calendarType: 'google' | 'outlook' = 'google'
) {
  return apiRequest({
    url: `/api/meeting-prep/${id}/add-to-agenda`,
    method: 'POST',
    data: {
      calendarType
    }
  });
}
