import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MessageSquare } from 'lucide-react';
import { ObjectionHandlerDialog } from './ObjectionHandlerDialog';

interface ObjectionHandlerButtonProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

export function ObjectionHandlerButton({
  opportunityId,
  contactId,
  companyId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Objection Handler'
}: ObjectionHandlerButtonProps) {
  return (
    <ObjectionHandlerDialog
      opportunityId={opportunityId}
      contactId={contactId}
      companyId={companyId}
      opportunityName={opportunityName}
      contactName={contactName}
      companyName={companyName}
      variant={variant}
      size={size}
      buttonText={buttonText}
    />
  );
}
