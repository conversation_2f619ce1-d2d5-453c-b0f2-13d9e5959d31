import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, MessageSquare, FileText, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { handleRealTimeObjection, recordResponseUsage } from '@/api/objection-handler-api';
import {
  RealTimeObjectionHandlerProps,
  RealTimeObjectionResponse,
  Rebuttal,
  SupportingEvidence
} from '@/types/objection-handler';

/**
 * RealTimeObjectionHandler Component
 *
 * This component provides a user interface for handling real-time objections during sales conversations.
 * It allows users to enter an objection from a customer, processes it using AI, and displays
 * appropriate responses, rebuttals, and supporting evidence.
 *
 * @component
 * @example
 * ```tsx
 * <RealTimeObjectionHandler
 *   opportunityId="opp123"
 *   contactId="contact456"
 *   onSuccess={(response) => console.log('Objection handled:', response)}
 * />
 * ```
 */
export function RealTimeObjectionHandler({
  opportunityId,
  contactId,
  companyId,
  onSuccess
}: RealTimeObjectionHandlerProps) {
  const { toast } = useToast();
  const [objectionText, setObjectionText] = useState('');
  const [activeTab, setActiveTab] = useState('response');
  const [responseId, setResponseId] = useState<string | null>(null);

  // Handle real-time objection mutation
  const objectionMutation = useMutation({
    mutationFn: ({ text }: { text: string }) =>
      handleRealTimeObjection(text, { opportunityId, contactId, companyId }),
    onSuccess: (data) => {
      if (data.responseId) {
        setResponseId(data.responseId);
      }

      toast({
        title: 'Success',
        description: 'Objection handled successfully',
      });

      if (onSuccess) {
        onSuccess(data);
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to handle objection',
        variant: 'destructive',
      });
    },
  });

  // Record response usage mutation
  const usageMutation = useMutation({
    mutationFn: ({ id, wasSuccessful }: { id: string; wasSuccessful: boolean }) =>
      recordResponseUsage(id, wasSuccessful),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Feedback recorded successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to record feedback',
        variant: 'destructive',
      });
    },
  });

  // Handle submitting the objection
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!objectionText.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an objection',
        variant: 'destructive',
      });
      return;
    }

    objectionMutation.mutate({ text: objectionText });
  };

  // Handle recording feedback
  const handleFeedback = (wasSuccessful: boolean) => {
    if (!responseId) return;

    usageMutation.mutate({
      id: responseId,
      wasSuccessful
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Real-Time Objection Handler</CardTitle>
        <CardDescription>
          Enter a customer objection to get AI-powered responses and rebuttals
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Textarea
                placeholder="Enter the customer's objection here..."
                value={objectionText}
                onChange={(e) => setObjectionText(e.target.value)}
                className="min-h-[100px]"
                disabled={objectionMutation.isPending}
              />
            </div>
            <Button
              type="submit"
              disabled={objectionMutation.isPending || !objectionText.trim()}
              className="w-full"
            >
              {objectionMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Handle Objection
                </>
              )}
            </Button>
          </div>
        </form>

        {objectionMutation.isSuccess && objectionMutation.data && (
          <div className="mt-6">
            <div className="mb-2 flex items-center justify-between">
              <Badge variant="outline" className="text-xs">
                {objectionMutation.data.objectionClass}
              </Badge>

              {responseId && (
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback(true)}
                    disabled={usageMutation.isPending}
                  >
                    <ThumbsUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback(false)}
                    disabled={usageMutation.isPending}
                  >
                    <ThumbsDown className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="response">Response</TabsTrigger>
                <TabsTrigger value="rebuttals">Rebuttals</TabsTrigger>
                <TabsTrigger value="evidence">Evidence</TabsTrigger>
              </TabsList>

              <TabsContent value="response" className="mt-4">
                <Card>
                  <CardContent className="pt-6">
                    <p className="whitespace-pre-wrap">{objectionMutation.data.response}</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="rebuttals" className="mt-4">
                <Card>
                  <CardContent className="pt-6">
                    {objectionMutation.data.rebuttals && objectionMutation.data.rebuttals.length > 0 ? (
                      <div className="space-y-4">
                        {objectionMutation.data.rebuttals.map((rebuttal: any) => (
                          <div key={rebuttal.id} className="border p-3 rounded-md">
                            <div className="flex justify-between items-center mb-2">
                              <Badge variant="secondary">Win Rate: {Math.round(rebuttal.winRate * 100)}%</Badge>
                            </div>
                            <p className="text-sm">{rebuttal.text}</p>
                            {rebuttal.evidence && rebuttal.evidence.length > 0 && (
                              <div className="mt-2">
                                <p className="text-xs text-muted-foreground">Supporting Evidence:</p>
                                <ul className="text-xs list-disc list-inside">
                                  {rebuttal.evidence.map((evidence: string, index: number) => (
                                    <li key={index}>{evidence}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No alternative rebuttals available</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="evidence" className="mt-4">
                <Card>
                  <CardContent className="pt-6">
                    {objectionMutation.data.supportingEvidence && objectionMutation.data.supportingEvidence.length > 0 ? (
                      <div className="space-y-4">
                        {objectionMutation.data.supportingEvidence.map((evidence: any) => (
                          <div key={evidence.id} className="border p-3 rounded-md">
                            <div className="flex items-center gap-2 mb-2">
                              <FileText className="h-4 w-4" />
                              <h4 className="font-medium">{evidence.title}</h4>
                              <Badge variant="outline" className="ml-auto">{evidence.type}</Badge>
                            </div>
                            <p className="text-sm">{evidence.description}</p>
                            {evidence.url && (
                              <a
                                href={evidence.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:underline mt-2 inline-block"
                              >
                                View Document
                              </a>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No supporting evidence available</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
