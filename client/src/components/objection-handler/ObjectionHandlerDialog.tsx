import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  MessageSquare,
  Search,
  Plus,
  Sparkles,
  ThumbsUp,
  ThumbsDown,
  Copy,
  Loader2,
  Tag,
  Filter
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  getObjectionCategories,
  getObjections,
  getResponsesForObjection,
  generateAIResponse,
  classifyObjection,
  recordResponseUsage
} from '@/api/objection-handler-api';

interface ObjectionHandlerDialogProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

interface Objection {
  _id: string;
  name: string;
  category: string;
  description: string;
  tags: string[];
  isCommon: boolean;
}

interface ObjectionResponse {
  _id: string;
  objectionId: string;
  response: string;
  context: string;
  effectiveness: number;
  usedCount: number;
  successCount: number;
  isAIGenerated: boolean;
  createdAt: string;
}

export function ObjectionHandlerDialog({
  opportunityId,
  contactId,
  companyId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Objection Handler'
}: ObjectionHandlerDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedObjection, setSelectedObjection] = useState<Objection | null>(null);
  const [customObjectionText, setCustomObjectionText] = useState('');
  const [isClassifying, setIsClassifying] = useState(false);
  const [selectedTab, setSelectedTab] = useState('search');
  const [selectedResponseId, setSelectedResponseId] = useState<string | null>(null);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Reset state when dialog is closed
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setSelectedCategory('');
      setSelectedObjection(null);
      setCustomObjectionText('');
      setIsClassifying(false);
      setSelectedTab('search');
      setSelectedResponseId(null);
    }
  }, [isOpen]);

  // Fetch objection categories
  const {
    data: categories,
    isLoading: isCategoriesLoading
  } = useQuery({
    queryKey: ['objection-categories'],
    queryFn: getObjectionCategories
  });

  // Fetch objections based on search and category
  const {
    data: objections,
    isLoading: isObjectionsLoading,
    refetch: refetchObjections
  } = useQuery({
    queryKey: ['objections', searchQuery, selectedCategory],
    queryFn: () => getObjections({
      search: searchQuery,
      category: selectedCategory || undefined,
      isCommon: true
    }),
    enabled: isOpen
  });

  // Fetch responses for the selected objection
  const {
    data: responses,
    isLoading: isResponsesLoading,
    refetch: refetchResponses
  } = useQuery({
    queryKey: ['objection-responses', selectedObjection?._id],
    queryFn: () => selectedObjection
      ? getResponsesForObjection(selectedObjection._id)
      : Promise.resolve([]),
    enabled: !!selectedObjection
  });

  // Classify custom objection
  const classifyMutation = useMutation({
    mutationFn: classifyObjection,
    onSuccess: (data) => {
      setIsClassifying(false);

      // Set the category based on classification
      setSelectedCategory(data.category);

      // Refetch objections with the new category
      refetchObjections();

      toast({
        title: 'Objection Classified',
        description: `Category: ${data.category}`,
      });

      // If there are suggested objections, select the first one
      if (data.suggestedObjections && data.suggestedObjections.length > 0) {
        // Find the objection in the list
        const suggestedId = data.suggestedObjections[0].id;
        const suggestedObjection = objections?.find((obj: { _id: string }) => obj._id === suggestedId);

        if (suggestedObjection) {
          setSelectedObjection(suggestedObjection);
        }
      }
    },
    onError: (error) => {
      setIsClassifying(false);
      toast({
        title: 'Classification Failed',
        description: error instanceof Error ? error.message : 'Failed to classify objection',
        variant: 'destructive',
      });
    }
  });

  // Generate AI response
  const generateResponseMutation = useMutation({
    mutationFn: (objectionId: string) => generateAIResponse(objectionId, {
      opportunityId,
      contactId,
      companyId
    }),
    onSuccess: (data) => {
      toast({
        title: 'Response Generated',
        description: 'AI has generated a response for this objection',
      });

      // Refetch responses
      refetchResponses();

      // Select the newly generated response
      setSelectedResponseId(data._id);
    },
    onError: (error) => {
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate response',
        variant: 'destructive',
      });
    }
  });

  // Record response usage
  const recordUsageMutation = useMutation({
    mutationFn: ({ id, wasSuccessful }: { id: string; wasSuccessful: boolean }) =>
      recordResponseUsage(id, wasSuccessful),
    onSuccess: () => {
      // Refetch responses
      refetchResponses();
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to record response usage',
        variant: 'destructive',
      });
    }
  });

  // Handle search
  const handleSearch = () => {
    refetchObjections();
  };

  // Handle objection selection
  const handleObjectionSelect = (objection: {
    _id: string;
    name: string;
    category: string;
    description: string;
    tags: string[];
    isCommon: boolean;
  }) => {
    setSelectedObjection(objection);
    setSelectedResponseId(null);
  };

  // Handle custom objection classification
  const handleClassifyObjection = () => {
    if (!customObjectionText.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an objection to classify',
        variant: 'destructive',
      });
      return;
    }

    setIsClassifying(true);
    classifyMutation.mutate(customObjectionText);
  };

  // Handle generate response
  const handleGenerateResponse = () => {
    if (!selectedObjection) {
      toast({
        title: 'Error',
        description: 'Please select an objection first',
        variant: 'destructive',
      });
      return;
    }

    generateResponseMutation.mutate(selectedObjection._id);
  };

  // Handle copy response to clipboard
  const handleCopyResponse = (response: string) => {
    navigator.clipboard.writeText(response);
    toast({
      title: 'Copied',
      description: 'Response copied to clipboard',
    });
  };

  // Handle response feedback
  const handleResponseFeedback = (id: string, wasSuccessful: boolean) => {
    recordUsageMutation.mutate({ id, wasSuccessful });

    toast({
      title: wasSuccessful ? 'Success Recorded' : 'Feedback Recorded',
      description: wasSuccessful
        ? 'This response was marked as successful'
        : 'Thank you for your feedback',
    });
  };

  // Format effectiveness as stars
  const formatEffectiveness = (effectiveness: number) => {
    return '★'.repeat(effectiveness) + '☆'.repeat(5 - effectiveness);
  };

  // Get badge color for category
  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case 'price':
        return 'bg-red-100 text-red-800';
      case 'product':
        return 'bg-blue-100 text-blue-800';
      case 'competition':
        return 'bg-purple-100 text-purple-800';
      case 'timing':
        return 'bg-yellow-100 text-yellow-800';
      case 'authority':
        return 'bg-orange-100 text-orange-800';
      case 'need':
        return 'bg-green-100 text-green-800';
      case 'trust':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Objection Handler</DialogTitle>
          <DialogDescription>
            Find effective responses to sales objections based on your specific context.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Context information */}
          {(opportunityName || contactName || companyName) && (
            <Card>
              <CardHeader className="py-2">
                <CardTitle className="text-sm">Context</CardTitle>
              </CardHeader>
              <CardContent className="py-2 space-y-1">
                {opportunityName && (
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Opportunity:</span>
                    <span>{opportunityName}</span>
                  </div>
                )}
                {contactName && (
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Contact:</span>
                    <span>{contactName}</span>
                  </div>
                )}
                {companyName && (
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Company:</span>
                    <span>{companyName}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="search">
                <Search className="h-4 w-4 mr-2" />
                Search Objections
              </TabsTrigger>
              <TabsTrigger value="custom">
                <Plus className="h-4 w-4 mr-2" />
                Custom Objection
              </TabsTrigger>
            </TabsList>

            {/* Search Objections Tab */}
            <TabsContent value="search" className="space-y-4">
              <div className="flex flex-col md:flex-row gap-2">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search objections..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSearch();
                        }
                      }}
                    />
                  </div>
                </div>
                <div className="w-full md:w-[200px]">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <div className="flex items-center">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="All Categories" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Categories</SelectItem>
                      {isCategoriesLoading ? (
                        <SelectItem value="" disabled>Loading...</SelectItem>
                      ) : (
                        categories?.map((category: string) => (
                          <SelectItem key={category} value={category}>
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleSearch}>
                  Search
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Objections List */}
                <Card className="h-[400px] overflow-y-auto">
                  <CardHeader className="py-3">
                    <CardTitle className="text-lg">Objections</CardTitle>
                  </CardHeader>
                  <CardContent className="py-0">
                    {isObjectionsLoading ? (
                      <div className="space-y-2">
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                      </div>
                    ) : objections?.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>No objections found</p>
                        <p className="text-sm">Try a different search or category</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {objections?.map((objection: {
                          _id: string;
                          name: string;
                          category: string;
                          description: string;
                          tags: string[];
                          isCommon: boolean;
                        }) => (
                          <div
                            key={objection._id}
                            className={`p-3 rounded-md cursor-pointer hover:bg-gray-50 ${
                              selectedObjection?._id === objection._id ? 'bg-blue-50 border border-blue-200' : 'border'
                            }`}
                            onClick={() => handleObjectionSelect(objection)}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{objection.name}</p>
                                <Badge className={getCategoryBadgeColor(objection.category)}>
                                  {objection.category.charAt(0).toUpperCase() + objection.category.slice(1)}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Responses List */}
                <Card className="h-[400px] overflow-y-auto">
                  <CardHeader className="py-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg">Responses</CardTitle>
                      {selectedObjection && (
                        <Button
                          size="sm"
                          onClick={handleGenerateResponse}
                          disabled={generateResponseMutation.isPending}
                        >
                          {generateResponseMutation.isPending ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Sparkles className="h-4 w-4 mr-2" />
                          )}
                          Generate
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="py-0">
                    {!selectedObjection ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>Select an objection to see responses</p>
                      </div>
                    ) : isResponsesLoading ? (
                      <div className="space-y-2">
                        <Skeleton className="h-24 w-full" />
                        <Skeleton className="h-24 w-full" />
                      </div>
                    ) : responses?.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>No responses found</p>
                        <p className="text-sm">Generate a response with AI</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {responses?.map((response: {
                          _id: string;
                          effectiveness: number;
                          isAIGenerated?: boolean;
                          response: string;
                          usedCount: number;
                          successCount: number;
                        }) => (
                          <div
                            key={response._id}
                            className={`p-3 rounded-md border ${
                              selectedResponseId === response._id ? 'bg-blue-50 border-blue-200' : ''
                            }`}
                            onClick={() => setSelectedResponseId(response._id)}
                          >
                            <div className="flex justify-between items-start mb-2">
                              <div className="flex items-center">
                                <span className="text-yellow-500 text-sm mr-2">
                                  {formatEffectiveness(response.effectiveness)}
                                </span>
                                {response.isAIGenerated && (
                                  <Badge variant="outline" className="ml-1">
                                    <Sparkles className="h-3 w-3 mr-1" />
                                    AI
                                  </Badge>
                                )}
                              </div>
                              <div className="flex space-x-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleCopyResponse(response.response);
                                  }}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-sm mb-2">{response.response}</p>
                            <div className="flex justify-between items-center mt-2">
                              <div className="text-xs text-gray-500">
                                Used {response.usedCount} times
                                {response.successCount > 0 && ` (${response.successCount} successful)`}
                              </div>
                              <div className="flex space-x-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleResponseFeedback(response._id, true);
                                  }}
                                >
                                  <ThumbsUp className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleResponseFeedback(response._id, false);
                                  }}
                                >
                                  <ThumbsDown className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Selected Objection Details */}
              {selectedObjection && (
                <Card>
                  <CardHeader className="py-3">
                    <CardTitle className="text-lg">{selectedObjection.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>{selectedObjection.description}</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedObjection.tags.map((tag) => (
                        <Badge key={tag} variant="outline">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Custom Objection Tab */}
            <TabsContent value="custom" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Enter Custom Objection</CardTitle>
                  <CardDescription>
                    Describe the objection you're facing to get AI-powered responses
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="objection-text">Objection</Label>
                      <Textarea
                        id="objection-text"
                        placeholder="Enter the objection you're facing..."
                        value={customObjectionText}
                        onChange={(e) => setCustomObjectionText(e.target.value)}
                        rows={4}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="objection-category">Category</Label>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger id="objection-category">
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                          {isCategoriesLoading ? (
                            <SelectItem value="" disabled>Loading...</SelectItem>
                          ) : (
                            categories?.map((category: string) => (
                              <SelectItem key={category} value={category}>
                                {category.charAt(0).toUpperCase() + category.slice(1)}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={handleClassifyObjection}
                    disabled={isClassifying || !customObjectionText.trim()}
                  >
                    {isClassifying ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Tag className="h-4 w-4 mr-2" />
                    )}
                    Classify Objection
                  </Button>

                  <Button
                    onClick={() => {
                      if (!selectedCategory) {
                        toast({
                          title: 'Error',
                          description: 'Please select a category',
                          variant: 'destructive',
                        });
                        return;
                      }

                      // TODO: Implement custom objection handling
                      toast({
                        title: 'Coming Soon',
                        description: 'This feature is not yet implemented',
                      });
                    }}
                    disabled={!customObjectionText.trim() || !selectedCategory}
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Response
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
