import { useState, useEffect, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, Mic, MicOff, MessageSquare, FileText, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { handleRealTimeObjection, recordResponseUsage } from '@/api/objection-handler-api';
import {
  VoiceObjectionHandlerProps,
  RealTimeObjectionResponse,
  Rebuttal,
  SupportingEvidence,
  VoiceObjectionResponse
} from '@/types/objection-handler';

/**
 * VoiceObjectionHandler Component
 *
 * This component provides a voice-based interface for handling real-time objections during sales conversations.
 * It allows users to speak an objection from a customer, transcribes it, processes it using AI, and displays
 * appropriate responses, rebuttals, and supporting evidence.
 *
 * @component
 * @example
 * ```tsx
 * <VoiceObjectionHandler
 *   opportunityId="opp123"
 *   contactId="contact456"
 *   onSuccess={(response) => console.log('Objection handled:', response)}
 * />
 * ```
 */
export function VoiceObjectionHandler({
  opportunityId,
  contactId,
  companyId,
  onSuccess
}: VoiceObjectionHandlerProps) {
  const { toast } = useToast();
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [activeTab, setActiveTab] = useState('response');
  const [responseId, setResponseId] = useState<string | null>(null);

  // References for speech recognition
  const recognitionRef = useRef<any>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window)) {
      // Define SpeechRecognition type
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;

      recognitionRef.current.onresult = (event: any) => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        setTranscript(finalTranscript || interimTranscript);
      };

      recognitionRef.current.onerror = (event: any) => {
        console.error('Speech recognition error', event.error);
        setIsListening(false);

        toast({
          title: 'Error',
          description: `Speech recognition error: ${event.error}`,
          variant: 'destructive',
        });
      };
    } else {
      toast({
        title: 'Error',
        description: 'Speech recognition is not supported in this browser',
        variant: 'destructive',
      });
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [toast]);

  // Handle real-time objection mutation
  const objectionMutation = useMutation({
    mutationFn: ({ text }: { text: string }) =>
      handleRealTimeObjection(text, { opportunityId, contactId, companyId }),
    onSuccess: (data) => {
      if (data.responseId) {
        setResponseId(data.responseId);
      }

      toast({
        title: 'Success',
        description: 'Objection handled successfully',
      });

      if (onSuccess) {
        onSuccess(data);
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to handle objection',
        variant: 'destructive',
      });
    },
  });

  // Record response usage mutation
  const usageMutation = useMutation({
    mutationFn: ({ id, wasSuccessful }: { id: string; wasSuccessful: boolean }) =>
      recordResponseUsage(id, wasSuccessful),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Feedback recorded successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to record feedback',
        variant: 'destructive',
      });
    },
  });

  // Toggle listening
  const toggleListening = () => {
    if (isListening) {
      recognitionRef.current?.stop();
      setIsListening(false);
    } else {
      setTranscript('');
      recognitionRef.current?.start();
      setIsListening(true);
    }
  };

  // Handle submitting the objection
  const handleSubmit = () => {
    if (!transcript.trim()) {
      toast({
        title: 'Error',
        description: 'No objection detected',
        variant: 'destructive',
      });
      return;
    }

    objectionMutation.mutate({ text: transcript });
  };

  // Handle recording feedback
  const handleFeedback = (wasSuccessful: boolean) => {
    if (!responseId) return;

    usageMutation.mutate({
      id: responseId,
      wasSuccessful
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Voice Objection Handler</CardTitle>
        <CardDescription>
          Speak the customer's objection to get AI-powered responses and rebuttals
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="border rounded-md p-4 min-h-[100px] relative">
            {transcript ? (
              <p className="whitespace-pre-wrap">{transcript}</p>
            ) : (
              <p className="text-muted-foreground">
                {isListening ? 'Listening for objection...' : 'Press the microphone button to start listening'}
              </p>
            )}
            {isListening && (
              <div className="absolute top-2 right-2">
                <span className="flex h-3 w-3">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                </span>
              </div>
            )}
          </div>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant={isListening ? 'destructive' : 'default'}
              onClick={toggleListening}
              className="flex-1"
            >
              {isListening ? (
                <>
                  <MicOff className="mr-2 h-4 w-4" />
                  Stop Listening
                </>
              ) : (
                <>
                  <Mic className="mr-2 h-4 w-4" />
                  Start Listening
                </>
              )}
            </Button>

            <Button
              type="button"
              disabled={objectionMutation.isPending || !transcript.trim()}
              onClick={handleSubmit}
              className="flex-1"
            >
              {objectionMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Handle Objection
                </>
              )}
            </Button>
          </div>
        </div>

        {objectionMutation.isSuccess && objectionMutation.data && (
          <div className="mt-6">
            <div className="mb-2 flex items-center justify-between">
              <Badge variant="outline" className="text-xs">
                {objectionMutation.data.objectionClass}
              </Badge>

              {responseId && (
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback(true)}
                    disabled={usageMutation.isPending}
                  >
                    <ThumbsUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback(false)}
                    disabled={usageMutation.isPending}
                  >
                    <ThumbsDown className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="response">Response</TabsTrigger>
                <TabsTrigger value="rebuttals">Rebuttals</TabsTrigger>
                <TabsTrigger value="evidence">Evidence</TabsTrigger>
              </TabsList>

              <TabsContent value="response" className="mt-4">
                <Card>
                  <CardContent className="pt-6">
                    <p className="whitespace-pre-wrap">{objectionMutation.data.response}</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="rebuttals" className="mt-4">
                <Card>
                  <CardContent className="pt-6">
                    {objectionMutation.data.rebuttals && objectionMutation.data.rebuttals.length > 0 ? (
                      <div className="space-y-4">
                        {objectionMutation.data.rebuttals.map((rebuttal: any) => (
                          <div key={rebuttal.id} className="border p-3 rounded-md">
                            <div className="flex justify-between items-center mb-2">
                              <Badge variant="secondary">Win Rate: {Math.round(rebuttal.winRate * 100)}%</Badge>
                            </div>
                            <p className="text-sm">{rebuttal.text}</p>
                            {rebuttal.evidence && rebuttal.evidence.length > 0 && (
                              <div className="mt-2">
                                <p className="text-xs text-muted-foreground">Supporting Evidence:</p>
                                <ul className="text-xs list-disc list-inside">
                                  {rebuttal.evidence.map((evidence: string, index: number) => (
                                    <li key={index}>{evidence}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No alternative rebuttals available</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="evidence" className="mt-4">
                <Card>
                  <CardContent className="pt-6">
                    {objectionMutation.data.supportingEvidence && objectionMutation.data.supportingEvidence.length > 0 ? (
                      <div className="space-y-4">
                        {objectionMutation.data.supportingEvidence.map((evidence: any) => (
                          <div key={evidence.id} className="border p-3 rounded-md">
                            <div className="flex items-center gap-2 mb-2">
                              <FileText className="h-4 w-4" />
                              <h4 className="font-medium">{evidence.title}</h4>
                              <Badge variant="outline" className="ml-auto">{evidence.type}</Badge>
                            </div>
                            <p className="text-sm">{evidence.description}</p>
                            {evidence.url && (
                              <a
                                href={evidence.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:underline mt-2 inline-block"
                              >
                                View Document
                              </a>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No supporting evidence available</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
