import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import {
  User,
  MessageSquare,
  Clock,
  Sparkles,
  Target,
  AlertTriangle,
  CheckCircle2,
  Lightbulb,
  Phone,
  Video
} from 'lucide-react';
import { format } from 'date-fns';

interface ContactPersonaProps {
  contactId: string;
  persona?: {
    summary: string;
    communicationPreferences: {
      preferredChannel?: 'email' | 'phone' | 'in-person' | 'video';
      bestTimeToContact?: string;
      responseTime?: 'fast' | 'medium' | 'slow';
    };
    interests: string[];
    painPoints: string[];
    decisionFactors: string[];
    aiConfidence: number;
    lastUpdated?: string;
  } | null;
  isLoading?: boolean;
}

export const ContactPersona = ({ contactId, persona, isLoading }: ContactPersonaProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isGenerating, setIsGenerating] = useState(false);

  // Generate persona mutation
  const generateMutation = useMutation({
    mutationFn: async () => {
      return mongoApiClient.contacts.generatePersona(contactId);
    },
    onSuccess: () => {
      toast({
        title: 'Persona generated',
        description: 'The contact persona has been generated successfully.',
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['contact', contactId] });
      setIsGenerating(false);
    },
    onError: (error) => {
      toast({
        title: 'Failed to generate persona',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
      setIsGenerating(false);
    }
  });

  // Enrich contact mutation
  const enrichMutation = useMutation({
    mutationFn: async () => {
      return mongoApiClient.contacts.enrich(contactId);
    },
    onSuccess: (data) => {
      toast({
        title: 'Contact enriched',
        description: `Updated ${data.enrichedFields.length} fields from ${data.sources.join(', ')}.`,
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['contact', contactId] });
    },
    onError: (error) => {
      toast({
        title: 'Failed to enrich contact',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  });

  // Handle generate button click
  const handleGenerate = () => {
    setIsGenerating(true);
    generateMutation.mutate();
  };

  // Handle enrich button click
  const handleEnrich = () => {
    enrichMutation.mutate();
  };

  // Get icon for preferred channel
  const getChannelIcon = (channel?: string) => {
    switch (channel) {
      case 'email':
        return <MessageSquare className="h-4 w-4" />;
      case 'phone':
        return <Phone className="h-4 w-4" />;
      case 'in-person':
        return <User className="h-4 w-4" />;
      case 'video':
        return <Video className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  // Get color for confidence level
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-500';
    if (confidence >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl">Contact Persona</CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-48" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <div className="grid grid-cols-3 gap-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
            <Skeleton className="h-32 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render empty state
  if (!persona) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl">Contact Persona</CardTitle>
          <CardDescription>
            AI-generated insights about this contact
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Sparkles className="h-12 w-12 mx-auto mb-4 text-blue-400" />
            <p className="mb-2">No persona available yet</p>
            <p className="text-sm text-gray-500 mb-6">
              Generate an AI-powered persona to better understand this contact's preferences and needs.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-2">
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || generateMutation.isPending}
              >
                <Sparkles className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
                Generate Persona
              </Button>
              <Button
                variant="outline"
                onClick={handleEnrich}
                disabled={enrichMutation.isPending}
              >
                <Sparkles className={`h-4 w-4 mr-2 ${enrichMutation.isPending ? 'animate-spin' : ''}`} />
                Enrich Contact
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-xl">Contact Persona</CardTitle>
          <CardDescription>
            AI-generated insights about this contact
            {persona.lastUpdated && (
              <span className="ml-2 text-xs text-gray-500">
                Updated {format(new Date(persona.lastUpdated), 'MMM d, yyyy')}
              </span>
            )}
          </CardDescription>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleEnrich}
            disabled={enrichMutation.isPending}
          >
            <Sparkles className={`h-4 w-4 mr-2 ${enrichMutation.isPending ? 'animate-spin' : ''}`} />
            Enrich
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerate}
            disabled={isGenerating || generateMutation.isPending}
          >
            <Sparkles className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <p className="text-blue-800">{persona.summary}</p>
        </div>

        {/* AI Confidence */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">AI Confidence</span>
            <span className="text-sm">{Math.round(persona.aiConfidence * 100)}%</span>
          </div>
          <Progress
            value={persona.aiConfidence * 100}
            className={getConfidenceColor(persona.aiConfidence)}
          />
        </div>

        {/* Communication Preferences */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3">Communication Preferences</h3>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {persona.communicationPreferences.preferredChannel && (
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                {getChannelIcon(persona.communicationPreferences.preferredChannel)}
                <div>
                  <p className="text-xs text-gray-500">Preferred Channel</p>
                  <p className="font-medium">
                    {persona.communicationPreferences.preferredChannel.charAt(0).toUpperCase() +
                     persona.communicationPreferences.preferredChannel.slice(1)}
                  </p>
                </div>
              </div>
            )}

            {persona.communicationPreferences.bestTimeToContact && (
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                <Clock className="h-4 w-4" />
                <div>
                  <p className="text-xs text-gray-500">Best Time</p>
                  <p className="font-medium">{persona.communicationPreferences.bestTimeToContact}</p>
                </div>
              </div>
            )}

            {persona.communicationPreferences.responseTime && (
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                <MessageSquare className="h-4 w-4" />
                <div>
                  <p className="text-xs text-gray-500">Response Time</p>
                  <p className="font-medium">
                    {persona.communicationPreferences.responseTime.charAt(0).toUpperCase() +
                     persona.communicationPreferences.responseTime.slice(1)}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Interests */}
        {persona.interests && persona.interests.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <Lightbulb className="h-4 w-4 text-blue-500" />
              <h3 className="text-sm font-medium">Interests</h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {persona.interests.map((interest, index) => (
                <Badge key={index} variant="secondary">
                  {interest}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Pain Points */}
        {persona.painPoints && persona.painPoints.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <h3 className="text-sm font-medium">Pain Points</h3>
            </div>
            <ul className="space-y-2">
              {persona.painPoints.map((point, index) => (
                <li key={index} className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                  <span>{point}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Decision Factors */}
        {persona.decisionFactors && persona.decisionFactors.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-3">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <h3 className="text-sm font-medium">Decision Factors</h3>
            </div>
            <ul className="space-y-2">
              {persona.decisionFactors.map((factor, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Target className="h-4 w-4 text-green-500 mt-0.5" />
                  <span>{factor}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
