import { <PERSON> } from "wouter";
import {
  <PERSON>,
  Users,
  Building2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Link as LinkIcon,
  Settings,
  Server,
  MessageSquare,
  FileText,
  BarChart2,
  Mail,
  GitBranch,
  LineChart,
  BarChart3,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface SidebarProps {
  currentPath: string;
}

const Sidebar: React.FC<SidebarProps> = ({ currentPath }) => {
  const isActive = (path: string) => {
    return currentPath === path || (path !== "/" && currentPath.startsWith(path));
  };

  return (
    <aside className="hidden md:flex md:flex-col w-64 bg-white border-r border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
          </svg>
          <h1 className="ml-2 text-xl font-bold text-gray-800">Aizako CRM</h1>
        </div>
      </div>

      <nav className="flex flex-col flex-1 overflow-y-auto p-2">
        <Link href="/">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <Home className="h-5 w-5 mr-3" />
            Dashboard
          </a>
        </Link>

        <Link href="/contacts">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/contacts") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <Users className="h-5 w-5 mr-3" />
            Contacts
          </a>
        </Link>

        <Link href="/companies">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/companies") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <Building2 className="h-5 w-5 mr-3" />
            Companies
          </a>
        </Link>

        <Link href="/opportunities">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/opportunities") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <TrendingUp className="h-5 w-5 mr-3" />
            Opportunities
          </a>
        </Link>

        <Link href="/ai-assistant">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/ai-assistant") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <Brain className="h-5 w-5 mr-3" />
            AI Assistant
          </a>
        </Link>

        <Link href="/ai-architecture">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/ai-architecture") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <Server className="h-5 w-5 mr-3" />
            AI Architecture
          </a>
        </Link>

        <Link href="/objection-handler">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/objection-handler") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <MessageSquare className="h-5 w-5 mr-3" />
            Objection Handler
          </a>
        </Link>

        <Link href="/proposal-generator">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/proposal-generator") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <FileText className="h-5 w-5 mr-3" />
            Proposal Generator
          </a>
        </Link>

        <Link href="/proposal-analytics">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/proposal-analytics") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <BarChart2 className="h-5 w-5 mr-3" />
            Proposal Analytics
          </a>
        </Link>

        <Link href="/email-configuration">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/email-configuration") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <Mail className="h-5 w-5 mr-3" />
            Email Configuration
          </a>
        </Link>

        <Link href="/workflow-builder">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/workflow-builder") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <GitBranch className="h-5 w-5 mr-3" />
            Workflow Builder
          </a>
        </Link>

        <Link href="/predictive-insights">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/predictive-insights") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <LineChart className="h-5 w-5 mr-3" />
            Predictive Insights
          </a>
        </Link>

        <Link href="/conversational-bi">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/conversational-bi") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <BarChart3 className="h-5 w-5 mr-3" />
            Conversational BI
          </a>
        </Link>

        <Link href="/attribution-dashboard">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/attribution-dashboard") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <PieChart className="h-5 w-5 mr-3" />
            Attribution AI
          </a>
        </Link>

        <Link href="/relationships">
          <a className={cn(
            "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg mb-1 hover:bg-gray-100",
            isActive("/relationships") && "active border-l-4 border-primary bg-blue-50"
          )}>
            <LinkIcon className="h-5 w-5 mr-3" />
            Relationships
          </a>
        </Link>

        <div className="mt-auto">
          <Link href="/settings">
            <a className={cn(
              "sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100",
              isActive("/settings") && "active border-l-4 border-primary bg-blue-50"
            )}>
              <Settings className="h-5 w-5 mr-3" />
              Settings
            </a>
          </Link>
        </div>
      </nav>
    </aside>
  );
};

export default Sidebar;
