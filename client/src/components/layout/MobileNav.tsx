import { <PERSON> } from "wouter";
import {
  Home,
  Users,
  TrendingUp,
  Brain,
  MessageSquare,
  FileText,
  GitBranch,
  LineChart,
  BarChart3,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface MobileNavProps {
  currentPath: string;
}

const MobileNav: React.FC<MobileNavProps> = ({ currentPath }) => {
  const isActive = (path: string) => {
    return currentPath === path || (path !== "/" && currentPath.startsWith(path));
  };

  return (
    <div className="fixed inset-x-0 bottom-0 md:hidden bg-white border-t border-gray-200 z-10">
      <nav className="flex justify-around">
        <Link href="/">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/") && "active border-b-4 border-primary"
          )}>
            <Home className="h-6 w-6" />
            <span className="text-xs">Home</span>
          </a>
        </Link>

        <Link href="/contacts">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/contacts") && "active border-b-4 border-primary"
          )}>
            <Users className="h-6 w-6" />
            <span className="text-xs">Contacts</span>
          </a>
        </Link>

        <Link href="/opportunities">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/opportunities") && "active border-b-4 border-primary"
          )}>
            <TrendingUp className="h-6 w-6" />
            <span className="text-xs">Deals</span>
          </a>
        </Link>

        <Link href="/ai-assistant">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/ai-assistant") && "active border-b-4 border-primary"
          )}>
            <Brain className="h-6 w-6" />
            <span className="text-xs">AI</span>
          </a>
        </Link>

        <Link href="/objection-handler">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/objection-handler") && "active border-b-4 border-primary"
          )}>
            <MessageSquare className="h-6 w-6" />
            <span className="text-xs">Objections</span>
          </a>
        </Link>

        <Link href="/proposal-generator">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/proposal-generator") && "active border-b-4 border-primary"
          )}>
            <FileText className="h-6 w-6" />
            <span className="text-xs">Proposals</span>
          </a>
        </Link>

        <Link href="/workflow-builder">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/workflow-builder") && "active border-b-4 border-primary"
          )}>
            <GitBranch className="h-6 w-6" />
            <span className="text-xs">Workflows</span>
          </a>
        </Link>

        <Link href="/predictive-insights">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/predictive-insights") && "active border-b-4 border-primary"
          )}>
            <LineChart className="h-6 w-6" />
            <span className="text-xs">Insights</span>
          </a>
        </Link>

        <Link href="/conversational-bi">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/conversational-bi") && "active border-b-4 border-primary"
          )}>
            <BarChart3 className="h-6 w-6" />
            <span className="text-xs">BI</span>
          </a>
        </Link>

        <Link href="/attribution-dashboard">
          <a className={cn(
            "sidebar-item flex flex-col items-center py-2 flex-1 text-center",
            isActive("/attribution-dashboard") && "active border-b-4 border-primary"
          )}>
            <PieChart className="h-6 w-6" />
            <span className="text-xs">Attribution</span>
          </a>
        </Link>
      </nav>
    </div>
  );
};

export default MobileNav;
