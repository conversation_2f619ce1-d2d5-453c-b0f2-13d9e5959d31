import React from "react";
import Sidebar from "./Sidebar";
import Header from "./Header";
import MobileNav from "./MobileNav";
import { useAuth } from "@/lib/auth";
import { useLocation, useRoute } from "wouter";

const AppShell: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const [location] = useLocation();
  const [isSignInPage] = useRoute("/signin");
  const [isSignUpPage] = useRoute("/signup");

  // Don't show the app shell on auth pages
  if (isSignInPage || isSignUpPage) {
    return <>{children}</>;
  }

  // If not authenticated and not loading, redirect to sign in
  if (!isAuthenticated && !isLoading) {
    window.location.href = "/signin";
    return null;
  }

  // Show loading while checking auth status
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 text-gray-800">
      {/* Desktop Sidebar */}
      <Sidebar currentPath={location} />

      {/* Main Content */}
      <main className="flex-1 overflow-y-auto pb-16 md:pb-0">
        <Header />
        {children}
      </main>

      {/* Mobile Navigation */}
      <MobileNav currentPath={location} />
    </div>
  );
};

export default AppShell;
