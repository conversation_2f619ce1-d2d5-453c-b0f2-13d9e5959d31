import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart2, 
  Calendar, 
  DollarSign, 
  ThumbsUp, 
  ThumbsDown, 
  Sparkles, 
  ArrowRight,
  Building,
  User
} from 'lucide-react';
import { WinLossAnalysisDetail } from './WinLossAnalysisDetail';

interface WinLossAnalysisViewProps {
  analyses: Array<{
    _id: string;
    title: string;
    description?: string;
    opportunityId: {
      _id: string;
      name: string;
      value: number;
      currency: string;
      stage: string;
    };
    contactId?: {
      _id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    companyId?: {
      _id: string;
      name: string;
      industry: string;
    };
    outcome: 'won' | 'lost';
    value: number;
    currency: string;
    closedDate: string;
    salesCycle: number;
    isAIGenerated: boolean;
    createdAt: string;
  }>;
  isLoading: boolean;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
}

export function WinLossAnalysisView({
  analyses,
  isLoading,
  opportunityId,
  contactId,
  companyId
}: WinLossAnalysisViewProps) {
  const [selectedAnalysisId, setSelectedAnalysisId] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<string>('all');
  
  // Filter analyses based on the selected tab
  const filteredAnalyses = analyses.filter(analysis => {
    if (selectedTab === 'won') return analysis.outcome === 'won';
    if (selectedTab === 'lost') return analysis.outcome === 'lost';
    return true;
  });
  
  // Format currency
  const formatCurrency = (value: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      maximumFractionDigits: 0,
    }).format(value);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };
  
  // Get the selected analysis
  const selectedAnalysis = selectedAnalysisId 
    ? analyses.find(a => a._id === selectedAnalysisId) 
    : null;
  
  if (selectedAnalysis) {
    return (
      <WinLossAnalysisDetail 
        analysisId={selectedAnalysis._id} 
        onBack={() => setSelectedAnalysisId(null)} 
      />
    );
  }
  
  return (
    <div>
      <Tabs defaultValue="all" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="won">Won</TabsTrigger>
          <TabsTrigger value="lost">Lost</TabsTrigger>
        </TabsList>
        
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        ) : filteredAnalyses.length > 0 ? (
          <div className="space-y-4">
            {filteredAnalyses.map((analysis) => (
              <Card key={analysis._id} className="relative">
                {analysis.isAIGenerated && (
                  <Badge className="absolute top-2 right-2 bg-purple-100 text-purple-800 border-purple-300">
                    <Sparkles className="h-3 w-3 mr-1" />
                    AI Generated
                  </Badge>
                )}
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{analysis.title}</CardTitle>
                      <CardDescription>
                        {analysis.opportunityId.name} • {formatDate(analysis.closedDate)}
                      </CardDescription>
                    </div>
                    <Badge className={`
                      ${analysis.outcome === 'won' 
                        ? 'bg-green-100 text-green-800 border-green-300' 
                        : 'bg-red-100 text-red-800 border-red-300'
                      }
                    `}>
                      {analysis.outcome === 'won' 
                        ? <ThumbsUp className="h-3 w-3 mr-1" /> 
                        : <ThumbsDown className="h-3 w-3 mr-1" />
                      }
                      {analysis.outcome.charAt(0).toUpperCase() + analysis.outcome.slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 mb-2">
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-sm">{formatCurrency(analysis.value, analysis.currency)}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-sm">{analysis.salesCycle} days sales cycle</span>
                    </div>
                    <div className="flex items-center">
                      {analysis.companyId ? (
                        <>
                          <Building className="h-4 w-4 mr-1 text-gray-400" />
                          <span className="text-sm">{analysis.companyId.name}</span>
                        </>
                      ) : analysis.contactId ? (
                        <>
                          <User className="h-4 w-4 mr-1 text-gray-400" />
                          <span className="text-sm">{analysis.contactId.firstName} {analysis.contactId.lastName}</span>
                        </>
                      ) : (
                        <>
                          <BarChart2 className="h-4 w-4 mr-1 text-gray-400" />
                          <span className="text-sm">Analysis</span>
                        </>
                      )}
                    </div>
                  </div>
                  {analysis.description && (
                    <p className="text-sm text-gray-700 line-clamp-2">{analysis.description}</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="ml-auto"
                    onClick={() => setSelectedAnalysisId(analysis._id)}
                  >
                    View Details
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8">
            <BarChart2 className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium">No analyses found</h3>
            <p className="text-sm text-gray-500 mt-1">
              {opportunityId 
                ? "Generate an analysis for this opportunity to get started." 
                : "Select an opportunity to generate an analysis."}
            </p>
          </div>
        )}
      </Tabs>
    </div>
  );
}
