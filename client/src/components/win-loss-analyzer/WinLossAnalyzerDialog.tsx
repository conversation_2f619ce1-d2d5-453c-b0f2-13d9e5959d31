import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart2, Loader2, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  generateWinLossAnalysis,
  getWinLossAnalyses,
  getWinLossStatistics
} from '@/api/win-loss-analyzer-api';
import { WinLossAnalysisView } from './WinLossAnalysisView';
import { WinLossStatistics } from './WinLossStatistics';

interface WinLossAnalyzerDialogProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

export function WinLossAnalyzerDialog({
  opportunityId,
  contactId,
  companyId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Win/Loss Analyzer'
}: WinLossAnalyzerDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>('analyses');
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Fetch analyses based on the filters
  const {
    data: analyses,
    isLoading: isAnalysesLoading,
    refetch: refetchAnalyses
  } = useQuery({
    queryKey: ['win-loss-analyses', opportunityId, contactId, companyId],
    queryFn: () => getWinLossAnalyses({
      opportunityId,
      contactId,
      companyId
    }),
    enabled: isOpen
  });
  
  // Fetch statistics
  const {
    data: statistics,
    isLoading: isStatisticsLoading,
    refetch: refetchStatistics
  } = useQuery({
    queryKey: ['win-loss-statistics', companyId],
    queryFn: () => getWinLossStatistics({
      companyId
    }),
    enabled: isOpen && selectedTab === 'statistics'
  });
  
  // Generate analysis mutation
  const generateAnalysisMutation = useMutation({
    mutationFn: generateWinLossAnalysis,
    onSuccess: () => {
      setIsGenerating(false);
      toast({
        title: 'Success',
        description: 'Win/Loss analysis generated successfully',
      });
      refetchAnalyses();
    },
    onError: (error) => {
      setIsGenerating(false);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate win/loss analysis',
        variant: 'destructive',
      });
    }
  });
  
  // Handle generating analysis
  const handleGenerateAnalysis = () => {
    if (!opportunityId) {
      toast({
        title: 'Error',
        description: 'Opportunity ID is required to generate an analysis',
        variant: 'destructive',
      });
      return;
    }
    
    setIsGenerating(true);
    generateAnalysisMutation.mutate(opportunityId);
  };
  
  // Get the title for the dialog
  const getDialogTitle = () => {
    if (opportunityName) return `Win/Loss Analysis for ${opportunityName}`;
    if (contactName) return `Win/Loss Analysis for ${contactName}`;
    if (companyName) return `Win/Loss Analysis for ${companyName}`;
    return 'Win/Loss Analyzer';
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size}>
          <BarChart2 className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          <DialogDescription>
            Analyze won and lost opportunities to identify patterns and improve your sales process.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="analyses" value={selectedTab} onValueChange={setSelectedTab}>
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="analyses">Analyses</TabsTrigger>
              <TabsTrigger value="statistics">Statistics</TabsTrigger>
            </TabsList>
            
            {opportunityId && (
              <Button
                onClick={handleGenerateAnalysis}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Analysis
                  </>
                )}
              </Button>
            )}
          </div>
          
          <TabsContent value="analyses" className="space-y-4">
            <WinLossAnalysisView 
              analyses={analyses || []} 
              isLoading={isAnalysesLoading} 
              opportunityId={opportunityId}
              contactId={contactId}
              companyId={companyId}
            />
          </TabsContent>
          
          <TabsContent value="statistics" className="space-y-4">
            <WinLossStatistics 
              statistics={statistics} 
              isLoading={isStatisticsLoading}
              companyName={companyName}
            />
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
