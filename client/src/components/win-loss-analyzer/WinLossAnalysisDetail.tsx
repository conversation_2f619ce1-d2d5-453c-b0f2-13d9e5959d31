import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart2,
  Calendar,
  DollarSign,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
  ArrowLeft,
  Building,
  User,
  CheckCircle,
  XCircle,
  Lightbulb,
  AlertTriangle
} from 'lucide-react';
import { getWinLossAnalysisById } from '@/api/win-loss-analyzer-api';

interface WinLossAnalysisDetailProps {
  analysisId: string;
  onBack: () => void;
}

export function WinLossAnalysisDetail({
  analysisId,
  onBack
}: WinLossAnalysisDetailProps) {
  // Fetch analysis details
  const {
    data: analysis,
    isLoading
  } = useQuery({
    queryKey: ['win-loss-analysis', analysisId],
    queryFn: () => getWinLossAnalysisById(analysisId)
  });

  // Format currency
  const formatCurrency = (value?: number, currency?: string) => {
    if (value === undefined) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="sm" onClick={onBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (!analysis) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center py-8">
              <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
              <h3 className="text-lg font-medium">Analysis not found</h3>
              <p className="text-sm text-gray-500 mt-1">
                The requested analysis could not be loaded.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <Badge className={`
          ${analysis.outcome === 'won'
            ? 'bg-green-100 text-green-800 border-green-300'
            : 'bg-red-100 text-red-800 border-red-300'
          }
        `}>
          {analysis.outcome === 'won'
            ? <ThumbsUp className="h-3 w-3 mr-1" />
            : <ThumbsDown className="h-3 w-3 mr-1" />
          }
          {analysis.outcome.charAt(0).toUpperCase() + analysis.outcome.slice(1)}
        </Badge>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{analysis.title}</CardTitle>
              <CardDescription>
                {analysis.opportunityId?.name} • {formatDate(analysis.closedDate)}
              </CardDescription>
            </div>
            {analysis.isAIGenerated && (
              <Badge className="bg-purple-100 text-purple-800 border-purple-300">
                <Sparkles className="h-3 w-3 mr-1" />
                AI Generated
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Summary */}
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">Summary</h3>
            <p className="text-sm">{analysis.description}</p>
          </div>

          {/* Key Details */}
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">Key Details</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-xs text-gray-500">Value</p>
                <p className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                  {formatCurrency(analysis.value, analysis.currency)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Sales Cycle</p>
                <p className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                  {analysis.salesCycle} days
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Closed Date</p>
                <p className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                  {formatDate(analysis.closedDate)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Stage</p>
                <p className="flex items-center">
                  <BarChart2 className="h-4 w-4 mr-1 text-gray-400" />
                  {analysis.stage}
                </p>
              </div>
            </div>
          </div>

          {/* Key Factors */}
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">Key Factors</h3>
            <div className="space-y-2">
              {analysis.keyFactors?.map((factor: any, index: number) => (
                <div key={index} className="p-3 bg-gray-50 rounded-md">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center">
                      {factor.impact === 'positive' ? (
                        <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 mr-2 text-red-500" />
                      )}
                      <span className="font-medium">{factor.factor}</span>
                    </div>
                    <Badge className={`
                      ${factor.impact === 'positive'
                        ? 'bg-green-100 text-green-800 border-green-300'
                        : 'bg-red-100 text-red-800 border-red-300'
                      }
                    `}>
                      Impact: {factor.weight}/10
                    </Badge>
                  </div>
                  {factor.description && (
                    <p className="text-sm mt-1 ml-6">{factor.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Competitor Info */}
          {analysis.competitorInfo && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Competitor Information</h3>
              <div className="p-4 bg-gray-50 rounded-md">
                {analysis.competitorInfo.name && (
                  <p className="font-medium mb-2">{analysis.competitorInfo.name}</p>
                )}

                {analysis.competitorInfo.strengths && analysis.competitorInfo.strengths.length > 0 && (
                  <div className="mb-2">
                    <p className="text-sm font-medium text-gray-700">Strengths:</p>
                    <ul className="list-disc list-inside text-sm">
                      {analysis.competitorInfo.strengths.map((strength: any, index: number) => (
                        <li key={index}>{strength}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {analysis.competitorInfo.weaknesses && analysis.competitorInfo.weaknesses.length > 0 && (
                  <div className="mb-2">
                    <p className="text-sm font-medium text-gray-700">Weaknesses:</p>
                    <ul className="list-disc list-inside text-sm">
                      {analysis.competitorInfo.weaknesses.map((weakness: any, index: number) => (
                        <li key={index}>{weakness}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {analysis.competitorInfo.pricingDifference !== undefined && (
                  <p className="text-sm">
                    <span className="font-medium">Pricing Difference: </span>
                    {analysis.competitorInfo.pricingDifference > 0
                      ? `${analysis.competitorInfo.pricingDifference}% higher than our price`
                      : analysis.competitorInfo.pricingDifference < 0
                        ? `${Math.abs(analysis.competitorInfo.pricingDifference)}% lower than our price`
                        : 'Same price as our offering'
                    }
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Learnings and Recommendations */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Key Learnings</h3>
              <div className="p-4 bg-gray-50 rounded-md">
                <ul className="space-y-2">
                  {analysis.learnings?.map((learning: any, index: number) => (
                    <li key={index} className="flex items-start">
                      <Lightbulb className="h-4 w-4 mr-2 text-amber-500 mt-0.5" />
                      <span className="text-sm">{learning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Recommendations</h3>
              <div className="p-4 bg-gray-50 rounded-md">
                <ul className="space-y-2">
                  {analysis.recommendations?.map((recommendation: any, index: number) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-4 w-4 mr-2 text-blue-500 mt-0.5" />
                      <span className="text-sm">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Feedback */}
          {analysis.feedback && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Feedback</h3>
              <div className="p-4 bg-gray-50 rounded-md">
                <p className="text-sm">{analysis.feedback}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
