import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { BarChart2, Loader2 } from 'lucide-react';
import { WinLossAnalyzerDialog } from './WinLossAnalyzerDialog';

interface WinLossAnalyzerButtonProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

export function WinLossAnalyzerButton({
  opportunityId,
  contactId,
  companyId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Win/Loss Analyzer'
}: WinLossAnalyzerButtonProps) {
  return (
    <WinLossAnalyzerDialog
      opportunityId={opportunityId}
      contactId={contactId}
      companyId={companyId}
      opportunityName={opportunityName}
      contactName={contactName}
      companyName={companyName}
      variant={variant}
      size={size}
      buttonText={buttonText}
    />
  );
}
