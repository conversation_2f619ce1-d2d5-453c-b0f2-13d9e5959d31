import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  BarChart2, 
  DollarSign, 
  Calendar, 
  ThumbsUp, 
  ThumbsDown, 
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

interface WinLossStatisticsProps {
  statistics?: {
    totalDeals: number;
    wonDeals: number;
    lostDeals: number;
    winRate: number;
    totalValue: number;
    avgDealSize: number;
    avgSalesCycle: number;
    topWinFactors: Array<{ factor: string; frequency: number; impact: number }>;
    topLossFactors: Array<{ factor: string; frequency: number; impact: number }>;
  };
  isLoading: boolean;
  companyName?: string;
}

export function WinLossStatistics({
  statistics,
  isLoading,
  companyName
}: WinLossStatisticsProps) {
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(value);
  };
  
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      </div>
    );
  }
  
  if (!statistics) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
        <h3 className="text-lg font-medium">No statistics available</h3>
        <p className="text-sm text-gray-500 mt-1">
          {companyName 
            ? `No win/loss data available for ${companyName}.` 
            : "No win/loss data available."}
        </p>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Win Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
              <span className="text-2xl font-bold">{statistics.winRate.toFixed(1)}%</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {statistics.wonDeals} won / {statistics.totalDeals} total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Average Deal Size</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-green-500" />
              <span className="text-2xl font-bold">{formatCurrency(statistics.avgDealSize)}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {formatCurrency(statistics.totalValue)} total value
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Average Sales Cycle</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-purple-500" />
              <span className="text-2xl font-bold">{Math.round(statistics.avgSalesCycle)} days</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              From creation to close
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Deals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BarChart2 className="h-5 w-5 mr-2 text-orange-500" />
              <span className="text-2xl font-bold">{statistics.totalDeals}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {statistics.wonDeals} won, {statistics.lostDeals} lost
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Top Factors */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ThumbsUp className="h-5 w-5 mr-2 text-green-500" />
              Top Win Factors
            </CardTitle>
            <CardDescription>
              Factors that contributed most to winning deals
            </CardDescription>
          </CardHeader>
          <CardContent>
            {statistics.topWinFactors.length > 0 ? (
              <div className="space-y-3">
                {statistics.topWinFactors.map((factor, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-8 text-center text-sm font-medium text-gray-500">
                      #{index + 1}
                    </div>
                    <div className="flex-1 ml-2">
                      <div className="text-sm font-medium">{factor.factor}</div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-green-500 h-2.5 rounded-full" 
                          style={{ width: `${Math.min(100, factor.impact * 10)}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-2 text-sm text-gray-500">
                      {factor.frequency}x
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-4">
                <p className="text-sm text-gray-500">
                  No win factors available
                </p>
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ThumbsDown className="h-5 w-5 mr-2 text-red-500" />
              Top Loss Factors
            </CardTitle>
            <CardDescription>
              Factors that contributed most to losing deals
            </CardDescription>
          </CardHeader>
          <CardContent>
            {statistics.topLossFactors.length > 0 ? (
              <div className="space-y-3">
                {statistics.topLossFactors.map((factor, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-8 text-center text-sm font-medium text-gray-500">
                      #{index + 1}
                    </div>
                    <div className="flex-1 ml-2">
                      <div className="text-sm font-medium">{factor.factor}</div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-red-500 h-2.5 rounded-full" 
                          style={{ width: `${Math.min(100, factor.impact * 10)}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-2 text-sm text-gray-500">
                      {factor.frequency}x
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-4">
                <p className="text-sm text-gray-500">
                  No loss factors available
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
