import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { apiRequest } from "@/api/api-utils";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

// Import types and schemas from the centralized type system
import { CompanyFormValues } from "@shared/types/forms";
// Add id property to CompanyFormValues for editing
interface ExtendedCompanyFormValues extends CompanyFormValues {
  id?: number;
}
import { Company } from "@shared/types/core";
import { CreateCompanyRequest, UpdateCompanyRequest } from "@shared/types/api";
import { companyFormSchema } from "@schemas/forms";
import { validateData } from "@shared/utils/validation";

interface CompanyFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialData?: ExtendedCompanyFormValues;
  isEditing?: boolean;
}

const CompanyForm: React.FC<CompanyFormProps> = ({
  open,
  onOpenChange,
  initialData,
  isEditing = false,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Default values for the form
  const defaultValues: Partial<ExtendedCompanyFormValues> = {
    name: "",
    industry: "",
    website: "",
    employees: undefined,
    status: "lead",
    notes: "",
    ...initialData,
  };

  // Initialize form with Zod resolver
  const form = useForm<ExtendedCompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues,
  });

  // Create company mutation
  const createCompany = useMutation({
    mutationFn: async (data: CreateCompanyRequest) => {
      return apiRequest<{ company: Company }>({
        url: "/api/companies",
        method: "POST",
        data,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      toast({
        title: "Company created",
        description: "The company has been created successfully.",
      });
      form.reset();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create company.",
        variant: "destructive",
      });
    },
  });

  // Update company mutation
  const updateCompany = useMutation({
    mutationFn: async (data: UpdateCompanyRequest) => {
      return apiRequest<{ company: Company }>({
        url: `/api/companies/${data.id}`,
        method: "PATCH",
        data,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      toast({
        title: "Company updated",
        description: "The company has been updated successfully.",
      });
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update company.",
        variant: "destructive",
      });
    },
  });

  // Form submission handler
  const onSubmit = (data: ExtendedCompanyFormValues) => {
    // Validate data using our utility
    const validation = validateData(companyFormSchema, data);

    if (!validation.success) {
      toast({
        title: "Validation Error",
        description: validation.error || "Please check the form for errors.",
        variant: "destructive",
      });
      return;
    }

    // Handle employees field - convert string to number if needed
    const employees = typeof data.employees === 'string'
      ? data.employees === '' ? undefined : parseInt(data.employees, 10)
      : data.employees;

    const formattedData = {
      ...data,
      employees,
    };

    if (isEditing && initialData && initialData.id) {
      updateCompany.mutate({
        id: initialData.id,
        ...formattedData,
      });
    } else {
      createCompany.mutate(formattedData);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Company" : "Add New Company"}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name*</FormLabel>
                  <FormControl>
                    <Input placeholder="Acme Inc" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry</FormLabel>
                    <FormControl>
                      <Input placeholder="Technology" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="lead">Lead</SelectItem>
                        <SelectItem value="prospect">Prospect</SelectItem>
                        <SelectItem value="customer">Customer</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input placeholder="https://www.example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="employees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Number of Employees</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="100"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value === '' ? '' : parseInt(e.target.value, 10))}
                        value={field.value === undefined ? '' : field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any relevant information about this company"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                variant="outline"
                type="button"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createCompany.isPending || updateCompany.isPending}
              >
                {createCompany.isPending || updateCompany.isPending ? (
                  "Saving..."
                ) : isEditing ? (
                  "Update Company"
                ) : (
                  "Add Company"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CompanyForm;
