// Custom Toaster component
import React from "react";
import { Toast, ToastProvider, ToastViewport } from "./toast";
import { useToast } from "@/hooks/use-toast";

interface ToasterProps {
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
  closeButton?: boolean;
}

export function Toaster({
  position = "bottom-right",
  closeButton = true,
  ...props
}: ToasterProps) {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <div className="text-sm font-semibold">{title}</div>}
              {description && (
                <div className="text-sm opacity-90">{description}</div>
              )}
            </div>
            {action}
          </Toast>
        );
      })}
      <ToastViewport />
    </ToastProvider>
  );
}
