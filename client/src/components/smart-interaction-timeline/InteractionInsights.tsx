import { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Sparkles,
  BarChart3,
  PieChart,
  TrendingUp,
  Clock,
  Calendar,
  MessageSquare,
  Mail,
  Phone,
  AlertCircle,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { format, parseISO, subDays } from 'date-fns';
import type { Interaction, InteractionType, InteractionInsights as IInteractionInsights } from './SmartInteractionTimeline.d';

// Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface InteractionInsightsProps {
  interactions: Interaction[];
  contactId: string | undefined;
}

interface InsightData {
  summary: string;
  interactionsByType: Record<InteractionType, number>;
  interactionsByMonth: Record<string, number>;
  sentimentDistribution: Record<string, number>;
  responseTime: {
    average: number;
    trend: Array<{ date: string; value: number }>;
  };
  topTopics: Array<{ topic: string; count: number }>;
  recommendations: string[];
}

export const InteractionInsights = ({ interactions, contactId }: InteractionInsightsProps) => {
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);

  // Fetch insights
  const {
    data: insights,
    isLoading,
    error
  } = useQuery({
    queryKey: ['interaction-insights', contactId],
    queryFn: async () => {
      if (!contactId) throw new Error('Contact ID is required');
      return mongoApiClient.interactions.getInsights(contactId);
    },
    enabled: !!contactId
  });

  // Generate insights mutation
  const generateMutation = useMutation({
    mutationFn: async () => {
      if (!contactId) throw new Error('Contact ID is required');

      return mongoApiClient.interactions.generateInsights(contactId);
    },
    onSuccess: (data) => {
      toast({
        title: 'Insights generated',
        description: 'New interaction insights have been generated.',
        variant: 'default',
      });
      setIsGenerating(false);
    },
    onError: (error) => {
      toast({
        title: 'Failed to generate insights',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
      setIsGenerating(false);
    }
  });

  // Handle generate button click
  const handleGenerate = () => {
    setIsGenerating(true);
    generateMutation.mutate();
  };

  // Prepare chart data for interactions by type
  const interactionsByTypeData = {
    labels: insights?.interactionsByType ? Object.keys(insights.interactionsByType).map(
      type => type.charAt(0).toUpperCase() + type.slice(1)
    ) : [],
    datasets: [
      {
        label: 'Interactions',
        data: insights?.interactionsByType ? Object.values(insights.interactionsByType) : [],
        backgroundColor: [
          'rgba(54, 162, 235, 0.6)', // Email
          'rgba(75, 192, 192, 0.6)', // Call
          'rgba(153, 102, 255, 0.6)', // Meeting
          'rgba(255, 206, 86, 0.6)', // Note
          'rgba(255, 159, 64, 0.6)', // SMS
          'rgba(54, 162, 235, 0.6)', // Social
          'rgba(255, 99, 132, 0.6)', // Task
          'rgba(201, 203, 207, 0.6)', // Document
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(255, 159, 64, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(201, 203, 207, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Prepare chart data for sentiment distribution
  const sentimentData = {
    labels: insights?.sentimentDistribution ? Object.keys(insights.sentimentDistribution).map(
      sentiment => sentiment.charAt(0).toUpperCase() + sentiment.slice(1)
    ) : [],
    datasets: [
      {
        label: 'Sentiment',
        data: insights?.sentimentDistribution ? Object.values(insights.sentimentDistribution) : [],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)', // Positive
          'rgba(201, 203, 207, 0.6)', // Neutral
          'rgba(255, 99, 132, 0.6)', // Negative
          'rgba(255, 206, 86, 0.6)', // Mixed
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(201, 203, 207, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 206, 86, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Prepare chart data for interactions by month
  const interactionsByMonthData = {
    labels: insights?.interactionsByMonth ? Object.keys(insights.interactionsByMonth) : [],
    datasets: [
      {
        label: 'Interactions',
        data: insights?.interactionsByMonth ? Object.values(insights.interactionsByMonth) : [],
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Prepare chart data for response time trend
  const responseTimeData = {
    labels: insights?.responseTime?.trend ? insights.responseTime.trend.map(item => item.date) : [],
    datasets: [
      {
        label: 'Response Time (hours)',
        data: insights?.responseTime?.trend ? insights.responseTime.trend.map(item => item.value) : [],
        fill: false,
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        tension: 0.1,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle className="text-xl">Interaction Insights</CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-48" />
            </CardDescription>
          </div>
          <Skeleton className="h-9 w-24" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-500">
            <AlertCircle className="h-12 w-12 mx-auto mb-4" />
            <p>Error loading insights: {error instanceof Error ? error.message : 'Unknown error'}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={handleGenerate}
              disabled={isGenerating || generateMutation.isPending}
            >
              <Sparkles className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
              Generate Insights
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If no insights available, show generate button
  if (!insights) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Sparkles className="h-12 w-12 mx-auto mb-4 text-blue-400" />
            <p className="mb-2">No insights available yet</p>
            <p className="text-sm text-gray-500 mb-6">
              Generate AI-powered insights to better understand your interactions with this contact.
            </p>
            <Button
              onClick={handleGenerate}
              disabled={isGenerating || generateMutation.isPending}
            >
              <Sparkles className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
              Generate Insights
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-xl">Interaction Insights</CardTitle>
          <CardDescription>
            AI-powered analysis of your communication patterns
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleGenerate}
          disabled={isGenerating || generateMutation.isPending}
        >
          <Sparkles className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        {/* Summary */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <p className="text-blue-800">{insights.summary}</p>
        </div>

        {/* Charts */}
        <Tabs defaultValue="activity" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="activity">
              <BarChart3 className="h-4 w-4 mr-2" />
              Activity
            </TabsTrigger>
            <TabsTrigger value="sentiment">
              <PieChart className="h-4 w-4 mr-2" />
              Sentiment
            </TabsTrigger>
            <TabsTrigger value="trends">
              <TrendingUp className="h-4 w-4 mr-2" />
              Trends
            </TabsTrigger>
            <TabsTrigger value="recommendations">
              <Sparkles className="h-4 w-4 mr-2" />
              Recommendations
            </TabsTrigger>
          </TabsList>

          {/* Activity Tab */}
          <TabsContent value="activity">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-4 rounded-lg border">
                <h3 className="text-sm font-medium mb-4">Interactions by Type</h3>
                <div className="h-64">
                  <Bar data={interactionsByTypeData} options={chartOptions} />
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg border">
                <h3 className="text-sm font-medium mb-4">Interactions by Month</h3>
                <div className="h-64">
                  <Bar data={interactionsByMonthData} options={chartOptions} />
                </div>
              </div>

              {insights.topTopics && insights.topTopics.length > 0 && (
                <div className="bg-white p-4 rounded-lg border md:col-span-2">
                  <h3 className="text-sm font-medium mb-4">Top Discussion Topics</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {insights.topTopics.map((topic, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <p className="font-medium">{topic.topic}</p>
                        <p className="text-sm text-gray-500">{topic.count} mentions</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Sentiment Tab */}
          <TabsContent value="sentiment">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-4 rounded-lg border">
                <h3 className="text-sm font-medium mb-4">Sentiment Distribution</h3>
                <div className="h-64">
                  <Pie data={sentimentData} options={chartOptions} />
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg border">
                <h3 className="text-sm font-medium mb-4">Sentiment Highlights</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <ThumbsUp className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-green-700">Positive Interactions</p>
                      <p className="text-sm text-gray-600">
                        {insights.sentimentDistribution?.positive || 0} interactions with positive sentiment
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <ThumbsDown className="h-5 w-5 text-red-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-red-700">Negative Interactions</p>
                      <p className="text-sm text-gray-600">
                        {insights.sentimentDistribution?.negative || 0} interactions with negative sentiment
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-yellow-700">Mixed Sentiment</p>
                      <p className="text-sm text-gray-600">
                        {insights.sentimentDistribution?.mixed || 0} interactions with mixed sentiment
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Trends Tab */}
          <TabsContent value="trends">
            <div className="grid grid-cols-1 gap-6">
              {insights.responseTime && (
                <div className="bg-white p-4 rounded-lg border">
                  <h3 className="text-sm font-medium mb-2">Response Time Trend</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Average response time: {insights.responseTime.average.toFixed(1)} hours
                  </p>
                  <div className="h-64">
                    <Line data={responseTimeData} options={chartOptions} />
                  </div>
                </div>
              )}

              <div className="bg-white p-4 rounded-lg border">
                <h3 className="text-sm font-medium mb-4">Communication Channels</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Mail className="h-5 w-5 text-blue-600 mr-2" />
                      <p className="font-medium">Email</p>
                    </div>
                    <p className="text-2xl font-bold">{insights.interactionsByType?.email || 0}</p>
                  </div>

                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Phone className="h-5 w-5 text-green-600 mr-2" />
                      <p className="font-medium">Calls</p>
                    </div>
                    <p className="text-2xl font-bold">{insights.interactionsByType?.call || 0}</p>
                  </div>

                  <div className="bg-purple-50 p-3 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Calendar className="h-5 w-5 text-purple-600 mr-2" />
                      <p className="font-medium">Meetings</p>
                    </div>
                    <p className="text-2xl font-bold">{insights.interactionsByType?.meeting || 0}</p>
                  </div>

                  <div className="bg-sky-50 p-3 rounded-lg">
                    <div className="flex items-center mb-2">
                      <MessageSquare className="h-5 w-5 text-sky-600 mr-2" />
                      <p className="font-medium">Messages</p>
                    </div>
                    <p className="text-2xl font-bold">
                      {(insights.interactionsByType?.social || 0)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Recommendations Tab */}
          <TabsContent value="recommendations">
            <div className="bg-white p-4 rounded-lg border">
              <h3 className="text-sm font-medium mb-4">AI Recommendations</h3>
              {insights.recommendations && insights.recommendations.length > 0 ? (
                <ul className="space-y-3">
                  {insights.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                      <Sparkles className="h-5 w-5 text-blue-500 mt-0.5" />
                      <p>{recommendation}</p>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-center text-gray-500 py-4">No recommendations available</p>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
