import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { format } from 'date-fns';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/hooks/use-toast";
import { Calendar, Clock, Mail, Phone, MessageSquare, FileText, AlertCircle, CheckCircle, RefreshCw } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchInteractions, syncInteractions } from '@/lib/api';
import { Interaction } from '@/types/interactions';
import { FilterOptions } from '@/types/filters';

const SmartInteractionTimeline: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('all');
  const [filters, setFilters] = useState<FilterOptions>({});
  const [searchInput, setSearchInput] = useState('');
  const queryClient = useQueryClient();

  // Fetch interactions
  const { data: interactions, isLoading, error } = useQuery({
    queryKey: ['interactions', id, filters],
    queryFn: () => fetchInteractions(id!, filters),
    enabled: !!id,
  });

  // Sync interactions mutation
  const syncMutation = useMutation({
    mutationFn: () => syncInteractions(id!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['interactions', id] });
      toast({
        title: "Interactions synced",
        description: "Your interaction timeline has been updated with the latest data.",
      });
    },
    onError: (error) => {
      toast({
        title: "Sync failed",
        description: "Failed to sync interactions. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Handle search
  const handleSearch = () => {
    setFilters(prev => ({ ...prev, search: searchInput }));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle tab changes
  useEffect(() => {
    if (activeTab === 'all') {
      setFilters(prev => ({ ...prev, types: undefined }));
    } else if (activeTab === 'email') {
      setFilters(prev => ({ ...prev, types: ['email'] }));
    } else if (activeTab === 'call') {
      setFilters(prev => ({ ...prev, types: ['call'] }));
    } else if (activeTab === 'meeting') {
      setFilters(prev => ({ ...prev, types: ['meeting'] }));
    } else if (activeTab === 'other') {
      setFilters(prev => ({ ...prev, types: ['chat', 'social', 'note', 'task', 'other'] }));
    }
  }, [activeTab]);

  // Get icon for interaction type
  const getInteractionIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'call':
        return <Phone className="h-4 w-4" />;
      case 'meeting':
        return <Calendar className="h-4 w-4" />;
      case 'chat':
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  // Get color for sentiment
  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment) {
      case 'positive':
        return 'bg-green-100 text-green-800';
      case 'negative':
        return 'bg-red-100 text-red-800';
      case 'neutral':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Smart Interaction Timeline</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => syncMutation.mutate()}
            disabled={syncMutation.isPending}
          >
            {syncMutation.isPending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync
              </>
            )}
          </Button>
        </CardTitle>
        <CardDescription>
          AI-powered timeline of all interactions with this contact
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search and filters */}
          <div className="flex space-x-2">
            <Input
              placeholder="Search interactions..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="flex-1"
            />
            <Button onClick={handleSearch}>Search</Button>
          </div>

          <div className="flex space-x-2">
            <Select
              value={filters.sentiment || ''}
              onValueChange={(value) => handleFilterChange('sentiment', value || undefined)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sentiment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Sentiments</SelectItem>
                <SelectItem value="positive">Positive</SelectItem>
                <SelectItem value="neutral">Neutral</SelectItem>
                <SelectItem value="negative">Negative</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.sources?.join(',') || ''}
              onValueChange={(value) => handleFilterChange('sources', value ? value.split(',') : undefined)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Sources</SelectItem>
                <SelectItem value="gmail">Gmail</SelectItem>
                <SelectItem value="outlook">Outlook</SelectItem>
                <SelectItem value="google-calendar">Google Calendar</SelectItem>
                <SelectItem value="office365-calendar">Office 365 Calendar</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-5 w-full">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="email">Email</TabsTrigger>
              <TabsTrigger value="call">Call</TabsTrigger>
              <TabsTrigger value="meeting">Meeting</TabsTrigger>
              <TabsTrigger value="other">Other</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-4">
              {renderInteractionList(interactions, isLoading, error)}
            </TabsContent>
            <TabsContent value="email" className="mt-4">
              {renderInteractionList(interactions, isLoading, error)}
            </TabsContent>
            <TabsContent value="call" className="mt-4">
              {renderInteractionList(interactions, isLoading, error)}
            </TabsContent>
            <TabsContent value="meeting" className="mt-4">
              {renderInteractionList(interactions, isLoading, error)}
            </TabsContent>
            <TabsContent value="other" className="mt-4">
              {renderInteractionList(interactions, isLoading, error)}
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );

  // Helper function to render the interaction list
  function renderInteractionList(interactions: Interaction[] | undefined, isLoading: boolean, error: Error | null) {
    if (isLoading) {
      return (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center p-6 text-center">
          <div>
            <AlertCircle className="h-10 w-10 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium">Failed to load interactions</h3>
            <p className="text-sm text-gray-500 mt-2">
              {error.message || "An error occurred while loading the interaction timeline."}
            </p>
          </div>
        </div>
      );
    }

    if (!interactions || interactions.length === 0) {
      return (
        <div className="flex items-center justify-center p-6 text-center">
          <div>
            <FileText className="h-10 w-10 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium">No interactions found</h3>
            <p className="text-sm text-gray-500 mt-2">
              There are no interactions in the selected time period or matching your filters.
            </p>
            <Button
              variant="outline"
              size="sm"
              className="mt-4"
              onClick={() => syncMutation.mutate()}
              disabled={syncMutation.isPending}
            >
              Sync Interactions
            </Button>
          </div>
        </div>
      );
    }

    return (
      <ScrollArea className="h-[500px] pr-4">
        <div className="space-y-4">
          {interactions.map((interaction) => (
            <div key={interaction.id} className="border rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center">
                  <div className={`p-2 rounded-full mr-3 ${
                    interaction.type === 'email' ? 'bg-blue-100' :
                    interaction.type === 'call' ? 'bg-green-100' :
                    interaction.type === 'meeting' ? 'bg-purple-100' :
                    'bg-gray-100'
                  }`}>
                    {getInteractionIcon(interaction.type)}
                  </div>
                  <div>
                    <h4 className="font-medium">{interaction.type.charAt(0).toUpperCase() + interaction.type.slice(1)}</h4>
                    <p className="text-sm text-gray-500 flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {format(new Date(interaction.timestamp), 'MMM d, yyyy h:mm a')}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  {interaction.sentiment && (
                    <Badge variant="outline" className={getSentimentColor(interaction.sentiment)}>
                      {interaction.sentiment.charAt(0).toUpperCase() + interaction.sentiment.slice(1)}
                    </Badge>
                  )}
                  {interaction.direction && (
                    <Badge variant="outline">
                      {interaction.direction === 'inbound' ? 'Inbound' : 'Outbound'}
                    </Badge>
                  )}
                  {interaction.aiGenerated && (
                    <Badge variant="outline" className="bg-purple-100 text-purple-800">
                      AI
                    </Badge>
                  )}
                </div>
              </div>

              <p className="text-sm mb-3">{interaction.summary}</p>

              {interaction.nextAction && (
                <div className="bg-amber-50 p-3 rounded-md mt-2">
                  <h5 className="text-sm font-medium flex items-center">
                    <CheckCircle className="h-4 w-4 mr-1 text-amber-600" />
                    Next Action
                  </h5>
                  <p className="text-sm">{interaction.nextAction.description}</p>
                  {interaction.nextAction.dueDate && (
                    <p className="text-xs text-gray-500 mt-1">
                      Due: {format(new Date(interaction.nextAction.dueDate), 'MMM d, yyyy')}
                    </p>
                  )}
                </div>
              )}

              {interaction.participants && interaction.participants.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs text-gray-500">Participants:</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {interaction.participants.map((participant, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {participant.name || participant.email || 'Unknown'}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    );
  }
};

export default SmartInteractionTimeline;
