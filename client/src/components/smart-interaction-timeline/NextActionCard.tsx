import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sparkles, Clock, Calendar, AlertCircle } from 'lucide-react';
import { format, parseISO, isToday, isTomorrow, addDays, isPast } from 'date-fns';
import type { Interaction, NextAction } from './SmartInteractionTimeline.d';

interface NextActionCardProps {
  interactions: Interaction[];
  contactId: string | undefined;
}

export const NextActionCard = ({ interactions, contactId }: NextActionCardProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isGenerating, setIsGenerating] = useState(false);

  // Extract next actions from interactions
  const nextActions = interactions
    .filter(interaction => interaction.nextAction)
    .map(interaction => ({
      interactionId: interaction._id,
      action: interaction.nextAction!,
      timestamp: interaction.timestamp,
      type: interaction.type
    }))
    .sort((a, b) => {
      // Sort by due date if available, otherwise by interaction timestamp
      if (a.action.dueDate && b.action.dueDate) {
        return new Date(a.action.dueDate).getTime() - new Date(b.action.dueDate).getTime();
      } else if (a.action.dueDate) {
        return -1;
      } else if (b.action.dueDate) {
        return 1;
      } else {
        return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
      }
    });

  // Format due date
  const formatDueDate = (dueDate?: string) => {
    if (!dueDate) return null;

    try {
      const date = parseISO(dueDate);

      if (isToday(date)) {
        return 'Today';
      } else if (isTomorrow(date)) {
        return 'Tomorrow';
      } else {
        return format(date, 'MMM d, yyyy');
      }
    } catch (error) {
      return dueDate;
    }
  };

  // Get badge color based on priority
  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get badge color based on due date
  const getDueDateColor = (dueDate?: string) => {
    if (!dueDate) return 'bg-gray-100 text-gray-800';

    try {
      const date = parseISO(dueDate);

      if (isPast(date)) {
        return 'bg-red-100 text-red-800';
      } else if (isToday(date)) {
        return 'bg-yellow-100 text-yellow-800';
      } else if (isTomorrow(date)) {
        return 'bg-blue-100 text-blue-800';
      } else {
        return 'bg-green-100 text-green-800';
      }
    } catch (error) {
      return 'bg-gray-100 text-gray-800';
    }
  };

  // Mark action as completed mutation
  const completeMutation = useMutation({
    mutationFn: async ({ interactionId, nextAction }: { interactionId: string, nextAction: NextAction }) => {
      if (!contactId) throw new Error('Contact ID is required');

      return mongoApiClient.interactions.updateNextAction(interactionId, {
        ...nextAction,
        completed: true
      });
    },
    onSuccess: () => {
      toast({
        title: 'Action completed',
        description: 'The next action has been marked as completed.',
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['interactions', contactId] });
    },
    onError: (error) => {
      toast({
        title: 'Failed to complete action',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  });

  // Generate next actions mutation
  const generateMutation = useMutation({
    mutationFn: async () => {
      if (!contactId) throw new Error('Contact ID is required');

      return mongoApiClient.interactions.generateNextActions(contactId);
    },
    onSuccess: (data) => {
      toast({
        title: 'Next actions generated',
        description: `Generated ${data.generated} new next actions.`,
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['interactions', contactId] });
      setIsGenerating(false);
    },
    onError: (error) => {
      toast({
        title: 'Failed to generate next actions',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
      setIsGenerating(false);
    }
  });

  // Handle generate button click
  const handleGenerate = () => {
    setIsGenerating(true);
    generateMutation.mutate();
  };

  // Handle checkbox change
  const handleCheckboxChange = (interactionId: string, nextAction: NextAction) => {
    completeMutation.mutate({ interactionId, nextAction });
  };

  // Group next actions by due date
  const groupedActions = nextActions.reduce<Record<string, typeof nextActions>>((groups, item) => {
    const dueDateKey = item.action.dueDate ? formatDueDate(item.action.dueDate) : 'No Due Date';
    // Make sure dueDateKey is a string
    const key = String(dueDateKey);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {});

  // Sort groups by due date
  const sortedGroups = Object.entries(groupedActions).sort((a, b) => {
    const dateOrder = {
      'Overdue': 0,
      'Today': 1,
      'Tomorrow': 2,
      'No Due Date': 999
    };

    const aOrder = dateOrder[a[0] as keyof typeof dateOrder];
    const bOrder = dateOrder[b[0] as keyof typeof dateOrder];

    if (aOrder !== undefined && bOrder !== undefined) {
      return aOrder - bOrder;
    } else if (aOrder !== undefined) {
      return -1;
    } else if (bOrder !== undefined) {
      return 1;
    } else {
      return a[0].localeCompare(b[0]);
    }
  });

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-xl">Next Actions</CardTitle>
          <CardDescription>
            Actions to take based on your interactions
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleGenerate}
          disabled={isGenerating || generateMutation.isPending}
        >
          <Sparkles className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
          Generate
        </Button>
      </CardHeader>
      <CardContent>
        {nextActions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="mb-2">No next actions found</p>
            <p className="text-sm">
              Click the Generate button to analyze interactions and suggest next actions.
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-6">
              {sortedGroups.map(([dueDate, actions]) => (
                <div key={dueDate}>
                  <div className="flex items-center mb-2">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <h3 className="font-medium text-sm text-gray-700">{dueDate}</h3>
                  </div>
                  <div className="space-y-3">
                    {actions.map(({ interactionId, action, type }) => (
                      <div
                        key={`${interactionId}-${action.id || action.description}`}
                        className={`p-3 rounded-lg border ${action.completed ? 'bg-gray-50 opacity-60' : 'bg-white'}`}
                      >
                        <div className="flex items-start gap-3">
                          <Checkbox
                            checked={action.completed}
                            onCheckedChange={() => handleCheckboxChange(interactionId, action)}
                            disabled={completeMutation.isPending}
                          />
                          <div className="flex-1">
                            <p className={action.completed ? 'line-through text-gray-500' : ''}>
                              {action.description}
                            </p>
                            <div className="flex flex-wrap gap-2 mt-2">
                              {action.priority && (
                                <Badge variant="outline" className={getPriorityColor(action.priority)}>
                                  {action.priority.charAt(0).toUpperCase() + action.priority.slice(1)} Priority
                                </Badge>
                              )}
                              {action.dueDate && (
                                <Badge variant="outline" className={getDueDateColor(action.dueDate)}>
                                  <Clock className="h-3 w-3 mr-1" />
                                  {formatDueDate(action.dueDate)}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Separator className="my-4" />
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};
