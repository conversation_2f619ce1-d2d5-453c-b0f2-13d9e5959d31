/**
 * Type definitions for the Smart Interaction Timeline component
 */

/**
 * Interaction type enum
 */
export type InteractionType = 'email' | 'call' | 'meeting' | 'chat' | 'social' | 'note' | 'task' | 'sms' | 'document' | 'other';

/**
 * Interaction sentiment enum
 */
export type InteractionSentiment = 'positive' | 'neutral' | 'negative' | 'mixed';

/**
 * Interaction direction enum
 */
export type InteractionDirection = 'inbound' | 'outbound';

/**
 * Interaction priority enum
 */
export type InteractionPriority = 'low' | 'medium' | 'high';

/**
 * Interaction participant interface
 */
export interface InteractionParticipant {
  id?: string;
  email?: string;
  name?: string;
  role?: string;
}

/**
 * Interaction content interface
 */
export interface InteractionContent {
  text?: string;
  html?: string;
  attachments?: Array<{
    name: string;
    type: string;
    url?: string;
  }>;
}

/**
 * Next action interface
 */
export interface NextAction {
  id?: string;
  type?: InteractionType;
  description: string;
  dueDate?: string;
  priority?: InteractionPriority;
  completed?: boolean;
  completedAt?: string;
  assignedTo?: string;
}

/**
 * Interaction interface
 */
export interface Interaction {
  id: string;
  _id: string; // MongoDB ID
  type: InteractionType;
  source: string;
  sourceId?: string;
  timestamp: string;
  summary: string;
  sentiment?: InteractionSentiment;
  direction?: InteractionDirection;
  channel?: string; // Added channel property
  subject?: string; // Added subject property
  participants?: InteractionParticipant[];
  content?: InteractionContent;
  nextAction?: NextAction;
  aiGenerated: boolean;
  aiConfidence?: number;
}

/**
 * Interaction insights interface
 */
export interface InteractionInsights {
  summary: string;
  interactionsByType: Record<InteractionType, number>;
  interactionsByMonth: Record<string, number>;
  sentimentDistribution: Record<string, number>;
  responseTime: {
    average: number;
    trend: Array<{ date: string; value: number }>;
  };
  topTopics: Array<{ topic: string; count: number }>;
  recommendations: string[];
}

/**
 * Smart Interaction Timeline props
 */
export interface SmartInteractionTimelineProps {
  contactId: string;
  opportunityId?: string;
}

/**
 * Add Interaction Dialog props
 */
export interface AddInteractionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contactId: string;
}

/**
 * Interaction Insights props
 */
export interface InteractionInsightsProps {
  contactId: string;
}

/**
 * Next Action Card props
 */
export interface NextActionCardProps {
  interaction: Interaction;
  contactId: string;
}
