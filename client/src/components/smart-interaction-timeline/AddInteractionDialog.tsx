import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Sparkles } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import type { InteractionType, InteractionSentiment } from './SmartInteractionTimeline.d';

interface AddInteractionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contactId: string;
}

// Form schema
const formSchema = z.object({
  type: z.enum(['email', 'call', 'meeting', 'note', 'sms', 'social', 'task', 'document'] as const),
  direction: z.enum(['inbound', 'outbound'] as const).optional(),
  channel: z.string().optional(),
  timestamp: z.date(),
  subject: z.string().optional(),
  summary: z.string().min(1, 'Summary is required'),
  content: z.string().optional(),
  sentiment: z.enum(['positive', 'neutral', 'negative', 'mixed'] as const).optional(),
  nextAction: z.object({
    description: z.string().optional(),
    dueDate: z.date().optional(),
    priority: z.enum(['low', 'medium', 'high'] as const).optional(),
  }).optional(),
});

type FormValues = z.infer<typeof formSchema>;

export const AddInteractionDialog = ({ open, onOpenChange, contactId }: AddInteractionDialogProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: 'note',
      timestamp: new Date(),
      summary: '',
      sentiment: 'neutral',
      nextAction: {
        priority: 'medium',
      },
    },
  });

  // Watch form values for conditional rendering
  const watchType = form.watch('type');
  const watchContent = form.watch('content');

  // Add interaction mutation
  const addMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      return mongoApiClient.interactions.create(contactId, {
        type: values.type,
        direction: values.direction,
        channel: values.channel,
        timestamp: values.timestamp.toISOString(),
        subject: values.subject,
        summary: values.summary,
        content: { text: values.content },
        sentiment: values.sentiment,
        nextAction: values.nextAction && values.nextAction.description ? {
          description: values.nextAction.description,
          dueDate: values.nextAction.dueDate?.toISOString(),
          priority: values.nextAction.priority,
        } : undefined,
      });
    },
    onSuccess: () => {
      toast({
        title: 'Interaction added',
        description: 'The interaction has been added successfully.',
        variant: 'default',
      });
      queryClient.invalidateQueries({ queryKey: ['interactions', contactId] });
      onOpenChange(false);
      form.reset();
    },
    onError: (error) => {
      toast({
        title: 'Failed to add interaction',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  });

  // Generate summary mutation
  const generateSummaryMutation = useMutation({
    mutationFn: async (content: string) => {
      return mongoApiClient.interactions.generateSummary(content);
    },
    onSuccess: (data) => {
      form.setValue('summary', data.summary);
      if (data.sentiment) {
        form.setValue('sentiment', data.sentiment as InteractionSentiment);
      }
      setIsGeneratingSummary(false);
    },
    onError: (error) => {
      toast({
        title: 'Failed to generate summary',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
      setIsGeneratingSummary(false);
    }
  });

  // Handle form submission
  const onSubmit = (values: FormValues) => {
    addMutation.mutate(values);
  };

  // Handle generate summary button click
  const handleGenerateSummary = () => {
    const content = form.getValues('content');
    if (!content) {
      toast({
        title: 'Content required',
        description: 'Please enter content to generate a summary.',
        variant: 'destructive',
      });
      return;
    }

    setIsGeneratingSummary(true);
    generateSummaryMutation.mutate(content);
  };

  // Get label for interaction type
  const getTypeLabel = (type: InteractionType) => {
    switch (type) {
      case 'email': return 'Email';
      case 'call': return 'Call';
      case 'meeting': return 'Meeting';
      case 'note': return 'Note';
      case 'sms': return 'SMS';
      case 'social': return 'Social Media';
      case 'task': return 'Task';
      case 'document': return 'Document';
      default: return type;
    }
  };

  // Get channel options based on type
  const getChannelOptions = () => {
    switch (watchType) {
      case 'social':
        return (
          <>
            <SelectItem value="linkedin">LinkedIn</SelectItem>
            <SelectItem value="twitter">Twitter</SelectItem>
            <SelectItem value="facebook">Facebook</SelectItem>
            <SelectItem value="instagram">Instagram</SelectItem>
          </>
        );
      case 'email':
        return (
          <>
            <SelectItem value="gmail">Gmail</SelectItem>
            <SelectItem value="outlook">Outlook</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </>
        );
      case 'call':
        return (
          <>
            <SelectItem value="phone">Phone</SelectItem>
            <SelectItem value="voip">VoIP</SelectItem>
            <SelectItem value="video">Video Call</SelectItem>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Interaction</DialogTitle>
          <DialogDescription>
            Record a new interaction with this contact.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              {/* Interaction Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="call">Call</SelectItem>
                        <SelectItem value="meeting">Meeting</SelectItem>
                        <SelectItem value="note">Note</SelectItem>
                        <SelectItem value="sms">SMS</SelectItem>
                        <SelectItem value="social">Social Media</SelectItem>
                        <SelectItem value="task">Task</SelectItem>
                        <SelectItem value="document">Document</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Timestamp */}
              <FormField
                control={form.control}
                name="timestamp"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date & Time</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Direction - Only for email, call, sms, social */}
            {['email', 'call', 'sms', 'social'].includes(watchType) && (
              <FormField
                control={form.control}
                name="direction"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Direction</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select direction" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="inbound">Inbound</SelectItem>
                        <SelectItem value="outbound">Outbound</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Channel - Only for social, email, call */}
            {['social', 'email', 'call'].includes(watchType) && (
              <FormField
                control={form.control}
                name="channel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Channel</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select channel" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {getChannelOptions()}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Subject - For email, meeting, document */}
            {['email', 'meeting', 'document'].includes(watchType) && (
              <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subject</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter subject" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Content */}
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={`Enter ${getTypeLabel(watchType).toLowerCase()} content`}
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The detailed content of the interaction.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Summary with AI generation */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <FormLabel>Summary</FormLabel>
                {watchContent && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleGenerateSummary}
                    disabled={isGeneratingSummary || !watchContent}
                  >
                    <Sparkles className={`h-4 w-4 mr-2 ${isGeneratingSummary ? 'animate-spin' : ''}`} />
                    Generate
                  </Button>
                )}
              </div>
              <FormField
                control={form.control}
                name="summary"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a summary of the interaction"
                        className="min-h-[60px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Sentiment */}
            <FormField
              control={form.control}
              name="sentiment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sentiment</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select sentiment" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="positive">Positive</SelectItem>
                      <SelectItem value="neutral">Neutral</SelectItem>
                      <SelectItem value="negative">Negative</SelectItem>
                      <SelectItem value="mixed">Mixed</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Next Action */}
            <div className="space-y-4 border p-4 rounded-lg">
              <h4 className="font-medium">Next Action</h4>

              <FormField
                control={form.control}
                name="nextAction.description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Input placeholder="What needs to be done next?" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="nextAction.dueDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Due Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nextAction.priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={addMutation.isPending}
              >
                {addMutation.isPending ? 'Saving...' : 'Save Interaction'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
