import { <PERSON><PERSON>, <PERSON>si<PERSON> } from 'reactflow';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, Play, GitBranch } from 'lucide-react';

interface WorkflowNodeProps {
  data: {
    type: string;
    config?: Record<string, any>;
    description: string;
    nodeType: 'trigger' | 'action' | 'condition';
  };
  isConnectable: boolean;
}

export default function WorkflowNode({ data, isConnectable }: WorkflowNodeProps) {
  // Determine node color based on type
  const getNodeColor = () => {
    switch (data.nodeType) {
      case 'trigger':
        return 'bg-blue-100 border-blue-300';
      case 'action':
        return 'bg-green-100 border-green-300';
      case 'condition':
        return 'bg-yellow-100 border-yellow-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  };

  // Determine icon based on type
  const getNodeIcon = () => {
    switch (data.nodeType) {
      case 'trigger':
        return <Zap className="h-4 w-4 text-blue-500" />;
      case 'action':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'condition':
        return <GitBranch className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="relative">
      {/* Input handle (not for trigger nodes) */}
      {data.nodeType !== 'trigger' && (
        <Handle
          type="target"
          position={Position.Left}
          isConnectable={isConnectable}
          className="w-3 h-3 bg-muted-foreground"
        />
      )}

      {/* Node content */}
      <Card className={`w-64 shadow-md ${getNodeColor()}`}>
        <CardHeader className="p-3 pb-0">
          <div className="flex justify-between items-center">
            <Badge variant="outline" className="capitalize">
              {data.nodeType}
            </Badge>
            <Badge variant="secondary" className="capitalize">
              {data.type}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            {getNodeIcon()}
            <span className="text-sm font-medium">{data.description}</span>
          </div>
          {data.config && Object.keys(data.config).length > 0 && (
            <div className="mt-2 text-xs text-muted-foreground">
              <div className="flex flex-wrap gap-1">
                {Object.entries(data.config).map(([key, value]) => (
                  <Badge key={key} variant="outline" className="text-xs">
                    {key}: {typeof value === 'object' ? '{}' : String(value)}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        isConnectable={isConnectable}
        className="w-3 h-3 bg-muted-foreground"
      />
    </div>
  );
}
