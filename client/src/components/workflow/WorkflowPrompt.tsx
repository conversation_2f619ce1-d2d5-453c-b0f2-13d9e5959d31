import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';

interface WorkflowPromptProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  isLoading: boolean;
}

export default function WorkflowPrompt({
  value,
  onChange,
  onSubmit,
  isLoading,
}: WorkflowPromptProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Submit on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      onSubmit();
    }
  };

  return (
    <div className="space-y-4">
      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Describe your workflow in plain English..."
        rows={6}
        disabled={isLoading}
      />
      <div className="flex justify-end">
        <Button onClick={onSubmit} disabled={isLoading || !value.trim()}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Workflow...
            </>
          ) : (
            'Create Workflow'
          )}
        </Button>
      </div>
      <div className="text-sm text-muted-foreground">
        <p className="font-medium mb-2">Tips:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Be specific about what should trigger the workflow</li>
          <li>Describe the conditions that should be checked</li>
          <li>Specify what actions should be taken</li>
          <li>Include any timing requirements (e.g., "wait 2 days")</li>
        </ul>
      </div>
    </div>
  );
}
