import { useCallback } from 'react';
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  Node,
  Edge,
  NodeTypes,
  EdgeTypes,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { WorkflowNode, WorkflowEdge } from '@/api';
import WorkflowNodeComponent from './WorkflowNode';

// Define custom node types
const nodeTypes: NodeTypes = {
  trigger: WorkflowNodeComponent,
  action: WorkflowNodeComponent,
  condition: WorkflowNodeComponent,
};

interface WorkflowCanvasProps {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  onNodesChange?: (nodes: WorkflowNode[]) => void;
  onEdgesChange?: (edges: WorkflowEdge[]) => void;
  readOnly?: boolean;
}

export default function WorkflowCanvas({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  readOnly = false,
}: WorkflowCanvasProps) {
  // Convert WorkflowNode to ReactFlow Node
  const reactFlowNodes: Node[] = nodes.map((node) => ({
    id: node.id,
    type: node.type,
    position: node.position,
    data: {
      ...node.data,
      nodeType: node.type,
    },
    draggable: !readOnly,
    connectable: !readOnly,
  }));

  // Convert WorkflowEdge to ReactFlow Edge
  const reactFlowEdges: Edge[] = edges.map((edge) => ({
    id: edge.id,
    source: edge.source,
    target: edge.target,
    label: edge.label,
    data: edge.condition,
    animated: true,
  }));

  // Handle node changes
  const handleNodesChange = useCallback(
    (changes: any) => {
      if (readOnly || !onNodesChange) return;

      // Update node positions
      const updatedNodes = [...nodes];
      changes.forEach((change: any) => {
        if (change.type === 'position' && change.position) {
          const nodeIndex = updatedNodes.findIndex((n) => n.id === change.id);
          if (nodeIndex !== -1) {
            updatedNodes[nodeIndex] = {
              ...updatedNodes[nodeIndex],
              position: change.position,
            };
          }
        }
      });

      onNodesChange(updatedNodes);
    },
    [nodes, onNodesChange, readOnly]
  );

  // Handle edge changes
  const handleEdgesChange = useCallback(
    (changes: any) => {
      if (readOnly || !onEdgesChange) return;

      // Handle edge changes
      const updatedEdges = [...edges];
      // Implement edge change logic here

      onEdgesChange(updatedEdges);
    },
    [edges, onEdgesChange, readOnly]
  );

  return (
    <div style={{ height: 500 }}>
      <ReactFlow
        nodes={reactFlowNodes}
        edges={reactFlowEdges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-right"
      >
        <Background />
        <Controls />
        <MiniMap />
        <Panel position="top-right">
          <div className="bg-background border rounded p-2 text-xs">
            <p className="font-medium mb-1">Node Types:</p>
            <div className="flex items-center gap-2 mb-1">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <span>Trigger</span>
            </div>
            <div className="flex items-center gap-2 mb-1">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Action</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span>Condition</span>
            </div>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
}
