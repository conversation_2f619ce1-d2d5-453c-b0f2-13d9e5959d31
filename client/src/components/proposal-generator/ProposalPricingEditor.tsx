import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Trash2, Save, Calculator } from 'lucide-react';

interface PricingItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface ProposalPricingEditorProps {
  items: PricingItem[];
  currency?: string;
  onSave: (items: PricingItem[], currency: string) => void;
  readOnly?: boolean;
}

const ProposalPricingEditor: React.FC<ProposalPricingEditorProps> = ({
  items: initialItems,
  currency: initialCurrency = 'USD',
  onSave,
  readOnly = false,
}) => {
  const [items, setItems] = useState<PricingItem[]>(initialItems);
  const [currency, setCurrency] = useState<string>(initialCurrency);

  const calculateTotal = (item: PricingItem): number => {
    return item.quantity * item.unitPrice;
  };

  const handleAddItem = () => {
    const newItem: PricingItem = {
      id: `item-${Date.now()}`,
      name: '',
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0,
    };
    
    setItems([...items, newItem]);
  };

  const handleRemoveItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  const handleItemChange = (id: string, field: keyof PricingItem, value: string | number) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        
        // Recalculate total if quantity or unitPrice changed
        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = calculateTotal(updatedItem);
        }
        
        return updatedItem;
      }
      return item;
    }));
  };

  const handleSave = () => {
    onSave(items, currency);
  };

  const calculateGrandTotal = (): number => {
    return items.reduce((sum, item) => sum + item.total, 0);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Pricing Details</CardTitle>
        {!readOnly && (
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Pricing
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Input
            id="currency"
            value={currency}
            onChange={(e) => setCurrency(e.target.value)}
            disabled={readOnly}
            className="w-32"
          />
        </div>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">Item</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="w-[100px] text-right">Quantity</TableHead>
              <TableHead className="w-[120px] text-right">Unit Price</TableHead>
              <TableHead className="w-[120px] text-right">Total</TableHead>
              {!readOnly && <TableHead className="w-[80px]"></TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => (
              <TableRow key={item.id}>
                <TableCell>
                  <Input
                    value={item.name}
                    onChange={(e) => handleItemChange(item.id, 'name', e.target.value)}
                    disabled={readOnly}
                    placeholder="Item name"
                  />
                </TableCell>
                <TableCell>
                  <Textarea
                    value={item.description || ''}
                    onChange={(e) => handleItemChange(item.id, 'description', e.target.value)}
                    disabled={readOnly}
                    placeholder="Description"
                    rows={1}
                    className="min-h-[40px]"
                  />
                </TableCell>
                <TableCell className="text-right">
                  <Input
                    type="number"
                    value={item.quantity}
                    onChange={(e) => handleItemChange(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                    disabled={readOnly}
                    className="text-right"
                    min={0}
                  />
                </TableCell>
                <TableCell className="text-right">
                  <Input
                    type="number"
                    value={item.unitPrice}
                    onChange={(e) => handleItemChange(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                    disabled={readOnly}
                    className="text-right"
                    min={0}
                    step={0.01}
                  />
                </TableCell>
                <TableCell className="text-right font-medium">
                  {formatCurrency(item.total)}
                </TableCell>
                {!readOnly && (
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveItem(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {!readOnly && (
          <Button variant="outline" onClick={handleAddItem} className="mt-2">
            <Plus className="mr-2 h-4 w-4" />
            Add Item
          </Button>
        )}
      </CardContent>
      <CardFooter className="flex justify-between border-t p-4">
        <div className="flex items-center">
          <Calculator className="mr-2 h-5 w-5 text-muted-foreground" />
          <span className="text-muted-foreground">Grand Total</span>
        </div>
        <div className="text-xl font-bold">
          {formatCurrency(calculateGrandTotal())}
        </div>
      </CardFooter>
    </Card>
  );
};

export default ProposalPricingEditor;
