import { ProposalGeneratorDialog } from './ProposalGeneratorDialog';

interface ProposalGeneratorButtonProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

export function ProposalGeneratorButton({
  opportunityId,
  contactId,
  companyId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Generate Proposal'
}: ProposalGeneratorButtonProps) {
  return (
    <ProposalGeneratorDialog
      opportunityId={opportunityId}
      contactId={contactId}
      companyId={companyId}
      opportunityName={opportunityName}
      contactName={contactName}
      companyName={companyName}
      variant={variant}
      size={size}
      buttonText={buttonText}
    />
  );
}
