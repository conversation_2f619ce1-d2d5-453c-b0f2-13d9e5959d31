import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Proposal } from '@/types/proposal';
import { Save } from 'lucide-react';

interface ProposalEditorProps {
  proposal: Proposal;
  onSave: (updatedProposal: Proposal) => void;
  readOnly?: boolean;
}

const ProposalEditor: React.FC<ProposalEditorProps> = ({
  proposal,
  onSave,
  readOnly = false,
}) => {
  const [editedProposal, setEditedProposal] = useState<Proposal>({ ...proposal });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedProposal((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    onSave(editedProposal);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Edit Proposal</CardTitle>
        {!readOnly && (
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            name="title"
            value={editedProposal.title}
            onChange={handleChange}
            disabled={readOnly}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={editedProposal.description}
            onChange={handleChange}
            disabled={readOnly}
            rows={3}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="content">Content</Label>
          <Textarea
            id="content"
            name="content"
            value={editedProposal.content}
            onChange={handleChange}
            disabled={readOnly}
            rows={15}
            className="font-mono"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ProposalEditor;
