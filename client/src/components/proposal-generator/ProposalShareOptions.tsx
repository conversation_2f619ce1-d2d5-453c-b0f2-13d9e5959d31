import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { createProposalShareableLink } from '@/api/proposal-generator-api';
import { Copy, Link, Mail, Linkedin, Twitter, Facebook, Check, Loader2 } from 'lucide-react';
import { ProposalExportFormat } from '@shared/types';

interface ProposalShareOptionsProps {
  proposalId: string;
  proposalName: string;
  onEmailShare?: (email: string, message: string) => void;
  onSocialShare?: (platform: string, message: string) => void;
}

const ProposalShareOptions: React.FC<ProposalShareOptionsProps> = ({
  proposalId,
  proposalName,
  onEmailShare,
  onSocialShare
}) => {
  const [activeTab, setActiveTab] = useState<string>('link');
  const [shareableLink, setShareableLink] = useState<string>('');
  const [expirationDays, setExpirationDays] = useState<string>('7');
  const [format, setFormat] = useState<ProposalExportFormat>('pdf');
  const [trackViews, setTrackViews] = useState<boolean>(true);
  const [passwordProtect, setPasswordProtect] = useState<boolean>(false);
  const [password, setPassword] = useState<string>('');
  const [emailRecipient, setEmailRecipient] = useState<string>('');
  const [emailMessage, setEmailMessage] = useState<string>('');
  const [socialPlatform, setSocialPlatform] = useState<string>('linkedin');
  const [socialMessage, setSocialMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [linkCopied, setLinkCopied] = useState<boolean>(false);

  const { toast } = useToast();

  const handleGenerateLink = async () => {
    try {
      setLoading(true);

      const response = await createProposalShareableLink(proposalId, {
        format: format,
        expiresIn: parseInt(expirationDays)
      });

      setShareableLink(response.shareUrl);

      toast({
        title: 'Link Generated',
        description: `Shareable link created successfully. Expires in ${expirationDays} days.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate shareable link',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = () => {
    if (shareableLink) {
      navigator.clipboard.writeText(shareableLink);
      setLinkCopied(true);

      toast({
        title: 'Link Copied',
        description: 'Shareable link copied to clipboard',
      });

      setTimeout(() => setLinkCopied(false), 2000);
    }
  };

  const handleEmailShare = () => {
    if (onEmailShare && emailRecipient) {
      onEmailShare(emailRecipient, emailMessage);
      setEmailRecipient('');
      setEmailMessage('');

      toast({
        title: 'Email Sent',
        description: `Proposal shared with ${emailRecipient}`,
      });
    } else {
      toast({
        title: 'Error',
        description: 'Please enter a recipient email address',
        variant: 'destructive'
      });
    }
  };

  const handleSocialShare = () => {
    if (onSocialShare) {
      onSocialShare(socialPlatform, socialMessage);
      setSocialMessage('');

      toast({
        title: 'Shared',
        description: `Proposal shared on ${socialPlatform}`,
      });
    }
  };

  const renderSocialIcon = () => {
    switch (socialPlatform) {
      case 'linkedin':
        return <Linkedin className="h-4 w-4 mr-2" />;
      case 'twitter':
        return <Twitter className="h-4 w-4 mr-2" />;
      case 'facebook':
        return <Facebook className="h-4 w-4 mr-2" />;
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Share Proposal</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="link">
              <Link className="h-4 w-4 mr-2" />
              Link
            </TabsTrigger>
            <TabsTrigger value="email">
              <Mail className="h-4 w-4 mr-2" />
              Email
            </TabsTrigger>
            <TabsTrigger value="social">
              <Linkedin className="h-4 w-4 mr-2" />
              Social
            </TabsTrigger>
          </TabsList>

          <TabsContent value="link" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="format">Format</Label>
                  <Select
                    value={format}
                    onValueChange={(value) => setFormat(value as ProposalExportFormat)}
                  >
                    <SelectTrigger id="format">
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="docx">Word (DOCX)</SelectItem>
                      <SelectItem value="markdown">Markdown</SelectItem>
                      <SelectItem value="claude-html">Claude HTML</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expiration">Expires After</Label>
                  <Select value={expirationDays} onValueChange={setExpirationDays}>
                    <SelectTrigger id="expiration">
                      <SelectValue placeholder="Select expiration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Day</SelectItem>
                      <SelectItem value="7">7 Days</SelectItem>
                      <SelectItem value="30">30 Days</SelectItem>
                      <SelectItem value="90">90 Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="track-views">Track Views</Label>
                  <Switch
                    id="track-views"
                    checked={trackViews}
                    onCheckedChange={setTrackViews}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="password-protect">Password Protect</Label>
                  <Switch
                    id="password-protect"
                    checked={passwordProtect}
                    onCheckedChange={setPasswordProtect}
                  />
                </div>

                {passwordProtect && (
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>
                )}
              </div>

              {shareableLink ? (
                <div className="space-y-2">
                  <Label htmlFor="shareable-link">Shareable Link</Label>
                  <div className="flex">
                    <Input
                      id="shareable-link"
                      value={shareableLink}
                      readOnly
                      className="flex-1 rounded-r-none"
                    />
                    <Button
                      variant="secondary"
                      className="rounded-l-none"
                      onClick={handleCopyLink}
                    >
                      {linkCopied ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <Button
                  onClick={handleGenerateLink}
                  disabled={loading || (passwordProtect && !password)}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Link className="h-4 w-4 mr-2" />
                      Generate Link
                    </>
                  )}
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email-recipient">Recipient Email</Label>
                <Input
                  id="email-recipient"
                  type="email"
                  placeholder="<EMAIL>"
                  value={emailRecipient}
                  onChange={(e) => setEmailRecipient(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email-message">Message (Optional)</Label>
                <Input
                  id="email-message"
                  placeholder="Add a personal message..."
                  value={emailMessage}
                  onChange={(e) => setEmailMessage(e.target.value)}
                />
              </div>

              <Button
                onClick={handleEmailShare}
                disabled={!emailRecipient || !onEmailShare}
                className="w-full"
              >
                <Mail className="h-4 w-4 mr-2" />
                Send Email
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="social" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="social-platform">Platform</Label>
                <Select value={socialPlatform} onValueChange={setSocialPlatform}>
                  <SelectTrigger id="social-platform">
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="linkedin">LinkedIn</SelectItem>
                    <SelectItem value="twitter">Twitter</SelectItem>
                    <SelectItem value="facebook">Facebook</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="social-message">Message</Label>
                <Input
                  id="social-message"
                  placeholder="Add a message for your post..."
                  value={socialMessage}
                  onChange={(e) => setSocialMessage(e.target.value)}
                />
              </div>

              <Button
                onClick={handleSocialShare}
                disabled={!onSocialShare}
                className="w-full"
              >
                {renderSocialIcon()}
                Share on {socialPlatform.charAt(0).toUpperCase() + socialPlatform.slice(1)}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ProposalShareOptions;
