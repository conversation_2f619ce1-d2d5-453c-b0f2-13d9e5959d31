import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Download, FileText, FileImage, FileCode } from 'lucide-react';
import { ProposalDownloadOptions as ProposalDownloadOptionsType, ProposalExportFormat } from '@shared/types';

interface ProposalDownloadOptionsProps {
  proposalId: string;
  onDownload: (options: ProposalDownloadOptionsType) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  availableFormats?: ProposalExportFormat[];
}

/**
 * Component for configuring proposal download options
 */
const ProposalDownloadOptions: React.FC<ProposalDownloadOptionsProps> = ({
  proposalId,
  onDownload,
  onCancel,
  isLoading = false,
  availableFormats = ['pdf', 'docx', 'markdown', 'html', 'claude-html'],
}) => {
  const [format, setFormat] = useState<ProposalExportFormat>('pdf');
  const [includeCompanyLogo, setIncludeCompanyLogo] = useState(true);
  const [includeSignaturePage, setIncludeSignaturePage] = useState(true);
  const [includeAttachments, setIncludeAttachments] = useState(true);
  const [customHeader, setCustomHeader] = useState('');
  const [customFooter, setCustomFooter] = useState('');
  const [watermark, setWatermark] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const options: ProposalDownloadOptionsType = {
      format,
      includeCompanyLogo,
      includeSignaturePage,
      includeAttachments,
      ...(customHeader && { customHeader }),
      ...(customFooter && { customFooter }),
      ...(watermark && { watermark }),
    };
    
    onDownload(options);
  };

  const getFormatIcon = (format: ProposalExportFormat) => {
    switch (format) {
      case 'pdf':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'docx':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'markdown':
        return <FileCode className="h-4 w-4 mr-2" />;
      case 'html':
      case 'claude-html':
        return <FileCode className="h-4 w-4 mr-2" />;
      default:
        return <FileText className="h-4 w-4 mr-2" />;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Download className="h-5 w-5 mr-2 text-primary" />
            Download Options
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="format">Format</Label>
            <Select
              value={format}
              onValueChange={(value) => setFormat(value as ProposalExportFormat)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a format" />
              </SelectTrigger>
              <SelectContent>
                {availableFormats.includes('pdf') && (
                  <SelectItem value="pdf" className="flex items-center">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      PDF Document
                    </div>
                  </SelectItem>
                )}
                {availableFormats.includes('docx') && (
                  <SelectItem value="docx">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      Word Document (DOCX)
                    </div>
                  </SelectItem>
                )}
                {availableFormats.includes('markdown') && (
                  <SelectItem value="markdown">
                    <div className="flex items-center">
                      <FileCode className="h-4 w-4 mr-2" />
                      Markdown
                    </div>
                  </SelectItem>
                )}
                {availableFormats.includes('html') && (
                  <SelectItem value="html">
                    <div className="flex items-center">
                      <FileCode className="h-4 w-4 mr-2" />
                      HTML
                    </div>
                  </SelectItem>
                )}
                {availableFormats.includes('claude-html') && (
                  <SelectItem value="claude-html">
                    <div className="flex items-center">
                      <FileCode className="h-4 w-4 mr-2" />
                      Claude HTML (Enhanced)
                    </div>
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label>Document Options</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeCompanyLogo" 
                  checked={includeCompanyLogo}
                  onCheckedChange={(checked) => setIncludeCompanyLogo(!!checked)}
                />
                <Label htmlFor="includeCompanyLogo">Include Company Logo</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeSignaturePage" 
                  checked={includeSignaturePage}
                  onCheckedChange={(checked) => setIncludeSignaturePage(!!checked)}
                />
                <Label htmlFor="includeSignaturePage">Include Signature Page</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeAttachments" 
                  checked={includeAttachments}
                  onCheckedChange={(checked) => setIncludeAttachments(!!checked)}
                />
                <Label htmlFor="includeAttachments">Include Attachments</Label>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="customHeader">Custom Header (Optional)</Label>
            <Input
              id="customHeader"
              placeholder="Enter custom header text"
              value={customHeader}
              onChange={(e) => setCustomHeader(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="customFooter">Custom Footer (Optional)</Label>
            <Input
              id="customFooter"
              placeholder="Enter custom footer text"
              value={customFooter}
              onChange={(e) => setCustomFooter(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="watermark">Watermark (Optional)</Label>
            <Input
              id="watermark"
              placeholder="Enter watermark text"
              value={watermark}
              onChange={(e) => setWatermark(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>
      
      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
        )}
        
        <Button
          type="submit"
          disabled={isLoading}
          className="min-w-[120px]"
        >
          {isLoading ? (
            <>
              <div className="h-4 w-4 mr-2 animate-spin" />
              Downloading...
            </>
          ) : (
            <>
              {getFormatIcon(format)}
              Download {format.toUpperCase()}
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

export default ProposalDownloadOptions;
