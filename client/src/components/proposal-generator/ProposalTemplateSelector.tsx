import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { ProposalTemplate } from '@/types/proposal';
import { getProposalTemplates } from '@/api/proposal-generator-api';
import { Loader2 } from 'lucide-react';

interface ProposalTemplateSelectorProps {
  onSelectTemplate: (template: ProposalTemplate) => void;
  selectedTemplateId?: string;
}

const ProposalTemplateSelector: React.FC<ProposalTemplateSelectorProps> = ({
  onSelectTemplate,
  selectedTemplateId,
}) => {
  const [templates, setTemplates] = useState<ProposalTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedId, setSelectedId] = useState<string | undefined>(selectedTemplateId);

  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoading(true);
        const data = await getProposalTemplates({});
        setTemplates(data);

        // If no template is selected and we have templates, select the first one
        if (!selectedId && data.length > 0) {
          setSelectedId(data[0].id);
          onSelectTemplate(data[0]);
        }
      } catch (err) {
        setError('Failed to load proposal templates');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
  }, [onSelectTemplate, selectedId]);

  const handleSelectTemplate = (id: string) => {
    setSelectedId(id);
    const template = templates.find(t => t.id === id);
    if (template) {
      onSelectTemplate(template);
    }
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Select a Template</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Select a Template</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-destructive">{error}</div>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Select a Template</CardTitle>
      </CardHeader>
      <CardContent>
        {templates.length === 0 ? (
          <div className="text-muted-foreground">No templates available</div>
        ) : (
          <RadioGroup
            value={selectedId}
            onValueChange={handleSelectTemplate}
            className="space-y-4"
          >
            {templates.map((template) => (
              <div key={template.id} className="flex items-start space-x-3">
                <RadioGroupItem value={template.id} id={`template-${template.id}`} />
                <div className="grid gap-1.5">
                  <Label htmlFor={`template-${template.id}`} className="font-medium">
                    {template.name}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {template.description}
                  </p>
                </div>
              </div>
            ))}
          </RadioGroup>
        )}
      </CardContent>
    </Card>
  );
};

export default ProposalTemplateSelector;
