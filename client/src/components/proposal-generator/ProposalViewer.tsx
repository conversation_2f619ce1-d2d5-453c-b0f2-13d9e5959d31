import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Proposal } from '@/types/proposal';
import { Download, Share } from 'lucide-react';
import { ProposalShareDialog } from './ProposalShareDialog';

interface ProposalViewerProps {
  proposal: Proposal;
  onDownload?: () => void;
}

const ProposalViewer: React.FC<ProposalViewerProps> = ({
  proposal,
  onDownload,
}) => {
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{proposal.title}</CardTitle>
        <div className="flex gap-2">
          {onDownload && (
            <Button variant="outline" size="sm" onClick={onDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={() => setIsShareDialogOpen(true)}>
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="preview">
          <TabsList className="mb-4">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
          </TabsList>
          <TabsContent value="preview" className="prose max-w-none">
            <div dangerouslySetInnerHTML={{ __html: proposal.content }} />
          </TabsContent>
          <TabsContent value="content">
            <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[600px]">
              {proposal.content}
            </pre>
          </TabsContent>
        </Tabs>
      </CardContent>

      {isShareDialogOpen && (
        <ProposalShareDialog
          proposalId={proposal.id}
          proposalName={proposal.title}
          trigger={<></>}
        />
      )}
    </Card>
  );
};

export default ProposalViewer;
