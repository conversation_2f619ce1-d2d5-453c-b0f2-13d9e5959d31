import { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Loader2, Save } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { updateProposalClauses } from '@/api/proposal-generator-api';
import type {
  LegalClausePickerProps,
  IndustrySpecificClause
} from '@/types/proposal-generator';

/**
 * LegalClausePicker Component
 *
 * This component provides a user interface for selecting legal clauses to include in a proposal.
 * It displays both required and optional clauses specific to the company's industry, allowing users
 * to review the clause text and select which ones to include.
 *
 * @component
 * @example
 * ```tsx
 * <LegalClausePicker
 *   proposalId="prop123"
 *   companyId="comp456"
 *   industrySpecificClauses={[
 *     { id: "1", name: "Data Protection", text: "...", required: true },
 *     { id: "2", name: "Liability", text: "...", required: false }
 *   ]}
 *   onSuccess={() => console.log('Clauses updated')}
 * />
 * ```
 */
export function LegalClausePicker({
  proposalId,
  companyId,
  industrySpecificClauses = [],
  onSuccess
}: LegalClausePickerProps) {
  const { toast } = useToast();
  const [selectedClauses, setSelectedClauses] = useState<string[]>([]);

  // Initialize selected clauses with required ones
  useEffect(() => {
    const requiredClauses = industrySpecificClauses
      .filter(clause => clause.required)
      .map(clause => clause.id);

    setSelectedClauses(requiredClauses);
  }, [industrySpecificClauses]);

  // Update proposal clauses mutation
  const updateClausesMutation = useMutation({
    mutationFn: (clauses: string[]) => updateProposalClauses(proposalId, clauses),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Legal clauses updated successfully',
      });

      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update legal clauses',
        variant: 'destructive',
      });
    },
  });

  // Handle clause selection
  const handleClauseChange = (clauseId: string, checked: boolean) => {
    if (checked) {
      setSelectedClauses(prev => [...prev, clauseId]);
    } else {
      setSelectedClauses(prev => prev.filter(id => id !== clauseId));
    }
  };

  // Handle saving clauses
  const handleSaveClauses = () => {
    updateClausesMutation.mutate(selectedClauses);
  };

  // Group clauses by required status
  const requiredClauses = industrySpecificClauses.filter(clause => clause.required);
  const optionalClauses = industrySpecificClauses.filter(clause => !clause.required);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Legal Clauses</CardTitle>
        <CardDescription>
          Select the legal clauses to include in your proposal
        </CardDescription>
      </CardHeader>
      <CardContent>
        {industrySpecificClauses.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            No industry-specific clauses available
          </div>
        ) : (
          <div className="space-y-6">
            {requiredClauses.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-3 flex items-center">
                  Required Clauses
                  <Badge variant="destructive" className="ml-2">Required</Badge>
                </h3>
                <Accordion type="multiple" className="w-full">
                  {requiredClauses.map(clause => (
                    <AccordionItem key={clause.id} value={clause.id}>
                      <div className="flex items-center space-x-2 py-1">
                        <Checkbox
                          id={`clause-${clause.id}`}
                          checked={selectedClauses.includes(clause.id)}
                          onCheckedChange={(checked) => {
                            if (checked === false) {
                              toast({
                                title: 'Required Clause',
                                description: 'This clause is required and cannot be removed',
                                variant: 'destructive',
                              });
                            }
                            // Always keep required clauses selected
                            handleClauseChange(clause.id, true);
                          }}
                          disabled={true}
                        />
                        <AccordionTrigger className="hover:no-underline py-0">
                          <Label
                            htmlFor={`clause-${clause.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {clause.name}
                          </Label>
                        </AccordionTrigger>
                      </div>
                      <AccordionContent>
                        <div className="pl-6 text-sm text-muted-foreground">
                          {clause.text}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            )}

            {optionalClauses.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-3">Optional Clauses</h3>
                <Accordion type="multiple" className="w-full">
                  {optionalClauses.map(clause => (
                    <AccordionItem key={clause.id} value={clause.id}>
                      <div className="flex items-center space-x-2 py-1">
                        <Checkbox
                          id={`clause-${clause.id}`}
                          checked={selectedClauses.includes(clause.id)}
                          onCheckedChange={(checked) => {
                            handleClauseChange(clause.id, checked === true);
                          }}
                        />
                        <AccordionTrigger className="hover:no-underline py-0">
                          <Label
                            htmlFor={`clause-${clause.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {clause.name}
                          </Label>
                        </AccordionTrigger>
                      </div>
                      <AccordionContent>
                        <div className="pl-6 text-sm text-muted-foreground">
                          {clause.text}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            )}

            <div className="pt-4">
              <Button
                onClick={handleSaveClauses}
                disabled={updateClausesMutation.isPending}
                className="w-full"
              >
                {updateClausesMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Clauses
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
