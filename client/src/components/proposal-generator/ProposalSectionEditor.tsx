import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sparkles, Save } from 'lucide-react';
import { ProposalSection, ProposalSectionType } from '@shared/types';

interface ProposalSectionEditorProps {
  section: ProposalSection;
  onSave: (updatedSection: ProposalSection) => void;
  onGenerateWithAI?: (sectionType: ProposalSectionType, prompt: string) => Promise<string>;
  readOnly?: boolean;
}

const ProposalSectionEditor: React.FC<ProposalSectionEditorProps> = ({
  section,
  onSave,
  onGenerateWithAI,
  readOnly = false,
}) => {
  const [editedSection, setEditedSection] = useState<ProposalSection>({ ...section });
  const [aiPrompt, setAiPrompt] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedSection((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleTypeChange = (value: string) => {
    setEditedSection((prev) => ({
      ...prev,
      type: value as ProposalSectionType,
    }));
  };

  const handleSave = () => {
    onSave(editedSection);
  };

  const handleGenerateWithAI = async () => {
    if (!onGenerateWithAI || !aiPrompt) return;
    
    try {
      setIsGenerating(true);
      const generatedContent = await onGenerateWithAI(editedSection.type, aiPrompt);
      
      setEditedSection((prev) => ({
        ...prev,
        content: generatedContent,
      }));
      
      setAiPrompt('');
    } catch (error) {
      console.error('Error generating content with AI:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Edit Section</CardTitle>
        {!readOnly && (
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Section Name</Label>
          <Input
            id="name"
            name="name"
            value={editedSection.name}
            onChange={handleChange}
            disabled={readOnly}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="type">Section Type</Label>
          <Select
            value={editedSection.type}
            onValueChange={handleTypeChange}
            disabled={readOnly}
          >
            <SelectTrigger id="type">
              <SelectValue placeholder="Select section type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text">Text</SelectItem>
              <SelectItem value="pricing">Pricing</SelectItem>
              <SelectItem value="timeline">Timeline</SelectItem>
              <SelectItem value="team">Team</SelectItem>
              <SelectItem value="testimonials">Testimonials</SelectItem>
              <SelectItem value="terms">Terms & Conditions</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="content">Content</Label>
          <Textarea
            id="content"
            name="content"
            value={editedSection.content}
            onChange={handleChange}
            disabled={readOnly}
            rows={10}
            className="font-mono"
          />
        </div>
        
        {onGenerateWithAI && !readOnly && (
          <div className="space-y-2 border-t pt-4">
            <Label htmlFor="aiPrompt">Generate with AI</Label>
            <div className="flex gap-2">
              <Textarea
                id="aiPrompt"
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                placeholder={`Enter instructions for generating the ${editedSection.name} section...`}
                rows={2}
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={handleGenerateWithAI}
                disabled={isGenerating || !aiPrompt}
                className="self-end"
              >
                <Sparkles className="mr-2 h-4 w-4" />
                {isGenerating ? 'Generating...' : 'Generate'}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProposalSectionEditor;
