import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Download, FileText, Sparkles, FileCode, Info, ExternalLink } from 'lucide-react';

interface FormatSelectorProps {
  onFormatSelected: (format: string) => void;
  trigger?: React.ReactNode;
}

interface FormatOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  isDownloadable: boolean;
  previewImage: string;
  features: string[];
}

export function FormatSelector({ onFormatSelected, trigger }: FormatSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');
  const [showPreview, setShowPreview] = useState<string | null>(null);

  const formatOptions: FormatOption[] = [
    {
      id: 'pdf',
      name: 'PDF Document',
      description: 'Professional, print-ready document format',
      icon: <Download className="h-5 w-5 mr-2 text-blue-600" />,
      isDownloadable: true,
      previewImage: '/images/pdf-preview.png',
      features: [
        'High-quality print format',
        'Professional layout and typography',
        'Consistent appearance across devices',
        'Ideal for formal business proposals',
        'Can be easily printed or attached to emails'
      ]
    },
    {
      id: 'docx',
      name: 'Word Document (DOCX)',
      description: 'Editable Microsoft Word format',
      icon: <FileText className="h-5 w-5 mr-2 text-blue-600" />,
      isDownloadable: true,
      previewImage: '/images/docx-preview.png',
      features: [
        'Fully editable document',
        'Compatible with Microsoft Word',
        'Can be customized after generation',
        'Supports tracked changes for collaboration',
        'Familiar format for most business users'
      ]
    },
    {
      id: 'markdown',
      name: 'Markdown',
      description: 'Simple, portable text format',
      icon: <FileCode className="h-5 w-5 mr-2 text-blue-600" />,
      isDownloadable: true,
      previewImage: '/images/markdown-preview.png',
      features: [
        'Lightweight plain text format',
        'Easy to edit in any text editor',
        'Can be converted to other formats',
        'Great for technical documentation',
        'Supported by many documentation systems'
      ]
    },
    {
      id: 'claude-html',
      name: 'Claude-Enhanced Design',
      description: 'Beautiful web design with enhanced styling',
      icon: <Sparkles className="h-5 w-5 mr-2 text-purple-600" />,
      isDownloadable: false,
      previewImage: '/images/claude-html-preview.png',
      features: [
        'AI-generated beautiful design',
        'Interactive elements and animations',
        'Responsive layout for all devices',
        'Modern visual styling with gradients and colors',
        'Best viewed in web browsers'
      ]
    }
  ];

  const handleSelect = () => {
    onFormatSelected(selectedFormat);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            Select Format
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>Select Document Format</DialogTitle>
          <DialogDescription>
            Choose the format for your proposal document. Some formats are downloadable while others are web-only.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <RadioGroup value={selectedFormat} onValueChange={setSelectedFormat} className="space-y-4">
              {formatOptions.map((format) => (
                <div
                  key={format.id}
                  className={`flex items-start space-x-2 border p-3 rounded-md hover:bg-gray-50 cursor-pointer transition-all ${selectedFormat === format.id ? 'border-blue-500 bg-blue-50' : ''}`}
                  onMouseEnter={() => setShowPreview(format.id)}
                  onMouseLeave={() => setShowPreview(null)}
                >
                  <RadioGroupItem value={format.id} id={format.id} className="mt-1" />
                  <Label htmlFor={format.id} className="flex-1 cursor-pointer">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        {format.icon}
                        <span className="font-medium">{format.name}</span>
                      </div>
                      <div className="flex items-center">
                        <Badge
                          variant="outline"
                          className={format.isDownloadable ? 'bg-green-50 text-green-700 border-green-200' : 'bg-blue-50 text-blue-700 border-blue-200'}
                        >
                          {format.isDownloadable ? (
                            <>
                              <Download className="h-3 w-3 mr-1" />
                              Downloadable
                            </>
                          ) : (
                            <>
                              <ExternalLink className="h-3 w-3 mr-1" />
                              Web Only
                            </>
                          )}
                        </Badge>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
                                <Info className="h-3 w-3 text-gray-400" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="font-medium">{format.name}</p>
                              <ul className="text-xs mt-1 list-disc pl-4">
                                {format.features.map((feature, index) => (
                                  <li key={index}>{feature}</li>
                                ))}
                              </ul>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      {format.description}
                    </p>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          <div className="border rounded-md p-4 flex flex-col items-center justify-center bg-gray-50">
            <h3 className="text-sm font-medium mb-2">Format Preview</h3>
            <div className="relative w-full h-64 bg-white rounded border overflow-hidden">
              {formatOptions.map((format) => (
                <div
                  key={format.id}
                  className={`absolute inset-0 transition-opacity duration-300 flex items-center justify-center ${(showPreview === format.id || (!showPreview && selectedFormat === format.id)) ? 'opacity-100' : 'opacity-0'}`}
                >
                  <div className="text-center p-4">
                    <div className="w-full h-40 bg-gray-100 rounded mb-2 flex items-center justify-center">
                      {/* This would be an actual image in production */}
                      <div className={`w-32 h-32 rounded ${format.id === 'pdf' ? 'bg-red-100' : format.id === 'docx' ? 'bg-blue-100' : format.id === 'markdown' ? 'bg-green-100' : 'bg-purple-100'} flex items-center justify-center`}>
                        {format.icon}
                      </div>
                    </div>
                    <p className="text-sm font-medium">{format.name}</p>
                    <p className="text-xs text-gray-500">{format.isDownloadable ? 'Can be downloaded' : 'View in browser only'}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSelect}>
            Generate {selectedFormat.toUpperCase()}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
