import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import {
  createProposalShareableLink,
  exportProposalToEmail,
  exportProposalToSocial
} from '@/api/proposal-generator-api';
import { ProposalExportFormat } from '@shared/types';
import {
  Share2,
  Mail,
  Link,
  Linkedin,
  Twitter,
  Facebook,
  Copy,
  Download,
  FileText,
  FileCode,
  Sparkles,
  Clock,
  Check,
  Loader2
} from 'lucide-react';

interface ProposalShareDialogProps {
  proposalId: string;
  proposalName: string;
  availableFormats?: string[];
  trigger?: React.ReactNode;
}

export function ProposalShareDialog({
  proposalId,
  proposalName,
  availableFormats = [],
  trigger
}: ProposalShareDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('link');

  // Link sharing state
  const [linkFormat, setLinkFormat] = useState<ProposalExportFormat>('claude-html');
  const [linkExpiration, setLinkExpiration] = useState<string>('7');
  const [shareableLink, setShareableLink] = useState<string>('');
  const [linkCopied, setLinkCopied] = useState(false);

  // Email sharing state
  const [emailRecipient, setEmailRecipient] = useState<string>('');
  const [emailFormat, setEmailFormat] = useState<ProposalExportFormat>('pdf');
  const [emailMessage, setEmailMessage] = useState<string>('');

  // Social sharing state
  const [socialPlatform, setSocialPlatform] = useState<string>('linkedin');
  const [socialMessage, setSocialMessage] = useState<string>('');

  const { toast } = useToast();

  // Create shareable link mutation
  const createLinkMutation = useMutation({
    mutationFn: () => createProposalShareableLink(proposalId, {
      format: linkFormat,
      expiresIn: parseInt(linkExpiration)
    }),
    onSuccess: (data) => {
      setShareableLink(data.shareUrl);
      toast({
        title: 'Link Created',
        description: `Shareable link created successfully. Expires on ${data.expiresAt ? new Date(data.expiresAt).toLocaleDateString() : 'N/A'}.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Link Creation Failed',
        description: error instanceof Error ? error.message : 'Failed to create shareable link',
        variant: 'destructive',
      });
    }
  });

  // Export to email mutation
  const emailExportMutation = useMutation({
    mutationFn: () => exportProposalToEmail(proposalId, {
      to: [emailRecipient],
      format: emailFormat,
      message: emailMessage
    }),
    onSuccess: () => {
      toast({
        title: 'Email Sent',
        description: `Proposal has been sent to ${emailRecipient}`,
      });
      setEmailRecipient('');
      setEmailMessage('');
    },
    onError: (error) => {
      toast({
        title: 'Email Failed',
        description: error instanceof Error ? error.message : 'Failed to send email',
        variant: 'destructive',
      });
    }
  });

  // Export to social media mutation
  const socialExportMutation = useMutation({
    mutationFn: () => exportProposalToSocial(proposalId, {
      platform: socialPlatform as any,
      message: socialMessage
    }),
    onSuccess: () => {
      toast({
        title: 'Shared to Social Media',
        description: `Proposal has been shared to ${socialPlatform}`,
      });
      setSocialMessage('');
    },
    onError: (error) => {
      toast({
        title: 'Social Share Failed',
        description: error instanceof Error ? error.message : 'Failed to share to social media',
        variant: 'destructive',
      });
    }
  });

  // Handle copy link to clipboard
  const handleCopyLink = () => {
    if (shareableLink) {
      navigator.clipboard.writeText(shareableLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);

      toast({
        title: 'Link Copied',
        description: 'Shareable link copied to clipboard',
      });
    }
  };

  // Get format icon
  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return <Download className="h-4 w-4 mr-2" />;
      case 'docx':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'markdown':
        return <FileCode className="h-4 w-4 mr-2" />;
      case 'claude-html':
        return <Sparkles className="h-4 w-4 mr-2" />;
      default:
        return <FileText className="h-4 w-4 mr-2" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Share Proposal</DialogTitle>
          <DialogDescription>
            Share "{proposalName}" with clients or colleagues
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="link">
              <Link className="h-4 w-4 mr-2" />
              Shareable Link
            </TabsTrigger>
            <TabsTrigger value="email">
              <Mail className="h-4 w-4 mr-2" />
              Email
            </TabsTrigger>
            <TabsTrigger value="social">
              <Linkedin className="h-4 w-4 mr-2" />
              Social Media
            </TabsTrigger>
          </TabsList>

          <TabsContent value="link" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="link-format">Format</Label>
                  <Select
                    value={linkFormat}
                    onValueChange={(value) => setLinkFormat(value as ProposalExportFormat)}
                  >
                    <SelectTrigger id="link-format">
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="claude-html">
                        <div className="flex items-center">
                          {getFormatIcon('claude-html')}
                          <span>Claude-Enhanced Design</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="pdf">
                        <div className="flex items-center">
                          {getFormatIcon('pdf')}
                          <span>PDF</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="docx">
                        <div className="flex items-center">
                          {getFormatIcon('docx')}
                          <span>Word Document</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="markdown">
                        <div className="flex items-center">
                          {getFormatIcon('markdown')}
                          <span>Markdown</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="link-expiration">Expires After</Label>
                  <Select value={linkExpiration} onValueChange={setLinkExpiration}>
                    <SelectTrigger id="link-expiration">
                      <SelectValue placeholder="Select expiration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>1 Day</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="7">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>7 Days</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="30">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>30 Days</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="90">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>90 Days</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {shareableLink ? (
                <div className="space-y-2">
                  <Label htmlFor="shareable-link">Shareable Link</Label>
                  <div className="flex">
                    <Input
                      id="shareable-link"
                      value={shareableLink}
                      readOnly
                      className="flex-1 rounded-r-none"
                    />
                    <Button
                      variant="secondary"
                      className="rounded-l-none"
                      onClick={handleCopyLink}
                    >
                      {linkCopied ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <Button
                  onClick={() => createLinkMutation.mutate()}
                  disabled={createLinkMutation.isPending}
                  className="w-full"
                >
                  {createLinkMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Link...
                    </>
                  ) : (
                    <>
                      <Link className="h-4 w-4 mr-2" />
                      Generate Shareable Link
                    </>
                  )}
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email-recipient">Recipient Email</Label>
                <Input
                  id="email-recipient"
                  type="email"
                  placeholder="<EMAIL>"
                  value={emailRecipient}
                  onChange={(e) => setEmailRecipient(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email-format">Format</Label>
                <Select
                  value={emailFormat}
                  onValueChange={(value) => setEmailFormat(value as ProposalExportFormat)}
                >
                  <SelectTrigger id="email-format">
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">
                      <div className="flex items-center">
                        {getFormatIcon('pdf')}
                        <span>PDF</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="docx">
                      <div className="flex items-center">
                        {getFormatIcon('docx')}
                        <span>Word Document</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="markdown">
                      <div className="flex items-center">
                        {getFormatIcon('markdown')}
                        <span>Markdown</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email-message">Message (Optional)</Label>
                <Textarea
                  id="email-message"
                  placeholder="Enter a message to include with the proposal..."
                  value={emailMessage}
                  onChange={(e) => setEmailMessage(e.target.value)}
                  rows={4}
                />
              </div>

              <Button
                onClick={() => emailExportMutation.mutate()}
                disabled={emailExportMutation.isPending || !emailRecipient}
                className="w-full"
              >
                {emailExportMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="social" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="social-platform">Platform</Label>
                <Select value={socialPlatform} onValueChange={setSocialPlatform}>
                  <SelectTrigger id="social-platform">
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="linkedin">
                      <div className="flex items-center">
                        <Linkedin className="h-4 w-4 mr-2" />
                        <span>LinkedIn</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="twitter">
                      <div className="flex items-center">
                        <Twitter className="h-4 w-4 mr-2" />
                        <span>Twitter</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="facebook">
                      <div className="flex items-center">
                        <Facebook className="h-4 w-4 mr-2" />
                        <span>Facebook</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="social-message">Message</Label>
                <Textarea
                  id="social-message"
                  placeholder="Enter a message to share with the proposal..."
                  value={socialMessage}
                  onChange={(e) => setSocialMessage(e.target.value)}
                  rows={4}
                />
              </div>

              <Button
                onClick={() => socialExportMutation.mutate()}
                disabled={socialExportMutation.isPending}
                className="w-full"
              >
                {socialExportMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sharing...
                  </>
                ) : (
                  <>
                    {socialPlatform === 'linkedin' ? (
                      <Linkedin className="h-4 w-4 mr-2" />
                    ) : socialPlatform === 'twitter' ? (
                      <Twitter className="h-4 w-4 mr-2" />
                    ) : (
                      <Facebook className="h-4 w-4 mr-2" />
                    )}
                    Share to {socialPlatform === 'twitter' ? 'Twitter' :
                              socialPlatform === 'linkedin' ? 'LinkedIn' : 'Facebook'}
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
