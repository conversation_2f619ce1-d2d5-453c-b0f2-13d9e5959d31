import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2, Sparkles } from 'lucide-react';
import { ProposalAIGenerationOptions, ProposalAIGenerationResponse } from '@shared/types';

interface ProposalAIGeneratorProps {
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
  opportunityName?: string;
  companyName?: string;
  onGenerate: (generatedContent: ProposalAIGenerationResponse) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

/**
 * AI-powered proposal generator component
 */
const ProposalAIGenerator: React.FC<ProposalAIGeneratorProps> = ({
  opportunityId,
  companyId,
  contactIds,
  opportunityName,
  companyName,
  onGenerate,
  onCancel,
  isLoading = false,
}) => {
  const [prompt, setPrompt] = useState('');
  const [model, setModel] = useState('claude-3-opus-20240229');
  const [includeSections, setIncludeSections] = useState({
    executiveSummary: true,
    solution: true,
    timeline: true,
    pricing: true,
    team: false,
    testimonials: false,
    terms: true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const options: ProposalAIGenerationOptions = {
      opportunityId,
      companyId,
      contactIds,
      prompt,
      model,
      includeSections,
    };
    
    // This would typically call an API, but for now we'll just pass the options
    // to the parent component
    onGenerate({
      title: `Proposal for ${companyName || 'Client'}`,
      description: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
      sections: [],
      terms: '',
      aiPrompt: prompt,
      aiModel: model,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-primary" />
            AI Proposal Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="model">AI Model</Label>
            <Select
              value={model}
              onValueChange={setModel}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="claude-3-opus-20240229">Claude 3 Opus (Highest Quality)</SelectItem>
                <SelectItem value="claude-3-sonnet-20240229">Claude 3 Sonnet (Balanced)</SelectItem>
                <SelectItem value="claude-3-haiku-20240307">Claude 3 Haiku (Fastest)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="prompt">Instructions for AI</Label>
            <Textarea
              id="prompt"
              placeholder="Describe what you want in the proposal. Include specific details about the client, their needs, your solution, pricing, etc."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[150px]"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label>Sections to Include</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="executiveSummary" 
                  checked={includeSections.executiveSummary}
                  onCheckedChange={(checked) => 
                    setIncludeSections({...includeSections, executiveSummary: !!checked})
                  }
                />
                <Label htmlFor="executiveSummary">Executive Summary</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="solution" 
                  checked={includeSections.solution}
                  onCheckedChange={(checked) => 
                    setIncludeSections({...includeSections, solution: !!checked})
                  }
                />
                <Label htmlFor="solution">Solution Description</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="timeline" 
                  checked={includeSections.timeline}
                  onCheckedChange={(checked) => 
                    setIncludeSections({...includeSections, timeline: !!checked})
                  }
                />
                <Label htmlFor="timeline">Project Timeline</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="pricing" 
                  checked={includeSections.pricing}
                  onCheckedChange={(checked) => 
                    setIncludeSections({...includeSections, pricing: !!checked})
                  }
                />
                <Label htmlFor="pricing">Pricing Details</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="team" 
                  checked={includeSections.team}
                  onCheckedChange={(checked) => 
                    setIncludeSections({...includeSections, team: !!checked})
                  }
                />
                <Label htmlFor="team">Team Introduction</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="testimonials" 
                  checked={includeSections.testimonials}
                  onCheckedChange={(checked) => 
                    setIncludeSections({...includeSections, testimonials: !!checked})
                  }
                />
                <Label htmlFor="testimonials">Client Testimonials</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="terms" 
                  checked={includeSections.terms}
                  onCheckedChange={(checked) => 
                    setIncludeSections({...includeSections, terms: !!checked})
                  }
                />
                <Label htmlFor="terms">Terms & Conditions</Label>
              </div>
            </div>
          </div>
          
          {(opportunityName || companyName) && (
            <div className="p-3 bg-muted rounded-md">
              <h4 className="text-sm font-medium">Context Information</h4>
              <div className="text-xs text-muted-foreground mt-1">
                {opportunityName && <div>Opportunity: {opportunityName}</div>}
                {companyName && <div>Company: {companyName}</div>}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
        )}
        
        <Button
          type="submit"
          disabled={isLoading || !prompt}
          className="min-w-[120px]"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Generate
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

export default ProposalAIGenerator;
