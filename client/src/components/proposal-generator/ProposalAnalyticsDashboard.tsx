import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { getProposalAnalytics } from '@/api/proposal-generator-api';
import { ProposalAnalytics, ProposalAnalyticsEvent } from '@shared/types';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartData,
  ChartOptions
} from 'chart.js';
import {
  Eye,
  Download,
  Share2,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Users,
  Calendar,
  MapPin,
  Globe,
  Smartphone,
  Laptop
} from 'lucide-react';
import { format, parseISO, formatDistanceToNow } from 'date-fns';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ProposalAnalyticsDashboardProps {
  proposalId: string;
  proposalName: string;
}

const ProposalAnalyticsDashboard: React.FC<ProposalAnalyticsDashboardProps> = ({
  proposalId,
  proposalName
}) => {
  const [analytics, setAnalytics] = useState<ProposalAnalytics | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  const { toast } = useToast();

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await getProposalAnalytics(proposalId);
      setAnalytics(data);
    } catch (err) {
      setError('Failed to load analytics data');
      toast({
        title: 'Error',
        description: 'Failed to load proposal analytics',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [proposalId]);

  const handleRefresh = () => {
    fetchAnalytics();
  };

  // Prepare chart data for views over time
  const prepareViewsChartData = (): ChartData<'line'> => {
    if (!analytics || !analytics.events) {
      return {
        labels: [],
        datasets: []
      };
    }

    // Get view events and sort by timestamp
    const viewEvents = analytics.events
      .filter(event => event.type === 'view')
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    // Group by day
    const viewsByDay = viewEvents.reduce((acc, event) => {
      const day = format(new Date(event.timestamp), 'yyyy-MM-dd');
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const labels = Object.keys(viewsByDay);
    const data = Object.values(viewsByDay);

    return {
      labels,
      datasets: [
        {
          label: 'Views',
          data,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.5)',
          tension: 0.3
        }
      ]
    };
  };

  // Prepare chart data for event types
  const prepareEventTypesChartData = (): ChartData<'bar'> => {
    if (!analytics || !analytics.events) {
      return {
        labels: [],
        datasets: []
      };
    }

    // Count events by type
    const eventCounts = analytics.events.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const labels = Object.keys(eventCounts).map(type =>
      type.charAt(0).toUpperCase() + type.slice(1)
    );
    const data = Object.values(eventCounts);

    return {
      labels,
      datasets: [
        {
          label: 'Event Count',
          data,
          backgroundColor: [
            'rgba(59, 130, 246, 0.7)', // view - blue
            'rgba(16, 185, 129, 0.7)', // download - green
            'rgba(245, 158, 11, 0.7)', // share - amber
            'rgba(139, 92, 246, 0.7)', // accept - purple
            'rgba(239, 68, 68, 0.7)',  // reject - red
            'rgba(107, 114, 128, 0.7)' // comment - gray
          ]
        }
      ]
    };
  };

  const chartOptions: ChartOptions<'line'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      }
    }
  };

  const renderEventIcon = (type: string) => {
    switch (type) {
      case 'view':
        return <Eye className="h-4 w-4" />;
      case 'download':
        return <Download className="h-4 w-4" />;
      case 'share':
        return <Share2 className="h-4 w-4" />;
      case 'accept':
        return <CheckCircle className="h-4 w-4" />;
      case 'reject':
        return <XCircle className="h-4 w-4" />;
      case 'comment':
        return <Clock className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const renderEventList = () => {
    if (!analytics || !analytics.events || analytics.events.length === 0) {
      return <p className="text-muted-foreground">No events recorded yet.</p>;
    }

    // Sort events by timestamp (newest first)
    const sortedEvents = [...analytics.events].sort(
      (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    return (
      <div className="space-y-4">
        {sortedEvents.map((event, index) => (
          <div key={index} className="flex items-start space-x-3 border-b pb-3">
            <div className="bg-muted p-2 rounded-full">
              {renderEventIcon(event.type)}
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <div>
                  <Badge variant="outline" className="mb-1">
                    {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                  </Badge>
                  <p className="text-sm text-muted-foreground">
                    {event.data && JSON.stringify(event.data)}
                  </p>
                </div>
                <div className="text-right text-sm text-muted-foreground">
                  <p>{format(new Date(event.timestamp), 'MMM d, yyyy')}</p>
                  <p>{format(new Date(event.timestamp), 'h:mm a')}</p>
                </div>
              </div>
              {event.location && (
                <div className="mt-1 flex items-center text-xs text-muted-foreground">
                  <MapPin className="h-3 w-3 mr-1" />
                  {event.location.city && event.location.region ?
                    `${event.location.city}, ${event.location.region}` :
                    event.location.country}
                </div>
              )}
              {event.userAgent && (
                <div className="mt-1 flex items-center text-xs text-muted-foreground">
                  {event.userAgent.includes('Mobile') ?
                    <Smartphone className="h-3 w-3 mr-1" /> :
                    <Laptop className="h-3 w-3 mr-1" />}
                  {event.userAgent}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[250px]" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Skeleton className="h-[120px] w-full" />
          <Skeleton className="h-[120px] w-full" />
          <Skeleton className="h-[120px] w-full" />
        </div>
        <Skeleton className="h-[300px] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Analytics Error</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">{proposalName} Analytics</h2>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Eye className="h-5 w-5 text-blue-500 mr-2" />
              <div className="text-2xl font-bold">{analytics?.views || 0}</div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {analytics?.uniqueViews || 0} unique viewers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Downloads</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Download className="h-5 w-5 text-green-500 mr-2" />
              <div className="text-2xl font-bold">{analytics?.downloads || 0}</div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {analytics?.events?.filter(e => e.type === 'download').length || 0} total downloads
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Average View Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-amber-500 mr-2" />
              <div className="text-2xl font-bold">
                {analytics?.averageViewDuration ?
                  `${Math.round(analytics.averageViewDuration / 60)}m ${Math.round(analytics.averageViewDuration % 60)}s` :
                  'N/A'}
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {analytics?.lastViewedAt ?
                `Last viewed ${formatDistanceToNow(new Date(analytics.lastViewedAt))} ago` :
                'No views yet'}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Event Log</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Views Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <Line
                  data={prepareViewsChartData()}
                  options={chartOptions}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Event Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <Bar
                  data={prepareEventTypesChartData()}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'top' as const,
                      },
                      tooltip: {
                        mode: 'index',
                        intersect: false,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          precision: 0
                        }
                      }
                    },
                    indexAxis: 'y' as const,
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>Event Log</CardTitle>
              <CardDescription>
                Detailed history of all interactions with this proposal
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderEventList()}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProposalAnalyticsDashboard;
