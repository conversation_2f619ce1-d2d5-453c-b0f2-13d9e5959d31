import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  FileText,
  Search,
  Plus,
  Sparkles,
  Download,
  Send,
  Loader2,
  Tag,
  Filter,
  Check,
  X,
  ExternalLink
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  getProposalTemplates,
  generateProposal,
  generateProposalDocument,
  sendProposal
} from '@/api/proposal-generator-api';

import type {
  ProposalGeneratorDialogProps,
  Template,
  Proposal,
  ProposalGenerationResponse,
  ProposalExportResponse
} from '@/types/proposal-generator-dialog';

export function ProposalGeneratorDialog({
  opportunityId,
  contactId,
  companyId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Generate Proposal'
}: ProposalGeneratorDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [generatedProposal, setGeneratedProposal] = useState<Proposal | null>(null);
  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);
  const [isSendingProposal, setIsSendingProposal] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<'pdf' | 'docx' | 'markdown' | 'claude-html'>('pdf');

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Reset state when dialog is closed
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setSelectedCategory('');
      setSelectedTemplate(null);
      setGeneratedProposal(null);
      setIsGeneratingDocument(false);
      setIsSendingProposal(false);
    }
  }, [isOpen]);

  // Fetch proposal templates
  const {
    data: templates,
    isLoading: isTemplatesLoading
  } = useQuery({
    queryKey: ['proposal-templates', searchQuery, selectedCategory],
    queryFn: () => getProposalTemplates({
      search: searchQuery,
      category: selectedCategory || undefined
    }),
    enabled: isOpen
  });

  // Generate proposal mutation
  const generateProposalMutation = useMutation({
    mutationFn: (params: { opportunityId: string; templateId: string; contactId?: string; companyId?: string }) =>
      generateProposal(params),
    onSuccess: (data: any) => {
      setGeneratedProposal(data as Proposal);
      toast({
        title: 'Proposal Generated',
        description: 'Your proposal has been generated successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate proposal',
        variant: 'destructive',
      });
    }
  });

  // Generate document mutation
  const generateDocumentMutation = useMutation({
    mutationFn: ({ id, format }: { id: string; format: string }) =>
      generateProposalDocument(id, { format: format as any }),
    onSuccess: (data: any) => {
      setIsGeneratingDocument(false);

      if (data.documentUrl) {
        // For downloadable formats, open in a new tab
        if (data.isDownloadable) {
          window.open(data.documentUrl, '_blank');
        }

        // Update the generated proposal with the document URL and format info
        if (generatedProposal) {
          setGeneratedProposal({
            ...generatedProposal,
            documentUrl: data.documentUrl,
            format: data.format,
            availableFormats: [
              ...(generatedProposal.availableFormats || []),
              data.format
            ].filter((v, i, a) => a.indexOf(v) === i) // Remove duplicates
          });
        }

        toast({
          title: 'Document Generated',
          description: `Your proposal has been generated in ${data.format.toUpperCase()} format${
            data.isDownloadable ? ' and is ready to download' : ' and is ready to view'
          }`,
        });
      }
    },
    onError: (error) => {
      setIsGeneratingDocument(false);
      toast({
        title: 'Document Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate document',
        variant: 'destructive',
      });
    }
  });

  // Send proposal mutation
  const sendProposalMutation = useMutation({
    mutationFn: sendProposal,
    onSuccess: (data) => {
      setIsSendingProposal(false);

      // Update the generated proposal with the new status
      if (generatedProposal) {
        setGeneratedProposal({
          ...generatedProposal,
          status: data.status,
          sentAt: data.sentAt
        });
      }

      toast({
        title: 'Proposal Sent',
        description: 'Your proposal has been sent successfully',
      });
    },
    onError: (error) => {
      setIsSendingProposal(false);
      toast({
        title: 'Sending Failed',
        description: error instanceof Error ? error.message : 'Failed to send proposal',
        variant: 'destructive',
      });
    }
  });

  // Handle search
  const handleSearch = () => {
    // The query will be refetched automatically due to the dependency on searchQuery
  };

  // Handle template selection
  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
  };

  // Handle generate proposal
  const handleGenerateProposal = () => {
    if (!opportunityId) {
      toast({
        title: 'Error',
        description: 'Opportunity ID is required to generate a proposal',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedTemplate) {
      toast({
        title: 'Error',
        description: 'Please select a template first',
        variant: 'destructive',
      });
      return;
    }

    generateProposalMutation.mutate({
      opportunityId,
      templateId: selectedTemplate._id,
      contactId,
      companyId
    });
  };

  // Handle generate document
  const handleGenerateDocument = () => {
    if (!generatedProposal) {
      toast({
        title: 'Error',
        description: 'Please generate a proposal first',
        variant: 'destructive',
      });
      return;
    }

    setIsGeneratingDocument(true);
    generateDocumentMutation.mutate({
      id: generatedProposal._id,
      format: selectedFormat
    });
  };

  // Handle send proposal
  const handleSendProposal = () => {
    if (!generatedProposal) {
      toast({
        title: 'Error',
        description: 'Please generate a proposal first',
        variant: 'destructive',
      });
      return;
    }

    if (!generatedProposal.documentUrl) {
      toast({
        title: 'Error',
        description: 'Please generate a document first',
        variant: 'destructive',
      });
      return;
    }

    setIsSendingProposal(true);
    sendProposalMutation.mutate(generatedProposal._id);
  };

  // Get badge color for category
  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case 'sales':
        return 'bg-blue-100 text-blue-800';
      case 'service':
        return 'bg-green-100 text-green-800';
      case 'partnership':
        return 'bg-purple-100 text-purple-800';
      case 'custom':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get badge color for status
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'viewed':
        return 'bg-purple-100 text-purple-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
        >
          <FileText className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Proposal Generator</DialogTitle>
          <DialogDescription>
            Generate professional proposals based on opportunity data and customizable templates.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Context information */}
          {(opportunityName || contactName || companyName) && (
            <Card>
              <CardHeader className="py-2">
                <CardTitle className="text-sm">Context</CardTitle>
              </CardHeader>
              <CardContent className="py-2 space-y-1">
                {opportunityName && (
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Opportunity:</span>
                    <span>{opportunityName}</span>
                  </div>
                )}
                {contactName && (
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Contact:</span>
                    <span>{contactName}</span>
                  </div>
                )}
                {companyName && (
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Company:</span>
                    <span>{companyName}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {!generatedProposal ? (
            <div className="space-y-4">
              <div className="flex flex-col md:flex-row gap-2">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search templates..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSearch();
                        }
                      }}
                    />
                  </div>
                </div>
                <div className="w-full md:w-[200px]">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <div className="flex items-center">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="All Categories" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Categories</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="service">Service</SelectItem>
                      <SelectItem value="partnership">Partnership</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleSearch}>
                  Search
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Templates List */}
                <Card className="h-[400px] overflow-y-auto">
                  <CardHeader className="py-3">
                    <CardTitle className="text-lg">Templates</CardTitle>
                  </CardHeader>
                  <CardContent className="py-0">
                    {isTemplatesLoading ? (
                      <div className="space-y-2">
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                      </div>
                    ) : templates?.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>No templates found</p>
                        <p className="text-sm">Try a different search or category</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {templates?.map((template: Template) => (
                          <div
                            key={template._id}
                            className={`p-3 rounded-md cursor-pointer hover:bg-gray-50 ${
                              selectedTemplate?._id === template._id ? 'bg-blue-50 border border-blue-200' : 'border'
                            }`}
                            onClick={() => handleTemplateSelect(template)}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{template.name}</p>
                                <div className="flex items-center space-x-2 mt-1">
                                  <Badge className={getCategoryBadgeColor(template.category)}>
                                    {template.category.charAt(0).toUpperCase() + template.category.slice(1)}
                                  </Badge>
                                  {template.isDefault && (
                                    <Badge variant="outline">Default</Badge>
                                  )}
                                </div>
                              </div>
                              <div className="text-xs text-gray-500">
                                Used {template.usageCount} times
                              </div>
                            </div>
                            {template.description && (
                              <p className="text-sm text-gray-600 mt-2">{template.description}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Template Details */}
                <Card className="h-[400px] overflow-y-auto">
                  <CardHeader className="py-3">
                    <CardTitle className="text-lg">Template Details</CardTitle>
                  </CardHeader>
                  <CardContent className="py-0">
                    {!selectedTemplate ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>Select a template to see details</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <h3 className="font-medium text-lg">{selectedTemplate.name}</h3>
                          <Badge className={getCategoryBadgeColor(selectedTemplate.category)}>
                            {selectedTemplate.category.charAt(0).toUpperCase() + selectedTemplate.category.slice(1)}
                          </Badge>
                          {selectedTemplate.isDefault && (
                            <Badge variant="outline" className="ml-2">Default</Badge>
                          )}
                        </div>

                        {selectedTemplate.description && (
                          <div>
                            <h4 className="font-medium text-sm">Description</h4>
                            <p className="text-sm">{selectedTemplate.description}</p>
                          </div>
                        )}

                        <div>
                          <h4 className="font-medium text-sm">Usage Statistics</h4>
                          <p className="text-sm">
                            Used {selectedTemplate.usageCount} times
                            {selectedTemplate.successCount > 0 && ` (${selectedTemplate.successCount} successful)`}
                          </p>
                        </div>

                        {selectedTemplate.tags && selectedTemplate.tags.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm">Tags</h4>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {selectedTemplate.tags.map((tag) => (
                                <Badge key={tag} variant="outline">
                                  <Tag className="h-3 w-3 mr-1" />
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="pt-4">
                          <Button
                            onClick={handleGenerateProposal}
                            disabled={generateProposalMutation.isPending || !opportunityId}
                            className="w-full"
                          >
                            {generateProposalMutation.isPending ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Generating...
                              </>
                            ) : (
                              <>
                                <Sparkles className="h-4 w-4 mr-2" />
                                Generate Proposal
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>{generatedProposal.name}</CardTitle>
                    <Badge className={getStatusBadgeColor(generatedProposal.status)}>
                      {generatedProposal.status.charAt(0).toUpperCase() + generatedProposal.status.slice(1)}
                    </Badge>
                  </div>
                  {generatedProposal.description && (
                    <CardDescription>{generatedProposal.description}</CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm">Value</Label>
                        <p className="font-medium">{generatedProposal.currency} {generatedProposal.value.toLocaleString()}</p>
                      </div>
                      {generatedProposal.validUntil && (
                        <div>
                          <Label className="text-sm">Valid Until</Label>
                          <p className="font-medium">{new Date(generatedProposal.validUntil).toLocaleDateString()}</p>
                        </div>
                      )}
                    </div>

                    <div>
                      <Label className="text-sm">Sections</Label>
                      <div className="space-y-2 mt-1">
                        {generatedProposal.sections.map((section) => (
                          <div key={section.id} className="p-2 border rounded-md">
                            <div className="flex justify-between items-center">
                              <p className="font-medium">{section.name}</p>
                              <Badge variant="outline">{section.type}</Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {generatedProposal.tags && generatedProposal.tags.length > 0 && (
                      <div>
                        <Label className="text-sm">Tags</Label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {generatedProposal.tags.map((tag) => (
                            <Badge key={tag} variant="outline">
                              <Tag className="h-3 w-3 mr-1" />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => setGeneratedProposal(null)}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Start Over
                  </Button>

                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="format-select" className="whitespace-nowrap">Format:</Label>
                      <Select value={selectedFormat} onValueChange={(value) => setSelectedFormat(value as any)}>
                        <SelectTrigger id="format-select" className="w-[180px]">
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pdf">
                            <div className="flex items-center">
                              <span>PDF</span>
                              <Badge variant="outline" className="ml-2 bg-green-50">Downloadable</Badge>
                            </div>
                          </SelectItem>
                          <SelectItem value="docx">
                            <div className="flex items-center">
                              <span>DOCX</span>
                              <Badge variant="outline" className="ml-2 bg-green-50">Downloadable</Badge>
                            </div>
                          </SelectItem>
                          <SelectItem value="markdown">
                            <div className="flex items-center">
                              <span>Markdown</span>
                              <Badge variant="outline" className="ml-2 bg-green-50">Downloadable</Badge>
                            </div>
                          </SelectItem>
                          <SelectItem value="claude-html">
                            <div className="flex items-center">
                              <span>Claude HTML</span>
                              <Badge variant="outline" className="ml-2 bg-blue-50">Web Only</Badge>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={handleGenerateDocument}
                        disabled={isGeneratingDocument}
                        className="flex-1"
                      >
                        {isGeneratingDocument ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Download className="h-4 w-4 mr-2" />
                        )}
                        Generate {selectedFormat.toUpperCase()}
                      </Button>

                      <Button
                        onClick={handleSendProposal}
                        disabled={isSendingProposal || !generatedProposal.documentUrl || generatedProposal.status !== 'draft'}
                        className="flex-1"
                      >
                        {isSendingProposal ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Send className="h-4 w-4 mr-2" />
                        )}
                        Send Proposal
                      </Button>
                    </div>
                  </div>
                </CardFooter>
              </Card>

              {generatedProposal.documentUrl && (
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg">Document Preview</CardTitle>
                      {generatedProposal.format && (
                        <Badge className={
                          ['pdf', 'docx', 'markdown'].includes(generatedProposal.format)
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }>
                          {generatedProposal.format.toUpperCase()}
                          {['pdf', 'docx', 'markdown'].includes(generatedProposal.format) && (
                            <span className="ml-1">• Downloadable</span>
                          )}
                          {generatedProposal.format === 'claude-html' && (
                            <span className="ml-1">• Web Only</span>
                          )}
                        </Badge>
                      )}
                    </div>
                    {generatedProposal.availableFormats && generatedProposal.availableFormats.length > 0 && (
                      <CardDescription>
                        Available formats: {generatedProposal.availableFormats.map(f => f.toUpperCase()).join(', ')}
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md overflow-hidden">
                      <iframe
                        src={generatedProposal.documentUrl}
                        className="w-full h-[400px]"
                        title="Proposal Document"
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <div className="text-sm text-muted-foreground">
                      {generatedProposal.format === 'claude-html' ? (
                        <span>Claude-enhanced design with beautiful styling</span>
                      ) : ['pdf', 'docx', 'markdown'].includes(generatedProposal.format || '') ? (
                        <span>This format can be downloaded and shared with clients</span>
                      ) : (
                        <span>Preview mode</span>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => window.open(generatedProposal.documentUrl, '_blank')}
                    >
                      {['pdf', 'docx', 'markdown'].includes(generatedProposal.format || '') ? (
                        <>
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </>
                      ) : (
                        <>
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open in New Tab
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
