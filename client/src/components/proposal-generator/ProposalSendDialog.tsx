import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { sendProposal } from '@/api/proposal-generator-api';
import { Mail, Loader2 } from 'lucide-react';

interface ProposalSendDialogProps {
  proposalId: string;
  proposalName: string;
  recipientEmail?: string;
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

const ProposalSendDialog: React.FC<ProposalSendDialogProps> = ({
  proposalId,
  proposalName,
  recipientEmail = '',
  trigger,
  onSuccess
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [to, setTo] = useState<string>(recipientEmail);
  const [cc, setCc] = useState<string>('');
  const [bcc, setBcc] = useState<string>('');
  const [subject, setSubject] = useState<string>(`Proposal: ${proposalName}`);
  const [message, setMessage] = useState<string>('');
  const [includeAttachment, setIncludeAttachment] = useState<boolean>(true);
  const [attachmentFormat, setAttachmentFormat] = useState<string>('pdf');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  const { toast } = useToast();
  
  const handleSend = async () => {
    if (!to) {
      toast({
        title: 'Missing recipient',
        description: 'Please enter at least one recipient email address',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      setIsLoading(true);
      
      await sendProposal(proposalId);
      
      toast({
        title: 'Proposal sent',
        description: `Proposal has been sent to ${to}`,
      });
      
      setIsOpen(false);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      toast({
        title: 'Failed to send proposal',
        description: error instanceof Error ? error.message : 'An error occurred while sending the proposal',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const validateEmails = (emails: string): boolean => {
    if (!emails.trim()) return true;
    
    const emailList = emails.split(',').map(email => email.trim());
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    return emailList.every(email => emailRegex.test(email));
  };
  
  const isToValid = validateEmails(to);
  const isCcValid = validateEmails(cc);
  const isBccValid = validateEmails(bcc);
  const isFormValid = isToValid && isCcValid && isBccValid && to.trim() !== '';
  
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="default" size="sm">
            <Mail className="h-4 w-4 mr-2" />
            Send Proposal
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Send Proposal</DialogTitle>
          <DialogDescription>
            Send "{proposalName}" to recipients via email
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="to">
              To <span className="text-destructive">*</span>
            </Label>
            <Input
              id="to"
              placeholder="<EMAIL>"
              value={to}
              onChange={(e) => setTo(e.target.value)}
              className={!isToValid ? 'border-destructive' : ''}
            />
            {!isToValid && (
              <p className="text-sm text-destructive">Please enter valid email addresses separated by commas</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cc">CC</Label>
            <Input
              id="cc"
              placeholder="<EMAIL>"
              value={cc}
              onChange={(e) => setCc(e.target.value)}
              className={!isCcValid ? 'border-destructive' : ''}
            />
            {!isCcValid && (
              <p className="text-sm text-destructive">Please enter valid email addresses separated by commas</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="bcc">BCC</Label>
            <Input
              id="bcc"
              placeholder="<EMAIL>"
              value={bcc}
              onChange={(e) => setBcc(e.target.value)}
              className={!isBccValid ? 'border-destructive' : ''}
            />
            {!isBccValid && (
              <p className="text-sm text-destructive">Please enter valid email addresses separated by commas</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              placeholder="Enter your message here..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={5}
            />
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeAttachment"
                checked={includeAttachment}
                onCheckedChange={(checked) => setIncludeAttachment(checked as boolean)}
              />
              <Label htmlFor="includeAttachment">Include proposal as attachment</Label>
            </div>
            
            {includeAttachment && (
              <div className="space-y-2 ml-6">
                <Label htmlFor="attachmentFormat">Format</Label>
                <Select value={attachmentFormat} onValueChange={setAttachmentFormat}>
                  <SelectTrigger id="attachmentFormat" className="w-[180px]">
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF Document</SelectItem>
                    <SelectItem value="docx">Word Document (DOCX)</SelectItem>
                    <SelectItem value="markdown">Markdown</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSend} 
            disabled={isLoading || !isFormValid}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Send Proposal
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProposalSendDialog;
