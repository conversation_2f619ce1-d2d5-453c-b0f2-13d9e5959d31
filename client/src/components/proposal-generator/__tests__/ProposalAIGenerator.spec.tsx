import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import ProposalAIGenerator from '../ProposalAIGenerator';

// Mock the onGenerate function
const mockOnGenerate = vi.fn();
const mockOnCancel = vi.fn();

describe('ProposalAIGenerator', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    render(React.createElement(ProposalAIGenerator, { onGenerate: mockOnGenerate }));

    // Check that the component renders with the correct title
    expect(screen.getByText('AI Proposal Generator')).toBeInTheDocument();
    
    // Check that the model selector is present
    expect(screen.getByText('AI Model')).toBeInTheDocument();
    
    // Check that the instructions textarea is present
    expect(screen.getByLabelText('Instructions for AI')).toBeInTheDocument();
    
    // Check that the sections checkboxes are present
    expect(screen.getByLabelText('Executive Summary')).toBeInTheDocument();
    expect(screen.getByLabelText('Solution Description')).toBeInTheDocument();
    expect(screen.getByLabelText('Project Timeline')).toBeInTheDocument();
    expect(screen.getByLabelText('Pricing Details')).toBeInTheDocument();
    expect(screen.getByLabelText('Team Introduction')).toBeInTheDocument();
    expect(screen.getByLabelText('Client Testimonials')).toBeInTheDocument();
    expect(screen.getByLabelText('Terms & Conditions')).toBeInTheDocument();
    
    // Check that the generate button is present and disabled (since prompt is empty)
    const generateButton = screen.getByRole('button', { name: /generate/i });
    expect(generateButton).toBeInTheDocument();
    expect(generateButton).toBeDisabled();
  });

  it('enables the generate button when prompt is entered', () => {
    render(React.createElement(ProposalAIGenerator, { onGenerate: mockOnGenerate }));

    // Find the prompt textarea and enter text
    const promptTextarea = screen.getByLabelText('Instructions for AI');
    fireEvent.change(promptTextarea, { target: { value: 'Create a proposal for a new website' } });
    
    // Check that the generate button is enabled
    const generateButton = screen.getByRole('button', { name: /generate/i });
    expect(generateButton).not.toBeDisabled();
  });

  it('calls onGenerate with correct data when form is submitted', () => {
    render(React.createElement(ProposalAIGenerator, {
      opportunityId: 'opp123',
      companyId: 'comp456',
      contactIds: ['contact789'],
      opportunityName: 'New Website Project',
      companyName: 'Acme Inc',
      onGenerate: mockOnGenerate
    }));

    // Find the prompt textarea and enter text
    const promptTextarea = screen.getByLabelText('Instructions for AI');
    fireEvent.change(promptTextarea, { target: { value: 'Create a proposal for a new website' } });
    
    // Submit the form
    const generateButton = screen.getByRole('button', { name: /generate/i });
    fireEvent.click(generateButton);
    
    // Check that onGenerate was called with the correct data
    expect(mockOnGenerate).toHaveBeenCalledTimes(1);
    expect(mockOnGenerate).toHaveBeenCalledWith(expect.objectContaining({
      title: 'Proposal for Acme Inc',
      description: expect.any(String),
      aiPrompt: 'Create a proposal for a new website',
      aiModel: 'claude-3-opus-20240229',
    }));
  });

  it('shows context information when provided', () => {
    render(React.createElement(ProposalAIGenerator, {
      opportunityName: 'New Website Project',
      companyName: 'Acme Inc',
      onGenerate: mockOnGenerate
    }));

    // Check that the context information is displayed
    expect(screen.getByText('Context Information')).toBeInTheDocument();
    expect(screen.getByText('Opportunity: New Website Project')).toBeInTheDocument();
    expect(screen.getByText('Company: Acme Inc')).toBeInTheDocument();
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(React.createElement(ProposalAIGenerator, {
      onGenerate: mockOnGenerate,
      onCancel: mockOnCancel
    }));

    // Find and click the cancel button
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);
    
    // Check that onCancel was called
    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('shows loading state when isLoading is true', () => {
    render(React.createElement(ProposalAIGenerator, {
      onGenerate: mockOnGenerate,
      isLoading: true
    }));

    // Check that the generate button shows loading state
    expect(screen.getByText('Generating...')).toBeInTheDocument();
    
    // Check that the generate button is disabled
    const generateButton = screen.getByRole('button', { name: /generating/i });
    expect(generateButton).toBeDisabled();
  });
});
