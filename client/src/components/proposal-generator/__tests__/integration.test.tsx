import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProposalAIGenerator from '../ProposalAIGenerator';
import ProposalDownloadOptions from '../ProposalDownloadOptions';
import { generateProposalWithAI, generateProposalDocument } from '@/api/proposal-generator-api';

// Mock the API functions
jest.mock('@/api/proposal-generator-api', () => ({
  generateProposalWithAI: jest.fn(),
  generateProposalDocument: jest.fn()
}));

// Create a new QueryClient for testing
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Wrap components with QueryClientProvider
const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>
      {ui}
    </QueryClientProvider>
  );
};

describe('Proposal Generator Integration', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('generates a proposal with AI and then downloads it', async () => {
    // Mock API responses
    const mockProposalResponse = {
      title: 'Website Development Proposal',
      description: 'A comprehensive proposal for developing a new website',
      sections: [
        {
          title: 'Executive Summary',
          content: 'This proposal outlines our approach to developing your new website...',
          type: 'text',
          order: 1
        },
        {
          title: 'Solution Description',
          content: 'Our solution includes the following features...',
          type: 'text',
          order: 2
        }
      ],
      terms: 'Standard terms and conditions apply...',
      aiPrompt: 'Create a proposal for a new website',
      aiModel: 'claude-3-opus-20240229'
    };
    
    const mockDocumentResponse = {
      url: 'https://example.com/proposals/123.pdf',
      expiresAt: new Date('2023-12-31')
    };
    
    (generateProposalWithAI as jest.Mock).mockResolvedValue(mockProposalResponse);
    (generateProposalDocument as jest.Mock).mockResolvedValue(mockDocumentResponse);
    
    // Mock callback functions
    const mockOnGenerate = jest.fn();
    const mockOnDownload = jest.fn();
    
    // Step 1: Render the AI Generator
    const { unmount } = renderWithQueryClient(
      <ProposalAIGenerator
        opportunityId="opp123"
        companyId="comp456"
        opportunityName="New Website Project"
        companyName="Acme Inc"
        onGenerate={mockOnGenerate}
      />
    );
    
    // Enter prompt
    const promptTextarea = screen.getByLabelText('Instructions for AI');
    await userEvent.type(promptTextarea, 'Create a proposal for a new website');
    
    // Submit the form
    const generateButton = screen.getByRole('button', { name: /generate/i });
    await userEvent.click(generateButton);
    
    // Check that the API was called correctly
    expect(generateProposalWithAI).not.toHaveBeenCalled(); // Direct API call not made in component
    expect(mockOnGenerate).toHaveBeenCalledWith(expect.objectContaining({
      title: expect.any(String),
      description: expect.any(String),
      aiPrompt: 'Create a proposal for a new website',
      aiModel: expect.any(String)
    }));
    
    // Unmount the AI Generator
    unmount();
    
    // Step 2: Render the Download Options
    renderWithQueryClient(
      <ProposalDownloadOptions
        proposalId="proposal123"
        onDownload={mockOnDownload}
      />
    );
    
    // Submit the form
    const downloadButton = screen.getByRole('button', { name: /download pdf/i });
    await userEvent.click(downloadButton);
    
    // Check that the callback was called correctly
    expect(mockOnDownload).toHaveBeenCalledWith(expect.objectContaining({
      format: 'pdf',
      includeCompanyLogo: true,
      includeSignaturePage: true,
      includeAttachments: true
    }));
  });

  it('handles API errors gracefully', async () => {
    // Mock API error
    (generateProposalWithAI as jest.Mock).mockRejectedValue(new Error('API Error'));
    
    // Create a component that uses the API directly
    const TestComponent = () => {
      const [error, setError] = React.useState<string | null>(null);
      const [isLoading, setIsLoading] = React.useState(false);
      
      const handleGenerate = async () => {
        setIsLoading(true);
        setError(null);
        
        try {
          await generateProposalWithAI({
            prompt: 'Create a proposal',
            model: 'claude-3-opus-20240229'
          });
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Unknown error');
        } finally {
          setIsLoading(false);
        }
      };
      
      return (
        <div>
          <button onClick={handleGenerate}>Generate</button>
          {isLoading && <div>Loading...</div>}
          {error && <div data-testid="error-message">{error}</div>}
        </div>
      );
    };
    
    // Render the test component
    renderWithQueryClient(<TestComponent />);
    
    // Click the generate button
    const generateButton = screen.getByRole('button', { name: /generate/i });
    await userEvent.click(generateButton);
    
    // Check that loading state is shown
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    // Wait for the error to be displayed
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
    });
    
    // Check that the error message is correct
    expect(screen.getByTestId('error-message')).toHaveTextContent('API Error');
  });
});
