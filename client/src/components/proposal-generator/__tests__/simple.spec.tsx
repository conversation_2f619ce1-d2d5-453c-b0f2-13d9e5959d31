import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import React from 'react';

// Simple test to verify Vitest is working
describe('Simple test', () => {
  it('should pass', () => {
    expect(true).toBe(true);
  });

  it('should render a simple component', () => {
    render(React.createElement('div', { 'data-testid': 'test-div' }, 'Test'));
    expect(screen.getByTestId('test-div')).toBeInTheDocument();
  });
});
