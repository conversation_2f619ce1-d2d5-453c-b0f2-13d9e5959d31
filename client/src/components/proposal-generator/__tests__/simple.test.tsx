/**
 * Simple test for ProposalAIGenerator component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';

// Simple test to verify Jest is working
describe('Simple test', () => {
  it('should pass', () => {
    expect(true).toBe(true);
  });

  it('should render a simple component', () => {
    render(<div data-testid="test-div">Test</div>);
    expect(screen.getByTestId('test-div')).toBeInTheDocument();
  });
});
