import React from 'react';
import { useSubscription } from '@/hooks/use-subscription';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface UsageDisplayProps {
  /**
   * The resource type to display usage for
   */
  resourceType: string;
  
  /**
   * The display name for the resource
   */
  displayName: string;
  
  /**
   * Optional description for the resource
   */
  description?: string;
  
  /**
   * Optional unit for the resource (e.g., "MB", "users")
   */
  unit?: string;
  
  /**
   * Optional class name for the container
   */
  className?: string;
}

/**
 * A component that displays usage information for a specific resource
 */
export function UsageDisplay({
  resourceType,
  displayName,
  description,
  unit = '',
  className
}: UsageDisplayProps) {
  const { usage, loading, getUsagePercentage, plan } = useSubscription();
  
  // If still loading, show a loading indicator
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">{displayName}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // If no usage data is available, show a message
  if (!usage) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">{displayName}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No usage data available</p>
        </CardContent>
      </Card>
    );
  }
  
  // Get the current usage and limit
  const currentUsage = usage.usage[resourceType] || 0;
  const limit = usage.limits[resourceType] || 0;
  const percentage = getUsagePercentage(resourceType);
  
  // Determine the color based on the percentage
  let progressColor = 'bg-green-500';
  if (percentage > 90) {
    progressColor = 'bg-red-500';
  } else if (percentage > 75) {
    progressColor = 'bg-amber-500';
  } else if (percentage > 50) {
    progressColor = 'bg-blue-500';
  }
  
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">{displayName}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>
              {currentUsage.toLocaleString()} / {limit.toLocaleString()} {unit}
            </span>
            <span>{percentage}%</span>
          </div>
          <Progress value={percentage} className={progressColor} />
        </div>
      </CardContent>
    </Card>
  );
}
