import React, { useState } from 'react';
import { useSubscription, SubscriptionPlan } from '@/hooks/use-subscription';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Check, Loader2 } from 'lucide-react';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface PlanSelectorProps {
  /**
   * Optional callback when a plan is selected
   */
  onPlanSelected?: (plan: SubscriptionPlan) => void;

  /**
   * Optional class name for the container
   */
  className?: string;
}

/**
 * A component that displays available subscription plans and allows selection
 */
export function PlanSelector({ onPlanSelected, className }: PlanSelectorProps) {
  const { plan: currentPlan, loading, refreshSubscription } = useSubscription();
  const { toast } = useToast();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loadingPlans, setLoadingPlans] = useState(true);
  const [selectedPlanId, setSelectedPlanId] = useState<number | null>(null);
  const [changingPlan, setChangingPlan] = useState(false);

  // Fetch available plans
  React.useEffect(() => {
    async function fetchPlans() {
      try {
        const response = await apiRequest({
          url: '/api/subscription/plans',
          method: 'GET'
        });
        setPlans(response.filter((p: SubscriptionPlan) => p.status === 'active'));
        setLoadingPlans(false);

        // Set the selected plan to the current plan
        if (currentPlan) {
          setSelectedPlanId(currentPlan.id);
        }
      } catch (error) {
        console.error('Error fetching plans:', error);
        toast({
          title: 'Error',
          description: 'Failed to load subscription plans',
          variant: 'destructive'
        });
        setLoadingPlans(false);
      }
    }

    fetchPlans();
  }, [currentPlan]);

  // Handle plan change
  const handlePlanChange = async () => {
    if (!selectedPlanId || selectedPlanId === currentPlan?.id) return;

    setChangingPlan(true);

    try {
      await apiRequest({
        url: '/api/subscription/change-plan',
        method: 'POST',
        data: {
          planId: selectedPlanId
        }
      });

      toast({
        title: 'Success',
        description: 'Subscription plan updated successfully'
      });

      // Refresh subscription data
      await refreshSubscription();

      // Call the callback if provided
      if (onPlanSelected) {
        const selectedPlan = plans.find(p => p.id === selectedPlanId);
        if (selectedPlan) {
          onPlanSelected(selectedPlan);
        }
      }
    } catch (error) {
      console.error('Error changing plan:', error);
      toast({
        title: 'Error',
        description: 'Failed to update subscription plan',
        variant: 'destructive'
      });
    } finally {
      setChangingPlan(false);
    }
  };

  // If still loading, show a loading indicator
  if (loading || loadingPlans) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className={className}>
      <RadioGroup
        value={selectedPlanId?.toString()}
        onValueChange={(value) => setSelectedPlanId(parseInt(value))}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        {plans.map((plan) => (
          <div key={plan.id} className="relative">
            <RadioGroupItem
              value={plan.id.toString()}
              id={`plan-${plan.id}`}
              className="sr-only"
            />
            <Label
              htmlFor={`plan-${plan.id}`}
              className="cursor-pointer"
            >
              <Card className={`h-full transition-all ${
                selectedPlanId === plan.id
                  ? 'border-primary ring-2 ring-primary ring-opacity-50'
                  : 'border-border hover:border-primary/50'
              }`}>
                {currentPlan?.id === plan.id && (
                  <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                    Current
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-3xl font-bold">
                    {plan.price === 0 ? 'Free' : `$${plan.price}`}
                    {plan.price > 0 && (
                      <span className="text-sm font-normal text-muted-foreground">
                        /{plan.billingPeriod === 'monthly' ? 'mo' : 'yr'}
                      </span>
                    )}
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Includes:</h4>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-primary mt-0.5" />
                        <span>Up to {plan.limits.users} users</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-primary mt-0.5" />
                        <span>{plan.limits.contacts.toLocaleString()} contacts</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-primary mt-0.5" />
                        <span>{plan.limits.storage} MB storage</span>
                      </li>
                      {plan.features['ai.assistant.basic'] && (
                        <li className="flex items-start gap-2">
                          <Check className="h-4 w-4 text-primary mt-0.5" />
                          <span>Basic AI Assistant</span>
                        </li>
                      )}
                      {plan.features['ai.assistant.advanced'] && (
                        <li className="flex items-start gap-2">
                          <Check className="h-4 w-4 text-primary mt-0.5" />
                          <span>Advanced AI Assistant</span>
                        </li>
                      )}
                      {plan.features['ai.insights'] && (
                        <li className="flex items-start gap-2">
                          <Check className="h-4 w-4 text-primary mt-0.5" />
                          <span>AI Insights</span>
                        </li>
                      )}
                      {plan.features['ai.document'] && (
                        <li className="flex items-start gap-2">
                          <Check className="h-4 w-4 text-primary mt-0.5" />
                          <span>Document Intelligence</span>
                        </li>
                      )}
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  {currentPlan?.id === plan.id ? (
                    <Button variant="outline" disabled className="w-full">
                      Current Plan
                    </Button>
                  ) : (
                    <Button variant="default" className="w-full">
                      Select Plan
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </Label>
          </div>
        ))}
      </RadioGroup>

      {selectedPlanId !== currentPlan?.id && (
        <div className="mt-6 flex justify-end">
          <Button
            onClick={handlePlanChange}
            disabled={changingPlan || selectedPlanId === currentPlan?.id}
          >
            {changingPlan && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {changingPlan ? 'Updating...' : 'Update Subscription'}
          </Button>
        </div>
      )}
    </div>
  );
}
