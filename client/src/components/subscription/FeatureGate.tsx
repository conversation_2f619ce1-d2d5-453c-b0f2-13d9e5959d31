import React from 'react';
import { useSubscription } from '@/hooks/use-subscription';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Lock } from 'lucide-react';
import Link from 'next/link';

interface FeatureGateProps {
  /**
   * The feature key to check
   */
  featureKey: string;

  /**
   * Content to render when the feature is enabled
   */
  children: React.ReactNode;

  /**
   * Optional custom fallback content to render when the feature is disabled
   */
  fallback?: React.ReactNode;

  /**
   * Whether to render nothing when the feature is disabled (instead of the default fallback)
   */
  renderNothing?: boolean;

  /**
   * Optional title for the upgrade prompt
   */
  upgradeTitle?: string;

  /**
   * Optional description for the upgrade prompt
   */
  upgradeDescription?: string;

  /**
   * Optional URL for the upgrade button
   */
  upgradeUrl?: string;

  /**
   * Optional class name for the container
   */
  className?: string;
}

/**
 * A component that conditionally renders content based on subscription feature entitlement
 */
export function FeatureGate({
  featureKey,
  children,
  fallback,
  renderNothing = false,
  upgradeTitle = 'Feature not available',
  upgradeDescription = 'This feature requires a higher subscription plan.',
  upgradeUrl = '/settings/subscription',
  className
}: FeatureGateProps) {
  const { hasFeature, loading, plan } = useSubscription();

  // If still loading, show a loading indicator
  if (loading) {
    return (
      <div className="flex justify-center items-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    );
  }

  // If the feature is enabled, render the children
  if (hasFeature(featureKey)) {
    return <>{children}</>;
  }

  // If custom fallback is provided, render it
  if (fallback) {
    return <>{fallback}</>;
  }

  // If renderNothing is true, render nothing
  if (renderNothing) {
    return null;
  }

  // Otherwise, render the default upgrade prompt
  return (
    <Card className={`border-dashed border-muted-foreground/20 ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="h-4 w-4 text-muted-foreground" />
          {upgradeTitle}
        </CardTitle>
        <CardDescription>{upgradeDescription}</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          You are currently on the <span className="font-medium">{plan?.name || 'Free'}</span> plan.
          Upgrade to access this feature and more.
        </p>
      </CardContent>
      <CardFooter>
        <Button size="sm" onClick={() => window.location.href = upgradeUrl}>
          Upgrade Plan
        </Button>
      </CardFooter>
    </Card>
  );
}
