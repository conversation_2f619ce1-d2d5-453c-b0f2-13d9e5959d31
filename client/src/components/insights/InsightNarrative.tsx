interface InsightNarrativeProps {
  narrative: string;
}

export default function InsightNarrative({ narrative }: InsightNarrativeProps) {
  // Split narrative into paragraphs
  const paragraphs = narrative.split('\n\n').filter(p => p.trim());

  return (
    <div className="prose prose-sm max-w-none">
      {paragraphs.map((paragraph, index) => (
        <p key={index}>{paragraph}</p>
      ))}
    </div>
  );
}
