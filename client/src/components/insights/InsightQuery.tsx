import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Search } from 'lucide-react';

interface InsightQueryProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  isLoading: boolean;
}

export default function InsightQuery({
  value,
  onChange,
  onSubmit,
  isLoading,
}: InsightQueryProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Submit on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      onSubmit();
    }
  };

  return (
    <div className="space-y-4">
      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Ask a question about your business data..."
        rows={4}
        disabled={isLoading}
      />
      <div className="flex justify-end">
        <Button onClick={onSubmit} disabled={isLoading || !value.trim()}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating Insight...
            </>
          ) : (
            <>
              <Search className="mr-2 h-4 w-4" />
              Generate Insight
            </>
          )}
        </Button>
      </div>
      <div className="text-sm text-muted-foreground">
        <p className="font-medium mb-2">Example questions:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Why is our Q2 pipeline soft compared to last quarter?</li>
          <li>Which deals are most likely to close this month?</li>
          <li>What's the trend in our average deal size over the past 6 months?</li>
          <li>Which sales reps have the highest conversion rate?</li>
        </ul>
      </div>
    </div>
  );
}
