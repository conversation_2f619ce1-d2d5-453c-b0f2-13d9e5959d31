import { useEffect, useRef } from 'react';
import { ChartSpec } from '@/api';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

// Import Chart.js
import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  PolarAreaController,
  RadarController,
  BubbleController,
  Tooltip,
  Legend,
  Title,
  SubTitle,
  Filler,
  ChartType,
} from 'chart.js';

// Register Chart.js components
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  PolarAreaController,
  RadarController,
  BubbleController,
  Tooltip,
  Legend,
  Title,
  SubTitle,
  Filler
);

interface InsightChartProps {
  chartSpec: ChartSpec;
}

export default function InsightChart({ chartSpec }: InsightChartProps) {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // Destroy previous chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // Create new chart
    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // Create chart based on chart spec
    chartInstance.current = new Chart(ctx, {
      type: chartSpec.type as ChartType,
      data: chartSpec.data,
      options: {
        ...chartSpec.options,
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!chartSpec.title,
            text: chartSpec.title || '',
            font: {
              size: 16,
              weight: 'bold',
            },
          },
          subtitle: {
            display: !!chartSpec.subtitle,
            text: chartSpec.subtitle || '',
            font: {
              size: 14,
            },
            padding: {
              bottom: 10,
            },
          },
          legend: {
            position: 'bottom',
          },
          tooltip: {
            enabled: true,
          },
        },
      },
    });

    // Cleanup on unmount
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [chartSpec]);

  // Fallback for unsupported chart types
  const supportedChartTypes = [
    // Basic charts
    'bar', 'line', 'pie', 'doughnut', 'scatter', 'radar', 'polarArea', 'bubble',

    // Advanced charts - these are supported by Chart.js or extensions
    'heatmap', 'treemap', 'sankey',

    // Business-specific charts - these may require custom rendering
    'pipeline_waterfall', 'cohort_analysis', 'win_loss_analysis', 'sales_velocity', 'forecast_comparison',

    // Predictive charts - these may require custom rendering
    'forecast', 'anomaly_detection', 'what_if_analysis', 'trend_prediction'
  ];

  if (!supportedChartTypes.includes(chartSpec.type)) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center h-[300px]">
            <p className="text-muted-foreground">
              Chart type "{chartSpec.type}" is not supported in this view.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // For advanced chart types that require custom rendering
  const advancedChartTypes = [
    'pipeline_waterfall', 'cohort_analysis', 'win_loss_analysis', 'sales_velocity',
    'forecast_comparison', 'forecast', 'anomaly_detection', 'what_if_analysis',
    'trend_prediction', 'sankey'
  ];

  if (advancedChartTypes.includes(chartSpec.type)) {
    // For now, we'll render a placeholder for advanced chart types
    // In a real implementation, we would use specialized chart libraries or custom rendering
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center h-[300px]">
            <p className="font-medium mb-2">Advanced Chart: {chartSpec.title || chartSpec.type}</p>
            <p className="text-muted-foreground text-center">
              This advanced chart type requires specialized rendering.
              <br />
              In a production environment, this would be rendered using a specialized chart library.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="h-[300px] w-full">
      <canvas ref={chartRef} />
    </div>
  );
}
