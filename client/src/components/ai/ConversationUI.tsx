import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { AIChatResponse, sendChatMessage } from "@/lib/openai";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Brain, Send, User } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";

const ConversationUI = () => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<{
    role: "user" | "assistant";
    content: string;
    timestamp?: string;
  }[]>([]);

  const bottomRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch chat history
  const { data: chatHistory, isLoading: isLoadingHistory } = useQuery({
    queryKey: ['/api/ai/history'],
    queryFn: async () => {
      const response = await fetch('/api/ai/history');
      if (!response.ok) {
        throw new Error('Failed to fetch chat history');
      }
      return response.json() as Promise<AIChatResponse[]>;
    }
  });

  // Initialize messages from chat history
  useEffect(() => {
    if (chatHistory && chatHistory.length > 0) {
      // Convert chat history to messages format
      // Note: Reversing to get oldest messages first
      const historyMessages = chatHistory
        .slice()
        .reverse()
        .flatMap(chat => [
          {
            role: "user" as const,
            content: chat.message,
            timestamp: chat.timestamp
          },
          {
            role: "assistant" as const,
            content: chat.response,
            timestamp: chat.timestamp
          }
        ]);
      
      setMessages(historyMessages);
    }
  }, [chatHistory]);

  // Scroll to bottom when messages change
  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: sendChatMessage,
    onSuccess: (data) => {
      // Add assistant's response to messages
      setMessages(prev => [...prev, {
        role: "assistant",
        content: data.response,
        timestamp: data.timestamp
      }]);
      
      // Invalidate chat history query to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/ai/history'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send message",
        variant: "destructive",
      });
      
      // Add error message
      setMessages(prev => [...prev, {
        role: "assistant",
        content: "Sorry, I encountered an error processing your request. Please try again later."
      }]);
    }
  });

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) return;
    
    // Add user message to the conversation
    setMessages(prev => [...prev, { role: "user", content: message }]);
    
    // Send message to API
    sendMessageMutation.mutate(message);
    
    // Clear input
    setMessage("");
  };

  // Format the timestamp
  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {isLoadingHistory ? (
          <div className="flex flex-col space-y-4">
            <Skeleton className="h-16 w-3/4 ml-auto" />
            <Skeleton className="h-20 w-3/4" />
            <Skeleton className="h-16 w-3/4 ml-auto" />
            <Skeleton className="h-24 w-3/4" />
          </div>
        ) : messages.length === 0 ? (
          <div className="h-full flex flex-col items-center justify-center text-gray-500">
            <Brain className="h-16 w-16 mb-4 text-gray-300" />
            <h3 className="text-xl font-medium mb-2">Welcome to the AI Assistant</h3>
            <p className="text-center max-w-md mb-6">
              Ask me anything about your contacts, companies, or opportunities. I can help you analyze data, provide insights, and suggest next steps.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 w-full max-w-lg">
              <Button variant="outline" onClick={() => setMessage("Show me a summary of my pipeline")}>
                Show me my pipeline summary
              </Button>
              <Button variant="outline" onClick={() => setMessage("Which contacts should I follow up with?")}>
                Who needs follow-up?
              </Button>
              <Button variant="outline" onClick={() => setMessage("What opportunities are at risk?")}>
                Identify at-risk deals
              </Button>
              <Button variant="outline" onClick={() => setMessage("Suggest new connections between my contacts")}>
                Find new connections
              </Button>
            </div>
          </div>
        ) : (
          messages.map((msg, index) => (
            <div
              key={index}
              className={`flex ${
                msg.role === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`flex gap-3 max-w-[80%] ${
                  msg.role === "user" ? "flex-row-reverse" : ""
                }`}
              >
                <Avatar className={msg.role === "assistant" ? "bg-primary" : "bg-secondary"}>
                  {msg.role === "assistant" ? (
                    <Brain className="h-5 w-5 text-white" />
                  ) : (
                    <User className="h-5 w-5 text-white" />
                  )}
                </Avatar>
                <div>
                  <div
                    className={`rounded-lg px-4 py-2 max-w-full ${
                      msg.role === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted"
                    }`}
                  >
                    <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                  </div>
                  {msg.timestamp && (
                    <p className="text-xs text-gray-500 mt-1">
                      {formatTimestamp(msg.timestamp)}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={bottomRef} />
      </div>

      <div className="border-t p-4">
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message..."
            className="resize-none"
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage(e);
              }
            }}
          />
          <Button type="submit" disabled={sendMessageMutation.isPending || !message.trim()}>
            {sendMessageMutation.isPending ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default ConversationUI;
