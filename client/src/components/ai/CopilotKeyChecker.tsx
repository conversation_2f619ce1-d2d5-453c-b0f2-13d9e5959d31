import { useEffect, useState } from 'react';

export function <PERSON><PERSON>lot<PERSON>ey<PERSON>he<PERSON>() {
  const [keyStatus, setKeyStatus] = useState({
    exists: false,
    firstFiveChars: '',
    keyInfo: ''
  });

  useEffect(() => {
    // Access the key from the environment
    const key = import.meta.env.NEXT_PUBLIC_COPILOT_KEY;
    
    // For debugging only
    console.log('NEXT_PUBLIC_COPILOT_KEY exists:', !!key);
    if (key) {
      console.log('Key type:', typeof key);
      console.log('Key length:', typeof key === 'string' ? key.length : 'not a string');
    }
    
    setKeyStatus({
      exists: !!key,
      firstFiveChars: key && typeof key === 'string' ? key.substring(0, 5) : '',
      keyInfo: `${typeof key} / ${key ? 'present' : 'missing'}`
    });
  }, []);

  return (
    <div className="p-2 border rounded-md my-2">
      <h3 className="font-medium">CopilotKit Key Status</h3>
      <p>Key exists: {keyStatus.exists ? 'Yes' : 'No'}</p>
      <p>Key info: {keyStatus.keyInfo}</p>
      {keyStatus.exists && keyStatus.firstFiveChars && (
        <p>First 5 characters: {keyStatus.firstFiveChars}...</p>
      )}
    </div>
  );
}