import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>alogContent, 
  DialogDescription, 
  Dialog<PERSON>ooter, 
  DialogHeader, 
  Dialog<PERSON><PERSON>le, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from '@/lib/queryClient';

export function AddAPIKeyDialog() {
  const [open, setOpen] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      toast({
        title: "API key required",
        description: "Please enter your OpenAI API key",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Call API to set the key
      const response = await apiRequest(
        'POST', 
        '/api/settings/openai-key', 
        { apiKey }
      );
      
      // Parse the JSON response
      const responseData = await response.json();
      toast({
        title: "API key saved",
        description: "Your OpenAI API key has been saved. AI features are now available.",
      });
      setOpen(false);
      // Reload the page to apply changes
      window.location.reload();
    } catch (error) {
      toast({
        title: "Failed to save API key",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" className="w-full">Add OpenAI API Key</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add OpenAI API Key</DialogTitle>
          <DialogDescription>
            Enter your OpenAI API key to enable AI features like chat assistance,
            insights, and contact enrichment.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="apiKey" className="text-right">
                API Key
              </Label>
              <Input
                id="apiKey"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-..."
                className="col-span-3"
                autoComplete="off"
              />
            </div>
            <div className="col-span-4 text-xs text-muted-foreground">
              You can get your API key from your OpenAI account dashboard. 
              Your key will be stored securely and is only used for this application.
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="submit" 
              disabled={isSubmitting || !apiKey.trim()}
            >
              {isSubmitting ? "Saving..." : "Save Key"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}