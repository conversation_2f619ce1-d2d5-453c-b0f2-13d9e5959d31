import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ArrowUpCircle, Server, AlertTriangle } from "lucide-react";

interface AIBridgeStatus {
  available: boolean;
  region: string;
  compression: boolean;
  python_service?: {
    available: boolean;
    openai_available: boolean;
    version: string | null;
  };
}

const AIBridgeTest: React.FC = () => {
  const [status, setStatus] = useState<AIBridgeStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(true);
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('general');
  const { toast } = useToast();

  // Check AI Bridge status on component mount
  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    try {
      setStatusLoading(true);
      const res = await fetch('/api/ai-bridge/status');
      const data = await res.json();
      setStatus(data);
      setError(null);
    } catch (err) {
      setStatus(null);
      setError('Failed to connect to AI Bridge service');
      console.error('Error checking AI Bridge status:', err);
    } finally {
      setStatusLoading(false);
    }
  };

  const handleSubmit = async (type: 'general' | 'crew') => {
    if (!query.trim()) {
      toast({
        title: "Input Required",
        description: "Please enter a question or prompt",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      const endpoint = type === 'general' 
        ? '/api/ai-bridge/process' 
        : '/api/ai-bridge/crew';

      const payload = type === 'general' 
        ? { query, context: {} } 
        : { task: query, context: {} };

      const res = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-User-Region': 'ZA' // For testing regional routing
        },
        body: JSON.stringify(payload)
      });

      const data = await res.json();
      
      if (res.ok) {
        setResponse(data);
      } else {
        setError(data.message || 'Error processing AI request');
      }
    } catch (err) {
      setError('Failed to connect to AI service');
      console.error('Error sending query:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          AI Bridge Testing Interface
        </CardTitle>
        <CardDescription>
          Test the hybrid Node.js/Python AI architecture with different types of queries
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {statusLoading ? (
          <div className="flex justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : status?.available ? (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <ArrowUpCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">AI Bridge Connected</AlertTitle>
            <AlertDescription className="text-green-700">
              Region: {status.region} | Compression: {status.compression ? 'Enabled' : 'Disabled'}
              {status.python_service && (
                <><br />Python Service: {status.python_service.available ? 'Online' : 'Offline'} | 
                OpenAI Available: {status.python_service.openai_available ? 'Yes' : 'No'}</>
              )}
            </AlertDescription>
          </Alert>
        ) : (
          <Alert className="mb-4 bg-amber-50 border-amber-200">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertTitle className="text-amber-800">AI Bridge Unavailable</AlertTitle>
            <AlertDescription className="text-amber-700">
              Using local OpenAI fallback for processing
            </AlertDescription>
          </Alert>
        )}
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="general">General Query</TabsTrigger>
            <TabsTrigger value="crew">CrewAI Query</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="pt-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Enter your query:</label>
                <Input 
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="E.g., How can I improve my sales pipeline?"
                  className="mt-1"
                />
              </div>
              
              <Button 
                onClick={() => handleSubmit('general')}
                disabled={loading || !query.trim()}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Submit Query'
                )}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="crew" className="pt-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Enter task for AI crew:</label>
                <Input 
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="E.g., Analyze our current opportunities and suggest next steps"
                  className="mt-1"
                />
              </div>
              
              <Button 
                onClick={() => handleSubmit('crew')}
                disabled={loading || !query.trim()}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Agent team is working...
                  </>
                ) : (
                  'Run CrewAI Task'
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
        
        {error && (
          <Alert className="mt-4 bg-red-50 border-red-200">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertTitle className="text-red-800">Error</AlertTitle>
            <AlertDescription className="text-red-700">{error}</AlertDescription>
          </Alert>
        )}
        
        {response && (
          <div className="mt-4">
            <label className="text-sm font-medium">Response:</label>
            <Textarea
              readOnly
              value={typeof response === 'string' ? response : JSON.stringify(response, null, 2)}
              className="mt-1 min-h-[200px] font-mono text-sm"
            />
            <div className="mt-2 text-xs text-gray-500">
              Source: {response.source || 'unknown'}
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline" onClick={checkStatus} disabled={statusLoading}>
          {statusLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Check Status'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default AIBridgeTest;