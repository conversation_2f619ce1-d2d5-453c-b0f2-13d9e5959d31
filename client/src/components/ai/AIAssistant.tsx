import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { CopilotSidebar, CopilotChat } from '@copilotkit/react-ui';
import type { CopilotChatProps, CopilotSidebarProps } from '@/@types/components';
import { useCopilotAction, useCopilotChat } from '@copilotkit/react-core';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/lib/auth';
import { AlertCircle, Loader2, Send, Sparkles, RefreshCw, Database } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { AddAPIKeyDialog } from './AddAPIKeyDialog';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './CopilotKeyChecker';
import { env } from '@/lib/env';

interface Insight {
  title: string;
  description: string;
  recommendation?: string;
}

export function AIAssistant() {
  const [activeTab, setActiveTab] = useState('chat');
  const [apiAvailable, setApiAvailable] = useState(true);
  const [copilotKeyAvailable, setCopilotKeyAvailable] = useState(!!env.NEXT_PUBLIC_COPILOT_KEY);
  const [insights, setInsights] = useState<Insight[]>([]);
  const [isLoadingInsights, setIsLoadingInsights] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  // Register CopilotKit actions for retrieving CRM data
  useCopilotAction({
    name: "get_contacts",
    description: "Get a list of contacts from the CRM",
    parameters: [],
    handler: async () => {
      try {
        const response = await fetch('/api/contacts', {
          method: 'GET',
          credentials: 'include',
        });
        const data = await response.json();
        return { contacts: data };
      } catch (error) {
        console.error('Error fetching contacts:', error);
        return { error: 'Failed to fetch contacts' };
      }
    },
  });

  useCopilotAction({
    name: "get_companies",
    description: "Get a list of companies from the CRM",
    parameters: [],
    handler: async () => {
      try {
        const response = await fetch('/api/companies', {
          method: 'GET',
          credentials: 'include',
        });
        const data = await response.json();
        return { companies: data };
      } catch (error) {
        console.error('Error fetching companies:', error);
        return { error: 'Failed to fetch companies' };
      }
    },
  });

  useCopilotAction({
    name: "get_opportunities",
    description: "Get a list of opportunities from the CRM",
    parameters: [],
    handler: async () => {
      try {
        const response = await fetch('/api/opportunities', {
          method: 'GET',
          credentials: 'include',
        });
        const data = await response.json();
        return { opportunities: data };
      } catch (error) {
        console.error('Error fetching opportunities:', error);
        return { error: 'Failed to fetch opportunities' };
      }
    },
  });

  useCopilotAction({
    name: "generate_insights",
    description: "Generate AI insights based on CRM data",
    parameters: [],
    handler: async () => {
      try {
        setIsLoadingInsights(true);
        const response = await fetch('/api/copilot/insights', {
          method: 'GET',
          credentials: 'include',
        });
        const data = await response.json();
        setInsights(data.insights || []);
        setIsLoadingInsights(false);
        return { insights: data.insights || [] };
      } catch (error) {
        console.error('Error generating insights:', error);
        setIsLoadingInsights(false);
        return { error: 'Failed to generate insights' };
      }
    },
  });

  // Check if local OpenAI API is available
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const response = await fetch('/api/copilot/status', {
          method: 'GET',
          credentials: 'include',
        });

        const data = await response.json();
        setApiAvailable(data.available);

        if (!data.available) {
          toast({
            title: 'Local AI Service Limited',
            description: data.message || "OpenAI API key is not configured locally. Using CopilotKit Cloud instead.",
            variant: 'default',
            duration: 5000,
          });
        }
      } catch (error) {
        console.warn('Failed to check AI API status:', error);
      }
    };

    checkApiStatus();
  }, [toast]);

  // Load initial insights
  useEffect(() => {
    const loadInsights = async () => {
      try {
        setIsLoadingInsights(true);
        const response = await fetch('/api/copilot/insights', {
          method: 'GET',
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          setInsights(data.insights || []);
        }
      } catch (error) {
        console.error('Error loading insights:', error);
      } finally {
        setIsLoadingInsights(false);
      }
    };

    if (activeTab === 'insights') {
      loadInsights();
    }
  }, [activeTab]);

  // Function to generate new insights
  const generateNewInsights = async () => {
    try {
      setIsLoadingInsights(true);
      toast({
        title: "Generating insights...",
        description: "This may take a moment",
      });

      const response = await fetch('/api/copilot/insights', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'x-copilotkit-api-key': env.NEXT_PUBLIC_COPILOT_KEY || '',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to generate insights');
      }

      const data = await response.json();
      setInsights(data.insights || []);

      toast({
        title: "Insights generated",
        description: "New insights are now available",
        variant: 'default',
      });
    } catch (error) {
      console.error('Error generating insights:', error);
      toast({
        title: "Error",
        description: "Failed to generate insights. Please try again.",
        variant: 'destructive',
      });
    } finally {
      setIsLoadingInsights(false);
    }
  };

  return (
    <div className="relative h-full">
      {/* Main content */}
      <Card className="h-full flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            Aizako AI Assistant
          </CardTitle>
          <CardDescription>
            Your AI-powered CRM assistant. Ask questions about your contacts, companies, or get advice.
          </CardDescription>
        </CardHeader>

        <CardContent className="flex-grow overflow-auto">
          {copilotKeyAvailable ? (
            <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-3 text-blue-800">
              <div className="flex items-start">
                <div className="mr-2 pt-0.5">
                  <Sparkles className="h-4 w-4 text-blue-500" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm">Using CopilotKit Cloud</p>
                  <p className="text-xs mt-1">
                    Enhanced AI features powered by CopilotKit Cloud are active.
                  </p>
                  <div className="mt-2 flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs"
                      onClick={() => {
                        toast({
                          title: 'CopilotKit Cloud',
                          description: 'The application is using CopilotKit Cloud for enhanced AI features.',
                          duration: 5000,
                        });
                      }}
                    >
                      Learn More
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="mb-4 bg-amber-50 border border-amber-200 rounded-md p-3 text-amber-800">
              <div className="flex items-start">
                <div className="mr-2 pt-0.5">
                  <AlertCircle className="h-4 w-4 text-amber-500" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm">CopilotKit Cloud Not Configured</p>
                  <p className="text-xs mt-1">
                    Set the NEXT_PUBLIC_COPILOT_KEY environment variable to enable enhanced AI features.
                  </p>
                  <div className="mt-2">
                    <AddAPIKeyDialog />
                  </div>
                </div>
              </div>
            </div>
          )}

          <Tabs defaultValue="chat" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="chat">Chat</TabsTrigger>
              <TabsTrigger value="insights">AI Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="chat" className="space-y-4 mt-4 h-[calc(100%-80px)]">
              <div className="bg-muted/50 rounded-lg p-4 mb-4">
                <h3 className="font-medium mb-2">Examples you can ask:</h3>
                <ul className="space-y-2">
                  <li className="text-sm">• Show me a summary of my contacts</li>
                  <li className="text-sm">• Which opportunities are closing this month?</li>
                  <li className="text-sm">• Identify relationship gaps in my network</li>
                  <li className="text-sm">• Generate talking points for my next meeting with [contact]</li>
                  <li className="text-sm">• What are the top 3 companies I should focus on?</li>
                </ul>
              </div>

              {/* Enhanced CopilotKit Chat component */}
              <div className="h-[calc(100%-120px)]">
                <CopilotChat
                  className="border rounded-md h-full"
                />
              </div>
            </TabsContent>

            <TabsContent value="insights" className="space-y-4 mt-4">
              <Button
                variant="outline"
                onClick={generateNewInsights}
                disabled={isLoadingInsights}
                className="w-full"
              >
                {isLoadingInsights ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Insights...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Generate New Insights
                  </>
                )}
              </Button>

              {/* Display AI-generated insights */}
              <div className="space-y-4">
                {insights.length > 0 ? (
                  insights.map((insight, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">{insight.title}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm mb-2">{insight.description}</p>
                        {insight.recommendation && (
                          <p className="text-sm font-medium text-blue-600">
                            Recommendation: {insight.recommendation}
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  ))
                ) : isLoadingInsights ? (
                  <div className="flex justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">No Insights Available</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">
                        Generate insights to get AI-powered recommendations based on your CRM data.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}