import React, { ReactNode, useState, useEffect } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import type { CopilotKitProps } from '@/@types/components';
import { CopilotSidebar } from '@copilotkit/react-ui';
import { useMemo } from 'react';
import { useAuth } from '@/lib/auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { env } from '@/lib/env';

export function CopilotKitProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [apiStatus, setApiStatus] = useState<{ available: boolean; message?: string }>({ available: true });
  const [copilotKeyStatus, setCopilotKeyStatus] = useState<boolean>(!!env.NEXT_PUBLIC_COPILOT_KEY);

  // Check if OpenAI API is available on mount for local AI features
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const response = await fetch('/api/copilot/status', {
          method: 'GET',
          credentials: 'include',
        });

        const data = await response.json();
        setApiStatus({
          available: data.available,
          message: !data.available ? (data.message || "OpenAI API key is not configured. Some AI features will be limited.") : undefined
        });
      } catch (error) {
        console.warn('Failed to check AI API status:', error);
        // Don't show warning if it's just a network error
      }
    };

    checkApiStatus();
  }, []);

  // Set up system prompt for CopilotKit Cloud
  const chatSystemPrompt = useMemo(() => {
    return `You are an AI assistant for Aizako CRM, an AI-first customer relationship management system designed specifically for African markets.

      You have access to the following CRM data:
      - Contacts: People the user interacts with professionally
      - Companies: Organizations the user works with
      - Opportunities: Potential business deals in various stages
      - Relationships: Connections between contacts and companies
      - Activities: Interactions and events related to contacts and companies

      Be professional, helpful and concise in your responses. Focus on actionable insights.
      The user's name is ${user?.email ? user.email.split('@')[0] : 'there'}.

      When asked about specific contacts, companies, or opportunities, you can retrieve this information.
      You can also generate insights based on the CRM data and suggest next steps.

      Ensure all advice and responses are ethical and focus on improving business relationships.`;
  }, [user?.email]);

  // Configure CopilotKit options
  const copilotKitOptions = useMemo(() => {
    return {
      publicApiKey: env.NEXT_PUBLIC_COPILOT_KEY,
      runtimeUrl: "/api/copilotkit",
      systemPrompt: chatSystemPrompt,
      // Add error handling for API key issues
      onError: (error: unknown) => {
        console.error("CopilotKit error:", error);
        // We could add more sophisticated error handling here
      }
    };
  }, [chatSystemPrompt]);

  return (
    <>
      {!apiStatus.available && (
        <Alert className="mb-4 mx-auto max-w-3xl mt-2 border-amber-500 bg-amber-50">
          <AlertCircle className="h-4 w-4 text-amber-500" />
          <AlertTitle className="text-amber-700">Local AI Service Limited</AlertTitle>
          <AlertDescription className="text-amber-600">
            {apiStatus.message || "OpenAI API key is not configured locally. CopilotKit Cloud will be used for AI features."}
          </AlertDescription>
        </Alert>
      )}

      {!copilotKeyStatus && (
        <Alert className="mb-4 mx-auto max-w-3xl mt-2 border-blue-500 bg-blue-50">
          <AlertCircle className="h-4 w-4 text-blue-500" />
          <AlertTitle className="text-blue-700">CopilotKit Cloud Configuration</AlertTitle>
          <AlertDescription className="text-blue-600">
            CopilotKit Cloud API key is not configured. Please set the NEXT_PUBLIC_COPILOT_KEY environment variable.
          </AlertDescription>
        </Alert>
      )}

      {/* Use CopilotKit with enhanced options */}
      <CopilotKit {...copilotKitOptions}>
        {children}
      </CopilotKit>
    </>
  );
}

// Re-export components for easy access
export { CopilotSidebar };