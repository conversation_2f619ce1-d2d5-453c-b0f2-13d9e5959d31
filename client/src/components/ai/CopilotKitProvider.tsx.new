import React, { ReactNode, useState, useEffect } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotSidebar } from '@copilotkit/react-ui';
import { useMemo } from 'react';
import { useAuth } from '@/lib/auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { env } from '@/lib/env';

export function CopilotKitProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [apiStatus, setApiStatus] = useState<{ available: boolean; message?: string }>({ available: true });
  
  // Check if OpenAI API is available on mount for local AI features
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const response = await fetch('/api/copilot/status', {
          method: 'GET',
          credentials: 'include',
        });
        
        const data = await response.json();
        
        if (!data.available) {
          setApiStatus({ 
            available: false, 
            message: data.message || "OpenAI API key is not configured. Some AI features will be limited."
          });
        }
      } catch (error) {
        console.warn('Failed to check AI API status:', error);
        // Don't show warning if it's just a network error
      }
    };
    
    checkApiStatus();
  }, []);
  
  // Set up system prompt for CopilotKit Cloud
  const chatSystemPrompt = useMemo(() => {
    return `You are an AI assistant for Kairo CRM. You have access to all the data in the CRM.
      Be professional, helpful and concise in your responses.
      The user's name is ${user?.email ? user.email.split('@')[0] : 'there'}.
      Ensure all advice and responses are ethical and focus on improving business relationships.`;
  }, [user?.email]);

  return (
    <>
      {!apiStatus.available && (
        <Alert className="mb-4 mx-auto max-w-3xl mt-2 border-amber-500 bg-amber-50">
          <AlertCircle className="h-4 w-4 text-amber-500" />
          <AlertTitle className="text-amber-700">Local AI Service Limited</AlertTitle>
          <AlertDescription className="text-amber-600">
            {apiStatus.message || "OpenAI API key is not configured locally. CopilotKit Cloud will be used for AI features."}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Use CopilotKit Cloud if the key is available, otherwise fallback to local processing */}
      <CopilotKit 
        publicApiKey={env.NEXT_PUBLIC_COPILOT_KEY}
        runtimeUrl="/api/copilotkit"
      >
        {children}
      </CopilotKit>
    </>
  );
}

// Re-export components for easy access
export { CopilotSidebar };