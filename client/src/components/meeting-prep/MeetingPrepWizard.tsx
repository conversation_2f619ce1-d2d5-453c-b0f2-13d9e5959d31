import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Loader2, FileText, User, Building, DollarSign } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { generateMeetingPrep } from '@/api/meeting-prep-api';
import { MeetingPrepButton } from './MeetingPrepButton';

interface MeetingPrepWizardProps {
  contactId?: string;
  companyId?: string;
  opportunityId?: string;
  contactName?: string;
  companyName?: string;
  opportunityName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

export function MeetingPrepWizard({
  contactId,
  companyId,
  opportunityId,
  contactName,
  companyName,
  opportunityName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Meeting Prep'
}: MeetingPrepWizardProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>('contact');
  const [selectedId, setSelectedId] = useState<string | undefined>(contactId || companyId || opportunityId);
  
  // Set initial tab based on provided IDs
  useState(() => {
    if (opportunityId) {
      setSelectedTab('opportunity');
      setSelectedId(opportunityId);
    } else if (companyId) {
      setSelectedTab('company');
      setSelectedId(companyId);
    } else if (contactId) {
      setSelectedTab('contact');
      setSelectedId(contactId);
    }
  });
  
  // Meeting prep generation mutation
  const meetingPrepMutation = useMutation({
    mutationFn: generateMeetingPrep,
    onSuccess: (data) => {
      setIsGenerating(false);
      setIsOpen(false);
      toast({
        title: 'Success',
        description: 'Meeting prep document generated successfully',
      });
      
      if (data.documentUrl) {
        // Open the document in a new tab
        window.open(data.documentUrl, '_blank');
      }
    },
    onError: (error) => {
      setIsGenerating(false);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate meeting prep',
        variant: 'destructive',
      });
    },
  });
  
  // Handle generating meeting prep
  const handleGenerateMeetingPrep = () => {
    if (!selectedId) {
      toast({
        title: 'Error',
        description: 'Please select a contact, company, or opportunity',
        variant: 'destructive',
      });
      return;
    }
    
    setIsGenerating(true);
    meetingPrepMutation.mutate({
      contactId: selectedTab === 'contact' ? selectedId : undefined,
      companyId: selectedTab === 'company' ? selectedId : undefined,
      opportunityId: selectedTab === 'opportunity' ? selectedId : undefined
    });
  };
  
  // Handle tab change
  const handleTabChange = (value: string) => {
    setSelectedTab(value);
    
    // Update selected ID based on tab
    if (value === 'contact') {
      setSelectedId(contactId);
    } else if (value === 'company') {
      setSelectedId(companyId);
    } else if (value === 'opportunity') {
      setSelectedId(opportunityId);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
        >
          <FileText className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Meeting Prep Wizard</DialogTitle>
          <DialogDescription>
            Generate a comprehensive meeting preparation document with account history, stakeholders, recent news, and talking points.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={selectedTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="contact" disabled={!contactId}>
              <User className="h-4 w-4 mr-2" />
              Contact
            </TabsTrigger>
            <TabsTrigger value="company" disabled={!companyId}>
              <Building className="h-4 w-4 mr-2" />
              Company
            </TabsTrigger>
            <TabsTrigger value="opportunity" disabled={!opportunityId}>
              <DollarSign className="h-4 w-4 mr-2" />
              Opportunity
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="contact" className="space-y-4">
            {contactId ? (
              <Card>
                <CardHeader>
                  <CardTitle>Contact</CardTitle>
                  <CardDescription>
                    Generate meeting prep for this contact
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={selectedId} onValueChange={setSelectedId}>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={contactId} id={`contact-${contactId}`} />
                      <Label htmlFor={`contact-${contactId}`}>{contactName || 'Selected Contact'}</Label>
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Contact Selected</CardTitle>
                  <CardDescription>
                    Please select a contact to generate meeting prep
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="company" className="space-y-4">
            {companyId ? (
              <Card>
                <CardHeader>
                  <CardTitle>Company</CardTitle>
                  <CardDescription>
                    Generate meeting prep for this company
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={selectedId} onValueChange={setSelectedId}>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={companyId} id={`company-${companyId}`} />
                      <Label htmlFor={`company-${companyId}`}>{companyName || 'Selected Company'}</Label>
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Company Selected</CardTitle>
                  <CardDescription>
                    Please select a company to generate meeting prep
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="opportunity" className="space-y-4">
            {opportunityId ? (
              <Card>
                <CardHeader>
                  <CardTitle>Opportunity</CardTitle>
                  <CardDescription>
                    Generate meeting prep for this opportunity
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={selectedId} onValueChange={setSelectedId}>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={opportunityId} id={`opportunity-${opportunityId}`} />
                      <Label htmlFor={`opportunity-${opportunityId}`}>{opportunityName || 'Selected Opportunity'}</Label>
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Opportunity Selected</CardTitle>
                  <CardDescription>
                    Please select an opportunity to generate meeting prep
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleGenerateMeetingPrep}
            disabled={isGenerating || !selectedId}
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Generate Meeting Prep
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
