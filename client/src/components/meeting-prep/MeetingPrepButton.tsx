import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Loader2, FileText, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { generateMeetingPrep, generateAddToAgendaUrl } from '@/api/meeting-prep-api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

interface MeetingPrepButtonProps {
  contactId?: string;
  companyId?: string;
  opportunityId?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  onSuccess?: (documentUrl: string) => void;
}

export function MeetingPrepButton({
  contactId,
  companyId,
  opportunityId,
  variant = 'outline',
  size = 'sm',
  onSuccess
}: MeetingPrepButtonProps) {
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedMeetingPrep, setGeneratedMeetingPrep] = useState<{ id: string; documentUrl?: string } | null>(null);
  const [isAddingToAgenda, setIsAddingToAgenda] = useState(false);

  // Meeting prep generation mutation
  const meetingPrepMutation = useMutation({
    mutationFn: generateMeetingPrep,
    onSuccess: (data) => {
      setIsGenerating(false);
      setGeneratedMeetingPrep(data);

      toast({
        title: 'Success',
        description: 'Meeting prep document generated successfully',
      });

      if (onSuccess && data.documentUrl) {
        onSuccess(data.documentUrl);
      } else if (data.documentUrl) {
        // Open the document in a new tab
        window.open(data.documentUrl, '_blank');
      }
    },
    onError: (error) => {
      setIsGenerating(false);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate meeting prep',
        variant: 'destructive',
      });
    },
  });

  // Add to agenda mutation
  const addToAgendaMutation = useMutation({
    mutationFn: ({ id, calendarType }: { id: string; calendarType: 'google' | 'outlook' }) =>
      generateAddToAgendaUrl(id, calendarType),
    onSuccess: (data) => {
      setIsAddingToAgenda(false);

      // Open the calendar URL in a new tab
      if (data.url) {
        window.open(data.url, '_blank');
      }
    },
    onError: (error) => {
      setIsAddingToAgenda(false);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to add to agenda',
        variant: 'destructive',
      });
    },
  });

  // Handle generating meeting prep
  const handleGenerateMeetingPrep = () => {
    if (!contactId && !companyId && !opportunityId) {
      toast({
        title: 'Error',
        description: 'At least one of contactId, companyId, or opportunityId must be provided',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);
    meetingPrepMutation.mutate({
      contactId,
      companyId,
      opportunityId
    });
  };

  // Handle adding to agenda
  const handleAddToAgenda = (calendarType: 'google' | 'outlook') => {
    if (!generatedMeetingPrep || !generatedMeetingPrep.id) {
      toast({
        title: 'Error',
        description: 'Please generate a meeting prep document first',
        variant: 'destructive',
      });
      return;
    }

    setIsAddingToAgenda(true);
    addToAgendaMutation.mutate({
      id: generatedMeetingPrep.id,
      calendarType
    });
  };

  // If we have a generated meeting prep, show a dropdown with options
  if (generatedMeetingPrep) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            disabled={isAddingToAgenda}
          >
            {isAddingToAgenda ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Adding to Agenda...
              </>
            ) : (
              <>
                <Calendar className="h-4 w-4 mr-2" />
                Meeting Options
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => window.open(generatedMeetingPrep.documentUrl, '_blank')}>
            <FileText className="h-4 w-4 mr-2" />
            View Meeting Prep
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddToAgenda('google')}>
            <Calendar className="h-4 w-4 mr-2" />
            Add to Google Calendar
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddToAgenda('outlook')}>
            <Calendar className="h-4 w-4 mr-2" />
            Add to Outlook Calendar
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Otherwise, show the generate button
  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleGenerateMeetingPrep}
      disabled={isGenerating || (!contactId && !companyId && !opportunityId)}
    >
      {isGenerating ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Generating...
        </>
      ) : (
        <>
          <FileText className="h-4 w-4 mr-2" />
          Meeting Prep
        </>
      )}
    </Button>
  );
}
