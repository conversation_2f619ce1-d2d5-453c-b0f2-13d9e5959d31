import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Calendar, 
  Clock, 
  Mail, 
  Phone, 
  Video, 
  CheckSquare, 
  Plus, 
  Sparkles, 
  Loader2,
  Tag,
  Filter,
  AlertTriangle,
  Check,
  X
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  getFollowUps,
  getFollowUpById,
  createFollowUp,
  updateFollowUp,
  deleteFollowUp,
  generateFollowUpRecommendations
} from '@/api/follow-up-coach-api';

interface FollowUpCoachDialogProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  activityId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

interface FollowUp {
  _id: string;
  title: string;
  description?: string;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  activityId?: string;
  scheduledDate: string;
  completedDate?: string;
  status: 'pending' | 'completed' | 'cancelled';
  type: 'email' | 'call' | 'meeting' | 'task' | 'other';
  priority: 'low' | 'medium' | 'high';
  template?: string;
  content?: string;
  reminderDate?: string;
  isAIGenerated: boolean;
  tags: string[];
  customFields: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export function FollowUpCoachDialog({
  opportunityId,
  contactId,
  companyId,
  activityId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Follow-up Coach'
}: FollowUpCoachDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>('upcoming');
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedFollowUp, setSelectedFollowUp] = useState<FollowUp | null>(null);
  
  // Fetch follow-ups based on the selected tab
  const {
    data: followUps,
    isLoading: isFollowUpsLoading,
    refetch: refetchFollowUps
  } = useQuery({
    queryKey: ['follow-ups', selectedTab, opportunityId, contactId, companyId],
    queryFn: () => getFollowUps({
      status: selectedTab === 'upcoming' ? 'pending' : selectedTab === 'completed' ? 'completed' : undefined,
      opportunityId,
      contactId,
      companyId
    }),
    enabled: isOpen
  });
  
  // Generate follow-up recommendations mutation
  const generateRecommendationsMutation = useMutation({
    mutationFn: generateFollowUpRecommendations,
    onSuccess: (data) => {
      setIsGenerating(false);
      toast({
        title: 'Success',
        description: `${data.length} follow-up recommendations generated`,
      });
      refetchFollowUps();
    },
    onError: (error) => {
      setIsGenerating(false);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate follow-up recommendations',
        variant: 'destructive',
      });
    }
  });
  
  // Update follow-up status mutation
  const updateFollowUpMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: 'completed' | 'cancelled' }) => 
      updateFollowUp(id, { status }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Follow-up status updated',
      });
      refetchFollowUps();
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update follow-up status',
        variant: 'destructive',
      });
    }
  });
  
  // Handle generating follow-up recommendations
  const handleGenerateRecommendations = () => {
    if (!opportunityId && !contactId && !companyId && !activityId) {
      toast({
        title: 'Error',
        description: 'At least one of opportunityId, contactId, companyId, or activityId must be provided',
        variant: 'destructive',
      });
      return;
    }
    
    setIsGenerating(true);
    generateRecommendationsMutation.mutate({
      opportunityId,
      contactId,
      companyId,
      activityId,
      count: 3
    });
  };
  
  // Handle marking a follow-up as complete
  const handleMarkComplete = (id: string) => {
    updateFollowUpMutation.mutate({ id, status: 'completed' });
  };
  
  // Handle cancelling a follow-up
  const handleCancel = (id: string) => {
    updateFollowUpMutation.mutate({ id, status: 'cancelled' });
  };
  
  // Get the title for the dialog
  const getDialogTitle = () => {
    if (contactName) return `Follow-ups for ${contactName}`;
    if (companyName) return `Follow-ups for ${companyName}`;
    if (opportunityName) return `Follow-ups for ${opportunityName}`;
    return 'Follow-up Coach';
  };
  
  // Get the icon for a follow-up type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'call':
        return <Phone className="h-4 w-4" />;
      case 'meeting':
        return <Video className="h-4 w-4" />;
      case 'task':
        return <CheckSquare className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };
  
  // Get the color for a priority
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size}>
          <Calendar className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          <DialogDescription>
            Plan and manage your follow-ups to keep your sales process moving forward.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="upcoming" value={selectedTab} onValueChange={setSelectedTab}>
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="all">All</TabsTrigger>
            </TabsList>
            
            <Button
              onClick={handleGenerateRecommendations}
              disabled={isGenerating || (!opportunityId && !contactId && !companyId && !activityId)}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate Recommendations
                </>
              )}
            </Button>
          </div>
          
          <TabsContent value="upcoming" className="space-y-4">
            {isFollowUpsLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            ) : followUps && followUps.length > 0 ? (
              <div className="space-y-4">
                {followUps.map((followUp: FollowUp) => (
                  <Card key={followUp._id} className="relative">
                    {followUp.isAIGenerated && (
                      <Badge className="absolute top-2 right-2 bg-purple-100 text-purple-800 border-purple-300">
                        <Sparkles className="h-3 w-3 mr-1" />
                        AI Generated
                      </Badge>
                    )}
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          {getTypeIcon(followUp.type)}
                          <CardTitle className="ml-2 text-lg">{followUp.title}</CardTitle>
                        </div>
                        <Badge className={`${getPriorityColor(followUp.priority)}`}>
                          {followUp.priority.charAt(0).toUpperCase() + followUp.priority.slice(1)} Priority
                        </Badge>
                      </div>
                      <CardDescription>
                        Scheduled for {new Date(followUp.scheduledDate).toLocaleDateString()} at {new Date(followUp.scheduledDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {followUp.description && <p className="text-sm text-gray-700 mb-2">{followUp.description}</p>}
                      {followUp.content && (
                        <div className="bg-gray-50 p-3 rounded-md text-sm">
                          {followUp.content}
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="flex justify-end space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleCancel(followUp._id)}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Cancel
                      </Button>
                      <Button 
                        variant="default" 
                        size="sm"
                        onClick={() => handleMarkComplete(followUp._id)}
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Mark Complete
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium">No upcoming follow-ups</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Generate recommendations or create a new follow-up to get started.
                </p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="completed" className="space-y-4">
            {isFollowUpsLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            ) : followUps && followUps.length > 0 ? (
              <div className="space-y-4">
                {followUps.map((followUp: FollowUp) => (
                  <Card key={followUp._id} className="relative opacity-80">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          {getTypeIcon(followUp.type)}
                          <CardTitle className="ml-2 text-lg">{followUp.title}</CardTitle>
                        </div>
                        <Badge className="bg-green-100 text-green-800 border-green-300">
                          Completed {followUp.completedDate && new Date(followUp.completedDate).toLocaleDateString()}
                        </Badge>
                      </div>
                      <CardDescription>
                        Scheduled for {new Date(followUp.scheduledDate).toLocaleDateString()} at {new Date(followUp.scheduledDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {followUp.description && <p className="text-sm text-gray-700">{followUp.description}</p>}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <CheckSquare className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium">No completed follow-ups</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Complete some follow-ups to see them here.
                </p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="all" className="space-y-4">
            {isFollowUpsLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            ) : followUps && followUps.length > 0 ? (
              <div className="space-y-4">
                {followUps.map((followUp: FollowUp) => (
                  <Card key={followUp._id} className={`relative ${followUp.status === 'completed' ? 'opacity-80' : ''}`}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          {getTypeIcon(followUp.type)}
                          <CardTitle className="ml-2 text-lg">{followUp.title}</CardTitle>
                        </div>
                        <Badge className={`
                          ${followUp.status === 'completed' 
                            ? 'bg-green-100 text-green-800 border-green-300' 
                            : followUp.status === 'cancelled'
                              ? 'bg-gray-100 text-gray-800 border-gray-300'
                              : getPriorityColor(followUp.priority)
                          }
                        `}>
                          {followUp.status === 'completed' 
                            ? `Completed ${followUp.completedDate && new Date(followUp.completedDate).toLocaleDateString()}`
                            : followUp.status === 'cancelled'
                              ? 'Cancelled'
                              : `${followUp.priority.charAt(0).toUpperCase() + followUp.priority.slice(1)} Priority`
                          }
                        </Badge>
                      </div>
                      <CardDescription>
                        Scheduled for {new Date(followUp.scheduledDate).toLocaleDateString()} at {new Date(followUp.scheduledDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {followUp.description && <p className="text-sm text-gray-700">{followUp.description}</p>}
                    </CardContent>
                    {followUp.status === 'pending' && (
                      <CardFooter className="flex justify-end space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleCancel(followUp._id)}
                        >
                          <X className="h-4 w-4 mr-1" />
                          Cancel
                        </Button>
                        <Button 
                          variant="default" 
                          size="sm"
                          onClick={() => handleMarkComplete(followUp._id)}
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Mark Complete
                        </Button>
                      </CardFooter>
                    )}
                  </Card>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium">No follow-ups found</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Generate recommendations or create a new follow-up to get started.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
