import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar, Loader2 } from 'lucide-react';
import { FollowUpCoachDialog } from './FollowUpCoachDialog';

interface FollowUpCoachButtonProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  activityId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

export function FollowUpCoachButton({
  opportunityId,
  contactId,
  companyId,
  activityId,
  opportunityName,
  contactName,
  companyName,
  variant = 'outline',
  size = 'sm',
  buttonText = 'Follow-up Coach'
}: FollowUpCoachButtonProps) {
  return (
    <FollowUpCoachDialog
      opportunityId={opportunityId}
      contactId={contactId}
      companyId={companyId}
      activityId={activityId}
      opportunityName={opportunityName}
      contactName={contactName}
      companyName={companyName}
      variant={variant}
      size={size}
      buttonText={buttonText}
    />
  );
}
