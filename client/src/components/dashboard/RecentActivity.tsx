import { useEffect, useState } from "react";
import { Calendar, CheckCircle, MessageSquare, Brain } from "lucide-react";
import { Activity } from "@shared/schema";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";

interface RecentActivityProps {
  limit?: number;
}

const RecentActivity: React.FC<RecentActivityProps> = ({ limit = 4 }) => {
  const { data: activities, isLoading, error } = useQuery({
    queryKey: ['/api/activities'],
    queryFn: async () => {
      const response = await fetch(`/api/activities?limit=${limit}`);
      if (!response.ok) {
        throw new Error('Failed to fetch activities');
      }
      return response.json() as Promise<Activity[]>;
    }
  });

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'meeting_scheduled':
        return <Calendar className="h-5 w-5 text-primary" />;
      case 'opportunity_status_change':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'comment_added':
        return <MessageSquare className="h-5 w-5 text-secondary" />;
      case 'ai_insight':
        return <Brain className="h-5 w-5 text-purple-600" />;
      default:
        return <Calendar className="h-5 w-5 text-primary" />;
    }
  };

  const getActivityBgColor = (type: string) => {
    switch (type) {
      case 'meeting_scheduled':
        return 'bg-blue-100';
      case 'opportunity_status_change':
        return 'bg-green-100';
      case 'comment_added':
        return 'bg-violet-100';
      case 'ai_insight':
        return 'bg-purple-100';
      default:
        return 'bg-blue-100';
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      if (diffInHours < 1) {
        return 'Just now';
      }
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Recent Activity</h3>
          <button className="text-sm text-primary font-medium">View All</button>
        </div>
        <div className="space-y-4">
          {[...Array(limit)].map((_, i) => (
            <div key={i} className="flex items-start">
              <Skeleton className="h-10 w-10 rounded-lg" />
              <div className="ml-3 flex-1">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/4" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Recent Activity</h3>
        </div>
        <p className="text-red-500">Error loading activities</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Recent Activity</h3>
        <button className="text-sm text-primary font-medium">View All</button>
      </div>
      <div className="space-y-4">
        {activities && activities.length > 0 ? (
          activities.map((activity) => (
            <div key={activity.id} className="flex items-start">
              <div className={`${getActivityBgColor(activity.type)} p-2 rounded-lg`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-800">
                  {activity.title}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {activity.timestamp && formatDate(new Date(activity.timestamp))}
                </p>
              </div>
            </div>
          ))
        ) : (
          <p className="text-sm text-gray-500">No recent activities</p>
        )}
      </div>
    </div>
  );
};

export default RecentActivity;
