import { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { GripVertical } from "lucide-react";

interface DraggableCardProps {
  id: string;
  title: string;
  onDragStart: (id: string) => void;
  onDragEnd: () => void;
  onDragOver: (id: string) => void;
  onDrop: (id: string) => void;
  isDragging: boolean;
  children: React.ReactNode;
  className?: string;
}

export default function DraggableCard({
  id,
  title,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  isDragging,
  children,
  className = ""
}: DraggableCardProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("text/plain", id);
    
    // Set a ghost image (optional)
    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      e.dataTransfer.setDragImage(cardRef.current, rect.width / 2, 20);
    }
    
    setTimeout(() => {
      if (cardRef.current) {
        cardRef.current.style.opacity = "0.4";
      }
    }, 0);
    
    onDragStart(id);
  };

  const handleDragEnd = () => {
    if (cardRef.current) {
      cardRef.current.style.opacity = "1";
    }
    onDragEnd();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isDragging) {
      setIsDragOver(true);
      onDragOver(id);
    }
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const droppedId = e.dataTransfer.getData("text/plain");
    if (droppedId !== id) {
      onDrop(id);
    }
  };

  return (
    <Card 
      ref={cardRef}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      className={`${className} ${isDragging ? 'opacity-40' : ''} ${isDragOver ? 'ring-2 ring-primary' : ''}`}
      style={{ cursor: 'move' }}
    >
      <CardHeader className="p-4 pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
        <GripVertical className="h-5 w-5 text-muted-foreground cursor-move" />
      </CardHeader>
      <CardContent className="p-4 pt-0">
        {children}
      </CardContent>
    </Card>
  );
}