import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "wouter";
import { Skeleton } from "@/components/ui/skeleton";
import { Relationship } from "@shared/schema";

const RelationshipNetwork = () => {
  const { data: relationships, isLoading, error } = useQuery({
    queryKey: ['/api/relationships'],
    queryFn: async () => {
      const response = await fetch('/api/relationships');
      if (!response.ok) {
        throw new Error('Failed to fetch relationships');
      }
      return response.json() as Promise<Relationship[]>;
    }
  });

  // This is a simplified version - in a real app, you would use
  // React Force Graph or similar library for a proper visualization
  
  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Relationship Network</h3>
        <Link href="/relationships">
          <a className="text-sm text-primary font-medium">Full View</a>
        </Link>
      </div>
      
      {isLoading ? (
        <Skeleton className="h-48 w-full rounded-lg" />
      ) : error ? (
        <div className="bg-red-50 p-3 rounded-lg text-red-500">
          Error loading relationship data
        </div>
      ) : (
        <>
          <div className="bg-gray-50 rounded-lg p-4 mb-3">
            {/* Simplified Relationship Graph */}
            <div className="relative h-48">
              {/* Center node */}
              <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="bg-primary text-white text-xs font-bold rounded-full w-10 h-10 flex items-center justify-center">
                  YOU
                </div>
              </div>
              
              {/* Connection nodes */}
              <div className="absolute left-1/4 top-1/4">
                <div className="bg-blue-100 text-primary text-xs font-medium rounded-full w-8 h-8 flex items-center justify-center">
                  TC
                </div>
              </div>
              
              <div className="absolute right-1/4 top-1/4">
                <div className="bg-violet-100 text-secondary text-xs font-medium rounded-full w-8 h-8 flex items-center justify-center">
                  AI
                </div>
              </div>
              
              <div className="absolute left-1/4 bottom-1/4">
                <div className="bg-green-100 text-green-600 text-xs font-medium rounded-full w-8 h-8 flex items-center justify-center">
                  GL
                </div>
              </div>
              
              <div className="absolute right-1/4 bottom-1/4">
                <div className="bg-yellow-100 text-yellow-600 text-xs font-medium rounded-full w-8 h-8 flex items-center justify-center">
                  SJ
                </div>
              </div>
              
              {/* Connection lines */}
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 200 200">
                <line x1="100" y1="100" x2="50" y2="50" stroke="#E5E7EB" strokeWidth="2" />
                <line x1="100" y1="100" x2="150" y2="50" stroke="#E5E7EB" strokeWidth="2" />
                <line x1="100" y1="100" x2="50" y2="150" stroke="#E5E7EB" strokeWidth="2" />
                <line x1="100" y1="100" x2="150" y2="150" stroke="#E5E7EB" strokeWidth="2" />
              </svg>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            <p className="mb-2">Key connection insights:</p>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li><span className="font-medium">TechCorp</span> is connected to <span className="font-medium">Acme Inc</span> through 2 contacts</li>
              <li><span className="font-medium">Sarah Johnson</span> previously worked with <span className="font-medium">Michael Brown</span></li>
              <li>3 new potential connections identified last week</li>
            </ul>
          </div>
        </>
      )}
    </div>
  );
};

export default RelationshipNetwork;
