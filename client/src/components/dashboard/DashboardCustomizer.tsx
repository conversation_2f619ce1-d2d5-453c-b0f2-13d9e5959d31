import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>ting<PERSON>, GripVertical, Save } from "lucide-react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface DashboardCard {
  id: string;
  title: string;
  description: string;
}

interface DashboardPreferences {
  enabledCards: string[];
  cardOrder: string[];
}

const availableCards: DashboardCard[] = [
  {
    id: "metrics",
    title: "Key Metrics",
    description: "Shows important numbers like contacts, opportunities, and pipeline value"
  },
  {
    id: "activities",
    title: "Recent Activities",
    description: "Latest activities across all contacts and companies"
  },
  {
    id: "opportunities",
    title: "Open Opportunities",
    description: "Your current sales pipeline with status and value"
  },
  {
    id: "aiAssistant",
    title: "AI Assistant",
    description: "Get help from your intelligent CRM assistant"
  },
  {
    id: "relationships",
    title: "Relationship Network",
    description: "Visual map of your business relationships"
  },
  {
    id: "insights",
    title: "AI Insights",
    description: "AI-generated suggestions based on your CRM data"
  }
];

export default function DashboardCustomizer() {
  const [open, setOpen] = useState(false);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [cardOrder, setCardOrder] = useState<string[]>([]);
  const [draggedCard, setDraggedCard] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch current preferences
  const { data: preferences, isLoading } = useQuery({
    queryKey: ['/api/user/preferences'],
    queryFn: async () => {
      const response = await fetch('/api/user/preferences');
      if (!response.ok) {
        throw new Error('Failed to fetch preferences');
      }
      return response.json();
    }
  });

  // Update preferences mutation
  const mutation = useMutation({
    mutationFn: async (newPreferences: DashboardPreferences) => {
      return apiRequest('/api/user/preferences', 'POST', {
        preferences: { dashboard: newPreferences }
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/preferences'] });
      toast({
        title: "Dashboard saved",
        description: "Your dashboard layout has been updated.",
      });
      setOpen(false);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to save dashboard preferences.",
        variant: "destructive",
      });
    }
  });

  // Update local state when preferences are loaded
  useEffect(() => {
    if (preferences?.dashboard) {
      setSelectedCards(preferences.dashboard.enabledCards || []);
      setCardOrder(preferences.dashboard.cardOrder || []);
    } else {
      // Default selection if no preferences exist
      const defaultCards = ["metrics", "activities", "opportunities", "aiAssistant"];
      setSelectedCards(defaultCards);
      setCardOrder(defaultCards);
    }
  }, [preferences]);

  const handleSave = () => {
    // Ensure card order contains all selected cards (and only selected cards)
    const newCardOrder = [...cardOrder.filter(id => selectedCards.includes(id))];
    
    // Add any selected cards that aren't in the order yet
    selectedCards.forEach(id => {
      if (!newCardOrder.includes(id)) {
        newCardOrder.push(id);
      }
    });
    
    mutation.mutate({
      enabledCards: selectedCards,
      cardOrder: newCardOrder
    });
  };

  const handleCardSelection = (cardId: string, checked: boolean) => {
    if (checked) {
      setSelectedCards([...selectedCards, cardId]);
    } else {
      setSelectedCards(selectedCards.filter(id => id !== cardId));
    }
  };

  const handleDragStart = (cardId: string) => {
    setDraggedCard(cardId);
  };

  const handleDragOver = (e: React.DragEvent, targetCardId: string) => {
    e.preventDefault();
    if (!draggedCard || draggedCard === targetCardId) return;
  };

  const handleDrop = (targetCardId: string) => {
    if (!draggedCard || draggedCard === targetCardId) return;
    
    const newCardOrder = [...cardOrder];
    const draggedIndex = newCardOrder.indexOf(draggedCard);
    const targetIndex = newCardOrder.indexOf(targetCardId);
    
    if (draggedIndex !== -1 && targetIndex !== -1) {
      // Remove the dragged card from its original position
      newCardOrder.splice(draggedIndex, 1);
      // Insert it at the new position
      newCardOrder.splice(targetIndex, 0, draggedCard);
      setCardOrder(newCardOrder);
    }
    
    setDraggedCard(null);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-1" />
          Customize
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Customize Your Dashboard</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <h3 className="text-sm font-medium mb-3">1. Select cards to display (max 6)</h3>
          <div className="space-y-2">
            {availableCards.map((card) => (
              <div key={card.id} className="flex items-start space-x-2">
                <Checkbox 
                  id={`card-${card.id}`}
                  checked={selectedCards.includes(card.id)}
                  onCheckedChange={(checked) => handleCardSelection(card.id, checked === true)}
                  disabled={selectedCards.length >= 6 && !selectedCards.includes(card.id)}
                />
                <div className="grid gap-1">
                  <Label htmlFor={`card-${card.id}`}>{card.title}</Label>
                  <p className="text-xs text-muted-foreground">{card.description}</p>
                </div>
              </div>
            ))}
          </div>
          
          {selectedCards.length > 0 && (
            <>
              <h3 className="text-sm font-medium mt-6 mb-3">2. Arrange cards (drag to reorder)</h3>
              <div className="space-y-2">
                {cardOrder
                  .filter(id => selectedCards.includes(id))
                  .map((cardId) => {
                    const card = availableCards.find(c => c.id === cardId);
                    if (!card) return null;
                    
                    return (
                      <div
                        key={card.id}
                        draggable
                        onDragStart={() => handleDragStart(card.id)}
                        onDragOver={(e) => handleDragOver(e, card.id)}
                        onDrop={() => handleDrop(card.id)}
                        className="flex items-center p-2 border rounded-md bg-white cursor-move"
                      >
                        <GripVertical className="h-4 w-4 mr-2 text-gray-400" />
                        <span>{card.title}</span>
                      </div>
                    );
                  })}
              </div>
            </>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={mutation.isPending}
          >
            {mutation.isPending ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}