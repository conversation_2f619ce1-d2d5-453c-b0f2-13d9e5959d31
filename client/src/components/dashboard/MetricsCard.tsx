import { ArrowDown, ArrowUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface MetricsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change?: {
    value: number;
    trend: 'up' | 'down';
    text: string;
  };
  iconBgClassName?: string;
}

const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  icon,
  change,
  iconBgClassName = "bg-blue-100"
}) => {
  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-bold text-gray-800 mt-1">{value}</p>
        </div>
        <div className={cn("p-2 rounded-lg", iconBgClassName)}>
          {icon}
        </div>
      </div>
      
      {change && (
        <div className="flex items-center mt-3">
          <span className={cn(
            "text-sm font-medium flex items-center",
            change.trend === 'up' ? "text-green-600" : "text-red-600"
          )}>
            {change.trend === 'up' ? (
              <ArrowUp className="h-4 w-4 mr-1" />
            ) : (
              <ArrowDown className="h-4 w-4 mr-1" />
            )}
            {change.value}%
          </span>
          <span className="text-sm text-gray-500 ml-2">{change.text}</span>
        </div>
      )}
    </div>
  );
};

export default MetricsCard;
