import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Brain, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { sendChatMessage, getChatHistory, AIChatResponse } from "@/lib/openai";
import { Skeleton } from "@/components/ui/skeleton";
import { Link } from "wouter";

const AIChatAssistant = () => {
  const [message, setMessage] = useState("");
  const queryClient = useQueryClient();
  
  const { data: chatHistory, isLoading: isLoadingHistory } = useQuery({
    queryKey: ['/api/ai/history'],
    queryFn: getChatHistory
  });

  const mutation = useMutation({
    mutationFn: sendChatMessage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/ai/history'] });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;
    
    mutation.mutate(message);
    setMessage("");
  };

  const suggestedQueries = [
    "Suggest leads to contact this week",
    "Find customers who might be interested in our new product",
    "What's the status of my deals?"
  ];

  const handleSuggestedQuery = (query: string) => {
    mutation.mutate(query);
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">AI Assistant</h3>
        <Link href="/ai-assistant">
          <a className="text-sm text-primary font-medium">Open Chat</a>
        </Link>
      </div>
      
      <div className="space-y-3 mb-4">
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm font-medium text-gray-800 mb-1">Try asking:</p>
          <div className="space-y-2">
            {suggestedQueries.map((query, index) => (
              <button
                key={index}
                className="w-full text-left text-xs bg-white border border-gray-200 rounded-md px-3 py-2 hover:border-primary text-gray-600"
                onClick={() => handleSuggestedQuery(query)}
              >
                {query}
              </button>
            ))}
          </div>
        </div>
        
        {isLoadingHistory ? (
          <Skeleton className="h-20 w-full" />
        ) : chatHistory && chatHistory.length > 0 ? (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
            <div className="flex items-start">
              <div className="bg-primary p-1.5 rounded-md">
                <Brain className="h-4 w-4 text-white" />
              </div>
              <div className="ml-3 flex-1">
                <p className="text-xs font-medium text-gray-600">AI Insight</p>
                <p className="text-sm text-gray-800 mt-1">
                  {chatHistory[0].response}
                </p>
              </div>
            </div>
          </div>
        ) : null}
      </div>
      
      <form onSubmit={handleSubmit} className="relative">
        <Input
          type="text"
          placeholder="Ask something..."
          className="w-full py-2 pl-3 pr-10 text-sm bg-gray-100 border border-transparent rounded-lg focus:outline-none focus:bg-white focus:border-primary"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          disabled={mutation.isPending}
        />
        <Button
          type="submit"
          className="absolute right-1 top-1 bg-primary text-white p-1 rounded-md"
          size="icon"
          disabled={mutation.isPending}
        >
          <Send className="h-5 w-5" />
        </Button>
      </form>
    </div>
  );
};

export default AIChatAssistant;
