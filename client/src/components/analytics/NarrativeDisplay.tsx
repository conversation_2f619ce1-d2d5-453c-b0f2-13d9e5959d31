import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ChevronDown, ChevronUp, Info, Search } from 'lucide-react';

interface NarrativeDisplayProps {
  narrative: string;
  rootCauseAnalysis?: string;
}

const NarrativeDisplay: React.FC<NarrativeDisplayProps> = ({ 
  narrative, 
  rootCauseAnalysis 
}) => {
  const [expanded, setExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('summary');

  // Format narrative with bullet points
  const formatNarrative = (text: string) => {
    if (!text) return '';
    
    // Split by newlines and format as bullet points if not already
    return text.split('\n').map((line, index) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return null;
      
      // If line already starts with a bullet point or number, return as is
      if (/^[•\-*\d]+\.?\s/.test(trimmedLine)) {
        return <p key={index} className="mb-2">{trimmedLine}</p>;
      }
      
      // Otherwise, add a bullet point
      return <p key={index} className="mb-2">• {trimmedLine}</p>;
    }).filter(Boolean);
  };

  // Determine if we should show tabs
  const showTabs = !!rootCauseAnalysis;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Insights</CardTitle>
          <button 
            onClick={() => setExpanded(!expanded)}
            className="text-muted-foreground hover:text-foreground"
          >
            {expanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </button>
        </div>
        <CardDescription>
          AI-generated analysis of the data
        </CardDescription>
      </CardHeader>
      <CardContent>
        {showTabs ? (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="summary">
                <Info className="h-4 w-4 mr-2" />
                Summary
              </TabsTrigger>
              <TabsTrigger value="rootCause">
                <Search className="h-4 w-4 mr-2" />
                Root Cause
              </TabsTrigger>
            </TabsList>
            <TabsContent value="summary" className="pt-4">
              <div className={`prose prose-sm max-w-none ${!expanded ? 'line-clamp-3' : ''}`}>
                {formatNarrative(narrative)}
              </div>
            </TabsContent>
            <TabsContent value="rootCause" className="pt-4">
              <div className={`prose prose-sm max-w-none ${!expanded ? 'line-clamp-3' : ''}`}>
                {formatNarrative(rootCauseAnalysis || '')}
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className={`prose prose-sm max-w-none ${!expanded ? 'line-clamp-3' : ''}`}>
            {formatNarrative(narrative)}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default NarrativeDisplay;
