import React from 'react';
import { SuggestedExperimentType } from '@/types/analytics-reporting';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { 
  Beaker, 
  Lightbulb, 
  TrendingUp, 
  ArrowRight,
  Zap
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface SuggestedExperimentsProps {
  experiments: SuggestedExperimentType[];
}

const SuggestedExperiments: React.FC<SuggestedExperimentsProps> = ({ experiments }) => {
  if (!experiments || experiments.length === 0) {
    return null;
  }

  // Get confidence level label and color
  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 80) {
      return { label: 'High', color: 'bg-green-500' };
    } else if (confidence >= 50) {
      return { label: 'Medium', color: 'bg-yellow-500' };
    } else {
      return { label: 'Low', color: 'bg-red-500' };
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <Lightbulb className="h-5 w-5 mr-2 text-yellow-500" />
          <CardTitle className="text-lg">Suggested Experiments</CardTitle>
        </div>
        <CardDescription>
          AI-recommended actions to improve your results
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          {experiments.map((experiment, index) => {
            const confidenceInfo = getConfidenceLevel(experiment.confidence);
            
            return (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="hover:no-underline">
                  <div className="flex items-center text-left">
                    <Beaker className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="font-medium">{experiment.title}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pt-2">
                    <p className="text-sm">{experiment.description}</p>
                    
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">Expected Impact:</span>
                      <span className="text-sm">{experiment.estimated_impact}</span>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-xs">Confidence</span>
                        <span className="text-xs font-medium">{confidenceInfo.label} ({experiment.confidence}%)</span>
                      </div>
                      <Progress value={experiment.confidence} className={confidenceInfo.color} />
                    </div>
                    
                    <div className="pt-2">
                      <Button variant="outline" size="sm" className="w-full">
                        <Zap className="h-3 w-3 mr-2" />
                        Create Workflow
                        <ArrowRight className="h-3 w-3 ml-2" />
                      </Button>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </CardContent>
    </Card>
  );
};

export default SuggestedExperiments;
