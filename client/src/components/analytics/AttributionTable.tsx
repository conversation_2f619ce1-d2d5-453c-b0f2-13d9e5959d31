import React, { useState } from 'react';
import { AttributionResultsType } from '@/types/analytics-reporting';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ArrowUpDown, 
  Search, 
  ChevronDown, 
  ChevronUp,
  Download
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface AttributionTableProps {
  results: AttributionResultsType[];
  groupBy: string;
}

const AttributionTable: React.FC<AttributionTableProps> = ({ results, groupBy }) => {
  const [sortField, setSortField] = useState<keyof AttributionResultsType>('credit_pct');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Handle sort
  const handleSort = (field: keyof AttributionResultsType) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Format data
  const formatData = () => {
    let filteredData = [...results];
    
    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filteredData = filteredData.filter(item => {
        const fieldValue = String(item[groupBy as keyof AttributionResultsType] || '').toLowerCase();
        return fieldValue.includes(term);
      });
    }
    
    // Apply sorting
    filteredData.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (aValue === undefined || bValue === undefined) return 0;
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      const aString = String(aValue);
      const bString = String(bValue);
      
      return sortDirection === 'asc' 
        ? aString.localeCompare(bString) 
        : bString.localeCompare(aString);
    });
    
    return filteredData;
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Get group by field label
  const getGroupByLabel = () => {
    switch (groupBy) {
      case 'channel':
        return 'Channel';
      case 'campaign':
        return 'Campaign';
      case 'medium':
        return 'Medium';
      case 'source':
        return 'Source';
      case 'creative':
        return 'Creative';
      case 'keyword':
        return 'Keyword';
      default:
        return 'Channel';
    }
  };

  // Export to CSV
  const exportToCsv = () => {
    const data = formatData();
    const headers = [
      getGroupByLabel(),
      'Attribution (%)',
      'Revenue',
      'Cost',
      'ROI',
      'Conversions'
    ];
    
    const csvRows = [
      headers.join(','),
      ...data.map(item => [
        `"${item[groupBy as keyof AttributionResultsType] || 'Unknown'}"`,
        item.credit_pct.toFixed(2),
        item.revenue.toFixed(2),
        item.cost.toFixed(2),
        item.roi.toFixed(2),
        item.conversions
      ].join(','))
    ];
    
    const csvString = csvRows.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `attribution_${groupBy}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const data = formatData();

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={`Search by ${getGroupByLabel().toLowerCase()}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button variant="outline" size="sm" onClick={exportToCsv}>
          <Download className="h-4 w-4 mr-2" />
          Export CSV
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort(groupBy as keyof AttributionResultsType)}
                  className="flex items-center justify-between w-full"
                >
                  {getGroupByLabel()}
                  {sortField === groupBy ? (
                    sortDirection === 'asc' ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )
                  ) : (
                    <ArrowUpDown className="h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('credit_pct')}
                  className="flex items-center justify-between w-full"
                >
                  Attribution (%)
                  {sortField === 'credit_pct' ? (
                    sortDirection === 'asc' ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )
                  ) : (
                    <ArrowUpDown className="h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('revenue')}
                  className="flex items-center justify-between w-full"
                >
                  Revenue
                  {sortField === 'revenue' ? (
                    sortDirection === 'asc' ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )
                  ) : (
                    <ArrowUpDown className="h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('cost')}
                  className="flex items-center justify-between w-full"
                >
                  Cost
                  {sortField === 'cost' ? (
                    sortDirection === 'asc' ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )
                  ) : (
                    <ArrowUpDown className="h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('roi')}
                  className="flex items-center justify-between w-full"
                >
                  ROI
                  {sortField === 'roi' ? (
                    sortDirection === 'asc' ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )
                  ) : (
                    <ArrowUpDown className="h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('conversions')}
                  className="flex items-center justify-between w-full"
                >
                  Conversions
                  {sortField === 'conversions' ? (
                    sortDirection === 'asc' ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )
                  ) : (
                    <ArrowUpDown className="h-4 w-4" />
                  )}
                </Button>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No results found.
                </TableCell>
              </TableRow>
            ) : (
              data.map((result) => (
                <TableRow key={result.id}>
                  <TableCell className="font-medium">
                    {result[groupBy as keyof AttributionResultsType] || 'Unknown'}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>{result.credit_pct.toFixed(1)}%</span>
                      </div>
                      <Progress value={result.credit_pct} className="h-2" />
                    </div>
                  </TableCell>
                  <TableCell>{formatCurrency(result.revenue)}</TableCell>
                  <TableCell>{formatCurrency(result.cost)}</TableCell>
                  <TableCell>{result.roi.toFixed(2)}</TableCell>
                  <TableCell>{result.conversions.toLocaleString()}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default AttributionTable;
