import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { optimizeBudget } from '@/api/analytics-reporting-api';
import { BudgetOptimizationResponse } from '@/types/analytics-reporting';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, RefreshCw, TrendingUp, ArrowRight, ArrowDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface BudgetOptimizerProps {
  window: string;
  model: 'markov' | 'shapley';
}

const BudgetOptimizer: React.FC<BudgetOptimizerProps> = ({ window, model }) => {
  const [target, setTarget] = useState<'revenue' | 'conversions' | 'roi'>('revenue');
  const [budgetConstraint, setBudgetConstraint] = useState<number | undefined>(undefined);
  const { toast } = useToast();

  // Optimize budget mutation
  const optimizeBudgetMutation = useMutation({
    mutationFn: optimizeBudget,
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to optimize budget',
        variant: 'destructive',
      });
    },
  });

  // Handle optimize
  const handleOptimize = () => {
    optimizeBudgetMutation.mutate({
      window,
      model_type: model,
      optimization_target: target,
      budget_constraint: budgetConstraint,
    });
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(0)}%`;
  };

  // Get color based on value
  const getValueColor = (value: number) => {
    return value >= 0 ? 'text-green-500' : 'text-red-500';
  };

  // Get arrow icon based on value
  const getValueIcon = (value: number) => {
    return value >= 0 ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <ArrowDown className="h-4 w-4 text-red-500" />
    );
  };

  const results = optimizeBudgetMutation.data;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="text-sm font-medium mb-1 block">Optimization Target</label>
          <Select value={target} onValueChange={(value) => setTarget(value as any)}>
            <SelectTrigger>
              <SelectValue placeholder="Select target" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="revenue">Maximize Revenue</SelectItem>
              <SelectItem value="conversions">Maximize Conversions</SelectItem>
              <SelectItem value="roi">Maximize ROI</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <label className="text-sm font-medium mb-1 block">Budget Constraint (optional)</label>
          <Input
            type="number"
            placeholder="Enter budget limit"
            value={budgetConstraint || ''}
            onChange={(e) => setBudgetConstraint(e.target.value ? Number(e.target.value) : undefined)}
          />
        </div>
        <div className="flex items-end">
          <Button
            onClick={handleOptimize}
            disabled={optimizeBudgetMutation.isPending}
            className="w-full"
          >
            {optimizeBudgetMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Optimize Budget
          </Button>
        </div>
      </div>

      {results && results.success && (
        <div className="space-y-6">
          {/* Summary cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Projected Impact</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Revenue</span>
                    <div className="flex items-center">
                      {getValueIcon(results.summary.projected_revenue_increase)}
                      <span className={`ml-1 font-medium ${getValueColor(results.summary.projected_revenue_increase)}`}>
                        {formatCurrency(results.summary.projected_revenue_increase)}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Conversions</span>
                    <div className="flex items-center">
                      {getValueIcon(results.summary.projected_conversion_increase)}
                      <span className={`ml-1 font-medium ${getValueColor(results.summary.projected_conversion_increase)}`}>
                        +{results.summary.projected_conversion_increase.toFixed(0)}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">ROI</span>
                    <div className="flex items-center">
                      {getValueIcon(results.summary.projected_roi_increase)}
                      <span className={`ml-1 font-medium ${getValueColor(results.summary.projected_roi_increase)}`}>
                        +{results.summary.projected_roi_increase.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Current Budget</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(results.summary.total_current_budget)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total marketing spend
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Recommended Budget</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(results.summary.total_recommended_budget)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Optimized allocation
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recommendations table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Channel/Campaign</TableHead>
                  <TableHead>Current Budget</TableHead>
                  <TableHead>Recommended Budget</TableHead>
                  <TableHead>Change</TableHead>
                  <TableHead>Projected Impact</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {results.recommendations.map((rec, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {rec.campaign ? `${rec.channel} / ${rec.campaign}` : rec.channel}
                    </TableCell>
                    <TableCell>{formatCurrency(rec.current_budget)}</TableCell>
                    <TableCell>{formatCurrency(rec.recommended_budget)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {getValueIcon(rec.change_amount)}
                        <span className={`ml-1 ${getValueColor(rec.change_amount)}`}>
                          {formatCurrency(rec.change_amount)} ({formatPercentage(rec.change_percentage)})
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-xs space-y-1">
                        <div>Revenue: {formatCurrency(rec.projected_impact.revenue)}</div>
                        <div>Conversions: {rec.projected_impact.conversions.toFixed(0)}</div>
                        <div>ROI: {rec.projected_impact.roi.toFixed(2)}</div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {!results && !optimizeBudgetMutation.isPending && (
        <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
          <TrendingUp className="h-12 w-12 mb-4" />
          <p>Click "Optimize Budget" to get AI-powered budget recommendations</p>
        </div>
      )}
    </div>
  );
};

export default BudgetOptimizer;
