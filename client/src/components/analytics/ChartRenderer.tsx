import React from 'react';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { ChartSpecType } from '@/types/analytics-reporting';

interface ChartRendererProps {
  chartSpec: ChartSpecType;
}

// Default colors for charts
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe', '#00c49f', '#ffbb28', '#ff8042',
  '#a4de6c', '#d0ed57', '#83a6ed', '#8dd1e1', '#82ca9d', '#a4de6c', '#d0ed57', '#ffc658'
];

const ChartRenderer: React.FC<ChartRendererProps> = ({ chartSpec }) => {
  const { type, data, options } = chartSpec;

  // Handle empty data
  if (!data || (Array.isArray(data) && data.length === 0)) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        No data available
      </div>
    );
  }

  // Render different chart types
  switch (type) {
    case 'bar':
      return (
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            {...options}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey={options?.xAxis?.dataKey || 'name'} 
              angle={options?.xAxis?.angle || -45}
              textAnchor="end"
              height={60}
            />
            <YAxis />
            <Tooltip />
            <Legend />
            {options?.series ? (
              options.series.map((series: any, index: number) => (
                <Bar
                  key={series.dataKey || `series-${index}`}
                  dataKey={series.dataKey}
                  name={series.name || series.dataKey}
                  fill={series.color || COLORS[index % COLORS.length]}
                  stackId={series.stackId}
                />
              ))
            ) : (
              <Bar
                dataKey={options?.dataKey || 'value'}
                fill={options?.color || COLORS[0]}
              />
            )}
          </BarChart>
        </ResponsiveContainer>
      );

    case 'line':
      return (
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            {...options}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey={options?.xAxis?.dataKey || 'name'} 
              angle={options?.xAxis?.angle || -45}
              textAnchor="end"
              height={60}
            />
            <YAxis />
            <Tooltip />
            <Legend />
            {options?.series ? (
              options.series.map((series: any, index: number) => (
                <Line
                  key={series.dataKey || `series-${index}`}
                  type="monotone"
                  dataKey={series.dataKey}
                  name={series.name || series.dataKey}
                  stroke={series.color || COLORS[index % COLORS.length]}
                  activeDot={{ r: 8 }}
                  strokeWidth={2}
                />
              ))
            ) : (
              <Line
                type="monotone"
                dataKey={options?.dataKey || 'value'}
                stroke={options?.color || COLORS[0]}
                activeDot={{ r: 8 }}
                strokeWidth={2}
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      );

    case 'pie':
      return (
        <ResponsiveContainer width="100%" height="100%">
          <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={true}
              outerRadius={options?.outerRadius || 80}
              innerRadius={options?.innerRadius || 0}
              fill="#8884d8"
              dataKey={options?.dataKey || 'value'}
              nameKey={options?.nameKey || 'name'}
              label={options?.label !== false ? ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%` : undefined}
            >
              {data.map((entry: any, index: number) => (
                <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      );

    case 'area':
      return (
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            {...options}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey={options?.xAxis?.dataKey || 'name'} 
              angle={options?.xAxis?.angle || -45}
              textAnchor="end"
              height={60}
            />
            <YAxis />
            <Tooltip />
            <Legend />
            {options?.series ? (
              options.series.map((series: any, index: number) => (
                <Area
                  key={series.dataKey || `series-${index}`}
                  type="monotone"
                  dataKey={series.dataKey}
                  name={series.name || series.dataKey}
                  stroke={series.color || COLORS[index % COLORS.length]}
                  fill={series.color || COLORS[index % COLORS.length]}
                  fillOpacity={0.3}
                  stackId={series.stackId}
                />
              ))
            ) : (
              <Area
                type="monotone"
                dataKey={options?.dataKey || 'value'}
                stroke={options?.color || COLORS[0]}
                fill={options?.color || COLORS[0]}
                fillOpacity={0.3}
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      );

    // Add more chart types as needed

    default:
      return (
        <div className="flex items-center justify-center h-full text-muted-foreground">
          Unsupported chart type: {type}
        </div>
      );
  }
};

export default ChartRenderer;
