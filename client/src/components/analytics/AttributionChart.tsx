import React, { useState } from 'react';
import { AttributionResultsType } from '@/types/analytics-reporting';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Label,
} from 'recharts';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

interface AttributionChartProps {
  results: AttributionResultsType[];
  groupBy: string;
}

// Default colors for charts
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe', '#00c49f', '#ffbb28', '#ff8042',
  '#a4de6c', '#d0ed57', '#83a6ed', '#8dd1e1', '#82ca9d', '#a4de6c', '#d0ed57', '#ffc658'
];

const AttributionChart: React.FC<AttributionChartProps> = ({ results, groupBy }) => {
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  const [metric, setMetric] = useState<'credit_pct' | 'revenue' | 'roi' | 'conversions'>('credit_pct');

  // Format data for chart
  const formatData = () => {
    return results.map((result, index) => ({
      name: result[groupBy as keyof AttributionResultsType] || 'Unknown',
      value: result[metric],
      color: COLORS[index % COLORS.length],
      ...result
    }));
  };

  // Get metric label
  const getMetricLabel = () => {
    switch (metric) {
      case 'credit_pct':
        return 'Attribution (%)';
      case 'revenue':
        return 'Revenue ($)';
      case 'roi':
        return 'ROI';
      case 'conversions':
        return 'Conversions';
      default:
        return 'Value';
    }
  };

  // Format tooltip value
  const formatTooltipValue = (value: number) => {
    switch (metric) {
      case 'credit_pct':
        return `${value.toFixed(1)}%`;
      case 'revenue':
        return `$${value.toLocaleString()}`;
      case 'roi':
        return value.toFixed(2);
      case 'conversions':
        return value.toLocaleString();
      default:
        return value;
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background p-3 border rounded-md shadow-md">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">
            {getMetricLabel()}: {formatTooltipValue(data.value)}
          </p>
          {metric !== 'credit_pct' && (
            <p className="text-sm">
              Attribution: {data.credit_pct.toFixed(1)}%
            </p>
          )}
          {metric !== 'revenue' && (
            <p className="text-sm">
              Revenue: ${data.revenue.toLocaleString()}
            </p>
          )}
          {metric !== 'roi' && (
            <p className="text-sm">
              ROI: {data.roi.toFixed(2)}
            </p>
          )}
          <p className="text-sm">
            Cost: ${data.cost.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  // Render pie chart
  const renderPieChart = () => {
    const data = formatData();
    return (
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={true}
            outerRadius={150}
            fill="#8884d8"
            dataKey="value"
            nameKey="name"
            label={({ name, value, percent }) => 
              `${name}: ${formatTooltipValue(value)} (${(percent * 100).toFixed(0)}%)`
            }
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  // Render bar chart
  const renderBarChart = () => {
    const data = formatData();
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="name" 
            angle={-45} 
            textAnchor="end"
            height={80}
          />
          <YAxis>
            <Label
              value={getMetricLabel()}
              angle={-90}
              position="insideLeft"
              style={{ textAnchor: 'middle' }}
            />
          </YAxis>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Bar dataKey="value" name={getMetricLabel()}>
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="h-full">
      <div className="flex justify-end space-x-2 mb-4">
        <Select value={metric} onValueChange={(value) => setMetric(value as any)}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select metric" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="credit_pct">Attribution (%)</SelectItem>
            <SelectItem value="revenue">Revenue</SelectItem>
            <SelectItem value="roi">ROI</SelectItem>
            <SelectItem value="conversions">Conversions</SelectItem>
          </SelectContent>
        </Select>
        
        <Select value={chartType} onValueChange={(value) => setChartType(value as any)}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Chart type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="bar">Bar Chart</SelectItem>
            <SelectItem value="pie">Pie Chart</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {chartType === 'pie' ? renderPieChart() : renderBarChart()}
    </div>
  );
};

export default AttributionChart;
