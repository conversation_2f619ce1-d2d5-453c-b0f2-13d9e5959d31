import React from 'react';
import { CBICacheType } from '@/types/analytics-reporting';
import { formatDistanceToNow } from 'date-fns';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AreaChart } from 'lucide-react';

interface RecentQueriesProps {
  insights: CBICacheType[];
  onSelect: (insight: CBICacheType) => void;
  currentInsightId?: string;
}

const RecentQueries: React.FC<RecentQueriesProps> = ({ 
  insights, 
  onSelect,
  currentInsightId
}) => {
  // Get chart icon based on chart type
  const getChartIcon = (chartType: string) => {
    switch (chartType) {
      case 'bar':
        return <BarChart className="h-4 w-4" />;
      case 'pie':
        return <PieChart className="h-4 w-4" />;
      case 'line':
        return <LineChart className="h-4 w-4" />;
      case 'area':
        return <AreaChart className="h-4 w-4" />;
      default:
        return <BarChart className="h-4 w-4" />;
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };

  return (
    <div className="space-y-1">
      {insights.length === 0 ? (
        <div className="text-center py-4 text-muted-foreground">
          No recent questions
        </div>
      ) : (
        insights.map((insight) => (
          <div
            key={insight.id}
            className={`flex items-start p-2 rounded-md cursor-pointer hover:bg-muted ${
              currentInsightId === insight.id ? 'bg-muted' : ''
            }`}
            onClick={() => onSelect(insight)}
          >
            <div className="mr-2 mt-1 text-muted-foreground">
              {getChartIcon(insight.chart_spec.type)}
            </div>
            <div className="flex-grow">
              <p className="text-sm font-medium line-clamp-2">{insight.question}</p>
              <p className="text-xs text-muted-foreground">
                {formatTimeAgo(insight.created_at)}
              </p>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default RecentQueries;
