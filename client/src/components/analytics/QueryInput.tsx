import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { HelpCircle } from 'lucide-react';

interface QueryInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Example questions to suggest to users
const EXAMPLE_QUESTIONS = [
  "What was our conversion rate last month?",
  "Which marketing channel has the highest ROI?",
  "How many leads did we generate by source last quarter?",
  "What's our average deal size by industry?",
  "Show me monthly revenue trend for the past year",
  "Compare sales performance by rep this quarter vs last quarter",
  "What's our customer acquisition cost by channel?",
  "Which campaigns had the highest click-through rate?",
  "What's our sales pipeline forecast for next quarter?",
  "Show me the top 5 reasons deals are lost"
];

const QueryInput: React.FC<QueryInputProps> = ({ 
  value, 
  onChange, 
  placeholder = "Ask a question about your data...",
  disabled = false
}) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Update suggestions based on input value
  useEffect(() => {
    if (!value) {
      // Show random examples when input is empty
      const randomExamples = [...EXAMPLE_QUESTIONS]
        .sort(() => 0.5 - Math.random())
        .slice(0, 3);
      setSuggestions(randomExamples);
    } else {
      // Filter examples that match the input
      const matchingSuggestions = EXAMPLE_QUESTIONS.filter(q => 
        q.toLowerCase().includes(value.toLowerCase())
      ).slice(0, 3);
      setSuggestions(matchingSuggestions);
    }
  }, [value]);

  // Handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: string) => {
    const event = {
      target: { value: suggestion }
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(event);
    setShowSuggestions(false);
  };

  return (
    <div className="relative w-full">
      <div className="flex w-full items-center space-x-2">
        <div className="relative flex-grow">
          <Input
            type="text"
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            disabled={disabled}
            className="pr-10"
            onFocus={() => setShowSuggestions(true)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6"
              >
                <HelpCircle className="h-4 w-4 text-muted-foreground" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-2">
                <h4 className="font-medium">Example questions</h4>
                <ul className="space-y-1 text-sm">
                  {EXAMPLE_QUESTIONS.slice(0, 5).map((q, i) => (
                    <li key={i} className="cursor-pointer hover:text-primary" onClick={() => handleSelectSuggestion(q)}>
                      • {q}
                    </li>
                  ))}
                </ul>
                <h4 className="font-medium pt-2">Tips</h4>
                <ul className="space-y-1 text-sm">
                  <li>Be specific about time periods (e.g., "last quarter")</li>
                  <li>Specify metrics (e.g., "conversion rate", "revenue")</li>
                  <li>Ask for comparisons (e.g., "compare this month vs last month")</li>
                </ul>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-background border rounded-md shadow-lg">
          <ul className="py-1">
            {suggestions.map((suggestion, index) => (
              <li
                key={index}
                className="px-4 py-2 hover:bg-muted cursor-pointer text-sm"
                onClick={() => handleSelectSuggestion(suggestion)}
              >
                {suggestion}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default QueryInput;
