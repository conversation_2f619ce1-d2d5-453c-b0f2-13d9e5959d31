import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, CheckCircle2, TrendingUp, Lightbulb, Clock, RefreshCw } from 'lucide-react';

interface PipelineInsight {
  _id: string;
  title: string;
  description: string;
  type: 'opportunity' | 'risk' | 'trend' | 'suggestion' | 'reminder';
  importance: number;
  targetType: string;
  targetId: string;
  generatedBy: string;
  isRead: boolean;
  actionTaken: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PipelineInsightsProps {
  opportunityId: string;
}

export function PipelineInsights({ opportunityId }: PipelineInsightsProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isGenerating, setIsGenerating] = useState(false);

  // Fetch insights for this opportunity
  const { 
    data: insights, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['pipelineInsights', opportunityId],
    queryFn: async () => {
      return mongoApiClient.pipeline.getOpportunityInsights(opportunityId);
    },
    enabled: !!opportunityId
  });

  // Mark insight as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: async (insightId: string) => {
      return mongoApiClient.insights.markAsRead(insightId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pipelineInsights', opportunityId] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to mark insight as read',
        variant: 'destructive',
      });
    },
  });

  // Generate new insights mutation
  const generateInsightsMutation = useMutation({
    mutationFn: async () => {
      return mongoApiClient.pipeline.runChecks('all');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pipelineInsights', opportunityId] });
      toast({
        title: 'Success',
        description: 'New pipeline insights generated',
      });
      setIsGenerating(false);
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate insights',
        variant: 'destructive',
      });
      setIsGenerating(false);
    },
  });

  // Handle generating new insights
  const handleGenerateInsights = () => {
    setIsGenerating(true);
    generateInsightsMutation.mutate();
  };

  // Handle marking an insight as read
  const handleMarkAsRead = (insightId: string) => {
    markAsReadMutation.mutate(insightId);
  };

  // Get icon based on insight type
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity':
        return <TrendingUp className="h-5 w-5 text-green-500" />;
      case 'risk':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'trend':
        return <TrendingUp className="h-5 w-5 text-blue-500" />;
      case 'suggestion':
        return <Lightbulb className="h-5 w-5 text-amber-500" />;
      case 'reminder':
        return <Clock className="h-5 w-5 text-purple-500" />;
      default:
        return <Lightbulb className="h-5 w-5 text-gray-500" />;
    }
  };

  // Get badge color based on insight type
  const getInsightBadgeColor = (type: string) => {
    switch (type) {
      case 'opportunity':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'risk':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'trend':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'suggestion':
        return 'bg-amber-100 text-amber-800 hover:bg-amber-200';
      case 'reminder':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Pipeline Insights</h3>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleGenerateInsights}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Generate Insights
            </>
          )}
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-3">
          <Skeleton className="h-[150px] w-full rounded-lg" />
          <Skeleton className="h-[150px] w-full rounded-lg" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>Error loading insights: {error instanceof Error ? error.message : 'Unknown error'}</p>
            </div>
          </CardContent>
        </Card>
      ) : insights && insights.length > 0 ? (
        <div className="space-y-4">
          {insights.map((insight: PipelineInsight) => (
            <Card key={insight._id} className={insight.isRead ? 'opacity-70' : ''}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center">
                    {getInsightIcon(insight.type)}
                    <CardTitle className="ml-2 text-lg">{insight.title}</CardTitle>
                  </div>
                  <Badge className={getInsightBadgeColor(insight.type)}>
                    {insight.type.charAt(0).toUpperCase() + insight.type.slice(1)}
                  </Badge>
                </div>
                <CardDescription className="text-xs">
                  Generated on {formatDate(insight.createdAt)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-line">{insight.description}</p>
              </CardContent>
              {!insight.isRead && (
                <CardFooter className="pt-0 flex justify-end">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => handleMarkAsRead(insight._id)}
                    className="text-xs"
                  >
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    Mark as read
                  </Button>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="pt-6 pb-6">
            <div className="text-center text-gray-500">
              <Lightbulb className="h-8 w-8 mx-auto mb-2" />
              <p>No insights available for this opportunity yet.</p>
              <p className="text-sm mt-1">
                Generate insights to get AI-powered analysis of this opportunity.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default PipelineInsights;
