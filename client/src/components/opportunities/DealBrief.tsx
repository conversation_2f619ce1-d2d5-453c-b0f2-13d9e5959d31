import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus,
  FileText,
  Phone,
  Mail,
  Calendar,
  MessageSquare,
  ArrowRight
} from 'lucide-react';

interface DealBriefProps {
  opportunityId: string;
}

interface DealBrief {
  summary: string;
  sentimentTrend: string;
  keyObjections: string[];
  nextBestAction: string;
  relevantActivities: Array<{
    id: string;
    title: string;
    type: string;
    date: Date;
    snippet: string;
  }>;
  relevantDocuments: Array<{
    id: string;
    name: string;
    type: string;
    snippet: string;
  }>;
}

export function DealBrief({ opportunityId }: DealBriefProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isGenerating, setIsGenerating] = useState(false);

  // Fetch deal brief for this opportunity
  const {
    data: dealBrief,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['dealBrief', opportunityId],
    queryFn: async () => {
      return mongoApiClient.opportunities.getDealBrief(opportunityId);
    },
    enabled: !!opportunityId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  // Handle generating new deal brief
  const handleGenerateBrief = async () => {
    setIsGenerating(true);
    try {
      await refetch();
      toast({
        title: 'Success',
        description: 'Deal brief generated successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate deal brief',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Get sentiment icon
  const getSentimentIcon = (sentiment: string) => {
    if (!sentiment) return <Minus className="h-5 w-5 text-gray-500" />;

    const lowerSentiment = sentiment.toLowerCase();

    if (lowerSentiment.includes('positive')) {
      return <TrendingUp className="h-5 w-5 text-green-500" />;
    } else if (lowerSentiment.includes('negative')) {
      return <TrendingDown className="h-5 w-5 text-red-500" />;
    } else if (lowerSentiment.includes('neutral')) {
      return <Minus className="h-5 w-5 text-gray-500" />;
    } else if (lowerSentiment.includes('mixed')) {
      return <RefreshCw className="h-5 w-5 text-amber-500" />;
    } else {
      return <Minus className="h-5 w-5 text-gray-500" />;
    }
  };

  // Get activity icon
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'call':
        return <Phone className="h-4 w-4 text-blue-500" />;
      case 'email':
        return <Mail className="h-4 w-4 text-purple-500" />;
      case 'meeting':
        return <Calendar className="h-4 w-4 text-green-500" />;
      case 'note':
        return <MessageSquare className="h-4 w-4 text-amber-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Deal Brief</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={handleGenerateBrief}
          disabled={isGenerating || isLoading}
        >
          {isGenerating ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Generate Brief
            </>
          )}
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-3">
          <Skeleton className="h-[200px] w-full rounded-lg" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>Error loading deal brief: {error instanceof Error ? error.message : 'Unknown error'}</p>
            </div>
          </CardContent>
        </Card>
      ) : dealBrief ? (
        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">{dealBrief.summary}</p>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center">
                  {getSentimentIcon(dealBrief.sentimentTrend)}
                  <CardTitle className="ml-2 text-lg">Sentiment Trend</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{dealBrief.sentimentTrend}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-amber-500" />
                  <CardTitle className="ml-2 text-lg">Key Objections</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                {dealBrief.keyObjections.length > 0 ? (
                  <ul className="list-disc pl-5 space-y-1">
                    {dealBrief.keyObjections.map((objection: { text: string }, index: number) => (
                      <li key={index} className="text-sm">{objection.text}</li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500">No key objections identified.</p>
                )}
              </CardContent>
            </Card>
          </div>

          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-2">
              <div className="flex items-center">
                <ArrowRight className="h-5 w-5 text-blue-600" />
                <CardTitle className="ml-2 text-lg text-blue-700">Next Best Action</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm font-medium text-blue-800">{dealBrief.nextBestAction}</p>
            </CardContent>
          </Card>

          <Tabs defaultValue="activities" className="w-full">
            <TabsList>
              <TabsTrigger value="activities">Relevant Activities</TabsTrigger>
              <TabsTrigger value="documents">Relevant Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="activities" className="mt-4">
              {dealBrief.relevantActivities && dealBrief.relevantActivities.length > 0 ? (
                <div className="space-y-3">
                  {dealBrief.relevantActivities.map((activity: {
                    id: string;
                    type: string;
                    title: string;
                    date: string | Date;
                    snippet: string;
                  }) => (
                    <Card key={activity.id} className="overflow-hidden">
                      <div className="flex items-start p-4">
                        <div className="mr-3 mt-1">
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <h4 className="font-medium text-sm">{activity.title}</h4>
                            <Badge variant="outline" className="text-xs">
                              {formatDate(activity.date as unknown as string)}
                            </Badge>
                          </div>
                          <p className="text-sm mt-1 text-gray-600">{activity.snippet}</p>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="pt-6 pb-6 text-center text-gray-500">
                    No relevant activities found.
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="documents" className="mt-4">
              {dealBrief.relevantDocuments && dealBrief.relevantDocuments.length > 0 ? (
                <div className="space-y-3">
                  {dealBrief.relevantDocuments.map((document: {
                    id: string;
                    name: string;
                    type: string;
                    snippet: string;
                  }) => (
                    <Card key={document.id} className="overflow-hidden">
                      <div className="flex items-start p-4">
                        <div className="mr-3 mt-1">
                          <FileText className="h-4 w-4 text-blue-500" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <h4 className="font-medium text-sm">{document.name}</h4>
                            <Badge variant="outline" className="text-xs">
                              {document.type}
                            </Badge>
                          </div>
                          <p className="text-sm mt-1 text-gray-600">{document.snippet}</p>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="pt-6 pb-6 text-center text-gray-500">
                    No relevant documents found.
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <Card>
          <CardContent className="pt-6 pb-6">
            <div className="text-center text-gray-500">
              <FileText className="h-8 w-8 mx-auto mb-2" />
              <p>No deal brief available yet.</p>
              <p className="text-sm mt-1">
                Generate a deal brief to get AI-powered analysis of this opportunity.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default DealBrief;
