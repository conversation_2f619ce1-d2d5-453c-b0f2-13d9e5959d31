import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertCircle,
  RefreshCw,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  CheckCircle2,
  XCircle,
  Clock,
  BarChart3
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface StageAnalysisProps {
  opportunityId: string;
}

interface StageAnalysis {
  currentStage: string;
  recommendedStage: string;
  confidence: number;
  explanation: string;
  keyIndicators: string[];
  requiresReview: boolean;
}

interface StageTransition {
  _id: string;
  opportunityId: string;
  previousStage: string;
  newStage: string;
  confidence: number;
  explanation: string;
  keyIndicators: string[];
  appliedBy: 'ai' | 'user' | null;
  appliedAt: string | null;
  reviewedBy: string | null;
  reviewedAt: string | null;
  approved: boolean | null;
  createdAt: string;
  updatedAt: string;
}

export function StageAnalysis({ opportunityId }: StageAnalysisProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Fetch stage analysis for this opportunity
  const {
    data: analysis,
    isLoading: isAnalysisLoading,
    error: analysisError,
    refetch: refetchAnalysis
  } = useQuery({
    queryKey: ['stageAnalysis', opportunityId],
    queryFn: async () => {
      return mongoApiClient.opportunities.getStageAnalysis(opportunityId);
    },
    enabled: !!opportunityId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  // Fetch stage transitions for this opportunity
  const {
    data: transitions,
    isLoading: isTransitionsLoading,
    error: transitionsError,
    refetch: refetchTransitions
  } = useQuery({
    queryKey: ['stageTransitions', opportunityId],
    queryFn: async () => {
      return mongoApiClient.opportunities.getStageTransitions(opportunityId);
    },
    enabled: !!opportunityId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  // Apply stage transition mutation
  const applyTransitionMutation = useMutation({
    mutationFn: async ({ id, approved }: { id: string; approved: boolean }) => {
      return mongoApiClient.stageTransitions.applyTransition(id, approved);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stageTransitions', opportunityId] });
      queryClient.invalidateQueries({ queryKey: ['opportunity', opportunityId] });
      toast({
        title: 'Success',
        description: 'Stage transition applied successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to apply stage transition',
        variant: 'destructive',
      });
    },
  });

  // Handle generating new analysis
  const handleGenerateAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      await refetchAnalysis();
      toast({
        title: 'Success',
        description: 'Stage analysis generated successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate stage analysis',
        variant: 'destructive',
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Handle applying a stage transition
  const handleApplyTransition = (id: string, approved: boolean) => {
    applyTransitionMutation.mutate({ id, approved });
  };

  // Format stage label
  const formatStageLabel = (stage?: string) => {
    if (!stage) return '-';
    return stage
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get stage badge color
  const getStageBadgeColor = (stage?: string) => {
    if (!stage) return 'bg-gray-100 text-gray-800';

    switch (stage) {
      case 'prospecting':
        return 'bg-blue-100 text-blue-800';
      case 'qualification':
        return 'bg-indigo-100 text-indigo-800';
      case 'needs_analysis':
        return 'bg-purple-100 text-purple-800';
      case 'value_proposition':
        return 'bg-pink-100 text-pink-800';
      case 'decision_makers':
        return 'bg-yellow-100 text-yellow-800';
      case 'proposal':
        return 'bg-pink-100 text-pink-800';
      case 'negotiation':
        return 'bg-orange-100 text-orange-800';
      case 'closed_won':
        return 'bg-green-100 text-green-800';
      case 'closed_lost':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get stage direction icon
  const getStageDirectionIcon = (currentStage: string, recommendedStage: string) => {
    const stages = [
      'prospecting',
      'qualification',
      'needs_analysis',
      'value_proposition',
      'decision_makers',
      'proposal',
      'negotiation',
      'closed_won',
      'closed_lost'
    ];

    const currentIndex = stages.indexOf(currentStage);
    const recommendedIndex = stages.indexOf(recommendedStage);

    if (currentIndex === recommendedIndex) {
      return <ArrowRight className="h-5 w-5 text-gray-500" />;
    } else if (currentIndex < recommendedIndex) {
      return <ArrowUp className="h-5 w-5 text-green-500" />;
    } else {
      return <ArrowDown className="h-5 w-5 text-red-500" />;
    }
  };

  // Format date
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Format confidence percentage
  const formatConfidence = (confidence?: number) => {
    if (confidence === undefined || confidence === null) return '-';
    return `${Math.round(confidence * 100)}%`;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Stage Analysis</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={handleGenerateAnalysis}
          disabled={isAnalyzing}
        >
          {isAnalyzing ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Analyze Stage
            </>
          )}
        </Button>
      </div>

      <Tabs defaultValue="analysis" className="w-full">
        <TabsList>
          <TabsTrigger value="analysis">Current Analysis</TabsTrigger>
          <TabsTrigger value="history">Stage History</TabsTrigger>
        </TabsList>

        <TabsContent value="analysis" className="mt-4">
          {isAnalysisLoading ? (
            <div className="space-y-3">
              <Skeleton className="h-[200px] w-full rounded-lg" />
            </div>
          ) : analysisError ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-red-500">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                  <p>Error loading stage analysis: {analysisError instanceof Error ? analysisError.message : 'Unknown error'}</p>
                </div>
              </CardContent>
            </Card>
          ) : analysis ? (
            <div className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Stage Recommendation</CardTitle>
                    <Badge className="ml-2">
                      {formatConfidence(analysis.confidence)} Confidence
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center space-x-4 py-2">
                    <div className="text-center">
                      <p className="text-sm text-gray-500 mb-1">Current Stage</p>
                      <Badge className={getStageBadgeColor(analysis.currentStage)}>
                        {formatStageLabel(analysis.currentStage)}
                      </Badge>
                    </div>

                    <div className="flex-shrink-0">
                      {getStageDirectionIcon(analysis.currentStage, analysis.recommendedStage)}
                    </div>

                    <div className="text-center">
                      <p className="text-sm text-gray-500 mb-1">Recommended Stage</p>
                      <Badge className={getStageBadgeColor(analysis.recommendedStage)}>
                        {formatStageLabel(analysis.recommendedStage)}
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium mb-1">Explanation:</p>
                    <p className="text-sm">{analysis.explanation}</p>
                  </div>

                  {analysis.keyIndicators && analysis.keyIndicators.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-1">Key Indicators:</p>
                      <ul className="list-disc pl-5 space-y-1">
                        {analysis.keyIndicators.map((indicator: {
                          name: string;
                          status: string;
                          description: string;
                        }, index: number) => (
                          <li key={index} className="text-sm">
                            <span className={`font-medium ${
                              indicator.status === 'positive' ? 'text-green-600' :
                              indicator.status === 'negative' ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {indicator.name}
                            </span>
                            : {indicator.description}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
                {analysis.currentStage !== analysis.recommendedStage && (
                  <CardFooter className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Create a new transition and apply it
                        mongoApiClient.stageTransitions.analyzeAll(false)
                          .then(() => {
                            refetchTransitions();
                            toast({
                              title: 'Success',
                              description: 'Stage transition created. Review in the Stage History tab.',
                            });
                          })
                          .catch((error) => {
                            toast({
                              title: 'Error',
                              description: error instanceof Error ? error.message : 'Failed to create stage transition',
                              variant: 'destructive',
                            });
                          });
                      }}
                    >
                      Create Transition
                    </Button>
                  </CardFooter>
                )}
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6 pb-6">
                <div className="text-center text-gray-500">
                  <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                  <p>No stage analysis available yet.</p>
                  <p className="text-sm mt-1">
                    Generate a stage analysis to get AI-powered recommendations.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="mt-4">
          {isTransitionsLoading ? (
            <div className="space-y-3">
              <Skeleton className="h-[200px] w-full rounded-lg" />
            </div>
          ) : transitionsError ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-red-500">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                  <p>Error loading stage transitions: {transitionsError instanceof Error ? transitionsError.message : 'Unknown error'}</p>
                </div>
              </CardContent>
            </Card>
          ) : transitions && transitions.length > 0 ? (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Stage Transition History</CardTitle>
                <CardDescription>
                  History of stage changes and recommendations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>From</TableHead>
                      <TableHead>To</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transitions.map((transition: StageTransition) => (
                      <TableRow key={transition._id}>
                        <TableCell className="font-medium">
                          {formatDate(transition.createdAt)}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStageBadgeColor(transition.previousStage)}>
                            {formatStageLabel(transition.previousStage)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStageBadgeColor(transition.newStage)}>
                            {formatStageLabel(transition.newStage)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {transition.appliedAt ? (
                            <Badge className="bg-green-100 text-green-800">
                              Applied
                            </Badge>
                          ) : transition.reviewedAt ? (
                            transition.approved ? (
                              <Badge className="bg-green-100 text-green-800">
                                Approved
                              </Badge>
                            ) : (
                              <Badge className="bg-red-100 text-red-800">
                                Rejected
                              </Badge>
                            )
                          ) : (
                            <Badge className="bg-yellow-100 text-yellow-800">
                              Pending
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          {!transition.appliedAt && !transition.reviewedAt && (
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleApplyTransition(transition._id, true)}
                                title="Approve"
                              >
                                <CheckCircle2 className="h-4 w-4 text-green-500" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleApplyTransition(transition._id, false)}
                                title="Reject"
                              >
                                <XCircle className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6 pb-6">
                <div className="text-center text-gray-500">
                  <Clock className="h-8 w-8 mx-auto mb-2" />
                  <p>No stage transitions available yet.</p>
                  <p className="text-sm mt-1">
                    Stage transitions will appear here when the AI recommends a stage change.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default StageAnalysis;
