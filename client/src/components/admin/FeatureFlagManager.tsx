import { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/api/api-utils";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Trash, Edit, Plus, Search, RefreshCw } from "lucide-react";

// Feature flag form schema
const featureFlagFormSchema = z.object({
  key: z.string().min(1, "Key is required").max(100).regex(/^[a-z0-9_.-]+$/, "Key must contain only lowercase letters, numbers, underscores, dots, and hyphens"),
  name: z.string().min(1, "Name is required").max(100),
  description: z.string().optional(),
  enabled: z.boolean().default(false),
  enabledForPercentage: z.number().min(0).max(100).default(0),
  tags: z.string().optional(),
});

type FeatureFlagFormValues = z.infer<typeof featureFlagFormSchema>;

interface FeatureFlag {
  key: string;
  name: string;
  description?: string;
  enabled: boolean;
  enabledForUsers: string[];
  enabledForTenants: string[];
  enabledForPercentage: number;
  rules: {
    type: string;
    value: any;
  }[];
  tags: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function FeatureFlagManager() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedFlag, setSelectedFlag] = useState<FeatureFlag | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterEnabled, setFilterEnabled] = useState<boolean | undefined>(undefined);
  const [filterTag, setFilterTag] = useState<string | undefined>(undefined);

  // Fetch feature flags
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["feature-flags", filterEnabled, filterTag, searchQuery],
    queryFn: async () => {
      let url = "/api/feature-flags";
      const params = new URLSearchParams();

      if (filterEnabled !== undefined) {
        params.append("enabled", String(filterEnabled));
      }

      if (filterTag) {
        params.append("tags", filterTag);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await apiRequest<{ success: boolean; featureFlags: FeatureFlag[] }>({
        url,
        method: "GET",
      });

      return response.featureFlags;
    },
  });

  // Filter feature flags by search query
  const filteredFlags = data?.filter((flag: FeatureFlag) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      flag.key.toLowerCase().includes(query) ||
      flag.name.toLowerCase().includes(query) ||
      flag.description?.toLowerCase().includes(query) ||
      flag.tags.some((tag: string) => tag.toLowerCase().includes(query))
    );
  });

  // Create feature flag form
  const createForm = useForm<FeatureFlagFormValues>({
    resolver: zodResolver(featureFlagFormSchema),
    defaultValues: {
      key: "",
      name: "",
      description: "",
      enabled: false,
      enabledForPercentage: 0,
      tags: "",
    },
  });

  // Edit feature flag form
  const editForm = useForm<Omit<FeatureFlagFormValues, "key">>({
    resolver: zodResolver(featureFlagFormSchema.omit({ key: true })),
    defaultValues: {
      name: "",
      description: "",
      enabled: false,
      enabledForPercentage: 0,
      tags: "",
    },
  });

  // Create feature flag mutation
  const createFeatureFlag = useMutation({
    mutationFn: async (data: FeatureFlagFormValues) => {
      return apiRequest({
        url: "/api/feature-flags",
        method: "POST",
        data,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["feature-flags"] });
      toast({
        title: "Feature flag created",
        description: "The feature flag has been created successfully.",
      });
      setIsCreateDialogOpen(false);
      createForm.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create feature flag.",
        variant: "destructive",
      });
    },
  });

  // Update feature flag mutation
  const updateFeatureFlag = useMutation({
    mutationFn: async (data: { key: string; values: Omit<FeatureFlagFormValues, "key"> }) => {
      return apiRequest({
        url: `/api/feature-flags/${data.key}`,
        method: "PATCH",
        data: data.values,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["feature-flags"] });
      toast({
        title: "Feature flag updated",
        description: "The feature flag has been updated successfully.",
      });
      setIsEditDialogOpen(false);
      setSelectedFlag(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update feature flag.",
        variant: "destructive",
      });
    },
  });

  // Delete feature flag mutation
  const deleteFeatureFlag = useMutation({
    mutationFn: async (key: string) => {
      return apiRequest({
        url: `/api/feature-flags/${key}`,
        method: "DELETE",
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["feature-flags"] });
      toast({
        title: "Feature flag deleted",
        description: "The feature flag has been deleted successfully.",
      });
      setIsDeleteDialogOpen(false);
      setSelectedFlag(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete feature flag.",
        variant: "destructive",
      });
    },
  });

  // Handle create form submission
  const onCreateSubmit = (values: FeatureFlagFormValues) => {
    // Convert tags string to array
    const formattedValues = {
      ...values,
      tags: values.tags ? values.tags.split(',').map(tag => tag.trim()) : []
    };
    createFeatureFlag.mutate(formattedValues as any);
  };

  // Handle edit form submission
  const onEditSubmit = (values: Omit<FeatureFlagFormValues, "key">) => {
    if (!selectedFlag) return;

    // Convert tags string to array
    const formattedValues = {
      ...values,
      tags: values.tags ? values.tags.split(',').map(tag => tag.trim()) : []
    };

    updateFeatureFlag.mutate({
      key: selectedFlag.key,
      values: formattedValues as any,
    });
  };

  // Handle delete confirmation
  const onDeleteConfirm = () => {
    if (!selectedFlag) return;

    deleteFeatureFlag.mutate(selectedFlag.key);
  };

  // Open edit dialog with selected flag data
  const openEditDialog = (flag: FeatureFlag) => {
    setSelectedFlag(flag);

    editForm.reset({
      name: flag.name,
      description: flag.description || "",
      enabled: flag.enabled,
      enabledForPercentage: flag.enabledForPercentage,
      tags: flag.tags.join(", "),
    });

    setIsEditDialogOpen(true);
  };

  // Open delete dialog with selected flag
  const openDeleteDialog = (flag: FeatureFlag) => {
    setSelectedFlag(flag);
    setIsDeleteDialogOpen(true);
  };

  // Get all unique tags from feature flags
  const allTags: string[] = Array.from(
    new Set(data?.flatMap((flag: FeatureFlag) => flag.tags).filter(Boolean) || [])
  ).sort() as string[];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Feature Flags</h2>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Flag
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative w-full sm:w-auto flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search feature flags..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex gap-2 items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilterEnabled(undefined)}
            className={filterEnabled === undefined ? "bg-primary text-primary-foreground" : ""}
          >
            All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilterEnabled(true)}
            className={filterEnabled === true ? "bg-primary text-primary-foreground" : ""}
          >
            Enabled
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilterEnabled(false)}
            className={filterEnabled === false ? "bg-primary text-primary-foreground" : ""}
          >
            Disabled
          </Button>
        </div>

        <Button variant="ghost" size="icon" onClick={() => refetch()}>
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      {allTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {allTags.map((tag: string) => (
            <Badge
              key={tag}
              variant={filterTag === tag ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setFilterTag(filterTag === tag ? undefined : tag)}
            >
              {tag}
            </Badge>
          ))}
        </div>
      )}

      {isLoading ? (
        <div className="text-center py-8">Loading feature flags...</div>
      ) : filteredFlags?.length === 0 ? (
        <div className="text-center py-8">No feature flags found.</div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Key</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Rollout %</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFlags?.map((flag: FeatureFlag) => (
                <TableRow key={flag.key}>
                  <TableCell className="font-mono">{flag.key}</TableCell>
                  <TableCell>
                    <div className="font-medium">{flag.name}</div>
                    {flag.description && (
                      <div className="text-sm text-muted-foreground">{flag.description}</div>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant={flag.enabled ? "default" : "secondary"}>
                      {flag.enabled ? "Enabled" : "Disabled"}
                    </Badge>
                  </TableCell>
                  <TableCell>{flag.enabledForPercentage}%</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {flag.tags.map((tag: string) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => openEditDialog(flag)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => openDeleteDialog(flag)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Create Feature Flag Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create Feature Flag</DialogTitle>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(onCreateSubmit)} className="space-y-4">
              <FormField
                control={createForm.control}
                name="key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Key*</FormLabel>
                    <FormControl>
                      <Input placeholder="feature-key" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="Feature Name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Description of the feature flag"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Enabled</FormLabel>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="enabledForPercentage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Percentage Rollout</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="tag1, tag2, tag3"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={createFeatureFlag.isPending}>
                  {createFeatureFlag.isPending ? "Creating..." : "Create"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Feature Flag Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Feature Flag</DialogTitle>
          </DialogHeader>
          {selectedFlag && (
            <Form {...editForm}>
              <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
                <div className="font-mono text-sm bg-muted p-2 rounded">
                  {selectedFlag.key}
                </div>

                <FormField
                  control={editForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name*</FormLabel>
                      <FormControl>
                        <Input placeholder="Feature Name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Description of the feature flag"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="enabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Enabled</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="enabledForPercentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Percentage Rollout</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="tags"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tags</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="tag1, tag2, tag3"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={updateFeatureFlag.isPending}>
                    {updateFeatureFlag.isPending ? "Updating..." : "Update"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Feature Flag Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Delete Feature Flag</DialogTitle>
          </DialogHeader>
          {selectedFlag && (
            <>
              <p>
                Are you sure you want to delete the feature flag <strong>{selectedFlag.name}</strong> ({selectedFlag.key})?
              </p>
              <p className="text-sm text-muted-foreground">
                This action cannot be undone.
              </p>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDeleteDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  onClick={onDeleteConfirm}
                  disabled={deleteFeatureFlag.isPending}
                >
                  {deleteFeatureFlag.isPending ? "Deleting..." : "Delete"}
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
