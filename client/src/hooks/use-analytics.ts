import { useCallback, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { apiRequest } from '@/lib/api';
import type { TrackEventRequest, TrackEventResponse } from '@/types/analytics';

/**
 * Hook to track analytics events
 * @returns Object with track function
 */
export function useAnalytics() {
  const router = useRouter();
  const sessionId = useRef<string>(getOrCreateSessionId());

  // Track page views
  useEffect(() => {
    // Skip tracking on initial render
    if (router.isReady) {
      trackEvent('page', 'view', {
        path: router.pathname,
        query: router.query,
        url: window.location.href,
        title: document.title,
      });
    }

    // Track page views on route change
    const handleRouteChange = (url: string) => {
      trackEvent('page', 'view', {
        path: url,
        url: window.location.href,
        title: document.title,
      });
    };

    router.events.on('routeChangeComplete', handleRouteChange);

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router.isReady, router.pathname, router.query]);

  // Track function
  const track = useCallback((eventType: string, eventName: string, properties: Record<string, any> = {}) => {
    trackEvent(eventType, eventName, properties);
  }, []);

  return { track };
}

/**
 * Track an analytics event
 * @param eventType The type of event (e.g., 'page', 'user', 'feature')
 * @param eventName The name of the event (e.g., 'view', 'signup', 'click')
 * @param properties Additional properties for the event
 */
export async function trackEvent(
  eventType: string,
  eventName: string,
  properties: Record<string, any> = {}
): Promise<void> {
  try {
    const sessionId = getOrCreateSessionId();

    const data: TrackEventRequest = {
      eventType,
      eventName,
      properties,
      sessionId,
      context: {
        page: {
          url: window.location.href,
          path: window.location.pathname,
          title: document.title,
          referrer: document.referrer,
        },
      },
    };

    await apiRequest<TrackEventResponse>({
      url: '/api/analytics/events',
      method: 'POST',
      data,
    });
  } catch (error) {
    console.error('Error tracking analytics event:', error);
  }
}

/**
 * Get or create a session ID
 * @returns Session ID
 */
function getOrCreateSessionId(): string {
  // Check if we have a session ID in localStorage
  let sessionId = localStorage.getItem('analytics_session_id');

  // If not, create a new one
  if (!sessionId) {
    sessionId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    localStorage.setItem('analytics_session_id', sessionId);

    // Set session expiration (30 minutes)
    const expiration = Date.now() + 30 * 60 * 1000;
    localStorage.setItem('analytics_session_expiration', expiration.toString());
  } else {
    // Check if session has expired
    const expiration = localStorage.getItem('analytics_session_expiration');

    if (expiration && parseInt(expiration, 10) < Date.now()) {
      // Create a new session
      sessionId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      localStorage.setItem('analytics_session_id', sessionId);
    }

    // Update expiration (30 minutes from now)
    const newExpiration = Date.now() + 30 * 60 * 1000;
    localStorage.setItem('analytics_session_expiration', newExpiration.toString());
  }

  return sessionId;
}

/**
 * Hook to track form submissions
 * @param formId Identifier for the form
 * @param formName Human-readable name for the form
 * @returns Object with tracking functions
 */
export function useFormAnalytics(formId: string, formName: string) {
  const { track } = useAnalytics();

  const trackSubmit = useCallback((values: Record<string, any>) => {
    track('form', 'submit', {
      formId,
      formName,
      values: sanitizeFormValues(values),
    });
  }, [track, formId, formName]);

  const trackError = useCallback((errors: Record<string, any>) => {
    track('form', 'error', {
      formId,
      formName,
      errors,
    });
  }, [track, formId, formName]);

  const trackFieldChange = useCallback((fieldName: string, value: any) => {
    track('form', 'field_change', {
      formId,
      formName,
      fieldName,
      value: typeof value === 'string' ? value : JSON.stringify(value),
    });
  }, [track, formId, formName]);

  return { trackSubmit, trackError, trackFieldChange };
}

/**
 * Sanitize form values to remove sensitive information
 * @param values Form values
 * @returns Sanitized values
 */
function sanitizeFormValues(values: Record<string, any>): Record<string, any> {
  const sensitiveFields = ['password', 'token', 'secret', 'credit_card', 'card', 'cvv', 'ssn'];

  return Object.keys(values).reduce((sanitized, key) => {
    if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof values[key] === 'object' && values[key] !== null) {
      sanitized[key] = sanitizeFormValues(values[key]);
    } else {
      sanitized[key] = values[key];
    }

    return sanitized;
  }, {} as Record<string, any>);
}

/**
 * Hook to track user interactions with a feature
 * @param featureId Identifier for the feature
 * @param featureName Human-readable name for the feature
 * @returns Object with tracking functions
 */
export function useFeatureAnalytics(featureId: string, featureName: string) {
  const { track } = useAnalytics();

  const trackUse = useCallback((action: string, properties: Record<string, any> = {}) => {
    track('feature', action, {
      featureId,
      featureName,
      ...properties,
    });
  }, [track, featureId, featureName]);

  const trackImpression = useCallback(() => {
    track('feature', 'impression', {
      featureId,
      featureName,
    });
  }, [track, featureId, featureName]);

  return { trackUse, trackImpression };
}

/**
 * Hook to track user engagement
 * @returns Object with tracking functions
 */
export function useEngagementAnalytics() {
  const { track } = useAnalytics();

  useEffect(() => {
    // Track time spent on page
    let startTime = Date.now();
    let isActive = true;

    const trackTimeSpent = () => {
      if (isActive) {
        const timeSpent = Math.floor((Date.now() - startTime) / 1000);

        track('engagement', 'time_spent', {
          seconds: timeSpent,
          path: window.location.pathname,
        });
      }
    };

    // Track when user becomes inactive/active
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // User left the page
        trackTimeSpent();
        isActive = false;
      } else {
        // User returned to the page
        startTime = Date.now();
        isActive = true;
      }
    };

    // Track scroll depth
    let maxScrollDepth = 0;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      const scrollDepth = Math.floor((scrollTop / (scrollHeight - clientHeight)) * 100);

      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;

        // Track at 25%, 50%, 75%, and 100%
        if (maxScrollDepth === 25 || maxScrollDepth === 50 || maxScrollDepth === 75 || maxScrollDepth === 100) {
          track('engagement', 'scroll_depth', {
            depth: maxScrollDepth,
            path: window.location.pathname,
          });
        }
      }
    };

    // Set up event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('scroll', handleScroll);

    // Track page exit
    const trackExit = () => {
      trackTimeSpent();
    };

    window.addEventListener('beforeunload', trackExit);

    return () => {
      // Clean up event listeners
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('beforeunload', trackExit);

      // Track time spent when component unmounts
      trackTimeSpent();
    };
  }, [track]);

  return { track };
}
