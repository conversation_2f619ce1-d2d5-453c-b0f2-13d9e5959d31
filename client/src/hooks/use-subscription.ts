import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth';
import { apiRequest } from '@/lib/api';

export interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'archived';
  isDefault: boolean;
  sortOrder: number;
  price: number;
  currency: string;
  billingPeriod: 'monthly' | 'yearly' | 'custom';
  trialDays: number;
  limits: Record<string, number>;
  features: Record<string, boolean>;
  moduleSettings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface TenantSubscription {
  id: number;
  tenantId: string;
  planId: number;
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'expired';
  startDate: string;
  endDate: string;
  trialEndsAt?: string;
  customLimits?: Record<string, number>;
  customFeatures?: Record<string, boolean>;
  billingDetails?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  canceledAt?: string;
}

export interface Feature {
  id: number;
  key: string;
  name: string;
  description: string;
  category: string;
  module: string;
  defaultValue: boolean;
  requiresRestart: boolean;
  uiComponent?: string;
  dependencies?: string[];
  createdAt: string;
  updatedAt: string;
  enabled?: boolean; // Added by the API
}

export interface UsageData {
  usage: Record<string, number>;
  featureUsage: Record<string, number>;
  limits: Record<string, number>;
  period: string;
}

export interface UseSubscriptionResult {
  subscription: TenantSubscription | null;
  plan: SubscriptionPlan | null;
  features: Feature[];
  usage: UsageData | null;
  loading: boolean;
  error: string | null;
  hasFeature: (featureKey: string) => boolean;
  checkResourceLimit: (resourceType: string, currentUsage?: number) => boolean;
  getUsagePercentage: (resourceType: string) => number;
  refreshSubscription: () => Promise<void>;
}

export function useSubscription(): UseSubscriptionResult {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<TenantSubscription | null>(null);
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null);
  const [features, setFeatures] = useState<Feature[]>([]);
  const [usage, setUsage] = useState<UsageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscriptionData = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Fetch subscription data
      const response = await apiRequest({
        url: '/api/subscription',
        method: 'GET'
      });
      setSubscription(response.subscription);
      setPlan(response.plan);

      // Fetch features
      const featuresResponse = await apiRequest({
        url: '/api/subscription/features',
        method: 'GET'
      });
      setFeatures(featuresResponse);

      // Fetch usage data
      const usageResponse = await apiRequest({
        url: '/api/subscription/usage',
        method: 'GET'
      });
      setUsage(usageResponse);
    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError('Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchSubscriptionData();
  }, [fetchSubscriptionData]);

  // Check if a feature is enabled
  const hasFeature = useCallback(
    (featureKey: string): boolean => {
      // If still loading, assume feature is not available
      if (loading || !subscription || !plan) return false;

      // First check if the feature exists in the features list
      const feature = features.find((f) => f.key === featureKey);
      if (feature && feature.enabled !== undefined) {
        return feature.enabled;
      }

      // If not in features list, check custom features
      if (subscription.customFeatures && featureKey in subscription.customFeatures) {
        return subscription.customFeatures[featureKey];
      }

      // Finally check plan features
      return plan.features[featureKey] || false;
    },
    [loading, subscription, plan, features]
  );

  // Check if within resource limits
  const checkResourceLimit = useCallback(
    (resourceType: string, currentUsage: number = 0): boolean => {
      if (loading || !subscription || !plan || !usage) return false;

      // Get the current usage from the usage data
      const existingUsage = usage.usage[resourceType] || 0;
      const totalUsage = existingUsage + currentUsage;

      // Get the limit from custom limits or plan limits
      let limit: number;
      if (subscription.customLimits && resourceType in subscription.customLimits) {
        limit = subscription.customLimits[resourceType];
      } else {
        limit = plan.limits[resourceType] || 0;
      }

      // Check if within limit
      return totalUsage <= limit;
    },
    [loading, subscription, plan, usage]
  );

  // Get usage percentage for a resource
  const getUsagePercentage = useCallback(
    (resourceType: string): number => {
      if (loading || !subscription || !plan || !usage) return 0;

      // Get the current usage from the usage data
      const currentUsage = usage.usage[resourceType] || 0;

      // Get the limit from custom limits or plan limits
      let limit: number;
      if (subscription.customLimits && resourceType in subscription.customLimits) {
        limit = subscription.customLimits[resourceType];
      } else {
        limit = plan.limits[resourceType] || 0;
      }

      // Calculate percentage (avoid division by zero)
      if (limit === 0) return 0;
      return Math.min(Math.round((currentUsage / limit) * 100), 100);
    },
    [loading, subscription, plan, usage]
  );

  return {
    subscription,
    plan,
    features,
    usage,
    loading,
    error,
    hasFeature,
    checkResourceLimit,
    getUsagePercentage,
    refreshSubscription: fetchSubscriptionData
  };
}
