import { useState, useEffect, useCallback } from 'react';
import { apiRequest } from '@/lib/api';
import type {
  CheckFeatureFlagResponse,
  CheckFeatureFlagsRequest,
  CheckFeatureFlagsResponse
} from '@/types/feature-flags';

/**
 * Hook to check if a feature flag is enabled
 * @param key The feature flag key
 * @param defaultValue Default value to use while loading
 * @returns Object with isEnabled, isLoading, and error
 */
export function useFeatureFlag(key: string, defaultValue = false) {
  const [isEnabled, setIsEnabled] = useState<boolean>(defaultValue);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const checkFeatureFlag = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiRequest<CheckFeatureFlagResponse>({
        url: `/api/feature-flags/check/${key}`,
        method: 'GET',
      });

      setIsEnabled(response.enabled);
    } catch (err) {
      console.error(`Error checking feature flag ${key}:`, err);
      setError(err instanceof Error ? err : new Error(String(err)));
      // Keep the default value on error
    } finally {
      setIsLoading(false);
    }
  }, [key]);

  useEffect(() => {
    checkFeatureFlag();
  }, [checkFeatureFlag]);

  return { isEnabled, isLoading, error, refresh: checkFeatureFlag };
}

/**
 * Hook to get multiple feature flags at once
 * @param keys Array of feature flag keys
 * @param defaultValues Default values to use while loading
 * @returns Object with featureFlags, isLoading, and error
 */
export function useFeatureFlags(keys: string[], defaultValues: Record<string, boolean> = {}) {
  const [featureFlags, setFeatureFlags] = useState<Record<string, boolean>>(
    keys.reduce((acc, key) => ({
      ...acc,
      [key]: defaultValues[key] ?? false,
    }), {})
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const checkFeatureFlags = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data: CheckFeatureFlagsRequest = { keys };
      const response = await apiRequest<CheckFeatureFlagsResponse>({
        url: '/api/feature-flags/check-batch',
        method: 'POST',
        data,
      });

      setFeatureFlags(response.flags);
    } catch (err) {
      console.error('Error checking feature flags:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      // Keep the default values on error
    } finally {
      setIsLoading(false);
    }
  }, [keys]);

  useEffect(() => {
    checkFeatureFlags();
  }, [checkFeatureFlags]);

  return { featureFlags, isLoading, error, refresh: checkFeatureFlags };
}

/**
 * Component to conditionally render based on a feature flag
 */
export function FeatureFlag({
  flag,
  children,
  fallback = null
}: {
  flag: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { isEnabled, isLoading } = useFeatureFlag(flag);

  if (isLoading) {
    return null;
  }

  return isEnabled ? <>{children}</> : <>{fallback}</>;
}
