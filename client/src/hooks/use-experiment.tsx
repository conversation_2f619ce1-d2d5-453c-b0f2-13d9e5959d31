import { useState, useEffect, useCallback } from 'react';
import { apiRequest } from '@/lib/api';
import type {
  GetVariantResponse,
  TrackConversionRequest,
  TrackConversionResponse,
  GetResultsResponse
} from '@/types/experiments';

/**
 * Hook to get the variant for an experiment
 * @param experimentKey The experiment key
 * @param defaultVariant Default variant to use while loading
 * @returns Object with variant, isLoading, and error
 */
export function useExperiment(experimentKey: string, defaultVariant = 'control') {
  const [variant, setVariant] = useState<string>(defaultVariant);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const getVariant = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiRequest<GetVariantResponse>({
        url: `/api/experiments/${experimentKey}/variant`,
        method: 'GET',
      });

      if (response.variant) {
        setVariant(response.variant);
      }
    } catch (err) {
      console.error(`Error getting variant for experiment ${experimentKey}:`, err);
      setError(err instanceof Error ? err : new Error(String(err)));
      // Keep the default variant on error
    } finally {
      setIsLoading(false);
    }
  }, [experimentKey]);

  useEffect(() => {
    getVariant();
  }, [getVariant]);

  /**
   * Track a conversion for the experiment
   * @param goalKey The goal key
   * @param value Optional value for the conversion
   * @param metadata Optional metadata for the conversion
   */
  const trackConversion = useCallback(async (
    goalKey: string,
    value?: number,
    metadata?: Record<string, any>
  ) => {
    try {
      const data: TrackConversionRequest = {
        goalKey,
        value,
        metadata,
      };

      await apiRequest<TrackConversionResponse>({
        url: `/api/experiments/${experimentKey}/conversion`,
        method: 'POST',
        data,
      });

      return true;
    } catch (err) {
      console.error(`Error tracking conversion for experiment ${experimentKey}:`, err);
      return false;
    }
  }, [experimentKey]);

  return { variant, isLoading, error, trackConversion };
}

/**
 * Component to conditionally render based on an experiment variant
 */
export function ExperimentVariant({
  experiment,
  variant,
  children,
  fallback = null,
  onMount,
}: {
  experiment: string;
  variant: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onMount?: () => void;
}) {
  const { variant: assignedVariant, isLoading } = useExperiment(experiment);

  useEffect(() => {
    if (!isLoading && assignedVariant === variant && onMount) {
      onMount();
    }
  }, [isLoading, assignedVariant, variant, onMount]);

  if (isLoading) {
    return null;
  }

  return assignedVariant === variant ? <>{children}</> : <>{fallback}</>;
}

/**
 * Hook to track experiment conversions
 * @param experimentKey The experiment key
 * @returns Function to track conversions
 */
export function useExperimentConversion(experimentKey: string) {
  const trackConversion = useCallback(async (
    goalKey: string,
    value?: number,
    metadata?: Record<string, any>
  ) => {
    try {
      const data: TrackConversionRequest = {
        goalKey,
        value,
        metadata,
      };

      await apiRequest<TrackConversionResponse>({
        url: `/api/experiments/${experimentKey}/conversion`,
        method: 'POST',
        data,
      });

      return true;
    } catch (err) {
      console.error(`Error tracking conversion for experiment ${experimentKey}:`, err);
      return false;
    }
  }, [experimentKey]);

  return trackConversion;
}

/**
 * Hook to get experiment results
 * @param experimentKey The experiment key
 * @returns Object with results, isLoading, and error
 */
export function useExperimentResults(experimentKey: string) {
  const [results, setResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const getResults = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiRequest<GetResultsResponse>({
        url: `/api/experiments/${experimentKey}/results`,
        method: 'GET',
      });

      setResults(response);
    } catch (err) {
      console.error(`Error getting results for experiment ${experimentKey}:`, err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsLoading(false);
    }
  }, [experimentKey]);

  useEffect(() => {
    getResults();
  }, [getResults]);

  return { results, isLoading, error, refresh: getResults };
}
