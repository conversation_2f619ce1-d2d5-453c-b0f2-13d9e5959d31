import React from 'react';
import { Metadata } from 'next';
import { PlanSelector } from '@/components/subscription/PlanSelector';
import { UsageDisplay } from '@/components/subscription/UsageDisplay';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export const metadata: Metadata = {
  title: 'Subscription Settings | Aizako CRM',
  description: 'Manage your subscription plan and usage',
};

export default function SubscriptionSettingsPage() {
  return (
    <div className="container mx-auto py-6 space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Subscription Settings</h1>
        <p className="text-muted-foreground">
          Manage your subscription plan and monitor your usage
        </p>
      </div>
      
      <Tabs defaultValue="plans" className="w-full">
        <TabsList>
          <TabsTrigger value="plans">Subscription Plans</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>
        
        <TabsContent value="plans" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Choose a Plan</CardTitle>
              <CardDescription>
                Select the subscription plan that best fits your needs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PlanSelector />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="usage" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Resource Usage</CardTitle>
              <CardDescription>
                Monitor your resource usage across your subscription
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <UsageDisplay 
                  resourceType="users" 
                  displayName="Users" 
                  description="Team members with access to your CRM"
                />
                <UsageDisplay 
                  resourceType="contacts" 
                  displayName="Contacts" 
                  description="Individual contacts in your CRM"
                />
                <UsageDisplay 
                  resourceType="companies" 
                  displayName="Companies" 
                  description="Organizations in your CRM"
                />
                <UsageDisplay 
                  resourceType="opportunities" 
                  displayName="Opportunities" 
                  description="Sales opportunities being tracked"
                />
                <UsageDisplay 
                  resourceType="storage" 
                  displayName="Storage" 
                  description="Total storage used for documents and attachments"
                  unit="MB"
                />
                <UsageDisplay 
                  resourceType="apiRequests" 
                  displayName="API Requests" 
                  description="API requests made in the current period"
                />
              </div>
              
              <Separator className="my-6" />
              
              <div>
                <h3 className="text-lg font-medium mb-4">AI Usage</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <UsageDisplay 
                    resourceType="ai.tokens" 
                    displayName="AI Tokens" 
                    description="AI tokens used for assistant and insights"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="billing" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing Information</CardTitle>
              <CardDescription>
                Manage your billing information and payment methods
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Billing management is not yet available in this version.
                Please contact support for billing inquiries.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
