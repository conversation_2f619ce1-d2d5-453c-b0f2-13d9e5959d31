import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Users, TrendingUp, DollarSign, Brain, Lightbulb, BarChart2 } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import MetricsCard from "@/components/dashboard/MetricsCard";
import RecentActivity from "@/components/dashboard/RecentActivity";
import OpportunityTable from "@/components/dashboard/OpportunityTable";
import AIChatAssistant from "@/components/dashboard/AIChatAssistant";
import RelationshipNetwork from "@/components/dashboard/RelationshipNetwork";
import DashboardCustomizer from "@/components/dashboard/DashboardCustomizer";
import DraggableCard from "@/components/dashboard/DraggableCard";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { WinLossAnalyzerButton } from "@/components/win-loss-analyzer/WinLossAnalyzerButton";

interface DashboardMetrics {
  totalContacts: number;
  totalCompanies: number;
  openOpportunities: number;
  pipelineValue: number;
  aiInsightsGenerated: number;
}

interface DashboardPreferences {
  enabledCards: string[];
  cardOrder: string[];
}

const DEFAULT_CARDS: string[] = ["metrics", "activities", "opportunities", "aiAssistant"];

const Dashboard = () => {
  const [timeRange, setTimeRange] = useState("7days");
  const [draggedCard, setDraggedCard] = useState<string | null>(null);
  const [cardOrder, setCardOrder] = useState<string[]>(DEFAULT_CARDS);
  const [enabledCards, setEnabledCards] = useState<string[]>(DEFAULT_CARDS);

  // Fetch dashboard metrics
  const { data: metrics, isLoading: isLoadingMetrics } = useQuery({
    queryKey: ['/api/dashboard/metrics'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/metrics');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard metrics');
      }
      return response.json() as Promise<DashboardMetrics>;
    }
  });

  // Fetch user preferences
  const { data: preferences } = useQuery({
    queryKey: ['/api/user/preferences'],
    queryFn: async () => {
      const response = await fetch('/api/user/preferences');
      if (!response.ok) {
        throw new Error('Failed to fetch preferences');
      }
      return response.json();
    }
  });

  // Update local state when preferences are loaded
  useEffect(() => {
    if (preferences?.dashboard) {
      const dashPrefs = preferences.dashboard as DashboardPreferences;

      if (dashPrefs.enabledCards && dashPrefs.enabledCards.length > 0) {
        setEnabledCards(dashPrefs.enabledCards);
      }

      if (dashPrefs.cardOrder && dashPrefs.cardOrder.length > 0) {
        setCardOrder(dashPrefs.cardOrder);
      }
    }
  }, [preferences]);

  const formatCurrency = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value}`;
  };

  const handleDragStart = (id: string) => {
    setDraggedCard(id);
  };

  const handleDragEnd = () => {
    setDraggedCard(null);
  };

  const handleDragOver = (_id: string) => {
    // Not needed for current implementation
  };

  const handleDrop = (targetId: string) => {
    if (!draggedCard || draggedCard === targetId) return;

    const newCardOrder = [...cardOrder];
    const draggedIndex = newCardOrder.indexOf(draggedCard);
    const targetIndex = newCardOrder.indexOf(targetId);

    if (draggedIndex !== -1 && targetIndex !== -1) {
      // Remove the dragged card from its original position
      newCardOrder.splice(draggedIndex, 1);
      // Insert it at the new position
      newCardOrder.splice(targetIndex, 0, draggedCard);
      setCardOrder(newCardOrder);

      // Save the new order to backend
      apiRequest('/api/user/preferences', 'POST', {
        preferences: {
          dashboard: {
            enabledCards,
            cardOrder: newCardOrder
          }
        }
      }).catch(error => {
        console.error('Failed to save card order:', error);
      });
    }
  };

  const renderMetricsCard = () => (
    <Card className="mb-6">
      <CardContent className="p-0">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
          {isLoadingMetrics ? (
            <>
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </>
          ) : (
            <>
              <MetricsCard
                title="Total Contacts"
                value={metrics?.totalContacts || 0}
                icon={<Users className="h-6 w-6 text-primary" />}
                change={{
                  value: 12,
                  trend: "up",
                  text: "vs last month"
                }}
                iconBgClassName="bg-blue-100"
              />

              <MetricsCard
                title="Open Opportunities"
                value={metrics?.openOpportunities || 0}
                icon={<TrendingUp className="h-6 w-6 text-secondary" />}
                change={{
                  value: 5,
                  trend: "down",
                  text: "vs last month"
                }}
                iconBgClassName="bg-violet-100"
              />

              <MetricsCard
                title="Pipeline Value"
                value={metrics?.pipelineValue ? formatCurrency(metrics.pipelineValue) : "$0"}
                icon={<DollarSign className="h-6 w-6 text-accent" />}
                change={{
                  value: 18,
                  trend: "up",
                  text: "vs last month"
                }}
                iconBgClassName="bg-emerald-100"
              />

              <MetricsCard
                title="AI Insights Generated"
                value={metrics?.aiInsightsGenerated || 0}
                icon={<Brain className="h-6 w-6 text-purple-600" />}
                change={{
                  value: 32,
                  trend: "up",
                  text: "vs last month"
                }}
                iconBgClassName="bg-purple-100"
              />
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Render card content based on card ID
  const renderCardContent = (cardId: string) => {
    switch (cardId) {
      case 'metrics':
        return renderMetricsCard();
      case 'activities':
        return (
          <DraggableCard
            id="activities"
            title="Recent Activity"
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            isDragging={draggedCard === "activities"}
            className="mb-6"
          >
            <RecentActivity limit={4} />
          </DraggableCard>
        );
      case 'opportunities':
        return (
          <DraggableCard
            id="opportunities"
            title="Open Opportunities"
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            isDragging={draggedCard === "opportunities"}
            className="mb-6"
          >
            <OpportunityTable limit={3} />
          </DraggableCard>
        );
      case 'aiAssistant':
        return (
          <DraggableCard
            id="aiAssistant"
            title="AI Assistant"
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            isDragging={draggedCard === "aiAssistant"}
            className="mb-6"
          >
            <AIChatAssistant />
          </DraggableCard>
        );
      case 'relationships':
        return (
          <DraggableCard
            id="relationships"
            title="Relationship Network"
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            isDragging={draggedCard === "relationships"}
            className="mb-6"
          >
            <RelationshipNetwork />
          </DraggableCard>
        );
      case 'insights':
        return (
          <DraggableCard
            id="insights"
            title="AI Insights"
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            isDragging={draggedCard === "insights"}
            className="mb-6"
          >
            <div className="space-y-4">
              <div className="flex items-start space-x-3 p-3 bg-amber-50 border border-amber-200 rounded-md">
                <Lightbulb className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-amber-800">Potential Lead at Acme Corp</h4>
                  <p className="text-xs text-amber-700 mt-1">
                    Based on recent interactions, John Smith at Acme Corp might be interested in your enterprise solution.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <Lightbulb className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-blue-800">Follow-up Reminder</h4>
                  <p className="text-xs text-blue-700 mt-1">
                    It's been 14 days since your last contact with TechGiant. Consider scheduling a follow-up meeting.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-purple-50 border border-purple-200 rounded-md">
                <Lightbulb className="h-5 w-5 text-purple-500 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-purple-800">Relationship Insight</h4>
                  <p className="text-xs text-purple-700 mt-1">
                    Sarah Johnson and Michael Williams from different companies have a LinkedIn connection. This might help with your current deal.
                  </p>
                </div>
              </div>
            </div>
          </DraggableCard>
        );
      default:
        return null;
    }
  };

  // Render the dynamic grid layout based on number of cards
  const getGridLayout = () => {
    const numCards = enabledCards.length;

    if (numCards <= 1) {
      return "grid-cols-1";
    } else if (numCards === 2) {
      return "grid-cols-1 lg:grid-cols-2";
    } else {
      return "grid-cols-1 lg:grid-cols-3";
    }
  };

  return (
    <div className="p-4 md:p-6 pb-20 md:pb-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-800">Dashboard</h2>
          <div className="flex space-x-2">
            <WinLossAnalyzerButton
              buttonText="Win/Loss Analysis"
              variant="outline"
              size="sm"
            />
            <DashboardCustomizer />
            <select
              className="appearance-none bg-white border border-gray-300 text-gray-700 pl-3 pr-8 py-1.5 rounded-lg text-sm font-medium focus:outline-none focus:ring-primary focus:border-primary"
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <option value="7days">Last 7 days</option>
              <option value="30days">Last 30 days</option>
              <option value="90days">Last 90 days</option>
              <option value="year">This year</option>
            </select>
          </div>
        </div>

        {enabledCards.length === 0 ? (
          <div className="p-12 text-center border rounded-lg bg-gray-50">
            <h3 className="text-lg font-medium text-gray-600 mb-2">No dashboard cards selected</h3>
            <p className="text-gray-500 mb-4">Customize your dashboard by selecting which cards to display</p>
            <DashboardCustomizer />
          </div>
        ) : (
          <div>
            {/* First, render the metrics card if it's enabled, since it's special */}
            {enabledCards.includes('metrics') && renderMetricsCard()}

            {/* Then render the rest of the cards in the specified order */}
            <div className={`grid ${getGridLayout()} gap-6`}>
              {cardOrder
                .filter(id => enabledCards.includes(id) && id !== 'metrics')
                .map(cardId => (
                  <div key={cardId}>
                    {renderCardContent(cardId)}
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
