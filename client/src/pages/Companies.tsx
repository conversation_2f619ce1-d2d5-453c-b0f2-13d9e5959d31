import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building2 } from "lucide-react";
import CompanyTable from "@/components/companies/CompanyTable";
import CompanyForm from "@/components/companies/CompanyForm";

const Companies = () => {
  const [showAddCompany, setShowAddCompany] = useState(false);

  return (
    <div className="p-4 md:p-6 pb-20 md:pb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-gray-800">Companies</h2>
        <Button onClick={() => setShowAddCompany(true)}>
          <Building2 className="h-5 w-5 mr-2" />
          Add Company
        </Button>
      </div>

      <div className="mb-6">
        <CompanyTable />
      </div>

      <CompanyForm 
        open={showAddCompany} 
        onOpenChange={setShowAddCompany} 
      />
    </div>
  );
};

export default Companies;
