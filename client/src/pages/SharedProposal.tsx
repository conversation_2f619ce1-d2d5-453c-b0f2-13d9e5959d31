import { useState, useEffect, useRef } from 'react';
import { useParams } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Download, Clock, ExternalLink, Eye, FileText, FileCode, Sparkles } from 'lucide-react';

interface SharedProposal {
  id: string;
  name: string;
  description?: string;
  documentUrl: string;
  format: string;
  expiresAt: string;
  isDownloadable: boolean;
}

export default function SharedProposal() {
  const { token } = useParams();
  const [proposal, setProposal] = useState<SharedProposal | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewStartTime, setViewStartTime] = useState<number>(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Fetch the proposal data
  useEffect(() => {
    const fetchProposal = async () => {
      try {
        setLoading(true);
        const data = await apiRequest(`/api/public/proposals/${token}`, 'GET');
        setProposal(data as unknown as SharedProposal);
        setViewStartTime(Date.now());
      } catch (error: any) {
        console.error('Error fetching proposal:', error);
        setError(error.message || 'Failed to load proposal. The link may have expired or been removed.');
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      fetchProposal();
    }
  }, [token]);

  // Track view duration
  useEffect(() => {
    if (!proposal || !viewStartTime) return;

    // Track view duration every 30 seconds
    const interval = setInterval(() => {
      const duration = Math.floor((Date.now() - viewStartTime) / 1000);
      trackInteraction('view_duration', {}, duration);
    }, 30000);

    // Track view duration when component unmounts
    return () => {
      clearInterval(interval);
      const duration = Math.floor((Date.now() - viewStartTime) / 1000);
      trackInteraction('view_duration', {}, duration);
    };
  }, [proposal, viewStartTime]);

  // Track interactions
  const trackInteraction = async (type: string, data: Record<string, any> = {}, duration?: number) => {
    if (!token) return;

    try {
      await apiRequest(`/api/public/proposals/${token}/track`, 'POST', { type, data, duration });
    } catch (error) {
      console.error('Error tracking interaction:', error);
    }
  };

  // Handle download
  const handleDownload = () => {
    if (!proposal || !proposal.isDownloadable) return;

    // Track download
    trackInteraction('download', { format: proposal.format });

    // Open the document URL in a new tab
    window.open(`/api/public/proposals/${token}/content`, '_blank');
  };

  // Get format icon
  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return <Download className="h-4 w-4 mr-2" />;
      case 'docx':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'markdown':
        return <FileCode className="h-4 w-4 mr-2" />;
      case 'claude-html':
        return <Sparkles className="h-4 w-4 mr-2" />;
      default:
        return <FileText className="h-4 w-4 mr-2" />;
    }
  };

  // Format expiration date
  const formatExpirationDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Calculate days until expiration
  const getDaysUntilExpiration = (dateString: string) => {
    const expirationDate = new Date(dateString);
    const now = new Date();
    const diffTime = expirationDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
        <Card className="w-full max-w-4xl">
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2 mt-2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[600px] w-full" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-32" />
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
        <Card className="w-full max-w-4xl">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Proposal</CardTitle>
            <CardDescription>
              We couldn't load the requested proposal.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{error}</p>
            <p className="mt-4 text-gray-600">
              The link may have expired or been removed. Please contact the sender for a new link.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!proposal) {
    return null;
  }

  const daysUntilExpiration = getDaysUntilExpiration(proposal.expiresAt);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <Card className="w-full max-w-5xl">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{proposal.name}</CardTitle>
              {proposal.description && (
                <CardDescription className="mt-2">{proposal.description}</CardDescription>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="flex items-center">
                {getFormatIcon(proposal.format)}
                {proposal.format === 'claude-html' ? 'Claude Enhanced' :
                 proposal.format === 'docx' ? 'Word Document' :
                 proposal.format === 'markdown' ? 'Markdown' :
                 'PDF Document'}
              </Badge>
              <Badge
                variant="outline"
                className={daysUntilExpiration <= 3 ? 'bg-red-50 text-red-700 border-red-200' : 'bg-blue-50 text-blue-700 border-blue-200'}
              >
                <Clock className="h-3 w-3 mr-1" />
                Expires in {daysUntilExpiration} days
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="border rounded-md overflow-hidden bg-white">
            <iframe
              ref={iframeRef}
              src={`/api/public/proposals/${token}/content`}
              className="w-full h-[600px]"
              title={proposal.name}
              onLoad={() => trackInteraction('view', { format: proposal.format })}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground flex items-center">
            <Eye className="h-4 w-4 mr-1" />
            <span>Expires on {formatExpirationDate(proposal.expiresAt)}</span>
          </div>
          <div className="flex space-x-2">
            {proposal.isDownloadable ? (
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download {proposal.format.toUpperCase()}
              </Button>
            ) : (
              <Button variant="outline" disabled>
                <ExternalLink className="h-4 w-4 mr-2" />
                Web View Only
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
