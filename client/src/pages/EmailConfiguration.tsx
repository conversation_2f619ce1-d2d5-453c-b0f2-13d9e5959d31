import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Loader2, Mail, Check, AlertCircle, Send, Save, RefreshCw } from 'lucide-react';

interface EmailConfig {
  _id: string;
  provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses' | 'gmail' | 'outlook' | 'other';
  isEnabled: boolean;
  fromName: string;
  fromEmail: string;
  replyToEmail?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
  apiKey?: string;
  apiSecret?: string;
  region?: string;
  templates: {
    proposalShare?: {
      subject: string;
      body: string;
    };
    proposalAccepted?: {
      subject: string;
      body: string;
    };
    proposalRejected?: {
      subject: string;
      body: string;
    };
  };
}

export default function EmailConfiguration() {
  const [activeTab, setActiveTab] = useState<string>('general');
  const [testEmail, setTestEmail] = useState<string>('');
  const [formData, setFormData] = useState<Partial<EmailConfig>>({
    provider: 'smtp',
    isEnabled: false,
    fromName: '',
    fromEmail: '',
    replyToEmail: '',
    smtpHost: '',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    smtpSecure: true,
    apiKey: '',
    apiSecret: '',
    region: 'us-east-1',
    templates: {
      proposalShare: {
        subject: '',
        body: ''
      },
      proposalAccepted: {
        subject: '',
        body: ''
      },
      proposalRejected: {
        subject: '',
        body: ''
      }
    }
  });

  const { toast } = useToast();

  // Fetch email configuration
  const { data: config, isLoading, error, refetch } = useQuery({
    queryKey: ['emailConfig'],
    queryFn: async () => {
      try {
        return await apiRequest('/api/email-config', 'GET');
      } catch (error) {
        // If 404, return null (no config yet)
        if (error instanceof Error && error.message.includes('404')) {
          return null;
        }
        throw error;
      }
    }
  });

  // Update form data when config is loaded
  useEffect(() => {
    if (config) {
      setFormData(config as Partial<EmailConfig>);
    }
  }, [config]);

  // Save email configuration
  const saveConfigMutation = useMutation({
    mutationFn: async (data: Partial<EmailConfig>) => {
      return await apiRequest('/api/email-config', 'POST', data);
    },
    onSuccess: () => {
      toast({
        title: 'Configuration Saved',
        description: 'Your email configuration has been saved successfully.',
      });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: 'Error Saving Configuration',
        description: error.message || 'Failed to save email configuration',
        variant: 'destructive',
      });
    }
  });

  // Test email configuration
  const testConfigMutation = useMutation({
    mutationFn: async (email: string) => {
      return await apiRequest('/api/email-config/test', 'POST', { testEmail: email });
    },
    onSuccess: () => {
      toast({
        title: 'Test Email Sent',
        description: `A test email has been sent to ${testEmail}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error Sending Test Email',
        description: error.message || 'Failed to send test email',
        variant: 'destructive',
      });
    }
  });

  // Handle form changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = { ...prev } as Record<string, any>;

      // Handle nested fields
      if (field.includes('.')) {
        const [parent, child, grandchild] = field.split('.');
        if (grandchild) {
          newData[parent] = {
            ...newData[parent],
            [child]: {
              ...(newData[parent] as Record<string, any>)?.[child],
              [grandchild]: value
            }
          };
        } else {
          newData[parent] = {
            ...newData[parent],
            [child]: value
          };
        }
      } else {
        newData[field] = value;
      }

      return newData as Partial<EmailConfig>;
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveConfigMutation.mutate(formData);
  };

  // Handle test email
  const handleTestEmail = (e: React.FormEvent) => {
    e.preventDefault();
    if (!testEmail) {
      toast({
        title: 'Email Required',
        description: 'Please enter an email address to send the test to',
        variant: 'destructive',
      });
      return;
    }
    testConfigMutation.mutate(testEmail);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-96 mt-2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-600 flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              Error Loading Email Configuration
            </CardTitle>
            <CardDescription>
              We encountered an error while loading your email configuration.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{(error as Error).message}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => refetch()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Email Configuration</h1>
          <p className="text-muted-foreground">Configure your email settings for sending proposals and notifications</p>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            checked={formData.isEnabled || false}
            onCheckedChange={(checked) => handleChange('isEnabled', checked)}
            id="email-enabled"
          />
          <Label htmlFor="email-enabled">
            {formData.isEnabled ? (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <Check className="h-3 w-3 mr-1" />
                Enabled
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                Disabled
              </Badge>
            )}
          </Label>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="general">General Settings</TabsTrigger>
          <TabsTrigger value="smtp">SMTP Configuration</TabsTrigger>
          <TabsTrigger value="api">API Keys</TabsTrigger>
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
          <TabsTrigger value="test">Test & Verify</TabsTrigger>
        </TabsList>

        <form onSubmit={handleSubmit}>
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Email Settings</CardTitle>
                <CardDescription>
                  Configure the basic settings for your email communications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="provider">Email Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => handleChange('provider', value)}
                  >
                    <SelectTrigger id="provider">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="smtp">SMTP Server</SelectItem>
                      <SelectItem value="gmail">Gmail</SelectItem>
                      <SelectItem value="outlook">Outlook</SelectItem>
                      <SelectItem value="sendgrid">SendGrid</SelectItem>
                      <SelectItem value="mailgun">Mailgun</SelectItem>
                      <SelectItem value="ses">Amazon SES</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fromName">From Name</Label>
                  <Input
                    id="fromName"
                    value={formData.fromName || ''}
                    onChange={(e) => handleChange('fromName', e.target.value)}
                    placeholder="Your Name or Company Name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fromEmail">From Email</Label>
                  <Input
                    id="fromEmail"
                    type="email"
                    value={formData.fromEmail || ''}
                    onChange={(e) => handleChange('fromEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="replyToEmail">Reply-To Email (Optional)</Label>
                  <Input
                    id="replyToEmail"
                    type="email"
                    value={formData.replyToEmail || ''}
                    onChange={(e) => handleChange('replyToEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={saveConfigMutation.isPending}>
                  {saveConfigMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="smtp">
            <Card>
              <CardHeader>
                <CardTitle>SMTP Configuration</CardTitle>
                <CardDescription>
                  Configure your SMTP server settings for sending emails
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="smtpHost">SMTP Host</Label>
                  <Input
                    id="smtpHost"
                    value={formData.smtpHost || ''}
                    onChange={(e) => handleChange('smtpHost', e.target.value)}
                    placeholder="smtp.example.com"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smtpPort">SMTP Port</Label>
                  <Input
                    id="smtpPort"
                    type="number"
                    value={formData.smtpPort || 587}
                    onChange={(e) => handleChange('smtpPort', parseInt(e.target.value))}
                    placeholder="587"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smtpUsername">SMTP Username</Label>
                  <Input
                    id="smtpUsername"
                    value={formData.smtpUsername || ''}
                    onChange={(e) => handleChange('smtpUsername', e.target.value)}
                    placeholder="username"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smtpPassword">SMTP Password</Label>
                  <Input
                    id="smtpPassword"
                    type="password"
                    value={formData.smtpPassword || ''}
                    onChange={(e) => handleChange('smtpPassword', e.target.value)}
                    placeholder="••••••••"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.smtpSecure || false}
                    onCheckedChange={(checked) => handleChange('smtpSecure', checked)}
                    id="smtpSecure"
                  />
                  <Label htmlFor="smtpSecure">Use Secure Connection (TLS/SSL)</Label>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={saveConfigMutation.isPending}>
                  {saveConfigMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="api">
            <Card>
              <CardHeader>
                <CardTitle>API Keys</CardTitle>
                <CardDescription>
                  Configure API keys for email service providers like SendGrid, Mailgun, or Amazon SES
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    value={formData.apiKey || ''}
                    onChange={(e) => handleChange('apiKey', e.target.value)}
                    placeholder="Your API Key"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="apiSecret">API Secret (if required)</Label>
                  <Input
                    id="apiSecret"
                    type="password"
                    value={formData.apiSecret || ''}
                    onChange={(e) => handleChange('apiSecret', e.target.value)}
                    placeholder="Your API Secret"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="region">Region (for AWS SES)</Label>
                  <Select
                    value={formData.region || 'us-east-1'}
                    onValueChange={(value) => handleChange('region', value)}
                  >
                    <SelectTrigger id="region">
                      <SelectValue placeholder="Select region" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="us-east-1">US East (N. Virginia)</SelectItem>
                      <SelectItem value="us-east-2">US East (Ohio)</SelectItem>
                      <SelectItem value="us-west-1">US West (N. California)</SelectItem>
                      <SelectItem value="us-west-2">US West (Oregon)</SelectItem>
                      <SelectItem value="eu-west-1">EU (Ireland)</SelectItem>
                      <SelectItem value="eu-central-1">EU (Frankfurt)</SelectItem>
                      <SelectItem value="ap-northeast-1">Asia Pacific (Tokyo)</SelectItem>
                      <SelectItem value="ap-southeast-1">Asia Pacific (Singapore)</SelectItem>
                      <SelectItem value="ap-southeast-2">Asia Pacific (Sydney)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={saveConfigMutation.isPending}>
                  {saveConfigMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="templates">
            <Card>
              <CardHeader>
                <CardTitle>Email Templates</CardTitle>
                <CardDescription>
                  Customize the email templates used for proposal sharing and notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Proposal Share Template</h3>

                  <div className="space-y-2">
                    <Label htmlFor="proposalShareSubject">Subject Line</Label>
                    <Input
                      id="proposalShareSubject"
                      value={formData.templates?.proposalShare?.subject || ''}
                      onChange={(e) => handleChange('templates.proposalShare.subject', e.target.value)}
                      placeholder="{{senderName}} has shared a proposal with you"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="proposalShareBody">Email Body</Label>
                    <Textarea
                      id="proposalShareBody"
                      value={formData.templates?.proposalShare?.body || ''}
                      onChange={(e) => handleChange('templates.proposalShare.body', e.target.value)}
                      placeholder="Email body template"
                      rows={6}
                    />
                    <p className="text-sm text-muted-foreground">
                      Available variables: {`{{senderName}}, {{recipientName}}, {{proposalName}},
                      {{proposalUrl}}, {{expirationDate}}, {{message}}, {{companyName}}`}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Proposal Accepted Template</h3>

                  <div className="space-y-2">
                    <Label htmlFor="proposalAcceptedSubject">Subject Line</Label>
                    <Input
                      id="proposalAcceptedSubject"
                      value={formData.templates?.proposalAccepted?.subject || ''}
                      onChange={(e) => handleChange('templates.proposalAccepted.subject', e.target.value)}
                      placeholder="Proposal Accepted: {{proposalName}}"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="proposalAcceptedBody">Email Body</Label>
                    <Textarea
                      id="proposalAcceptedBody"
                      value={formData.templates?.proposalAccepted?.body || ''}
                      onChange={(e) => handleChange('templates.proposalAccepted.body', e.target.value)}
                      placeholder="Email body template"
                      rows={6}
                    />
                    <p className="text-sm text-muted-foreground">
                      Available variables: {`{{recipientName}}, {{proposalName}}, {{senderName}},
                      {{companyName}}, {{value}}`}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Proposal Rejected Template</h3>

                  <div className="space-y-2">
                    <Label htmlFor="proposalRejectedSubject">Subject Line</Label>
                    <Input
                      id="proposalRejectedSubject"
                      value={formData.templates?.proposalRejected?.subject || ''}
                      onChange={(e) => handleChange('templates.proposalRejected.subject', e.target.value)}
                      placeholder="Proposal Status Update: {{proposalName}}"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="proposalRejectedBody">Email Body</Label>
                    <Textarea
                      id="proposalRejectedBody"
                      value={formData.templates?.proposalRejected?.body || ''}
                      onChange={(e) => handleChange('templates.proposalRejected.body', e.target.value)}
                      placeholder="Email body template"
                      rows={6}
                    />
                    <p className="text-sm text-muted-foreground">
                      Available variables: {`{{recipientName}}, {{proposalName}}, {{rejectionReason}},
                      {{senderName}}, {{companyName}}`}
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={saveConfigMutation.isPending}>
                  {saveConfigMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Templates
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="test">
            <Card>
              <CardHeader>
                <CardTitle>Test Email Configuration</CardTitle>
                <CardDescription>
                  Send a test email to verify your configuration is working correctly
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="testEmail">Test Email Address</Label>
                  <Input
                    id="testEmail"
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    placeholder="Enter email address to send test to"
                  />
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <h3 className="text-sm font-medium text-yellow-800 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Important Note
                  </h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    Make sure to save your configuration before testing. The test will use your current saved settings.
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  onClick={handleTestEmail}
                  disabled={testConfigMutation.isPending || !formData.isEnabled}
                >
                  {testConfigMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send Test Email
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </form>
      </Tabs>
    </div>
  );
}
