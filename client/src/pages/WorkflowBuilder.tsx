import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Workflow, WorkflowNode, WorkflowEdge, parseWorkflow, compileWorkflow, createWorkflow } from '@/api';
import WorkflowCanvas from '@/components/workflow/WorkflowCanvas';
import WorkflowPrompt from '@/components/workflow/WorkflowPrompt';

export default function WorkflowBuilder() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('prompt');
  const [isLoading, setIsLoading] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [dslYaml, setDslYaml] = useState('');
  const [nodes, setNodes] = useState<WorkflowNode[]>([]);
  const [edges, setEdges] = useState<WorkflowEdge[]>([]);
  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);
  const [createdWorkflow, setCreatedWorkflow] = useState<Workflow | null>(null);

  // Parse prompt to DSL
  const handleParsePrompt = async () => {
    if (!prompt.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a workflow description',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    setErrors([]);
    setWarnings([]);

    try {
      const result = await parseWorkflow({ prompt });

      if (result.success) {
        setDslYaml(result.dsl_yaml || '');
        setNodes(result.nodes || []);
        setEdges(result.edges || []);
        setActiveTab('canvas');

        // Extract name from DSL if available
        try {
          const dslObj = JSON.parse(result.dsl_yaml || '{}');
          if (dslObj.workflow && dslObj.workflow.name) {
            setWorkflowName(dslObj.workflow.name);
          }
          if (dslObj.workflow && dslObj.workflow.description) {
            setWorkflowDescription(dslObj.workflow.description);
          }
        } catch (e) {
          // Ignore parsing errors
        }

        toast({
          title: 'Success',
          description: 'Workflow created from your description',
        });
      } else {
        setErrors([result.error || 'Unknown error']);
        toast({
          title: 'Error',
          description: result.error || 'Failed to parse workflow description',
          variant: 'destructive',
        });
      }
    } catch (error) {
      setErrors([(error as Error).message || 'Unknown error']);
      toast({
        title: 'Error',
        description: 'Failed to parse workflow description',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Compile DSL to graph
  const handleCompileDsl = async () => {
    if (!dslYaml.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter DSL YAML',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    setErrors([]);
    setWarnings([]);

    try {
      const result = await compileWorkflow({ dsl_yaml: dslYaml });

      setNodes(result.nodes || []);
      setEdges(result.edges || []);
      setErrors(result.errors || []);
      setWarnings(result.warnings || []);

      if (result.errors.length === 0) {
        setActiveTab('canvas');
        toast({
          title: 'Success',
          description: 'Workflow compiled successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Compilation failed with errors',
          variant: 'destructive',
        });
      }
    } catch (error) {
      setErrors([(error as Error).message || 'Unknown error']);
      toast({
        title: 'Error',
        description: 'Failed to compile workflow',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Save workflow
  const handleSaveWorkflow = async () => {
    if (!workflowName.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a workflow name',
        variant: 'destructive',
      });
      return;
    }

    if (!dslYaml.trim()) {
      toast({
        title: 'Error',
        description: 'Please create a workflow first',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const workflow = await createWorkflow({
        name: workflowName,
        description: workflowDescription,
        dsl_yaml: dslYaml,
        original_prompt: prompt,
      });

      setCreatedWorkflow(workflow);
      toast({
        title: 'Success',
        description: 'Workflow saved successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save workflow',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Visual NL Workflow Builder</h1>
          <p className="text-muted-foreground">
            Create workflows using natural language or visual editor
          </p>
        </div>
        <Button
          onClick={handleSaveWorkflow}
          disabled={isLoading || !dslYaml.trim() || !workflowName.trim()}
        >
          {isLoading ? 'Saving...' : 'Save Workflow'}
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Workflow Details</CardTitle>
            <CardDescription>Enter basic information about your workflow</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Name
                </label>
                <Input
                  id="name"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                  placeholder="Enter workflow name"
                />
              </div>
              <div>
                <label htmlFor="description" className="block text-sm font-medium mb-1">
                  Description
                </label>
                <Textarea
                  id="description"
                  value={workflowDescription}
                  onChange={(e) => setWorkflowDescription(e.target.value)}
                  placeholder="Enter workflow description"
                  rows={2}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="prompt">Natural Language</TabsTrigger>
          <TabsTrigger value="dsl">DSL Editor</TabsTrigger>
          <TabsTrigger value="canvas">Visual Canvas</TabsTrigger>
        </TabsList>

        <TabsContent value="prompt">
          <Card>
            <CardHeader>
              <CardTitle>Describe Your Workflow</CardTitle>
              <CardDescription>
                Describe what you want your workflow to do in plain English
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WorkflowPrompt
                value={prompt}
                onChange={setPrompt}
                onSubmit={handleParsePrompt}
                isLoading={isLoading}
              />
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">
                Example: "When a lead's score reaches 80 and there's no reply in 2 days, send a 'Nudge' email and assign to an SDR"
              </p>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="dsl">
          <Card>
            <CardHeader>
              <CardTitle>DSL Editor</CardTitle>
              <CardDescription>
                Edit the workflow DSL directly (YAML format)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={dslYaml}
                onChange={(e) => setDslYaml(e.target.value)}
                placeholder="Enter workflow DSL in YAML format"
                rows={15}
                className="font-mono"
              />
              {errors.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-destructive mb-2">Errors:</h3>
                  <ul className="list-disc pl-5 text-sm text-destructive">
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
              {warnings.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-yellow-500 mb-2">Warnings:</h3>
                  <ul className="list-disc pl-5 text-sm text-yellow-500">
                    {warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={handleCompileDsl} disabled={isLoading || !dslYaml.trim()}>
                {isLoading ? 'Compiling...' : 'Compile DSL'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="canvas">
          <Card>
            <CardHeader>
              <CardTitle>Visual Canvas</CardTitle>
              <CardDescription>
                Visualize and edit your workflow
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[500px] flex items-center justify-center">
                  <Skeleton className="h-[500px] w-full" />
                </div>
              ) : (
                <WorkflowCanvas
                  nodes={nodes}
                  edges={edges}
                  onNodesChange={(newNodes) => setNodes(newNodes)}
                  onEdgesChange={(newEdges) => setEdges(newEdges)}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {createdWorkflow && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Workflow Created</CardTitle>
            <CardDescription>
              Your workflow has been saved successfully
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium">Status:</span>
              <Badge variant="outline">{createdWorkflow.status}</Badge>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium">Version:</span>
              <span>{createdWorkflow.version}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">ID:</span>
              <code className="bg-muted px-2 py-1 rounded text-sm">
                {createdWorkflow._id}
              </code>
            </div>
          </CardContent>
          <CardFooter>
            <p className="text-sm text-muted-foreground">
              You can now manage this workflow from the Workflows page
            </p>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}
