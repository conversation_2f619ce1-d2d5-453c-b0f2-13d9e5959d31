import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { Opportunity } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import OpportunityForm from "@/components/opportunities/OpportunityForm";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MoreH<PERSON>zon<PERSON>, <PERSON>cil, Trash2, <PERSON>Link } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";

const Opportunities = () => {
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] = useState<Opportunity | null>(null);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: opportunities, isLoading, error } = useQuery({
    queryKey: ['/api/opportunities'],
    queryFn: async () => {
      const response = await fetch('/api/opportunities');
      if (!response.ok) {
        throw new Error('Failed to fetch opportunities');
      }
      return response.json() as Promise<Opportunity[]>;
    }
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/opportunities/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/opportunities'] });
      toast({
        title: "Success",
        description: "Opportunity deleted successfully",
      });
      setIsDeleteDialogOpen(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete opportunity",
        variant: "destructive",
      });
    },
  });

  const handleEditClick = (opportunity: Opportunity) => {
    setSelectedOpportunity(opportunity);
    setIsEditOpen(true);
  };

  const handleDeleteClick = (opportunity: Opportunity) => {
    setSelectedOpportunity(opportunity);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (selectedOpportunity) {
      deleteMutation.mutate(selectedOpportunity.id);
    }
  };

  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined) return "N/A";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  };

  const getStageBadgeColor = (stage: string) => {
    switch (stage) {
      case 'discovery':
        return "bg-blue-100 text-blue-800";
      case 'qualified':
        return "bg-indigo-100 text-indigo-800";
      case 'proposal':
        return "bg-yellow-100 text-yellow-800";
      case 'negotiation':
        return "bg-green-100 text-green-800";
      case 'closed_won':
        return "bg-emerald-100 text-emerald-800";
      case 'closed_lost':
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatStageLabel = (stage: string) => {
    return stage.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatDate = (dateString: string | Date | null | undefined) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="p-4 md:p-6 pb-20 md:pb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-gray-800">Opportunities</h2>
        <Button onClick={() => setIsAddOpen(true)}>
          <PlusCircle className="h-5 w-5 mr-2" />
          Add Opportunity
        </Button>
      </div>

      <div className="bg-white rounded-xl shadow overflow-hidden">
        {isLoading ? (
          <div className="p-4">
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
          </div>
        ) : error ? (
          <div className="p-4 text-red-500">
            Error loading opportunities: {error instanceof Error ? error.message : 'Unknown error'}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Stage</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Probability</TableHead>
                <TableHead>Close Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {opportunities && opportunities.length > 0 ? (
                opportunities.map((opportunity) => (
                  <TableRow key={opportunity.id}>
                    <TableCell className="font-medium">
                      <Link href={`/opportunities/${opportunity.id}`} className="hover:underline text-blue-600 flex items-center">
                        {opportunity.name}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStageBadgeColor(opportunity.stage || 'new')}>
                        {formatStageLabel(opportunity.stage || 'new')}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatCurrency(opportunity.value)}</TableCell>
                    <TableCell>{opportunity.probability || 0}%</TableCell>
                    <TableCell>{formatDate(opportunity.closeDate)}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/opportunities/${opportunity.id}`} className="flex items-center">
                              <ExternalLink className="h-4 w-4 mr-2" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditClick(opportunity)}>
                            <Pencil className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteClick(opportunity)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4">
                    No opportunities found. Add your first opportunity to get started.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </div>

      {/* Add Opportunity Modal */}
      <OpportunityForm
        open={isAddOpen}
        onOpenChange={setIsAddOpen}
      />

      {/* Edit Opportunity Modal */}
      {selectedOpportunity && (
        <OpportunityForm
          open={isEditOpen}
          onOpenChange={setIsEditOpen}
          initialData={selectedOpportunity}
          isEditing
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the opportunity and cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Opportunities;
