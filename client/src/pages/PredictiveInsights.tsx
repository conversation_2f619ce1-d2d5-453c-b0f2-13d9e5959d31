import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ThumbsUp, ThumbsDown, RefreshCw, Clock, Search } from 'lucide-react';
import { Insight, generateInsight, getCachedInsights, submitFeedback } from '@/api';
import InsightQuery from '@/components/insights/InsightQuery';
import InsightChart from '@/components/insights/InsightChart';
import InsightNarrative from '@/components/insights/InsightNarrative';

export default function PredictiveInsights() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('query');
  const [isLoading, setIsLoading] = useState(false);
  const [query, setQuery] = useState('');
  const [currentInsight, setCurrentInsight] = useState<Insight | null>(null);
  const [cachedInsights, setCachedInsights] = useState<Insight[]>([]);
  const [totalInsights, setTotalInsights] = useState(0);
  const [isLoadingCached, setIsLoadingCached] = useState(false);

  // Load cached insights on mount
  useEffect(() => {
    loadCachedInsights();
  }, []);

  // Load cached insights
  const loadCachedInsights = async () => {
    setIsLoadingCached(true);
    try {
      const result = await getCachedInsights({ limit: 10 });
      setCachedInsights(result.insights);
      setTotalInsights(result.total);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load cached insights',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingCached(false);
    }
  };

  // Generate insight from query
  const handleGenerateInsight = async (refresh = false) => {
    if (!query.trim() && !currentInsight) {
      toast({
        title: 'Error',
        description: 'Please enter a question',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await generateInsight({
        question: query || currentInsight?.question || '',
        refresh,
      });

      if (result.success && result.insight) {
        setCurrentInsight(result.insight);
        setActiveTab('result');

        // If this is a new insight, add it to the cached list
        if (result.source !== 'cache') {
          setCachedInsights((prev) => [result.insight!, ...prev]);
          setTotalInsights((prev) => prev + 1);
        }

        toast({
          title: 'Success',
          description: result.source === 'cache'
            ? 'Loaded insight from cache'
            : 'Generated new insight',
        });
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to generate insight',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate insight',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Submit feedback for an insight
  const handleSubmitFeedback = async (helpful: boolean, comment?: string) => {
    if (!currentInsight) return;

    try {
      await submitFeedback(currentInsight._id, { helpful, comment });

      toast({
        title: 'Thank you',
        description: 'Your feedback has been submitted',
      });

      // Update the current insight with the feedback
      setCurrentInsight((prev) => {
        if (!prev) return null;

        const feedback = prev.feedback || [];
        return {
          ...prev,
          feedback: [
            ...feedback,
            {
              helpful,
              comment,
              submitted_by: 'current_user', // This will be replaced by the server
              submitted_at: new Date().toISOString(),
            },
          ],
        };
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit feedback',
        variant: 'destructive',
      });
    }
  };

  // Load a cached insight
  const handleLoadCachedInsight = (insight: Insight) => {
    setCurrentInsight(insight);
    setQuery(insight.question);
    setActiveTab('result');
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Predictive Insights Hub</h1>
          <p className="text-muted-foreground">
            Ask questions about your business data and get AI-powered insights
          </p>
        </div>
        {currentInsight && (
          <Button
            variant="outline"
            onClick={() => handleGenerateInsight(true)}
            disabled={isLoading}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            {isLoading ? 'Refreshing...' : 'Refresh Insight'}
          </Button>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="query">Ask a Question</TabsTrigger>
          <TabsTrigger value="result">Insight</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="query">
          <Card>
            <CardHeader>
              <CardTitle>Ask a Business Question</CardTitle>
              <CardDescription>
                Ask any question about your business data to get AI-powered insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <InsightQuery
                value={query}
                onChange={setQuery}
                onSubmit={handleGenerateInsight}
                isLoading={isLoading}
              />
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">
                Example: "Why is Q2 pipeline soft?" or "Which deals are most likely to close this month?"
              </p>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="result">
          {isLoading ? (
            <Card>
              <CardHeader>
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-1/2 mt-2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full mb-6" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ) : currentInsight ? (
            <>
              <Card className="mb-6">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{currentInsight.question}</CardTitle>
                      <CardDescription>
                        Generated {formatDate(currentInsight.created_at)}
                        {currentInsight.refresh_count > 0 &&
                          ` • Refreshed ${currentInsight.refresh_count} times`}
                      </CardDescription>
                    </div>
                    <Badge variant="outline" className="ml-2">
                      <Clock className="mr-1 h-3 w-3" />
                      Expires {formatDate(currentInsight.expires_at)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <InsightChart chartSpec={currentInsight.chart_spec} />
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle>Narrative</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <InsightNarrative narrative={currentInsight.narrative} />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Why It Matters</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="list-disc pl-5 space-y-2">
                      {currentInsight.why_it_matters.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {currentInsight.recommended_plays.length > 0 && (
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Recommended Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {currentInsight.recommended_plays.map((play, index) => (
                        <Card key={index}>
                          <CardHeader>
                            <div className="flex justify-between items-center">
                              <CardTitle className="text-lg">{play.title}</CardTitle>
                              <Badge variant={
                                play.impact === 'high' ? 'destructive' :
                                play.impact === 'medium' ? 'default' : 'outline'
                              }>
                                {play.impact} impact
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p>{play.description}</p>
                          </CardContent>
                          <CardFooter>
                            {play.action_url && (
                              <Button variant="outline" size="sm" asChild>
                                <a href={play.action_url} target="_blank" rel="noopener noreferrer">
                                  Take Action
                                </a>
                              </Button>
                            )}
                            {play.workflow_template && (
                              <Button variant="outline" size="sm">
                                Create Workflow
                              </Button>
                            )}
                            {play.module_integration && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Handle module integration
                                  // Access module_integration properties safely
                                  const module = play.module_integration?.module;
                                  const action = play.module_integration?.action;
                                  toast({
                                    title: `Integration with ${module}`,
                                    description: `Action: ${action}`,
                                  });

                                  // Navigate to the appropriate module based on the integration
                                  switch (module) {
                                    case 'proposal_generator':
                                      window.location.href = '/proposal-generator';
                                      break;
                                    case 'objection_handler':
                                      window.location.href = '/objection-handler';
                                      break;
                                    case 'follow_up_coach':
                                      window.location.href = '/follow-up-coach';
                                      break;
                                    case 'win_loss_analyzer':
                                      window.location.href = '/win-loss-analyzer';
                                      break;
                                    case 'meeting_prep':
                                      window.location.href = '/meeting-prep';
                                      break;
                                    case 'workflow':
                                      window.location.href = '/workflow-builder';
                                      break;
                                    default:
                                      break;
                                  }
                                }}
                              >
                                Open in {play.module_integration.module.replace('_', ' ')}
                              </Button>
                            )}
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Was this insight helpful?</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      onClick={() => handleSubmitFeedback(true)}
                      disabled={currentInsight.feedback?.some(f => f.helpful)}
                    >
                      <ThumbsUp className="mr-2 h-4 w-4" />
                      Yes, it was helpful
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleSubmitFeedback(false)}
                      disabled={currentInsight.feedback?.some(f => !f.helpful)}
                    >
                      <ThumbsDown className="mr-2 h-4 w-4" />
                      No, it wasn't helpful
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>No Insight Generated</CardTitle>
                <CardDescription>
                  Ask a question to generate an insight
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center py-12">
                  <Search className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-center text-muted-foreground">
                    Go to the "Ask a Question" tab to generate an insight
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Insight History</CardTitle>
              <CardDescription>
                Previously generated insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingCached ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <Skeleton className="h-5 w-3/4" />
                        <Skeleton className="h-4 w-1/2 mt-2" />
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              ) : cachedInsights.length > 0 ? (
                <div className="space-y-4">
                  {cachedInsights.map((insight) => (
                    <Card key={insight._id} className="cursor-pointer hover:bg-muted/50 transition-colors"
                      onClick={() => handleLoadCachedInsight(insight)}>
                      <CardHeader>
                        <CardTitle className="text-lg">{insight.question}</CardTitle>
                        <CardDescription>
                          Generated {formatDate(insight.created_at)}
                          {insight.refresh_count > 0 &&
                            ` • Refreshed ${insight.refresh_count} times`}
                        </CardDescription>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <p className="text-center text-muted-foreground">
                    No insights have been generated yet
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">
                Showing {cachedInsights.length} of {totalInsights} insights
              </p>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
