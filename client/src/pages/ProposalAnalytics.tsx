import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { Link } from 'wouter';
import {
  <PERSON><PERSON><PERSON><PERSON>cle,
  BarC<PERSON>2,
  <PERSON>,
  Download,
  Eye,
  FileT<PERSON>t,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hart<PERSON><PERSON>,
  RefreshCw,
  Share2,
  TrendingUp,
  Users
} from 'lucide-react';

interface ProposalSummary {
  id: string;
  name: string;
  status: string;
  createdAt: string;
  sentAt?: string;
  viewedAt?: string;
  acceptedAt?: string;
  rejectedAt?: string;
  views: number;
  uniqueViews: number;
  downloads: number;
  shares: number;
}

interface AnalyticsSummary {
  totalProposals: number;
  totalViews: number;
  uniqueViews: number;
  totalDownloads: number;
  totalShares: number;
  averageViewDuration: number;
  conversionRate: number;
  proposals: ProposalSummary[];
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];
const STATUS_COLORS = {
  draft: 'bg-gray-100 text-gray-800',
  sent: 'bg-blue-100 text-blue-800',
  viewed: 'bg-yellow-100 text-yellow-800',
  accepted: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800'
};

export default function ProposalAnalytics() {
  const [activeTab, setActiveTab] = useState<string>('overview');
  const { toast } = useToast();

  // Fetch analytics summary
  const { data, isLoading, error, refetch } = useQuery<AnalyticsSummary>({
    queryKey: ['proposalAnalytics'],
    queryFn: async () => {
      const response = await apiRequest('/api/proposals/analytics/summary', 'GET');
      return response as unknown as AnalyticsSummary;
    }
  });

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Format duration in seconds to minutes and seconds
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Prepare data for status distribution chart
  const getStatusDistributionData = () => {
    if (!data?.proposals) return [];

    const statusCounts = {
      draft: 0,
      sent: 0,
      viewed: 0,
      accepted: 0,
      rejected: 0
    };

    data.proposals.forEach(proposal => {
      statusCounts[proposal.status as keyof typeof statusCounts]++;
    });

    return Object.entries(statusCounts).map(([status, count]) => ({
      name: status.charAt(0).toUpperCase() + status.slice(1),
      value: count
    }));
  };

  // Prepare data for views and downloads chart
  const getViewsAndDownloadsData = () => {
    if (!data?.proposals) return [];

    return data.proposals
      .filter(p => p.views > 0 || p.downloads > 0)
      .slice(0, 10)
      .map(proposal => ({
        name: proposal.name.length > 20 ? proposal.name.substring(0, 20) + '...' : proposal.name,
        views: proposal.views,
        uniqueViews: proposal.uniqueViews,
        downloads: proposal.downloads
      }));
  };

  // Prepare data for conversion funnel
  const getConversionFunnelData = () => {
    if (!data) return [];

    const created = data.totalProposals;
    const sent = data.proposals.filter(p => p.sentAt).length;
    const viewed = data.proposals.filter(p => p.viewedAt).length;
    const accepted = data.proposals.filter(p => p.acceptedAt).length;

    return [
      { name: 'Created', value: created },
      { name: 'Sent', value: sent },
      { name: 'Viewed', value: viewed },
      { name: 'Accepted', value: accepted }
    ];
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {[1, 2, 3, 4].map(i => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-96 mt-2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-600 flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              Error Loading Analytics
            </CardTitle>
            <CardDescription>
              We encountered an error while loading your proposal analytics.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{(error as Error).message}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => refetch()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Proposal Analytics</h1>
          <p className="text-muted-foreground">Track and analyze your proposal performance</p>
        </div>
        <Button variant="outline" onClick={() => refetch()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              Total Proposals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalProposals}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center">
              <Eye className="h-4 w-4 mr-2" />
              Total Views
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalViews}</div>
            <div className="text-xs text-muted-foreground">{data.uniqueViews} unique views</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center">
              <Download className="h-4 w-4 mr-2" />
              Total Downloads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalDownloads}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Conversion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.conversionRate.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">Viewed to Accepted</div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="proposals">Proposals</TabsTrigger>
          <TabsTrigger value="charts">Charts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChartIcon className="h-5 w-5 mr-2" />
                  Proposal Status Distribution
                </CardTitle>
                <CardDescription>
                  Breakdown of proposals by current status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={getStatusDistributionData()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {getStatusDistributionData().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart2 className="h-5 w-5 mr-2" />
                  Conversion Funnel
                </CardTitle>
                <CardDescription>
                  Proposal journey from creation to acceptance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getConversionFunnelData()}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" />
                      <Tooltip />
                      <Bar dataKey="value" fill="#3182CE" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Eye className="h-5 w-5 mr-2" />
                  Top Proposals by Views and Downloads
                </CardTitle>
                <CardDescription>
                  Most viewed and downloaded proposals
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getViewsAndDownloadsData()}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 60,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="name"
                        angle={-45}
                        textAnchor="end"
                        height={70}
                      />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="views" name="Total Views" fill="#3182CE" />
                      <Bar dataKey="uniqueViews" name="Unique Views" fill="#63B3ED" />
                      <Bar dataKey="downloads" name="Downloads" fill="#38A169" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="proposals">
          <Card>
            <CardHeader>
              <CardTitle>Proposal Performance</CardTitle>
              <CardDescription>
                Detailed view of all your proposals and their performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableCaption>A list of your proposals and their performance metrics.</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Proposal Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Last Activity</TableHead>
                    <TableHead className="text-right">Views</TableHead>
                    <TableHead className="text-right">Downloads</TableHead>
                    <TableHead className="text-right">Shares</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.proposals.map((proposal) => (
                    <TableRow key={proposal.id}>
                      <TableCell className="font-medium">{proposal.name}</TableCell>
                      <TableCell>
                        <Badge className={STATUS_COLORS[proposal.status as keyof typeof STATUS_COLORS]}>
                          {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(proposal.createdAt)}</TableCell>
                      <TableCell>
                        {proposal.acceptedAt ? `Accepted on ${formatDate(proposal.acceptedAt)}` :
                         proposal.rejectedAt ? `Rejected on ${formatDate(proposal.rejectedAt)}` :
                         proposal.viewedAt ? `Viewed on ${formatDate(proposal.viewedAt)}` :
                         proposal.sentAt ? `Sent on ${formatDate(proposal.sentAt)}` :
                         'Draft'}
                      </TableCell>
                      <TableCell className="text-right">{proposal.views}</TableCell>
                      <TableCell className="text-right">{proposal.downloads}</TableCell>
                      <TableCell className="text-right">{proposal.shares}</TableCell>
                      <TableCell className="text-right">
                        <Link href={`/proposals/${proposal.id}/analytics`}>
                          <Button variant="outline" size="sm">
                            <BarChart2 className="h-4 w-4" />
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))}
                  {data.proposals.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-4">
                        No proposals found. Create your first proposal to see analytics.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="charts">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Engagement Metrics
                </CardTitle>
                <CardDescription>
                  Views, downloads, and shares over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={data.proposals.map((p, index) => ({
                        name: p.name,
                        views: p.views,
                        downloads: p.downloads,
                        shares: p.shares,
                        index
                      }))}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 10,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="index" />
                      <YAxis />
                      <Tooltip
                        formatter={(value, name, props) => [value, name]}
                        labelFormatter={(value) => data.proposals[value as number]?.name || ''}
                      />
                      <Legend />
                      <Line type="monotone" dataKey="views" name="Views" stroke="#3182CE" activeDot={{ r: 8 }} />
                      <Line type="monotone" dataKey="downloads" name="Downloads" stroke="#38A169" />
                      <Line type="monotone" dataKey="shares" name="Shares" stroke="#DD6B20" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Average View Duration
                </CardTitle>
                <CardDescription>
                  How long viewers spend looking at your proposals
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="text-5xl font-bold text-blue-600">
                    {formatDuration(data.averageViewDuration)}
                  </div>
                  <p className="text-muted-foreground mt-2">Average time spent viewing proposals</p>

                  <div className="mt-8 flex items-center space-x-2">
                    <Share2 className="h-5 w-5 text-blue-600" />
                    <span className="text-lg">
                      {data.totalShares} total shares across all proposals
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
