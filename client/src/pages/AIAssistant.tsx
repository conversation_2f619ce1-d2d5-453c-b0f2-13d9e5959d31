import { useEffect } from "react";
import { AIAssistant as AIAssistantComponent } from "@/components/ai/AIAssistant";

const AIAssistant = () => {
  // Use a simpler component that just wraps the AIAssistant component
  // This avoids the nested Tabs issue that was causing the error
  
  return (
    <div className="p-4 md:p-6 h-full">
      <AIAssistantComponent />
    </div>
  );
};

export default AIAssistant;
