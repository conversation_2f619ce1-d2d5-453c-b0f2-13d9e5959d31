import { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  getObjectionCategories,
  getObjections,
  getResponsesForObjection,
  generateAIResponse,
  createObjection,
  createResponse
} from '@/api/objection-handler-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  MessageSquare,
  Search,
  Plus,
  <PERSON>rk<PERSON>,
  Thum<PERSON>Up,
  Thum<PERSON>Down,
  Co<PERSON>,
  Loader2,
  Tag,
  Filter
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Objection {
  _id: string;
  name: string;
  category: string;
  description: string;
  tags: string[];
  isCommon: boolean;
}

interface ObjectionResponse {
  _id: string;
  objectionId: string;
  response: string;
  context: string;
  effectiveness: number;
  usedCount: number;
  successCount: number;
  isAIGenerated: boolean;
  createdAt: string;
}

const ObjectionHandler = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedObjection, setSelectedObjection] = useState<Objection | null>(null);
  const [selectedResponseId, setSelectedResponseId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('browse');

  const { toast } = useToast();

  // Fetch objection categories
  const {
    data: categories,
    isLoading: isCategoriesLoading
  } = useQuery({
    queryKey: ['objection-categories'],
    queryFn: getObjectionCategories
  });

  // Fetch objections based on search and category
  const {
    data: objections,
    isLoading: isObjectionsLoading,
    refetch: refetchObjections
  } = useQuery({
    queryKey: ['objections', searchQuery, selectedCategory],
    queryFn: () => getObjections({
      search: searchQuery,
      category: selectedCategory || undefined
    })
  });

  // Fetch responses for the selected objection
  const {
    data: responses,
    isLoading: isResponsesLoading,
    refetch: refetchResponses
  } = useQuery({
    queryKey: ['objection-responses', selectedObjection?._id],
    queryFn: () => selectedObjection
      ? getResponsesForObjection(selectedObjection._id)
      : Promise.resolve([]),
    enabled: !!selectedObjection
  });

  // Generate AI response
  const generateResponseMutation = useMutation({
    mutationFn: (objectionId: string) => generateAIResponse(objectionId),
    onSuccess: (data) => {
      toast({
        title: 'Response Generated',
        description: 'AI has generated a response for this objection',
      });

      // Refetch responses
      refetchResponses();

      // Select the newly generated response
      setSelectedResponseId(data._id);
    },
    onError: (error) => {
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate response',
        variant: 'destructive',
      });
    }
  });

  // Handle search
  const handleSearch = () => {
    refetchObjections();
  };

  // Handle objection selection
  const handleObjectionSelect = (objection: Objection) => {
    setSelectedObjection(objection);
    setSelectedResponseId(null);
  };

  // Handle generate response
  const handleGenerateResponse = () => {
    if (!selectedObjection) {
      toast({
        title: 'Error',
        description: 'Please select an objection first',
        variant: 'destructive',
      });
      return;
    }

    generateResponseMutation.mutate(selectedObjection._id);
  };

  // Handle copy response to clipboard
  const handleCopyResponse = (response: string) => {
    navigator.clipboard.writeText(response);
    toast({
      title: 'Copied',
      description: 'Response copied to clipboard',
    });
  };

  // Format effectiveness as stars
  const formatEffectiveness = (effectiveness: number) => {
    return '★'.repeat(effectiveness) + '☆'.repeat(5 - effectiveness);
  };

  // Get badge color for category
  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case 'price':
        return 'bg-red-100 text-red-800';
      case 'product':
        return 'bg-blue-100 text-blue-800';
      case 'competition':
        return 'bg-purple-100 text-purple-800';
      case 'timing':
        return 'bg-yellow-100 text-yellow-800';
      case 'authority':
        return 'bg-orange-100 text-orange-800';
      case 'need':
        return 'bg-green-100 text-green-800';
      case 'trust':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Objection Handler</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="browse">
            <Search className="h-4 w-4 mr-2" />
            Browse Objections
          </TabsTrigger>
          <TabsTrigger value="create">
            <Plus className="h-4 w-4 mr-2" />
            Create New
          </TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="space-y-4">
          <div className="flex flex-col md:flex-row gap-2">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search objections..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                />
              </div>
            </div>
            <div className="w-full md:w-[200px]">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="All Categories" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Categories</SelectItem>
                  {isCategoriesLoading ? (
                    <SelectItem value="" disabled>Loading...</SelectItem>
                  ) : (
                    categories?.map((category: any) => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleSearch}>
              Search
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Objections List */}
            <Card className="h-[500px] overflow-y-auto">
              <CardHeader className="py-3">
                <CardTitle className="text-lg">Objections</CardTitle>
              </CardHeader>
              <CardContent className="py-0">
                {isObjectionsLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                  </div>
                ) : objections?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No objections found</p>
                    <p className="text-sm">Try a different search or category</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {objections?.map((objection: any) => (
                      <div
                        key={objection._id}
                        className={`p-3 rounded-md cursor-pointer hover:bg-gray-50 ${
                          selectedObjection?._id === objection._id ? 'bg-blue-50 border border-blue-200' : 'border'
                        }`}
                        onClick={() => handleObjectionSelect(objection)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{objection.name}</p>
                            <Badge className={getCategoryBadgeColor(objection.category)}>
                              {objection.category.charAt(0).toUpperCase() + objection.category.slice(1)}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Responses List */}
            <Card className="h-[500px] overflow-y-auto">
              <CardHeader className="py-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">Responses</CardTitle>
                  {selectedObjection && (
                    <Button
                      size="sm"
                      onClick={handleGenerateResponse}
                      disabled={generateResponseMutation.isPending}
                    >
                      {generateResponseMutation.isPending ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Sparkles className="h-4 w-4 mr-2" />
                      )}
                      Generate
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="py-0">
                {!selectedObjection ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>Select an objection to see responses</p>
                  </div>
                ) : isResponsesLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-24 w-full" />
                    <Skeleton className="h-24 w-full" />
                  </div>
                ) : responses?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No responses found</p>
                    <p className="text-sm">Generate a response with AI</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {responses?.map((response: any) => (
                      <div
                        key={response._id}
                        className={`p-3 rounded-md border ${
                          selectedResponseId === response._id ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                        onClick={() => setSelectedResponseId(response._id)}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center">
                            <span className="text-yellow-500 text-sm mr-2">
                              {formatEffectiveness(response.effectiveness)}
                            </span>
                            {response.isAIGenerated && (
                              <Badge variant="outline" className="ml-1">
                                <Sparkles className="h-3 w-3 mr-1" />
                                AI
                              </Badge>
                            )}
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCopyResponse(response.response);
                              }}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <p className="text-sm mb-2">{response.response}</p>
                        <div className="flex justify-between items-center mt-2">
                          <div className="text-xs text-gray-500">
                            Used {response.usedCount} times
                            {response.successCount > 0 && ` (${response.successCount} successful)`}
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Selected Objection Details */}
          {selectedObjection && (
            <Card>
              <CardHeader className="py-3">
                <CardTitle className="text-lg">{selectedObjection.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{selectedObjection.description}</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedObjection.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create New Objection</CardTitle>
              <CardDescription>
                Add a new objection to the database
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="objection-name">Objection Name</Label>
                  <Input
                    id="objection-name"
                    placeholder="Enter a name for the objection..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="objection-category">Category</Label>
                  <Select>
                    <SelectTrigger id="objection-category">
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {isCategoriesLoading ? (
                        <SelectItem value="" disabled>Loading...</SelectItem>
                      ) : (
                        categories?.map((category: string) => (
                          <SelectItem key={category} value={category}>
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="objection-description">Description</Label>
                  <Textarea
                    id="objection-description"
                    placeholder="Describe the objection in detail..."
                    rows={4}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Objection
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ObjectionHandler;
