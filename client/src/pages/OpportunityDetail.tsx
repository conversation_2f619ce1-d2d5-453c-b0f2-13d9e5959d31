import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Calendar, DollarSign, BarChart, Users, Building, Pencil, FileText, MessageSquare } from 'lucide-react';
import OpportunityForm from '@/components/opportunities/OpportunityForm';
import PipelineInsights from '@/components/opportunities/PipelineInsights';
import DealBrief from '@/components/opportunities/DealBrief';
import StageAnalysis from '@/components/opportunities/StageAnalysis';
import { MeetingPrepWizard } from '@/components/meeting-prep/MeetingPrepWizard';
import { ObjectionHandlerButton } from '@/components/objection-handler/ObjectionHandlerButton';
import { ProposalGeneratorButton } from '@/components/proposal-generator/ProposalGeneratorButton';
import { FollowUpCoachButton } from '@/components/follow-up-coach/FollowUpCoachButton';
import { WinLossAnalyzerButton } from '@/components/win-loss-analyzer/WinLossAnalyzerButton';

const OpportunityDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isEditOpen, setIsEditOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch opportunity details
  const {
    data: opportunity,
    isLoading,
    error
  } = useQuery({
    queryKey: ['opportunity', id],
    queryFn: async () => {
      if (!id) throw new Error('Opportunity ID is required');
      return mongoApiClient.opportunities.getById(id);
    },
    enabled: !!id
  });

  // Fetch related contact
  const {
    data: contact,
    isLoading: isContactLoading
  } = useQuery({
    queryKey: ['contact', opportunity?.contactId],
    queryFn: async () => {
      if (!opportunity?.contactId) return null;
      return mongoApiClient.contacts.getById(opportunity.contactId);
    },
    enabled: !!opportunity?.contactId
  });

  // Fetch related company
  const {
    data: company,
    isLoading: isCompanyLoading
  } = useQuery({
    queryKey: ['company', opportunity?.companyId],
    queryFn: async () => {
      if (!opportunity?.companyId) return null;
      return mongoApiClient.companies.getById(opportunity.companyId);
    },
    enabled: !!opportunity?.companyId
  });

  // Format currency
  const formatCurrency = (value?: number) => {
    if (value === undefined || value === null) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: opportunity?.currency || 'USD',
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format date
  const formatDate = (dateString?: string | Date) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  // Format stage label
  const formatStageLabel = (stage?: string) => {
    if (!stage) return '-';
    return stage
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get stage badge color
  const getStageBadgeColor = (stage?: string) => {
    if (!stage) return 'bg-gray-100 text-gray-800';

    switch (stage) {
      case 'prospecting':
        return 'bg-blue-100 text-blue-800';
      case 'qualification':
        return 'bg-indigo-100 text-indigo-800';
      case 'needs_analysis':
        return 'bg-purple-100 text-purple-800';
      case 'proposal':
        return 'bg-pink-100 text-pink-800';
      case 'negotiation':
        return 'bg-orange-100 text-orange-800';
      case 'closed_won':
        return 'bg-green-100 text-green-800';
      case 'closed_lost':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle back button click
  const handleBack = () => {
    navigate('/opportunities');
  };

  // Handle edit button click
  const handleEdit = () => {
    setIsEditOpen(true);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">
            {isLoading ? <Skeleton className="h-8 w-48" /> : opportunity?.name}
          </h1>
        </div>
        <div className="flex space-x-2">
          {!isLoading && opportunity && (
            <>
              <MeetingPrepWizard
                opportunityId={id}
                opportunityName={opportunity.name}
                contactId={opportunity.contactId}
                contactName={contact ? `${contact.firstName} ${contact.lastName}` : undefined}
                companyId={opportunity.companyId}
                companyName={company?.name}
                buttonText="Meeting Prep"
              />
              <ObjectionHandlerButton
                opportunityId={id}
                opportunityName={opportunity.name}
                contactId={opportunity.contactId}
                contactName={contact ? `${contact.firstName} ${contact.lastName}` : undefined}
                companyId={opportunity.companyId}
                companyName={company?.name}
                buttonText="Objection Handler"
              />
              <ProposalGeneratorButton
                opportunityId={id}
                opportunityName={opportunity.name}
                contactId={opportunity.contactId}
                contactName={contact ? `${contact.firstName} ${contact.lastName}` : undefined}
                companyId={opportunity.companyId}
                companyName={company?.name}
                buttonText="Generate Proposal"
              />
              <FollowUpCoachButton
                opportunityId={id}
                opportunityName={opportunity.name}
                contactId={opportunity.contactId}
                contactName={contact ? `${contact.firstName} ${contact.lastName}` : undefined}
                companyId={opportunity.companyId}
                companyName={company?.name}
                buttonText="Follow-up Coach"
              />
              <WinLossAnalyzerButton
                opportunityId={id}
                opportunityName={opportunity.name}
                contactId={opportunity.contactId}
                contactName={contact ? `${contact.firstName} ${contact.lastName}` : undefined}
                companyId={opportunity.companyId}
                companyName={company?.name}
                buttonText="Win/Loss Analyzer"
              />
            </>
          )}
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Main content */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-[200px] rounded-lg" />
          <Skeleton className="h-[200px] rounded-lg" />
          <Skeleton className="h-[200px] rounded-lg" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500">
              <p>Error loading opportunity: {error instanceof Error ? error.message : 'Unknown error'}</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Opportunity details */}
          <Card>
            <CardHeader>
              <CardTitle>Opportunity Details</CardTitle>
              <CardDescription>Key information about this opportunity</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Stage</p>
                <Badge className={getStageBadgeColor(opportunity?.stage)}>
                  {formatStageLabel(opportunity?.stage)}
                </Badge>
              </div>

              <div>
                <p className="text-sm text-gray-500">Value</p>
                <p className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                  {formatCurrency(opportunity?.value)}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Probability</p>
                <p className="flex items-center">
                  <BarChart className="h-4 w-4 mr-1 text-gray-400" />
                  {opportunity?.probability || 0}%
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Expected Close Date</p>
                <p className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                  {formatDate(opportunity?.closeDate)}
                </p>
              </div>

              {opportunity?.description && (
                <div>
                  <p className="text-sm text-gray-500">Description</p>
                  <p className="text-sm mt-1">{opportunity.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Middle column - Related entities */}
          <Card>
            <CardHeader>
              <CardTitle>Related Entities</CardTitle>
              <CardDescription>Contact and company information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Contact information */}
              <div>
                <p className="text-sm text-gray-500 mb-2">Primary Contact</p>
                {isContactLoading ? (
                  <Skeleton className="h-16 w-full" />
                ) : contact ? (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <Users className="h-5 w-5 mr-2 text-gray-400" />
                      <p className="font-medium">{contact.firstName} {contact.lastName}</p>
                    </div>
                    {contact.title && <p className="text-sm text-gray-500 ml-7">{contact.title}</p>}
                    {contact.email && <p className="text-sm text-gray-500 ml-7">{contact.email}</p>}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No contact associated</p>
                )}
              </div>

              {/* Company information */}
              <div>
                <p className="text-sm text-gray-500 mb-2">Company</p>
                {isCompanyLoading ? (
                  <Skeleton className="h-16 w-full" />
                ) : company ? (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <Building className="h-5 w-5 mr-2 text-gray-400" />
                      <p className="font-medium">{company.name}</p>
                    </div>
                    {company.industry && <p className="text-sm text-gray-500 ml-7">Industry: {company.industry}</p>}
                    {company.size && <p className="text-sm text-gray-500 ml-7">Size: {company.size}</p>}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No company associated</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Right column - Pipeline Insights */}
          <Card>
            <CardContent className="pt-6">
              {id && <PipelineInsights opportunityId={id} />}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs for activities, documents, etc. */}
      <Tabs defaultValue="deal-brief" className="w-full">
        <TabsList>
          <TabsTrigger value="deal-brief">Deal Brief</TabsTrigger>
          <TabsTrigger value="stage-analysis">Stage Analysis</TabsTrigger>
          <TabsTrigger value="activities">Activities</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
        </TabsList>
        <TabsContent value="deal-brief" className="mt-4">
          {id && <DealBrief opportunityId={id} />}
        </TabsContent>
        <TabsContent value="stage-analysis" className="mt-4">
          {id && <StageAnalysis opportunityId={id} />}
        </TabsContent>
        <TabsContent value="activities" className="mt-4">
          <Card>
            <CardContent className="pt-6">
              <p className="text-center text-gray-500">Activities will be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="documents" className="mt-4">
          <Card>
            <CardContent className="pt-6">
              <p className="text-center text-gray-500">Documents will be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="notes" className="mt-4">
          <Card>
            <CardContent className="pt-6">
              <p className="text-center text-gray-500">Notes will be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Opportunity Modal */}
      {opportunity && (
        <OpportunityForm
          open={isEditOpen}
          onOpenChange={setIsEditOpen}
          initialData={opportunity}
          isEditing={true}
        />
      )}
    </div>
  );
};

export default OpportunityDetail;
