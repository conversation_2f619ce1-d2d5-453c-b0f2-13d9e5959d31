import React from 'react';
import AppShell from '@/components/layout/AppShell';
import AIBridgeTest from '@/components/ai/AIBridgeTest';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { MoveRight, Cpu, Network, Database, Server, Cloud, Workflow } from 'lucide-react';

const AIArchitecture: React.FC = () => {
  return (
    <AppShell>
      <div className="container mx-auto py-6 space-y-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Left Column - Architecture Overview */}
          <div className="flex-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Workflow className="mr-2 h-5 w-5" />
                  Hybrid Architecture Overview
                </CardTitle>
                <CardDescription>
                  Our AI-first CRM implements a hybrid architecture with specialized components
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {/* Architecture Diagram */}
                  <div className="border rounded-lg p-4 bg-slate-50">
                    <div className="flex flex-col space-y-4">
                      <div className="flex justify-between items-center">
                        <div className="bg-blue-100 p-3 rounded-lg shadow-sm">
                          <div className="font-medium text-blue-800">React Frontend</div>
                          <div className="text-xs text-blue-600">CopilotKit + Firebase Auth</div>
                        </div>
                        <MoveRight className="text-slate-400" />
                        <div className="bg-green-100 p-3 rounded-lg shadow-sm">
                          <div className="font-medium text-green-800">Node.js Core</div>
                          <div className="text-xs text-green-600">Express + MongoDB</div>
                        </div>
                      </div>

                      <div className="flex items-center justify-center">
                        <div className="h-10 border-l-2 border-dashed border-slate-300"></div>
                      </div>

                      <div className="bg-purple-100 p-3 rounded-lg shadow-sm flex justify-between items-center">
                        <div>
                          <div className="font-medium text-purple-800">AI Bridge Layer</div>
                          <div className="text-xs text-purple-600">REST + Server-Sent Events</div>
                        </div>
                        <Server className="h-6 w-6 text-purple-600" />
                      </div>

                      <div className="flex items-center justify-center">
                        <div className="h-10 border-l-2 border-dashed border-slate-300"></div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="bg-red-100 p-3 rounded-lg shadow-sm">
                          <div className="font-medium text-red-800">Python AI Layer</div>
                          <div className="text-xs text-red-600">CrewAI + LlamaIndex</div>
                        </div>
                        <MoveRight className="text-slate-400" />
                        <div className="bg-amber-100 p-3 rounded-lg shadow-sm">
                          <div className="font-medium text-amber-800">Edge Workers</div>
                          <div className="text-xs text-amber-600">Johannesburg/Lagos Regions</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Key Architecture Points */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-lg p-3 bg-white">
                      <div className="flex items-center mb-2">
                        <Cpu className="h-4 w-4 mr-2 text-blue-600" />
                        <span className="font-medium">Task-based Agents</span>
                      </div>
                      <p className="text-sm text-gray-600">
                        Specialized AI agents for contact enrichment, opportunity analysis, and pipeline optimization.
                      </p>
                    </div>
                    
                    <div className="border rounded-lg p-3 bg-white">
                      <div className="flex items-center mb-2">
                        <Network className="h-4 w-4 mr-2 text-green-600" />
                        <span className="font-medium">Edge Optimized</span>
                      </div>
                      <p className="text-sm text-gray-600">
                        Distributed computing across African regions with Cloudflare Edge Workers for low latency.
                      </p>
                    </div>
                    
                    <div className="border rounded-lg p-3 bg-white">
                      <div className="flex items-center mb-2">
                        <Database className="h-4 w-4 mr-2 text-purple-600" />
                        <span className="font-medium">Schema Flexibility</span>
                      </div>
                      <p className="text-sm text-gray-600">
                        MongoDB for document storage with Neo4j for advanced B2B relationship mapping.
                      </p>
                    </div>
                    
                    <div className="border rounded-lg p-3 bg-white">
                      <div className="flex items-center mb-2">
                        <Cloud className="h-4 w-4 mr-2 text-red-600" />
                        <span className="font-medium">Fallback Mechanism</span>
                      </div>
                      <p className="text-sm text-gray-600">
                        Local OpenAI processing when Python AI services are unavailable or unreachable.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - AI Bridge Test Interface */}
          <div className="flex-1">
            <AIBridgeTest />
          </div>
        </div>
      </div>
    </AppShell>
  );
};

export default AIArchitecture;