import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, RefreshCw, BarChart3, PieChart, TrendingUp } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  runAttribution,
  getAttributionResults,
  optimizeBudget
} from '@/api/analytics-reporting-api';
import {
  AttributionResultsType,
  GetAttributionResultsResponse
} from '@/types/analytics-reporting';
import AttributionChart from '@/components/analytics/AttributionChart';
import AttributionTable from '@/components/analytics/AttributionTable';
import BudgetOptimizer from '@/components/analytics/BudgetOptimizer';
import AttributionSummary from '@/components/analytics/AttributionSummary';

// Window options
const windowOptions = [
  { value: 'last_7_days', label: 'Last 7 Days' },
  { value: 'last_30_days', label: 'Last 30 Days' },
  { value: 'last_90_days', label: 'Last 90 Days' },
  { value: 'current_month', label: 'Current Month' },
  { value: 'current_quarter', label: 'Current Quarter' },
  { value: 'current_year', label: 'Current Year' },
];

// Model options
const modelOptions = [
  { value: 'markov', label: 'Markov Chain' },
  { value: 'shapley', label: 'Shapley Values' },
  { value: 'first_touch', label: 'First Touch' },
  { value: 'last_touch', label: 'Last Touch' },
  { value: 'linear', label: 'Linear' },
];

// Group by options
const groupByOptions = [
  { value: 'channel', label: 'Channel' },
  { value: 'campaign', label: 'Campaign' },
  { value: 'medium', label: 'Medium' },
  { value: 'source', label: 'Source' },
];

export default function AttributionDashboard() {
  const [window, setWindow] = useState('last_30_days');
  const [model, setModel] = useState('markov');
  const [groupBy, setGroupBy] = useState('channel');
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch attribution results
  const {
    data: attributionData,
    isLoading: isLoadingAttribution,
    error: attributionError,
    refetch: refetchAttribution
  } = useQuery<GetAttributionResultsResponse>({
    queryKey: ['attributionResults', window, model, groupBy],
    queryFn: () => getAttributionResults({ window, model_type: model as any, group_by: groupBy as any }),
  });

  // Run attribution mutation
  const runAttributionMutation = useMutation({
    mutationFn: runAttribution,
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: 'Attribution model run started',
          description: data.message || 'Attribution calculation has been started',
        });
        // Refetch after a delay to allow calculation to complete
        setTimeout(() => {
          refetchAttribution();
        }, 3000);
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to run attribution model',
          variant: 'destructive',
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to run attribution model',
        variant: 'destructive',
      });
    },
  });

  // Handle run attribution
  const handleRunAttribution = () => {
    runAttributionMutation.mutate({
      window,
      model_type: model as any,
      force_refresh: true
    });
  };

  // Handle window change
  const handleWindowChange = (value: string) => {
    setWindow(value);
  };

  // Handle model change
  const handleModelChange = (value: string) => {
    setModel(value);
  };

  // Handle group by change
  const handleGroupByChange = (value: string) => {
    setGroupBy(value);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Attribution AI</h1>
        <Button
          onClick={handleRunAttribution}
          disabled={runAttributionMutation.isPending}
        >
          {runAttributionMutation.isPending ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Run Attribution
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Select value={window} onValueChange={handleWindowChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select time window" />
            </SelectTrigger>
            <SelectContent>
              {windowOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Select value={model} onValueChange={handleModelChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select attribution model" />
            </SelectTrigger>
            <SelectContent>
              {modelOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Select value={groupBy} onValueChange={handleGroupByChange}>
            <SelectTrigger>
              <SelectValue placeholder="Group by" />
            </SelectTrigger>
            <SelectContent>
              {groupByOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary cards */}
      {attributionData?.success && attributionData.summary && (
        <AttributionSummary summary={attributionData.summary} />
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-[400px]">
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="details">
            <PieChart className="h-4 w-4 mr-2" />
            Details
          </TabsTrigger>
          <TabsTrigger value="optimizer">
            <TrendingUp className="h-4 w-4 mr-2" />
            Budget Optimizer
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Attribution Overview</CardTitle>
              <CardDescription>
                Attribution results by {groupByOptions.find(o => o.value === groupBy)?.label.toLowerCase()}
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[500px]">
              {isLoadingAttribution ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : attributionError ? (
                <div className="text-destructive flex justify-center items-center h-full">
                  Failed to load attribution data
                </div>
              ) : !attributionData?.results.length ? (
                <div className="flex justify-center items-center h-full text-muted-foreground">
                  No attribution data available. Run attribution to generate data.
                </div>
              ) : (
                <AttributionChart
                  results={attributionData.results}
                  groupBy={groupBy}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Attribution Details</CardTitle>
              <CardDescription>
                Detailed attribution results
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingAttribution ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              ) : attributionError ? (
                <div className="text-destructive py-4">
                  Failed to load attribution data
                </div>
              ) : !attributionData?.results.length ? (
                <div className="flex justify-center py-4 text-muted-foreground">
                  No attribution data available. Run attribution to generate data.
                </div>
              ) : (
                <AttributionTable
                  results={attributionData.results}
                  groupBy={groupBy}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimizer" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Budget Optimizer</CardTitle>
              <CardDescription>
                AI-powered budget optimization recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BudgetOptimizer
                window={window}
                model={model as 'markov' | 'shapley'}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
