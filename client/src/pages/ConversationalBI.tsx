import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, Send, RefreshCw, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { askQuestion, getRecentInsights } from '@/api/analytics-reporting-api';
import { CBICacheType } from '@/types/analytics-reporting';
import ChartRenderer from '@/components/analytics/ChartRenderer';
import NarrativeDisplay from '@/components/analytics/NarrativeDisplay';
import QueryInput from '@/components/analytics/QueryInput';
import RecentQueries from '@/components/analytics/RecentQueries';
import SuggestedExperiments from '@/components/analytics/SuggestedExperiments';

export default function ConversationalBI() {
  const [question, setQuestion] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentInsight, setCurrentInsight] = useState<CBICacheType | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch recent insights
  const {
    data: recentInsights,
    isLoading: isLoadingRecent,
    error: recentError
  } = useQuery({
    queryKey: ['recentInsights'],
    queryFn: () => getRecentInsights(),
  });

  // Ask question mutation
  const askQuestionMutation = useMutation({
    mutationFn: askQuestion,
    onSuccess: (data) => {
      if (data.success) {
        setCurrentInsight(data.insight);
        queryClient.invalidateQueries({ queryKey: ['recentInsights'] });
        setIsSubmitting(false);
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to get insights',
          variant: 'destructive',
        });
        setIsSubmitting(false);
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to get insights',
        variant: 'destructive',
      });
      setIsSubmitting(false);
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!question.trim()) return;

    setIsSubmitting(true);
    askQuestionMutation.mutate({ question });
  };

  // Handle selecting a recent insight
  const handleSelectInsight = (insight: CBICacheType) => {
    setCurrentInsight(insight);
  };

  // Handle refresh
  const handleRefresh = () => {
    if (!currentInsight) return;

    setIsSubmitting(true);
    askQuestionMutation.mutate({
      question: currentInsight.question,
      refresh: true
    });
  };

  // Set the first insight as current when loading completes
  useEffect(() => {
    if (recentInsights?.length && !currentInsight) {
      setCurrentInsight(recentInsights[0]);
    }
  }, [recentInsights, currentInsight]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Conversational BI</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left sidebar - Recent queries */}
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Recent Questions</CardTitle>
              <CardDescription>
                Your previously asked questions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingRecent ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              ) : recentError ? (
                <div className="text-destructive py-4">
                  Failed to load recent questions
                </div>
              ) : (
                <RecentQueries
                  insights={recentInsights || []}
                  onSelect={handleSelectInsight}
                  currentInsightId={currentInsight?.id}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Main content - Current insight */}
        <div className="md:col-span-2">
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>Ask Your Data</CardTitle>
              <CardDescription>
                Ask questions about your business data in plain English
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              {/* Query input */}
              <form onSubmit={handleSubmit} className="mb-6">
                <div className="flex gap-2">
                  <QueryInput
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    placeholder="e.g., What was our conversion rate last month?"
                    disabled={isSubmitting}
                  />
                  <Button type="submit" disabled={isSubmitting || !question.trim()}>
                    {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                  </Button>
                </div>
              </form>

              {/* Results */}
              {currentInsight ? (
                <div className="space-y-6">
                  {/* Chart */}
                  <div className="h-80 w-full">
                    <ChartRenderer chartSpec={currentInsight.chart_spec} />
                  </div>

                  {/* Narrative */}
                  <NarrativeDisplay
                    narrative={currentInsight.narrative}
                    rootCauseAnalysis={currentInsight.root_cause_analysis}
                  />

                  {/* Suggested experiments */}
                  {currentInsight.suggested_experiments && currentInsight.suggested_experiments.length > 0 && (
                    <SuggestedExperiments experiments={currentInsight.suggested_experiments} />
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                  <p>Ask a question to get started</p>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-4">
              <div className="flex items-center text-sm text-muted-foreground">
                {currentInsight?.source === 'cache' ? 'Cached result' : 'Generated result'}
                {currentInsight && (
                  <span className="ml-2">
                    ({new Date(currentInsight.created_at).toLocaleString()})
                  </span>
                )}
              </div>
              <div className="flex gap-2">
                {currentInsight && (
                  <>
                    <Button variant="outline" size="sm" onClick={handleRefresh}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                    <Button variant="ghost" size="sm">
                      <ThumbsUp className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <ThumbsDown className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
