import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus } from "lucide-react";
import ContactTable from "@/components/contacts/ContactTable";
import ContactForm from "@/components/contacts/ContactForm";

const Contacts = () => {
  const [showAddContact, setShowAddContact] = useState(false);

  return (
    <div className="p-4 md:p-6 pb-20 md:pb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-gray-800">Contacts</h2>
        <Button onClick={() => setShowAddContact(true)}>
          <UserPlus className="h-5 w-5 mr-2" />
          Add Contact
        </Button>
      </div>

      <div className="mb-6">
        <ContactTable />
      </div>

      <ContactForm 
        open={showAddContact} 
        onOpenChange={setShowAddContact} 
      />
    </div>
  );
};

export default Contacts;
