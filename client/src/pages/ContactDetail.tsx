import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { mongoApiClient } from '@/api/mongo-api-client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Mail, Phone, Building, Pencil, FileText, MessageSquare } from 'lucide-react';
import ContactForm from '@/components/contacts/ContactForm';
import { MeetingPrepButton } from '@/components/meeting-prep/MeetingPrepButton';
import { MeetingPrepWizard } from '@/components/meeting-prep/MeetingPrepWizard';
import { ObjectionHandlerButton } from '@/components/objection-handler/ObjectionHandlerButton';
import { FollowUpCoachButton } from '@/components/follow-up-coach/FollowUpCoachButton';
import SmartInteractionTimeline from '@/components/smart-interaction-timeline/SmartInteractionTimeline';

const ContactDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isEditOpen, setIsEditOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch contact details
  const {
    data: contact,
    isLoading,
    error
  } = useQuery({
    queryKey: ['contact', id],
    queryFn: async () => {
      if (!id) throw new Error('Contact ID is required');
      return mongoApiClient.contacts.getById(id);
    },
    enabled: !!id
  });

  // Fetch related company
  const {
    data: company,
    isLoading: isCompanyLoading
  } = useQuery({
    queryKey: ['company', contact?.companyId],
    queryFn: async () => {
      if (!contact?.companyId) return null;
      return mongoApiClient.companies.getById(contact.companyId);
    },
    enabled: !!contact?.companyId
  });

  // Fetch related opportunities
  const {
    data: opportunities,
    isLoading: isOpportunitiesLoading
  } = useQuery({
    queryKey: ['opportunities', 'contact', id],
    queryFn: async () => {
      if (!id) return [];
      return mongoApiClient.opportunities.getByContactId(id);
    },
    enabled: !!id
  });

  // Handle back button click
  const handleBack = () => {
    navigate('/contacts');
  };

  // Handle edit button click
  const handleEdit = () => {
    setIsEditOpen(true);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">
            {isLoading ? (
              <Skeleton className="h-8 w-48" />
            ) : (
              `${contact?.firstName} ${contact?.lastName}`
            )}
          </h1>
        </div>
        <div className="flex space-x-2">
          {!isLoading && contact && (
            <>
              <MeetingPrepWizard
                contactId={id}
                contactName={`${contact.firstName} ${contact.lastName}`}
                companyId={contact.companyId}
                companyName={company?.name}
                buttonText="Meeting Prep"
              />
              <ObjectionHandlerButton
                contactId={id}
                contactName={`${contact.firstName} ${contact.lastName}`}
                companyId={contact.companyId}
                companyName={company?.name}
                buttonText="Objection Handler"
              />
              <FollowUpCoachButton
                contactId={id}
                contactName={`${contact.firstName} ${contact.lastName}`}
                companyId={contact.companyId}
                companyName={company?.name}
                buttonText="Follow-up Coach"
              />
            </>
          )}
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Main content */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-[200px] rounded-lg" />
          <Skeleton className="h-[200px] rounded-lg" />
          <Skeleton className="h-[200px] rounded-lg" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500">
              <p>Error loading contact: {error instanceof Error ? error.message : 'Unknown error'}</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Contact details */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Details</CardTitle>
              <CardDescription>Key information about this contact</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-gray-500 mb-1">Status</p>
                <Badge className={
                  contact?.status === 'lead' ? 'bg-blue-100 text-blue-800' :
                  contact?.status === 'customer' ? 'bg-green-100 text-green-800' :
                  contact?.status === 'prospect' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }>
                  {contact?.status ? (contact.status.charAt(0).toUpperCase() + contact.status.slice(1)) : 'Unknown'}
                </Badge>
              </div>

              {contact?.title && (
                <div>
                  <p className="text-sm text-gray-500 mb-1">Title</p>
                  <p>{contact.title}</p>
                </div>
              )}

              {contact?.email && (
                <div>
                  <p className="text-sm text-gray-500 mb-1">Email</p>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-gray-400" />
                    <a href={`mailto:${contact.email}`} className="text-blue-600 hover:underline">
                      {contact.email}
                    </a>
                  </div>
                </div>
              )}

              {contact?.phone && (
                <div>
                  <p className="text-sm text-gray-500 mb-1">Phone</p>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-gray-400" />
                    <a href={`tel:${contact.phone}`} className="text-blue-600 hover:underline">
                      {contact.phone}
                    </a>
                  </div>
                </div>
              )}

              {contact?.source && (
                <div>
                  <p className="text-sm text-gray-500 mb-1">Source</p>
                  <p>{contact.source}</p>
                </div>
              )}

              {contact?.createdAt && (
                <div>
                  <p className="text-sm text-gray-500 mb-1">Created</p>
                  <p>{new Date(contact.createdAt).toLocaleDateString()}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Middle column - Company information */}
          <Card>
            <CardHeader>
              <CardTitle>Company</CardTitle>
              <CardDescription>Company information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isCompanyLoading ? (
                <Skeleton className="h-16 w-full" />
              ) : company ? (
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Building className="h-5 w-5 mr-2 text-gray-400" />
                    <p className="font-medium">{company.name}</p>
                  </div>

                  {company.industry && (
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Industry</p>
                      <p>{company.industry}</p>
                    </div>
                  )}

                  {company.size && (
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Size</p>
                      <p>{company.size}</p>
                    </div>
                  )}

                  {company.website && (
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Website</p>
                      <a href={company.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        {company.website}
                      </a>
                    </div>
                  )}

                  {company.location && (
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Location</p>
                      <p>{company.location}</p>
                    </div>
                  )}

                  {/* Meeting prep button for company */}
                  <div className="mt-4">
                    <MeetingPrepButton
                      companyId={company.id || (company as any)._id}
                      variant="outline"
                      size="sm"
                    />
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500">No company associated with this contact</p>
              )}
            </CardContent>
          </Card>

          {/* Right column - Opportunities */}
          <Card>
            <CardHeader>
              <CardTitle>Opportunities</CardTitle>
              <CardDescription>Related opportunities</CardDescription>
            </CardHeader>
            <CardContent>
              {isOpportunitiesLoading ? (
                <Skeleton className="h-16 w-full" />
              ) : opportunities && opportunities.length > 0 ? (
                <div className="space-y-3">
                  {opportunities.map((opportunity) => (
                    <div key={opportunity.id || (opportunity as any)._id} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{opportunity.name}</p>
                          <p className="text-sm text-gray-500">
                            {opportunity.value ? `${opportunity.currency || '$'}${opportunity.value.toLocaleString()}` : 'No value set'}
                          </p>
                          <Badge className={
                            opportunity.stage === 'discovery' ? 'bg-blue-100 text-blue-800' :
                            opportunity.stage === 'proposal' ? 'bg-yellow-100 text-yellow-800' :
                            opportunity.stage === 'negotiation' ? 'bg-orange-100 text-orange-800' :
                            opportunity.stage === 'closed_won' ? 'bg-green-100 text-green-800' :
                            opportunity.stage === 'closed_lost' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }>
                            {opportunity.stage?.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') || 'Unknown'}
                          </Badge>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/opportunities/${opportunity.id || (opportunity as any)._id}`)}
                          >
                            View
                          </Button>
                          <MeetingPrepButton
                            opportunityId={opportunity.id || (opportunity as any)._id}
                            variant="ghost"
                            size="sm"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No opportunities associated with this contact</p>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs for interactions, notes, etc. */}
      <Tabs defaultValue="interactions" className="w-full">
        <TabsList>
          <TabsTrigger value="interactions">Interactions</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>
        <TabsContent value="interactions" className="mt-4">
          {id && <SmartInteractionTimeline />}
        </TabsContent>
        <TabsContent value="notes" className="mt-4">
          <Card>
            <CardContent className="pt-6">
              {contact?.notes ? (
                <div className="prose max-w-none">
                  <p>{contact.notes}</p>
                </div>
              ) : (
                <p className="text-center text-gray-500">No notes available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="documents" className="mt-4">
          <Card>
            <CardContent className="pt-6">
              <p className="text-center text-gray-500">Documents will be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Contact Modal */}
      {contact && (
        <ContactForm
          open={isEditOpen}
          onOpenChange={setIsEditOpen}
          initialData={contact ? {
            ...contact,
            id: typeof contact.id === 'string' ? parseInt(contact.id) : contact.id,
            status: contact.status || 'lead'
          } : undefined}
          isEditing={true}
        />
      )}
    </div>
  );
};

export default ContactDetail;
