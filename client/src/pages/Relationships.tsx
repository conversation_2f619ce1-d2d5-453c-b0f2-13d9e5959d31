import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Relationship } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import { Link, Network, Users, Building2, Filter, PlusCircle } from "lucide-react";
import ForceGraph2D from "react-force-graph-2d";

type Node = {
  id: string;
  name: string;
  type: 'contact' | 'company' | 'you';
  val: number;
};

type Link = {
  source: string;
  target: string;
  type: string;
  strength: number;
};

type GraphData = {
  nodes: Node[];
  links: Link[];
};

const Relationships = () => {
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
  const [highlightNodes, setHighlightNodes] = useState(new Set());
  const [highlightLinks, setHighlightLinks] = useState(new Set());
  const [filterType, setFilterType] = useState<string>("all");
  const graphRef = useRef<any>();

  const { data: relationships, isLoading, error } = useQuery({
    queryKey: ['/api/relationships'],
    queryFn: async () => {
      const response = await fetch('/api/relationships');
      if (!response.ok) {
        throw new Error('Failed to fetch relationships');
      }
      return response.json() as Promise<Relationship[]>;
    }
  });

  // For demo purposes, let's create sample data
  useEffect(() => {
    // This would normally come from relationships data
    const sampleData: GraphData = {
      nodes: [
        { id: 'you', name: 'You', type: 'you', val: 15 },
        { id: 'contact1', name: 'Sarah Johnson', type: 'contact', val: 10 },
        { id: 'contact2', name: 'Michael Brown', type: 'contact', val: 8 },
        { id: 'contact3', name: 'Emily Davis', type: 'contact', val: 8 },
        { id: 'company1', name: 'TechCorp', type: 'company', val: 12 },
        { id: 'company2', name: 'Acme Inc', type: 'company', val: 12 },
        { id: 'company3', name: 'Global Ltd', type: 'company', val: 10 },
      ],
      links: [
        { source: 'you', target: 'contact1', type: 'manages', strength: 5 },
        { source: 'you', target: 'contact2', type: 'knows', strength: 3 },
        { source: 'you', target: 'contact3', type: 'knows', strength: 2 },
        { source: 'you', target: 'company1', type: 'works_for', strength: 5 },
        { source: 'contact1', target: 'company1', type: 'works_for', strength: 5 },
        { source: 'contact2', target: 'company2', type: 'works_for', strength: 5 },
        { source: 'contact3', target: 'company3', type: 'works_for', strength: 5 },
        { source: 'contact1', target: 'contact2', type: 'knows', strength: 2 },
        { source: 'company1', target: 'company2', type: 'partner', strength: 3 },
        { source: 'company2', target: 'company3', type: 'customer', strength: 4 },
      ]
    };

    setGraphData(sampleData);
  }, [relationships]);

  const getNodeColor = (node: Node) => {
    if (highlightNodes.size > 0 && !highlightNodes.has(node.id)) {
      return 'rgba(200, 200, 200, 0.5)';
    }

    switch (node.type) {
      case 'contact':
        return '#3B82F6'; // primary blue
      case 'company':
        return '#8B5CF6'; // secondary purple
      case 'you':
        return '#10B981'; // green
      default:
        return '#CBD5E1'; // gray
    }
  };

  const getLinkColor = (link: any) => {
    if (highlightLinks.size > 0 && !highlightLinks.has(link)) {
      return 'rgba(200, 200, 200, 0.25)';
    }

    switch (link.type) {
      case 'works_for':
        return 'rgba(59, 130, 246, 0.5)'; // blue
      case 'knows':
        return 'rgba(139, 92, 246, 0.5)'; // purple
      case 'partner':
        return 'rgba(16, 185, 129, 0.5)'; // green
      case 'customer':
        return 'rgba(249, 115, 22, 0.5)'; // orange
      default:
        return 'rgba(203, 213, 225, 0.5)'; // gray
    }
  };

  const getLinkWidth = (link: any) => {
    if (highlightLinks.size > 0 && !highlightLinks.has(link)) {
      return 1;
    }
    return 2 + (link.strength || 1);
  };

  const handleNodeHover = (node: any) => {
    if (!node) {
      setHighlightNodes(new Set());
      setHighlightLinks(new Set());
      return;
    }

    const neighbors = new Set<string>();
    const links = new Set();

    // Find all links connected to this node
    graphData.links.forEach(link => {
      const sourceId = typeof link.source === 'object' ? (link.source as any).id : link.source;
      const targetId = typeof link.target === 'object' ? (link.target as any).id : link.target;

      if (sourceId === node.id) {
        neighbors.add(targetId);
        links.add(link);
      } else if (targetId === node.id) {
        neighbors.add(sourceId);
        links.add(link);
      }
    });

    neighbors.add(node.id);
    setHighlightNodes(neighbors);
    setHighlightLinks(links);
  };

  const handleFilterChange = (type: string) => {
    setFilterType(type);

    // Apply filters based on the selected type
    if (type === 'all') {
      // Reset the graph to show all nodes
      if (graphRef.current) {
        graphRef.current.d3Force('charge').strength(-120);
        graphRef.current.d3Force('link').distance(70);
        graphRef.current.d3ReheatSimulation();
      }
    } else {
      // Show only nodes of the selected type
      if (graphRef.current) {
        graphRef.current.d3Force('charge').strength(-150);
        graphRef.current.d3Force('link').distance(100);
        graphRef.current.d3ReheatSimulation();
      }
    }
  };

  return (
    <div className="p-4 md:p-6 pb-20 md:pb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-gray-800">Relationship Network</h2>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => handleFilterChange('all')}>
            <Network className="h-4 w-4 mr-2" />
            All
          </Button>
          <Button variant="outline" onClick={() => handleFilterChange('contacts')}>
            <Users className="h-4 w-4 mr-2" />
            Contacts
          </Button>
          <Button variant="outline" onClick={() => handleFilterChange('companies')}>
            <Building2 className="h-4 w-4 mr-2" />
            Companies
          </Button>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Relationship
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        <div className="xl:col-span-3">
          <Card className="h-[calc(100vh-200px)]">
            <CardContent className="p-0">
              {isLoading ? (
                <div className="flex items-center justify-center h-full">
                  <Skeleton className="h-full w-full" />
                </div>
              ) : error ? (
                <div className="flex items-center justify-center h-full text-red-500">
                  Error loading relationship data
                </div>
              ) : graphData.nodes.length > 0 ? (
                <div className="h-full w-full">
                  <ForceGraph2D
                    ref={graphRef}
                    graphData={graphData}
                    nodeLabel={(node: any) => `${node.name} (${node.type})`}
                    nodeColor={getNodeColor}
                    nodeVal={(node: any) => node.val}
                    linkColor={getLinkColor}
                    linkWidth={getLinkWidth}
                    linkLabel={(link: any) => link.type}
                    onNodeHover={handleNodeHover}
                    linkDirectionalArrowLength={3}
                    linkDirectionalArrowRelPos={1}
                    cooldownTicks={100}
                    onEngineStop={() => graphRef.current?.zoomToFit(400, 50)}
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full p-4">
                  <Link className="h-16 w-16 text-gray-300 mb-2" />
                  <p className="text-gray-500 text-lg mb-2">No relationship data available</p>
                  <p className="text-gray-400 text-sm mb-4 text-center">
                    Start adding relationships between your contacts and companies to visualize your network
                  </p>
                  <Button>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Your First Relationship
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Network Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <p className="font-medium text-gray-800">Network Size</p>
                  <p className="text-gray-600">{graphData.nodes.length} nodes • {graphData.links.length} connections</p>
                </div>
                <div>
                  <p className="font-medium text-gray-800">Key Connectors</p>
                  <ul className="list-disc list-inside text-gray-600">
                    <li>Sarah Johnson (5 connections)</li>
                    <li>TechCorp (4 connections)</li>
                  </ul>
                </div>
                <div>
                  <p className="font-medium text-gray-800">Relationship Types</p>
                  <ul className="list-disc list-inside text-gray-600">
                    <li>Works for (4)</li>
                    <li>Knows (3)</li>
                    <li>Partner (1)</li>
                    <li>Customer (1)</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Suggested Connections</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="flex items-start">
                    <div className="bg-primary p-1.5 rounded-md">
                      <Users className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-xs font-medium text-gray-600">AI Insight</p>
                      <p className="text-sm text-gray-800 mt-1">
                        <span className="font-medium">Sarah Johnson</span> and <span className="font-medium">Emily Davis</span> might know each other based on their company relationships.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="flex items-start">
                    <div className="bg-primary p-1.5 rounded-md">
                      <Building2 className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-xs font-medium text-gray-600">AI Insight</p>
                      <p className="text-sm text-gray-800 mt-1">
                        <span className="font-medium">TechCorp</span> and <span className="font-medium">Global Ltd</span> operate in similar industries and might benefit from introduction.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Relationships;
