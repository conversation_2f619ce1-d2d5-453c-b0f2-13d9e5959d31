import { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  getProposalTemplates,
  getProposals,
  createProposalTemplate,
  deleteProposalTemplate,
  generateProposal,
  generateProposalDocument,
  sendProposal,
  markProposalAsAccepted,
  markProposalAsRejected
} from '@/api/proposal-generator-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  FileText,
  Search,
  Plus,
  Sparkles,
  Download,
  Send,
  Loader2,
  Tag,
  Filter,
  Check,
  X,
  MoreHorizontal,
  Eye,
  Trash2,
  ExternalLink,
  Share2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Link } from 'wouter';
import { ProposalShareDialog } from '@/components/proposal-generator/ProposalShareDialog';

const ProposalGenerator = () => {
  const [activeTab, setActiveTab] = useState('proposals');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [deleteTemplateId, setDeleteTemplateId] = useState<string | null>(null);

  const { toast } = useToast();

  // Fetch proposals
  const {
    data: proposals,
    isLoading: isProposalsLoading,
    refetch: refetchProposals
  } = useQuery({
    queryKey: ['proposals', searchQuery, selectedStatus],
    queryFn: () => getProposals({
      search: searchQuery,
      status: selectedStatus || undefined
    })
  });

  // Fetch templates
  const {
    data: templates,
    isLoading: isTemplatesLoading,
    refetch: refetchTemplates
  } = useQuery({
    queryKey: ['proposal-templates', searchQuery, selectedCategory],
    queryFn: () => getProposalTemplates({
      search: searchQuery,
      category: selectedCategory || undefined
    })
  });

  // Delete template mutation
  const deleteTemplateMutation = useMutation({
    mutationFn: deleteProposalTemplate,
    onSuccess: () => {
      toast({
        title: 'Template Deleted',
        description: 'The template has been deleted successfully',
      });
      refetchTemplates();
    },
    onError: (error) => {
      toast({
        title: 'Deletion Failed',
        description: error instanceof Error ? error.message : 'Failed to delete template',
        variant: 'destructive',
      });
    }
  });

  // Generate document mutation
  const generateDocumentMutation = useMutation({
    mutationFn: ({ id, format }: { id: string; format: string }) =>
      generateProposalDocument(id, format as any),
    onSuccess: (data) => {
      if (data && 'documentUrl' in data) {
        // For downloadable formats, open in a new tab
        if ('isDownloadable' in data && data.isDownloadable) {
          window.open(data.documentUrl as string, '_blank');
        }

        toast({
          title: 'Document Generated',
          description: `Your proposal has been generated in ${('format' in data ? (data.format as string).toUpperCase() : 'PDF')} format${
            ('isDownloadable' in data && data.isDownloadable) ? ' and is ready to download' : ' and is ready to view'
          }`,
        });

        refetchProposals();
      }
    },
    onError: (error) => {
      toast({
        title: 'Document Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate document',
        variant: 'destructive',
      });
    }
  });

  // Send proposal mutation
  const sendProposalMutation = useMutation({
    mutationFn: sendProposal,
    onSuccess: () => {
      toast({
        title: 'Proposal Sent',
        description: 'Your proposal has been sent successfully',
      });
      refetchProposals();
    },
    onError: (error) => {
      toast({
        title: 'Sending Failed',
        description: error instanceof Error ? error.message : 'Failed to send proposal',
        variant: 'destructive',
      });
    }
  });

  // Accept proposal mutation
  const acceptProposalMutation = useMutation({
    mutationFn: markProposalAsAccepted,
    onSuccess: () => {
      toast({
        title: 'Proposal Accepted',
        description: 'The proposal has been marked as accepted',
      });
      refetchProposals();
    },
    onError: (error) => {
      toast({
        title: 'Action Failed',
        description: error instanceof Error ? error.message : 'Failed to accept proposal',
        variant: 'destructive',
      });
    }
  });

  // Reject proposal mutation
  const rejectProposalMutation = useMutation({
    mutationFn: (id: string) => markProposalAsRejected(id),
    onSuccess: () => {
      toast({
        title: 'Proposal Rejected',
        description: 'The proposal has been marked as rejected',
      });
      refetchProposals();
    },
    onError: (error) => {
      toast({
        title: 'Action Failed',
        description: error instanceof Error ? error.message : 'Failed to reject proposal',
        variant: 'destructive',
      });
    }
  });

  // Handle search
  const handleSearch = () => {
    if (activeTab === 'proposals') {
      refetchProposals();
    } else {
      refetchTemplates();
    }
  };

  // Handle delete template
  const handleDeleteTemplate = () => {
    if (deleteTemplateId) {
      deleteTemplateMutation.mutate(deleteTemplateId);
      setDeleteTemplateId(null);
    }
  };

  // Get badge color for category
  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case 'sales':
        return 'bg-blue-100 text-blue-800';
      case 'service':
        return 'bg-green-100 text-green-800';
      case 'partnership':
        return 'bg-purple-100 text-purple-800';
      case 'custom':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get badge color for status
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'viewed':
        return 'bg-purple-100 text-purple-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Proposal Generator</h1>
        <Button onClick={() => setActiveTab('templates')}>
          <Plus className="h-4 w-4 mr-2" />
          New Template
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="proposals">
            <FileText className="h-4 w-4 mr-2" />
            Proposals
          </TabsTrigger>
          <TabsTrigger value="templates">
            <Sparkles className="h-4 w-4 mr-2" />
            Templates
          </TabsTrigger>
        </TabsList>

        <TabsContent value="proposals" className="space-y-4">
          <div className="flex flex-col md:flex-row gap-2">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search proposals..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                />
              </div>
            </div>
            <div className="w-full md:w-[200px]">
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="All Statuses" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="viewed">Viewed</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleSearch}>
              Search
            </Button>
          </div>

          <Card>
            <CardHeader className="py-4">
              <CardTitle>Proposals</CardTitle>
            </CardHeader>
            <CardContent>
              {isProposalsLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : proposals?.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No proposals found</p>
                  <p className="text-sm">Try a different search or create a new proposal</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium">Name</th>
                        <th className="text-left py-3 px-4 font-medium">Status</th>
                        <th className="text-left py-3 px-4 font-medium">Value</th>
                        <th className="text-left py-3 px-4 font-medium">Created</th>
                        <th className="text-left py-3 px-4 font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {proposals?.map((proposal: any) => (
                        <tr key={proposal._id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="flex flex-col">
                              <span className="font-medium">{proposal.name}</span>
                              {proposal.description && (
                                <span className="text-sm text-gray-500">{proposal.description}</span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <Badge className={getStatusBadgeColor(proposal.status)}>
                              {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
                            </Badge>
                          </td>
                          <td className="py-3 px-4">
                            {proposal.currency} {proposal.value.toLocaleString()}
                          </td>
                          <td className="py-3 px-4">
                            {new Date(proposal.createdAt).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              {proposal.documentUrl && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => window.open(proposal.documentUrl, '_blank')}
                                  >
                                    <ExternalLink className="h-4 w-4" />
                                  </Button>

                                  <ProposalShareDialog
                                    proposalId={proposal._id}
                                    proposalName={proposal.name}
                                    availableFormats={proposal.availableFormats}
                                    trigger={
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        data-share-id={proposal._id}
                                      >
                                        <Share2 className="h-4 w-4" />
                                      </Button>
                                    }
                                  />
                                </>
                              )}

                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  {proposal.documentUrl ? (
                                    <DropdownMenuItem
                                      onClick={() => window.open(proposal.documentUrl, '_blank')}
                                    >
                                      {['pdf', 'docx', 'markdown'].includes(proposal.format || '') ? (
                                        <>
                                          <Download className="h-4 w-4 mr-2" />
                                          Download {proposal.format?.toUpperCase()}
                                        </>
                                      ) : (
                                        <>
                                          <ExternalLink className="h-4 w-4 mr-2" />
                                          View Document
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                  ) : (
                                    <DropdownMenuItem
                                      onClick={() => {
                                        // Open format selector dialog
                                        const formatSelector = document.createElement('div');
                                        formatSelector.id = `format-selector-${proposal._id}`;
                                        document.body.appendChild(formatSelector);

                                        // We're using a simple approach here since we can't render React components directly
                                        // In a real implementation, you would use a proper React portal or state management
                                        const format = window.prompt(
                                          'Select format (pdf, docx, markdown, claude-html):',
                                          'pdf'
                                        );

                                        if (format && ['pdf', 'docx', 'markdown', 'claude-html'].includes(format)) {
                                          generateDocumentMutation.mutate({
                                            id: proposal._id,
                                            format
                                          });
                                        }

                                        // Clean up
                                        if (formatSelector) {
                                          document.body.removeChild(formatSelector);
                                        }
                                      }}
                                    >
                                      <Download className="h-4 w-4 mr-2" />
                                      Generate Document
                                    </DropdownMenuItem>
                                  )}

                                  {proposal.availableFormats && proposal.availableFormats.length > 0 && (
                                    <DropdownMenuItem
                                      onClick={() => {
                                        // Show format options with current formats highlighted
                                        const availableFormats = proposal.availableFormats.map((f: string) => f.toUpperCase()).join(', ');
                                        const format = window.prompt(
                                          `Current formats: ${availableFormats}\nSelect another format to generate (pdf, docx, markdown, claude-html):`,
                                          'pdf'
                                        );

                                        if (format && ['pdf', 'docx', 'markdown', 'claude-html'].includes(format)) {
                                          generateDocumentMutation.mutate({
                                            id: proposal._id,
                                            format
                                          });
                                        }
                                      }}
                                    >
                                      <FileText className="h-4 w-4 mr-2" />
                                      Generate Another Format
                                    </DropdownMenuItem>
                                  )}

                                  {proposal.status === 'draft' && proposal.documentUrl && (
                                    <DropdownMenuItem
                                      onClick={() => sendProposalMutation.mutate(proposal._id)}
                                    >
                                      <Send className="h-4 w-4 mr-2" />
                                      Send Proposal
                                    </DropdownMenuItem>
                                  )}

                                  {proposal.documentUrl && (
                                    <DropdownMenuItem
                                      onClick={() => {
                                        // Close the dropdown menu
                                        const closeEvent = new MouseEvent('click', {
                                          bubbles: true,
                                          cancelable: true,
                                          view: window
                                        });
                                        document.dispatchEvent(closeEvent);

                                        // Open the share dialog programmatically
                                        const shareButton = document.querySelector(`button[data-share-id="${proposal._id}"]`) as HTMLButtonElement;
                                        if (shareButton) {
                                          shareButton.click();
                                        }
                                      }}
                                    >
                                      <Share2 className="h-4 w-4 mr-2" />
                                      Share Proposal
                                    </DropdownMenuItem>
                                  )}

                                  {(proposal.status === 'sent' || proposal.status === 'viewed') && (
                                    <>
                                      <DropdownMenuItem
                                        onClick={() => acceptProposalMutation.mutate(proposal._id)}
                                      >
                                        <Check className="h-4 w-4 mr-2" />
                                        Mark as Accepted
                                      </DropdownMenuItem>

                                      <DropdownMenuItem
                                        onClick={() => rejectProposalMutation.mutate(proposal._id)}
                                      >
                                        <X className="h-4 w-4 mr-2" />
                                        Mark as Rejected
                                      </DropdownMenuItem>
                                    </>
                                  )}

                                  <DropdownMenuItem asChild>
                                    <Link href={`/opportunities/${proposal.opportunityId}`}>
                                      <ExternalLink className="h-4 w-4 mr-2" />
                                      View Opportunity
                                    </Link>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="flex flex-col md:flex-row gap-2">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search templates..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                />
              </div>
            </div>
            <div className="w-full md:w-[200px]">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="All Categories" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Categories</SelectItem>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="service">Service</SelectItem>
                  <SelectItem value="partnership">Partnership</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleSearch}>
              Search
            </Button>
          </div>

          <Card>
            <CardHeader className="py-4">
              <CardTitle>Templates</CardTitle>
            </CardHeader>
            <CardContent>
              {isTemplatesLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : templates?.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No templates found</p>
                  <p className="text-sm">Try a different search or create a new template</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium">Name</th>
                        <th className="text-left py-3 px-4 font-medium">Category</th>
                        <th className="text-left py-3 px-4 font-medium">Usage</th>
                        <th className="text-left py-3 px-4 font-medium">Default</th>
                        <th className="text-left py-3 px-4 font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {templates?.map((template: any) => (
                        <tr key={template._id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="flex flex-col">
                              <span className="font-medium">{template.name}</span>
                              {template.description && (
                                <span className="text-sm text-gray-500">{template.description}</span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <Badge className={getCategoryBadgeColor(template.category)}>
                              {template.category.charAt(0).toUpperCase() + template.category.slice(1)}
                            </Badge>
                          </td>
                          <td className="py-3 px-4">
                            {template.usageCount} uses
                            {template.successCount > 0 && ` (${template.successCount} successful)`}
                          </td>
                          <td className="py-3 px-4">
                            {template.isDefault ? (
                              <Badge variant="outline" className="bg-green-50">
                                <Check className="h-3 w-3 mr-1" />
                                Default
                              </Badge>
                            ) : (
                              <span className="text-gray-500">-</span>
                            )}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setDeleteTemplateId(template._id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>

                              <Button
                                variant="ghost"
                                size="icon"
                                asChild
                              >
                                <Link href={`/proposal-templates/${template._id}`}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Template Confirmation Dialog */}
      <AlertDialog open={!!deleteTemplateId} onOpenChange={(open) => !open && setDeleteTemplateId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the template
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteTemplate}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ProposalGenerator;
