/**
 * Types for the ProposalGeneratorDialog component
 */

import { Id } from '@shared/types/utils';

/**
 * Props for the ProposalGeneratorDialog component
 */
export interface ProposalGeneratorDialogProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  opportunityName?: string;
  contactName?: string;
  companyName?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
}

/**
 * Template interface
 */
export interface Template {
  _id: string;
  name: string;
  category: string;
  description?: string;
  tags: string[];
  isDefault: boolean;
  usageCount: number;
  successCount: number;
}

/**
 * Proposal interface
 */
export interface Proposal {
  _id: string;
  name: string;
  description?: string;
  opportunityId: string;
  contactId?: string;
  companyId?: string;
  templateId?: string;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  status: string;
  value: number;
  currency: string;
  validUntil?: string;
  sentAt?: string;
  viewedAt?: string;
  acceptedAt?: string;
  rejectedAt?: string;
  documentUrl?: string;
  documentId?: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
  format?: string;
  availableFormats?: string[];
}

/**
 * Proposal generation response from shared types
 */
export interface ProposalGenerationResponseShared {
  _id: string;
  name: string;
  description?: string;
  opportunityId: string;
  contactId?: string;
  companyId?: string;
  templateId?: string;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  status: string;
  value: number;
  currency: string;
  validUntil?: string;
  sentAt?: string;
  viewedAt?: string;
  acceptedAt?: string;
  rejectedAt?: string;
  documentUrl?: string;
  documentId?: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
}

/**
 * Proposal generation response
 */
export type ProposalGenerationResponse = ProposalGenerationResponseShared;

/**
 * Proposal export response from shared types
 */
export interface ProposalExportResponseShared {
  documentUrl: string;
  expiresAt: Date;
  format: string;
  isDownloadable: boolean;
}

/**
 * Proposal export response
 */
export type ProposalExportResponse = ProposalExportResponseShared;
