/**
 * Analytics types for the client-side application
 */

/**
 * Analytics event type
 */
export type AnalyticsEventType = 
  | 'page'
  | 'user'
  | 'feature'
  | 'form'
  | 'engagement'
  | 'conversion'
  | 'error'
  | 'custom';

/**
 * Analytics event data
 */
export interface AnalyticsEventData {
  [key: string]: any;
}

/**
 * Analytics event page context
 */
export interface AnalyticsEventPage {
  url: string;
  path: string;
  title?: string;
  referrer?: string;
}

/**
 * Analytics event user context
 */
export interface AnalyticsEventUser {
  id?: string;
  email?: string;
  tenantId?: string;
  role?: string;
  [key: string]: any;
}

/**
 * Analytics event context
 */
export interface AnalyticsEventContext {
  page?: AnalyticsEventPage;
  user?: AnalyticsEventUser;
  [key: string]: any;
}

/**
 * Track event request
 */
export interface TrackEventRequest {
  eventType: string;
  eventName: string;
  properties?: Record<string, any>;
  sessionId?: string;
  context?: AnalyticsEventContext;
}

/**
 * Track event response
 */
export interface TrackEventResponse {
  success: boolean;
  eventId?: string;
  timestamp?: string;
}

/**
 * Analytics filter
 */
export interface AnalyticsFilter {
  field: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'not_contains' | 'in' | 'not_in';
  value: any;
}

/**
 * Analytics timeframe
 */
export interface AnalyticsTimeframe {
  start: string;
  end: string;
  granularity: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
}

/**
 * Analytics metric
 */
export interface AnalyticsMetric {
  name: string;
  aggregation: 'count' | 'sum' | 'avg' | 'min' | 'max' | 'distinct';
  field?: string;
}

/**
 * Analytics dimension
 */
export interface AnalyticsDimension {
  name: string;
  field: string;
}

/**
 * Analytics chart type
 */
export type AnalyticsChartType = 
  | 'line'
  | 'bar'
  | 'pie'
  | 'area'
  | 'scatter'
  | 'table'
  | 'number';

/**
 * Analytics data point
 */
export interface AnalyticsDataPoint {
  [key: string]: any;
}

/**
 * Analytics query
 */
export interface AnalyticsQuery {
  metrics: AnalyticsMetric[];
  dimensions?: AnalyticsDimension[];
  filters?: AnalyticsFilter[];
  timeframe?: AnalyticsTimeframe;
  limit?: number;
  offset?: number;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

/**
 * Analytics result
 */
export interface AnalyticsResult {
  data: AnalyticsDataPoint[];
  total?: number;
  metadata?: {
    [key: string]: any;
  };
}

/**
 * Analytics insight
 */
export interface AnalyticsInsight {
  type: 'trend' | 'anomaly' | 'correlation' | 'recommendation';
  title: string;
  description: string;
  importance: 'low' | 'medium' | 'high';
  data?: any;
}
