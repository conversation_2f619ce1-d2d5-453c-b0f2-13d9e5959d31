/**
 * Proposal Generator types for the client-side application
 * These types represent the data structures used by the Proposal Generator feature
 */

import { Id } from '@shared/types/utils';

/**
 * Industry-specific legal clause
 */
export interface IndustrySpecificClause {
  id: string;
  name: string;
  text: string; // Changed from content to text to match component usage
  industry: string;
  category: string;
  required: boolean;
  description?: string;
}

/**
 * Props for the LegalClausePicker component
 */
export interface LegalClausePickerProps {
  proposalId: string;
  companyId: string;
  industrySpecificClauses?: IndustrySpecificClause[];
  onSuccess?: () => void;
}

/**
 * Proposal export format
 */
export type ProposalExportFormat =
  | 'html'
  | 'pdf'
  | 'docx'
  | 'markdown'
  | 'claude-html';

/**
 * Proposal download options
 */
export interface ProposalDownloadOptions {
  format: ProposalExportFormat;
  includeCompanyLogo?: boolean;
  includeSignaturePage?: boolean;
  includeAttachments?: boolean;
  customHeader?: string;
  customFooter?: string;
  watermark?: string;
}

/**
 * Proposal share request
 */
export interface ProposalShareRequest {
  proposalId: string;
  expiresAt?: Date;
  password?: string;
  format?: ProposalExportFormat;
  email?: string;
}

/**
 * Proposal share response
 */
export interface ProposalShareResponse {
  shareId: string;
  shareUrl: string;
  expiresAt?: Date;
  documentUrl?: string;
  isDownloadable?: boolean;
  format?: ProposalExportFormat;
}

/**
 * Proposal email request
 */
export interface ProposalEmailRequest {
  proposalId: string;
  email: string;
  subject?: string;
  message?: string;
  format?: ProposalExportFormat;
  includeAttachment?: boolean;
}

/**
 * Proposal email response
 */
export interface ProposalEmailResponse {
  success: boolean;
  messageId?: string;
  sentAt: Date;
}

/**
 * Proposal clauses update request
 */
export interface ProposalClausesUpdateRequest {
  proposalId: string;
  clauses: string[];
}

/**
 * Proposal clauses update response
 */
export interface ProposalClausesUpdateResponse {
  success: boolean;
  proposal: {
    id: string;
    sections: Array<{
      id: string;
      name: string;
      content: string;
    }>;
  };
}

/**
 * Proposal view
 */
export interface ProposalView {
  id: string;
  proposalId: string;
  viewedAt: Date;
  viewDuration?: number;
  ip?: string;
  userAgent?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
}

/**
 * Proposal share
 */
export interface ProposalShare {
  id: string;
  proposalId: string;
  token: string;
  url: string;
  expiresAt?: Date;
  createdBy: string;
  createdAt: Date;
  accessCount: number;
  lastAccessedAt?: Date;
}

/**
 * Proposal social export request
 */
export interface ProposalSocialExportRequest {
  proposalId: string;
  platform: 'linkedin' | 'twitter' | 'facebook';
  message?: string;
}

/**
 * Proposal social export response
 */
export interface ProposalSocialExportResponse {
  success: boolean;
  postId?: string;
  postUrl?: string;
  postedAt: Date;
}
