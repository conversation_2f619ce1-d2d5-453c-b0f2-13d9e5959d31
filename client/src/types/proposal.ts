import { z } from 'zod';

// Proposal template schema
export const proposalTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  content: z.string(),
  createdAt: z.string().or(z.date()),
  updatedAt: z.string().or(z.date()),
  isDefault: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export type ProposalTemplate = z.infer<typeof proposalTemplateSchema>;

// Proposal schema
export const proposalSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  content: z.string(),
  createdAt: z.string().or(z.date()),
  updatedAt: z.string().or(z.date()),
  createdBy: z.string(),
  tenantId: z.string(),
  opportunityId: z.string().optional(),
  contactId: z.string().optional(),
  templateId: z.string().optional(),
  aiModel: z.string().optional(),
  status: z.enum(['draft', 'sent', 'viewed', 'accepted', 'rejected']).optional(),
  metadata: z.record(z.any()).optional(),
});

export type Proposal = z.infer<typeof proposalSchema>;

// Proposal generation request schema
export const proposalGenerationRequestSchema = z.object({
  title: z.string(),
  description: z.string(),
  aiModel: z.string(),
  executiveSummary: z.string().optional(),
  solutionDescription: z.string().optional(),
  projectTimeline: z.string().optional(),
  pricingDetails: z.string().optional(),
  teamIntroduction: z.string().optional(),
  clientTestimonials: z.string().optional(),
  termsAndConditions: z.string().optional(),
  opportunityId: z.string().optional(),
  contactId: z.string().optional(),
  templateId: z.string().optional(),
  contextInfo: z.record(z.string()).optional(),
});

export type ProposalGenerationRequest = z.infer<typeof proposalGenerationRequestSchema>;

// Proposal document generation request schema
export const proposalDocumentRequestSchema = z.object({
  proposalId: z.string(),
  format: z.enum(['pdf', 'docx', 'markdown', 'html', 'claude-html']),
  includeCompanyLogo: z.boolean().optional(),
  includeSignaturePage: z.boolean().optional(),
  includeAttachments: z.boolean().optional(),
  customHeader: z.string().optional(),
  customFooter: z.string().optional(),
  watermark: z.string().optional(),
});

export type ProposalDocumentRequest = z.infer<typeof proposalDocumentRequestSchema>;

// Proposal document response schema
export const proposalDocumentResponseSchema = z.object({
  documentUrl: z.string(),
  format: z.enum(['pdf', 'docx', 'markdown', 'html', 'claude-html']),
  expiresAt: z.string().or(z.date()).optional(),
});

export type ProposalDocumentResponse = z.infer<typeof proposalDocumentResponseSchema>;

// Proposal share request schema
export const proposalShareRequestSchema = z.object({
  proposalId: z.string(),
  email: z.string().email().optional(),
  message: z.string().optional(),
  trackViews: z.boolean().optional(),
  expirationDays: z.number().optional(),
  format: z.enum(['pdf', 'docx', 'markdown', 'html', 'claude-html']).optional(),
});

export type ProposalShareRequest = z.infer<typeof proposalShareRequestSchema>;

// Proposal share response schema
export const proposalShareResponseSchema = z.object({
  shareId: z.string(),
  shareUrl: z.string(),
  expiresAt: z.string().or(z.date()),
});

export type ProposalShareResponse = z.infer<typeof proposalShareResponseSchema>;
