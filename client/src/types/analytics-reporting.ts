/**
 * Analytics & Reporting types
 */

// Event types
export interface EventsRawType {
  id: string;
  tenant_id: string;
  visitor_id?: string;
  contact_id?: string;
  timestamp: string;
  channel: 'web' | 'email' | 'ads' | 'social' | 'voice' | 'offline';
  campaign?: string;
  medium?: string;
  source?: string;
  event_type: string;
  meta_json: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Attribution types
export interface AttributionResultsType {
  id: string;
  tenant_id: string;
  window: string;
  model_type: 'markov' | 'shapley' | 'first_touch' | 'last_touch' | 'linear';
  channel: string;
  campaign?: string;
  medium?: string;
  source?: string;
  creative?: string;
  keyword?: string;
  credit_pct: number;
  cost: number;
  revenue: number;
  roi: number;
  conversions: number;
  created_at: string;
  updated_at: string;
}

// Chart types
export interface ChartSpecType {
  type: string;
  data: any;
  options: Record<string, any>;
}

// Suggested experiment types
export interface SuggestedExperimentType {
  title: string;
  description: string;
  estimated_impact: string;
  confidence: number;
  workflow_template?: Record<string, any>;
}

// CBI types
export interface CBICacheType {
  id: string;
  tenant_id: string;
  question: string;
  question_hash: string;
  result_json: Record<string, any>;
  chart_spec: ChartSpecType;
  narrative: string;
  root_cause_analysis?: string;
  suggested_experiments?: SuggestedExperimentType[];
  dataset_ref: string;
  query_type: 'sql' | 'cypher' | 'vector';
  raw_query?: string;
  execution_time_ms: number;
  created_at: string;
  expires_at: string;
  source?: 'cache' | 'generated';
}

// Dataset field types
export interface DatasetFieldType {
  name: string;
  display_name: string;
  data_type: 'string' | 'number' | 'date' | 'boolean';
  description: string;
  is_dimension: boolean;
  is_metric: boolean;
  format?: 'currency' | 'percentage' | 'date' | 'number' | 'text';
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max' | 'distinct';
}

// Analytics dataset types
export interface AnalyticsDatasetType {
  id: string;
  tenant_id: string;
  name: string;
  display_name: string;
  description: string;
  source_type: 'bigquery' | 'mongodb' | 'neo4j';
  source_config: Record<string, any>;
  fields: DatasetFieldType[];
  is_active: boolean;
  refresh_frequency: 'hourly' | 'daily' | 'weekly';
  last_refreshed_at?: string;
  created_at: string;
  updated_at: string;
}

// Campaign cost types
export interface CampaignCostType {
  date: string;
  amount: number;
  currency: string;
  source: 'manual' | 'api';
}

// Marketing campaign types
export interface MarketingCampaignType {
  id: string;
  tenant_id: string;
  name: string;
  description: string;
  channel: 'web' | 'email' | 'ads' | 'social' | 'voice' | 'offline';
  platform?: string;
  utm_campaign?: string;
  utm_medium?: string;
  utm_source?: string;
  start_date: string;
  end_date?: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  budget: number;
  currency: string;
  costs: CampaignCostType[];
  total_cost: number;
  total_impressions: number;
  total_clicks: number;
  total_conversions: number;
  conversion_value: number;
  tags: string[];
  created_by: string;
  created_at: string;
  updated_at: string;
}

// API request/response types

// Conversational BI
export interface AskBIRequest {
  question: string;
  filters?: Record<string, any>;
  dataset_ref?: string;
  refresh?: boolean;
  tenant_id: string;
}

export interface AskBIResponse {
  success: boolean;
  insight: CBICacheType;
  source: 'cache' | 'generated';
  message?: string;
}

// Attribution
export interface RunAttributionRequest {
  window: string;
  model_type: 'markov' | 'shapley' | 'first_touch' | 'last_touch' | 'linear';
  tenant_id: string;
  force_refresh?: boolean;
}

export interface RunAttributionResponse {
  success: boolean;
  job_id?: string;
  message?: string;
}

export interface GetAttributionResultsRequest {
  window: string;
  model_type: 'markov' | 'shapley' | 'first_touch' | 'last_touch' | 'linear';
  tenant_id: string;
  group_by?: 'channel' | 'campaign' | 'medium' | 'source' | 'creative' | 'keyword';
  limit?: number;
  sort_by?: 'credit_pct' | 'roi' | 'revenue' | 'cost' | 'conversions';
  sort_order?: 'asc' | 'desc';
}

export interface GetAttributionResultsResponse {
  success: boolean;
  results: AttributionResultsType[];
  summary: {
    total_cost: number;
    total_revenue: number;
    total_conversions: number;
    average_roi: number;
  };
  message?: string;
}

// Budget optimization
export interface BudgetOptimizationRequest {
  window: string;
  model_type: 'markov' | 'shapley';
  tenant_id: string;
  optimization_target: 'revenue' | 'conversions' | 'roi';
  budget_constraint?: number;
}

export interface BudgetOptimizationResponse {
  success: boolean;
  recommendations: {
    channel: string;
    campaign?: string;
    current_budget: number;
    recommended_budget: number;
    change_amount: number;
    change_percentage: number;
    projected_impact: {
      revenue: number;
      conversions: number;
      roi: number;
    };
  }[];
  summary: {
    total_current_budget: number;
    total_recommended_budget: number;
    projected_revenue_increase: number;
    projected_conversion_increase: number;
    projected_roi_increase: number;
  };
  message?: string;
}

// Events collection
export interface TrackEventRequest {
  tenant_id: string;
  visitor_id?: string;
  contact_id?: string;
  timestamp?: string;
  channel: 'web' | 'email' | 'ads' | 'social' | 'voice' | 'offline';
  campaign?: string;
  medium?: string;
  source?: string;
  event_type: string;
  meta_json: Record<string, any>;
}

export interface TrackEventResponse {
  success: boolean;
  event_id?: string;
  message?: string;
}

// Datasets
export interface GetDatasetsRequest {
  tenant_id: string;
  is_active?: boolean;
}

export interface GetDatasetsResponse {
  success: boolean;
  datasets: AnalyticsDatasetType[];
  message?: string;
}
