/**
 * Core entity types for the client-side of Aizako CRM
 * These types represent the fundamental data models used throughout the application
 */

/**
 * ID type
 */
export type Id = string;

/**
 * Contact entity
 */
export interface Contact {
  id: Id;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  companyId?: Id;
  companyName?: string;
  status?: 'active' | 'inactive' | 'lead' | 'customer' | 'prospect';
  source?: string;
  tags?: string[];
  notes?: string;
  lastContactedAt?: Date;
  aiEnrichment?: {
    linkedinProfile?: string;
    twitterHandle?: string;
    companyRole?: string;
    interests?: string[];
    recentNews?: string[];
    enrichmentDate?: Date;
    enrichmentSource?: string;
    confidence?: number;
  };
  persona?: {
    summary?: string;
    communicationPreferences?: {
      preferredChannel?: string;
      bestTimeToContact?: string;
      responseTime?: string;
    };
    interests?: string[];
    painPoints?: string[];
    decisionFactors?: string[];
    aiConfidence?: number;
    lastUpdated?: Date;
  };
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Company entity
 */
export interface Company {
  id: Id;
  name: string;
  website?: string;
  industry?: string;
  size?: string;
  revenue?: string;
  location?: string;
  description?: string;
  logo?: string;
  status?: 'active' | 'inactive' | 'lead' | 'customer' | 'prospect';
  tags?: string[];
  notes?: string;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Opportunity entity
 */
export interface Opportunity {
  id: Id;
  name: string;
  description?: string;
  value: number;
  currency?: string;
  stage: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: Date;
  probability?: number;
  contactId?: Id;
  companyId?: Id;
  notes?: string;
  aiInsights?: Record<string, any>;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Activity entity
 */
export interface Activity {
  id: Id;
  type: 'call' | 'email' | 'meeting' | 'task' | 'note' | 'other';
  title: string;
  description?: string;
  date: Date;
  duration?: number;
  status?: 'scheduled' | 'completed' | 'canceled';
  outcome?: string;
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  createdBy?: Id;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Task entity
 */
export interface Task {
  id: Id;
  title: string;
  description?: string;
  dueDate?: Date;
  priority?: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed' | 'canceled';
  assignedTo?: Id;
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Document entity
 */
export interface Document {
  id: Id;
  name: string;
  type: string;
  url: string;
  size?: number;
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  tags?: string[];
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User entity
 */
export interface User {
  id: Id;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'user' | 'manager' | 'viewer';
  status: 'active' | 'inactive' | 'pending';
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Proposal entity
 */
export interface Proposal {
  id: Id;
  name: string;
  description?: string;
  opportunityId: Id;
  contactId?: Id;
  companyId?: Id;
  templateId: Id;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  value: number;
  currency: string;
  validUntil?: Date;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected';
  tags: string[];
  notes?: string;
  customFields?: Record<string, any>;
  createdBy: Id;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Tenant entity
 */
export interface Tenant {
  id: Id;
  name: string;
  slug: string;
  status: 'active' | 'inactive' | 'trial';
  plan: string;
  settings?: {
    timezone?: string;
    locale?: string;
    branding?: {
      logo?: string;
      primaryColor?: string;
      accentColor?: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Subscription entity
 */
export interface Subscription {
  id: Id;
  tenantId: Id;
  planId: string;
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'expired';
  startDate: Date;
  endDate?: Date;
  trialEndsAt?: Date;
  canceledAt?: Date;
  customFeatures?: Record<string, boolean>;
  customLimits?: Record<string, number>;
  billingDetails?: {
    customerId?: string;
    subscriptionId?: string;
    paymentMethodId?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

// No need to re-export types that are already exported above
