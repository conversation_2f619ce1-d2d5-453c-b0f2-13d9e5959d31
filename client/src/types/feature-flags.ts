/**
 * Feature flag types for the client-side application
 */

/**
 * Feature flag environment
 */
export type FeatureFlagEnvironment = 'development' | 'staging' | 'production';

/**
 * Feature flag type
 */
export type FeatureFlagType = 'boolean' | 'string' | 'number' | 'json';

/**
 * Feature flag rule type
 */
export type FeatureFlagRuleType = 'user' | 'tenant' | 'percentage' | 'date' | 'time' | 'custom';

/**
 * Feature flag rule operator
 */
export type FeatureFlagRuleOperator = 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex';

/**
 * Feature flag rule
 */
export interface FeatureFlagRule {
  id: string;
  type: FeatureFlagRuleType;
  property?: string;
  operator: FeatureFlagRuleOperator;
  value: any;
  negate?: boolean;
}

/**
 * Feature flag
 */
export interface FeatureFlag {
  id: string;
  key: string;
  name: string;
  description?: string;
  type: FeatureFlagType;
  defaultValue: any;
  rules?: FeatureFlagRule[];
  environments?: Record<FeatureFlagEnvironment, boolean>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Check feature flag request
 */
export interface CheckFeatureFlagRequest {
  key: string;
  userId?: string;
  tenantId?: string;
  context?: Record<string, any>;
}

/**
 * Check feature flag response
 */
export interface CheckFeatureFlagResponse {
  key: string;
  enabled: boolean;
  value?: any;
  ruleId?: string;
}

/**
 * Check feature flags request
 */
export interface CheckFeatureFlagsRequest {
  keys: string[];
  userId?: string;
  tenantId?: string;
  context?: Record<string, any>;
}

/**
 * Check feature flags response
 */
export interface CheckFeatureFlagsResponse {
  flags: Record<string, boolean>;
  values?: Record<string, any>;
  ruleIds?: Record<string, string>;
}
