/**
 * Objection Handler types for the client-side of Aizako CRM
 * These types represent the data structures used by the Objection Handler feature
 */

import { Id } from './core';

/**
 * Objection classification
 */
export type ObjectionClass =
  | 'price'
  | 'value'
  | 'competition'
  | 'timing'
  | 'authority'
  | 'need'
  | 'trust'
  | 'implementation'
  | 'technical'
  | 'legal'
  | 'other';

/**
 * Objection category
 */
export interface ObjectionCategory {
  id: Id;
  name: string;
  description?: string;
  color?: string;
  objectionCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Objection classification request
 */
export interface ObjectionClassificationRequest {
  objectionText: string;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
}

/**
 * Objection classification response
 */
export interface ObjectionClassificationResponse {
  objectionClass: ObjectionClass;
  confidence: number;
  alternativeClasses?: Array<{
    objectionClass: ObjectionClass;
    confidence: number;
  }>;
}

/**
 * Rebuttal
 */
export interface Rebuttal {
  id: string;
  text: string;
  winRate: number;
  evidence?: string[];
}

/**
 * Supporting evidence
 */
export interface SupportingEvidence {
  id: string;
  title: string;
  type: string;
  description: string;
  url?: string;
}

/**
 * Real-time objection handling request
 */
export interface RealTimeObjectionRequest {
  objectionText: string;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
}

/**
 * Real-time objection handling response
 */
export interface RealTimeObjectionResponse {
  responseId: string;
  objectionClass: ObjectionClass;
  response: string;
  rebuttals: Rebuttal[];
  supportingEvidence: SupportingEvidence[];
}

/**
 * Voice objection request
 */
export interface VoiceObjectionRequest {
  audioData: Blob;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
}

/**
 * Voice objection response
 */
export interface VoiceObjectionResponse {
  transcription: string;
  response: RealTimeObjectionResponse;
}

/**
 * Response usage record request
 */
export interface ResponseUsageRequest {
  responseId: string;
  wasSuccessful: boolean;
  notes?: string;
}

/**
 * Response usage record response
 */
export interface ResponseUsageResponse {
  id: string;
  responseId: string;
  wasSuccessful: boolean;
  timestamp: Date;
}

/**
 * Objection database record
 */
export interface Objection {
  id: Id;
  text: string;
  class: ObjectionClass;
  categoryId?: Id;
  opportunityId?: Id;
  contactId?: Id;
  companyId?: Id;
  userId: Id;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Response database record
 */
export interface ObjectionResponse {
  id: Id;
  objectionId: Id;
  text: string;
  wasSuccessful?: boolean;
  usageCount: number;
  successCount: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Objection handler request
 */
export interface ObjectionHandlerRequest {
  objectionText: string;
  objectionClass?: ObjectionClass;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  includeEvidence?: boolean;
  maxResponses?: number;
}

/**
 * Objection handler response
 */
export interface ObjectionHandlerResponse {
  objectionClass: ObjectionClass;
  confidence: number;
  responses: Array<{
    id: string;
    text: string;
    confidence: number;
    evidence?: SupportingEvidence[];
  }>;
}

/**
 * Props for RealTimeObjectionHandler component
 */
export interface RealTimeObjectionHandlerProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  onSuccess?: (response: RealTimeObjectionResponse) => void;
}

/**
 * Props for VoiceObjectionHandler component
 */
export interface VoiceObjectionHandlerProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  onSuccess?: (response: RealTimeObjectionResponse) => void;
}

// No need to re-export types that are already exported above
