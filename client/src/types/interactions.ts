/**
 * Types for interactions
 */

export interface Interaction {
  id: string;
  type: 'email' | 'call' | 'meeting' | 'chat' | 'social' | 'note' | 'task' | 'other';
  source: string;
  sourceId?: string;
  timestamp: string;
  summary: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  direction?: 'inbound' | 'outbound';
  participants?: Array<{
    id?: string;
    email?: string;
    name?: string;
    role?: string;
  }>;
  content?: {
    text?: string;
    html?: string;
    attachments?: Array<{
      name: string;
      type: string;
      url?: string;
    }>;
  };
  nextAction?: {
    type: 'email' | 'call' | 'meeting' | 'task' | 'other';
    description: string;
    dueDate?: string;
    priority?: 'low' | 'medium' | 'high';
  };
  aiGenerated: boolean;
  aiConfidence?: number;
}
