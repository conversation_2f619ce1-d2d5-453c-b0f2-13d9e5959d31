/**
 * Experiment types for the client-side application
 */

/**
 * Experiment type
 */
export type ExperimentType = 'a/b' | 'multivariate' | 'feature_flag';

/**
 * Experiment status
 */
export type ExperimentStatus = 'draft' | 'running' | 'paused' | 'completed' | 'archived';

/**
 * Experiment variant
 */
export interface ExperimentVariant {
  id: string;
  name: string;
  description?: string;
  weight: number;
  isControl?: boolean;
}

/**
 * Experiment goal
 */
export interface ExperimentGoal {
  id: string;
  key: string;
  name: string;
  description?: string;
  isPrimary?: boolean;
}

/**
 * Experiment user
 */
export interface ExperimentUser {
  id: string;
  userId?: string;
  anonymousId?: string;
  attributes?: Record<string, any>;
}

/**
 * Experiment config
 */
export interface ExperimentConfig {
  id: string;
  key: string;
  name: string;
  description?: string;
  type: ExperimentType;
  status: ExperimentStatus;
  variants: ExperimentVariant[];
  goals: ExperimentGoal[];
  targetingConditions?: Record<string, any>;
  startDate?: string;
  endDate?: string;
  sampleSize?: number;
  metadata?: Record<string, any>;
}

/**
 * Funnel step
 */
export interface FunnelStep {
  id: string;
  name: string;
  description?: string;
  order: number;
  eventKey: string;
  conditions?: Record<string, any>;
}

/**
 * Get variant request
 */
export interface GetVariantRequest {
  experimentKey: string;
  userId?: string;
  anonymousId?: string;
  attributes?: Record<string, any>;
}

/**
 * Get variant response
 */
export interface GetVariantResponse {
  experimentId: string;
  experimentKey: string;
  variant: string;
  isControl: boolean;
}

/**
 * Track conversion request
 */
export interface TrackConversionRequest {
  goalKey: string;
  value?: number;
  metadata?: Record<string, any>;
}

/**
 * Track conversion response
 */
export interface TrackConversionResponse {
  success: boolean;
  conversionId?: string;
  timestamp?: string;
}

/**
 * Experiment result variant
 */
export interface ExperimentResultVariant {
  name: string;
  participants: number;
  conversions: number;
  conversionRate: number;
  improvement?: number;
  pValue?: number;
  isWinner?: boolean;
  isLoser?: boolean;
}

/**
 * Experiment result goal
 */
export interface ExperimentResultGoal {
  key: string;
  name: string;
  variants: ExperimentResultVariant[];
}

/**
 * Get results response
 */
export interface GetResultsResponse {
  experimentId: string;
  experimentKey: string;
  status: ExperimentStatus;
  startDate: string;
  endDate?: string;
  totalParticipants: number;
  goals: ExperimentResultGoal[];
  hasSignificantResult: boolean;
  winningVariant?: string;
  metadata?: Record<string, any>;
}
