import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "@/pages/not-found";
import AppShell from "@/components/layout/AppShell";
import Dashboard from "@/pages/Dashboard";
import Contacts from "@/pages/Contacts";
import ContactDetail from "@/pages/ContactDetail";
import Companies from "@/pages/Companies";
import Opportunities from "@/pages/Opportunities";
import OpportunityDetail from "@/pages/OpportunityDetail";
import AIAssistant from "@/pages/AIAssistant";
import AIArchitecture from "@/pages/AIArchitecture";
import Relationships from "@/pages/Relationships";
import Settings from "@/pages/Settings";
import SignIn from "@/pages/SignIn";
import ObjectionHandler from "@/pages/ObjectionHandler";
import ProposalGenerator from "@/pages/ProposalGenerator";
import ProposalAnalytics from "@/pages/ProposalAnalytics";
import EmailConfiguration from "@/pages/EmailConfiguration";
import SharedProposal from "@/pages/SharedProposal";
import SignUp from "@/pages/SignUp";
import WorkflowBuilder from "@/pages/WorkflowBuilder";
import PredictiveInsights from "@/pages/PredictiveInsights";
import ConversationalBI from "@/pages/ConversationalBI";
import AttributionDashboard from "@/pages/AttributionDashboard";
import { AuthProvider, useAuth } from "@/lib/auth";
import { CopilotKitProvider } from "@/components/ai/CopilotKitProvider";
import { useState, useEffect } from "react";

// Protected route wrapper
function ProtectedRoute({ component: Component, ...rest }: { component: React.ComponentType<any> }) {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <div className="flex h-screen items-center justify-center">Loading...</div>;
  }

  if (!isAuthenticated) {
    window.location.href = "/signin";
    return null;
  }

  return <Component {...rest} />;
}

function Router() {
  const { isAuthenticated, isLoading } = useAuth();
  const [hasInit, setHasInit] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      setHasInit(true);
    }
  }, [isLoading]);

  if (!hasInit) {
    return <div className="flex h-screen items-center justify-center">Loading...</div>;
  }

  // For testing, bypass auth check
  const bypass = true;

  return (
    <Switch>
      <Route path="/signin" component={SignIn} />
      <Route path="/signup" component={SignUp} />
      <Route path="/shared/proposals/:token" component={SharedProposal} />
      <Route path="/ai-assistant">
        {bypass || isAuthenticated ? (
          <AppShell>
            <AIAssistant />
          </AppShell>
        ) : (
          <SignIn />
        )}
      </Route>
      <Route path="/ai-architecture">
        {bypass || isAuthenticated ? (
          <AIArchitecture />
        ) : (
          <SignIn />
        )}
      </Route>
      <Route path="/">
        {bypass || isAuthenticated ? (
          <AppShell>
            <Switch>
              <Route path="/" component={Dashboard} />
              <Route path="/contacts" component={Contacts} />
              <Route path="/contacts/:id" component={ContactDetail} />
              <Route path="/companies" component={Companies} />
              <Route path="/opportunities" component={Opportunities} />
              <Route path="/opportunities/:id" component={OpportunityDetail} />
              <Route path="/relationships" component={Relationships} />
              <Route path="/objection-handler" component={ObjectionHandler} />
              <Route path="/proposal-generator" component={ProposalGenerator} />
              <Route path="/proposal-analytics" component={ProposalAnalytics} />
              <Route path="/email-configuration" component={EmailConfiguration} />
              <Route path="/workflow-builder" component={WorkflowBuilder} />
              <Route path="/predictive-insights" component={PredictiveInsights} />
              <Route path="/conversational-bi" component={ConversationalBI} />
              <Route path="/attribution-dashboard" component={AttributionDashboard} />
              <Route path="/settings" component={Settings} />
              <Route component={NotFound} />
            </Switch>
          </AppShell>
        ) : (
          <SignIn />
        )}
      </Route>
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <CopilotKitProvider>
          <Router />
          <Toaster />
        </CopilotKitProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
