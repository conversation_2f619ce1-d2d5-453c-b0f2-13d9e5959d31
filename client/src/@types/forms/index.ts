/**
 * Form types for the Aizako CRM project
 */

import { Id } from '../utils';
import { Contact, Company, Opportunity } from '../core';

/**
 * Contact form data
 */
export interface ContactFormData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  title?: string;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  source?: string;
  companyId?: Id;
}

/**
 * Company form data
 */
export interface CompanyFormData {
  name: string;
  industry?: string;
  website?: string;
  employees?: number;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
}

/**
 * Opportunity form data
 */
export interface OpportunityFormData {
  name: string;
  value: number;
  currency?: string;
  stage: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: Date | string;
  probability?: number;
  notes?: string;
  contactId?: Id;
  companyId?: Id;
}

/**
 * Activity form data
 */
export interface ActivityFormData {
  type: string;
  title: string;
  description?: string;
  dueDate?: Date | string;
  completed: boolean;
  completedAt?: Date | string;
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  assignedTo?: Id;
}

/**
 * Task form data
 */
export interface TaskFormData {
  title: string;
  description?: string;
  dueDate?: Date | string;
  completed: boolean;
  completedAt?: Date | string;
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  assignedTo?: Id;
}

/**
 * Document form data
 */
export interface DocumentFormData {
  title: string;
  description?: string;
  file: File;
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
}

/**
 * Proposal form data
 */
export interface ProposalFormData {
  name: string;
  description?: string;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  value: number;
  currency?: string;
  validUntil?: Date | string;
  opportunityId: string;
  contactId?: string;
  companyId?: string;
}
