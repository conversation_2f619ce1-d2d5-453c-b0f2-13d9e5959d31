/**
 * Core entity types for the Aizako CRM project
 * These types represent the fundamental data models used throughout the application
 */

import { Id } from '../utils';

/**
 * User entity
 */
export interface User {
  id: Id;
  username: string;
  email: string;
  fullName?: string;
  preferences?: Record<string, any>;
  createdAt: Date;
}

/**
 * Contact entity
 */
export interface Contact {
  id: Id;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  title?: string;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  source?: string;
  companyId?: Id;
  aiEnrichment?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}

/**
 * Company entity
 */
export interface Company {
  id: Id;
  name: string;
  industry?: string;
  website?: string;
  employees?: number;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  aiEnrichment?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}

/**
 * Opportunity entity
 */
export interface Opportunity {
  id: Id;
  name: string;
  value: number;
  currency?: string;
  stage: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: Date;
  probability?: number;
  notes?: string;
  contactId?: Id;
  companyId?: Id;
  aiInsights?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}

/**
 * Activity entity
 */
export interface Activity {
  id: Id;
  type: string;
  title: string;
  description?: string;
  dueDate?: Date;
  completed: boolean;
  completedAt?: Date;
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
  assignedTo?: Id;
}

/**
 * Task entity
 */
export interface Task {
  id: Id;
  title: string;
  description?: string;
  dueDate?: Date;
  completed: boolean;
  completedAt?: Date;
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
  assignedTo?: Id;
}

/**
 * Document entity
 */
export interface Document {
  id: Id;
  title: string;
  description?: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}

/**
 * Proposal entity
 */
export interface Proposal {
  id: Id;
  name: string;
  description?: string;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected';
  value: number;
  currency?: string;
  validUntil?: Date;
  sentAt?: Date;
  viewedAt?: Date;
  acceptedAt?: Date;
  rejectedAt?: Date;
  opportunityId: string;
  contactId?: string;
  companyId?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}
