/**
 * Type definitions for objection handler
 */

export interface Objection {
  _id: string;
  name: string;
  category: string;
  description: string;
  tags: string[];
  createdBy: string;
  isCommon: boolean;
  customFields?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ObjectionResponse {
  _id: string;
  objectionId: string;
  title: string;
  content: string;
  effectiveness: number;
  tags: string[];
  createdBy: string;
  isApproved: boolean;
  customFields?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ObjectionCategory {
  _id: string;
  name: string;
  description?: string;
  count: number;
}

export interface ObjectionHandlerResponse {
  objectionClass: string;
  response: string;
  rebuttals: Array<{
    title: string;
    content: string;
  }>;
  supportingEvidence: Array<{
    type: string;
    content: string;
    source?: string;
  }>;
  followUpQuestions: string[];
}

export interface ObjectionHandlerRequest {
  objectionText: string;
  objectionClass?: string;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  includeRebuttals?: boolean;
  includeEvidence?: boolean;
  includeFollowUp?: boolean;
}

export interface VoiceObjectionRequest {
  audioBlob: Blob;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
}

export interface VoiceObjectionResponse {
  transcription: string;
  objectionClass: string;
  response: string;
  rebuttals?: Array<{
    title: string;
    content: string;
  }>;
  supportingEvidence?: Array<{
    type: string;
    content: string;
    source?: string;
  }>;
}

export interface RealTimeObjectionHandlerProps {
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  onSuccess?: (response: RealTimeObjectionResponse) => void;
}

export interface RealTimeObjectionResponse {
  responseId?: string;
  objectionClass: string;
  response: string;
  rebuttals: Rebuttal[];
  supportingEvidence: SupportingEvidence[];
}

export interface Rebuttal {
  id: string;
  text: string;
  winRate: number;
  evidence?: string[];
}

export interface SupportingEvidence {
  id: string;
  title: string;
  type: string;
  description: string;
  url?: string;
}
