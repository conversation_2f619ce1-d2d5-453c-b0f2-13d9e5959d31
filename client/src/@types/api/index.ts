/**
 * API types for the Aizako CRM project
 */

import { Contact, Company, Opportunity, Activity, Task, Document, Proposal } from '../core';
import { ContactFormData, CompanyFormData, OpportunityFormData, ActivityFormData } from '../forms';

/**
 * API response structure
 */
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T> {
  items: T[];
  meta: PaginationMeta;
}

/**
 * Contact API responses
 */
export interface ContactsResponse {
  contacts: Contact[];
  meta?: PaginationMeta;
}

export interface ContactResponse {
  contact: Contact;
}

/**
 * Company API responses
 */
export interface CompaniesResponse {
  companies: Company[];
  meta?: PaginationMeta;
}

export interface CompanyResponse {
  company: Company;
}

/**
 * Opportunity API responses
 */
export interface OpportunitiesResponse {
  opportunities: Opportunity[];
  meta?: PaginationMeta;
}

export interface OpportunityResponse {
  opportunity: Opportunity;
}

/**
 * Activity API responses
 */
export interface ActivitiesResponse {
  activities: Activity[];
  meta?: PaginationMeta;
}

export interface ActivityResponse {
  activity: Activity;
}

/**
 * Task API responses
 */
export interface TasksResponse {
  tasks: Task[];
  meta?: PaginationMeta;
}

export interface TaskResponse {
  task: Task;
}

/**
 * Document API responses
 */
export interface DocumentsResponse {
  documents: Document[];
  meta?: PaginationMeta;
}

export interface DocumentResponse {
  document: Document;
}

/**
 * Proposal API responses
 */
export interface ProposalsResponse {
  proposals: Proposal[];
  meta?: PaginationMeta;
}

export interface ProposalResponse {
  proposal: Proposal;
}

/**
 * API request parameters
 */
export interface ApiRequestParams {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data?: any;
  headers?: Record<string, string>;
}

/**
 * Contact API request types
 */
export type CreateContactRequest = ContactFormData;
export type UpdateContactRequest = Partial<ContactFormData>;

/**
 * Company API request types
 */
export type CreateCompanyRequest = CompanyFormData;
export type UpdateCompanyRequest = Partial<CompanyFormData>;

/**
 * Opportunity API request types
 */
export type CreateOpportunityRequest = OpportunityFormData;
export type UpdateOpportunityRequest = Partial<OpportunityFormData>;

/**
 * Activity API request types
 */
export type CreateActivityRequest = ActivityFormData;
export type UpdateActivityRequest = Partial<ActivityFormData>;
