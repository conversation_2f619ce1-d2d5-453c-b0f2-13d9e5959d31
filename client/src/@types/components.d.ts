/**
 * Type definitions for client-side components
 */

import { ReactNode } from 'react';

// Sonner Toaster component
export interface ToasterProps {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
  expand?: boolean;
  visibleToasts?: number;
  hotkey?: string[];
  richColors?: boolean;
  closeButton?: boolean;
  offset?: string | number;
  duration?: number;
  className?: string;
  toastOptions?: Record<string, any>;
  theme?: 'light' | 'dark' | 'system';
  children?: ReactNode;
}

// Theme Provider component
export interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: 'light' | 'dark' | 'system';
  storageKey?: string;
}

// CopilotKit components
export interface CopilotKitProps {
  runtimeUrl?: string;
  publicApiKey?: string;
  children: ReactNode;
  onError?: (error: any) => void;
}

export interface CopilotChatProps {
  className?: string;
  emptyStateComponent?: ReactNode;
  messageClassName?: string;
  children?: ReactNode;
}

export interface CopilotSidebarProps {
  className?: string;
  children?: ReactNode;
}

// Lucide React icons
export interface IconProps {
  size?: number | string;
  color?: string;
  strokeWidth?: number;
  className?: string;
}

// Define missing icon components
export const Phone: React.FC<IconProps> = (props) => null;
export const Video: React.FC<IconProps> = (props) => null;
