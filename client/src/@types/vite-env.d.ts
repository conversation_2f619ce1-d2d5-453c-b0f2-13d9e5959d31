/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_API_URL: string;
  readonly VITE_FIREBASE_API_KEY: string;
  readonly VITE_FIREBASE_AUTH_DOMAIN: string;
  readonly VITE_FIREBASE_PROJECT_ID: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET: string;
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string;
  readonly VITE_FIREBASE_APP_ID: string;
  readonly VITE_FIREBASE_MEASUREMENT_ID: string;
  readonly VITE_OPENAI_API_KEY: string;
  readonly VITE_VOYAGE_API_KEY: string;
  readonly VITE_RESEND_API_KEY: string;
  readonly VITE_NEO4J_URI: string;
  readonly VITE_NEO4J_USERNAME: string;
  readonly VITE_NEO4J_PASSWORD: string;
  readonly NEXT_PUBLIC_COPILOT_KEY: string;
  readonly VITE_ENABLE_AI: string;
  readonly VITE_ENABLE_NEO4J: string;
  readonly VITE_ENABLE_EMAIL_TRACKING: string;
  readonly VITE_ENABLE_ADVANCED_ANALYTICS: string;
  readonly [key: string]: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
