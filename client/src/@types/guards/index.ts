/**
 * Type guards for the Aizako CRM project
 */

import { Contact, Company, Opportunity, Activity, Task, Document, Proposal } from '../core';

/**
 * Type guard for Contact
 */
export function isContact(obj: any): obj is Contact {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.firstName === 'string' &&
    typeof obj.lastName === 'string'
  );
}

/**
 * Type guard for Contact array
 */
export function isContactArray(obj: any): obj is Contact[] {
  return Array.isArray(obj) && obj.every(isContact);
}

/**
 * Type guard for Company
 */
export function isCompany(obj: any): obj is Company {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string'
  );
}

/**
 * Type guard for Company array
 */
export function isCompanyArray(obj: any): obj is Company[] {
  return Array.isArray(obj) && obj.every(isCompany);
}

/**
 * Type guard for Opportunity
 */
export function isOpportunity(obj: any): obj is Opportunity {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.value === 'number'
  );
}

/**
 * Type guard for Opportunity array
 */
export function isOpportunityArray(obj: any): obj is Opportunity[] {
  return Array.isArray(obj) && obj.every(isOpportunity);
}

/**
 * Type guard for Activity
 */
export function isActivity(obj: any): obj is Activity {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.type === 'string' &&
    typeof obj.title === 'string'
  );
}

/**
 * Type guard for Activity array
 */
export function isActivityArray(obj: any): obj is Activity[] {
  return Array.isArray(obj) && obj.every(isActivity);
}

/**
 * Type guard for Task
 */
export function isTask(obj: any): obj is Task {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.title === 'string'
  );
}

/**
 * Type guard for Task array
 */
export function isTaskArray(obj: any): obj is Task[] {
  return Array.isArray(obj) && obj.every(isTask);
}

/**
 * Type guard for Document
 */
export function isDocument(obj: any): obj is Document {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.fileUrl === 'string'
  );
}

/**
 * Type guard for Document array
 */
export function isDocumentArray(obj: any): obj is Document[] {
  return Array.isArray(obj) && obj.every(isDocument);
}

/**
 * Type guard for Proposal
 */
export function isProposal(obj: any): obj is Proposal {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.content === 'string'
  );
}

/**
 * Type guard for Proposal array
 */
export function isProposalArray(obj: any): obj is Proposal[] {
  return Array.isArray(obj) && obj.every(isProposal);
}
