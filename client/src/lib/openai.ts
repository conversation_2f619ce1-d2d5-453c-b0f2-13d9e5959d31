// Interface for AI requests
export interface AIChatRequest {
  message: string;
}

export interface AIChatResponse {
  id: number;
  userId: number;
  message: string;
  response: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

// Function to make a chat request to the AI assistant
export async function sendChatMessage(message: string): Promise<AIChatResponse> {
  try {
    // First try the direct endpoint for local OpenAI API processing
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
        credentials: 'include',
      });

      if (response.ok) {
        return await response.json();
      }
    } catch (directError) {
      console.warn('Direct chat endpoint failed, falling back to AI endpoint:', directError);
      // If direct endpoint fails, we'll fall back to the AI endpoint below
    }

    // Fallback to the legacy AI endpoint
    const response = await fetch('/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message }),
      credentials: 'include',
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`AI chat request failed: ${response.status} ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending chat message:', error);
    throw error;
  }
}

// Function to get chat history
export async function getChatHistory(): Promise<AIChatResponse[]> {
  try {
    const response = await fetch('/api/ai/history', {
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch chat history: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching chat history:', error);
    throw error;
  }
}

// Function to enrich a contact with AI
export async function enrichContact(contactId: number): Promise<any> {
  try {
    const response = await fetch(`/api/ai/enrich-contact/${contactId}`, {
      method: 'POST',
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Contact enrichment failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error enriching contact:', error);
    throw error;
  }
}

// Helper function to format timestamp for display
export function formatTimestamp(timestamp: string): string {
  const date = new Date(timestamp);
  return date.toLocaleString();
}
