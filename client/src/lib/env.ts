// Helper for environment variables that ensures they are properly typed
// and provides fallbacks

interface EnvVars {
  NEXT_PUBLIC_COPILOT_KEY: string | undefined;
  VITE_FIREBASE_API_KEY: string | undefined;
  VITE_FIREBASE_PROJECT_ID: string | undefined;
  VITE_FIREBASE_APP_ID: string | undefined;
  CRM_OPENAI_API_KEY: string | undefined;
}

// Get environment variables safely - access directly with import.meta.env
// This ensures we get the values from Replit secrets
export const env: EnvVars = {
  // CopilotKit API key
  NEXT_PUBLIC_COPILOT_KEY: import.meta.env.NEXT_PUBLIC_COPILOT_KEY as string | undefined,
  
  // Firebase configuration
  VITE_FIREBASE_API_KEY: import.meta.env.VITE_FIREBASE_API_KEY as string | undefined,
  VITE_FIREBASE_PROJECT_ID: import.meta.env.VITE_FIREBASE_PROJECT_ID as string | undefined,
  VITE_FIREBASE_APP_ID: import.meta.env.VITE_FIREBASE_APP_ID as string | undefined,
  
  // OpenAI API key (used for server-side processing)
  CRM_OPENAI_API_KEY: import.meta.env.CRM_OPENAI_API_KEY as string | undefined,
};

// For debugging only
console.log('Environment variables status:');
console.log('NEXT_PUBLIC_COPILOT_KEY present:', !!env.NEXT_PUBLIC_COPILOT_KEY);
console.log('VITE_FIREBASE_API_KEY present:', !!env.VITE_FIREBASE_API_KEY);
console.log('VITE_FIREBASE_PROJECT_ID present:', !!env.VITE_FIREBASE_PROJECT_ID);
console.log('VITE_FIREBASE_APP_ID present:', !!env.VITE_FIREBASE_APP_ID);
console.log('CRM_OPENAI_API_KEY present:', !!env.CRM_OPENAI_API_KEY);