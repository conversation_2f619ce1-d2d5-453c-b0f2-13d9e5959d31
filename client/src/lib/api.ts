/**
 * API client for interacting with the backend
 */
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { FilterOptions } from '@/types/filters';
import { Interaction } from '@/types/interactions';

// Create axios instance
const apiClient = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Fetch interactions for a contact
 * @param contactId Contact ID
 * @param filters Filter options
 * @returns Promise with interactions
 */
export async function fetchInteractions(contactId: string, filters?: FilterOptions): Promise<Interaction[]> {
  try {
    const response = await apiClient.get(`/interactions`, {
      params: {
        contactId,
        ...filters,
      },
    });
    return response.data.interactions || [];
  } catch (error) {
    console.error('Error fetching interactions:', error);
    throw new Error('Failed to fetch interactions');
  }
}

/**
 * Sync interactions for a contact
 * @param contactId Contact ID
 * @returns Promise with sync result
 */
export async function syncInteractions(contactId: string): Promise<{ success: boolean; message?: string }> {
  try {
    const response = await apiClient.post(`/interactions/sync`, {
      contactId,
    });
    return {
      success: true,
      message: response.data.message || 'Interactions synced successfully',
    };
  } catch (error) {
    console.error('Error syncing interactions:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to sync interactions',
    };
  }
}

/**
 * Create a new interaction
 * @param interaction Interaction data
 * @returns Promise with created interaction
 */
export async function createInteraction(interaction: Partial<Interaction>): Promise<Interaction> {
  try {
    const response = await apiClient.post(`/interactions`, interaction);
    return response.data.interaction;
  } catch (error) {
    console.error('Error creating interaction:', error);
    throw new Error('Failed to create interaction');
  }
}

/**
 * Update an interaction
 * @param id Interaction ID
 * @param interaction Interaction data
 * @returns Promise with updated interaction
 */
export async function updateInteraction(id: string, interaction: Partial<Interaction>): Promise<Interaction> {
  try {
    const response = await apiClient.patch(`/interactions/${id}`, interaction);
    return response.data.interaction;
  } catch (error) {
    console.error('Error updating interaction:', error);
    throw new Error('Failed to update interaction');
  }
}

/**
 * Delete an interaction
 * @param id Interaction ID
 * @returns Promise with delete result
 */
export async function deleteInteraction(id: string): Promise<{ success: boolean }> {
  try {
    await apiClient.delete(`/interactions/${id}`);
    return { success: true };
  } catch (error) {
    console.error('Error deleting interaction:', error);
    throw new Error('Failed to delete interaction');
  }
}

/**
 * API request options
 */
export interface ApiRequestOptions {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data?: any;
  params?: any;
  headers?: Record<string, string>;
  withCredentials?: boolean;
}

/**
 * Generic API request function
 * @param options Request options
 * @returns Promise with response data
 */
export async function apiRequest<T = any>(options: ApiRequestOptions): Promise<T> {
  try {
    const { url, method, data, params, headers = {}, withCredentials = true } = options;

    const config: AxiosRequestConfig = {
      url,
      method,
      data,
      params,
      headers,
      withCredentials
    };

    const response: AxiosResponse<T> = await apiClient(config);
    return response.data;
  } catch (error) {
    console.error('API request error:', error);

    if (axios.isAxiosError(error) && error.response) {
      // Handle API error responses
      const errorMessage = error.response.data?.message || error.message || 'An error occurred';
      throw new Error(errorMessage);
    }

    throw new Error('An unexpected error occurred');
  }
}

export default {
  fetchInteractions,
  syncInteractions,
  createInteraction,
  updateInteraction,
  deleteInteraction,
  apiRequest,
};
