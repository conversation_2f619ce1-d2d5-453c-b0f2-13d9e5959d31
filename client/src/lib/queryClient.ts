import { QueryClient, QueryFunction } from "@tanstack/react-query";
import { auth } from "./firebase";

// Store for Firebase token - will be updated when a user is authenticated
let firebaseToken: string | null = null;

// Function to get Firebase token for auth headers
export async function getFirebaseToken(): Promise<string | null> {
  if (!auth.currentUser) return null;
  
  try {
    return await auth.currentUser.getIdToken(true);
  } catch (error) {
    console.error("Error getting Firebase token:", error);
    return null;
  }
}

// Update the token when needed
export async function updateFirebaseToken() {
  firebaseToken = await getFirebaseToken();
  return firebaseToken;
}

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  // Try to get a fresh token if we don't have one yet
  if (!firebaseToken) {
    await updateFirebaseToken();
  }
  
  // Prepare headers
  const headers: Record<string, string> = {};
  
  // Add Content-Type if we have data
  if (data) {
    headers["Content-Type"] = "application/json";
  }
  
  // Add Authorization header if we have a token
  if (firebaseToken) {
    headers["Authorization"] = `Bearer ${firebaseToken}`;
  }
  
  const res = await fetch(url, {
    method,
    headers,
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include", // Keep session cookies working too
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Try to get a fresh token if we don't have one yet
    if (!firebaseToken) {
      await updateFirebaseToken();
    }
    
    // Prepare headers
    const headers: Record<string, string> = {};
    
    // Add Authorization header if we have a token
    if (firebaseToken) {
      headers["Authorization"] = `Bearer ${firebaseToken}`;
    }
    
    const res = await fetch(queryKey[0] as string, {
      headers,
      credentials: "include", // Keep session cookies working too
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
