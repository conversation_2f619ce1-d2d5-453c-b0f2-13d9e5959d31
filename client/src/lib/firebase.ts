import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";

// Access environment variables directly
console.log('Firebase configuration debug:');
console.log('API Key direct check:', import.meta.env.VITE_FIREBASE_API_KEY ? 'Present' : 'Missing');
console.log('Project ID direct check:', import.meta.env.VITE_FIREBASE_PROJECT_ID);
console.log('App ID direct check:', import.meta.env.VITE_FIREBASE_APP_ID ? 'Present' : 'Missing');

// Create Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: `${import.meta.env.VITE_FIREBASE_PROJECT_ID}.firebaseapp.com`,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: `${import.meta.env.VITE_FIREBASE_PROJECT_ID}.appspot.com`,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

console.log('Firebase configuration complete:', !!firebaseConfig.apiKey && !!firebaseConfig.projectId && !!firebaseConfig.appId);

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();

// Configure Google provider with custom parameters
googleProvider.setCustomParameters({
  prompt: "select_account" // Force account selection even when one account is available
});

export default app;