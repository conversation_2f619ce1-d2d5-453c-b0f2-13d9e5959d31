'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { CheckCircle, XCircle, RefreshCw, AlertTriangle, Co<PERSON>, Plus, Trash2, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { useTenantId } from '@/hooks/useTenantId';
import { api } from '@/lib/api';
import { DomainVerificationWizard } from '@/components/settings/DomainVerificationWizard';

interface Domain {
  _id: string;
  domain: string;
  resendDomainId?: string;
  trackingDomain?: string;
  verificationStatus: 'pending' | 'verified' | 'failed';
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function DomainsPage() {
  const { toast } = useToast();
  const tenantId = useTenantId();
  const [domains, setDomains] = useState<Domain[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showWizard, setShowWizard] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch domains
  useEffect(() => {
    const fetchDomains = async () => {
      setIsLoading(true);
      try {
        const response = await api.get('/tenant/domains');
        setDomains(response.data || []);
      } catch (error) {
        console.error('Error fetching domains:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch domains',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDomains();
  }, [refreshTrigger, toast]);

  // Set domain as default
  const setAsDefault = async (domainId: string) => {
    try {
      // This would typically update the domain to be the default
      // For now, we'll just refresh the list
      setRefreshTrigger(prev => prev + 1);
      toast({
        title: 'Success',
        description: 'Domain set as default',
      });
    } catch (error) {
      console.error('Error setting domain as default:', error);
      toast({
        title: 'Error',
        description: 'Failed to set domain as default',
        variant: 'destructive',
      });
    }
  };

  // Delete domain
  const deleteDomain = async (domainId: string) => {
    try {
      await api.delete(`/tenant/domains/${domainId}`);
      setRefreshTrigger(prev => prev + 1);
      toast({
        title: 'Success',
        description: 'Domain deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting domain:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete domain',
        variant: 'destructive',
      });
    }
  };

  // Check verification status
  const checkVerificationStatus = async (domainId: string) => {
    try {
      await api.get(`/tenant/domains/${domainId}/verification`);
      setRefreshTrigger(prev => prev + 1);
      toast({
        title: 'Success',
        description: 'Verification status updated',
      });
    } catch (error) {
      console.error('Error checking verification status:', error);
      toast({
        title: 'Error',
        description: 'Failed to check verification status',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Email Domains</h1>
          <p className="text-muted-foreground">
            Manage domains for sending and tracking emails
          </p>
        </div>
        <Dialog open={showWizard} onOpenChange={setShowWizard}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Domain
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Add New Domain</DialogTitle>
              <DialogDescription>
                Follow the wizard to set up a new domain for email sending and tracking
              </DialogDescription>
            </DialogHeader>
            <DomainVerificationWizard 
              onComplete={() => {
                setShowWizard(false);
                setRefreshTrigger(prev => prev + 1);
              }} 
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Domains</CardTitle>
          <CardDescription>
            Domains used for sending and tracking emails
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : domains.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No domains found</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setShowWizard(true)}
              >
                Add Your First Domain
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Domain</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Tracking Domain</TableHead>
                  <TableHead>Default</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {domains.map((domain) => (
                  <TableRow key={domain._id}>
                    <TableCell className="font-medium">{domain.domain}</TableCell>
                    <TableCell>
                      {domain.verificationStatus === 'verified' ? (
                        <Badge variant="success" className="flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Verified
                        </Badge>
                      ) : domain.verificationStatus === 'failed' ? (
                        <Badge variant="destructive" className="flex items-center gap-1">
                          <XCircle className="h-3 w-3" />
                          Failed
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3" />
                          Pending
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {domain.trackingDomain || 'Not set up'}
                    </TableCell>
                    <TableCell>
                      {domain.isDefault ? (
                        <Badge variant="secondary">Default</Badge>
                      ) : (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => setAsDefault(domain._id)}
                        >
                          Set as Default
                        </Button>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => checkVerificationStatus(domain._id)}
                          title="Check verification status"
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          title="Domain settings"
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        {!domain.isDefault && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => deleteDomain(domain._id)}
                            title="Delete domain"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Email Tracking</CardTitle>
          <CardDescription>
            Configure email tracking settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <AlertTitle>About Email Tracking</AlertTitle>
              <AlertDescription>
                Email tracking allows you to see when recipients open your emails and click on links.
                This helps you understand engagement and follow up at the right time.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Tracking Features</h3>
              <ul className="list-disc pl-5 space-y-1">
                <li>Open tracking via invisible pixel</li>
                <li>Link click tracking</li>
                <li>Real-time notifications</li>
                <li>AI-powered reply drafts</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
