'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Search, 
  Plus, 
  TestTube, 
  RefreshCw, 
  Filter, 
  BarChart4, 
  Mail, 
  ArrowRight 
} from 'lucide-react';
import { useTenantId } from '@/hooks/useTenantId';
import { api } from '@/lib/api';
import ABTestingForm from '@/components/email/ABTestingForm';
import ABTestingResults from '@/components/email/ABTestingResults';

interface EmailTemplate {
  _id: string;
  name: string;
  subject: string;
  body: string;
  category: string;
  abTestingEnabled?: boolean;
  stats?: {
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
  };
}

export default function ABTestingPage() {
  const { toast } = useToast();
  const tenantId = useTenantId();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [activeTemplates, setActiveTemplates] = useState<EmailTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showResultsDialog, setShowResultsDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);

  // Fetch templates
  useEffect(() => {
    const fetchTemplates = async () => {
      setIsLoading(true);
      try {
        // Fetch all templates
        const response = await api.get('/email-templates', {
          params: {
            isActive: true,
            limit: 100
          }
        });
        
        setTemplates(response.data.data || []);
        
        // Filter templates with A/B testing enabled
        const activeTests = (response.data.data || []).filter(
          (template: EmailTemplate) => template.abTestingEnabled
        );
        
        setActiveTemplates(activeTests);
      } catch (error) {
        console.error('Error fetching templates:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch email templates',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplates();
  }, [toast]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Filter templates based on search query
    if (searchQuery) {
      const filtered = templates.filter(template => 
        template.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setTemplates(filtered);
    } else {
      // Reset to fetch all templates
      fetchTemplates();
    }
  };

  // Fetch templates function
  const fetchTemplates = async () => {
    setIsLoading(true);
    try {
      const response = await api.get('/email-templates', {
        params: {
          isActive: true,
          limit: 100
        }
      });
      
      setTemplates(response.data.data || []);
      
      // Filter templates with A/B testing enabled
      const activeTests = (response.data.data || []).filter(
        (template: EmailTemplate) => template.abTestingEnabled
      );
      
      setActiveTemplates(activeTests);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch email templates',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchTemplates();
    toast({
      title: 'Refreshed',
      description: 'Template list has been refreshed',
    });
  };

  // Handle create A/B test
  const handleCreateTest = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setShowCreateDialog(true);
  };

  // Handle view results
  const handleViewResults = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setShowResultsDialog(true);
  };

  // Handle test created successfully
  const handleTestCreated = () => {
    setShowCreateDialog(false);
    fetchTemplates();
    toast({
      title: 'Success',
      description: 'A/B test created successfully',
    });
  };

  // Handle test disabled
  const handleTestDisabled = () => {
    setShowResultsDialog(false);
    fetchTemplates();
    toast({
      title: 'Success',
      description: 'A/B test disabled successfully',
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Email A/B Testing</h1>
          <p className="text-muted-foreground">
            Test different email variants to optimize performance
          </p>
        </div>
        <Button variant="outline" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Active Tests</TabsTrigger>
          <TabsTrigger value="create">Create New Test</TabsTrigger>
        </TabsList>
        
        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active A/B Tests</CardTitle>
              <CardDescription>
                View and manage your active email A/B tests
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : activeTemplates.length === 0 ? (
                <div className="text-center py-8">
                  <TestTube className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">No active A/B tests</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    Create your first A/B test to optimize your email performance
                  </p>
                  <Button 
                    variant="outline" 
                    className="mt-4"
                    onClick={() => {
                      const tabTrigger = document.querySelector('[data-value="create"]');
                      if (tabTrigger && tabTrigger instanceof HTMLElement) {
                        tabTrigger.click();
                      }
                    }}
                  >
                    Create A/B Test
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {activeTemplates.map((template) => (
                    <Card key={template._id} className="border border-muted">
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between items-center">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <Badge variant="outline" className="bg-purple-100 text-purple-800">
                            A/B Testing
                          </Badge>
                        </div>
                        <CardDescription>
                          {template.category.charAt(0).toUpperCase() + template.category.slice(1).replace('_', ' ')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="text-sm truncate mb-2">
                          <span className="font-medium">Subject:</span> {template.subject}
                        </div>
                        {template.stats && (
                          <div className="grid grid-cols-3 gap-2 text-center text-sm mb-4">
                            <div>
                              <div className="font-medium">{template.stats.sent}</div>
                              <div className="text-xs text-muted-foreground">Sent</div>
                            </div>
                            <div>
                              <div className="font-medium">{template.stats.opened}</div>
                              <div className="text-xs text-muted-foreground">Opens</div>
                            </div>
                            <div>
                              <div className="font-medium">{template.stats.clicked}</div>
                              <div className="text-xs text-muted-foreground">Clicks</div>
                            </div>
                          </div>
                        )}
                        <Button 
                          variant="default" 
                          className="w-full"
                          onClick={() => handleViewResults(template)}
                        >
                          <BarChart4 className="h-4 w-4 mr-2" />
                          View Results
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create A/B Test</CardTitle>
              <CardDescription>
                Select a template to create an A/B test
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
                  <Input
                    type="search"
                    placeholder="Search templates..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button type="submit">
                    <Search className="h-4 w-4" />
                  </Button>
                </form>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="support">Support</SelectItem>
                    <SelectItem value="onboarding">Onboarding</SelectItem>
                    <SelectItem value="follow_up">Follow-up</SelectItem>
                    <SelectItem value="proposal">Proposal</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : templates.length === 0 ? (
                <div className="text-center py-8">
                  <Mail className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">No templates found</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    Create email templates first to run A/B tests
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {templates
                    .filter(template => !template.abTestingEnabled)
                    .filter(template => 
                      categoryFilter === 'all' || template.category === categoryFilter
                    )
                    .map((template) => (
                      <Card key={template._id} className="border border-muted">
                        <CardHeader className="p-4 pb-2">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <CardDescription>
                            {template.category.charAt(0).toUpperCase() + template.category.slice(1).replace('_', ' ')}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="p-4 pt-0">
                          <div className="text-sm truncate mb-4">
                            <span className="font-medium">Subject:</span> {template.subject}
                          </div>
                          <Button 
                            variant="outline" 
                            className="w-full"
                            onClick={() => handleCreateTest(template)}
                          >
                            <TestTube className="h-4 w-4 mr-2" />
                            Create A/B Test
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create A/B Test Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Create A/B Test</DialogTitle>
            <DialogDescription>
              Create variants to test different subjects and content
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <ABTestingForm
              templateId={selectedTemplate._id}
              templateName={selectedTemplate.name}
              originalSubject={selectedTemplate.subject}
              originalBody={selectedTemplate.body}
              onSuccess={handleTestCreated}
              onCancel={() => setShowCreateDialog(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Results Dialog */}
      <Dialog open={showResultsDialog} onOpenChange={setShowResultsDialog}>
        <DialogContent className="max-w-5xl">
          <DialogHeader>
            <DialogTitle>A/B Testing Results</DialogTitle>
            <DialogDescription>
              View performance metrics for your A/B test
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <ABTestingResults
              templateId={selectedTemplate._id}
              templateName={selectedTemplate.name}
              onDisableTest={handleTestDisabled}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
