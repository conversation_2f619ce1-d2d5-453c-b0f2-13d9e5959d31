'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  Brain, 
  RefreshCw, 
  Clock, 
  Calendar, 
  Mail, 
  MousePointer, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  CheckCircle, 
  XCircle, 
  Lightbulb, 
  ArrowUpRight, 
  User 
} from 'lucide-react';
import { useTenantId } from '@/hooks/useTenantId';
import { api } from '@/lib/api';

interface Recommendation {
  type: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  action?: string;
}

interface ContactPrediction {
  contactId: string;
  contactName: string;
  contactEmail: string;
  openProbability: number;
  clickProbability: number;
  replyProbability: number;
  bestTimeToSend: {
    dayOfWeek: number;
    hourOfDay: number;
    confidence: number;
  };
  bestTemplateId?: string;
  bestTemplateName?: string;
}

export default function PredictiveAnalyticsPage() {
  const { toast } = useToast();
  const tenantId = useTenantId();
  const [isLoading, setIsLoading] = useState(true);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [bestTime, setBestTime] = useState<any>(null);
  const [contactPredictions, setContactPredictions] = useState<ContactPrediction[]>([]);
  const [bestTemplates, setBestTemplates] = useState<any[]>([]);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch recommendations
        const recommendationsResponse = await api.get('/email-predictive/recommendations');
        setRecommendations(recommendationsResponse.data || []);

        // Fetch best time to send
        const bestTimeResponse = await api.get('/email-predictive/best-time');
        setBestTime(bestTimeResponse.data);

        // Fetch contact predictions for recent contacts
        const contactsResponse = await api.get('/contacts', {
          params: {
            limit: 10,
            sort: 'lastActivity',
            sortDirection: 'desc'
          }
        });

        if (contactsResponse.data && contactsResponse.data.data) {
          const contactIds = contactsResponse.data.data.map((contact: any) => contact._id);
          
          if (contactIds.length > 0) {
            const predictionsResponse = await api.post('/email-predictive/contact-predictions', {
              contactIds
            });
            
            setContactPredictions(predictionsResponse.data || []);
          }
        }
      } catch (error) {
        console.error('Error fetching predictive analytics data:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch predictive analytics data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Get day name
  const getDayName = (dayOfWeek: number): string => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek];
  };

  // Format time
  const formatTime = (hour: number): string => {
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:00 ${ampm}`;
  };

  // Get impact color
  const getImpactColor = (impact: string): string => {
    switch (impact) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-orange-100 text-orange-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get probability color
  const getProbabilityColor = (probability: number): string => {
    if (probability >= 0.7) return 'text-green-600';
    if (probability >= 0.4) return 'text-amber-600';
    return 'text-red-600';
  };

  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch recommendations
        const recommendationsResponse = await api.get('/email-predictive/recommendations');
        setRecommendations(recommendationsResponse.data || []);

        // Fetch best time to send
        const bestTimeResponse = await api.get('/email-predictive/best-time');
        setBestTime(bestTimeResponse.data);

        toast({
          title: 'Success',
          description: 'Predictive analytics data refreshed',
        });
      } catch (error) {
        console.error('Error refreshing predictive analytics data:', error);
        toast({
          title: 'Error',
          description: 'Failed to refresh predictive analytics data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Predictive Email Analytics</h1>
          <p className="text-muted-foreground">
            AI-powered insights and recommendations for email optimization
          </p>
        </div>
        <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
              <TabsTrigger value="contacts">Contact Predictions</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Clock className="h-5 w-5 mr-2" />
                      Best Time to Send
                    </CardTitle>
                    <CardDescription>
                      Optimal time to send emails based on historical data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {bestTime ? (
                      <div className="space-y-4">
                        <div className="flex justify-center items-center space-x-4">
                          <div className="text-center">
                            <div className="text-3xl font-bold">{getDayName(bestTime.dayOfWeek)}</div>
                            <div className="text-sm text-muted-foreground">Day</div>
                          </div>
                          <div className="text-4xl">at</div>
                          <div className="text-center">
                            <div className="text-3xl font-bold">{formatTime(bestTime.hourOfDay)}</div>
                            <div className="text-sm text-muted-foreground">Time</div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Open Rate</span>
                            <span className="text-sm font-medium">{(bestTime.openRate * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={bestTime.openRate * 100} className="h-2" />
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Confidence</span>
                            <span className="text-sm font-medium">{(bestTime.confidence * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={bestTime.confidence * 100} className="h-2" />
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground" />
                        <p className="mt-2 text-muted-foreground">
                          Not enough data to determine best time
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Lightbulb className="h-5 w-5 mr-2" />
                      Key Recommendations
                    </CardTitle>
                    <CardDescription>
                      Top suggestions to improve email performance
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {recommendations.length > 0 ? (
                      <div className="space-y-4">
                        {recommendations.slice(0, 3).map((recommendation, index) => (
                          <div key={index} className="flex items-start space-x-3">
                            <Badge variant="outline" className={getImpactColor(recommendation.impact)}>
                              {recommendation.impact}
                            </Badge>
                            <div>
                              <p className="text-sm">{recommendation.description}</p>
                              {recommendation.action && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  <span className="font-medium">Action:</span> {recommendation.action}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                        
                        {recommendations.length > 3 && (
                          <Button 
                            variant="link" 
                            className="p-0 h-auto"
                            onClick={() => {
                              const tabTrigger = document.querySelector('[data-value="recommendations"]');
                              if (tabTrigger && tabTrigger instanceof HTMLElement) {
                                tabTrigger.click();
                              }
                            }}
                          >
                            View all {recommendations.length} recommendations
                            <ArrowUpRight className="h-3 w-3 ml-1" />
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <CheckCircle className="h-12 w-12 mx-auto text-muted-foreground" />
                        <p className="mt-2 text-muted-foreground">
                          No recommendations at this time
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Contact Engagement Predictions
                  </CardTitle>
                  <CardDescription>
                    AI-powered predictions for your most recent contacts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {contactPredictions.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Contact</TableHead>
                            <TableHead>Open Probability</TableHead>
                            <TableHead>Click Probability</TableHead>
                            <TableHead>Reply Probability</TableHead>
                            <TableHead>Best Time</TableHead>
                            <TableHead>Best Template</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {contactPredictions.slice(0, 5).map((prediction) => (
                            <TableRow key={prediction.contactId}>
                              <TableCell className="font-medium">
                                {prediction.contactName}
                                <div className="text-xs text-muted-foreground">
                                  {prediction.contactEmail}
                                </div>
                              </TableCell>
                              <TableCell className={getProbabilityColor(prediction.openProbability)}>
                                {(prediction.openProbability * 100).toFixed(1)}%
                              </TableCell>
                              <TableCell className={getProbabilityColor(prediction.clickProbability)}>
                                {(prediction.clickProbability * 100).toFixed(1)}%
                              </TableCell>
                              <TableCell className={getProbabilityColor(prediction.replyProbability)}>
                                {(prediction.replyProbability * 100).toFixed(1)}%
                              </TableCell>
                              <TableCell>
                                {getDayName(prediction.bestTimeToSend.dayOfWeek)} at {formatTime(prediction.bestTimeToSend.hourOfDay)}
                              </TableCell>
                              <TableCell>
                                {prediction.bestTemplateName || 'No data'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground" />
                      <p className="mt-2 text-muted-foreground">
                        No contact predictions available
                      </p>
                    </div>
                  )}
                  
                  {contactPredictions.length > 5 && (
                    <div className="mt-4 text-center">
                      <Button 
                        variant="link" 
                        className="p-0 h-auto"
                        onClick={() => {
                          const tabTrigger = document.querySelector('[data-value="contacts"]');
                          if (tabTrigger && tabTrigger instanceof HTMLElement) {
                            tabTrigger.click();
                          }
                        }}
                      >
                        View all {contactPredictions.length} contacts
                        <ArrowUpRight className="h-3 w-3 ml-1" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="recommendations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>AI-Powered Recommendations</CardTitle>
                  <CardDescription>
                    Actionable insights to improve your email performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {recommendations.length > 0 ? (
                    <div className="space-y-6">
                      {recommendations.map((recommendation, index) => (
                        <div key={index} className="border-b pb-4 last:border-0 last:pb-0">
                          <div className="flex items-start space-x-3 mb-2">
                            <Badge variant="outline" className={getImpactColor(recommendation.impact)}>
                              {recommendation.impact}
                            </Badge>
                            <h3 className="text-lg font-medium">
                              {recommendation.type.split('_').map(word => 
                                word.charAt(0).toUpperCase() + word.slice(1)
                              ).join(' ')}
                            </h3>
                          </div>
                          <p className="text-sm mb-2">{recommendation.description}</p>
                          {recommendation.action && (
                            <div className="bg-muted p-3 rounded-md text-sm">
                              <span className="font-medium">Recommended Action:</span> {recommendation.action}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <CheckCircle className="h-12 w-12 mx-auto text-muted-foreground" />
                      <p className="mt-2 text-muted-foreground">
                        No recommendations at this time
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="contacts" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Engagement Predictions</CardTitle>
                  <CardDescription>
                    Detailed predictions for individual contacts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {contactPredictions.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Contact</TableHead>
                            <TableHead>Open Probability</TableHead>
                            <TableHead>Click Probability</TableHead>
                            <TableHead>Reply Probability</TableHead>
                            <TableHead>Best Time</TableHead>
                            <TableHead>Best Template</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {contactPredictions.map((prediction) => (
                            <TableRow key={prediction.contactId}>
                              <TableCell className="font-medium">
                                {prediction.contactName}
                                <div className="text-xs text-muted-foreground">
                                  {prediction.contactEmail}
                                </div>
                              </TableCell>
                              <TableCell className={getProbabilityColor(prediction.openProbability)}>
                                {(prediction.openProbability * 100).toFixed(1)}%
                              </TableCell>
                              <TableCell className={getProbabilityColor(prediction.clickProbability)}>
                                {(prediction.clickProbability * 100).toFixed(1)}%
                              </TableCell>
                              <TableCell className={getProbabilityColor(prediction.replyProbability)}>
                                {(prediction.replyProbability * 100).toFixed(1)}%
                              </TableCell>
                              <TableCell>
                                {getDayName(prediction.bestTimeToSend.dayOfWeek)} at {formatTime(prediction.bestTimeToSend.hourOfDay)}
                              </TableCell>
                              <TableCell>
                                {prediction.bestTemplateName || 'No data'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground" />
                      <p className="mt-2 text-muted-foreground">
                        No contact predictions available
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
