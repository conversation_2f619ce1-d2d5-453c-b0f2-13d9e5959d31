'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  Mail, 
  Eye, 
  MousePointer, 
  Reply, 
  Calendar, 
  RefreshCw, 
  Download, 
  Smartphone, 
  Laptop, 
  Tablet, 
  Globe 
} from 'lucide-react';
import { useTenantId } from '@/hooks/useTenantId';
import { api } from '@/lib/api';

// Define chart colors
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];
const DEVICE_COLORS = {
  desktop: '#0088FE',
  mobile: '#00C49F',
  tablet: '#FFBB28',
  unknown: '#FF8042'
};

export default function EmailAnalyticsPage() {
  const { toast } = useToast();
  const tenantId = useTenantId();
  const [period, setPeriod] = useState('30d');
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        const response = await api.get(`/email-analytics/dashboard?period=${period}`);
        setDashboardData(response.data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch email analytics data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [period, toast]);

  // Format trend data for charts
  const formatTrendData = () => {
    if (!dashboardData?.trends) return [];
    
    return dashboardData.trends.map((day: any) => ({
      date: day.date,
      sent: day.metrics.sent,
      opened: day.metrics.opened,
      clicked: day.metrics.clicked,
      replied: day.metrics.replied,
      openRate: parseFloat(day.metrics.openRate.toFixed(2)),
      clickRate: parseFloat(day.metrics.clickRate.toFixed(2)),
      replyRate: parseFloat(day.metrics.replyRate.toFixed(2))
    }));
  };

  // Format device data for charts
  const formatDeviceData = () => {
    if (!dashboardData?.deviceBreakdown) return [];
    
    return dashboardData.deviceBreakdown.map((device: any) => ({
      name: device.device,
      value: device.metrics.opened,
      color: DEVICE_COLORS[device.device as keyof typeof DEVICE_COLORS] || '#FF8042'
    }));
  };

  // Format location data for charts
  const formatLocationData = () => {
    if (!dashboardData?.locationBreakdown) return [];
    
    return dashboardData.locationBreakdown.map((location: any, index: number) => ({
      name: location.country,
      value: location.metrics.opened,
      color: COLORS[index % COLORS.length]
    }));
  };

  // Format template data for charts
  const formatTemplateData = () => {
    if (!dashboardData?.topTemplates) return [];
    
    return dashboardData.topTemplates.map((template: any) => ({
      name: template.templateName,
      openRate: parseFloat(template.metrics.openRate.toFixed(2)),
      clickRate: parseFloat(template.metrics.clickRate.toFixed(2)),
      replyRate: parseFloat(template.metrics.replyRate.toFixed(2))
    }));
  };

  // Handle period change
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
  };

  // Handle refresh
  const handleRefresh = () => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        const response = await api.get(`/email-analytics/dashboard?period=${period}`);
        setDashboardData(response.data);
        toast({
          title: 'Success',
          description: 'Email analytics data refreshed',
        });
      } catch (error) {
        console.error('Error refreshing dashboard data:', error);
        toast({
          title: 'Error',
          description: 'Failed to refresh email analytics data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  };

  // Handle export
  const handleExport = () => {
    // Implementation for exporting data
    toast({
      title: 'Export',
      description: 'Export functionality coming soon',
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Email Analytics</h1>
          <p className="text-muted-foreground">
            Track and analyze your email performance
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={period} onValueChange={handlePeriodChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Emails Sent
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-muted-foreground mr-2" />
                  <div className="text-2xl font-bold">
                    {dashboardData?.summary?.sent || 0}
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Open Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Eye className="h-5 w-5 text-muted-foreground mr-2" />
                  <div className="text-2xl font-bold">
                    {dashboardData?.summary?.openRate || 0}%
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Click Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <MousePointer className="h-5 w-5 text-muted-foreground mr-2" />
                  <div className="text-2xl font-bold">
                    {dashboardData?.summary?.clickRate || 0}%
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Reply Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Reply className="h-5 w-5 text-muted-foreground mr-2" />
                  <div className="text-2xl font-bold">
                    {dashboardData?.summary?.replyRate || 0}%
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="devices">Devices & Locations</TabsTrigger>
              <TabsTrigger value="ab-testing">A/B Testing</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Email Performance Trends</CardTitle>
                  <CardDescription>
                    Track your email performance over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={formatTrendData()}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="sent" stroke="#8884d8" name="Sent" />
                        <Line type="monotone" dataKey="opened" stroke="#82ca9d" name="Opened" />
                        <Line type="monotone" dataKey="clicked" stroke="#ffc658" name="Clicked" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Engagement Rates</CardTitle>
                  <CardDescription>
                    Open, click, and reply rates over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={formatTrendData()}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="openRate" stroke="#8884d8" name="Open Rate %" />
                        <Line type="monotone" dataKey="clickRate" stroke="#82ca9d" name="Click Rate %" />
                        <Line type="monotone" dataKey="replyRate" stroke="#ffc658" name="Reply Rate %" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="templates" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Templates</CardTitle>
                  <CardDescription>
                    Templates with the highest engagement rates
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={formatTemplateData()}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="openRate" fill="#8884d8" name="Open Rate %" />
                        <Bar dataKey="clickRate" fill="#82ca9d" name="Click Rate %" />
                        <Bar dataKey="replyRate" fill="#ffc658" name="Reply Rate %" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="devices" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Device Breakdown</CardTitle>
                    <CardDescription>
                      Email opens by device type
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={formatDeviceData()}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {formatDeviceData().map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Location Breakdown</CardTitle>
                    <CardDescription>
                      Email opens by country
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={formatLocationData()}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {formatLocationData().map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="ab-testing" className="space-y-4">
              {dashboardData?.abTestingResults && dashboardData.abTestingResults.length > 0 ? (
                dashboardData.abTestingResults.map((result: any, index: number) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle>{result.templateName}</CardTitle>
                      <CardDescription>
                        A/B Testing Results
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={result.variants.map((variant: any) => ({
                              name: variant.name,
                              openRate: parseFloat(variant.metrics.openRate.toFixed(2)),
                              clickRate: parseFloat(variant.metrics.clickRate.toFixed(2)),
                              replyRate: parseFloat(variant.metrics.replyRate.toFixed(2))
                            }))}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="openRate" fill="#8884d8" name="Open Rate %" />
                            <Bar dataKey="clickRate" fill="#82ca9d" name="Click Rate %" />
                            <Bar dataKey="replyRate" fill="#ffc658" name="Reply Rate %" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>No A/B Testing Data</CardTitle>
                    <CardDescription>
                      No A/B testing campaigns found for the selected period
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-center py-8 text-muted-foreground">
                      Create an A/B testing campaign to see results here
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
