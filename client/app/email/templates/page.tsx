'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Eye, 
  BarChart, 
  RefreshCw, 
  Filter, 
  Tag, 
  Mail, 
  FileText, 
  Settings, 
  TestTube 
} from 'lucide-react';
import { useTenantId } from '@/hooks/useTenantId';
import { api } from '@/lib/api';

interface EmailTemplate {
  _id: string;
  name: string;
  description?: string;
  subject: string;
  body: string;
  category: string;
  tags: string[];
  isActive: boolean;
  isDefault?: boolean;
  abTestingEnabled?: boolean;
  stats?: {
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
  };
  createdAt: string;
  updatedAt: string;
}

export default function EmailTemplatesPage() {
  const { toast } = useToast();
  const tenantId = useTenantId();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    subject: '',
    body: '',
    category: 'sales',
    tags: [] as string[],
    isActive: true,
    trackOpens: true,
    trackLinks: true
  });
  const [newTag, setNewTag] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch templates
  useEffect(() => {
    const fetchTemplates = async () => {
      setIsLoading(true);
      try {
        const params: Record<string, any> = {
          page,
          limit: 10,
          sort: 'createdAt',
          sortDirection: 'desc'
        };

        if (searchQuery) {
          params.search = searchQuery;
        }

        if (categoryFilter !== 'all') {
          params.category = categoryFilter;
        }

        const response = await api.get('/email-templates', { params });
        
        setTemplates(response.data.data || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
      } catch (error) {
        console.error('Error fetching templates:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch email templates',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplates();
  }, [page, searchQuery, categoryFilter, toast]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1); // Reset to first page on new search
  };

  // Handle create template
  const handleCreateTemplate = async () => {
    try {
      const response = await api.post('/email-templates', {
        name: formData.name,
        description: formData.description,
        subject: formData.subject,
        body: formData.body,
        category: formData.category,
        tags: formData.tags,
        isActive: formData.isActive,
        trackingSettings: {
          trackOpens: formData.trackOpens,
          trackLinks: formData.trackLinks
        }
      });

      toast({
        title: 'Success',
        description: 'Email template created successfully',
      });

      // Refresh templates
      const templatesResponse = await api.get('/email-templates', {
        params: {
          page,
          limit: 10,
          sort: 'createdAt',
          sortDirection: 'desc'
        }
      });
      
      setTemplates(templatesResponse.data.data || []);
      setShowCreateDialog(false);
      resetFormData();
    } catch (error: any) {
      console.error('Error creating template:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create email template',
        variant: 'destructive',
      });
    }
  };

  // Handle edit template
  const handleEditTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      const response = await api.put(`/email-templates/${selectedTemplate._id}`, {
        name: formData.name,
        description: formData.description,
        subject: formData.subject,
        body: formData.body,
        category: formData.category,
        tags: formData.tags,
        isActive: formData.isActive,
        trackingSettings: {
          trackOpens: formData.trackOpens,
          trackLinks: formData.trackLinks
        }
      });

      toast({
        title: 'Success',
        description: 'Email template updated successfully',
      });

      // Refresh templates
      const templatesResponse = await api.get('/email-templates', {
        params: {
          page,
          limit: 10,
          sort: 'createdAt',
          sortDirection: 'desc'
        }
      });
      
      setTemplates(templatesResponse.data.data || []);
      setShowEditDialog(false);
      setSelectedTemplate(null);
      resetFormData();
    } catch (error) {
      console.error('Error updating template:', error);
      toast({
        title: 'Error',
        description: 'Failed to update email template',
        variant: 'destructive',
      });
    }
  };

  // Handle delete template
  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      await api.delete(`/email-templates/${selectedTemplate._id}`);

      toast({
        title: 'Success',
        description: 'Email template deleted successfully',
      });

      // Refresh templates
      const templatesResponse = await api.get('/email-templates', {
        params: {
          page,
          limit: 10,
          sort: 'createdAt',
          sortDirection: 'desc'
        }
      });
      
      setTemplates(templatesResponse.data.data || []);
      setShowDeleteDialog(false);
      setSelectedTemplate(null);
    } catch (error) {
      console.error('Error deleting template:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete email template',
        variant: 'destructive',
      });
    }
  };

  // Handle duplicate template
  const handleDuplicateTemplate = async (template: EmailTemplate) => {
    try {
      const response = await api.post('/email-templates', {
        name: `${template.name} (Copy)`,
        description: template.description,
        subject: template.subject,
        body: template.body,
        category: template.category,
        tags: template.tags,
        isActive: template.isActive
      });

      toast({
        title: 'Success',
        description: 'Email template duplicated successfully',
      });

      // Refresh templates
      const templatesResponse = await api.get('/email-templates', {
        params: {
          page,
          limit: 10,
          sort: 'createdAt',
          sortDirection: 'desc'
        }
      });
      
      setTemplates(templatesResponse.data.data || []);
    } catch (error) {
      console.error('Error duplicating template:', error);
      toast({
        title: 'Error',
        description: 'Failed to duplicate email template',
        variant: 'destructive',
      });
    }
  };

  // Reset form data
  const resetFormData = () => {
    setFormData({
      name: '',
      description: '',
      subject: '',
      body: '',
      category: 'sales',
      tags: [],
      isActive: true,
      trackOpens: true,
      trackLinks: true
    });
  };

  // Set form data for editing
  const setFormDataForEditing = (template: EmailTemplate) => {
    setFormData({
      name: template.name,
      description: template.description || '',
      subject: template.subject,
      body: template.body,
      category: template.category,
      tags: template.tags || [],
      isActive: template.isActive,
      trackOpens: true, // Default values if not available in template
      trackLinks: true
    });
  };

  // Handle add tag
  const handleAddTag = () => {
    if (newTag && !formData.tags.includes(newTag)) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag]
      });
      setNewTag('');
    }
  };

  // Handle remove tag
  const handleRemoveTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    });
  };

  // Get category badge color
  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case 'sales':
        return 'bg-blue-100 text-blue-800';
      case 'marketing':
        return 'bg-green-100 text-green-800';
      case 'support':
        return 'bg-purple-100 text-purple-800';
      case 'onboarding':
        return 'bg-yellow-100 text-yellow-800';
      case 'follow_up':
        return 'bg-orange-100 text-orange-800';
      case 'proposal':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Email Templates</h1>
          <p className="text-muted-foreground">
            Create and manage email templates for your campaigns
          </p>
        </div>
        <Button onClick={() => {
          resetFormData();
          setShowCreateDialog(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>

      <div className="flex items-center justify-between">
        <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
          <Input
            type="search"
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Button type="submit">
            <Search className="h-4 w-4" />
          </Button>
        </form>
        <div className="flex items-center space-x-2">
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="sales">Sales</SelectItem>
              <SelectItem value="marketing">Marketing</SelectItem>
              <SelectItem value="support">Support</SelectItem>
              <SelectItem value="onboarding">Onboarding</SelectItem>
              <SelectItem value="follow_up">Follow-up</SelectItem>
              <SelectItem value="proposal">Proposal</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Templates</CardTitle>
          <CardDescription>
            Manage your email templates
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <RefreshCw className="h-8 w-8 animate-spin" />
            </div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No templates found</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Create your first email template to get started
              </p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => {
                  resetFormData();
                  setShowCreateDialog(true);
                }}
              >
                Create Template
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Stats</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template._id}>
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getCategoryBadgeColor(template.category)}>
                        {template.category.charAt(0).toUpperCase() + template.category.slice(1).replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-xs truncate">{template.subject}</TableCell>
                    <TableCell>
                      {template.isActive ? (
                        <Badge variant="success">Active</Badge>
                      ) : (
                        <Badge variant="secondary">Inactive</Badge>
                      )}
                      {template.abTestingEnabled && (
                        <Badge variant="outline" className="ml-2 bg-purple-100 text-purple-800">
                          A/B Testing
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {template.stats ? (
                        <div className="text-xs">
                          <span className="mr-2">Sent: {template.stats.sent}</span>
                          <span className="mr-2">Opens: {template.stats.opened}</span>
                          <span>Clicks: {template.stats.clicked}</span>
                        </div>
                      ) : (
                        <span className="text-xs text-muted-foreground">No data</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedTemplate(template);
                            setShowViewDialog(true);
                          }}
                          title="View template"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedTemplate(template);
                            setFormDataForEditing(template);
                            setShowEditDialog(true);
                          }}
                          title="Edit template"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDuplicateTemplate(template)}
                          title="Duplicate template"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedTemplate(template);
                            setShowDeleteDialog(true);
                          }}
                          title="Delete template"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Page {page} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Create Template Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Create Email Template</DialogTitle>
            <DialogDescription>
              Create a new email template for your campaigns
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="E.g., Welcome Email"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="support">Support</SelectItem>
                    <SelectItem value="onboarding">Onboarding</SelectItem>
                    <SelectItem value="follow_up">Follow-up</SelectItem>
                    <SelectItem value="proposal">Proposal</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Brief description of this template"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="subject">Email Subject</Label>
              <Input
                id="subject"
                value={formData.subject}
                onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                placeholder="Subject line"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="body">Email Body</Label>
              <Textarea
                id="body"
                value={formData.body}
                onChange={(e) => setFormData({ ...formData, body: e.target.value })}
                placeholder="Email content (HTML supported)"
                className="min-h-[200px]"
              />
            </div>
            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0"
                      onClick={() => handleRemoveTag(tag)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add a tag"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button type="button" onClick={handleAddTag}>
                  Add
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Settings</Label>
              <div className="flex flex-col gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => 
                      setFormData({ ...formData, isActive: checked as boolean })
                    }
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="trackOpens"
                    checked={formData.trackOpens}
                    onCheckedChange={(checked) => 
                      setFormData({ ...formData, trackOpens: checked as boolean })
                    }
                  />
                  <Label htmlFor="trackOpens">Track Opens</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="trackLinks"
                    checked={formData.trackLinks}
                    onCheckedChange={(checked) => 
                      setFormData({ ...formData, trackLinks: checked as boolean })
                    }
                  />
                  <Label htmlFor="trackLinks">Track Links</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTemplate}>
              Create Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Template Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Email Template</DialogTitle>
            <DialogDescription>
              Update your email template
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* Same form fields as create dialog */}
            {/* ... */}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditTemplate}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Template Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{selectedTemplate?.name}</DialogTitle>
            <DialogDescription>
              {selectedTemplate?.description}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium">Subject</h3>
              <p className="mt-1">{selectedTemplate?.subject}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Body</h3>
              <div 
                className="mt-1 p-4 border rounded-md max-h-[400px] overflow-auto"
                dangerouslySetInnerHTML={{ __html: selectedTemplate?.body || '' }}
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {selectedTemplate?.tags?.map((tag) => (
                <Badge key={tag} variant="secondary">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowViewDialog(false)}>
              Close
            </Button>
            <Button 
              onClick={() => {
                if (selectedTemplate) {
                  setFormDataForEditing(selectedTemplate);
                  setShowViewDialog(false);
                  setShowEditDialog(true);
                }
              }}
            >
              Edit Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Template Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Template</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this template? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteTemplate}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
