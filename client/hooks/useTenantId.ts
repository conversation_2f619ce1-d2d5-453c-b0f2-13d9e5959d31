import { useState, useEffect } from 'react';

/**
 * Hook to get the current tenant ID
 * @returns The current tenant ID or null if not available
 */
export function useTenantId(): string | null {
  const [tenantId, setTenantId] = useState<string | null>(null);

  useEffect(() => {
    // Get tenant ID from localStorage
    const storedTenantId = localStorage.getItem('tenantId');
    
    if (storedTenantId) {
      setTenantId(storedTenantId);
    } else {
      // If not in localStorage, try to get from URL or other sources
      // This is just an example, adjust based on your app's architecture
      const urlParams = new URLSearchParams(window.location.search);
      const urlTenantId = urlParams.get('tenantId');
      
      if (urlTenantId) {
        localStorage.setItem('tenantId', urlTenantId);
        setTenantId(urlTenantId);
      }
    }
  }, []);

  return tenantId;
}

/**
 * Set the current tenant ID
 * @param id The tenant ID to set
 */
export function setCurrentTenantId(id: string): void {
  localStorage.setItem('tenantId', id);
  
  // Optionally dispatch an event to notify other components
  window.dispatchEvent(new CustomEvent('tenantIdChanged', { detail: id }));
}

/**
 * Clear the current tenant ID
 */
export function clearTenantId(): void {
  localStorage.removeItem('tenantId');
  
  // Optionally dispatch an event to notify other components
  window.dispatchEvent(new CustomEvent('tenantIdChanged', { detail: null }));
}

export default useTenantId;
