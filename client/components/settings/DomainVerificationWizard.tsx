import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Clipboard, CheckCircle, XCircle, RefreshCw, AlertTriangle, Copy } from 'lucide-react';
import { useTenantId } from '@/hooks/useTenantId';
import { api } from '@/lib/api';

interface DNSRecord {
  type: string;
  host: string;
  value: string;
  priority?: number;
  ttl?: number;
}

interface DomainVerificationWizardProps {
  onComplete?: () => void;
}

export function DomainVerificationWizard({ onComplete }: DomainVerificationWizardProps) {
  const { toast } = useToast();
  const tenantId = useTenantId();
  const [step, setStep] = useState<'domain' | 'verification' | 'tracking' | 'complete'>('domain');
  const [domain, setDomain] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [domainId, setDomainId] = useState<string | null>(null);
  const [verificationRecords, setVerificationRecords] = useState<DNSRecord[]>([]);
  const [trackingRecords, setTrackingRecords] = useState<DNSRecord[]>([]);
  const [trackingSubdomain, setTrackingSubdomain] = useState('track');
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'verified' | 'failed'>('pending');
  const [dnsCheckResults, setDnsCheckResults] = useState<{
    verificationRecords: { record: DNSRecord; verified: boolean }[];
    trackingRecords?: { record: DNSRecord; verified: boolean }[];
    allVerified: boolean;
  } | null>(null);
  const [isCheckingDNS, setIsCheckingDNS] = useState(false);
  const [checkInterval, setCheckInterval] = useState<NodeJS.Timeout | null>(null);

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (checkInterval) {
        clearInterval(checkInterval);
      }
    };
  }, [checkInterval]);

  // Register domain
  const handleRegisterDomain = async () => {
    if (!domain) {
      toast({
        title: 'Error',
        description: 'Please enter a domain name',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await api.post('/tenant/domains', {
        domain,
        isDefault: true,
      });

      if (response.data) {
        setDomainId(response.data._id);
        setVerificationRecords(response.data.verificationRecords || []);
        setStep('verification');
        
        // Start checking verification status every 30 seconds
        const interval = setInterval(() => {
          checkVerificationStatus(response.data._id);
        }, 30000);
        
        setCheckInterval(interval);
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to register domain',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check verification status
  const checkVerificationStatus = async (id: string) => {
    try {
      const response = await api.get(`/tenant/domains/${id}/verification`);
      
      if (response.data) {
        setVerificationStatus(response.data.verificationStatus);
        
        if (response.data.verificationStatus === 'verified') {
          // Stop checking if verified
          if (checkInterval) {
            clearInterval(checkInterval);
            setCheckInterval(null);
          }
          
          // Move to tracking step
          setStep('tracking');
        }
      }
    } catch (error) {
      console.error('Error checking verification status:', error);
    }
  };

  // Check DNS records manually
  const checkDNSRecords = async () => {
    if (!domainId) return;
    
    setIsCheckingDNS(true);
    
    try {
      const response = await api.get(`/tenant/domains/${domainId}/dns-check`);
      
      if (response.data) {
        setDnsCheckResults(response.data);
      }
    } catch (error) {
      console.error('Error checking DNS records:', error);
      toast({
        title: 'Error',
        description: 'Failed to check DNS records',
        variant: 'destructive',
      });
    } finally {
      setIsCheckingDNS(false);
    }
  };

  // Set up tracking domain
  const setupTrackingDomain = async () => {
    if (!domainId) return;
    
    setIsSubmitting(true);
    
    try {
      const response = await api.post(`/tenant/domains/${domainId}/tracking`, {
        trackingSubdomain,
      });
      
      if (response.data) {
        setTrackingRecords(response.data.trackingRecords || []);
        setStep('complete');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to set up tracking domain',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied',
      description: 'Copied to clipboard',
    });
  };

  // Render DNS record
  const renderDNSRecord = (record: DNSRecord, verified?: boolean) => {
    return (
      <div key={`${record.type}-${record.host}`} className="mb-4 p-4 border rounded-md">
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center gap-2">
            <Badge variant={record.type === 'TXT' ? 'default' : 'secondary'}>
              {record.type}
            </Badge>
            <span className="font-medium">{record.host}</span>
          </div>
          {verified !== undefined && (
            verified ? (
              <Badge variant="success" className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                Verified
              </Badge>
            ) : (
              <Badge variant="destructive" className="flex items-center gap-1">
                <XCircle className="h-3 w-3" />
                Not Verified
              </Badge>
            )
          )}
        </div>
        <div className="flex items-center gap-2 bg-muted p-2 rounded-md">
          <code className="text-xs overflow-x-auto whitespace-nowrap flex-1">
            {record.value}
          </code>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => copyToClipboard(record.value)}
            title="Copy to clipboard"
          >
            <Copy className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Domain Verification Wizard</CardTitle>
        <CardDescription>
          Set up your domain for email sending and tracking
        </CardDescription>
      </CardHeader>
      <CardContent>
        {step === 'domain' && (
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="domain" className="text-sm font-medium">
                Domain Name
              </label>
              <Input
                id="domain"
                placeholder="example.com"
                value={domain}
                onChange={(e) => setDomain(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                Enter the domain you want to use for sending emails
              </p>
            </div>
          </div>
        )}

        {step === 'verification' && (
          <div className="space-y-6">
            <Alert variant={verificationStatus === 'verified' ? 'success' : 'info'}>
              <AlertTitle className="flex items-center gap-2">
                {verificationStatus === 'verified' ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    Domain Verified
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4" />
                    Verification Pending
                  </>
                )}
              </AlertTitle>
              <AlertDescription>
                {verificationStatus === 'verified'
                  ? 'Your domain has been verified successfully!'
                  : 'Please add the following DNS records to verify your domain. This process may take up to 24 hours to complete.'}
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">DNS Records</h3>
              {verificationRecords.map((record) => 
                renderDNSRecord(
                  record, 
                  dnsCheckResults?.verificationRecords.find(r => 
                    r.record.type === record.type && r.record.host === record.host
                  )?.verified
                )
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={checkDNSRecords}
                disabled={isCheckingDNS}
              >
                {isCheckingDNS ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Checking...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Check DNS Records
                  </>
                )}
              </Button>
              
              {dnsCheckResults && (
                <span className="text-sm">
                  {dnsCheckResults.allVerified
                    ? 'All records verified locally!'
                    : 'Some records are not verified yet.'}
                </span>
              )}
            </div>
          </div>
        )}

        {step === 'tracking' && (
          <div className="space-y-6">
            <Alert>
              <AlertTitle>Set Up Tracking Domain</AlertTitle>
              <AlertDescription>
                Setting up a tracking domain improves deliverability and provides a consistent brand experience.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <label htmlFor="trackingSubdomain" className="text-sm font-medium">
                Tracking Subdomain
              </label>
              <div className="flex items-center gap-2">
                <Input
                  id="trackingSubdomain"
                  value={trackingSubdomain}
                  onChange={(e) => setTrackingSubdomain(e.target.value)}
                  className="w-32"
                />
                <span>.</span>
                <Input value={domain} disabled className="flex-1" />
              </div>
              <p className="text-sm text-muted-foreground">
                This subdomain will be used for tracking email opens and clicks
              </p>
            </div>
          </div>
        )}

        {step === 'complete' && (
          <div className="space-y-6">
            <Alert variant="success">
              <AlertTitle className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Setup Complete
              </AlertTitle>
              <AlertDescription>
                Your domain has been verified and tracking domain has been set up successfully!
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Tracking Domain DNS Records</h3>
              <p className="text-sm text-muted-foreground">
                Please add the following DNS record to enable tracking:
              </p>
              {trackingRecords.map((record) => renderDNSRecord(record))}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {step === 'domain' && (
          <Button
            onClick={handleRegisterDomain}
            disabled={isSubmitting || !domain}
          >
            {isSubmitting ? 'Registering...' : 'Register Domain'}
          </Button>
        )}

        {step === 'verification' && (
          <Button
            onClick={() => setStep('tracking')}
            disabled={verificationStatus !== 'verified'}
          >
            {verificationStatus === 'verified' ? 'Continue' : 'Waiting for Verification...'}
          </Button>
        )}

        {step === 'tracking' && (
          <Button
            onClick={setupTrackingDomain}
            disabled={isSubmitting || !trackingSubdomain}
          >
            {isSubmitting ? 'Setting Up...' : 'Set Up Tracking Domain'}
          </Button>
        )}

        {step === 'complete' && (
          <Button onClick={onComplete}>
            Finish
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

export default DomainVerificationWizard;
