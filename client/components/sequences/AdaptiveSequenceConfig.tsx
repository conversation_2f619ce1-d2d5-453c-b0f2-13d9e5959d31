import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, Di<PERSON><PERSON><PERSON>le, DialogTrigger } from '@/components/ui/dialog';
import { 
  Plus, 
  Trash2, 
  Save, 
  RefreshCw, 
  Brain, 
  Zap, 
  Settings, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle 
} from 'lucide-react';
import { api } from '@/lib/api';

interface AdaptiveRuleCondition {
  type: 'open' | 'click' | 'reply' | 'no_open' | 'no_click' | 'no_reply';
  timeframe?: number; // hours
}

interface AdaptiveRuleAction {
  type: 'skip_step' | 'add_step' | 'change_template' | 'change_timing' | 'change_channel';
  stepIndex?: number;
  templateId?: string;
  timing?: {
    type: 'days' | 'hours' | 'minutes';
    value: number;
  };
  channel?: 'email' | 'call' | 'task' | 'linkedin' | 'sms';
}

interface AdaptiveRule {
  name: string;
  description?: string;
  condition: AdaptiveRuleCondition;
  action: AdaptiveRuleAction;
  isActive: boolean;
}

interface AdaptiveSequenceConfig {
  isAdaptive: boolean;
  optimizeFor: 'opens' | 'clicks' | 'replies';
  adaptationLevel: 'conservative' | 'moderate' | 'aggressive';
  rules: AdaptiveRule[];
  allowTemplateSelection: boolean;
  allowTimingAdjustment: boolean;
  allowChannelSwitching: boolean;
}

interface AdaptiveSequenceConfigProps {
  sequenceId: string;
  onConfigSaved?: () => void;
}

export function AdaptiveSequenceConfig({ sequenceId, onConfigSaved }: AdaptiveSequenceConfigProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [config, setConfig] = useState<AdaptiveSequenceConfig>({
    isAdaptive: false,
    optimizeFor: 'replies',
    adaptationLevel: 'moderate',
    rules: [],
    allowTemplateSelection: true,
    allowTimingAdjustment: true,
    allowChannelSwitching: false
  });
  const [showRuleDialog, setShowRuleDialog] = useState(false);
  const [currentRule, setCurrentRule] = useState<AdaptiveRule | null>(null);
  const [isEditingRule, setIsEditingRule] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);

  // Fetch configuration
  useEffect(() => {
    const fetchConfig = async () => {
      setIsLoading(true);
      try {
        const response = await api.get(`/sequences/${sequenceId}/adaptive-config`);
        
        if (response.data) {
          setConfig(response.data);
        }
        
        // Fetch templates for rule creation
        const templatesResponse = await api.get('/email-templates', {
          params: {
            isActive: true,
            limit: 100
          }
        });
        
        if (templatesResponse.data && templatesResponse.data.data) {
          setTemplates(templatesResponse.data.data);
        }
      } catch (error) {
        console.error('Error fetching adaptive configuration:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch adaptive configuration',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfig();
  }, [sequenceId, toast]);

  // Save configuration
  const saveConfig = async () => {
    setIsSaving(true);
    try {
      await api.put(`/sequences/${sequenceId}/adaptive-config`, config);
      
      toast({
        title: 'Success',
        description: 'Adaptive configuration saved successfully',
      });
      
      if (onConfigSaved) {
        onConfigSaved();
      }
    } catch (error) {
      console.error('Error saving adaptive configuration:', error);
      toast({
        title: 'Error',
        description: 'Failed to save adaptive configuration',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Create default rules
  const createDefaultRules = async () => {
    setIsLoading(true);
    try {
      await api.post(`/sequences/${sequenceId}/default-adaptive-rules`);
      
      // Refresh configuration
      const response = await api.get(`/sequences/${sequenceId}/adaptive-config`);
      
      if (response.data) {
        setConfig(response.data);
      }
      
      toast({
        title: 'Success',
        description: 'Default adaptive rules created successfully',
      });
    } catch (error) {
      console.error('Error creating default adaptive rules:', error);
      toast({
        title: 'Error',
        description: 'Failed to create default adaptive rules',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add or update rule
  const addOrUpdateRule = () => {
    if (!currentRule) return;
    
    if (isEditingRule) {
      // Update existing rule
      const updatedRules = config.rules.map(rule => 
        rule.name === currentRule.name ? currentRule : rule
      );
      
      setConfig({
        ...config,
        rules: updatedRules
      });
    } else {
      // Add new rule
      setConfig({
        ...config,
        rules: [...config.rules, currentRule]
      });
    }
    
    setShowRuleDialog(false);
    setCurrentRule(null);
    setIsEditingRule(false);
  };

  // Delete rule
  const deleteRule = (ruleName: string) => {
    setConfig({
      ...config,
      rules: config.rules.filter(rule => rule.name !== ruleName)
    });
  };

  // Toggle rule active state
  const toggleRuleActive = (ruleName: string) => {
    setConfig({
      ...config,
      rules: config.rules.map(rule => 
        rule.name === ruleName ? { ...rule, isActive: !rule.isActive } : rule
      )
    });
  };

  // Get condition description
  const getConditionDescription = (condition: AdaptiveRuleCondition): string => {
    const timeframe = condition.timeframe ? ` in the last ${condition.timeframe} hours` : '';
    
    switch (condition.type) {
      case 'open':
        return `Contact opens an email${timeframe}`;
      case 'click':
        return `Contact clicks a link${timeframe}`;
      case 'reply':
        return `Contact replies to an email${timeframe}`;
      case 'no_open':
        return `Contact doesn't open an email${timeframe}`;
      case 'no_click':
        return `Contact opens but doesn't click${timeframe}`;
      case 'no_reply':
        return `Contact engages but doesn't reply${timeframe}`;
      default:
        return 'Unknown condition';
    }
  };

  // Get action description
  const getActionDescription = (action: AdaptiveRuleAction): string => {
    switch (action.type) {
      case 'skip_step':
        return `Skip step ${action.stepIndex !== undefined ? action.stepIndex + 1 : 'next'}`;
      case 'add_step':
        return 'Add an additional step';
      case 'change_template':
        const template = templates.find(t => t._id === action.templateId);
        return `Change template to "${template ? template.name : action.templateId}"`;
      case 'change_timing':
        if (action.timing) {
          return `Change timing to ${action.timing.value} ${action.timing.type}`;
        }
        return 'Change timing';
      case 'change_channel':
        return `Change channel to ${action.channel || 'alternative'}`;
      default:
        return 'Unknown action';
    }
  };

  // Initialize new rule
  const initNewRule = () => {
    setCurrentRule({
      name: `Rule ${config.rules.length + 1}`,
      description: '',
      condition: {
        type: 'open',
        timeframe: 48
      },
      action: {
        type: 'skip_step',
        stepIndex: 1
      },
      isActive: true
    });
    setIsEditingRule(false);
    setShowRuleDialog(true);
  };

  // Edit rule
  const editRule = (rule: AdaptiveRule) => {
    setCurrentRule({ ...rule });
    setIsEditingRule(true);
    setShowRuleDialog(true);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center">
                <Brain className="h-5 w-5 mr-2" />
                Adaptive Sequence Configuration
              </CardTitle>
              <CardDescription>
                Configure how this sequence adapts to recipient behavior
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={config.isAdaptive}
                onCheckedChange={(checked) => setConfig({ ...config, isAdaptive: checked })}
                id="adaptive-mode"
              />
              <Label htmlFor="adaptive-mode">Adaptive Mode</Label>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <RefreshCw className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="optimize-for">Optimize For</Label>
                  <Select
                    value={config.optimizeFor}
                    onValueChange={(value: 'opens' | 'clicks' | 'replies') => 
                      setConfig({ ...config, optimizeFor: value })
                    }
                    disabled={!config.isAdaptive}
                  >
                    <SelectTrigger id="optimize-for">
                      <SelectValue placeholder="Select optimization goal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="opens">Opens</SelectItem>
                      <SelectItem value="clicks">Clicks</SelectItem>
                      <SelectItem value="replies">Replies</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    What outcome should the sequence prioritize
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="adaptation-level">Adaptation Level</Label>
                  <Select
                    value={config.adaptationLevel}
                    onValueChange={(value: 'conservative' | 'moderate' | 'aggressive') => 
                      setConfig({ ...config, adaptationLevel: value })
                    }
                    disabled={!config.isAdaptive}
                  >
                    <SelectTrigger id="adaptation-level">
                      <SelectValue placeholder="Select adaptation level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="conservative">Conservative</SelectItem>
                      <SelectItem value="moderate">Moderate</SelectItem>
                      <SelectItem value="aggressive">Aggressive</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    How aggressively the sequence should adapt
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="template-selection">AI Template Selection</Label>
                    <Switch
                      id="template-selection"
                      checked={config.allowTemplateSelection}
                      onCheckedChange={(checked) => 
                        setConfig({ ...config, allowTemplateSelection: checked })
                      }
                      disabled={!config.isAdaptive}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="timing-adjustment">AI Timing Adjustment</Label>
                    <Switch
                      id="timing-adjustment"
                      checked={config.allowTimingAdjustment}
                      onCheckedChange={(checked) => 
                        setConfig({ ...config, allowTimingAdjustment: checked })
                      }
                      disabled={!config.isAdaptive}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="channel-switching">AI Channel Switching</Label>
                    <Switch
                      id="channel-switching"
                      checked={config.allowChannelSwitching}
                      onCheckedChange={(checked) => 
                        setConfig({ ...config, allowChannelSwitching: checked })
                      }
                      disabled={!config.isAdaptive}
                    />
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Adaptive Rules</h3>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={createDefaultRules}
                      disabled={!config.isAdaptive}
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Create Default Rules
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={initNewRule}
                      disabled={!config.isAdaptive}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Rule
                    </Button>
                  </div>
                </div>
                
                {config.rules.length === 0 ? (
                  <div className="text-center py-8 border rounded-md">
                    <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground" />
                    <p className="mt-2 text-muted-foreground">
                      No adaptive rules configured
                    </p>
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={createDefaultRules}
                      disabled={!config.isAdaptive}
                    >
                      Create Default Rules
                    </Button>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Rule</TableHead>
                          <TableHead>When</TableHead>
                          <TableHead>Then</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {config.rules.map((rule) => (
                          <TableRow key={rule.name}>
                            <TableCell className="font-medium">
                              {rule.name}
                              {rule.description && (
                                <div className="text-xs text-muted-foreground">
                                  {rule.description}
                                </div>
                              )}
                            </TableCell>
                            <TableCell>{getConditionDescription(rule.condition)}</TableCell>
                            <TableCell>{getActionDescription(rule.action)}</TableCell>
                            <TableCell>
                              {rule.isActive ? (
                                <Badge variant="success" className="flex items-center gap-1">
                                  <CheckCircle className="h-3 w-3" />
                                  Active
                                </Badge>
                              ) : (
                                <Badge variant="secondary" className="flex items-center gap-1">
                                  <XCircle className="h-3 w-3" />
                                  Inactive
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => toggleRuleActive(rule.name)}
                                  title={rule.isActive ? 'Deactivate rule' : 'Activate rule'}
                                  disabled={!config.isAdaptive}
                                >
                                  {rule.isActive ? (
                                    <XCircle className="h-4 w-4" />
                                  ) : (
                                    <CheckCircle className="h-4 w-4" />
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => editRule(rule)}
                                  title="Edit rule"
                                  disabled={!config.isAdaptive}
                                >
                                  <Settings className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => deleteRule(rule.name)}
                                  title="Delete rule"
                                  disabled={!config.isAdaptive}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground flex items-center">
            <Info className="h-4 w-4 mr-1" />
            Changes are not saved until you click Save
          </div>
          <Button onClick={saveConfig} disabled={isLoading || isSaving}>
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Configuration
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      {/* Rule Dialog */}
      <Dialog open={showRuleDialog} onOpenChange={setShowRuleDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{isEditingRule ? 'Edit Rule' : 'Add Rule'}</DialogTitle>
            <DialogDescription>
              Configure when and how the sequence should adapt
            </DialogDescription>
          </DialogHeader>
          
          {currentRule && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="rule-name">Rule Name</Label>
                  <Input
                    id="rule-name"
                    value={currentRule.name}
                    onChange={(e) => setCurrentRule({ ...currentRule, name: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="rule-description">Description (Optional)</Label>
                  <Input
                    id="rule-description"
                    value={currentRule.description || ''}
                    onChange={(e) => setCurrentRule({ ...currentRule, description: e.target.value })}
                  />
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium">When</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="condition-type">Condition</Label>
                    <Select
                      value={currentRule.condition.type}
                      onValueChange={(value: AdaptiveRuleCondition['type']) => 
                        setCurrentRule({
                          ...currentRule,
                          condition: {
                            ...currentRule.condition,
                            type: value
                          }
                        })
                      }
                    >
                      <SelectTrigger id="condition-type">
                        <SelectValue placeholder="Select condition" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="open">Contact opens an email</SelectItem>
                        <SelectItem value="click">Contact clicks a link</SelectItem>
                        <SelectItem value="reply">Contact replies to an email</SelectItem>
                        <SelectItem value="no_open">Contact doesn't open an email</SelectItem>
                        <SelectItem value="no_click">Contact opens but doesn't click</SelectItem>
                        <SelectItem value="no_reply">Contact engages but doesn't reply</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="timeframe">Timeframe (hours)</Label>
                    <Input
                      id="timeframe"
                      type="number"
                      min="1"
                      max="168"
                      value={currentRule.condition.timeframe || 48}
                      onChange={(e) => setCurrentRule({
                        ...currentRule,
                        condition: {
                          ...currentRule.condition,
                          timeframe: parseInt(e.target.value)
                        }
                      })}
                    />
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Then</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="action-type">Action</Label>
                  <Select
                    value={currentRule.action.type}
                    onValueChange={(value: AdaptiveRuleAction['type']) => 
                      setCurrentRule({
                        ...currentRule,
                        action: {
                          type: value,
                          ...(value === 'skip_step' ? { stepIndex: 1 } : {}),
                          ...(value === 'change_template' ? { templateId: templates[0]?._id } : {}),
                          ...(value === 'change_timing' ? { 
                            timing: { type: 'days', value: 1 } 
                          } : {}),
                          ...(value === 'change_channel' ? { channel: 'email' } : {})
                        }
                      })
                    }
                  >
                    <SelectTrigger id="action-type">
                      <SelectValue placeholder="Select action" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="skip_step">Skip step</SelectItem>
                      <SelectItem value="add_step">Add step</SelectItem>
                      <SelectItem value="change_template">Change template</SelectItem>
                      <SelectItem value="change_timing">Change timing</SelectItem>
                      <SelectItem value="change_channel">Change channel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Action-specific fields */}
                {currentRule.action.type === 'skip_step' && (
                  <div className="space-y-2">
                    <Label htmlFor="step-index">Step to Skip</Label>
                    <Input
                      id="step-index"
                      type="number"
                      min="0"
                      value={currentRule.action.stepIndex !== undefined ? currentRule.action.stepIndex : 1}
                      onChange={(e) => setCurrentRule({
                        ...currentRule,
                        action: {
                          ...currentRule.action,
                          stepIndex: parseInt(e.target.value)
                        }
                      })}
                    />
                    <p className="text-xs text-muted-foreground">
                      0 = current step, 1 = next step, etc.
                    </p>
                  </div>
                )}
                
                {currentRule.action.type === 'change_template' && (
                  <div className="space-y-2">
                    <Label htmlFor="template-id">Template</Label>
                    <Select
                      value={currentRule.action.templateId}
                      onValueChange={(value) => 
                        setCurrentRule({
                          ...currentRule,
                          action: {
                            ...currentRule.action,
                            templateId: value
                          }
                        })
                      }
                    >
                      <SelectTrigger id="template-id">
                        <SelectValue placeholder="Select template" />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template._id} value={template._id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                {currentRule.action.type === 'change_timing' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="timing-value">Value</Label>
                      <Input
                        id="timing-value"
                        type="number"
                        min="1"
                        value={currentRule.action.timing?.value || 1}
                        onChange={(e) => setCurrentRule({
                          ...currentRule,
                          action: {
                            ...currentRule.action,
                            timing: {
                              type: currentRule.action.timing?.type || 'days',
                              value: parseInt(e.target.value)
                            }
                          }
                        })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="timing-type">Unit</Label>
                      <Select
                        value={currentRule.action.timing?.type || 'days'}
                        onValueChange={(value: 'days' | 'hours' | 'minutes') => 
                          setCurrentRule({
                            ...currentRule,
                            action: {
                              ...currentRule.action,
                              timing: {
                                type: value,
                                value: currentRule.action.timing?.value || 1
                              }
                            }
                          })
                        }
                      >
                        <SelectTrigger id="timing-type">
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="days">Days</SelectItem>
                          <SelectItem value="hours">Hours</SelectItem>
                          <SelectItem value="minutes">Minutes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
                
                {currentRule.action.type === 'change_channel' && (
                  <div className="space-y-2">
                    <Label htmlFor="channel">Channel</Label>
                    <Select
                      value={currentRule.action.channel || 'email'}
                      onValueChange={(value: 'email' | 'call' | 'task' | 'linkedin' | 'sms') => 
                        setCurrentRule({
                          ...currentRule,
                          action: {
                            ...currentRule.action,
                            channel: value
                          }
                        })
                      }
                    >
                      <SelectTrigger id="channel">
                        <SelectValue placeholder="Select channel" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="call">Call</SelectItem>
                        <SelectItem value="task">Task</SelectItem>
                        <SelectItem value="linkedin">LinkedIn</SelectItem>
                        <SelectItem value="sms">SMS</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRuleDialog(false)}>
              Cancel
            </Button>
            <Button onClick={addOrUpdateRule}>
              {isEditingRule ? 'Update Rule' : 'Add Rule'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default AdaptiveSequenceConfig;
