import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Trash2, TestTube, BarChart4 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';

interface ABTestingFormProps {
  templateId: string;
  templateName: string;
  originalSubject: string;
  originalBody: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface Variant {
  id: string;
  name: string;
  subject: string;
  body: string;
  weight: number;
}

export function ABTestingForm({
  templateId,
  templateName,
  originalSubject,
  originalBody,
  onSuccess,
  onCancel
}: ABTestingFormProps) {
  const { toast } = useToast();
  const [variants, setVariants] = useState<Variant[]>([
    {
      id: 'variant-1',
      name: 'Variant A',
      subject: '',
      body: '',
      weight: 50
    }
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Add a new variant
  const addVariant = () => {
    const newVariantId = `variant-${variants.length + 1}`;
    const newVariantName = `Variant ${String.fromCharCode(65 + variants.length)}`;
    
    // Calculate new weights to distribute evenly
    const newWeight = Math.floor(100 / (variants.length + 1));
    const updatedVariants = variants.map(v => ({
      ...v,
      weight: newWeight
    }));
    
    setVariants([
      ...updatedVariants,
      {
        id: newVariantId,
        name: newVariantName,
        subject: '',
        body: '',
        weight: newWeight
      }
    ]);
  };

  // Remove a variant
  const removeVariant = (id: string) => {
    if (variants.length <= 1) {
      toast({
        title: 'Error',
        description: 'You need at least one variant for A/B testing',
        variant: 'destructive',
      });
      return;
    }
    
    // Remove the variant and redistribute weights
    const filteredVariants = variants.filter(v => v.id !== id);
    const newWeight = Math.floor(100 / filteredVariants.length);
    
    setVariants(
      filteredVariants.map(v => ({
        ...v,
        weight: newWeight
      }))
    );
  };

  // Update variant field
  const updateVariant = (id: string, field: keyof Variant, value: string | number) => {
    setVariants(
      variants.map(v => 
        v.id === id ? { ...v, [field]: value } : v
      )
    );
  };

  // Update weight and ensure total is 100%
  const updateWeight = (id: string, newWeight: number) => {
    const currentVariant = variants.find(v => v.id === id);
    if (!currentVariant) return;
    
    const weightDiff = newWeight - currentVariant.weight;
    const otherVariants = variants.filter(v => v.id !== id);
    
    if (otherVariants.length === 0) {
      // If there's only one variant, it should always be 100%
      setVariants([{ ...currentVariant, weight: 100 }]);
      return;
    }
    
    // Distribute the weight difference among other variants
    const totalOtherWeight = otherVariants.reduce((sum, v) => sum + v.weight, 0);
    
    const updatedOtherVariants = otherVariants.map(v => {
      const proportion = v.weight / totalOtherWeight;
      const adjustedWeight = Math.max(1, v.weight - (weightDiff * proportion));
      return { ...v, weight: Math.round(adjustedWeight) };
    });
    
    setVariants([
      ...updatedOtherVariants,
      { ...currentVariant, weight: newWeight }
    ]);
  };

  // Submit A/B testing configuration
  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // Format variants for API
      const formattedVariants = variants.map(v => ({
        name: v.name,
        subject: v.subject || originalSubject,
        body: v.body || originalBody,
        weight: v.weight
      }));
      
      // Add original as a variant if not already included
      const hasOriginalVariant = variants.some(v => 
        v.name.toLowerCase() === 'original' || 
        v.name.toLowerCase() === 'control'
      );
      
      if (!hasOriginalVariant) {
        formattedVariants.push({
          name: 'Original',
          subject: originalSubject,
          body: originalBody,
          weight: Math.floor(100 / (formattedVariants.length + 1))
        });
        
        // Adjust weights to ensure total is 100%
        const newWeight = Math.floor(100 / formattedVariants.length);
        formattedVariants.forEach(v => {
          v.weight = newWeight;
        });
      }
      
      // Ensure weights sum to exactly 100%
      const totalWeight = formattedVariants.reduce((sum, v) => sum + v.weight, 0);
      if (totalWeight !== 100) {
        const diff = 100 - totalWeight;
        formattedVariants[0].weight += diff;
      }
      
      // Submit to API
      await api.post(`/email-templates/${templateId}/ab-testing`, {
        variants: formattedVariants
      });
      
      toast({
        title: 'Success',
        description: 'A/B testing configured successfully',
      });
      
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error configuring A/B testing:', error);
      toast({
        title: 'Error',
        description: 'Failed to configure A/B testing',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <TestTube className="h-5 w-5 mr-2" />
          A/B Testing for {templateName}
        </CardTitle>
        <CardDescription>
          Create variants to test different subjects and content
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Original Template</h3>
            <Badge variant="outline">Control</Badge>
          </div>
          <div className="p-4 border rounded-md space-y-2">
            <div>
              <Label>Subject</Label>
              <div className="p-2 bg-muted rounded-md mt-1">{originalSubject}</div>
            </div>
            <div>
              <Label>Body Preview</Label>
              <div className="p-2 bg-muted rounded-md mt-1 max-h-20 overflow-hidden">
                {originalBody.substring(0, 150)}...
              </div>
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Test Variants</h3>
            <Button variant="outline" size="sm" onClick={addVariant}>
              <Plus className="h-4 w-4 mr-2" />
              Add Variant
            </Button>
          </div>

          {variants.map((variant, index) => (
            <Card key={variant.id} className="border border-muted">
              <CardHeader className="p-4">
                <div className="flex justify-between items-center">
                  <Input
                    value={variant.name}
                    onChange={(e) => updateVariant(variant.id, 'name', e.target.value)}
                    className="w-40 font-medium"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeVariant(variant.id)}
                    disabled={variants.length <= 1}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor={`${variant.id}-subject`}>Subject Line</Label>
                  <Input
                    id={`${variant.id}-subject`}
                    value={variant.subject}
                    onChange={(e) => updateVariant(variant.id, 'subject', e.target.value)}
                    placeholder={originalSubject}
                  />
                  <p className="text-xs text-muted-foreground">
                    Leave empty to use the original subject
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`${variant.id}-body`}>Email Body</Label>
                  <Textarea
                    id={`${variant.id}-body`}
                    value={variant.body}
                    onChange={(e) => updateVariant(variant.id, 'body', e.target.value)}
                    placeholder="Leave empty to use the original body"
                    className="min-h-[100px]"
                  />
                  <p className="text-xs text-muted-foreground">
                    Leave empty to use the original body
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor={`${variant.id}-weight`}>Traffic Weight</Label>
                    <span className="text-sm font-medium">{variant.weight}%</span>
                  </div>
                  <Slider
                    id={`${variant.id}-weight`}
                    value={[variant.weight]}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={(value) => updateWeight(variant.id, value[0])}
                  />
                  <p className="text-xs text-muted-foreground">
                    Percentage of recipients who will receive this variant
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Start A/B Test'}
        </Button>
      </CardFooter>
    </Card>
  );
}

export default ABTestingForm;
