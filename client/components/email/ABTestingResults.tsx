import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { TestTube, Trophy, AlertTriangle, RefreshCw, Ban } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';

interface ABTestingResultsProps {
  templateId: string;
  templateName: string;
  onDisableTest?: () => void;
}

interface VariantResult {
  name: string;
  metrics: {
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
    openRate: number;
    clickRate: number;
    clickToOpenRate: number;
    replyRate: number;
  };
}

interface ABTestingResultData {
  templateId: string;
  templateName: string;
  variants: VariantResult[];
}

export function ABTestingResults({
  templateId,
  templateName,
  onDisableTest
}: ABTestingResultsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [results, setResults] = useState<ABTestingResultData | null>(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  // Fetch A/B testing results
  useEffect(() => {
    const fetchResults = async () => {
      setIsLoading(true);
      try {
        const response = await api.get(
          `/email-analytics/ab-testing/${templateId}?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`
        );
        setResults(response.data);
      } catch (error) {
        console.error('Error fetching A/B testing results:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch A/B testing results',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [templateId, dateRange, toast]);

  // Format data for charts
  const formatChartData = (metric: 'openRate' | 'clickRate' | 'replyRate') => {
    if (!results) return [];
    
    return results.variants.map(variant => ({
      name: variant.name,
      value: parseFloat(variant.metrics[metric].toFixed(2))
    }));
  };

  // Find winning variant
  const findWinningVariant = (metric: 'openRate' | 'clickRate' | 'replyRate') => {
    if (!results || results.variants.length === 0) return null;
    
    return results.variants.reduce((winner, current) => {
      return current.metrics[metric] > winner.metrics[metric] ? current : winner;
    }, results.variants[0]);
  };

  // Handle disable A/B testing
  const handleDisableTest = async () => {
    try {
      await api.delete(`/email-templates/${templateId}/ab-testing`);
      
      toast({
        title: 'Success',
        description: 'A/B testing disabled successfully',
      });
      
      if (onDisableTest) onDisableTest();
    } catch (error) {
      console.error('Error disabling A/B testing:', error);
      toast({
        title: 'Error',
        description: 'Failed to disable A/B testing',
        variant: 'destructive',
      });
    }
  };

  // Handle refresh results
  const handleRefresh = () => {
    const fetchResults = async () => {
      setIsLoading(true);
      try {
        const response = await api.get(
          `/email-analytics/ab-testing/${templateId}?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`
        );
        setResults(response.data);
        
        toast({
          title: 'Success',
          description: 'A/B testing results refreshed',
        });
      } catch (error) {
        console.error('Error refreshing A/B testing results:', error);
        toast({
          title: 'Error',
          description: 'Failed to refresh A/B testing results',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <TestTube className="h-5 w-5 mr-2" />
            <CardTitle>A/B Testing Results: {templateName}</CardTitle>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={handleDisableTest}>
              <Ban className="h-4 w-4 mr-2" />
              Disable Test
            </Button>
          </div>
        </div>
        <CardDescription>
          Compare performance metrics across different variants
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin" />
          </div>
        ) : !results || results.variants.length === 0 ? (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>No data available</AlertTitle>
            <AlertDescription>
              There is no A/B testing data available for this template in the selected date range.
            </AlertDescription>
          </Alert>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {['openRate', 'clickRate', 'replyRate'].map((metric) => {
                const winner = findWinningVariant(metric as 'openRate' | 'clickRate' | 'replyRate');
                return (
                  <Card key={metric} className="border-muted">
                    <CardHeader className="p-4 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Best {metric.replace('Rate', ' Rate')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      {winner && (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                            <div>
                              <div className="font-bold">{winner.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {parseFloat(winner.metrics[metric as keyof typeof winner.metrics].toFixed(2))}%
                              </div>
                            </div>
                          </div>
                          <Badge variant="outline" className="bg-green-100 text-green-800">
                            Winner
                          </Badge>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            <Tabs defaultValue="openRate" className="space-y-4">
              <TabsList>
                <TabsTrigger value="openRate">Open Rate</TabsTrigger>
                <TabsTrigger value="clickRate">Click Rate</TabsTrigger>
                <TabsTrigger value="replyRate">Reply Rate</TabsTrigger>
              </TabsList>
              
              {['openRate', 'clickRate', 'replyRate'].map((metric) => (
                <TabsContent key={metric} value={metric} className="space-y-4">
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={formatChartData(metric as 'openRate' | 'clickRate' | 'replyRate')}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip formatter={(value) => [`${value}%`, metric.replace('Rate', ' Rate')]} />
                        <Legend />
                        <Bar 
                          dataKey="value" 
                          name={metric.replace('Rate', ' Rate')} 
                          fill={
                            metric === 'openRate' ? '#8884d8' : 
                            metric === 'clickRate' ? '#82ca9d' : 
                            '#ffc658'
                          } 
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr>
                          <th className="text-left p-2 border-b">Variant</th>
                          <th className="text-right p-2 border-b">Sent</th>
                          <th className="text-right p-2 border-b">
                            {metric === 'openRate' ? 'Opens' : 
                             metric === 'clickRate' ? 'Clicks' : 
                             'Replies'}
                          </th>
                          <th className="text-right p-2 border-b">Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {results.variants.map((variant, index) => (
                          <tr key={index} className="hover:bg-muted/50">
                            <td className="p-2 border-b">{variant.name}</td>
                            <td className="text-right p-2 border-b">{variant.metrics.sent}</td>
                            <td className="text-right p-2 border-b">
                              {metric === 'openRate' ? variant.metrics.opened : 
                               metric === 'clickRate' ? variant.metrics.clicked : 
                               variant.metrics.replied}
                            </td>
                            <td className="text-right p-2 border-b font-medium">
                              {parseFloat(variant.metrics[metric as keyof typeof variant.metrics].toFixed(2))}%
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Data from {dateRange.startDate} to {dateRange.endDate}
        </div>
      </CardFooter>
    </Card>
  );
}

export default ABTestingResults;
