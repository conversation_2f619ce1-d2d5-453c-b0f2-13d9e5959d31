# Aizako CRM - MongoDB Implementation

This document provides instructions for setting up and using the MongoDB implementation of Aizako CRM.

## Overview

Aizako CRM now supports MongoDB as the primary database, with the ability to switch between MongoDB and PostgreSQL using environment variables.

## Setup

1. **Install MongoDB**

   You can use MongoDB Atlas (cloud-hosted) or install MongoDB locally.

   - [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
   - [MongoDB Community Edition](https://www.mongodb.com/try/download/community)

2. **Configure Environment Variables**

   Create a `.env` file in the root directory with the following variables:

   ```
   # MongoDB Configuration
   MONGODB_URI=mongodb+srv://username:<EMAIL>/aizako-crm?retryWrites=true&w=majority
   MONGODB_ENABLED=true
   ```

   Replace the `MONGODB_URI` with your actual MongoDB connection string.

3. **Install Dependencies**

   ```bash
   npm install
   ```

4. **Build the Application**

   ```bash
   npm run build
   ```

5. **Seed the Database**

   ```bash
   npm run db:seed:mongo
   ```

6. **Start the Server**

   ```bash
   npm run db:mongo
   ```

## Testing

To test the MongoDB implementation:

```bash
npm run test:mongo
```

This will run a test script that verifies the MongoDB connection and models.

## API Endpoints

All API endpoints remain the same, but they now work with MongoDB instead of PostgreSQL when `MONGODB_ENABLED` is set to `true`.

## MongoDB Models

The following MongoDB models have been implemented:

1. **User**: User accounts and authentication
2. **Contact**: Customer and prospect contacts
3. **Company**: Organizations and businesses
4. **Opportunity**: Sales opportunities and deals
5. **Activity**: User activities like calls, meetings, emails
6. **Relationship**: Relationships between contacts and companies
7. **AiChat**: AI chat conversations
8. **Insight**: AI-generated insights
9. **Document**: Files and documents
10. **Tag**: Centralized tag management
11. **Task**: Task management
12. **Notification**: User notifications
13. **Subscription Models**: Subscription plans, features, tenants, etc.

## Subscription System

The subscription system has been fully implemented with MongoDB support, including:

1. **Subscription Plans**: Define different subscription tiers with features and limits
2. **Features**: Enable/disable features based on subscription plan
3. **Resource Limits**: Enforce limits on resources like contacts, companies, etc.
4. **Usage Tracking**: Track resource usage against subscription limits
5. **Tenant Management**: Support for multi-tenant architecture

## Development

When developing with MongoDB, use the following command to start the server:

```bash
npm run db:mongo
```

This will start the server with MongoDB enabled.

## Switching Between Databases

To switch between MongoDB and PostgreSQL, simply change the `MONGODB_ENABLED` environment variable:

- `MONGODB_ENABLED=true`: Use MongoDB
- `MONGODB_ENABLED=false`: Use PostgreSQL

## Troubleshooting

### Connection Issues

If you're having trouble connecting to MongoDB, check the following:

1. Verify that your MongoDB connection string is correct
2. Ensure that your IP address is whitelisted in MongoDB Atlas
3. Check that MongoDB is running if using a local installation

### Data Migration

If you need to migrate data from PostgreSQL to MongoDB, you can use the following steps:

1. Export data from PostgreSQL
2. Transform data to match MongoDB schema
3. Import data into MongoDB

### Model Issues

If you're having issues with MongoDB models, check the following:

1. Ensure that all required fields are provided
2. Check that field types match the schema
3. Verify that references to other models use valid ObjectIds
