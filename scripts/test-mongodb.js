#!/usr/bin/env node

// This script tests the MongoDB implementation

require('dotenv').config();

// Set MongoDB as enabled
process.env.MONGODB_ENABLED = 'true';

// Import the MongoDB connection
const { connectToMongoDB, disconnectFromMongoDB } = require('../dist/server/mongodb-connection');

// Import the models
const { 
  User, 
  Contact, 
  Company, 
  Opportunity, 
  Activity, 
  SubscriptionPlan 
} = require('../dist/server/models/mongoose');

async function testMongoDB() {
  try {
    console.log('Testing MongoDB connection...');
    await connectToMongoDB();
    console.log('MongoDB connection successful!');
    
    // Test User model
    console.log('\nTesting User model...');
    const userCount = await User.countDocuments();
    console.log(`Found ${userCount} users`);
    
    // Test Contact model
    console.log('\nTesting Contact model...');
    const contactCount = await Contact.countDocuments();
    console.log(`Found ${contactCount} contacts`);
    
    // Test Company model
    console.log('\nTesting Company model...');
    const companyCount = await Company.countDocuments();
    console.log(`Found ${companyCount} companies`);
    
    // Test Opportunity model
    console.log('\nTesting Opportunity model...');
    const opportunityCount = await Opportunity.countDocuments();
    console.log(`Found ${opportunityCount} opportunities`);
    
    // Test Activity model
    console.log('\nTesting Activity model...');
    const activityCount = await Activity.countDocuments();
    console.log(`Found ${activityCount} activities`);
    
    // Test SubscriptionPlan model
    console.log('\nTesting SubscriptionPlan model...');
    const planCount = await SubscriptionPlan.countDocuments();
    console.log(`Found ${planCount} subscription plans`);
    
    console.log('\nAll tests passed!');
  } catch (error) {
    console.error('Error testing MongoDB:', error);
  } finally {
    await disconnectFromMongoDB();
  }
}

// Run the test
testMongoDB()
  .then(() => {
    console.log('MongoDB test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('MongoDB test failed:', error);
    process.exit(1);
  });
