#!/usr/bin/env node

// This script seeds the MongoDB database with initial data

require('dotenv').config();

// Set MongoDB as enabled
process.env.MONGODB_ENABLED = 'true';

// Import the seeder
const { seedMongoDB } = require('../dist/server/seed/mongo-seeder');

// Run the seeder
seedMongoDB()
  .then(() => {
    console.log('MongoDB database seeded successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error seeding MongoDB database:', error);
    process.exit(1);
  });
