#!/bin/bash

# Run tests for the Data Access Layer
echo "Running tests for the Data Access Layer..."

# Run the tests
npx jest --config=jest.config.js server/tests/dal

# Check if the tests passed
if [ $? -eq 0 ]; then
  echo "✅ All DAL tests passed!"
else
  echo "❌ Some DAL tests failed!"
  exit 1
fi

# Run tests for the new repositories
echo "Running tests for the new repositories..."

# Run the tests
npx jest --config=jest.config.js server/tests/dal/ai-chat-repository.test.ts server/tests/dal/activity-repository.test.ts

# Run tests for the services that use the DAL
echo "Running tests for services that use the DAL..."

# Run the tests
npx jest --config=jest.config.js server/tests/services/objection-handler-service.test.ts server/tests/services/contact-service.test.ts

# Check if the tests passed
if [ $? -eq 0 ]; then
  echo "✅ All service tests passed!"
else
  echo "❌ Some service tests failed!"
  exit 1
fi

echo "All tests passed! 🎉"
exit 0
