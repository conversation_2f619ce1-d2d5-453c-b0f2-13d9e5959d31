// Simple script to test MongoDB connection
import { MongoClient, ServerApiVersion } from 'mongodb';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get MongoDB connection details
const username = 'jp';
const password = 'ShakaSenghor189!';
const uri = `mongodb+srv://${username}:${password}@crm.aa5qnt1.mongodb.net/?retryWrites=true&w=majority&appName=CRM`;

// Create a new MongoClient with ServerApiVersion
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  }
});

async function run() {
  try {
    // Connect the client to the server
    await client.connect();
    console.log("Connected successfully to MongoDB server");

    // Get the database
    const db = client.db("aizako-crm");

    // List collections
    const collections = await db.listCollections().toArray();
    console.log("Available collections:");
    collections.forEach(collection => {
      console.log(` - ${collection.name}`);
    });

  } catch (err) {
    console.error("Error connecting to MongoDB:", err);
  } finally {
    // Ensures that the client will close when you finish/error
    await client.close();
  }
}

run().catch(console.dir);
