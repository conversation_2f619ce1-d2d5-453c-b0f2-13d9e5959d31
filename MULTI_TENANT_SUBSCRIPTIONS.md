# Aizako CRM Multi-Tenant Subscription Architecture

This document outlines the architecture and implementation guidelines for the multi-tenant subscription model in Aizako CRM. It provides a comprehensive guide for how each module should implement and interact with the subscription system and how authentication and data isolation should be handled across tenants.

## Table of Contents

1. [Subscription Model](#subscription-model)
2. [Multi-Tenancy Architecture](#multi-tenancy-architecture)
3. [Authentication and Authorization](#authentication-and-authorization)
4. [Data Isolation and Access Control](#data-isolation-and-access-control)
5. [Module Integration Guidelines](#module-integration-guidelines)
6. [Synchronization Between Modules](#synchronization-between-modules)
7. [Implementation Roadmap](#implementation-roadmap)

## Subscription Model

### Plan Tiers

Aizako CRM will offer the following subscription tiers:

| Feature | Free | Basic | Professional | Enterprise |
|---------|------|-------|--------------|------------|
| Contacts | 50 | 1,000 | 10,000 | Unlimited |
| Companies | 10 | 100 | 1,000 | Unlimited |
| Opportunities | 5 | 50 | 500 | Unlimited |
| AI Assistant | Limited | Basic | Advanced | Custom |
| Document Intelligence | No | Basic | Advanced | Custom |
| API Access | No | Limited | Full | Custom |
| Users | 1 | 5 | 20 | Custom |
| Support | Community | Email | Priority | Dedicated |
| Price (Monthly) | Free | $15 | $49 | Custom |

### Subscription Entity

The subscription model should be implemented as a shared entity across all modules:

```typescript
// Subscription types
export enum SubscriptionPlan {
  FREE = 'free',
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise'
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  PAST_DUE = 'past_due',
  CANCELED = 'canceled',
  TRIALING = 'trialing',
  EXPIRED = 'expired'
}

// Subscription entity
export interface Subscription {
  id: string;
  tenantId: string;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  trialEndsAt?: Date;
  maxUsers: number;
  maxContacts: number;
  maxCompanies: number;
  maxOpportunities: number;
  features: {
    aiAssistant: boolean;
    documentIntelligence: boolean;
    apiAccess: boolean;
    advancedAnalytics: boolean;
    // Additional features
  };
  billingDetails?: {
    customerId: string;
    subscriptionId: string;
    // Payment provider specific details
  };
  metadata?: Record<string, any>;
}
```

### Usage Tracking

Each module should track usage against subscription limits:

```typescript
export interface TenantUsage {
  tenantId: string;
  usersCount: number;
  contactsCount: number;
  companiesCount: number;
  opportunitiesCount: number;
  storageUsed: number; // in bytes
  aiCreditsUsed: number;
  lastUpdated: Date;
}
```

## Multi-Tenancy Architecture

### Tenant Model

The tenant is the core entity for multi-tenancy:

```typescript
export interface Tenant {
  id: string;
  name: string;
  slug: string; // URL-friendly identifier
  createdAt: Date;
  updatedAt: Date;
  ownerId: string; // Reference to the user who owns this tenant
  settings: {
    timezone: string;
    locale: string;
    branding: {
      logo?: string;
      primaryColor?: string;
      accentColor?: string;
    };
    // Additional tenant-specific settings
  };
  status: 'active' | 'suspended' | 'deleted';
  subscriptionId?: string; // Reference to current subscription
  domains?: string[]; // Custom domains for this tenant
}
```

### Database Schema Updates

All entities in the system should include a `tenantId` field to enable data isolation:

```typescript
// Example update to the contacts table
export const contacts = pgTable("contacts", {
  id: serial("id").primaryKey(),
  tenantId: text("tenant_id").notNull(), // Add tenant ID to all tables
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  // ... other fields
});

// Example update to the companies table
export const companies = pgTable("companies", {
  id: serial("id").primaryKey(),
  tenantId: text("tenant_id").notNull(), // Add tenant ID to all tables
  name: text("name").notNull(),
  // ... other fields
});

// Add tenant ID to all other tables
```

### Tenant Isolation Strategies

Implement one of the following isolation strategies based on scale requirements:

1. **Schema-based isolation**: Each tenant gets its own database schema
2. **Row-level isolation**: All tenants share tables with a `tenantId` column (recommended for most deployments)
3. **Database-level isolation**: Each tenant gets its own database (for enterprise customers with strict compliance requirements)

For the initial implementation, row-level isolation is recommended as it provides a good balance between isolation and resource efficiency.

## Authentication and Authorization

### User-Tenant Relationship

Users can belong to multiple tenants with different roles in each:

```typescript
export const userTenants = pgTable("user_tenants", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  tenantId: text("tenant_id").notNull(),
  role: text("role").notNull(), // 'owner', 'admin', 'member', 'guest'
  createdAt: timestamp("created_at").defaultNow().notNull(),
  invitedBy: integer("invited_by").references(() => users.id),
  status: text("status").notNull(), // 'active', 'invited', 'suspended'
});
```

### Authentication Flow

1. User authenticates with Firebase Auth or session-based auth
2. After successful authentication, the system retrieves the user's tenants
3. User selects a tenant to work with (or is directed to their default tenant)
4. The system creates a tenant-specific session that includes:
   - User ID
   - Tenant ID
   - User's role in the tenant
   - Subscription information

### JWT Structure

For API authentication, use JWTs with tenant information:

```typescript
// Example JWT payload
{
  "sub": "user123",
  "email": "<EMAIL>",
  "tenant_id": "tenant456",
  "role": "admin",
  "plan": "professional",
  "exp": 1672531200
}
```

## Data Isolation and Access Control

### Middleware for Tenant Isolation

Implement middleware that enforces tenant isolation on all API requests:

```typescript
// Tenant isolation middleware
const tenantIsolationMiddleware = (req: Request, res: Response, next: Function) => {
  // Get tenant ID from session or JWT
  const tenantId = req.session.tenantId || req.user.tenant_id;
  
  if (!tenantId) {
    return res.status(401).json({ message: "Tenant not specified" });
  }
  
  // Add tenant ID to request object for use in route handlers
  req.tenantId = tenantId;
  
  // Continue to route handler
  next();
};

// Apply middleware to all protected routes
app.use("/api", authenticateUser, tenantIsolationMiddleware);
```

### Query Filtering

All database queries should include tenant filtering:

```typescript
// Example: Getting contacts for the current tenant
async getContacts(limit = 100, offset = 0, tenantId: string): Promise<Contact[]> {
  return db.select().from(contacts)
    .where(eq(contacts.tenantId, tenantId))
    .limit(limit)
    .offset(offset);
}
```

### Subscription Enforcement

Implement middleware to enforce subscription limits:

```typescript
// Subscription limits middleware
const subscriptionLimitsMiddleware = async (req: Request, res: Response, next: Function) => {
  const tenantId = req.tenantId;
  
  // Get current subscription and usage
  const subscription = await getSubscription(tenantId);
  const usage = await getTenantUsage(tenantId);
  
  // Check if operation would exceed limits
  if (req.path.includes('/contacts') && req.method === 'POST') {
    if (usage.contactsCount >= subscription.maxContacts) {
      return res.status(403).json({ 
        message: "Contact limit reached for your subscription plan",
        limit: subscription.maxContacts,
        current: usage.contactsCount
      });
    }
  }
  
  // Similar checks for other resources
  
  // Continue to route handler
  next();
};

// Apply middleware after tenant isolation
app.use("/api", authenticateUser, tenantIsolationMiddleware, subscriptionLimitsMiddleware);
```

## Module Integration Guidelines

### Core Backend Module

The core Node.js backend should:

1. Maintain the tenant and subscription database tables
2. Provide APIs for tenant management and subscription status
3. Enforce tenant isolation and subscription limits
4. Track usage metrics for billing purposes

### AI Service Module

The Python-based AI service should:

1. Accept tenant ID with each request
2. Validate tenant access and subscription features
3. Apply different AI capabilities based on subscription tier
4. Track AI usage for billing purposes

Example AI service request:

```python
@app.post("/general", response_model=AIResponse)
async def general_query(request: GeneralQueryRequest):
    # Extract tenant ID from request
    tenant_id = request.tenant_id
    
    # Validate tenant subscription for AI features
    subscription = await get_tenant_subscription(tenant_id)
    if not subscription.features.get('aiAssistant'):
        return AIResponse(
            response="AI Assistant is not available on your current plan. Please upgrade to access this feature.",
            source="subscription_error"
        )
    
    # Process request with tenant context
    return await process_general_query(request.query, request.userId, request.context, tenant_id)
```

### Frontend Module

The React frontend should:

1. Allow users to switch between tenants they have access to
2. Display subscription information and usage metrics
3. Disable features not available in the current subscription
4. Provide upgrade paths for subscription management

## Synchronization Between Modules

### Shared Authentication

All modules should use the same authentication mechanism:

1. **Firebase Auth**: For user authentication
2. **JWT with Tenant Context**: For API authentication between modules
3. **API Keys**: For external integrations, scoped to specific tenants

### Event-Based Synchronization

Use an event system to keep modules in sync:

```typescript
// Example event types
export enum SystemEventType {
  TENANT_CREATED = 'tenant.created',
  TENANT_UPDATED = 'tenant.updated',
  SUBSCRIPTION_CHANGED = 'subscription.changed',
  USER_ADDED_TO_TENANT = 'user.added_to_tenant',
  USER_REMOVED_FROM_TENANT = 'user.removed_from_tenant',
  SUBSCRIPTION_LIMIT_REACHED = 'subscription.limit_reached'
}

// Example event structure
export interface SystemEvent {
  id: string;
  type: SystemEventType;
  tenantId: string;
  data: any;
  timestamp: Date;
}
```

Implement a message queue (Redis, RabbitMQ, or a cloud service) to distribute events between modules.

### Shared Configuration

Maintain a shared configuration service that all modules can access:

1. Tenant settings
2. Subscription details
3. Feature flags
4. System-wide settings

## Implementation Roadmap

### Phase 1: Database Schema Updates

1. Add tenant tables to the database
2. Update existing tables with tenant ID fields
3. Create subscription and usage tracking tables

### Phase 2: Authentication Updates

1. Enhance authentication to support tenant context
2. Implement tenant selection UI
3. Update JWT structure to include tenant information

### Phase 3: Tenant Isolation

1. Implement tenant isolation middleware
2. Update all queries to filter by tenant ID
3. Add subscription limit enforcement

### Phase 4: Subscription Management

1. Implement subscription plans and billing
2. Create usage tracking mechanisms
3. Add subscription management UI

### Phase 5: Module Integration

1. Update AI service to support multi-tenancy
2. Implement event-based synchronization
3. Create shared configuration service

## Conclusion

This multi-tenant subscription architecture provides a scalable foundation for Aizako CRM. By implementing these guidelines across all modules, the system will support multiple tenants with different subscription levels while maintaining proper data isolation and access control.

The architecture is designed to be flexible, allowing for future expansion of subscription tiers and features while maintaining backward compatibility with existing implementations.
