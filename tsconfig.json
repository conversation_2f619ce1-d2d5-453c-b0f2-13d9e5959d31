{"include": ["client/src/**/*", "shared/**/*", "server/**/*", "server/@types/**/*", "client/src/@types/**/*"], "exclude": ["node_modules", "build", "dist"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "react-jsx", "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "express-session", "jest"], "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"], "@types/*": ["./shared/types/*"], "@schemas/*": ["./shared/schemas/*"], "@server/*": ["./server/*"], "@models/*": ["./server/models/*"]}, "typeRoots": ["./node_modules/@types", "./server/@types"]}}