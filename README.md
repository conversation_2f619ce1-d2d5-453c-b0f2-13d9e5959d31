# Aizako CRM

## Overview

Aizako CRM is an AI-first customer relationship management system designed specifically for African markets. It leverages a hybrid architecture with advanced AI capabilities to streamline contact, lead, and opportunity management.

## Key Features

- **AI-Powered Workflow Automation**: Automate routine tasks and gain intelligent insights
- **Conversational Interface**: Natural language interactions powered by CopilotKit
- **Smart Relationship Mapping**: Visualize and navigate complex B2B relationships
- **Document Intelligence**: Extract and analyze information from documents
- **Vector Search**: Semantic search over communications and documents using MongoDB Vector Search
- **Deal Brief**: AI-generated deal summaries with sentiment analysis and next best actions
- **Auto-Stage Classifier**: Automatically classify deals in the right pipeline stage based on communications
- **Win/Loss Analyzer**: AI-driven analysis of won and lost opportunities to identify patterns and improve sales process
- **Follow-up Coach**: Intelligent follow-up recommendations to keep deals moving forward
- **Meeting Prep Wizard**: AI-powered preparation for important meetings
- **Objection Handler**: Smart responses to common sales objections
- **Proposal Generator**: Create professional proposals with AI assistance
- **African Market Optimization**: Built for network conditions and business requirements in African markets

## Tech Stack

- **Frontend**: React with CopilotKit Cloud integration
- **AI Layer**: Python-based with CrewAI agents and document services
- **Core Backend**: Node.js with Express
- **Bridge**: AI Bridge for seamless communication between JavaScript and Python components
- **Authentication**: Firebase Auth
- **Storage**: MongoDB for flexible data storage
- **Vector Search**: MongoDB Vector Search for semantic search capabilities
- **AI Models**: Voyage AI for embeddings and text generation

## Setup Instructions

Aizako CRM is a cloud-based solution that doesn't require self-hosting. To access the system:

1. Contact our sales team for a demo account
2. Log in using the provided credentials at app.aizako.com
3. Configure your organization settings and user permissions
4. Import your existing contacts and opportunities or start fresh

## Architecture

The system uses a hybrid approach with:
- Python microservices for AI capabilities
- Node.js for core application logic
- React for the frontend UI
- Auto-starting AI bridge for seamless integration

## Documentation

Comprehensive documentation is available in the `docs` directory:

### User Guides
- [Win/Loss Analyzer and Follow-up Coach](docs/user-guides/win-loss-follow-up-features.md)
- [Proposal Generator](docs/user-guides/proposal-generator-features.md)

### Technical Documentation
- [Win/Loss Analyzer and Follow-up Coach Implementation](docs/technical/win-loss-follow-up-implementation.md)
- [Proposal Generator Implementation](docs/technical/proposal-generator-implementation.md)
- [Subscription System](docs/subscription-system.md)
- [MongoDB Atlas Storage](docs/mongodb-atlas-storage.md)

### Repository Documentation
- [Repository Documentation](docs/repositories.md)
- [Repository Usage Guide](docs/repository-usage-guide.md)
- [Repository Testing Guide](docs/repository-testing-guide.md)
- [Repository Design Patterns](docs/repository-design-patterns.md)

### Quick Reference Guides
- [Proposal Generator Quick Reference](docs/quick-reference/proposal-generator-quick-reference.md)

### Release Notes
- [Proposal Generator Enhancements](docs/release-notes/proposal-generator-enhancements.md)

### Additional Resources
- [MongoDB Implementation](README-MONGODB.md)
- [AI Service](ai_service/README.md)

## License

Copyright (c) 2024 Aizako CRM. All rights reserved.