[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "llama-index-llms-openai>=0.3.30",
    "llama-index>=0.12.28",
    "llama-index-embeddings-openai>=0.3.1",
    "langchain-openai>=0.3.12",
    "nltk>=3.9.1",
    "llama-index-core>=0.12.28",
    "uvicorn>=0.34.0",
    "fastapi>=0.115.12",
    "pydantic>=2.11.3",
    "crewai>=0.108.0",
    "openai>=1.71.0",
    "redis>=5.2.1",
    "python-dotenv>=1.1.0",
]
