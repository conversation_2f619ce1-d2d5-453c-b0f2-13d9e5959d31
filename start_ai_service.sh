#!/bin/bash
# Function to kill existing processes
cleanup_existing() {
  echo "Cleaning up existing processes..."
  # Kill any running uvicorn processes on port 8000
  local pid=$(lsof -t -i:8000 2>/dev/null)
  if [ ! -z "$pid" ]; then
    echo "Killing process on port 8000: $pid"
    kill -15 $pid 2>/dev/null || kill -9 $pid 2>/dev/null
    sleep 1
  fi
  
  # Check for pid file
  if [ -f "ai_service.pid" ]; then
    local old_pid=$(cat ai_service.pid)
    if [ ! -z "$old_pid" ] && ps -p $old_pid > /dev/null 2>&1; then
      echo "Killing old service with PID: $old_pid"
      kill -15 $old_pid 2>/dev/null || kill -9 $old_pid 2>/dev/null
      sleep 1
    fi
    rm -f ai_service.pid
  fi
}

# Set environment variable for OpenAI API key if needed
# export OPENAI_API_KEY only if it's not already set
if [ -z "$OPENAI_API_KEY" ]; then
  # Read it from environment or .env file
  if [ -f ".env" ]; then
    source .env
  fi
fi

# Clean up any existing processes first
cleanup_existing

# Start the service
cd ai_service
echo "Starting AI service..."
# Clear log file
> uvicorn.log
# Start service with nohup
nohup python -m uvicorn main:app --host 0.0.0.0 --port 8000 > uvicorn.log 2>&1 &
echo $! > ../ai_service.pid
echo "AI service started with PID: $(cat ../ai_service.pid)"

# Wait a moment to ensure service has time to start
sleep 3

# Monitor startup
MAX_RETRIES=10
RETRY_COUNT=0
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  # Check if service is still running
  if ! ps -p $(cat ../ai_service.pid) > /dev/null 2>&1; then
    echo "ERROR: AI service terminated unexpectedly. Check ai_service/uvicorn.log for details."
    cat uvicorn.log | tail -n 20
    exit 1
  fi
  
  # Check if service is responding
  if curl -s http://localhost:8000/status > /dev/null 2>&1; then
    echo "SUCCESS: AI service is running and responding!"
    exit 0
  fi
  
  echo "Waiting for service to start... (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)"
  sleep 2
  RETRY_COUNT=$((RETRY_COUNT+1))
done

echo "WARNING: AI service started with PID $(cat ../ai_service.pid) but is not responding to status checks."
echo "Recent log output:"
cat uvicorn.log | tail -n 20
exit 0
