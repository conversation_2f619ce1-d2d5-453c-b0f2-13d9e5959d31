opilotKit Setup and Integration Guide for Teams
1. Architecture Overview
CopilotKit provides a framework for integrating AI assistants into your application with three key components:

Frontend UI Components: React components for chat interfaces
CopilotKit Cloud: Managed service for AI orchestration
LangGraph Integration: Connect to custom agents built with LangGraph
The architecture follows this flow:

User interacts with CopilotKit UI components
Requests go through Next.js API route to CopilotKit Cloud
CopilotKit Cloud connects to your LangGraph agents via tunnel
Responses flow back through the same path
2. Setup Process
Step 1: Installation
bash
CopyInsert
# Frontend packages
npm install @copilotkit/react-core @copilotkit/react-ui @copilotkit/runtime

# Backend packages (for FastAPI integration)
pip install copilotkit-sdk copilotkit-fastapi
Step 2: CopilotKit Cloud Account
Sign up at cloud.copilotkit.ai
Create a new project and get your public API key
Add to your environment variables:
CopyInsert
NEXT_PUBLIC_COPILOT_KEY=your-copilot-cloud-public-key
Step 3: Frontend Setup
Root Layout Configuration:
tsx
CopyInsert
// app/layout.tsx
import { CopilotKit } from "@copilotkit/react-core";
import "@copilotkit/react-ui/styles.css";

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <CopilotKit 
          runtimeUrl="/api/copilotkit"
          publicApiKey={process.env.NEXT_PUBLIC_COPILOT_KEY}
        >
          {children}
        </CopilotKit>
      </body>
    </html>
  );
}
Next.js API Route:
tsx
CopyInsert
// app/api/copilotkit/route.ts
import { NextRequest } from 'next/server';
import {
  CopilotRuntime,
  langGraphPlatformEndpoint,
  copilotRuntimeNextJSAppRouterEndpoint,
} from '@copilotkit/runtime';

const runtime = new CopilotRuntime({
  remoteEndpoints: [
    langGraphPlatformEndpoint({
      agents: [
        {
          name: 'my_agent',
          description: 'Description of what this agent does'
        },
        // Add more agents as needed
      ]
    }),
  ],
});

export async function POST(req: NextRequest) {
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    endpoint: '/api/copilotkit',
  });
  return handleRequest(req);
}

// CORS support
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
UI Components:
tsx
CopyInsert
import { CopilotChat } from "@copilotkit/react-ui";

export function ChatComponent() {
  return (
    <CopilotChat 
      className="h-[500px] w-full"
      context={{
        agentType: "my_agent",
        // Additional context your agent needs
      }}
    />
  );
}
Step 4: FastAPI Backend Setup
python
CopyInsert
from fastapi import FastAPI
from copilotkit_fastapi import add_fastapi_endpoint
from copilotkit_sdk import CopilotKitSDK

app = FastAPI()

# Initialize the SDK
sdk = CopilotKitSDK()

# Add the endpoint to your FastAPI app
add_fastapi_endpoint(app, sdk, "/copilotkit", use_thread_pool=False)

# Your LangGraph agent setup
# ...
3. Development Workflow
Local Development
Start your FastAPI server:
bash
CopyInsert in Terminal
uvicorn main:app --reload --port 8000
Start the CopilotKit tunnel:
bash
CopyInsert
# First authenticate (one-time)
npx copilotkit@latest login

# Start tunnel - MUST use same port as FastAPI server
npx copilotkit@latest dev --port 8000
Start Next.js development server:
bash
CopyInsert in Terminal
npm run dev
Managing LangGraph Agents
List available agents:
bash
CopyInsert in Terminal
npx copilotkit@latest list-agents
Deploy a new agent:
bash
CopyInsert in Terminal
npx copilotkit@latest deploy-agent --name my_agent --path ./my_agent_code
Update an existing agent:
bash
CopyInsert in Terminal
npx copilotkit@latest update-agent --name my_agent --path ./my_agent_code
4. Production Deployment
Deploy FastAPI server to a cloud provider (AWS, GCP, Azure)
Configure CopilotKit Cloud to connect to your production server
Deploy Next.js frontend with proper environment variables
5. Critical Requirements & Common Issues
Port Matching: FastAPI server and CopilotKit tunnel MUST use the same port
Agent Naming: Use consistent agent names across all configurations
CORS Support: Ensure your API routes handle OPTIONS requests
Authentication: Always authenticate with npx copilotkit@latest login
Provider Configuration: Use runtimeUrl (not url) in CopilotKit provider
6. Best Practices
Context Management: Pass relevant context to agents via the context prop
Error Handling: Wrap CopilotKit components in error boundaries
Multiple Agents: Create specialized agents for different tasks
Testing: Test agents thoroughly before deployment
Security: Never expose sensitive API keys in client-side code
7. Troubleshooting
404 Errors: Check FastAPI server, tunnel, and API route configuration
Authentication Issues: Verify API keys and run login command
Agent Connection Issues: Check agent names and tunnel logs
UI Errors: Ensure CopilotKit provider is properly configured at the root level