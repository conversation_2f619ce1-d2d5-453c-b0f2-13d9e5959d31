version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    # Look for package.json and package-lock.json files in the root directory
    directory: "/"
    # Check for updates once a week (on Monday)
    schedule:
      interval: "weekly"
      day: "monday"
    # Group all updates together
    groups:
      dependencies:
        patterns:
          - "*"
    # Set reviewers for pull requests
    reviewers:
      - "Jpkay"
    # Limit the number of open pull requests
    open-pull-requests-limit: 10
    # Specify labels for pull requests
    labels:
      - "dependencies"
      - "automerge"
    # Allow up to 5 updates per pull request
    pull-request-branch-name:
      separator: "-"
    # Specify version update strategy
    versioning-strategy: auto
    # Ignore certain dependencies
    ignore:
      # Ignore major version updates for these packages
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "@types/react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "@types/react-dom"
        update-types: ["version-update:semver-major"]
    # Set security updates to be prioritized
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
    commit-message:
      prefix: "deps"
      include: "scope"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
    reviewers:
      - "Jpkay"
    labels:
      - "dependencies"
      - "github-actions"
      - "automerge"
    commit-message:
      prefix: "ci"
      include: "scope"
