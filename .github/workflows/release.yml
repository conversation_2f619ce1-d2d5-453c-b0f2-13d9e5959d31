name: Release

on:
  push:
    branches:
      - main

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: |
          npm run lint
          npm run test:unit
          npm run test:integration
        env:
          MONGODB_ENABLED: true
          NODE_ENV: test
      
      - name: Build
        run: npm run build
      
      - name: Setup Git identity
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
      
      - name: Generate release notes
        id: release-notes
        run: |
          # Get the latest tag
          LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
          
          # Generate release notes
          echo "# Changes since $LATEST_TAG" > RELEASE_NOTES.md
          echo "" >> RELEASE_NOTES.md
          
          # Features
          echo "## Features" >> RELEASE_NOTES.md
          git log $LATEST_TAG..HEAD --pretty=format:"- %s" --grep="^feat" >> RELEASE_NOTES.md
          echo "" >> RELEASE_NOTES.md
          
          # Bug fixes
          echo "## Bug Fixes" >> RELEASE_NOTES.md
          git log $LATEST_TAG..HEAD --pretty=format:"- %s" --grep="^fix" >> RELEASE_NOTES.md
          echo "" >> RELEASE_NOTES.md
          
          # Performance improvements
          echo "## Performance Improvements" >> RELEASE_NOTES.md
          git log $LATEST_TAG..HEAD --pretty=format:"- %s" --grep="^perf" >> RELEASE_NOTES.md
          echo "" >> RELEASE_NOTES.md
          
          # Other changes
          echo "## Other Changes" >> RELEASE_NOTES.md
          git log $LATEST_TAG..HEAD --pretty=format:"- %s" --grep="^chore\|^docs\|^style\|^refactor\|^test" >> RELEASE_NOTES.md
          
          # Set release notes as output
          RELEASE_NOTES=$(cat RELEASE_NOTES.md)
          echo "RELEASE_NOTES<<EOF" >> $GITHUB_ENV
          echo "$RELEASE_NOTES" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
      
      - name: Semantic Release
        uses: cycjimmy/semantic-release-action@v3
        with:
          semantic_version: 19
          branches: |
            [
              'main'
            ]
          extra_plugins: |
            @semantic-release/changelog
            @semantic-release/git
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        if: steps.semantic-release.outputs.new_release_published == 'true'
        with:
          tag_name: v${{ steps.semantic-release.outputs.new_release_version }}
          name: Release v${{ steps.semantic-release.outputs.new_release_version }}
          body: ${{ env.RELEASE_NOTES }}
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
