name: Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
  
  # Auto-deploy to staging on successful release
  workflow_run:
    workflows: ["Release"]
    types:
      - completed

jobs:
  prepare:
    name: Prepare Deployment
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'workflow_dispatch' || github.event.workflow_run.conclusion == 'success' }}
    
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
      version: ${{ steps.set-version.outputs.version }}
    
    steps:
      - name: Set environment
        id: set-env
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi
      
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Set version
        id: set-version
        run: |
          # Get the latest tag
          VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
          echo "version=${VERSION#v}" >> $GITHUB_OUTPUT
  
  deploy:
    name: Deploy to ${{ needs.prepare.outputs.environment }}
    runs-on: ubuntu-latest
    needs: prepare
    environment: ${{ needs.prepare.outputs.environment }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build
        run: npm run build
        env:
          NODE_ENV: ${{ needs.prepare.outputs.environment }}
      
      - name: Create deployment package
        run: |
          mkdir -p deployment
          cp -r dist deployment/
          cp package.json deployment/
          cp package-lock.json deployment/
          cp -r .env.${{ needs.prepare.outputs.environment }} deployment/.env
          cd deployment && npm ci --production
          cd .. && tar -czf deployment.tar.gz deployment
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Upload to S3
        run: |
          aws s3 cp deployment.tar.gz s3://${{ secrets.S3_BUCKET }}/aizako-crm-${{ needs.prepare.outputs.environment }}-${{ needs.prepare.outputs.version }}.tar.gz
      
      - name: Deploy to EC2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            # Download deployment package
            aws s3 cp s3://${{ secrets.S3_BUCKET }}/aizako-crm-${{ needs.prepare.outputs.environment }}-${{ needs.prepare.outputs.version }}.tar.gz /tmp/
            
            # Create deployment directory
            mkdir -p /opt/aizako-crm-${{ needs.prepare.outputs.environment }}-${{ needs.prepare.outputs.version }}
            
            # Extract deployment package
            tar -xzf /tmp/aizako-crm-${{ needs.prepare.outputs.environment }}-${{ needs.prepare.outputs.version }}.tar.gz -C /opt/aizako-crm-${{ needs.prepare.outputs.environment }}-${{ needs.prepare.outputs.version }} --strip-components=1
            
            # Create symlink to current deployment
            ln -sfn /opt/aizako-crm-${{ needs.prepare.outputs.environment }}-${{ needs.prepare.outputs.version }} /opt/aizako-crm-${{ needs.prepare.outputs.environment }}-current
            
            # Restart service
            sudo systemctl restart aizako-crm-${{ needs.prepare.outputs.environment }}
            
            # Clean up old deployments (keep last 5)
            cd /opt && ls -1d aizako-crm-${{ needs.prepare.outputs.environment }}-* | grep -v "current" | sort -r | tail -n +6 | xargs rm -rf
      
      - name: Create deployment record
        uses: chrnorm/deployment-action@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          environment: ${{ needs.prepare.outputs.environment }}
          ref: ${{ github.sha }}
          description: 'Deployment to ${{ needs.prepare.outputs.environment }} environment'
      
      - name: Verify deployment
        run: |
          # Wait for service to start
          sleep 10
          
          # Check if service is running
          curl -s -o /dev/null -w "%{http_code}" https://${{ needs.prepare.outputs.environment == 'production' && 'app.aizako-crm.com' || 'staging.aizako-crm.com' }}/api/health | grep 200
      
      - name: Notify on success
        if: success()
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "text": "✅ Successfully deployed version ${{ needs.prepare.outputs.version }} to ${{ needs.prepare.outputs.environment }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "✅ *Successfully deployed to ${{ needs.prepare.outputs.environment }}*"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Version:*\n${{ needs.prepare.outputs.version }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Environment:*\n${{ needs.prepare.outputs.environment }}"
                    }
                  ]
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Deployment"
                      },
                      "url": "https://${{ needs.prepare.outputs.environment == 'production' && 'app.aizako-crm.com' || 'staging.aizako-crm.com' }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
  
  rollback:
    name: Rollback
    runs-on: ubuntu-latest
    needs: [prepare, deploy]
    if: failure() && needs.deploy.result == 'failure'
    environment: ${{ needs.prepare.outputs.environment }}
    
    steps:
      - name: Rollback deployment
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            # Get previous deployment
            PREV_DEPLOYMENT=$(cd /opt && ls -1d aizako-crm-${{ needs.prepare.outputs.environment }}-* | grep -v "current" | sort -r | head -n 1)
            
            # Create symlink to previous deployment
            ln -sfn /opt/$PREV_DEPLOYMENT /opt/aizako-crm-${{ needs.prepare.outputs.environment }}-current
            
            # Restart service
            sudo systemctl restart aizako-crm-${{ needs.prepare.outputs.environment }}
      
      - name: Notify on rollback
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "text": "⚠️ Deployment failed, rolled back to previous version",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "⚠️ *Deployment failed, rolled back to previous version*"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Environment:*\n${{ needs.prepare.outputs.environment }}"
                    }
                  ]
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Workflow"
                      },
                      "url": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
