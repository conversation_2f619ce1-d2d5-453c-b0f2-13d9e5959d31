name: Security Scan

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 0 * * 0'  # Run weekly on Sunday at midnight

jobs:
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run npm audit
        run: npm audit --production
        continue-on-error: true
      
      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/node@master
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
      
      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript, typescript
      
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
      
      - name: Generate security report
        run: |
          echo "# Security Scan Report" > security-report.md
          echo "## npm audit" >> security-report.md
          npm audit --json | jq -r '.advisories | to_entries | .[] | .value | "- " + .title + " (severity: " + .severity + ")"' >> security-report.md || echo "No vulnerabilities found" >> security-report.md
          echo "## Snyk" >> security-report.md
          cat .snyk-report.json | jq -r '.vulnerabilities[] | "- " + .packageName + ": " + .title + " (severity: " + .severity + ")"' >> security-report.md || echo "No vulnerabilities found" >> security-report.md
        continue-on-error: true
      
      - name: Upload security report
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: security-report.md
          retention-days: 7
