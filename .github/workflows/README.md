# GitHub Actions Workflows

This directory contains GitHub Actions workflows for the Aizako CRM project.

## Tests Workflow

The `tests.yml` workflow runs all tests for the project. It is triggered on:
- Push to the `main` branch
- Pull requests to the `main` branch

### Jobs

The workflow consists of the following jobs:

#### 1. Unit Tests

Runs unit tests using Vitest.

- **Command**: `npm run test:unit`
- **Artifacts**: Test coverage report

#### 2. Integration Tests

Runs integration tests using Jest.

- **Command**: `npm run test:integration`
- **Environment**:
  - `MONGODB_ENABLED=true`
  - `NODE_ENV=test`

#### 3. UI Tests

Runs UI tests using Playwright.

- **Command**: `npm run test:ui`
- **Artifacts**: Playwright report

#### 4. Build

Runs type checking and builds the project.

- **Commands**:
  - `npm run check` (TypeScript type checking)
  - `npm run build` (Build the project)

### Dependencies

The jobs have the following dependencies:
- Integration Tests depends on Unit Tests
- UI Tests depends on Integration Tests
- Build depends on Unit Tests and Integration Tests

This ensures that the most critical tests run first, and the workflow fails fast if there are issues.

## Adding New Workflows

When adding new workflows, please follow these guidelines:

1. Create a new YAML file in this directory
2. Add a descriptive name and comment at the top of the file
3. Document the workflow in this README
4. Ensure the workflow is efficient and only runs when necessary
5. Use appropriate caching to speed up builds
6. Upload artifacts for any generated reports or build outputs

## Troubleshooting

If a workflow fails, check the following:

1. Look at the specific step that failed in the GitHub Actions UI
2. Check if the same tests pass locally
3. Verify that all dependencies are correctly installed
4. Check for environment-specific issues (e.g., file paths, environment variables)

For persistent issues, consider adding more detailed logging to the failing step.
