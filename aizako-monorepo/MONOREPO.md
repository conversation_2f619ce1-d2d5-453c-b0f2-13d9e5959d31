# Aizako Monorepo Architecture

This document describes the architecture of the Aizako monorepo, which contains all modules and applications for the Aizako platform.

## Structure

The monorepo is organized into two main directories:

```
/packages
  ├─ crm             (CRM module)
  ├─ scribe          (Scribe module - to be integrated)
  ├─ invoicing       (Invoicing module - future)
  ├─ onboarding      (Onboarding module - future)
  ├─ ui-kit          (Shared design system)
  └─ core-lib        (Shared core libraries)
/apps
  ├─ demo-saas       (Demo SaaS application)
  └─ cli             (CLI tool for module management)
```

### Packages

Packages are reusable libraries that can be imported by other packages or apps. They are published to the npm registry under the `@aizako` scope.

#### core-lib

The `core-lib` package contains shared functionality used across all modules:

- **Authentication**: Firebase authentication utilities and components
- **Tenant Management**: Multi-tenant infrastructure and middleware
- **Database Utilities**: MongoDB connection and query utilities
- **Type System**: Shared types, interfaces, and Zod schemas

#### ui-kit

The `ui-kit` package contains shared UI components used across all modules:

- **Components**: Reusable UI components
- **Hooks**: Custom React hooks
- **Styles**: Shared styles and themes
- **Utils**: UI-related utilities

#### crm

The `crm` package contains the CRM module:

- **Models**: MongoDB models for contacts, companies, opportunities, and activities
- **Services**: Business logic and data access
- **API**: API routes and controllers
- **Components**: CRM-specific UI components

#### scribe

The `scribe` package will contain the Scribe module (to be integrated).

#### invoicing

The `invoicing` package will contain the Invoicing module (future).

#### onboarding

The `onboarding` package will contain the Onboarding module (future).

### Apps

Apps are standalone applications that use the packages.

#### demo-saas

The `demo-saas` app is a demo application that showcases the integration of all modules.

#### cli

The `cli` app is a command-line tool for managing modules.

## Dependencies

The dependencies between packages and apps are managed using pnpm workspaces. Each package and app has its own `package.json` file that specifies its dependencies.

Packages can depend on other packages using the `workspace:*` syntax:

```json
{
  "dependencies": {
    "@aizako/core-lib": "workspace:*"
  }
}
```

## Building

The monorepo uses Turborepo for building packages and apps. The build process is defined in the `turbo.json` file at the root of the monorepo.

To build all packages and apps:

```bash
pnpm build
```

To build a specific package or app:

```bash
pnpm --filter @aizako/core-lib build
```

## Testing

Each package and app has its own tests. The testing framework is Vitest for unit tests and Playwright for end-to-end tests.

To run all tests:

```bash
pnpm test
```

To run tests for a specific package or app:

```bash
pnpm --filter @aizako/core-lib test
```

## Versioning

The monorepo uses Changesets for versioning packages. When making changes to a package, you should create a changeset that describes the changes and specifies the version bump.

To create a changeset:

```bash
pnpm changeset
```

When a PR with changesets is merged, the CI/CD pipeline will automatically create a PR that bumps the versions of the affected packages.

## CI/CD

The monorepo uses GitHub Actions for CI/CD. The workflows are defined in the `.github/workflows` directory.

- `ci.yml`: Runs on every push and PR to the main branch. It builds and tests all packages and apps.
- `release.yml`: Runs on every push to the main branch. It creates a PR that bumps the versions of the affected packages based on the changesets.

## License

Copyright (c) 2024 Aizako. All rights reserved.
