# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
build
out
.next
.nuxt
.turbo

# Testing
coverage
.nyc_output

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Misc
.cache
.temp
.tmp
tmp
temp
