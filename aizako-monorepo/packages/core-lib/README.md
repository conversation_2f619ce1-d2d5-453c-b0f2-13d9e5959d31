# Aizako Core Library

The Aizako Core Library (`@aizako/core-lib`) is a shared library that provides common functionality used across all Aizako modules. It includes authentication, tenant management, database utilities, and a comprehensive type system.

## Installation

```bash
pnpm add @aizako/core-lib
```

## Features

### Authentication

The authentication module provides Firebase authentication utilities and components:

```typescript
import { 
  initializeFirebaseAuth, 
  signInWithEmailAndPassword, 
  signInWithGoogle, 
  signOut, 
  createUserWithEmailAndPassword,
  AuthProvider,
  useAuth
} from '@aizako/core-lib';

// Initialize Firebase
const firebaseConfig = {
  apiKey: 'your-api-key',
  projectId: 'your-project-id',
  appId: 'your-app-id',
};
initializeFirebaseAuth(firebaseConfig);

// Use the AuthProvider component
function App() {
  return (
    <AuthProvider>
      <YourApp />
    </AuthProvider>
  );
}

// Use the useAuth hook
function LoginButton() {
  const { loginWithEmailPassword, isAuthenticated } = useAuth();
  
  const handleLogin = async () => {
    try {
      await loginWithEmailPassword('<EMAIL>', 'password');
    } catch (error) {
      console.error('Login error:', error);
    }
  };
  
  return (
    <button onClick={handleLogin} disabled={isAuthenticated}>
      Login
    </button>
  );
}
```

### Tenant Management

The tenant management module provides multi-tenant infrastructure and components:

```typescript
import { 
  TenantProvider, 
  useTenant,
  tenantIsolationMiddleware,
  tenantAccessMiddleware,
  moduleMiddleware
} from '@aizako/core-lib';

// Use the TenantProvider component
function App() {
  return (
    <AuthProvider>
      <TenantProvider>
        <YourApp />
      </TenantProvider>
    </AuthProvider>
  );
}

// Use the useTenant hook
function TenantInfo() {
  const { tenant, isLoading } = useTenant();
  
  if (isLoading) {
    return <div>Loading tenant information...</div>;
  }
  
  if (!tenant) {
    return <div>No tenant information available</div>;
  }
  
  return (
    <div>
      <h2>Tenant: {tenant.name}</h2>
      <p>Plan: {tenant.plan}</p>
    </div>
  );
}

// Use the middleware in your Express app
app.use(tenantIsolationMiddleware);
app.use('/api/crm', moduleMiddleware('crm'));
```

### Database Utilities

The database module provides MongoDB connection and query utilities:

```typescript
import { 
  connectToMongoDB, 
  disconnectFromMongoDB,
  addTenantFilter
} from '@aizako/core-lib';

// Connect to MongoDB
await connectToMongoDB('mongodb://localhost:27017/aizako');

// Add tenant filter to a query
const query = Contact.find();
const tenantQuery = addTenantFilter(query, 'tenant-id');
const contacts = await tenantQuery.exec();

// Disconnect from MongoDB
await disconnectFromMongoDB();
```

### Type System

The type system module provides shared types, interfaces, and Zod schemas:

```typescript
import { 
  User, 
  Tenant, 
  UserSchema, 
  TenantSchema,
  isUser,
  isTenant
} from '@aizako/core-lib';

// Use the types
const user: User = {
  id: '123',
  email: '<EMAIL>',
  displayName: 'User',
  photoURL: null,
  tenantId: 'tenant-id',
  role: 'user',
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Use the schemas for validation
const result = UserSchema.safeParse(user);
if (result.success) {
  console.log('User is valid');
} else {
  console.error('User is invalid:', result.error);
}

// Use the type guards
if (isUser(user)) {
  console.log('Value is a User');
}
```

## Development

### Project Structure

```
/packages/core-lib
  ├─ src
  │   ├─ auth         (Authentication module)
  │   ├─ tenancy      (Tenant management module)
  │   ├─ types        (Type system)
  │   ├─ schemas      (Zod schemas)
  │   ├─ utils        (Utility functions)
  │   └─ index.ts     (Entry point)
  ├─ package.json
  ├─ tsconfig.json
  └─ tsup.config.ts
```

### Building

```bash
pnpm build
```

This will build the library using tsup and output the result to the `dist` directory.

### Testing

```bash
pnpm test
```

This will run the tests using Vitest.

## License

Copyright (c) 2024 Aizako. All rights reserved.
