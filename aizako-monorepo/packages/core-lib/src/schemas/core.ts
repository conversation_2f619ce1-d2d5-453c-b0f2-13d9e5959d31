import { z } from 'zod';

/**
 * Base entity schema
 */
export const BaseEntitySchema = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Tenant-scoped entity schema
 */
export const TenantScopedEntitySchema = BaseEntitySchema.extend({
  tenantId: z.string(),
});

/**
 * Pagination parameters schema
 */
export const PaginationParamsSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().default(10),
});

/**
 * Sorting parameters schema
 */
export const SortingParamsSchema = z.object({
  field: z.string(),
  direction: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * Query parameters schema
 */
export const QueryParamsSchema = z.object({
  pagination: PaginationParamsSchema.optional(),
  sorting: SortingParamsSchema.optional(),
  filters: z.record(z.any()).optional(),
});
