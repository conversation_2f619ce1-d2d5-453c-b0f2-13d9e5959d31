import {
  User as FirebaseUser,
  GoogleAuthProvider,
  createUserWithEmailAndPassword as firebaseCreateUser,
  signInWithEmailAndPassword as firebaseSignIn,
  signInWithPopup,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  update<PERSON>rofile,
  getAuth,
  Auth,
  UserCredential
} from "firebase/auth";
import { initializeApp, FirebaseApp } from "firebase/app";
import { User } from './types';

let app: FirebaseApp | null = null;
let auth: Auth | null = null;
let googleProvider: GoogleAuthProvider | null = null;

/**
 * Initialize Firebase authentication
 * @param config Firebase configuration
 */
export function initializeFirebaseAuth(config: Record<string, string>): void {
  if (app) {
    console.warn('Firebase already initialized');
    return;
  }

  const firebaseConfig = {
    apiKey: config.apiKey,
    authDomain: `${config.projectId}.firebaseapp.com`,
    projectId: config.projectId,
    storageBucket: `${config.projectId}.appspot.com`,
    appId: config.appId,
  };

  app = initializeApp(firebaseConfig);
  auth = getAuth(app);
  googleProvider = new GoogleAuthProvider();

  // Configure Google provider with custom parameters
  googleProvider.setCustomParameters({
    prompt: "select_account" // Force account selection even when one account is available
  });

  console.log('Firebase Auth initialized successfully');
}

/**
 * Format Firebase user to our User type
 * @param firebaseUser Firebase user
 * @returns Formatted user
 */
function formatUser(firebaseUser: FirebaseUser): User {
  return {
    id: firebaseUser.uid,
    email: firebaseUser.email || '',
    displayName: firebaseUser.displayName || null,
    photoURL: firebaseUser.photoURL || null,
    tenantId: '', // This will be set by the backend
    role: 'user', // Default role
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Get the current authenticated user
 * @returns Promise that resolves to the current user or null
 */
export function getCurrentUser(): Promise<User | null> {
  return new Promise((resolve, reject) => {
    if (!auth) {
      reject(new Error('Firebase Auth not initialized'));
      return;
    }

    const unsubscribe = onAuthStateChanged(
      auth,
      (firebaseUser) => {
        unsubscribe();
        if (firebaseUser) {
          resolve(formatUser(firebaseUser));
        } else {
          resolve(null);
        }
      },
      reject
    );
  });
}

/**
 * Sign in with email and password
 * @param email User email
 * @param password User password
 * @returns Promise that resolves to the user
 */
export async function signInWithEmailAndPassword(
  email: string,
  password: string
): Promise<User> {
  if (!auth) {
    throw new Error('Firebase Auth not initialized');
  }

  try {
    const userCredential: UserCredential = await firebaseSignIn(auth, email, password);
    return formatUser(userCredential.user);
  } catch (error: any) {
    console.error('Login error:', error);
    let errorMessage = 'Invalid credentials';

    // Handle specific Firebase auth errors
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      errorMessage = 'Invalid email or password';
    } else if (error.code === 'auth/too-many-requests') {
      errorMessage = 'Too many login attempts. Please try again later.';
    }

    throw new Error(errorMessage);
  }
}

/**
 * Sign in with Google
 * @returns Promise that resolves to the user
 */
export async function signInWithGoogle(): Promise<User> {
  if (!auth || !googleProvider) {
    throw new Error('Firebase Auth not initialized');
  }

  try {
    const userCredential: UserCredential = await signInWithPopup(auth, googleProvider);
    return formatUser(userCredential.user);
  } catch (error: any) {
    console.error('Google login error:', error);
    throw new Error(error.message || 'Could not log in with Google');
  }
}

/**
 * Sign out the current user
 * @returns Promise that resolves when sign out is complete
 */
export async function signOut(): Promise<void> {
  if (!auth) {
    throw new Error('Firebase Auth not initialized');
  }

  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Logout error:', error);
    throw new Error('Could not log out. Please try again.');
  }
}

/**
 * Create a new user with email and password
 * @param email User email
 * @param password User password
 * @param displayName User display name
 * @returns Promise that resolves to the created user
 */
export async function createUserWithEmailAndPassword(
  email: string,
  password: string,
  displayName: string
): Promise<User> {
  if (!auth) {
    throw new Error('Firebase Auth not initialized');
  }

  try {
    const userCredential: UserCredential = await firebaseCreateUser(auth, email, password);

    // Update profile if displayName is provided
    if (displayName) {
      try {
        await updateProfile(userCredential.user, {
          displayName
        });
      } catch (error) {
        console.error('Error updating user profile:', error);
      }
    }

    return formatUser(userCredential.user);
  } catch (error: any) {
    console.error('Registration error:', error);
    let errorMessage = 'Could not create account';

    // Handle specific Firebase auth errors
    if (error.code === 'auth/email-already-in-use') {
      errorMessage = 'Email already in use';
    } else if (error.code === 'auth/weak-password') {
      errorMessage = 'Password is too weak';
    } else if (error.code === 'auth/invalid-email') {
      errorMessage = 'Invalid email format';
    }

    throw new Error(errorMessage);
  }
}

/**
 * Get ID token for the current user
 * @returns Promise that resolves to the ID token
 */
export async function getIdToken(): Promise<string | null> {
  if (!auth) {
    throw new Error('Firebase Auth not initialized');
  }

  const currentUser = auth.currentUser;
  if (!currentUser) {
    return null;
  }

  try {
    return await currentUser.getIdToken();
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
}
