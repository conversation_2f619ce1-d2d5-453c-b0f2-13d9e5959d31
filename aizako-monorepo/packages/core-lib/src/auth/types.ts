import { z } from 'zod';

// User type
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  displayName: z.string().nullable(),
  photoURL: z.string().nullable(),
  tenantId: z.string(),
  role: z.enum(['admin', 'user', 'guest']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

// Auth state type
export const AuthStateSchema = z.object({
  user: UserSchema.nullable(),
  loading: z.boolean(),
  error: z.string().nullable(),
});

export type AuthState = z.infer<typeof AuthStateSchema>;

// Firebase config type
export const FirebaseConfigSchema = z.object({
  apiKey: z.string(),
  projectId: z.string(),
  appId: z.string(),
  measurementId: z.string().optional(),
});

export type FirebaseConfig = z.infer<typeof FirebaseConfigSchema>;

// Auth context type
export interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  loginWithEmailPassword: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  register: (email: string, password: string, displayName?: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Type guard for User
export function isUser(value: unknown): value is User {
  return UserSchema.safeParse(value).success;
}

// Type guard for FirebaseConfig
export function isFirebaseConfig(value: unknown): value is FirebaseConfig {
  return FirebaseConfigSchema.safeParse(value).success;
}
