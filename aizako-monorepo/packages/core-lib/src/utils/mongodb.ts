import mongoose from 'mongoose';

// Connection options
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  autoIndex: true, // Build indexes
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  family: 4 // Use IPv4, skip trying IPv6
};

// Global is used here to maintain a cached connection across hot reloads
// in development. This prevents connections growing exponentially
// during API Route usage.
let cached: { conn: typeof mongoose | null; promise: Promise<typeof mongoose> | null } =
  { conn: null, promise: null };

/**
 * Connect to MongoDB
 * @param uri MongoDB connection URI
 * @returns Promise that resolves when connection is established
 */
export async function connectToMongoDB(uri: string): Promise<typeof mongoose> {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = options as mongoose.ConnectOptions;

    mongoose.set('strictQuery', false);

    cached.promise = mongoose.connect(uri, opts).then((mongoose) => {
      console.log('MongoDB connected successfully');
      return mongoose;
    });
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

/**
 * Disconnect from MongoDB
 * @returns Promise that resolves when disconnection is complete
 */
export async function disconnectFromMongoDB(): Promise<void> {
  if (cached.conn) {
    await mongoose.disconnect();
    cached.conn = null;
    cached.promise = null;
    console.log('MongoDB disconnected');
  }
}

/**
 * Test MongoDB connection
 * @param uri MongoDB connection URI
 * @returns Promise that resolves to true if connection is successful, false otherwise
 */
export async function testMongoDBConnection(uri: string): Promise<boolean> {
  try {
    await connectToMongoDB(uri);
    return true;
  } catch (error) {
    console.error('MongoDB connection test failed:', error);
    return false;
  }
}

/**
 * Add tenant filter to MongoDB query
 * @param query MongoDB query
 * @param tenantId Tenant ID
 * @returns MongoDB query with tenant filter
 */
export function addTenantFilter<T>(
  query: mongoose.Query<T[], T>,
  tenantId: string
): mongoose.Query<T[], T> {
  return query.where('tenantId').equals(tenantId);
}

/**
 * Checks if a string is a valid MongoDB ObjectId
 * @param id The string to check
 * @returns True if the string is a valid ObjectId, false otherwise
 */
export function isValidObjectId(id: string | mongoose.Types.ObjectId): boolean {
  try {
    if (id instanceof mongoose.Types.ObjectId) return true;
    return mongoose.Types.ObjectId.isValid(id);
  } catch (error) {
    return false;
  }
}

/**
 * Converts a string ID to a MongoDB ObjectId
 * @param id The string ID to convert
 * @returns The MongoDB ObjectId
 * @throws Error if the ID is not a valid ObjectId
 */
export function toObjectId(id: string | mongoose.Types.ObjectId): mongoose.Types.ObjectId {
  if (id instanceof mongoose.Types.ObjectId) return id;

  if (!isValidObjectId(id)) {
    throw new Error(`Invalid ObjectId: ${id}`);
  }

  return new mongoose.Types.ObjectId(id);
}

/**
 * Converts a MongoDB ObjectId to a string
 * @param id The ObjectId to convert
 * @returns The string representation of the ObjectId
 */
export function fromObjectId(id: mongoose.Types.ObjectId | string | unknown): string {
  if (id instanceof mongoose.Types.ObjectId) {
    return id.toString();
  }

  if (typeof id === 'string') {
    return id;
  }

  if (id && typeof id === 'object' && '_id' in id && id._id instanceof mongoose.Types.ObjectId) {
    return id._id.toString();
  }

  if (id && typeof id === 'object' && 'toString' in id && typeof id.toString === 'function') {
    return id.toString();
  }

  throw new Error(`Cannot convert to string: ${id}`);
}

/**
 * Converts a filter object's _id field from string to ObjectId
 * @param filter The filter object
 * @returns The filter with _id converted to ObjectId
 */
export function convertFilterId<T extends Record<string, any>>(filter: T): T {
  if (!filter) return filter;

  const result = { ...filter };

  if ('_id' in result && typeof result._id === 'string') {
    result._id = toObjectId(result._id);
  }

  return result;
}

/**
 * Converts an array of string IDs to MongoDB ObjectIds
 * @param ids Array of string IDs
 * @returns Array of MongoDB ObjectIds
 */
export function toObjectIds(ids: string[]): mongoose.Types.ObjectId[] {
  return ids.map(id => toObjectId(id));
}

/**
 * Converts an array of MongoDB ObjectIds to strings
 * @param ids Array of MongoDB ObjectIds
 * @returns Array of strings
 */
export function fromObjectIds(ids: mongoose.Types.ObjectId[]): string[] {
  return ids.map(id => fromObjectId(id));
}
