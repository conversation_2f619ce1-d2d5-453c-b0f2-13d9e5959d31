import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

/**
 * Validate data against a Zod schema
 * @param schema Zod schema to validate against
 * @param data Data to validate
 * @returns Validated data
 * @throws Error if validation fails
 */
export function validateData<T>(schema: z.ZodType<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError = fromZodError(error);
      throw new Error(validationError.message);
    }
    throw error;
  }
}

/**
 * Safely validate data against a Zod schema
 * @param schema Zod schema to validate against
 * @param data Data to validate
 * @returns Object with success flag and validated data or error
 */
export function safeValidateData<T>(
  schema: z.ZodType<T>,
  data: unknown
): { success: true; data: T } | { success: false; error: string } {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  const validationError = fromZodError(result.error);
  return { success: false, error: validationError.message };
}
