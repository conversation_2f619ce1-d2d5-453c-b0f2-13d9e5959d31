import { BaseEntity, TenantScopedEntity } from '../core';

/**
 * Type guard for BaseEntity
 */
export function isBaseEntity(value: unknown): value is BaseEntity {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const entity = value as unknown as Record<string, unknown>;

  return (
    typeof entity.id === 'string' &&
    entity.createdAt instanceof Date &&
    entity.updatedAt instanceof Date
  );
}

/**
 * Type guard for TenantScopedEntity
 */
export function isTenantScopedEntity(value: unknown): value is TenantScopedEntity {
  if (!isBaseEntity(value)) {
    return false;
  }

  const entity = value as unknown as Record<string, unknown>;

  return typeof entity.tenantId === 'string';
}
