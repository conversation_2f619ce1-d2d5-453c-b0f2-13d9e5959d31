// Core types for the application

/**
 * Base entity interface
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Tenant-scoped entity interface
 */
export interface TenantScopedEntity extends BaseEntity {
  tenantId: string;
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Sorting parameters
 */
export interface SortingParams {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Query parameters
 */
export interface QueryParams {
  pagination?: PaginationParams;
  sorting?: SortingParams;
  filters?: Record<string, any>;
}

/**
 * Pagination result
 */
export interface PaginationResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
