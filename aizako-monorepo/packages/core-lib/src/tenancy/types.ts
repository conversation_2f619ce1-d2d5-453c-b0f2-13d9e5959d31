import { z } from 'zod';
import { ReactNode } from 'react';

// Tenant type
export const TenantSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string(),
  plan: z.string(),
  features: z.array(z.string()),
  limits: z.record(z.string(), z.number()),
  domains: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive', 'suspended', 'trial']).default('active'),
  trialEndsAt: z.date().optional(),
  settings: z.record(z.string(), z.any()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Tenant = z.infer<typeof TenantSchema>;

// Tenant domain type
export const TenantDomainSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  domain: z.string(),
  verified: z.boolean().default(false),
  primary: z.boolean().default(false),
  verificationToken: z.string().optional(),
  verificationMethod: z.enum(['dns', 'file', 'email']).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type TenantDomain = z.infer<typeof TenantDomainSchema>;

// Tenant user type
export const TenantUserSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  userId: z.string(),
  role: z.enum(['owner', 'admin', 'member', 'guest']).default('member'),
  permissions: z.array(z.string()).optional(),
  invitedBy: z.string().optional(),
  invitedAt: z.date().optional(),
  joinedAt: z.date().optional(),
  status: z.enum(['active', 'invited', 'inactive']).default('active'),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type TenantUser = z.infer<typeof TenantUserSchema>;

// Tenant context type
export const TenantContextSchema = z.object({
  tenant: TenantSchema.nullable(),
  loading: z.boolean(),
  error: z.string().nullable(),
});

export type TenantContext = z.infer<typeof TenantContextSchema>;

// License claim type
export const LicenseClaimSchema = z.object({
  tenantId: z.string(),
  plan: z.string(),
  features: z.array(z.string()),
  limits: z.record(z.string(), z.number()),
  exp: z.number(),
});

export type LicenseClaim = z.infer<typeof LicenseClaimSchema>;

// Tenant context provider props
export interface TenantContextProviderProps {
  children: ReactNode;
  initialTenantId?: string;
}

// Tenant context type
export interface TenantContextType {
  tenant: Tenant | null;
  isLoading: boolean;
  error: string | null;
  setCurrentTenant: (tenantId: string) => Promise<void>;
  createTenant: (tenantData: Partial<Tenant>) => Promise<Tenant>;
  updateTenant: (tenantId: string, tenantData: Partial<Tenant>) => Promise<Tenant>;
  deleteTenant: (tenantId: string) => Promise<boolean>;
  getUserTenants: () => Promise<Tenant[]>;
}

// Type guard for Tenant
export function isTenant(value: unknown): value is Tenant {
  return TenantSchema.safeParse(value).success;
}

// Type guard for TenantDomain
export function isTenantDomain(value: unknown): value is TenantDomain {
  return TenantDomainSchema.safeParse(value).success;
}

// Type guard for TenantUser
export function isTenantUser(value: unknown): value is TenantUser {
  return TenantUserSchema.safeParse(value).success;
}

// Type guard for LicenseClaim
export function isLicenseClaim(value: unknown): value is LicenseClaim {
  return LicenseClaimSchema.safeParse(value).success;
}
