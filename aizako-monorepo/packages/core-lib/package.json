{"name": "@aizako/core-lib", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"firebase": "^11.6.0", "firebase-admin": "^13.2.0", "mongodb": "^6.5.0", "mongoose": "^8.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@types/node": "^20.16.11", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "eslint": "^8.57.0", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.3.1"}}