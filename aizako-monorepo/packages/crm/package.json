{"name": "@aizako/crm", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "seed": "tsx scripts/seed-mongodb.ts", "migrate": "tsx scripts/run-migrations.ts", "migrate:create": "tsx scripts/create-migration.ts"}, "dependencies": {"@aizako/core-lib": "workspace:*", "@aizako/ui-kit": "workspace:*", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@mui/x-date-pickers": "^6.19.8", "@mui/x-date-pickers-pro": "^6.19.8", "axios": "^1.6.7", "date-fns": "^3.6.0", "docx": "^8.5.0", "dompurify": "^3.0.11", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "express": "^4.18.3", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "io.mongock:mongock-standalone": "^5.3.4", "io.mongock:mongodb-springdata-v4-driver": "^5.3.4", "mongodb": "^6.5.0", "mongoose": "^8.3.1", "pdf-lib": "^1.17.1", "pino": "^8.19.0", "pino-pretty": "^10.3.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "recharts": "^2.12.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "zod": "^3.23.8", "zod-to-openapi": "^0.15.1", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@types/dompurify": "^3.0.5", "@types/draft-js": "^0.11.18", "@types/jest": "^29.5.12", "@types/node": "^20.16.11", "@types/pino": "^7.0.5", "@types/react": "^18.3.11", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "axios-mock-adapter": "^1.22.0", "eslint": "^8.57.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mongodb-memory-server": "^9.1.7", "openapi-types": "^12.1.3", "rate-limiter-flexible": "^5.0.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}}