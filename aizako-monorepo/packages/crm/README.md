# Aizako CRM

The Aizako CRM (`@aizako/crm`) is a Customer Relationship Management module for the Aizako platform. It provides functionality for managing contacts, companies, opportunities, and activities.

## Installation

```bash
pnpm add @aizako/crm
```

## Features

### MongoDB Integration

The CRM module provides MongoDB integration with connection utilities:

```typescript
import { connectToMongoDB, disconnectFromMongoDB } from '@aizako/crm';

// Connect to MongoDB
await connectToMongoDB();

// Disconnect from MongoDB
await disconnectFromMongoDB();
```

### Models

The CRM module provides MongoDB models for contacts, companies, opportunities, activities, and more:

```typescript
import {
  Contact,
  Company,
  Opportunity,
  Activity,
  Task,
  Notification,
  Proposal,
  EmailTracking
} from '@aizako/crm';

// Create a new contact
const contact = new Contact({
  tenantId: 'tenant-id',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  status: 'lead',
});
await contact.save();

// Find contacts
const contacts = await Contact.find({ tenantId: 'tenant-id' });
```

### Services

The CRM module provides services for business logic and data access:

```typescript
import {
  ContactService,
  CompanyService,
  OpportunityService,
  ActivityService,
  NotificationService,
  ProposalService,
  EmailTrackingService,
  AiService,
  DocumentGenerationService
} from '@aizako/crm';

// Create a new contact
const contact = await ContactService.createContact(
  {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'lead',
  },
  'tenant-id'
);

// Get contacts with pagination
const { contacts, total } = await ContactService.getContacts(
  {
    page: 1,
    limit: 10,
    sortBy: 'lastName',
    sortOrder: 'asc',
    status: 'lead',
  },
  'tenant-id'
);

// Create a notification
const notification = await NotificationService.getInstance().createNotification(
  'tenant-id',
  'user-id',
  'New Contact',
  'A new contact has been created',
  'info',
  'contact',
  contact._id
);
```

### API

The CRM module provides API routes, controllers, and client utilities:

```typescript
import { CRM_API_ROUTES, ApiClientFactory, MongoApiClient } from '@aizako/crm';

// API routes
console.log(CRM_API_ROUTES.CONTACTS); // /api/crm/contacts
console.log(CRM_API_ROUTES.COMPANIES); // /api/crm/companies
console.log(CRM_API_ROUTES.OPPORTUNITIES); // /api/crm/opportunities
console.log(CRM_API_ROUTES.ACTIVITIES); // /api/crm/activities
console.log(CRM_API_ROUTES.TASKS); // /api/crm/tasks
console.log(CRM_API_ROUTES.PROPOSALS); // /api/crm/proposals
console.log(CRM_API_ROUTES.EMAIL_TRACKING); // /api/crm/email-tracking

// Get API client based on database type
const apiClient = ApiClientFactory.getApiClient();

// Use MongoDB API client directly
const mongoClient = new MongoApiClient();
const contacts = await mongoClient.getContacts({ page: 1, limit: 10 });
```

### Components

The CRM module provides CRM-specific UI components:

```typescript
import {
  ContactList,
  ContactDetail,
  CompanyList,
  CompanyDetail,
  OpportunityList,
  OpportunityDetail,
  ActivityList,
  ActivityDetail
} from '@aizako/crm';

function CRMDashboard() {
  return (
    <div>
      <h1>CRM Dashboard</h1>
      <ContactList />
    </div>
  );
}
```

## Development

### Project Structure

```
/packages/crm
  ├─ src
  │   ├─ components   (CRM-specific UI components)
  │   ├─ hooks        (CRM-specific hooks)
  │   ├─ models       (MongoDB models)
  │   ├─ services     (Business logic and data access)
  │   ├─ types        (CRM-specific types)
  │   ├─ utils        (CRM-specific utilities)
  │   ├─ api          (API routes and controllers)
  │   └─ index.ts     (Entry point)
  ├─ package.json
  ├─ tsconfig.json
  └─ tsup.config.ts
```

### Models

Models are defined in the `src/models` directory. Each model is a Mongoose schema and model:

```typescript
import mongoose, { Schema, Document } from 'mongoose';
import { TenantScopedEntity } from '@aizako/core-lib';

export interface IContact extends Document, TenantScopedEntity {
  firstName: string;
  lastName: string;
  email: string;
  // ...
}

const ContactSchema = new Schema<IContact>(
  {
    tenantId: { type: String, required: true, index: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: true },
    // ...
  },
  {
    timestamps: true,
  }
);

export const Contact = mongoose.model<IContact>('Contact', ContactSchema);
```

### Services

Services are defined in the `src/services` directory. Each service provides methods for interacting with a model:

```typescript
import { Contact, IContact } from '../models/contact';

export class ContactService {
  static async createContact(contactData: Partial<IContact>, tenantId: string): Promise<IContact> {
    const contact = new Contact({
      ...contactData,
      tenantId,
    });

    return await contact.save();
  }

  // ...
}
```

### API

API routes are defined in the `src/api` directory:

```typescript
export const CRM_API_ROUTES = {
  CONTACTS: '/api/crm/contacts',
  COMPANIES: '/api/crm/companies',
  OPPORTUNITIES: '/api/crm/opportunities',
  ACTIVITIES: '/api/crm/activities',
};
```

### Building

```bash
pnpm build
```

This will build the module using tsup and output the result to the `dist` directory.

### Testing

```bash
pnpm test
```

This will run the tests using Jest.

### Seeding Data

To seed the MongoDB database with sample data:

```bash
pnpm seed
```

This will create sample contacts, companies, opportunities, tasks, and activities in the MongoDB database.

## License

Copyright (c) 2024 Aizako. All rights reserved.
