#!/usr/bin/env tsx
/**
 * Run MongoDB migrations using Mongock
 * 
 * This script runs all pending migrations using Mongock.
 * It connects to MongoDB and applies migrations in order.
 * 
 * Usage:
 *   npm run migrate
 */

import { MongoClient } from 'mongodb';
import { mongockConfig } from '../src/config/mongock';
import { connectToMongoDB } from '../src/utils/mongodb';
import path from 'path';
import fs from 'fs';

// Import Mongock classes
const MongockStandalone = require('io.mongock:mongock-standalone');
const MongoDBDriver = require('io.mongock:mongodb-springdata-v4-driver');

/**
 * Run migrations
 */
async function runMigrations() {
  try {
    console.log('Starting MongoDB migrations...');
    
    // Connect to MongoDB
    await connectToMongoDB();
    console.log('Connected to MongoDB');
    
    // Create MongoDB client
    const client = new MongoClient(mongockConfig.mongoUri);
    await client.connect();
    console.log('MongoDB client connected');
    
    // Get database name from connection string
    const dbName = mongockConfig.mongoUri.split('/').pop()?.split('?')[0] || 'aizako-crm';
    const database = client.db(dbName);
    
    // Create Mongock driver
    const driver = MongoDBDriver.withDefaultLock(database);
    
    // Get migration files
    const migrationsDir = path.resolve(__dirname, '../src/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.ts') && !file.endsWith('.d.ts'))
      .map(file => path.join(migrationsDir, file));
    
    console.log(`Found ${migrationFiles.length} migration files`);
    
    // Create Mongock runner
    const runner = MongockStandalone.builder()
      .setDriver(driver)
      .addMigrationScanPackage(mongockConfig.migrationPackage)
      .setMigrationRepositoryName(mongockConfig.migrationCollectionName)
      .setLockRepositoryName(mongockConfig.lockCollectionName)
      .setLockAcquiredForMinutes(mongockConfig.lockAcquiredForMinutes)
      .setLockQuitTryingAfterMinutes(mongockConfig.maxWaitingForLockMinutes)
      .setLegacyMigration(false)
      .setTransactionEnabled(mongockConfig.transactionEnabled)
      .buildRunner();
    
    // Execute migrations
    console.log('Executing migrations...');
    await runner.execute();
    console.log('Migrations completed successfully');
    
    // Close MongoDB connection
    await client.close();
    console.log('MongoDB client closed');
    
    process.exit(0);
  } catch (error) {
    console.error('Error running migrations:', error);
    process.exit(1);
  }
}

// Run migrations
runMigrations();
