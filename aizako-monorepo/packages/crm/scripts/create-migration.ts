#!/usr/bin/env tsx
/**
 * Create a new MongoDB migration file
 * 
 * This script creates a new migration file with the current timestamp
 * and a descriptive name provided by the user.
 * 
 * Usage:
 *   npm run migrate:create -- "description-of-migration"
 * 
 * Example:
 *   npm run migrate:create -- "add-indexes-to-contacts"
 */

import fs from 'fs';
import path from 'path';
import readline from 'readline';

// Get migration description from command line arguments
const args = process.argv.slice(2);
let migrationName = args[0];

// Create readline interface for user input if no name provided
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Create migration file
 * @param name Migration name
 */
function createMigrationFile(name: string) {
  // Format name to kebab case
  const formattedName = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
  
  // Create timestamp
  const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
  
  // Create file name
  const fileName = `${timestamp}_${formattedName}.ts`;
  
  // Create migrations directory if it doesn't exist
  const migrationsDir = path.resolve(__dirname, '../src/migrations');
  if (!fs.existsSync(migrationsDir)) {
    fs.mkdirSync(migrationsDir, { recursive: true });
  }
  
  // Create file path
  const filePath = path.join(migrationsDir, fileName);
  
  // Create class name from migration name
  const className = formattedName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
  
  // Create file content
  const content = `/**
 * Migration: ${name}
 * Created: ${new Date().toISOString()}
 */
import { Db } from 'mongodb';

/**
 * Migration class for ${name}
 */
export class ${className}Migration {
  /**
   * Order of migration execution
   * Higher values run later
   */
  public static readonly order = 1;

  /**
   * Unique migration ID
   */
  public static readonly id = '${timestamp}-${formattedName}';

  /**
   * Author of the migration
   */
  public static readonly author = 'aizako-system';

  /**
   * Execute the migration
   * @param db MongoDB database instance
   */
  public async execute(db: Db): Promise<void> {
    // TODO: Implement migration logic
    console.log('Executing migration: ${name}');
    
    // Example: Create a new collection
    // await db.createCollection('new_collection');
    
    // Example: Add an index
    // await db.collection('collection_name').createIndex({ field: 1 });
    
    // Example: Update documents
    // await db.collection('collection_name').updateMany(
    //   { field: { $exists: false } },
    //   { $set: { field: 'default_value' } }
    // );
  }

  /**
   * Rollback the migration
   * @param db MongoDB database instance
   */
  public async rollback(db: Db): Promise<void> {
    // TODO: Implement rollback logic
    console.log('Rolling back migration: ${name}');
    
    // Example: Drop a collection
    // await db.dropCollection('new_collection');
    
    // Example: Drop an index
    // await db.collection('collection_name').dropIndex('field_1');
    
    // Example: Revert document updates
    // await db.collection('collection_name').updateMany(
    //   { field: 'default_value' },
    //   { $unset: { field: '' } }
    // );
  }
}

export default ${className}Migration;
`;
  
  // Write file
  fs.writeFileSync(filePath, content);
  
  console.log(`Migration file created: ${filePath}`);
}

// If migration name is provided, create file
if (migrationName) {
  createMigrationFile(migrationName);
  process.exit(0);
} else {
  // Otherwise, prompt for migration name
  rl.question('Enter migration name (e.g., "add-indexes-to-contacts"): ', (answer) => {
    if (!answer) {
      console.error('Migration name is required');
      process.exit(1);
    }
    
    createMigrationFile(answer);
    rl.close();
  });
}
