import mongoose from 'mongoose';
import { connectToMongoDB, disconnectFromMongoDB } from '../src/utils/mongodb';
import { MONGODB_URI } from '../src/config';
import { v4 as uuidv4 } from 'uuid';

// Import models
import * as Models from '../src/models';

// Sample tenant IDs
const TENANT_IDS = ['tenant-1', 'tenant-2'];

// Sample user IDs (would be Firebase Auth UIDs in a real app)
const USER_IDS = ['user-1', 'user-2', 'user-3'];

// Sample data
const sampleCompanies = [
  {
    name: 'Acme Corporation',
    domain: 'acme.com',
    industry: 'Manufacturing',
    size: '501-1000',
    location: 'New York, NY',
    description: 'A global manufacturing company',
    website: 'https://acme.com',
    status: 'active',
  },
  {
    name: 'TechStart Inc',
    domain: 'techstart.io',
    industry: 'Technology',
    size: '11-50',
    location: 'San Francisco, CA',
    description: 'An innovative tech startup',
    website: 'https://techstart.io',
    status: 'active',
  },
  {
    name: 'Global Services Ltd',
    domain: 'globalservices.com',
    industry: 'Consulting',
    size: '201-500',
    location: 'London, UK',
    description: 'A professional services firm',
    website: 'https://globalservices.com',
    status: 'active',
  }
];

const sampleContacts = [
  {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    title: 'CEO',
    status: 'active',
  },
  {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+****************',
    title: 'CTO',
    status: 'active',
  },
  {
    firstName: 'Michael',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+44 20 1234 5678',
    title: 'Sales Director',
    status: 'active',
  },
  {
    firstName: 'Sarah',
    lastName: 'Williams',
    email: '<EMAIL>',
    phone: '+****************',
    title: 'Marketing Manager',
    status: 'active',
  },
  {
    firstName: 'Robert',
    lastName: 'Brown',
    email: '<EMAIL>',
    phone: '+****************',
    title: 'Product Manager',
    status: 'lead',
  }
];

const sampleOpportunities = [
  {
    name: 'Enterprise Software Deal',
    value: 50000,
    currency: 'USD',
    stage: 'proposal',
    probability: 70,
    expectedCloseDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    source: 'referral',
    description: 'Enterprise software implementation for Acme Corp',
  },
  {
    name: 'Consulting Project',
    value: 25000,
    currency: 'USD',
    stage: 'qualification',
    probability: 40,
    expectedCloseDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
    source: 'website',
    description: 'Strategic consulting project for TechStart',
  },
  {
    name: 'Annual Support Contract',
    value: 75000,
    currency: 'USD',
    stage: 'negotiation',
    probability: 85,
    expectedCloseDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
    source: 'existing customer',
    description: 'Annual support and maintenance contract renewal',
  }
];

const sampleTasks = [
  {
    title: 'Follow up with John Doe',
    description: 'Send proposal and schedule a meeting',
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    priority: 'high',
    status: 'pending',
  },
  {
    title: 'Prepare presentation for TechStart',
    description: 'Create slides for the product demo',
    dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
    priority: 'medium',
    status: 'pending',
  },
  {
    title: 'Review contract with legal',
    description: 'Send the contract draft to legal department for review',
    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
    priority: 'high',
    status: 'pending',
  }
];

/**
 * Seed the MongoDB database with sample data
 */
async function seedMongoDB() {
  try {
    // Connect to MongoDB
    await connectToMongoDB(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data
    await clearCollections();
    console.log('Cleared existing data');

    // Create companies
    const companies = await createCompanies();
    console.log(`Created ${companies.length} companies`);

    // Create contacts
    const contacts = await createContacts(companies);
    console.log(`Created ${contacts.length} contacts`);

    // Create opportunities
    const opportunities = await createOpportunities(companies, contacts);
    console.log(`Created ${opportunities.length} opportunities`);

    // Create tasks
    const tasks = await createTasks(contacts, opportunities);
    console.log(`Created ${tasks.length} tasks`);

    // Create activities
    const activities = await createActivities(contacts, companies, opportunities);
    console.log(`Created ${activities.length} activities`);

    console.log('Database seeded successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Disconnect from MongoDB
    await disconnectFromMongoDB();
    console.log('Disconnected from MongoDB');
  }
}

/**
 * Clear all collections
 */
async function clearCollections() {
  await Models.Company.deleteMany({});
  await Models.Contact.deleteMany({});
  await Models.Opportunity.deleteMany({});
  await Models.Task.deleteMany({});
  await Models.Activity.deleteMany({});
}

/**
 * Create sample companies
 */
async function createCompanies() {
  const companies = [];

  for (const tenantId of TENANT_IDS) {
    for (const company of sampleCompanies) {
      const newCompany = new Models.Company({
        ...company,
        tenantId,
        owner: USER_IDS[Math.floor(Math.random() * USER_IDS.length)],
        tags: ['sample', 'seed-data'],
        customFields: {
          industry_segment: company.industry === 'Technology' ? 'Software' : 'General',
          employee_count: Math.floor(Math.random() * 1000) + 10,
        },
      });

      companies.push(await newCompany.save());
    }
  }

  return companies;
}

/**
 * Create sample contacts
 */
async function createContacts(companies: any[]) {
  const contacts = [];

  for (const tenantId of TENANT_IDS) {
    const tenantCompanies = companies.filter(c => c.tenantId === tenantId);

    for (const contact of sampleContacts) {
      // Assign contact to a company
      const companyIndex = Math.floor(Math.random() * tenantCompanies.length);
      const company = tenantCompanies[companyIndex];

      const newContact = new Models.Contact({
        ...contact,
        tenantId,
        companyId: company._id,
        owner: USER_IDS[Math.floor(Math.random() * USER_IDS.length)],
        tags: ['sample', 'seed-data'],
        customFields: {
          lead_source: ['website', 'referral', 'event'][Math.floor(Math.random() * 3)],
          last_meeting: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
        },
      });

      contacts.push(await newContact.save());
    }
  }

  return contacts;
}

/**
 * Create sample opportunities
 */
async function createOpportunities(companies: any[], contacts: any[]) {
  const opportunities = [];

  for (const tenantId of TENANT_IDS) {
    const tenantCompanies = companies.filter(c => c.tenantId === tenantId);
    const tenantContacts = contacts.filter(c => c.tenantId === tenantId);

    for (const opportunity of sampleOpportunities) {
      // Assign opportunity to a company and contact
      const companyIndex = Math.floor(Math.random() * tenantCompanies.length);
      const company = tenantCompanies[companyIndex];

      const contactIndex = Math.floor(Math.random() * tenantContacts.length);
      const contact = tenantContacts[contactIndex];

      const newOpportunity = new Models.Opportunity({
        ...opportunity,
        tenantId,
        companyId: company._id,
        contactId: contact._id,
        owner: USER_IDS[Math.floor(Math.random() * USER_IDS.length)],
        tags: ['sample', 'seed-data'],
        customFields: {
          deal_type: ['new', 'renewal', 'upsell'][Math.floor(Math.random() * 3)],
          competitor: ['None', 'Competitor A', 'Competitor B'][Math.floor(Math.random() * 3)],
        },
      });

      opportunities.push(await newOpportunity.save());
    }
  }

  return opportunities;
}

/**
 * Create sample tasks
 */
async function createTasks(contacts: any[], opportunities: any[]) {
  const tasks = [];

  for (const tenantId of TENANT_IDS) {
    const tenantContacts = contacts.filter(c => c.tenantId === tenantId);
    const tenantOpportunities = opportunities.filter(o => o.tenantId === tenantId);

    for (const task of sampleTasks) {
      // Randomly assign task to a contact or opportunity
      const assignToContact = Math.random() > 0.5;
      
      let contactId = null;
      let opportunityId = null;
      
      if (assignToContact && tenantContacts.length > 0) {
        const contactIndex = Math.floor(Math.random() * tenantContacts.length);
        contactId = tenantContacts[contactIndex]._id;
      } else if (tenantOpportunities.length > 0) {
        const opportunityIndex = Math.floor(Math.random() * tenantOpportunities.length);
        opportunityId = tenantOpportunities[opportunityIndex]._id;
      }

      const newTask = new Models.Task({
        ...task,
        tenantId,
        contactId,
        opportunityId,
        owner: USER_IDS[Math.floor(Math.random() * USER_IDS.length)],
        assignedTo: USER_IDS[Math.floor(Math.random() * USER_IDS.length)],
        tags: ['sample', 'seed-data'],
        customFields: {
          estimated_time: Math.floor(Math.random() * 120) + 15, // 15-135 minutes
          category: ['sales', 'follow-up', 'admin'][Math.floor(Math.random() * 3)],
        },
      });

      tasks.push(await newTask.save());
    }
  }

  return tasks;
}

/**
 * Create sample activities
 */
async function createActivities(contacts: any[], companies: any[], opportunities: any[]) {
  const activities = [];
  const activityTypes = ['call', 'meeting', 'email', 'note'];

  for (const tenantId of TENANT_IDS) {
    const tenantContacts = contacts.filter(c => c.tenantId === tenantId);
    const tenantCompanies = companies.filter(c => c.tenantId === tenantId);
    const tenantOpportunities = opportunities.filter(o => o.tenantId === tenantId);

    // Create 10 random activities per tenant
    for (let i = 0; i < 10; i++) {
      const type = activityTypes[Math.floor(Math.random() * activityTypes.length)];
      const date = new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);
      
      // Randomly assign activity to a contact, company, or opportunity
      const assignmentType = Math.floor(Math.random() * 3);
      
      let contactId = null;
      let companyId = null;
      let opportunityId = null;
      
      if (assignmentType === 0 && tenantContacts.length > 0) {
        const contactIndex = Math.floor(Math.random() * tenantContacts.length);
        contactId = tenantContacts[contactIndex]._id;
      } else if (assignmentType === 1 && tenantCompanies.length > 0) {
        const companyIndex = Math.floor(Math.random() * tenantCompanies.length);
        companyId = tenantCompanies[companyIndex]._id;
      } else if (tenantOpportunities.length > 0) {
        const opportunityIndex = Math.floor(Math.random() * tenantOpportunities.length);
        opportunityId = tenantOpportunities[opportunityIndex]._id;
      }

      const newActivity = new Models.Activity({
        type,
        title: `${type.charAt(0).toUpperCase() + type.slice(1)} with ${contactId ? 'contact' : companyId ? 'company' : 'opportunity'}`,
        description: `Sample ${type} activity created by seed script`,
        date,
        duration: type === 'meeting' ? 30 : type === 'call' ? 15 : null,
        completed: Math.random() > 0.3, // 70% chance of being completed
        tenantId,
        contactId,
        companyId,
        opportunityId,
        owner: USER_IDS[Math.floor(Math.random() * USER_IDS.length)],
        notes: `Notes for this ${type} activity`,
        outcome: Math.random() > 0.5 ? `Outcome of this ${type}` : null,
        customFields: {
          follow_up_required: Math.random() > 0.7,
          sentiment: ['positive', 'neutral', 'negative'][Math.floor(Math.random() * 3)],
        },
      });

      activities.push(await newActivity.save());
    }
  }

  return activities;
}

// Run the seed function
seedMongoDB();
