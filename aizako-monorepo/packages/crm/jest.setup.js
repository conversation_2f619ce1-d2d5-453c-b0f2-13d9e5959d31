// Mock console.error to avoid cluttering test output
const originalConsoleError = console.error;
console.error = (...args) => {
  // Filter out expected errors
  if (
    args[0]?.includes?.('Warning:') ||
    args[0]?.includes?.('Error:') ||
    args[0]?.includes?.('Invalid hook call')
  ) {
    return;
  }
  originalConsoleError(...args);
};

// Mock console.warn to avoid cluttering test output
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  // Filter out expected warnings
  if (
    args[0]?.includes?.('Warning:') ||
    args[0]?.includes?.('React does not recognize')
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Set up global variables
global.fetch = jest.fn();
global.Request = jest.fn();
global.Headers = jest.fn();

// Mock localStorage
const localStorageMock = (function() {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = (function() {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
