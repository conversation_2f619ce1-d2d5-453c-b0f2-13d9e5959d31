import axios from 'axios';
import { API_BASE_URL } from '../config';
import { getAuthToken } from '../utils/auth';

/**
 * Calendar Service
 * 
 * This service provides methods for managing calendar events.
 */
export class CalendarService {
  /**
   * Get events by proposal
   * @param proposalId Proposal ID
   * @param tenantId Tenant ID
   * @returns Events
   */
  static async getEventsByProposal(proposalId: string, tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/calendar/events/proposal/${proposalId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting events by proposal:', error);
      throw error;
    }
  }
  
  /**
   * Get event by ID
   * @param eventId Event ID
   * @param tenantId Tenant ID
   * @returns Event
   */
  static async getEventById(eventId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/calendar/events/${eventId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting event:', error);
      throw error;
    }
  }
  
  /**
   * Create event
   * @param eventData Event data
   * @param tenantId Tenant ID
   * @returns Created event
   */
  static async createEvent(eventData: any, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(`${API_BASE_URL}/api/calendar/events`, eventData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error creating event:', error);
      throw error;
    }
  }
  
  /**
   * Update event
   * @param eventId Event ID
   * @param eventData Event data
   * @param tenantId Tenant ID
   * @returns Updated event
   */
  static async updateEvent(eventId: string, eventData: any, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.put(`${API_BASE_URL}/api/calendar/events/${eventId}`, eventData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error updating event:', error);
      throw error;
    }
  }
  
  /**
   * Delete event
   * @param eventId Event ID
   * @param tenantId Tenant ID
   * @returns Success status
   */
  static async deleteEvent(eventId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.delete(`${API_BASE_URL}/api/calendar/events/${eventId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error deleting event:', error);
      throw error;
    }
  }
  
  /**
   * Get team members
   * @param tenantId Tenant ID
   * @returns Team members
   */
  static async getTeamMembers(tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/users/team-members`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting team members:', error);
      throw error;
    }
  }
  
  /**
   * Get calendar integrations
   * @param tenantId Tenant ID
   * @returns Calendar integrations
   */
  static async getCalendarIntegrations(tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/calendar/integrations`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting calendar integrations:', error);
      throw error;
    }
  }
  
  /**
   * Connect calendar integration
   * @param integrationType Integration type (google, outlook, etc.)
   * @param tenantId Tenant ID
   * @returns Authorization URL
   */
  static async connectCalendarIntegration(integrationType: string, tenantId: string): Promise<string> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(
        `${API_BASE_URL}/api/calendar/integrations/connect`,
        { type: integrationType },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Tenant-ID': tenantId,
          },
        }
      );
      
      return response.data.authUrl;
    } catch (error) {
      console.error('Error connecting calendar integration:', error);
      throw error;
    }
  }
  
  /**
   * Disconnect calendar integration
   * @param integrationId Integration ID
   * @param tenantId Tenant ID
   * @returns Success status
   */
  static async disconnectCalendarIntegration(integrationId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.delete(`${API_BASE_URL}/api/calendar/integrations/${integrationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error disconnecting calendar integration:', error);
      throw error;
    }
  }
  
  /**
   * Get available time slots
   * @param attendeeIds Attendee IDs
   * @param date Date
   * @param duration Duration in minutes
   * @param tenantId Tenant ID
   * @returns Available time slots
   */
  static async getAvailableTimeSlots(
    attendeeIds: string[],
    date: string,
    duration: number,
    tenantId: string
  ): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(
        `${API_BASE_URL}/api/calendar/available-slots`,
        {
          attendeeIds,
          date,
          duration,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Tenant-ID': tenantId,
          },
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Error getting available time slots:', error);
      throw error;
    }
  }
  
  /**
   * Create video conference
   * @param provider Provider (zoom, meet, teams)
   * @param eventData Event data
   * @param tenantId Tenant ID
   * @returns Video conference data
   */
  static async createVideoConference(
    provider: string,
    eventData: any,
    tenantId: string
  ): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(
        `${API_BASE_URL}/api/calendar/video-conference/${provider}`,
        eventData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Tenant-ID': tenantId,
          },
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Error creating video conference:', error);
      throw error;
    }
  }
  
  /**
   * Get calendar settings
   * @param tenantId Tenant ID
   * @returns Calendar settings
   */
  static async getCalendarSettings(tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/calendar/settings`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting calendar settings:', error);
      throw error;
    }
  }
  
  /**
   * Update calendar settings
   * @param settingsData Settings data
   * @param tenantId Tenant ID
   * @returns Updated settings
   */
  static async updateCalendarSettings(settingsData: any, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.put(`${API_BASE_URL}/api/calendar/settings`, settingsData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error updating calendar settings:', error);
      throw error;
    }
  }
}

export default CalendarService;
