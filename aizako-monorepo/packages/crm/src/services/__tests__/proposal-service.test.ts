import { ProposalService } from '../proposal-service';
import { Proposal } from '../../models/proposal';
import { CreateProposalSchema } from '../../types/proposals/schemas';
import { IProposal } from '../../types/proposals';
import { ObjectId } from 'mongodb';

// Mock the Proposal model
jest.mock('../../models/proposal');

describe('ProposalService', () => {
  const mockTenantId = 'tenant-123';
  const mockUserId = 'user-123';
  
  // Mock proposal data
  const mockProposalData = {
    title: 'Test Proposal',
    description: 'Test Description',
    status: 'draft',
    sections: [
      {
        id: 'section-1',
        title: 'Section 1',
        content: 'Content 1',
        order: 0,
        type: 'text',
        isVisible: true,
      },
    ],
    pricing: {
      currency: 'USD',
      items: [
        {
          id: 'item-1',
          name: 'Item 1',
          description: 'Item Description',
          quantity: 1,
          unitPrice: 100,
          total: 100,
        },
      ],
      subtotal: 100,
      total: 100,
    },
    terms: 'Terms and conditions',
    owner: new ObjectId(mockUserId),
    createdBy: new ObjectId(mockUserId),
  };
  
  // Mock proposal
  const mockProposal = {
    _id: new ObjectId('proposal-123'),
    ...mockProposalData,
    tenantId: mockTenantId,
    publicToken: 'public-token',
    publicUrl: '/proposals/public/public-token',
    publicAccessEnabled: true,
    emailEnabled: true,
    downloadEnabled: true,
    downloadFormats: ['pdf'],
    viewCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    save: jest.fn().mockResolvedValue({
      _id: new ObjectId('proposal-123'),
      ...mockProposalData,
      tenantId: mockTenantId,
      publicToken: 'public-token',
      publicUrl: '/proposals/public/public-token',
      publicAccessEnabled: true,
      emailEnabled: true,
      downloadEnabled: true,
      downloadFormats: ['pdf'],
      viewCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    }),
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('createProposal', () => {
    it('should create a new proposal', async () => {
      // Arrange
      (Proposal as jest.Mock).mockImplementation(() => mockProposal);
      
      // Validate with Zod schema
      const validatedData = CreateProposalSchema.parse(mockProposalData);
      
      // Act
      const result = await ProposalService.createProposal(validatedData, mockTenantId);
      
      // Assert
      expect(Proposal).toHaveBeenCalledWith({
        ...mockProposalData,
        tenantId: mockTenantId,
        publicToken: expect.any(String),
        publicUrl: expect.stringContaining('/proposals/public/'),
        viewCount: 0,
        status: 'draft',
      });
      expect(mockProposal.save).toHaveBeenCalled();
      expect(result).toEqual(mockProposal);
    });
    
    it('should throw an error if proposal creation fails', async () => {
      // Arrange
      const mockError = new Error('Failed to create proposal');
      (Proposal as jest.Mock).mockImplementation(() => ({
        ...mockProposal,
        save: jest.fn().mockRejectedValue(mockError),
      }));
      
      // Act & Assert
      await expect(ProposalService.createProposal(mockProposalData, mockTenantId))
        .rejects.toThrow(mockError);
    });
  });
  
  describe('getProposalById', () => {
    it('should get a proposal by ID', async () => {
      // Arrange
      (Proposal.findOne as jest.Mock).mockResolvedValue(mockProposal);
      
      // Act
      const result = await ProposalService.getProposalById('proposal-123', mockTenantId);
      
      // Assert
      expect(Proposal.findOne).toHaveBeenCalledWith({
        _id: 'proposal-123',
        tenantId: mockTenantId,
      });
      expect(result).toEqual(mockProposal);
    });
    
    it('should return null if proposal not found', async () => {
      // Arrange
      (Proposal.findOne as jest.Mock).mockResolvedValue(null);
      
      // Act
      const result = await ProposalService.getProposalById('proposal-123', mockTenantId);
      
      // Assert
      expect(Proposal.findOne).toHaveBeenCalledWith({
        _id: 'proposal-123',
        tenantId: mockTenantId,
      });
      expect(result).toBeNull();
    });
    
    it('should throw an error if getting proposal fails', async () => {
      // Arrange
      const mockError = new Error('Failed to get proposal');
      (Proposal.findOne as jest.Mock).mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(ProposalService.getProposalById('proposal-123', mockTenantId))
        .rejects.toThrow(mockError);
    });
  });
  
  describe('getProposalByToken', () => {
    it('should get a proposal by token', async () => {
      // Arrange
      (Proposal.findOne as jest.Mock).mockResolvedValue(mockProposal);
      
      // Act
      const result = await ProposalService.getProposalByToken('public-token');
      
      // Assert
      expect(Proposal.findOne).toHaveBeenCalledWith({
        publicToken: 'public-token',
        publicAccessEnabled: true,
        status: { $nin: ['draft', 'expired'] },
      });
      expect(result).toEqual(mockProposal);
    });
    
    it('should return null if proposal not found', async () => {
      // Arrange
      (Proposal.findOne as jest.Mock).mockResolvedValue(null);
      
      // Act
      const result = await ProposalService.getProposalByToken('public-token');
      
      // Assert
      expect(Proposal.findOne).toHaveBeenCalledWith({
        publicToken: 'public-token',
        publicAccessEnabled: true,
        status: { $nin: ['draft', 'expired'] },
      });
      expect(result).toBeNull();
    });
    
    it('should throw an error if getting proposal fails', async () => {
      // Arrange
      const mockError = new Error('Failed to get proposal');
      (Proposal.findOne as jest.Mock).mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(ProposalService.getProposalByToken('public-token'))
        .rejects.toThrow(mockError);
    });
  });
  
  describe('updateProposal', () => {
    it('should update a proposal', async () => {
      // Arrange
      const updateData = {
        title: 'Updated Title',
        description: 'Updated Description',
      };
      
      (Proposal.findOneAndUpdate as jest.Mock).mockResolvedValue({
        ...mockProposal,
        ...updateData,
      });
      
      // Act
      const result = await ProposalService.updateProposal('proposal-123', updateData, mockTenantId);
      
      // Assert
      expect(Proposal.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: 'proposal-123', tenantId: mockTenantId },
        { $set: updateData },
        { new: true }
      );
      expect(result).toEqual({
        ...mockProposal,
        ...updateData,
      });
    });
    
    it('should return null if proposal not found', async () => {
      // Arrange
      (Proposal.findOneAndUpdate as jest.Mock).mockResolvedValue(null);
      
      // Act
      const result = await ProposalService.updateProposal('proposal-123', { title: 'Updated Title' }, mockTenantId);
      
      // Assert
      expect(Proposal.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: 'proposal-123', tenantId: mockTenantId },
        { $set: { title: 'Updated Title' } },
        { new: true }
      );
      expect(result).toBeNull();
    });
    
    it('should throw an error if updating proposal fails', async () => {
      // Arrange
      const mockError = new Error('Failed to update proposal');
      (Proposal.findOneAndUpdate as jest.Mock).mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(ProposalService.updateProposal('proposal-123', { title: 'Updated Title' }, mockTenantId))
        .rejects.toThrow(mockError);
    });
  });
  
  describe('deleteProposal', () => {
    it('should delete a proposal', async () => {
      // Arrange
      (Proposal.deleteOne as jest.Mock).mockResolvedValue({ deletedCount: 1 });
      
      // Act
      const result = await ProposalService.deleteProposal('proposal-123', mockTenantId);
      
      // Assert
      expect(Proposal.deleteOne).toHaveBeenCalledWith({
        _id: 'proposal-123',
        tenantId: mockTenantId,
      });
      expect(result).toBe(true);
    });
    
    it('should return false if proposal not found', async () => {
      // Arrange
      (Proposal.deleteOne as jest.Mock).mockResolvedValue({ deletedCount: 0 });
      
      // Act
      const result = await ProposalService.deleteProposal('proposal-123', mockTenantId);
      
      // Assert
      expect(Proposal.deleteOne).toHaveBeenCalledWith({
        _id: 'proposal-123',
        tenantId: mockTenantId,
      });
      expect(result).toBe(false);
    });
    
    it('should throw an error if deleting proposal fails', async () => {
      // Arrange
      const mockError = new Error('Failed to delete proposal');
      (Proposal.deleteOne as jest.Mock).mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(ProposalService.deleteProposal('proposal-123', mockTenantId))
        .rejects.toThrow(mockError);
    });
  });
});
