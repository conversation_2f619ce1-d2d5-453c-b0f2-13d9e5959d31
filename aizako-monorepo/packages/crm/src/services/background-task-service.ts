import mongoose from 'mongoose';
import { EventEmitter } from 'events';

/**
 * Background task status
 */
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * Background task interface
 */
export interface IBackgroundTask extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  tenantId: string;
  userId?: string;
  taskType: string;
  status: TaskStatus;
  progress: number;
  params: Record<string, any>;
  result?: any;
  error?: string;
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  save(): Promise<this>;
}

/**
 * Background task schema
 */
const BackgroundTaskSchema = new mongoose.Schema<IBackgroundTask>(
  {
    tenantId: { type: String, required: true, index: true },
    userId: { type: String, index: true },
    taskType: { type: String, required: true, index: true },
    status: {
      type: String,
      enum: Object.values(TaskStatus),
      default: TaskStatus.PENDING,
      index: true,
    },
    progress: { type: Number, default: 0, min: 0, max: 100 },
    params: { type: Object, required: true },
    result: { type: mongoose.Schema.Types.Mixed },
    error: { type: String },
    startedAt: { type: Date },
    completedAt: { type: Date },
  },
  {
    timestamps: true,
  }
);

// Create indexes
BackgroundTaskSchema.index({ tenantId: 1, taskType: 1, status: 1 });
BackgroundTaskSchema.index({ createdAt: 1 });
BackgroundTaskSchema.index({ status: 1, taskType: 1 });

// Create the model
export const BackgroundTask = (mongoose.models.BackgroundTask as mongoose.Model<IBackgroundTask>) ||
  mongoose.model<IBackgroundTask>('BackgroundTask', BackgroundTaskSchema, 'backgroundtasks');

/**
 * Task handler function type
 */
export type TaskHandler = (
  task: IBackgroundTask,
  updateProgress: (progress: number) => Promise<void>
) => Promise<any>;

/**
 * Background Task Service
 *
 * This service provides background task processing capabilities.
 */
export class BackgroundTaskService {
  private static instance: BackgroundTaskService;
  private taskHandlers: Map<string, TaskHandler> = new Map();
  private eventEmitter: EventEmitter = new EventEmitter();
  private isProcessing: boolean = false;
  private processingInterval: NodeJS.Timeout | null = null;

  /**
   * Get the singleton instance
   * @returns BackgroundTaskService instance
   */
  public static getInstance(): BackgroundTaskService {
    if (!BackgroundTaskService.instance) {
      BackgroundTaskService.instance = new BackgroundTaskService();
    }
    return BackgroundTaskService.instance;
  }

  /**
   * Register a task handler
   * @param taskType Task type
   * @param handler Task handler function
   */
  public registerTaskHandler(taskType: string, handler: TaskHandler): void {
    this.taskHandlers.set(taskType, handler);
    console.log(`Registered handler for task type: ${taskType}`);
  }

  /**
   * Start the background task processor
   * @param intervalMs Processing interval in milliseconds (default: 5000)
   */
  public start(intervalMs: number = 5000): void {
    if (this.processingInterval) {
      return;
    }

    console.log('Starting background task processor');
    this.processingInterval = setInterval(() => this.processTasks(), intervalMs);

    // Process tasks immediately
    this.processTasks();
  }

  /**
   * Stop the background task processor
   */
  public stop(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('Stopped background task processor');
    }
  }

  /**
   * Create a new background task
   * @param taskType Task type
   * @param params Task parameters
   * @param tenantId Tenant ID
   * @param userId User ID (optional)
   * @returns Created task
   */
  public async createTask(
    taskType: string,
    params: Record<string, any>,
    tenantId: string,
    userId?: string
  ): Promise<IBackgroundTask> {
    const taskDoc = new BackgroundTask({
      tenantId,
      userId,
      taskType,
      status: TaskStatus.PENDING,
      progress: 0,
      params,
    });

    const task = await taskDoc.save();

    // Emit task created event
    this.eventEmitter.emit('taskCreated', task);

    return task;
  }

  /**
   * Get a task by ID
   * @param taskId Task ID
   * @returns Task or null if not found
   */
  public async getTask(taskId: string): Promise<IBackgroundTask | null> {
    return await BackgroundTask.findById(taskId).exec();
  }

  /**
   * Get tasks by tenant ID and status
   * @param tenantId Tenant ID
   * @param status Task status (optional)
   * @param taskType Task type (optional)
   * @param limit Maximum number of tasks to return (default: 100)
   * @returns Array of tasks
   */
  public async getTasks(
    tenantId: string,
    status?: TaskStatus,
    taskType?: string,
    limit: number = 100
  ): Promise<IBackgroundTask[]> {
    const query: any = { tenantId };

    if (status) {
      query.status = status;
    }

    if (taskType) {
      query.taskType = taskType;
    }

    return await BackgroundTask.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }

  /**
   * Cancel a task
   * @param taskId Task ID
   * @param tenantId Tenant ID
   * @returns True if successful
   */
  public async cancelTask(taskId: string, tenantId: string): Promise<boolean> {
    const task = await BackgroundTask.findOne({
      _id: new mongoose.Types.ObjectId(taskId),
      tenantId,
    }).exec();

    if (!task || task.status !== TaskStatus.PENDING) {
      return false;
    }

    task.status = TaskStatus.CANCELLED;
    await task.save();

    return true;
  }

  /**
   * Process pending tasks
   * @private
   */
  private async processTasks(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      // Find pending tasks
      const pendingTasks = await BackgroundTask.find({
        status: TaskStatus.PENDING,
      }).sort({ createdAt: 1 }).limit(10).exec();

      if (pendingTasks.length === 0) {
        this.isProcessing = false;
        return;
      }

      console.log(`Processing ${pendingTasks.length} pending tasks`);

      // Process each task
      for (const task of pendingTasks) {
        await this.processTask(task);
      }
    } catch (error) {
      console.error('Error processing tasks:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single task
   * @param task Task to process
   * @private
   */
  private async processTask(task: IBackgroundTask): Promise<void> {
    const handler = this.taskHandlers.get(task.taskType);

    if (!handler) {
      console.error(`No handler registered for task type: ${task.taskType}`);
      task.status = TaskStatus.FAILED;
      task.error = `No handler registered for task type: ${task.taskType}`;
      await task.save();
      return;
    }

    // Update task status to running
    task.status = TaskStatus.RUNNING;
    task.startedAt = new Date();
    await task.save();

    try {
      // Create progress update function
      const updateProgress = async (progress: number) => {
        task.progress = Math.min(Math.max(progress, 0), 100);
        await task.save();
      };

      // Execute the task handler
      const result = await handler(task, updateProgress);

      // Update task status to completed
      task.status = TaskStatus.COMPLETED;
      task.progress = 100;
      task.result = result;
      task.completedAt = new Date();
      await task.save();

      // Emit task completed event
      this.eventEmitter.emit('taskCompleted', task);
    } catch (error) {
      console.error(`Error processing task ${task._id}:`, error);

      // Update task status to failed
      task.status = TaskStatus.FAILED;
      task.error = error instanceof Error ? error.message : String(error);
      await task.save();

      // Emit task failed event
      this.eventEmitter.emit('taskFailed', task);
    }
  }

  /**
   * Subscribe to task events
   * @param event Event name ('taskCreated', 'taskCompleted', 'taskFailed')
   * @param listener Event listener
   */
  public on(event: string, listener: (task: IBackgroundTask) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * Unsubscribe from task events
   * @param event Event name
   * @param listener Event listener
   */
  public off(event: string, listener: (task: IBackgroundTask) => void): void {
    this.eventEmitter.off(event, listener);
  }
}

export default BackgroundTaskService;
