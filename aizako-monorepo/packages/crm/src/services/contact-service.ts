import { IContact, IInteraction } from '../models/contact';
import { PaginationParams } from '../types/pagination';
import { contactRepository, IContactRepository } from '../repositories';
import { QueryOptions } from '../types/query';
import { logger } from '../utils/logger';
import mongoose from 'mongoose';

/**
 * Contact filter options
 */
export interface ContactFilterOptions {
  search?: string;
  status?: string | string[];
  tags?: string | string[];
  owner?: string;
  companyId?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  lastContactedAfter?: Date;
  lastContactedBefore?: Date;
  scoreMin?: number;
  scoreMax?: number;
  customFields?: Record<string, any>;
}

/**
 * Contact sort options
 */
export interface ContactSortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Contact pagination options
 */
export interface ContactPaginationOptions {
  page: number;
  limit: number;
}

/**
 * Contact service
 */
export class ContactService {
  /**
   * Contact repository
   */
  private readonly contactRepo: IContactRepository;

  /**
   * Create a new contact service
   * @param contactRepo Contact repository
   */
  constructor(contactRepo: IContactRepository = contactRepository) {
    this.contactRepo = contactRepo;
  }

  /**
   * Create a new contact
   * @param contactData Contact data
   * @param tenantId Tenant ID
   * @returns Created contact
   */
  async createContact(contactData: Partial<IContact>, tenantId: string): Promise<IContact> {
    try {
      return await this.contactRepo.create({
        ...contactData,
        tenantId,
      });
    } catch (error) {
      logger.error('Error creating contact:', { error, contactData, tenantId });
      throw error;
    }
  }

  /**
   * Get contact by ID
   * @param id Contact ID
   * @param tenantId Tenant ID
   * @returns Contact or null if not found
   */
  async getContactById(id: string, tenantId: string): Promise<IContact | null> {
    try {
      return await this.contactRepo.findById(id, tenantId);
    } catch (error) {
      logger.error('Error getting contact by ID:', { error, id, tenantId });
      throw error;
    }
  }

  /**
   * Get contacts with pagination
   * @param params Pagination parameters
   * @param tenantId Tenant ID
   * @returns Paginated contacts
   */
  async getContacts(
    params: PaginationParams,
    tenantId: string
  ): Promise<{ contacts: IContact[]; total: number }> {
    try {
      const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = params;

      // Convert to query options
      const options: QueryOptions = {
        filter: filters,
        sort: { [sortBy]: sortOrder === 'asc' ? 1 : -1 },
        page,
        limit,
      };

      // Get paginated contacts
      const result = await this.contactRepo.findAllPaginated(tenantId, options);

      return {
        contacts: result.items,
        total: result.total
      };
    } catch (error) {
      logger.error('Error getting contacts:', { error, params, tenantId });
      throw error;
    }
  }

  /**
   * Get contacts with advanced filtering, sorting, and pagination
   * @param options Filter options
   * @param sortOptions Sort options
   * @param paginationOptions Pagination options
   * @param tenantId Tenant ID
   * @returns Contacts and total count
   */
  async getContactsAdvanced(
    options: ContactFilterOptions = {},
    sortOptions: ContactSortOptions = { field: 'createdAt', direction: 'desc' },
    paginationOptions: ContactPaginationOptions = { page: 1, limit: 20 },
    tenantId: string
  ): Promise<{ contacts: IContact[]; total: number }> {
    try {
      const filter: any = {};

      // Apply search filter
      if (options.search) {
        filter.$or = [
          { firstName: { $regex: options.search, $options: 'i' } },
          { lastName: { $regex: options.search, $options: 'i' } },
          { email: { $regex: options.search, $options: 'i' } },
        ];
      }

      // Apply status filter
      if (options.status) {
        filter.status = Array.isArray(options.status)
          ? { $in: options.status }
          : options.status;
      }

      // Apply tags filter
      if (options.tags) {
        filter.tags = Array.isArray(options.tags)
          ? { $all: options.tags }
          : { $all: [options.tags] };
      }

      // Apply owner filter
      if (options.owner) {
        filter.owner = new mongoose.Types.ObjectId(options.owner);
      }

      // Apply company filter
      if (options.companyId) {
        filter.companyId = new mongoose.Types.ObjectId(options.companyId);
      }

      // Apply date filters
      if (options.createdAfter || options.createdBefore) {
        filter.createdAt = {};

        if (options.createdAfter) {
          filter.createdAt.$gte = options.createdAfter;
        }

        if (options.createdBefore) {
          filter.createdAt.$lte = options.createdBefore;
        }
      }

      // Apply last contacted filters
      if (options.lastContactedAfter || options.lastContactedBefore) {
        filter.lastContactedAt = {};

        if (options.lastContactedAfter) {
          filter.lastContactedAt.$gte = options.lastContactedAfter;
        }

        if (options.lastContactedBefore) {
          filter.lastContactedAt.$lte = options.lastContactedBefore;
        }
      }

      // Apply score filters
      if (options.scoreMin !== undefined || options.scoreMax !== undefined) {
        filter['score.current'] = {};

        if (options.scoreMin !== undefined) {
          filter['score.current'].$gte = options.scoreMin;
        }

        if (options.scoreMax !== undefined) {
          filter['score.current'].$lte = options.scoreMax;
        }
      }

      // Apply custom fields filters
      if (options.customFields) {
        for (const [key, value] of Object.entries(options.customFields)) {
          filter[`customFields.${key}`] = value;
        }
      }

      // Create sort object
      const sort: any = {};
      sort[sortOptions.field] = sortOptions.direction === 'asc' ? 1 : -1;

      // Create query options
      const queryOptions: QueryOptions = {
        filter,
        sort,
        page: paginationOptions.page,
        limit: paginationOptions.limit,
      };

      // Get paginated contacts
      const result = await this.contactRepo.findAllPaginated(tenantId, queryOptions);

      return {
        contacts: result.items,
        total: result.total
      };
    } catch (error) {
      logger.error('Error getting contacts with advanced filtering:', { error, options, sortOptions, paginationOptions, tenantId });
      throw error;
    }
  }

  /**
   * Update contact
   * @param id Contact ID
   * @param contactData Contact data to update
   * @param tenantId Tenant ID
   * @returns Updated contact or null if not found
   */
  async updateContact(
    id: string,
    contactData: Partial<IContact>,
    tenantId: string
  ): Promise<IContact | null> {
    try {
      return await this.contactRepo.update(id, contactData, tenantId);
    } catch (error) {
      logger.error('Error updating contact:', { error, id, contactData, tenantId });
      throw error;
    }
  }

  /**
   * Delete contact
   * @param id Contact ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  async deleteContact(id: string, tenantId: string): Promise<boolean> {
    try {
      return await this.contactRepo.delete(id, tenantId);
    } catch (error) {
      logger.error('Error deleting contact:', { error, id, tenantId });
      throw error;
    }
  }

  /**
   * Add an interaction to a contact
   * @param contactId Contact ID
   * @param interaction Interaction data
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  async addInteraction(
    contactId: string,
    interaction: IInteraction,
    tenantId: string
  ): Promise<IContact | null> {
    try {
      const contact = await this.contactRepo.findById(contactId, tenantId);

      if (!contact) {
        return null;
      }

      // Add interaction and update last contacted date
      const updatedContact = {
        ...contact,
        interactions: [...(contact.interactions || []), interaction],
        lastContactedAt: new Date(),
      };

      return await this.contactRepo.update(contactId, updatedContact, tenantId);
    } catch (error) {
      logger.error('Error adding interaction to contact:', { error, contactId, interaction, tenantId });
      throw error;
    }
  }

  /**
   * Update contact score
   * @param contactId Contact ID
   * @param score Score data
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  async updateScore(
    contactId: string,
    score: IContact['score'],
    tenantId: string
  ): Promise<IContact | null> {
    try {
      return await this.contactRepo.update(contactId, { score }, tenantId);
    } catch (error) {
      logger.error('Error updating contact score:', { error, contactId, score, tenantId });
      throw error;
    }
  }

  /**
   * Update contact persona
   * @param contactId Contact ID
   * @param persona Persona data
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  async updatePersona(
    contactId: string,
    persona: IContact['persona'],
    tenantId: string
  ): Promise<IContact | null> {
    try {
      return await this.contactRepo.update(contactId, { persona }, tenantId);
    } catch (error) {
      logger.error('Error updating contact persona:', { error, contactId, persona, tenantId });
      throw error;
    }
  }

  /**
   * Add a tag to a contact
   * @param contactId Contact ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  async addTag(
    contactId: string,
    tagId: string,
    tenantId: string
  ): Promise<IContact | null> {
    try {
      return await this.contactRepo.addTag(contactId, tagId, tenantId);
    } catch (error) {
      logger.error('Error adding tag to contact:', { error, contactId, tagId, tenantId });
      throw error;
    }
  }

  /**
   * Remove a tag from a contact
   * @param contactId Contact ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  async removeTag(
    contactId: string,
    tagId: string,
    tenantId: string
  ): Promise<IContact | null> {
    try {
      return await this.contactRepo.removeTag(contactId, tagId, tenantId);
    } catch (error) {
      logger.error('Error removing tag from contact:', { error, contactId, tagId, tenantId });
      throw error;
    }
  }

  /**
   * Search contacts
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated contacts
   */
  async searchContacts(
    query: string,
    tenantId: string,
    options: QueryOptions = {}
  ): Promise<{ contacts: IContact[]; total: number; page: number; limit: number; totalPages: number }> {
    try {
      const result = await this.contactRepo.search(query, tenantId, options);
      return {
        contacts: result.items,
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      };
    } catch (error) {
      logger.error('Error searching contacts:', { error, query, tenantId, options });
      throw error;
    }
  }
}
