import { createHash } from 'crypto';
import mongoose from 'mongoose';
import { IProposal, ProposalDownloadOptions } from '../types/proposals';

/**
 * Document cache model interface
 */
interface IDocumentCache {
  _id: mongoose.Types.ObjectId;
  proposalId: mongoose.Types.ObjectId;
  tenantId: string;
  format: string;
  options: Record<string, any>;
  hash: string;
  content: Buffer | string;
  createdAt: Date;
  expiresAt: Date;
}

/**
 * Document cache schema
 */
const DocumentCacheSchema = new mongoose.Schema<IDocumentCache>({
  proposalId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Proposal',
  },
  tenantId: {
    type: String,
    required: true,
    index: true,
  },
  format: {
    type: String,
    required: true,
    enum: ['pdf', 'docx', 'md'],
  },
  options: {
    type: Object,
    required: true,
  },
  hash: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  content: {
    type: Buffer,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  expiresAt: {
    type: Date,
    required: true,
    index: true,
  },
});

// Create TTL index on expiresAt field
DocumentCacheSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Create compound index on proposalId and format
DocumentCacheSchema.index({ proposalId: 1, format: 1 });

// Create model
const DocumentCache = mongoose.model<IDocumentCache>('DocumentCache', DocumentCacheSchema);

/**
 * Document Cache Service
 * 
 * This service provides caching for generated documents to improve performance.
 */
export class DocumentCacheService {
  /**
   * Default cache expiration time (24 hours)
   */
  private static DEFAULT_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Generate a hash for a proposal and options
   * @param proposal Proposal object
   * @param options Download options
   * @returns Hash string
   */
  private static generateHash(proposal: IProposal, options: ProposalDownloadOptions): string {
    const proposalData = {
      id: proposal._id.toString(),
      title: proposal.title,
      description: proposal.description,
      sections: proposal.sections,
      pricing: proposal.pricing,
      terms: proposal.terms,
      updatedAt: proposal.updatedAt.toISOString(),
    };

    const data = JSON.stringify({
      proposal: proposalData,
      options,
    });

    return createHash('sha256').update(data).digest('hex');
  }

  /**
   * Get a cached document
   * @param proposal Proposal object
   * @param options Download options
   * @param tenantId Tenant ID
   * @returns Cached document content or null if not found
   */
  public static async getCache(
    proposal: IProposal,
    options: ProposalDownloadOptions,
    tenantId: string
  ): Promise<Buffer | string | null> {
    const hash = this.generateHash(proposal, options);

    const cache = await DocumentCache.findOne({
      hash,
      tenantId,
      expiresAt: { $gt: new Date() },
    });

    if (!cache) {
      return null;
    }

    return cache.content;
  }

  /**
   * Set a cached document
   * @param proposal Proposal object
   * @param options Download options
   * @param content Document content
   * @param tenantId Tenant ID
   * @param expiration Expiration time in milliseconds (default: 24 hours)
   * @returns True if successful
   */
  public static async setCache(
    proposal: IProposal,
    options: ProposalDownloadOptions,
    content: Buffer | string,
    tenantId: string,
    expiration: number = this.DEFAULT_EXPIRATION
  ): Promise<boolean> {
    const hash = this.generateHash(proposal, options);
    const expiresAt = new Date(Date.now() + expiration);

    try {
      // Delete any existing cache for this proposal and format
      await DocumentCache.deleteMany({
        proposalId: new mongoose.Types.ObjectId(proposal._id.toString()),
        format: options.format,
        tenantId,
      });

      // Create new cache entry
      await DocumentCache.create({
        proposalId: new mongoose.Types.ObjectId(proposal._id.toString()),
        tenantId,
        format: options.format,
        options,
        hash,
        content,
        expiresAt,
      });

      return true;
    } catch (error) {
      console.error('Error setting document cache:', error);
      return false;
    }
  }

  /**
   * Invalidate cache for a proposal
   * @param proposalId Proposal ID
   * @param tenantId Tenant ID
   * @returns Number of cache entries deleted
   */
  public static async invalidateCache(
    proposalId: string,
    tenantId: string
  ): Promise<number> {
    try {
      const result = await DocumentCache.deleteMany({
        proposalId: new mongoose.Types.ObjectId(proposalId),
        tenantId,
      });

      return result.deletedCount;
    } catch (error) {
      console.error('Error invalidating document cache:', error);
      return 0;
    }
  }

  /**
   * Clean expired cache entries
   * @returns Number of cache entries deleted
   */
  public static async cleanExpiredCache(): Promise<number> {
    try {
      const result = await DocumentCache.deleteMany({
        expiresAt: { $lt: new Date() },
      });

      return result.deletedCount;
    } catch (error) {
      console.error('Error cleaning expired document cache:', error);
      return 0;
    }
  }
}

export default DocumentCacheService;
