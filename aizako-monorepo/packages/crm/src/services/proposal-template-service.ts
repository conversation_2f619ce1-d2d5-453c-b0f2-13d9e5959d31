import axios from 'axios';
import { API_BASE_URL } from '../config';
import { getAuthToken } from '../utils/auth';

/**
 * Proposal Template Service
 * 
 * This service provides methods for managing proposal templates.
 */
export class ProposalTemplateService {
  /**
   * Get all templates
   * @param tenantId Tenant ID
   * @returns Templates
   */
  static async getTemplates(tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-templates`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting templates:', error);
      throw error;
    }
  }
  
  /**
   * Get template by ID
   * @param templateId Template ID
   * @param tenantId Tenant ID
   * @returns Template
   */
  static async getTemplateById(templateId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-templates/${templateId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting template:', error);
      throw error;
    }
  }
  
  /**
   * Create template
   * @param templateData Template data
   * @returns Created template
   */
  static async createTemplate(templateData: any): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-templates`, templateData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': templateData.tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error creating template:', error);
      throw error;
    }
  }
  
  /**
   * Update template
   * @param templateId Template ID
   * @param templateData Template data
   * @returns Updated template
   */
  static async updateTemplate(templateId: string, templateData: any): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.put(`${API_BASE_URL}/api/crm/proposal-templates/${templateId}`, templateData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': templateData.tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error updating template:', error);
      throw error;
    }
  }
  
  /**
   * Delete template
   * @param templateId Template ID
   * @param tenantId Tenant ID
   * @returns Success status
   */
  static async deleteTemplate(templateId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.delete(`${API_BASE_URL}/api/crm/proposal-templates/${templateId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error deleting template:', error);
      throw error;
    }
  }
  
  /**
   * Create proposal from template
   * @param templateId Template ID
   * @param proposalData Additional proposal data
   * @param tenantId Tenant ID
   * @returns Created proposal
   */
  static async createProposalFromTemplate(
    templateId: string,
    proposalData: any,
    tenantId: string
  ): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(
        `${API_BASE_URL}/api/crm/proposal-templates/${templateId}/create-proposal`,
        proposalData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'X-Tenant-ID': tenantId,
          },
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Error creating proposal from template:', error);
      throw error;
    }
  }
  
  /**
   * Get templates by category
   * @param category Category
   * @param tenantId Tenant ID
   * @returns Templates
   */
  static async getTemplatesByCategory(category: string, tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-templates/category/${category}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting templates by category:', error);
      throw error;
    }
  }
  
  /**
   * Get templates by user
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Templates
   */
  static async getTemplatesByUser(userId: string, tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-templates/user/${userId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting templates by user:', error);
      throw error;
    }
  }
  
  /**
   * Get shared templates
   * @param tenantId Tenant ID
   * @returns Shared templates
   */
  static async getSharedTemplates(tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-templates/shared`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting shared templates:', error);
      throw error;
    }
  }
  
  /**
   * Star template
   * @param templateId Template ID
   * @param tenantId Tenant ID
   * @returns Updated template
   */
  static async starTemplate(templateId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-templates/${templateId}/star`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error starring template:', error);
      throw error;
    }
  }
  
  /**
   * Unstar template
   * @param templateId Template ID
   * @param tenantId Tenant ID
   * @returns Updated template
   */
  static async unstarTemplate(templateId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(`${API_BASE_URL}/api/crm/proposal-templates/${templateId}/unstar`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error unstarring template:', error);
      throw error;
    }
  }
  
  /**
   * Get template categories
   * @param tenantId Tenant ID
   * @returns Template categories
   */
  static async getTemplateCategories(tenantId: string): Promise<string[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/proposal-templates/categories`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting template categories:', error);
      throw error;
    }
  }
}

export default ProposalTemplateService;
