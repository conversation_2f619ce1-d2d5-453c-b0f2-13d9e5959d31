import { Proposal } from '../models/proposal';
import * as ProposalTypes from '../types/proposals';
import { PaginationParams } from '../types/pagination';

// Use namespaced types
type IProposal = ProposalTypes.Types.IProposal;
type IProposalAnalyticsEvent = ProposalTypes.Analytics.IProposalAnalyticsEvent;
type ProposalDownloadOptions = {
  format?: 'pdf' | 'docx' | 'md';
  includeHeader?: boolean;
  includeFooter?: boolean;
  includeBranding?: boolean;
  includePageNumbers?: boolean;
  colorScheme?: string;
  paperSize?: 'a4' | 'letter' | 'legal';
  skipCache?: 'true' | 'false';
};
type ProposalSendOptions = {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  message: string;
  includeLink?: boolean;
  includeAttachment?: boolean;
  attachmentFormat?: 'pdf' | 'docx' | 'md';
  expiresAt?: Date;
};
type ProposalAIGenerationOptions = {
  prompt: string;
  model: string;
  includeSections?: {
    executiveSummary?: boolean;
    solution?: boolean;
    timeline?: boolean;
    pricing?: boolean;
    team?: boolean;
    testimonials?: boolean;
    terms?: boolean;
  };
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
};
import { DocumentGenerationService } from './document-generation-service';
import { AIService } from './ai-service';
import mongoose from 'mongoose';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

/**
 * Proposal filter options
 */
export interface ProposalFilterOptions {
  search?: string;
  status?: string | string[];
  opportunityId?: string;
  companyId?: string;
  contactIds?: string | string[];
  owner?: string;
  tags?: string | string[];
  createdAfter?: Date;
  createdBefore?: Date;
  sentAfter?: Date;
  sentBefore?: Date;
  viewedAfter?: Date;
  viewedBefore?: Date;
  expiresAfter?: Date;
  expiresBefore?: Date;
  aiGenerated?: boolean;
  customFields?: Record<string, any>;
}

/**
 * Proposal sort options
 */
export interface ProposalSortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Proposal pagination options
 */
export interface ProposalPaginationOptions {
  page: number;
  limit: number;
}

/**
 * Proposal service
 */
export class ProposalService {
  /**
   * Create a new proposal
   * @param proposalData Proposal data
   * @param tenantId Tenant ID
   * @returns Created proposal
   */
  static async createProposal(proposalData: Partial<IProposal>, tenantId: string): Promise<IProposal> {
    try {
      // Generate a public token for sharing
      const publicToken = crypto.randomBytes(32).toString('hex');

      const proposal = new Proposal({
        ...proposalData,
        tenantId,
        publicToken,
        publicUrl: `/proposals/public/${publicToken}`,
        viewCount: 0,
        status: 'draft',
      });

      return await proposal.save();
    } catch (error) {
      console.error('Error creating proposal:', error);
      throw error;
    }
  }

  /**
   * Get proposal by ID
   * @param id Proposal ID
   * @param tenantId Tenant ID
   * @returns Proposal or null if not found
   */
  static async getProposalById(id: string, tenantId: string): Promise<IProposal | null> {
    try {
      return await Proposal.findOne({ _id: id, tenantId });
    } catch (error) {
      console.error('Error getting proposal by ID:', error);
      throw error;
    }
  }

  /**
   * Get proposal by public token
   * @param token Public token
   * @returns Proposal or null if not found
   */
  static async getProposalByToken(token: string): Promise<IProposal | null> {
    try {
      return await Proposal.findOne({
        publicToken: token,
        publicAccessEnabled: true,
        status: { $nin: ['draft', 'expired'] }
      });
    } catch (error) {
      console.error('Error getting proposal by token:', error);
      throw error;
    }
  }

  /**
   * Get proposals with pagination
   * @param params Pagination parameters
   * @param tenantId Tenant ID
   * @returns Paginated proposals
   */
  static async getProposals(
    params: PaginationParams,
    tenantId: string
  ): Promise<{ proposals: IProposal[]; total: number }> {
    try {
      const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = params;

      const query = Proposal.find({ tenantId });

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query.where(key).equals(value);
        }
      });

      // Get total count
      const total = await Proposal.countDocuments(query.getQuery());

      // Apply pagination and sorting
      const proposals = await query
        .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      return { proposals, total };
    } catch (error) {
      console.error('Error getting proposals:', error);
      throw error;
    }
  }

  /**
   * Get proposals with advanced filtering, sorting, and pagination
   * @param options Filter options
   * @param sortOptions Sort options
   * @param paginationOptions Pagination options
   * @param tenantId Tenant ID
   * @returns Proposals and total count
   */
  static async getProposalsAdvanced(
    options: ProposalFilterOptions = {},
    sortOptions: ProposalSortOptions = { field: 'createdAt', direction: 'desc' },
    paginationOptions: ProposalPaginationOptions = { page: 1, limit: 20 },
    tenantId: string
  ): Promise<{ proposals: IProposal[]; total: number }> {
    try {
      const filter: any = { tenantId };

      // Apply search filter
      if (options.search) {
        filter.$or = [
          { title: { $regex: options.search, $options: 'i' } },
          { description: { $regex: options.search, $options: 'i' } },
        ];
      }

      // Apply status filter
      if (options.status) {
        filter.status = Array.isArray(options.status)
          ? { $in: options.status }
          : options.status;
      }

      // Apply opportunity filter
      if (options.opportunityId) {
        filter.opportunityId = new mongoose.Types.ObjectId(options.opportunityId);
      }

      // Apply company filter
      if (options.companyId) {
        filter.companyId = new mongoose.Types.ObjectId(options.companyId);
      }

      // Apply contacts filter
      if (options.contactIds) {
        if (Array.isArray(options.contactIds)) {
          filter.contactIds = { $in: options.contactIds.map(id => new mongoose.Types.ObjectId(id)) };
        } else {
          filter.contactIds = new mongoose.Types.ObjectId(options.contactIds);
        }
      }

      // Apply owner filter
      if (options.owner) {
        filter.owner = new mongoose.Types.ObjectId(options.owner);
      }

      // Apply tags filter
      if (options.tags) {
        filter.tags = Array.isArray(options.tags)
          ? { $all: options.tags }
          : { $all: [options.tags] };
      }

      // Apply created date filters
      if (options.createdAfter || options.createdBefore) {
        filter.createdAt = {};

        if (options.createdAfter) {
          filter.createdAt.$gte = options.createdAfter;
        }

        if (options.createdBefore) {
          filter.createdAt.$lte = options.createdBefore;
        }
      }

      // Apply sent date filters
      if (options.sentAfter || options.sentBefore) {
        filter.sentAt = {};

        if (options.sentAfter) {
          filter.sentAt.$gte = options.sentAfter;
        }

        if (options.sentBefore) {
          filter.sentAt.$lte = options.sentBefore;
        }
      }

      // Apply viewed date filters
      if (options.viewedAfter || options.viewedBefore) {
        filter.viewedAt = {};

        if (options.viewedAfter) {
          filter.viewedAt.$gte = options.viewedAfter;
        }

        if (options.viewedBefore) {
          filter.viewedAt.$lte = options.viewedBefore;
        }
      }

      // Apply expires date filters
      if (options.expiresAfter || options.expiresBefore) {
        filter.expiresAt = {};

        if (options.expiresAfter) {
          filter.expiresAt.$gte = options.expiresAfter;
        }

        if (options.expiresBefore) {
          filter.expiresAt.$lte = options.expiresBefore;
        }
      }

      // Apply AI generated filter
      if (options.aiGenerated !== undefined) {
        filter.aiGenerated = options.aiGenerated;
      }

      // Apply custom fields filters
      if (options.customFields) {
        for (const [key, value] of Object.entries(options.customFields)) {
          filter[`customFields.${key}`] = value;
        }
      }

      // Calculate skip value for pagination
      const skip = (paginationOptions.page - 1) * paginationOptions.limit;

      // Create sort object
      const sort: any = {};
      sort[sortOptions.field] = sortOptions.direction === 'asc' ? 1 : -1;

      // Execute query with pagination
      const [proposals, total] = await Promise.all([
        Proposal.find(filter)
          .sort(sort)
          .skip(skip)
          .limit(paginationOptions.limit),
        Proposal.countDocuments(filter),
      ]);

      return { proposals, total };
    } catch (error) {
      console.error('Error getting proposals with advanced filtering:', error);
      throw error;
    }
  }

  /**
   * Update proposal
   * @param id Proposal ID
   * @param proposalData Proposal data to update
   * @param tenantId Tenant ID
   * @param userId User ID (optional, for version history)
   * @param comment Comment for version history (optional)
   * @returns Updated proposal or null if not found
   */
  static async updateProposal(
    id: string,
    proposalData: Partial<IProposal>,
    tenantId: string,
    userId?: string,
    comment?: string
  ): Promise<IProposal | null> {
    try {
      // Get the current proposal for version history
      const currentProposal = await Proposal.findOne({
        _id: id,
        tenantId
      });

      if (!currentProposal) {
        return null;
      }

      // Update the proposal
      const updatedProposal = await Proposal.findOneAndUpdate(
        { _id: id, tenantId },
        { $set: proposalData },
        { new: true }
      );

      // Create version history if userId is provided
      if (updatedProposal && userId) {
        try {
          const VersionHistoryService = require('./version-history-service').default;
          await VersionHistoryService.createVersion(
            updatedProposal,
            currentProposal.toObject(),
            userId,
            comment
          );
        } catch (versionError) {
          console.error('Error creating version history:', versionError);
          // Continue even if version history fails
        }
      }

      return updatedProposal;
    } catch (error) {
      console.error('Error updating proposal:', error);
      throw error;
    }
  }

  /**
   * Delete proposal
   * @param id Proposal ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteProposal(id: string, tenantId: string): Promise<boolean> {
    try {
      const result = await Proposal.deleteOne({ _id: id, tenantId });
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting proposal:', error);
      throw error;
    }
  }

  /**
   * Send proposal
   * @param id Proposal ID
   * @param userId User ID sending the proposal
   * @param tenantId Tenant ID
   * @param options Send options
   * @returns Updated proposal
   */
  static async sendProposal(
    id: string,
    userId: string,
    tenantId: string,
    options: ProposalSendOptions
  ): Promise<IProposal | null> {
    try {
      // Get the proposal
      const proposal = await this.getProposalById(id, tenantId);

      if (!proposal) {
        throw new Error('Proposal not found');
      }

      // Update the proposal status
      const updatedProposal = await Proposal.findOneAndUpdate(
        { _id: id, tenantId },
        {
          $set: {
            status: 'sent',
            sentAt: new Date(),
            sentBy: new mongoose.Types.ObjectId(userId),
            ...(options.expiresAt && { expiresAt: options.expiresAt }),
          },
        },
        { new: true }
      );

      // TODO: Implement email sending logic
      // This would involve integrating with an email service like Resend
      // For now, we'll just return the updated proposal

      return updatedProposal;
    } catch (error) {
      console.error('Error sending proposal:', error);
      throw error;
    }
  }

  /**
   * Record proposal view
   * @param id Proposal ID
   * @param analyticsEvent Analytics event data
   * @returns Updated proposal
   */
  static async recordView(
    id: string,
    analyticsEvent: IProposalAnalyticsEvent
  ): Promise<IProposal | null> {
    try {
      return await Proposal.findOneAndUpdate(
        { _id: id },
        {
          $inc: { viewCount: 1 },
          $set: {
            status: 'viewed',
            viewedAt: new Date(),
            lastViewedAt: new Date()
          },
          $push: { analyticsEvents: analyticsEvent },
        },
        { new: true }
      );
    } catch (error) {
      console.error('Error recording proposal view:', error);
      throw error;
    }
  }

  /**
   * Accept proposal
   * @param id Proposal ID
   * @param userId User ID accepting the proposal
   * @param tenantId Tenant ID
   * @returns Updated proposal
   */
  static async acceptProposal(
    id: string,
    userId: string,
    tenantId: string
  ): Promise<IProposal | null> {
    try {
      return await Proposal.findOneAndUpdate(
        { _id: id, tenantId },
        {
          $set: {
            status: 'accepted',
            acceptedAt: new Date(),
            acceptedBy: new mongoose.Types.ObjectId(userId),
          },
        },
        { new: true }
      );
    } catch (error) {
      console.error('Error accepting proposal:', error);
      throw error;
    }
  }

  /**
   * Reject proposal
   * @param id Proposal ID
   * @param userId User ID rejecting the proposal
   * @param reason Rejection reason
   * @param tenantId Tenant ID
   * @returns Updated proposal
   */
  static async rejectProposal(
    id: string,
    userId: string,
    reason: string,
    tenantId: string
  ): Promise<IProposal | null> {
    try {
      return await Proposal.findOneAndUpdate(
        { _id: id, tenantId },
        {
          $set: {
            status: 'rejected',
            rejectedAt: new Date(),
            rejectedBy: new mongoose.Types.ObjectId(userId),
            rejectionReason: reason,
          },
        },
        { new: true }
      );
    } catch (error) {
      console.error('Error rejecting proposal:', error);
      throw error;
    }
  }
  /**
   * Generate proposal document
   * @param proposal Proposal data
   * @param options Document generation options
   * @param skipCache Whether to skip the cache
   * @returns Generated document
   */
  static async generateProposalDocument(
    proposal: IProposal,
    options: ProposalDownloadOptions,
    skipCache: boolean = false
  ): Promise<Buffer | string> {
    try {
      return await DocumentGenerationService.generateDocument(
        proposal,
        options,
        proposal.tenantId,
        skipCache
      );
    } catch (error) {
      console.error('Error generating proposal document:', error);
      throw error;
    }
  }

  /**
   * Generate proposal with AI
   * @param options AI generation options
   * @param tenantId Tenant ID
   * @returns Generated proposal
   */
  static async generateProposalWithAI(
    options: ProposalAIGenerationOptions,
    tenantId: string
  ): Promise<IProposal> {
    try {
      // Generate proposal data with AI
      const proposalData = await AIService.generateProposal(options);

      // Create the proposal
      return await this.createProposal({
        ...proposalData,
        status: 'draft',
        aiGenerated: true,
        aiPrompt: options.prompt,
        aiModel: options.model,
      }, tenantId);
    } catch (error) {
      console.error('Error generating proposal with AI:', error);
      throw error;
    }
  }

  /**
   * Generate proposal section with AI
   * @param sectionType Section type
   * @param prompt Prompt
   * @param model Model
   * @param context Additional context
   * @returns Generated section
   */
  static async generateProposalSectionWithAI(
    sectionType: string,
    prompt: string,
    model: string,
    context: {
      opportunityId?: string;
      companyId?: string;
      contactIds?: string[];
    } = {}
  ): Promise<{ id: string; title: string; content: string; type: string; order: number; isVisible: boolean; aiGenerated: boolean }> {
    try {
      // Generate section with AI
      const sectionData = await AIService.generateProposalSection(
        sectionType,
        prompt,
        model,
        context
      );

      // Add additional metadata
      return {
        ...sectionData,
        id: uuidv4(),
        order: 0,
        isVisible: true,
        aiGenerated: true,
      };
    } catch (error) {
      console.error('Error generating proposal section with AI:', error);
      throw error;
    }
  }
}

export default ProposalService;
