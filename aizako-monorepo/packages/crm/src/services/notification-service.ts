import mongoose from 'mongoose';
import { connectToMongoDB } from '../utils/mongodb';
import { MONGODB_URI } from '../config';

// Import notification model
import { Notification } from '../models';

/**
 * Notification Service
 * 
 * This service handles creating, retrieving, and managing notifications
 * for users in the CRM system.
 */
export class NotificationService {
  private static instance: NotificationService;
  
  /**
   * Get the singleton instance of the notification service
   * @returns NotificationService instance
   */
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }
  
  /**
   * Create a notification
   * @param tenantId Tenant ID
   * @param userId User ID
   * @param title Notification title
   * @param message Notification message
   * @param type Notification type
   * @param entityType Entity type (optional)
   * @param entityId Entity ID (optional)
   * @param data Additional data (optional)
   * @returns Created notification
   */
  async createNotification(
    tenantId: string,
    userId: string,
    title: string,
    message: string,
    type: 'info' | 'success' | 'warning' | 'error',
    entityType?: string,
    entityId?: string,
    data?: Record<string, any>
  ) {
    await connectToMongoDB(MONGODB_URI);
    
    const notification = new Notification({
      tenantId,
      userId,
      title,
      message,
      type,
      entityType,
      entityId,
      data,
      read: false,
      createdAt: new Date(),
    });
    
    return notification.save();
  }
  
  /**
   * Get notifications for a user
   * @param tenantId Tenant ID
   * @param userId User ID
   * @param limit Maximum number of notifications to return
   * @param skip Number of notifications to skip
   * @param includeRead Whether to include read notifications
   * @returns Notifications
   */
  async getNotifications(
    tenantId: string,
    userId: string,
    limit: number = 10,
    skip: number = 0,
    includeRead: boolean = false
  ) {
    await connectToMongoDB(MONGODB_URI);
    
    const query: any = {
      tenantId,
      userId,
    };
    
    if (!includeRead) {
      query.read = false;
    }
    
    return Notification.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
  }
  
  /**
   * Mark a notification as read
   * @param notificationId Notification ID
   * @returns Updated notification
   */
  async markAsRead(notificationId: string) {
    await connectToMongoDB(MONGODB_URI);
    
    return Notification.findByIdAndUpdate(
      notificationId,
      { read: true },
      { new: true }
    );
  }
  
  /**
   * Mark all notifications as read for a user
   * @param tenantId Tenant ID
   * @param userId User ID
   * @returns Update result
   */
  async markAllAsRead(tenantId: string, userId: string) {
    await connectToMongoDB(MONGODB_URI);
    
    return Notification.updateMany(
      { tenantId, userId, read: false },
      { read: true }
    );
  }
  
  /**
   * Delete a notification
   * @param notificationId Notification ID
   * @returns Delete result
   */
  async deleteNotification(notificationId: string) {
    await connectToMongoDB(MONGODB_URI);
    
    return Notification.findByIdAndDelete(notificationId);
  }
  
  /**
   * Delete all notifications for a user
   * @param tenantId Tenant ID
   * @param userId User ID
   * @returns Delete result
   */
  async deleteAllNotifications(tenantId: string, userId: string) {
    await connectToMongoDB(MONGODB_URI);
    
    return Notification.deleteMany({ tenantId, userId });
  }
  
  /**
   * Get unread notification count for a user
   * @param tenantId Tenant ID
   * @param userId User ID
   * @returns Unread notification count
   */
  async getUnreadCount(tenantId: string, userId: string) {
    await connectToMongoDB(MONGODB_URI);
    
    return Notification.countDocuments({
      tenantId,
      userId,
      read: false,
    });
  }
  
  /**
   * Create a system notification for all users in a tenant
   * @param tenantId Tenant ID
   * @param title Notification title
   * @param message Notification message
   * @param type Notification type
   * @param data Additional data (optional)
   * @returns Created notifications
   */
  async createSystemNotification(
    tenantId: string,
    title: string,
    message: string,
    type: 'info' | 'success' | 'warning' | 'error',
    data?: Record<string, any>
  ) {
    await connectToMongoDB(MONGODB_URI);
    
    // In a real application, we would get all users in the tenant
    // For now, we'll just create a notification with a null userId
    const notification = new Notification({
      tenantId,
      userId: null, // Indicates a system-wide notification
      title,
      message,
      type,
      entityType: 'system',
      data,
      read: false,
      createdAt: new Date(),
    });
    
    return notification.save();
  }
}

export default NotificationService;
