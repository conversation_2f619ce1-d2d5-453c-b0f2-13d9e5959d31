import axios from 'axios';
import { API_BASE_URL } from '../config';
import { getAuthToken } from '../utils/auth';

/**
 * Email Tracking Service
 * 
 * This service provides methods for tracking and managing emails related to proposals.
 */
export class EmailTrackingService {
  /**
   * Get email events by proposal
   * @param proposalId Proposal ID
   * @param tenantId Tenant ID
   * @returns Email events
   */
  static async getEmailEventsByProposal(proposalId: string, tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/events/proposal/${proposalId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting email events:', error);
      throw error;
    }
  }
  
  /**
   * Get email events by email
   * @param emailId Email ID
   * @param tenantId Tenant ID
   * @returns Email events
   */
  static async getEmailEventsByEmail(emailId: string, tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/events/email/${emailId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting email events:', error);
      throw error;
    }
  }
  
  /**
   * Get email templates
   * @param tenantId Tenant ID
   * @returns Email templates
   */
  static async getEmailTemplates(tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/templates`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting email templates:', error);
      throw error;
    }
  }
  
  /**
   * Get email template by ID
   * @param templateId Template ID
   * @param tenantId Tenant ID
   * @returns Email template
   */
  static async getEmailTemplateById(templateId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/templates/${templateId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting email template:', error);
      throw error;
    }
  }
  
  /**
   * Create email template
   * @param templateData Template data
   * @param tenantId Tenant ID
   * @returns Created template
   */
  static async createEmailTemplate(templateData: any, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(`${API_BASE_URL}/api/crm/email-tracking/templates`, templateData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error creating email template:', error);
      throw error;
    }
  }
  
  /**
   * Update email template
   * @param templateId Template ID
   * @param templateData Template data
   * @param tenantId Tenant ID
   * @returns Updated template
   */
  static async updateEmailTemplate(templateId: string, templateData: any, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.put(`${API_BASE_URL}/api/crm/email-tracking/templates/${templateId}`, templateData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error updating email template:', error);
      throw error;
    }
  }
  
  /**
   * Delete email template
   * @param templateId Template ID
   * @param tenantId Tenant ID
   * @returns Success status
   */
  static async deleteEmailTemplate(templateId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.delete(`${API_BASE_URL}/api/crm/email-tracking/templates/${templateId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error deleting email template:', error);
      throw error;
    }
  }
  
  /**
   * Send proposal email
   * @param emailData Email data
   * @param tenantId Tenant ID
   * @returns Sent email data
   */
  static async sendProposalEmail(emailData: {
    proposalId: string;
    to: string;
    subject: string;
    message: string;
    templateId?: string;
    trackOpens?: boolean;
    trackClicks?: boolean;
    schedule?: boolean;
    scheduleDate?: string;
  }, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(`${API_BASE_URL}/api/crm/email-tracking/send`, emailData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error sending proposal email:', error);
      throw error;
    }
  }
  
  /**
   * Get email analytics
   * @param tenantId Tenant ID
   * @param startDate Start date (optional)
   * @param endDate End date (optional)
   * @returns Email analytics
   */
  static async getEmailAnalytics(tenantId: string, startDate?: Date, endDate?: Date): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const params: any = {};
      if (startDate) params.startDate = startDate.toISOString();
      if (endDate) params.endDate = endDate.toISOString();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/analytics`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
        params,
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting email analytics:', error);
      throw error;
    }
  }
  
  /**
   * Get proposal email analytics
   * @param proposalId Proposal ID
   * @param tenantId Tenant ID
   * @returns Proposal email analytics
   */
  static async getProposalEmailAnalytics(proposalId: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/analytics/proposal/${proposalId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting proposal email analytics:', error);
      throw error;
    }
  }
  
  /**
   * Get email settings
   * @param tenantId Tenant ID
   * @returns Email settings
   */
  static async getEmailSettings(tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/settings`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting email settings:', error);
      throw error;
    }
  }
  
  /**
   * Update email settings
   * @param settingsData Settings data
   * @param tenantId Tenant ID
   * @returns Updated settings
   */
  static async updateEmailSettings(settingsData: any, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.put(`${API_BASE_URL}/api/crm/email-tracking/settings`, settingsData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error updating email settings:', error);
      throw error;
    }
  }
  
  /**
   * Verify email domain
   * @param domain Domain to verify
   * @param tenantId Tenant ID
   * @returns Verification status
   */
  static async verifyEmailDomain(domain: string, tenantId: string): Promise<any> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.post(`${API_BASE_URL}/api/crm/email-tracking/domains/verify`, { domain }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error verifying email domain:', error);
      throw error;
    }
  }
  
  /**
   * Get email domains
   * @param tenantId Tenant ID
   * @returns Email domains
   */
  static async getEmailDomains(tenantId: string): Promise<any[]> {
    try {
      const token = await getAuthToken();
      
      const response = await axios.get(`${API_BASE_URL}/api/crm/email-tracking/domains`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-Tenant-ID': tenantId,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error getting email domains:', error);
      throw error;
    }
  }
}

export default EmailTrackingService;
