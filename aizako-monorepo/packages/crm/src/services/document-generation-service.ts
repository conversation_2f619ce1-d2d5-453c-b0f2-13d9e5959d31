import { IProposal, ProposalDownloadOptions } from '../types/proposals';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, BorderStyle } from 'docx';
import { formatCurrency } from '../utils/formatters';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { v4 as uuidv4 } from 'uuid';
import { DocumentCacheService } from './document-cache-service';

/**
 * Document Generation Service
 *
 * This service provides methods for generating documents in various formats.
 */
export class DocumentGenerationService {
  /**
   * Generate a PDF document from a proposal
   * @param proposal Proposal data
   * @param options PDF generation options
   * @returns PDF document as a Buffer
   */
  static async generatePDF(proposal: IProposal, options: ProposalDownloadOptions): Promise<Buffer> {
    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();

    // Add a blank page
    let page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);

    // Get the standard font
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Set the font size
    const fontSize = 12;
    const titleFontSize = 24;
    const headingFontSize = 16;

    // Set the margins
    const margin = 50;
    const pageWidth = page.getWidth() - margin * 2;

    // Set the line height
    const lineHeight = fontSize * 1.5;

    // Set the current position
    let y = page.getHeight() - margin;

    // Set the color scheme
    let primaryColor = rgb(0, 0.3, 0.6); // Default blue

    if (options.colorScheme === 'professional') {
      primaryColor = rgb(0, 0.3, 0.6); // Blue
    } else if (options.colorScheme === 'creative') {
      primaryColor = rgb(0.5, 0, 0.5); // Purple
    } else if (options.colorScheme === 'modern') {
      primaryColor = rgb(0, 0.5, 0.5); // Teal
    } else if (options.colorScheme === 'classic') {
      primaryColor = rgb(0.3, 0.3, 0.3); // Gray
    }

    // Add header with logo if requested
    if (options.includeHeader) {
      // Add company name as header
      page.drawText('AIZAKO CRM', {
        x: margin,
        y,
        size: 14,
        font: boldFont,
        color: primaryColor,
      });

      y -= lineHeight * 2;
    }

    // Add title
    page.drawText(proposal.title, {
      x: margin,
      y,
      size: titleFontSize,
      font: boldFont,
      color: primaryColor,
    });

    y -= lineHeight * 2;

    // Add description if available
    if (proposal.description) {
      // Handle text wrapping for description
      const words = proposal.description.split(' ');
      let line = '';

      for (const word of words) {
        const testLine = line + word + ' ';
        const testWidth = font.widthOfTextAtSize(testLine, fontSize);

        if (testWidth > pageWidth) {
          page.drawText(line, {
            x: margin,
            y,
            size: fontSize,
            font,
          });

          line = word + ' ';
          y -= lineHeight;
        } else {
          line = testLine;
        }
      }

      // Draw the last line
      if (line) {
        page.drawText(line, {
          x: margin,
          y,
          size: fontSize,
          font,
        });

        y -= lineHeight * 2;
      }
    }

    // Add sections
    const visibleSections = proposal.sections.filter(section => section.isVisible);

    for (const section of visibleSections) {
      // Check if we need a new page
      if (y < margin * 2) {
        page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
        y = page.getHeight() - margin;
      }

      // Add section title
      page.drawText(section.title, {
        x: margin,
        y,
        size: headingFontSize,
        font: boldFont,
        color: primaryColor,
      });

      y -= lineHeight * 1.5;

      // Add section content with text wrapping
      const contentLines = section.content.split('\n');

      for (const contentLine of contentLines) {
        const words = contentLine.split(' ');
        let line = '';

        for (const word of words) {
          const testLine = line + word + ' ';
          const testWidth = font.widthOfTextAtSize(testLine, fontSize);

          if (testWidth > pageWidth) {
            page.drawText(line, {
              x: margin,
              y,
              size: fontSize,
              font,
            });

            line = word + ' ';
            y -= lineHeight;

            // Check if we need a new page
            if (y < margin * 2) {
              page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
              y = page.getHeight() - margin;
            }
          } else {
            line = testLine;
          }
        }

        // Draw the last line
        if (line) {
          page.drawText(line, {
            x: margin,
            y,
            size: fontSize,
            font,
          });

          y -= lineHeight;

          // Check if we need a new page
          if (y < margin * 2) {
            page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
            y = page.getHeight() - margin;
          }
        }
      }

      y -= lineHeight;
    }

    // Add pricing if available
    if (proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0) {
      // Check if we need a new page
      if (y < margin * 4) {
        page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
        y = page.getHeight() - margin;
      }

      // Add pricing title
      page.drawText('Pricing', {
        x: margin,
        y,
        size: headingFontSize,
        font: boldFont,
        color: primaryColor,
      });

      y -= lineHeight * 1.5;

      // Add pricing table headers
      const columnWidths = [pageWidth * 0.4, pageWidth * 0.2, pageWidth * 0.2, pageWidth * 0.2];

      page.drawText('Item', {
        x: margin,
        y,
        size: fontSize,
        font: boldFont,
      });

      page.drawText('Quantity', {
        x: margin + columnWidths[0],
        y,
        size: fontSize,
        font: boldFont,
      });

      page.drawText('Unit Price', {
        x: margin + columnWidths[0] + columnWidths[1],
        y,
        size: fontSize,
        font: boldFont,
      });

      page.drawText('Total', {
        x: margin + columnWidths[0] + columnWidths[1] + columnWidths[2],
        y,
        size: fontSize,
        font: boldFont,
      });

      y -= lineHeight;

      // Add pricing items
      for (const item of proposal.pricing.items) {
        // Check if we need a new page
        if (y < margin * 2) {
          page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
          y = page.getHeight() - margin;

          // Add pricing table headers again
          page.drawText('Item', {
            x: margin,
            y,
            size: fontSize,
            font: boldFont,
          });

          page.drawText('Quantity', {
            x: margin + columnWidths[0],
            y,
            size: fontSize,
            font: boldFont,
          });

          page.drawText('Unit Price', {
            x: margin + columnWidths[0] + columnWidths[1],
            y,
            size: fontSize,
            font: boldFont,
          });

          page.drawText('Total', {
            x: margin + columnWidths[0] + columnWidths[1] + columnWidths[2],
            y,
            size: fontSize,
            font: boldFont,
          });

          y -= lineHeight;
        }

        // Add item name
        page.drawText(item.name, {
          x: margin,
          y,
          size: fontSize,
          font,
        });

        // Add item quantity
        page.drawText(item.quantity.toString(), {
          x: margin + columnWidths[0],
          y,
          size: fontSize,
          font,
        });

        // Add item unit price
        page.drawText(formatCurrency(item.unitPrice, proposal.pricing.currency), {
          x: margin + columnWidths[0] + columnWidths[1],
          y,
          size: fontSize,
          font,
        });

        // Add item total
        page.drawText(formatCurrency(item.total, proposal.pricing.currency), {
          x: margin + columnWidths[0] + columnWidths[1] + columnWidths[2],
          y,
          size: fontSize,
          font,
        });

        y -= lineHeight;

        // Add item description if available
        if (item.description) {
          page.drawText(item.description, {
            x: margin + 10,
            y,
            size: fontSize - 2,
            font,
            color: rgb(0.5, 0.5, 0.5),
          });

          y -= lineHeight;
        }
      }

      // Add subtotal, discount, tax, and total
      y -= lineHeight;

      // Check if we need a new page
      if (y < margin * 4) {
        page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
        y = page.getHeight() - margin;
      }

      // Add subtotal
      page.drawText('Subtotal:', {
        x: margin + columnWidths[0] + columnWidths[1],
        y,
        size: fontSize,
        font: boldFont,
      });

      page.drawText(formatCurrency(proposal.pricing.subtotal, proposal.pricing.currency), {
        x: margin + columnWidths[0] + columnWidths[1] + columnWidths[2],
        y,
        size: fontSize,
        font,
      });

      y -= lineHeight;

      // Add discount if available
      if (proposal.pricing.discount && proposal.pricing.discount > 0) {
        page.drawText('Discount:', {
          x: margin + columnWidths[0] + columnWidths[1],
          y,
          size: fontSize,
          font: boldFont,
        });

        page.drawText(formatCurrency(proposal.pricing.discount, proposal.pricing.currency), {
          x: margin + columnWidths[0] + columnWidths[1] + columnWidths[2],
          y,
          size: fontSize,
          font,
        });

        y -= lineHeight;
      }

      // Add tax if available
      if (proposal.pricing.tax && proposal.pricing.tax > 0) {
        page.drawText('Tax:', {
          x: margin + columnWidths[0] + columnWidths[1],
          y,
          size: fontSize,
          font: boldFont,
        });

        page.drawText(formatCurrency(proposal.pricing.tax, proposal.pricing.currency), {
          x: margin + columnWidths[0] + columnWidths[1] + columnWidths[2],
          y,
          size: fontSize,
          font,
        });

        y -= lineHeight;
      }

      // Add total
      page.drawText('Total:', {
        x: margin + columnWidths[0] + columnWidths[1],
        y,
        size: fontSize,
        font: boldFont,
      });

      page.drawText(formatCurrency(proposal.pricing.total, proposal.pricing.currency), {
        x: margin + columnWidths[0] + columnWidths[1] + columnWidths[2],
        y,
        size: fontSize,
        font: boldFont,
      });

      y -= lineHeight * 2;
    }

    // Add terms if available
    if (proposal.terms) {
      // Check if we need a new page
      if (y < margin * 4) {
        page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
        y = page.getHeight() - margin;
      }

      // Add terms title
      page.drawText('Terms & Conditions', {
        x: margin,
        y,
        size: headingFontSize,
        font: boldFont,
        color: primaryColor,
      });

      y -= lineHeight * 1.5;

      // Add terms content with text wrapping
      const termsLines = proposal.terms.split('\n');

      for (const termsLine of termsLines) {
        const words = termsLine.split(' ');
        let line = '';

        for (const word of words) {
          const testLine = line + word + ' ';
          const testWidth = font.widthOfTextAtSize(testLine, fontSize);

          if (testWidth > pageWidth) {
            page.drawText(line, {
              x: margin,
              y,
              size: fontSize,
              font,
            });

            line = word + ' ';
            y -= lineHeight;

            // Check if we need a new page
            if (y < margin * 2) {
              page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
              y = page.getHeight() - margin;
            }
          } else {
            line = testLine;
          }
        }

        // Draw the last line
        if (line) {
          page.drawText(line, {
            x: margin,
            y,
            size: fontSize,
            font,
          });

          y -= lineHeight;

          // Check if we need a new page
          if (y < margin * 2) {
            page = pdfDoc.addPage([options.paperSize === 'letter' ? 612 : 595, options.paperSize === 'legal' ? 1008 : options.paperSize === 'letter' ? 792 : 842]);
            y = page.getHeight() - margin;
          }
        }
      }
    }

    // Add footer if requested
    if (options.includeFooter) {
      // Add page numbers if requested
      if (options.includePageNumbers) {
        for (let i = 0; i < pdfDoc.getPageCount(); i++) {
          const page = pdfDoc.getPage(i);

          page.drawText(`Page ${i + 1} of ${pdfDoc.getPageCount()}`, {
            x: page.getWidth() / 2 - 40,
            y: margin / 2,
            size: 10,
            font,
          });
        }
      }

      // Add footer text
      page.drawText('Confidential - For recipient use only', {
        x: margin,
        y: margin / 2,
        size: 10,
        font,
      });

      // Add date
      const date = new Date().toLocaleDateString();

      page.drawText(date, {
        x: page.getWidth() - margin - 80,
        y: margin / 2,
        size: 10,
        font,
      });
    }

    // Serialize the PDF to bytes
    const pdfBytes = await pdfDoc.save();

    return Buffer.from(pdfBytes);
  }

  /**
   * Generate a DOCX document from a proposal
   * @param proposal Proposal data
   * @param options DOCX generation options
   * @returns DOCX document as a Buffer
   */
  static async generateDOCX(proposal: IProposal, options: ProposalDownloadOptions): Promise<Buffer> {
    // Create document sections
    const sections = [];

    // Add title
    sections.push(
      new Paragraph({
        text: proposal.title,
        heading: HeadingLevel.TITLE,
      })
    );

    // Add description if available
    if (proposal.description) {
      sections.push(
        new Paragraph({
          text: proposal.description,
          spacing: {
            after: 200,
          },
        })
      );
    }

    // Add sections
    const visibleSections = proposal.sections.filter(section => section.isVisible);

    for (const section of visibleSections) {
      // Add section title
      sections.push(
        new Paragraph({
          text: section.title,
          heading: HeadingLevel.HEADING_1,
          spacing: {
            before: 400,
            after: 200,
          },
        })
      );

      // Add section content
      const contentLines = section.content.split('\n');

      for (const contentLine of contentLines) {
        sections.push(
          new Paragraph({
            text: contentLine,
          })
        );
      }
    }

    // Add pricing if available
    if (proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0) {
      // Add pricing title
      sections.push(
        new Paragraph({
          text: 'Pricing',
          heading: HeadingLevel.HEADING_1,
          spacing: {
            before: 400,
            after: 200,
          },
        })
      );

      // Create pricing table
      const table = new Table({
        width: {
          size: 100,
          type: 'pct',
        },
        borders: {
          top: {
            style: BorderStyle.SINGLE,
            size: 1,
          },
          bottom: {
            style: BorderStyle.SINGLE,
            size: 1,
          },
          left: {
            style: BorderStyle.SINGLE,
            size: 1,
          },
          right: {
            style: BorderStyle.SINGLE,
            size: 1,
          },
          insideHorizontal: {
            style: BorderStyle.SINGLE,
            size: 1,
          },
          insideVertical: {
            style: BorderStyle.SINGLE,
            size: 1,
          },
        },
        rows: [
          // Header row
          new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph('Item')],
                width: {
                  size: 40,
                  type: 'pct',
                },
              }),
              new TableCell({
                children: [new Paragraph('Quantity')],
                width: {
                  size: 20,
                  type: 'pct',
                },
              }),
              new TableCell({
                children: [new Paragraph('Unit Price')],
                width: {
                  size: 20,
                  type: 'pct',
                },
              }),
              new TableCell({
                children: [new Paragraph('Total')],
                width: {
                  size: 20,
                  type: 'pct',
                },
              }),
            ],
          }),
          // Item rows
          ...proposal.pricing.items.map(
            item =>
              new TableRow({
                children: [
                  new TableCell({
                    children: [
                      new Paragraph(item.name),
                      ...(item.description
                        ? [new Paragraph({ text: item.description, style: 'smallText' })]
                        : []),
                    ],
                  }),
                  new TableCell({
                    children: [new Paragraph(item.quantity.toString())],
                  }),
                  new TableCell({
                    children: [
                      new Paragraph(
                        formatCurrency(item.unitPrice, proposal.pricing.currency)
                      ),
                    ],
                  }),
                  new TableCell({
                    children: [
                      new Paragraph(
                        formatCurrency(item.total, proposal.pricing.currency)
                      ),
                    ],
                  }),
                ],
              })
          ),
        ],
      });

      sections.push(table);

      // Add subtotal, discount, tax, and total
      sections.push(
        new Paragraph({
          text: '',
          spacing: {
            after: 200,
          },
        })
      );

      sections.push(
        new Paragraph({
          children: [
            new TextRun({
              text: 'Subtotal: ',
              bold: true,
            }),
            new TextRun(
              formatCurrency(proposal.pricing.subtotal, proposal.pricing.currency)
            ),
          ],
          alignment: 'right',
        })
      );

      if (proposal.pricing.discount && proposal.pricing.discount > 0) {
        sections.push(
          new Paragraph({
            children: [
              new TextRun({
                text: 'Discount: ',
                bold: true,
              }),
              new TextRun(
                formatCurrency(proposal.pricing.discount, proposal.pricing.currency)
              ),
            ],
            alignment: 'right',
          })
        );
      }

      if (proposal.pricing.tax && proposal.pricing.tax > 0) {
        sections.push(
          new Paragraph({
            children: [
              new TextRun({
                text: 'Tax: ',
                bold: true,
              }),
              new TextRun(
                formatCurrency(proposal.pricing.tax, proposal.pricing.currency)
              ),
            ],
            alignment: 'right',
          })
        );
      }

      sections.push(
        new Paragraph({
          children: [
            new TextRun({
              text: 'Total: ',
              bold: true,
            }),
            new TextRun({
              text: formatCurrency(proposal.pricing.total, proposal.pricing.currency),
              bold: true,
            }),
          ],
          alignment: 'right',
        })
      );
    }

    // Add terms if available
    if (proposal.terms) {
      // Add terms title
      sections.push(
        new Paragraph({
          text: 'Terms & Conditions',
          heading: HeadingLevel.HEADING_1,
          spacing: {
            before: 400,
            after: 200,
          },
        })
      );

      // Add terms content
      const termsLines = proposal.terms.split('\n');

      for (const termsLine of termsLines) {
        sections.push(
          new Paragraph({
            text: termsLine,
          })
        );
      }
    }

    // Create the document
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: sections,
        },
      ],
    });

    // Generate the document
    const buffer = await Packer.toBuffer(doc);

    return buffer;
  }

  /**
   * Generate a Markdown document from a proposal
   * @param proposal Proposal data
   * @param options Markdown generation options
   * @returns Markdown document as a string
   */
  static generateMarkdown(proposal: IProposal, options: ProposalDownloadOptions): string {
    let markdown = '';

    // Add title
    markdown += `# ${proposal.title}\n\n`;

    // Add description if available
    if (proposal.description) {
      markdown += `${proposal.description}\n\n`;
    }

    // Add sections
    const visibleSections = proposal.sections.filter(section => section.isVisible);

    for (const section of visibleSections) {
      // Add section title
      markdown += `## ${section.title}\n\n`;

      // Add section content
      markdown += `${section.content}\n\n`;
    }

    // Add pricing if available
    if (proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0) {
      // Add pricing title
      markdown += `## Pricing\n\n`;

      // Add pricing table
      markdown += `| Item | Quantity | Unit Price | Total |\n`;
      markdown += `| ---- | -------- | ---------- | ----- |\n`;

      for (const item of proposal.pricing.items) {
        markdown += `| ${item.name}${
          item.description ? ` <br> *${item.description}*` : ''
        } | ${item.quantity} | ${formatCurrency(
          item.unitPrice,
          proposal.pricing.currency
        )} | ${formatCurrency(item.total, proposal.pricing.currency)} |\n`;
      }

      markdown += `\n`;

      // Add subtotal, discount, tax, and total
      markdown += `**Subtotal:** ${formatCurrency(
        proposal.pricing.subtotal,
        proposal.pricing.currency
      )}\n\n`;

      if (proposal.pricing.discount && proposal.pricing.discount > 0) {
        markdown += `**Discount:** ${formatCurrency(
          proposal.pricing.discount,
          proposal.pricing.currency
        )}\n\n`;
      }

      if (proposal.pricing.tax && proposal.pricing.tax > 0) {
        markdown += `**Tax:** ${formatCurrency(
          proposal.pricing.tax,
          proposal.pricing.currency
        )}\n\n`;
      }

      markdown += `**Total:** ${formatCurrency(
        proposal.pricing.total,
        proposal.pricing.currency
      )}\n\n`;
    }

    // Add terms if available
    if (proposal.terms) {
      // Add terms title
      markdown += `## Terms & Conditions\n\n`;

      // Add terms content
      markdown += `${proposal.terms}\n\n`;
    }

    // Add footer if requested
    if (options.includeFooter) {
      markdown += `---\n\n`;
      markdown += `*Confidential - For recipient use only*\n\n`;
      markdown += `*Generated on ${new Date().toLocaleDateString()}*\n`;
    }

    return markdown;
  }

  /**
   * Generate a document from a proposal
   * @param proposal Proposal data
   * @param options Document generation options
   * @param tenantId Tenant ID (for caching)
   * @param skipCache Whether to skip the cache
   * @returns Document as a Buffer or string
   */
  static async generateDocument(
    proposal: IProposal,
    options: ProposalDownloadOptions,
    tenantId?: string,
    skipCache: boolean = false
  ): Promise<Buffer | string> {
    // Check cache if tenantId is provided and skipCache is false
    if (tenantId && !skipCache) {
      try {
        const cachedDocument = await DocumentCacheService.getCache(proposal, options, tenantId);

        if (cachedDocument) {
          console.log(`Using cached ${options.format} document for proposal ${proposal._id}`);
          return cachedDocument;
        }
      } catch (error) {
        console.error('Error checking document cache:', error);
        // Continue with generation if cache check fails
      }
    }

    // Generate the document
    let document: Buffer | string;

    switch (options.format) {
      case 'pdf':
        document = await this.generatePDF(proposal, options);
        break;
      case 'docx':
        document = await this.generateDOCX(proposal, options);
        break;
      case 'md':
        document = this.generateMarkdown(proposal, options);
        break;
      default:
        throw new Error(`Unsupported format: ${options.format}`);
    }

    // Cache the document if tenantId is provided and skipCache is false
    if (tenantId && !skipCache) {
      try {
        await DocumentCacheService.setCache(proposal, options, document, tenantId);
        console.log(`Cached ${options.format} document for proposal ${proposal._id}`);
      } catch (error) {
        console.error('Error caching document:', error);
        // Continue even if caching fails
      }
    }

    return document;
  }
}
