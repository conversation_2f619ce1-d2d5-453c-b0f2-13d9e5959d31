import mongoose from 'mongoose';
import { ProposalVersion, IProposalVersion } from '../models/proposal-version';
import { IProposal } from '../models/proposal';
import { deepCompare } from '../utils/object-utils';

/**
 * Version History Service
 * 
 * This service provides version history tracking for proposals.
 */
export class VersionHistoryService {
  /**
   * Create a new version
   * @param proposal Current proposal state
   * @param previousProposal Previous proposal state (optional)
   * @param userId User ID of the person making the change
   * @param comment Optional comment about the change
   * @returns Created version
   */
  static async createVersion(
    proposal: IProposal,
    previousProposal: Partial<IProposal> | null,
    userId: string,
    comment?: string
  ): Promise<IProposalVersion> {
    try {
      // Get the latest version number
      const latestVersion = await ProposalVersion.findOne({
        proposalId: proposal._id,
        tenantId: proposal.tenantId,
      })
        .sort({ versionNumber: -1 })
        .select('versionNumber')
        .lean();
      
      const versionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;
      
      // Create a snapshot of the current proposal
      const snapshot = this.createSnapshot(proposal);
      
      // Calculate changes if previous proposal is provided
      const changes = previousProposal
        ? this.calculateChanges(previousProposal, proposal)
        : [{ field: 'all', oldValue: null, newValue: snapshot, type: 'add' }];
      
      // Create the version
      const version = await ProposalVersion.create({
        tenantId: proposal.tenantId,
        proposalId: proposal._id,
        versionNumber,
        snapshot,
        changes,
        createdBy: new mongoose.Types.ObjectId(userId),
        comment,
      });
      
      return version;
    } catch (error) {
      console.error('Error creating version:', error);
      throw error;
    }
  }
  
  /**
   * Get versions for a proposal
   * @param proposalId Proposal ID
   * @param tenantId Tenant ID
   * @param limit Maximum number of versions to return (default: 10)
   * @param skip Number of versions to skip (default: 0)
   * @returns Array of versions
   */
  static async getVersions(
    proposalId: string,
    tenantId: string,
    limit: number = 10,
    skip: number = 0
  ): Promise<IProposalVersion[]> {
    try {
      return await ProposalVersion.find({
        proposalId: new mongoose.Types.ObjectId(proposalId),
        tenantId,
      })
        .sort({ versionNumber: -1 })
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'name email')
        .lean();
    } catch (error) {
      console.error('Error getting versions:', error);
      throw error;
    }
  }
  
  /**
   * Get a specific version
   * @param proposalId Proposal ID
   * @param versionNumber Version number
   * @param tenantId Tenant ID
   * @returns Version or null if not found
   */
  static async getVersion(
    proposalId: string,
    versionNumber: number,
    tenantId: string
  ): Promise<IProposalVersion | null> {
    try {
      return await ProposalVersion.findOne({
        proposalId: new mongoose.Types.ObjectId(proposalId),
        versionNumber,
        tenantId,
      })
        .populate('createdBy', 'name email')
        .lean();
    } catch (error) {
      console.error('Error getting version:', error);
      throw error;
    }
  }
  
  /**
   * Create a snapshot of a proposal
   * @param proposal Proposal to snapshot
   * @returns Proposal snapshot
   */
  private static createSnapshot(proposal: IProposal): Partial<IProposal> {
    // Create a clean snapshot without unnecessary fields
    const {
      _id,
      tenantId,
      title,
      description,
      status,
      sections,
      pricing,
      terms,
      opportunityId,
      companyId,
      contactIds,
      owner,
      publicToken,
      publicUrl,
      publicAccessEnabled,
      downloadEnabled,
      downloadFormats,
      tags,
      aiGenerated,
      aiPrompt,
      aiModel,
      updatedAt,
    } = proposal;
    
    return {
      _id,
      tenantId,
      title,
      description,
      status,
      sections,
      pricing,
      terms,
      opportunityId,
      companyId,
      contactIds,
      owner,
      publicToken,
      publicUrl,
      publicAccessEnabled,
      downloadEnabled,
      downloadFormats,
      tags,
      aiGenerated,
      aiPrompt,
      aiModel,
      updatedAt,
    };
  }
  
  /**
   * Calculate changes between two proposal states
   * @param oldProposal Previous proposal state
   * @param newProposal Current proposal state
   * @returns Array of changes
   */
  private static calculateChanges(
    oldProposal: Partial<IProposal>,
    newProposal: IProposal
  ): Array<{
    field: string;
    oldValue: any;
    newValue: any;
    type: 'add' | 'update' | 'delete';
  }> {
    const changes: Array<{
      field: string;
      oldValue: any;
      newValue: any;
      type: 'add' | 'update' | 'delete';
    }> = [];
    
    // Compare basic fields
    const basicFields = [
      'title',
      'description',
      'status',
      'terms',
      'publicAccessEnabled',
      'downloadEnabled',
    ];
    
    for (const field of basicFields) {
      if (oldProposal[field] !== newProposal[field]) {
        changes.push({
          field,
          oldValue: oldProposal[field],
          newValue: newProposal[field],
          type: 'update',
        });
      }
    }
    
    // Compare sections
    if (oldProposal.sections && newProposal.sections) {
      // Check for added or updated sections
      for (const newSection of newProposal.sections) {
        const oldSection = oldProposal.sections.find(s => s.id === newSection.id);
        
        if (!oldSection) {
          // Section was added
          changes.push({
            field: `sections.${newSection.id}`,
            oldValue: null,
            newValue: newSection,
            type: 'add',
          });
        } else if (!deepCompare(oldSection, newSection)) {
          // Section was updated
          changes.push({
            field: `sections.${newSection.id}`,
            oldValue: oldSection,
            newValue: newSection,
            type: 'update',
          });
        }
      }
      
      // Check for deleted sections
      for (const oldSection of oldProposal.sections) {
        const newSection = newProposal.sections.find(s => s.id === oldSection.id);
        
        if (!newSection) {
          // Section was deleted
          changes.push({
            field: `sections.${oldSection.id}`,
            oldValue: oldSection,
            newValue: null,
            type: 'delete',
          });
        }
      }
    } else if (newProposal.sections && !oldProposal.sections) {
      // All sections were added
      changes.push({
        field: 'sections',
        oldValue: null,
        newValue: newProposal.sections,
        type: 'add',
      });
    } else if (!newProposal.sections && oldProposal.sections) {
      // All sections were deleted
      changes.push({
        field: 'sections',
        oldValue: oldProposal.sections,
        newValue: null,
        type: 'delete',
      });
    }
    
    // Compare pricing
    if (!deepCompare(oldProposal.pricing, newProposal.pricing)) {
      changes.push({
        field: 'pricing',
        oldValue: oldProposal.pricing,
        newValue: newProposal.pricing,
        type: oldProposal.pricing ? 'update' : 'add',
      });
    }
    
    return changes;
  }
}

export default VersionHistoryService;
