// Export all CRM services
export * from './contact-service';
export * from './company-service';
export * from './opportunity-service';
// export * from './activity-service';
export * from './proposal-service';
export * from './task-service';
// export * from './document-service';
// export * from './email-template-service';
export * from './email-tracking-service';
export * from './notification-service';
export * from './background-task-service';
export * from './ai-service';
export * from './document-generation-service';
// export * from './sequence-service';
// export * from './workflow-service';
// export * from './tenant-domain-service';
// export * from './tag-service';

// Import service classes
import { ContactService } from './contact-service';
// import { CompanyService } from './company-service';
// import { OpportunityService } from './opportunity-service';
// import { ProposalService } from './proposal-service';
// import { TaskService } from './task-service';
// import { EmailTrackingService } from './email-tracking-service';
// import { NotificationService } from './notification-service';
// import { AiService } from './ai-service';
// import { DocumentGenerationService } from './document-generation-service';

// Create service instances
export const contactService = new ContactService();
// export const companyService = new CompanyService();
// export const opportunityService = new OpportunityService();
// export const proposalService = new ProposalService();
// export const taskService = new TaskService();
// export const emailTrackingService = new EmailTrackingService();
// export const notificationService = new NotificationService();
// export const aiService = new AiService();
// export const documentGenerationService = new DocumentGenerationService();

// Export default services object
export default {
  contactService,
  // companyService,
  // opportunityService,
  // proposalService,
  // taskService,
  // emailTrackingService,
  // notificationService,
  // aiService,
  // documentGenerationService,
};
