// API configuration
export const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';

// MongoDB configuration
export const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/aizako-crm';

// Authentication configuration
export const AUTH_SECRET = process.env.AUTH_SECRET || 'your-auth-secret';
export const JWT_EXPIRATION = process.env.JWT_EXPIRATION || '1d';

// Email configuration
export const EMAIL_FROM = process.env.EMAIL_FROM || '<EMAIL>';
export const EMAIL_REPLY_TO = process.env.EMAIL_REPLY_TO || '<EMAIL>';

// Resend configuration
export const RESEND_API_KEY = process.env.RESEND_API_KEY || '';

// AI service configuration
export const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:3001';
export const ANTHROPIC_API_KEY = process.env.ANTHROPIC_API_KEY || '';

// Neo4j configuration
export const NEO4J_URI = process.env.NEO4J_URI || 'bolt://localhost:7687';
export const NEO4J_USERNAME = process.env.NEO4J_USERNAME || 'neo4j';
export const NEO4J_PASSWORD = process.env.NEO4J_PASSWORD || 'password';

// Redis configuration
export const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';

// Rate limiting configuration
export const RATE_LIMIT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000', 10);
export const RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);

// File storage configuration
export const STORAGE_BUCKET = process.env.STORAGE_BUCKET || 'aizako-crm-files';

// Logging configuration
export const LOG_LEVEL = process.env.LOG_LEVEL || 'info';

// Feature flags
export const FEATURE_FLAGS = {
  enableAI: process.env.ENABLE_AI === 'true',
  enableNeo4j: process.env.ENABLE_NEO4J === 'true',
  enableEmailTracking: process.env.ENABLE_EMAIL_TRACKING === 'true',
  enableAdvancedAnalytics: process.env.ENABLE_ADVANCED_ANALYTICS === 'true',
};

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_PAGE_SIZE = 100;
