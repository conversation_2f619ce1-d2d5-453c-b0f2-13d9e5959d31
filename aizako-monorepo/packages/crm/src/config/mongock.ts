/**
 * Mongock configuration for database migrations
 */
import { MONGODB_URI } from '../config';

/**
 * Mongock configuration
 */
export const mongockConfig = {
  /**
   * MongoDB connection URI
   */
  mongoUri: MONGODB_URI,

  /**
   * Migration package path
   * This is where Mongock will look for migration classes
   */
  migrationPackage: 'aizako-monorepo.packages.crm.src.migrations',

  /**
   * Lock acquisition timeout in milliseconds
   * How long <PERSON>go<PERSON> will wait to acquire a lock before failing
   */
  lockAcquiredForMinutes: 5,

  /**
   * Maximum wait time for lock in milliseconds
   * How long Mongock will wait if the lock is held by another process
   */
  maxWaitingForLockMinutes: 3,

  /**
   * Migration collection name
   * Where Mongock will store migration metadata
   */
  migrationCollectionName: 'mongockChangeLog',

  /**
   * Lock collection name
   * Where Mongo<PERSON> will store lock information
   */
  lockCollectionName: 'mongockLock',

  /**
   * Start system version
   * Migrations with lower versions will be ignored
   */
  startSystemVersion: '0',

  /**
   * End system version
   * Migrations with higher versions will be ignored
   */
  endSystemVersion: '999',

  /**
   * Throw exception if migration fails
   */
  throwExceptionIfCannotObtainLock: true,

  /**
   * Track ignored migrations
   */
  trackIgnored: false,

  /**
   * Enable transaction support
   * Set to true if MongoDB is running as a replica set
   */
  transactionEnabled: false,
};

export default mongockConfig;
