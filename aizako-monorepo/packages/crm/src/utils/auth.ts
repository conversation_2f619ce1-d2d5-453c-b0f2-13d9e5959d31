import { API_BASE_URL } from '../config';

/**
 * Get the authentication token from local storage
 * @returns The authentication token or null if not found
 */
export const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }
  
  return localStorage.getItem('authToken');
};

/**
 * Set the authentication token in local storage
 * @param token The authentication token
 */
export const setAuthToken = (token: string): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  localStorage.setItem('authToken', token);
};

/**
 * Remove the authentication token from local storage
 */
export const removeAuthToken = (): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  localStorage.removeItem('authToken');
};

/**
 * Get the current user ID from local storage
 * @returns The user ID or null if not found
 */
export const getUserId = (): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }
  
  return localStorage.getItem('userId');
};

/**
 * Set the current user ID in local storage
 * @param userId The user ID
 */
export const setUserId = (userId: string): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  localStorage.setItem('userId', userId);
};

/**
 * Get the current tenant ID from local storage
 * @returns The tenant ID or null if not found
 */
export const getTenantId = (): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }
  
  return localStorage.getItem('tenantId');
};

/**
 * Set the current tenant ID in local storage
 * @param tenantId The tenant ID
 */
export const setTenantId = (tenantId: string): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  localStorage.setItem('tenantId', tenantId);
};

/**
 * Check if the user is authenticated
 * @returns True if the user is authenticated, false otherwise
 */
export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

/**
 * Create headers with authentication token and tenant ID
 * @returns Headers object with authentication token and tenant ID
 */
export const createAuthHeaders = (): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  const token = getAuthToken();
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const tenantId = getTenantId();
  if (tenantId) {
    headers['x-tenant-id'] = tenantId;
  }

  return headers;
};
