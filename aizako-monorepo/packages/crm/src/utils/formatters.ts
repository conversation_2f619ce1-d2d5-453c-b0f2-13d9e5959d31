/**
 * Format a number as currency
 * @param amount Amount to format
 * @param currency Currency code (default: USD)
 * @param locale Locale (default: en-US)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Format a date
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @param locale Locale (default: en-US)
 * @returns Formatted date string
 */
export function formatDate(
  date: Date | string | number | undefined,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  },
  locale: string = 'en-US'
): string {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' || typeof date === 'number'
    ? new Date(date)
    : date;
  
  return new Intl.DateTimeFormat(locale, options).format(dateObj);
}

/**
 * Format a number
 * @param value Number to format
 * @param options Intl.NumberFormatOptions
 * @param locale Locale (default: en-US)
 * @returns Formatted number string
 */
export function formatNumber(
  value: number,
  options: Intl.NumberFormatOptions = {},
  locale: string = 'en-US'
): string {
  return new Intl.NumberFormat(locale, options).format(value);
}

/**
 * Format a percentage
 * @param value Value to format as percentage (0-1)
 * @param decimals Number of decimal places
 * @param locale Locale (default: en-US)
 * @returns Formatted percentage string
 */
export function formatPercentage(
  value: number,
  decimals: number = 1,
  locale: string = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

/**
 * Format a phone number
 * @param phoneNumber Phone number to format
 * @param format Format pattern (default: (xxx) xxx-xxxx)
 * @returns Formatted phone number
 */
export function formatPhoneNumber(
  phoneNumber: string,
  format: string = '(xxx) xxx-xxxx'
): string {
  // Remove all non-numeric characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if the input is valid
  if (cleaned.length === 0) {
    return '';
  }
  
  // Replace x with digits from the cleaned number
  let formatted = format;
  let digitIndex = 0;
  
  for (let i = 0; i < formatted.length; i++) {
    if (formatted[i] === 'x') {
      if (digitIndex < cleaned.length) {
        formatted = formatted.substring(0, i) + cleaned[digitIndex] + formatted.substring(i + 1);
        digitIndex++;
      } else {
        // If we run out of digits, replace remaining x's with placeholder
        formatted = formatted.substring(0, i) + '_' + formatted.substring(i + 1);
      }
    }
  }
  
  return formatted;
}

/**
 * Format file size
 * @param bytes Size in bytes
 * @param decimals Number of decimal places
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Truncate text with ellipsis
 * @param text Text to truncate
 * @param maxLength Maximum length
 * @param ellipsis Ellipsis string (default: ...)
 * @returns Truncated text
 */
export function truncateText(
  text: string,
  maxLength: number,
  ellipsis: string = '...'
): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - ellipsis.length) + ellipsis;
}

/**
 * Format a name (first + last)
 * @param firstName First name
 * @param lastName Last name
 * @returns Formatted name
 */
export function formatName(firstName?: string, lastName?: string): string {
  if (!firstName && !lastName) return '';
  if (!firstName) return lastName || '';
  if (!lastName) return firstName;
  
  return `${firstName} ${lastName}`;
}

/**
 * Format an address
 * @param address Address object
 * @param singleLine Whether to format as a single line
 * @returns Formatted address
 */
export function formatAddress(
  address: {
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  },
  singleLine: boolean = false
): string {
  const { street1, street2, city, state, postalCode, country } = address;
  
  if (!street1 && !city) return '';
  
  const parts = [
    street1,
    street2,
    city && state ? `${city}, ${state}` : city || state,
    postalCode,
    country,
  ].filter(Boolean);
  
  return singleLine ? parts.join(', ') : parts.join('\n');
}
