import { initializeApp, getApps, getApp } from 'firebase/app';
import {
  getAuth,
  signInWithEmailAndPassword as firebaseSignInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  createUserWithEmailAndPassword as firebaseCreateUserWithEmailAndPassword,
  onAuthStateChanged,
  User as FirebaseUser,
  getIdToken as firebaseGetIdToken
} from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration
// This should be replaced with environment variables in a real application
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY || '',
  authDomain: process.env.FIREBASE_AUTH_DOMAIN || '',
  projectId: process.env.FIREBASE_PROJECT_ID || '',
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET || '',
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
  appId: process.env.FIREBASE_APP_ID || '',
};

// Initialize Firebase
const app = getApps().length > 0 ? getApp() : initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const googleProvider = new GoogleAuthProvider();

// Configure Google provider
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

/**
 * Sign in with email and password
 * @param email User email
 * @param password User password
 * @returns Firebase user credential
 */
export const signInWithEmailAndPassword = async (email: string, password: string) => {
  return firebaseSignInWithEmailAndPassword(auth, email, password);
};

/**
 * Sign in with Google
 * @returns Firebase user credential
 */
export const signInWithGoogle = async () => {
  return signInWithPopup(auth, googleProvider);
};

/**
 * Create a new user with email and password
 * @param email User email
 * @param password User password
 * @returns Firebase user credential
 */
export const createUserWithEmailAndPassword = async (email: string, password: string) => {
  return firebaseCreateUserWithEmailAndPassword(auth, email, password);
};

/**
 * Sign out the current user
 */
export const signOut = async () => {
  return firebaseSignOut(auth);
};

/**
 * Get the current user
 * @returns Current Firebase user or null if not signed in
 */
export const getCurrentUser = (): Promise<FirebaseUser | null> => {
  return new Promise((resolve, reject) => {
    const unsubscribe = onAuthStateChanged(
      auth,
      (user) => {
        unsubscribe();
        resolve(user);
      },
      reject
    );
  });
};

/**
 * Get ID token for the current user
 * @param forceRefresh Force refresh the token
 * @returns ID token or null if not signed in
 */
export const getIdToken = async (forceRefresh = false): Promise<string | null> => {
  const user = auth.currentUser;
  if (!user) {
    return null;
  }
  
  return firebaseGetIdToken(user, forceRefresh);
};

/**
 * Listen for auth state changes
 * @param callback Callback function to handle auth state changes
 * @returns Unsubscribe function
 */
export const onAuthStateChange = (callback: (user: FirebaseUser | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

export { auth, db, app };
