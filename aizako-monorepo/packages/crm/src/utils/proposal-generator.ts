import { IProposal } from '../models/proposal';

/**
 * PDF generation options
 */
export interface PDFGenerationOptions {
  includeHeader: boolean;
  includeFooter: boolean;
  includeBranding: boolean;
  includePageNumbers: boolean;
  colorScheme: string;
  paperSize: string;
}

/**
 * DOCX generation options
 */
export interface DOCXGenerationOptions {
  includeHeader: boolean;
  includeFooter: boolean;
  includeBranding: boolean;
  includePageNumbers: boolean;
  colorScheme: string;
  paperSize: string;
}

/**
 * Markdown generation options
 */
export interface MarkdownGenerationOptions {
  includeHeader: boolean;
  includeFooter: boolean;
}

/**
 * Generate a PDF from a proposal
 * @param proposal Proposal data
 * @param options PDF generation options
 * @returns Promise resolving to a Blob containing the PDF
 */
export async function generateProposalPDF(
  proposal: IProposal,
  options: PDFGenerationOptions
): Promise<Blob> {
  // This is a placeholder implementation
  // In a real implementation, you would use a PDF generation library like pdfmake, jsPDF, or PDFKit
  
  // For now, we'll just simulate a delay and return a mock PDF blob
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Create a mock PDF blob
  const mockPDFContent = `
    PDF Content for Proposal: ${proposal.title}
    Generated with options: ${JSON.stringify(options)}
  `;
  
  return new Blob([mockPDFContent], { type: 'application/pdf' });
}

/**
 * Generate a DOCX from a proposal
 * @param proposal Proposal data
 * @param options DOCX generation options
 * @returns Promise resolving to a Blob containing the DOCX
 */
export async function generateProposalDOCX(
  proposal: IProposal,
  options: DOCXGenerationOptions
): Promise<Blob> {
  // This is a placeholder implementation
  // In a real implementation, you would use a DOCX generation library like docx
  
  // For now, we'll just simulate a delay and return a mock DOCX blob
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Create a mock DOCX blob
  const mockDOCXContent = `
    DOCX Content for Proposal: ${proposal.title}
    Generated with options: ${JSON.stringify(options)}
  `;
  
  return new Blob([mockDOCXContent], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
}

/**
 * Generate Markdown from a proposal
 * @param proposal Proposal data
 * @param options Markdown generation options
 * @returns Promise resolving to a Blob containing the Markdown
 */
export async function generateProposalMarkdown(
  proposal: IProposal,
  options: MarkdownGenerationOptions
): Promise<Blob> {
  // For now, we'll just simulate a delay and return a mock Markdown blob
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Create the Markdown content
  let markdownContent = '';
  
  // Add header if requested
  if (options.includeHeader) {
    markdownContent += `# ${proposal.title}\n\n`;
    
    if (proposal.description) {
      markdownContent += `${proposal.description}\n\n`;
    }
    
    markdownContent += `*Generated on ${new Date().toLocaleDateString()}*\n\n`;
    markdownContent += '---\n\n';
  }
  
  // Add sections
  const visibleSections = proposal.sections.filter(section => section.isVisible);
  
  for (const section of visibleSections) {
    markdownContent += `## ${section.title}\n\n`;
    markdownContent += `${section.content}\n\n`;
  }
  
  // Add pricing if available
  if (proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0) {
    markdownContent += '## Pricing\n\n';
    markdownContent += '| Item | Quantity | Unit Price | Total |\n';
    markdownContent += '| ---- | -------- | ---------- | ----- |\n';
    
    for (const item of proposal.pricing.items) {
      markdownContent += `| ${item.name} | ${item.quantity} | ${proposal.pricing.currency} ${item.unitPrice.toFixed(2)} | ${proposal.pricing.currency} ${item.total.toFixed(2)} |\n`;
    }
    
    markdownContent += '\n';
    markdownContent += `**Subtotal:** ${proposal.pricing.currency} ${proposal.pricing.subtotal.toFixed(2)}\n`;
    
    if (proposal.pricing.discount && proposal.pricing.discount > 0) {
      markdownContent += `**Discount:** ${proposal.pricing.currency} ${proposal.pricing.discount.toFixed(2)}\n`;
    }
    
    if (proposal.pricing.tax && proposal.pricing.tax > 0) {
      markdownContent += `**Tax:** ${proposal.pricing.currency} ${proposal.pricing.tax.toFixed(2)}\n`;
    }
    
    markdownContent += `**Total:** ${proposal.pricing.currency} ${proposal.pricing.total.toFixed(2)}\n\n`;
  }
  
  // Add terms if available
  if (proposal.terms) {
    markdownContent += '## Terms & Conditions\n\n';
    markdownContent += `${proposal.terms}\n\n`;
  }
  
  // Add footer if requested
  if (options.includeFooter) {
    markdownContent += '---\n\n';
    markdownContent += '*This proposal is valid for 30 days from the date of issue.*\n\n';
    markdownContent += '*Confidential - For recipient use only*\n';
  }
  
  return new Blob([markdownContent], { type: 'text/markdown' });
}

/**
 * Generate a proposal in the specified format
 * @param proposal Proposal data
 * @param format Format to generate (pdf, docx, md)
 * @param options Generation options
 * @returns Promise resolving to a Blob containing the generated document
 */
export async function generateProposal(
  proposal: IProposal,
  format: string,
  options: PDFGenerationOptions | DOCXGenerationOptions | MarkdownGenerationOptions
): Promise<Blob> {
  switch (format) {
    case 'pdf':
      return generateProposalPDF(proposal, options as PDFGenerationOptions);
    case 'docx':
      return generateProposalDOCX(proposal, options as DOCXGenerationOptions);
    case 'md':
      return generateProposalMarkdown(proposal, options as MarkdownGenerationOptions);
    default:
      throw new Error(`Unsupported format: ${format}`);
  }
}

/**
 * Download a blob as a file
 * @param blob Blob to download
 * @param filename Filename to use
 */
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Download a proposal in the specified format
 * @param proposal Proposal data
 * @param format Format to download (pdf, docx, md)
 * @param options Generation options
 */
export async function downloadProposal(
  proposal: IProposal,
  format: string,
  options: PDFGenerationOptions | DOCXGenerationOptions | MarkdownGenerationOptions
): Promise<void> {
  const blob = await generateProposal(proposal, format, options);
  
  // Generate a filename based on the proposal title and format
  const sanitizedTitle = proposal.title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
  const filename = `proposal_${sanitizedTitle}.${format}`;
  
  downloadBlob(blob, filename);
}
