/**
 * Centralized error handling for Aizako CRM
 * 
 * This module provides a set of custom error classes and utilities
 * for consistent error handling throughout the application.
 */

/**
 * Base error class for all application errors
 */
export class AppError extends Error {
  /**
   * HTTP status code
   */
  public statusCode: number;
  
  /**
   * Error code for client-side error handling
   */
  public errorCode: string;
  
  /**
   * Additional error details
   */
  public details?: any;
  
  /**
   * Whether the error is operational (expected) or programming (unexpected)
   */
  public isOperational: boolean;
  
  /**
   * Create a new AppError
   * @param message Error message
   * @param statusCode HTTP status code
   * @param errorCode Error code for client-side error handling
   * @param details Additional error details
   * @param isOperational Whether the error is operational (expected) or programming (unexpected)
   */
  constructor(
    message: string,
    statusCode: number = 500,
    errorCode: string = 'INTERNAL_SERVER_ERROR',
    details?: any,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
    this.isOperational = isOperational;
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error thrown when a resource is not found
 */
export class NotFoundError extends AppError {
  constructor(
    message: string = 'Resource not found',
    errorCode: string = 'RESOURCE_NOT_FOUND',
    details?: any
  ) {
    super(message, 404, errorCode, details, true);
  }
}

/**
 * Error thrown when validation fails
 */
export class ValidationError extends AppError {
  constructor(
    message: string = 'Validation failed',
    errorCode: string = 'VALIDATION_ERROR',
    details?: any
  ) {
    super(message, 400, errorCode, details, true);
  }
}

/**
 * Error thrown when authentication fails
 */
export class AuthenticationError extends AppError {
  constructor(
    message: string = 'Authentication failed',
    errorCode: string = 'AUTHENTICATION_ERROR',
    details?: any
  ) {
    super(message, 401, errorCode, details, true);
  }
}

/**
 * Error thrown when authorization fails
 */
export class AuthorizationError extends AppError {
  constructor(
    message: string = 'Not authorized',
    errorCode: string = 'AUTHORIZATION_ERROR',
    details?: any
  ) {
    super(message, 403, errorCode, details, true);
  }
}

/**
 * Error thrown when a conflict occurs
 */
export class ConflictError extends AppError {
  constructor(
    message: string = 'Conflict',
    errorCode: string = 'CONFLICT_ERROR',
    details?: any
  ) {
    super(message, 409, errorCode, details, true);
  }
}

/**
 * Error thrown when a rate limit is exceeded
 */
export class RateLimitError extends AppError {
  constructor(
    message: string = 'Rate limit exceeded',
    errorCode: string = 'RATE_LIMIT_ERROR',
    details?: any
  ) {
    super(message, 429, errorCode, details, true);
  }
}

/**
 * Error thrown when a dependency fails
 */
export class DependencyError extends AppError {
  constructor(
    message: string = 'Service dependency error',
    errorCode: string = 'DEPENDENCY_ERROR',
    details?: any
  ) {
    super(message, 502, errorCode, details, true);
  }
}

/**
 * Error thrown when a database operation fails
 */
export class DatabaseError extends AppError {
  constructor(
    message: string = 'Database error',
    errorCode: string = 'DATABASE_ERROR',
    details?: any
  ) {
    super(message, 500, errorCode, details, true);
  }
}

/**
 * Error thrown when a feature is not implemented
 */
export class NotImplementedError extends AppError {
  constructor(
    message: string = 'Feature not implemented',
    errorCode: string = 'NOT_IMPLEMENTED',
    details?: any
  ) {
    super(message, 501, errorCode, details, true);
  }
}

/**
 * Error thrown when a tenant is not found or invalid
 */
export class TenantError extends AppError {
  constructor(
    message: string = 'Tenant error',
    errorCode: string = 'TENANT_ERROR',
    details?: any
  ) {
    super(message, 403, errorCode, details, true);
  }
}

/**
 * Error thrown when a subscription is required
 */
export class SubscriptionError extends AppError {
  constructor(
    message: string = 'Subscription required',
    errorCode: string = 'SUBSCRIPTION_ERROR',
    details?: any
  ) {
    super(message, 402, errorCode, details, true);
  }
}

/**
 * Convert an error to an AppError
 * @param error Error to convert
 * @returns AppError
 */
export function toAppError(error: any): AppError {
  if (error instanceof AppError) {
    return error;
  }
  
  // Handle Mongoose validation errors
  if (error.name === 'ValidationError') {
    return new ValidationError(
      'Validation failed',
      'MONGOOSE_VALIDATION_ERROR',
      Object.values(error.errors).map((err: any) => ({
        path: err.path,
        message: err.message,
      }))
    );
  }
  
  // Handle Mongoose duplicate key errors
  if (error.name === 'MongoError' && error.code === 11000) {
    return new ConflictError(
      'Duplicate key error',
      'MONGOOSE_DUPLICATE_KEY',
      error.keyValue
    );
  }
  
  // Handle Zod validation errors
  if (error.name === 'ZodError') {
    return new ValidationError(
      'Validation failed',
      'ZOD_VALIDATION_ERROR',
      error.errors
    );
  }
  
  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError(
      'Invalid token',
      'JWT_ERROR',
      { message: error.message }
    );
  }
  
  // Handle token expiration
  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError(
      'Token expired',
      'TOKEN_EXPIRED',
      { expiredAt: error.expiredAt }
    );
  }
  
  // Default to internal server error
  return new AppError(
    error.message || 'Internal server error',
    500,
    'INTERNAL_SERVER_ERROR',
    undefined,
    false
  );
}

/**
 * Format an error for API response
 * @param error Error to format
 * @returns Formatted error object
 */
export function formatError(error: any): Record<string, any> {
  const appError = toAppError(error);
  
  return {
    success: false,
    error: appError.errorCode,
    message: appError.message,
    ...(appError.details ? { details: appError.details } : {}),
    ...(process.env.NODE_ENV === 'development' ? { stack: appError.stack } : {}),
  };
}

export default {
  AppError,
  NotFoundError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  ConflictError,
  RateLimitError,
  DependencyError,
  DatabaseError,
  NotImplementedError,
  TenantError,
  SubscriptionError,
  toAppError,
  formatError,
};
