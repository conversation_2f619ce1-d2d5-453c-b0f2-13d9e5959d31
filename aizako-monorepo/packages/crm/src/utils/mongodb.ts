import mongoose from 'mongoose';
import { MONGODB_URI } from '../config';

// Connection options
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  autoIndex: true, // Build indexes
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  family: 4 // Use IPv4, skip trying IPv6
};

// Global is used here to maintain a cached connection across hot reloads
// in development. This prevents connections growing exponentially
// during API Route usage.
let cached: { conn: typeof mongoose | null; promise: Promise<typeof mongoose> | null } =
  { conn: null, promise: null };

/**
 * Connect to MongoDB
 * @param uri MongoDB connection URI (optional, defaults to config.MONGODB_URI)
 * @returns Promise that resolves when connection is established
 */
export async function connectToMongoDB(uri: string = MONGODB_URI): Promise<typeof mongoose> {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = options as mongoose.ConnectOptions;

    mongoose.set('strictQuery', false);

    cached.promise = mongoose.connect(uri, opts).then((mongoose) => {
      console.log('MongoDB connected successfully');
      return mongoose;
    });
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

/**
 * Disconnect from MongoDB
 */
export async function disconnectFromMongoDB(): Promise<void> {
  if (cached.conn) {
    await mongoose.disconnect();
    cached.conn = null;
    cached.promise = null;
    console.log('MongoDB disconnected');
  }
}

/**
 * Test MongoDB connection
 * @param uri MongoDB connection URI (optional, defaults to config.MONGODB_URI)
 * @returns True if connection is successful, false otherwise
 */
export async function testMongoDBConnection(uri: string = MONGODB_URI): Promise<boolean> {
  try {
    await connectToMongoDB(uri);
    return true;
  } catch (error) {
    console.error('MongoDB connection test failed:', error);
    return false;
  }
}

/**
 * Get MongoDB connection status
 * @returns Connection status object
 */
export function getMongoDBConnectionStatus(): { isConnected: boolean; readyState: number } {
  return {
    isConnected: mongoose.connection.readyState === 1,
    readyState: mongoose.connection.readyState
  };
}

export default connectToMongoDB;
