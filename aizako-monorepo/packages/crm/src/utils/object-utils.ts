/**
 * Deep compare two values
 * @param obj1 First value
 * @param obj2 Second value
 * @returns True if the values are equal
 */
export function deepCompare(obj1: any, obj2: any): boolean {
  // If both are undefined or null, they're equal
  if (obj1 === obj2) {
    return true;
  }
  
  // If one is undefined or null but the other isn't, they're not equal
  if (obj1 == null || obj2 == null) {
    return false;
  }
  
  // If they're not both objects, compare them directly
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
    return obj1 === obj2;
  }
  
  // If one is an array but the other isn't, they're not equal
  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return false;
  }
  
  // If they're both arrays, compare each element
  if (Array.isArray(obj1)) {
    if (obj1.length !== obj2.length) {
      return false;
    }
    
    for (let i = 0; i < obj1.length; i++) {
      if (!deepCompare(obj1[i], obj2[i])) {
        return false;
      }
    }
    
    return true;
  }
  
  // If they're both objects, compare each property
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) {
    return false;
  }
  
  for (const key of keys1) {
    if (!keys2.includes(key)) {
      return false;
    }
    
    if (!deepCompare(obj1[key], obj2[key])) {
      return false;
    }
  }
  
  return true;
}

/**
 * Deep clone an object
 * @param obj Object to clone
 * @returns Cloned object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  const clone = {} as T;
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clone[key] = deepClone(obj[key]);
    }
  }
  
  return clone;
}

/**
 * Get the difference between two objects
 * @param obj1 First object
 * @param obj2 Second object
 * @returns Object containing the differences
 */
export function objectDiff(obj1: Record<string, any>, obj2: Record<string, any>): Record<string, any> {
  const diff: Record<string, any> = {};
  
  // Find properties in obj2 that are different from obj1
  for (const key in obj2) {
    if (Object.prototype.hasOwnProperty.call(obj2, key)) {
      // If the property doesn't exist in obj1, it's a new property
      if (!Object.prototype.hasOwnProperty.call(obj1, key)) {
        diff[key] = { type: 'added', value: obj2[key] };
        continue;
      }
      
      // If the property exists in both but is different
      if (!deepCompare(obj1[key], obj2[key])) {
        if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object' && 
            obj1[key] !== null && obj2[key] !== null &&
            !Array.isArray(obj1[key]) && !Array.isArray(obj2[key])) {
          // If both are objects, recursively find differences
          const nestedDiff = objectDiff(obj1[key], obj2[key]);
          if (Object.keys(nestedDiff).length > 0) {
            diff[key] = { type: 'modified', value: nestedDiff };
          }
        } else {
          // Otherwise, just mark it as modified
          diff[key] = { type: 'modified', oldValue: obj1[key], newValue: obj2[key] };
        }
      }
    }
  }
  
  // Find properties in obj1 that don't exist in obj2
  for (const key in obj1) {
    if (Object.prototype.hasOwnProperty.call(obj1, key) && 
        !Object.prototype.hasOwnProperty.call(obj2, key)) {
      diff[key] = { type: 'deleted', value: obj1[key] };
    }
  }
  
  return diff;
}

/**
 * Flatten an object into a single-level object with dot notation for nested properties
 * @param obj Object to flatten
 * @param prefix Prefix for nested properties
 * @returns Flattened object
 */
export function flattenObject(obj: Record<string, any>, prefix: string = ''): Record<string, any> {
  const flattened: Record<string, any> = {};
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // If it's an object, recursively flatten it
        Object.assign(flattened, flattenObject(value, newKey));
      } else {
        // Otherwise, add it to the flattened object
        flattened[newKey] = value;
      }
    }
  }
  
  return flattened;
}

/**
 * Unflatten an object with dot notation into a nested object
 * @param obj Flattened object
 * @returns Nested object
 */
export function unflattenObject(obj: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const keys = key.split('.');
      let current = result;
      
      for (let i = 0; i < keys.length; i++) {
        const k = keys[i];
        
        if (i === keys.length - 1) {
          // Last key, set the value
          current[k] = value;
        } else {
          // Not the last key, create nested object if it doesn't exist
          if (!current[k] || typeof current[k] !== 'object') {
            current[k] = {};
          }
          
          current = current[k];
        }
      }
    }
  }
  
  return result;
}

export default {
  deepCompare,
  deepClone,
  objectDiff,
  flattenObject,
  unflattenObject,
};
