/**
 * MongoDB transaction utilities for Aizako CRM
 * 
 * This module provides utilities for working with MongoDB transactions.
 * Transactions ensure that multiple database operations are executed atomically.
 */
import mongoose from 'mongoose';
import { logger } from './logger';
import { DatabaseError } from './errors';

/**
 * Transaction options
 */
export interface TransactionOptions {
  /**
   * Read concern
   * @default 'majority'
   */
  readConcern?: mongoose.ReadConcernLevel;
  
  /**
   * Write concern
   * @default 'majority'
   */
  writeConcern?: mongoose.WriteConcernSettings;
  
  /**
   * Maximum number of retry attempts
   * @default 3
   */
  maxRetries?: number;
  
  /**
   * Retry delay in milliseconds
   * @default 100
   */
  retryDelay?: number;
}

/**
 * Default transaction options
 */
const defaultOptions: TransactionOptions = {
  readConcern: 'majority',
  writeConcern: { w: 'majority' },
  maxRetries: 3,
  retryDelay: 100,
};

/**
 * Execute a function within a MongoDB transaction
 * 
 * This function creates a session, starts a transaction, and executes the provided function.
 * If the function succeeds, the transaction is committed. If it fails, the transaction is aborted.
 * 
 * @param fn Function to execute within the transaction
 * @param options Transaction options
 * @returns Result of the function
 * 
 * @example
 * ```typescript
 * const result = await withTransaction(async (session) => {
 *   const user = await User.create([{ name: 'John' }], { session });
 *   const profile = await Profile.create([{ userId: user[0]._id }], { session });
 *   return { user: user[0], profile: profile[0] };
 * });
 * ```
 */
export async function withTransaction<T>(
  fn: (session: mongoose.ClientSession) => Promise<T>,
  options: TransactionOptions = {}
): Promise<T> {
  // Merge options with defaults
  const opts = { ...defaultOptions, ...options };
  
  // Create session
  const session = await mongoose.startSession();
  
  try {
    // Start transaction
    session.startTransaction({
      readConcern: { level: opts.readConcern },
      writeConcern: opts.writeConcern,
    });
    
    // Execute function
    const result = await fn(session);
    
    // Commit transaction
    await session.commitTransaction();
    
    return result;
  } catch (error) {
    // Abort transaction
    await session.abortTransaction();
    
    // Log error
    logger.error({
      message: 'Transaction failed',
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    // Rethrow error
    throw error;
  } finally {
    // End session
    await session.endSession();
  }
}

/**
 * Execute a function with retry logic
 * 
 * This function executes the provided function and retries it if it fails
 * with a transient error (e.g., network error, write conflict).
 * 
 * @param fn Function to execute
 * @param options Retry options
 * @returns Result of the function
 * 
 * @example
 * ```typescript
 * const result = await withRetry(async () => {
 *   return await User.findOneAndUpdate(
 *     { _id: userId },
 *     { $inc: { counter: 1 } },
 *     { new: true }
 *   );
 * });
 * ```
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: Pick<TransactionOptions, 'maxRetries' | 'retryDelay'> = {}
): Promise<T> {
  // Merge options with defaults
  const opts = {
    maxRetries: defaultOptions.maxRetries,
    retryDelay: defaultOptions.retryDelay,
    ...options,
  };
  
  let lastError: Error | null = null;
  
  // Try to execute the function with retries
  for (let attempt = 1; attempt <= opts.maxRetries!; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Check if error is transient
      if (isTransientError(error)) {
        // Log retry attempt
        logger.warn({
          message: `Retrying operation (attempt ${attempt}/${opts.maxRetries})`,
          error: lastError.message,
          attempt,
          maxRetries: opts.maxRetries,
        });
        
        // Wait before retrying
        if (attempt < opts.maxRetries!) {
          await new Promise(resolve => setTimeout(resolve, opts.retryDelay));
        }
      } else {
        // Non-transient error, don't retry
        break;
      }
    }
  }
  
  // If we get here, all retries failed
  throw new DatabaseError(
    `Operation failed after ${opts.maxRetries} attempts`,
    'RETRY_FAILED',
    { lastError: lastError?.message }
  );
}

/**
 * Check if an error is transient (can be retried)
 * @param error Error to check
 * @returns True if the error is transient
 */
function isTransientError(error: any): boolean {
  // Check for MongoDB transient errors
  if (error.name === 'MongoError' || error.name === 'MongoServerError') {
    // Write conflict
    if (error.code === 112) {
      return true;
    }
    
    // Network errors
    if ([6, 7, 89, 91, 189, 9001].includes(error.code)) {
      return true;
    }
    
    // Replication errors
    if ([10107, 13436, 13435, 11602, 11600, 10058, 13116].includes(error.code)) {
      return true;
    }
  }
  
  // Check for Mongoose transient errors
  if (error.name === 'MongooseError') {
    if (error.message.includes('buffering timed out') || 
        error.message.includes('connection timed out')) {
      return true;
    }
  }
  
  // Check for general network errors
  if (error.name === 'NetworkError' || 
      error.message.includes('network') || 
      error.message.includes('timeout')) {
    return true;
  }
  
  return false;
}

export default {
  withTransaction,
  withRetry,
};
