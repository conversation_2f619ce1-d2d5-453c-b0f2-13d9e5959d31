/**
 * Logger utility for Aizako CRM
 * 
 * This module provides a structured logging utility using Pino.
 * It supports different log levels and formats logs as JSON for
 * easier parsing and analysis.
 */
import pino from 'pino';
import { LOG_LEVEL } from '../config';

// Define log levels
const levels = {
  fatal: 60,
  error: 50,
  warn: 40,
  info: 30,
  debug: 20,
  trace: 10,
};

// Create logger instance
export const logger = pino({
  level: LOG_LEVEL || 'info',
  timestamp: pino.stdTimeFunctions.isoTime,
  formatters: {
    level: (label) => {
      return { level: label };
    },
  },
  serializers: {
    err: pino.stdSerializers.err,
    error: pino.stdSerializers.err,
    req: pino.stdSerializers.req,
    res: pino.stdSerializers.res,
  },
  base: {
    service: 'aizako-crm',
    env: process.env.NODE_ENV || 'development',
  },
});

/**
 * Create a child logger with additional context
 * @param context Additional context to include in logs
 * @returns Child logger instance
 * 
 * @example
 * ```typescript
 * const userLogger = createLogger({ userId: '123', tenantId: '456' });
 * userLogger.info('User logged in');
 * ```
 */
export function createLogger(context: Record<string, any>) {
  return logger.child(context);
}

/**
 * Log HTTP request and response
 * @param req Express request
 * @param res Express response
 * @param responseTime Response time in milliseconds
 */
export function logHttpRequest(req: any, res: any, responseTime: number) {
  const logData = {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime,
    ...(req.user ? { userId: req.user.id } : {}),
    ...(req.tenantId ? { tenantId: req.tenantId } : {}),
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress,
  };
  
  if (res.statusCode >= 500) {
    logger.error(logData, 'HTTP request error');
  } else if (res.statusCode >= 400) {
    logger.warn(logData, 'HTTP request warning');
  } else {
    logger.info(logData, 'HTTP request');
  }
}

/**
 * Log database operation
 * @param operation Operation name
 * @param collection Collection name
 * @param filter Filter used
 * @param duration Operation duration in milliseconds
 * @param result Operation result
 */
export function logDbOperation(
  operation: string,
  collection: string,
  filter: Record<string, any>,
  duration: number,
  result: any
) {
  logger.debug({
    operation,
    collection,
    filter,
    duration,
    result: typeof result === 'object' ? 'object' : result,
    resultCount: Array.isArray(result) ? result.length : undefined,
  }, 'Database operation');
}

/**
 * Log external API call
 * @param service Service name
 * @param endpoint Endpoint called
 * @param method HTTP method
 * @param duration Call duration in milliseconds
 * @param statusCode Response status code
 */
export function logApiCall(
  service: string,
  endpoint: string,
  method: string,
  duration: number,
  statusCode: number
) {
  const logData = {
    service,
    endpoint,
    method,
    duration,
    statusCode,
  };
  
  if (statusCode >= 500) {
    logger.error(logData, 'External API call error');
  } else if (statusCode >= 400) {
    logger.warn(logData, 'External API call warning');
  } else {
    logger.info(logData, 'External API call');
  }
}

export default {
  logger,
  createLogger,
  logHttpRequest,
  logDbOperation,
  logApiCall,
};
