/**
 * Add tenant filter to a query
 * @param query Query object
 * @param tenantId Tenant ID
 * @returns Query object with tenant filter
 */
export function addTenantFilter<T extends Record<string, any>>(query: T, tenantId: string): T {
  return {
    ...query,
    tenantId,
  };
}

/**
 * Create a tenant filter object
 * @param tenantId Tenant ID
 * @returns Tenant filter object
 */
export function createTenantFilter(tenantId: string): { tenantId: string } {
  return { tenantId };
}

/**
 * Check if a tenant ID matches
 * @param obj Object to check
 * @param tenantId Tenant ID to match
 * @returns True if tenant ID matches
 */
export function isSameTenant(obj: { tenantId?: string }, tenantId: string): boolean {
  return obj.tenantId === tenantId;
}

/**
 * Validate tenant access
 * @param obj Object to validate
 * @param tenantId Tenant ID to validate against
 * @throws Error if tenant ID doesn't match
 */
export function validateTenantAccess(obj: { tenantId?: string }, tenantId: string): void {
  if (!isSameTenant(obj, tenantId)) {
    throw new Error('Tenant access denied');
  }
}
