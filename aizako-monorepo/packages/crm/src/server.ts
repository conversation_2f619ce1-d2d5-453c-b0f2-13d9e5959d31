import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { BackgroundTaskService } from './services/background-task-service';
import { registerAITaskHandlers } from './services/ai-task-handlers';
import proposalRoutes from './api/routes/proposal-routes';
import proposalAiRoutes from './api/routes/proposal-ai';
import { errorHandler, notFoundHandler } from './api/middleware/error-handler';
import { setupSwagger } from './api/openapi';
import { logger } from './utils/logger';

/**
 * Initialize the CRM server
 * @param mongoUri MongoDB connection URI
 * @param port Server port
 * @returns Express app
 */
export async function initServer(
  mongoUri: string,
  port: number = 3000
): Promise<express.Application> {
  // Connect to MongoDB
  await mongoose.connect(mongoUri);
  logger.info('Connected to MongoDB');

  // Initialize background task service
  const taskService = BackgroundTaskService.getInstance();
  registerAITaskHandlers(taskService);
  taskService.start();
  logger.info('Background task service started');

  // Create Express app
  const app = express();

  // Middleware
  app.use(helmet());
  app.use(cors());
  app.use(compression());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // API Documentation
  setupSwagger(app);

  // Routes
  app.use('/api/crm/proposals', proposalRoutes);
  app.use('/api/crm/proposals/ai', proposalAiRoutes);

  // Error handling
  app.use(notFoundHandler);
  app.use(errorHandler);

  // Start server
  app.listen(port, () => {
    logger.info(`CRM server listening on port ${port}`);
  });

  return app;
}

/**
 * Shutdown the CRM server
 */
export async function shutdownServer(): Promise<void> {
  // Stop background task service
  const taskService = BackgroundTaskService.getInstance();
  taskService.stop();
  logger.info('Background task service stopped');

  // Disconnect from MongoDB
  await mongoose.disconnect();
  logger.info('Disconnected from MongoDB');
}

// Start the server if this file is run directly
if (require.main === module) {
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/aizako-crm';
  const port = parseInt(process.env.PORT || '3000', 10);

  initServer(mongoUri, port).catch((error) => {
    logger.error('Failed to start server:', { error });
    process.exit(1);
  });

  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    logger.info('Received SIGINT. Shutting down gracefully...');
    await shutdownServer();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM. Shutting down gracefully...');
    await shutdownServer();
    process.exit(0);
  });
}
