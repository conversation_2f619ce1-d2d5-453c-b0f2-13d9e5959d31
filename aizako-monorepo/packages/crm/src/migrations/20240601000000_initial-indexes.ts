/**
 * Migration: Initial Indexes
 * Created: 2024-06-01T00:00:00.000Z
 * 
 * This migration creates initial indexes for all collections
 * to ensure optimal query performance.
 */
import { Db } from 'mongodb';

/**
 * Migration class for initial indexes
 */
export class InitialIndexesMigration {
  /**
   * Order of migration execution
   * Higher values run later
   */
  public static readonly order = 1;

  /**
   * Unique migration ID
   */
  public static readonly id = '20240601000000-initial-indexes';

  /**
   * Author of the migration
   */
  public static readonly author = 'aizako-system';

  /**
   * Execute the migration
   * @param db MongoDB database instance
   */
  public async execute(db: Db): Promise<void> {
    console.log('Creating initial indexes for all collections');
    
    // Create indexes for users collection
    await db.collection('users').createIndexes([
      { key: { email: 1 }, unique: true },
      { key: { username: 1 }, unique: true },
      { key: { createdAt: -1 } },
    ]);
    
    // Create indexes for contacts collection
    await db.collection('contacts').createIndexes([
      { key: { tenantId: 1 } },
      { key: { email: 1, tenantId: 1 } },
      { key: { lastName: 1, firstName: 1, tenantId: 1 } },
      { key: { status: 1, tenantId: 1 } },
      { key: { createdAt: -1 } },
    ]);
    
    // Create indexes for companies collection
    await db.collection('companies').createIndexes([
      { key: { tenantId: 1 } },
      { key: { name: 1, tenantId: 1 } },
      { key: { industry: 1, tenantId: 1 } },
      { key: { status: 1, tenantId: 1 } },
      { key: { createdAt: -1 } },
    ]);
    
    // Create indexes for opportunities collection
    await db.collection('opportunities').createIndexes([
      { key: { tenantId: 1 } },
      { key: { contactId: 1, tenantId: 1 } },
      { key: { companyId: 1, tenantId: 1 } },
      { key: { stage: 1, tenantId: 1 } },
      { key: { closeDate: 1, tenantId: 1 } },
      { key: { value: -1, tenantId: 1 } },
      { key: { createdAt: -1 } },
    ]);
    
    // Create indexes for activities collection
    await db.collection('activities').createIndexes([
      { key: { tenantId: 1 } },
      { key: { contactId: 1, tenantId: 1 } },
      { key: { opportunityId: 1, tenantId: 1 } },
      { key: { type: 1, tenantId: 1 } },
      { key: { dueDate: 1, tenantId: 1 } },
      { key: { completed: 1, tenantId: 1 } },
      { key: { createdAt: -1 } },
    ]);
    
    // Create indexes for interactions collection
    await db.collection('interactions').createIndexes([
      { key: { tenantId: 1 } },
      { key: { contactId: 1, tenantId: 1 } },
      { key: { opportunityId: 1, tenantId: 1 } },
      { key: { type: 1, tenantId: 1 } },
      { key: { date: -1, tenantId: 1 } },
      { key: { createdAt: -1 } },
    ]);
    
    // Create indexes for proposals collection
    await db.collection('proposals').createIndexes([
      { key: { tenantId: 1 } },
      { key: { opportunityId: 1, tenantId: 1 } },
      { key: { status: 1, tenantId: 1 } },
      { key: { createdAt: -1 } },
    ]);
    
    // Create indexes for email tracking collection
    await db.collection('emailtrackings').createIndexes([
      { key: { tenantId: 1 } },
      { key: { contactId: 1, tenantId: 1 } },
      { key: { messageId: 1 } },
      { key: { createdAt: -1 } },
    ]);
    
    console.log('Initial indexes created successfully');
  }

  /**
   * Rollback the migration
   * @param db MongoDB database instance
   */
  public async rollback(db: Db): Promise<void> {
    console.log('Rolling back initial indexes');
    
    // Drop indexes for users collection
    await db.collection('users').dropIndexes();
    
    // Drop indexes for contacts collection
    await db.collection('contacts').dropIndexes();
    
    // Drop indexes for companies collection
    await db.collection('companies').dropIndexes();
    
    // Drop indexes for opportunities collection
    await db.collection('opportunities').dropIndexes();
    
    // Drop indexes for activities collection
    await db.collection('activities').dropIndexes();
    
    // Drop indexes for interactions collection
    await db.collection('interactions').dropIndexes();
    
    // Drop indexes for proposals collection
    await db.collection('proposals').dropIndexes();
    
    // Drop indexes for email tracking collection
    await db.collection('emailtrackings').dropIndexes();
    
    console.log('Initial indexes rolled back successfully');
  }
}

export default InitialIndexesMigration;
