/**
 * Aizako CRM Module
 *
 * This module provides Customer Relationship Management functionality for the Aizako platform.
 * It includes models, services, components, hooks, and utilities for managing contacts,
 * companies, opportunities, activities, tasks, proposals, and more.
 *
 * @module @aizako/crm
 */

// Components exports
import * as Components from './components';
export { Components };

// Hooks exports
import * as Hooks from './hooks';
export { Hooks };

// Models exports
import * as Models from './models';
export { Models };

// Services exports
import * as Services from './services';
export { Services };

// Types exports
import * as Types from './types';
export { Types };

// Utils exports
import * as Utils from './utils';
export { Utils };

// API exports
export * from './api';

// Constants
export const CRM_MODULE_VERSION = '1.0.0';
export const CRM_MODULE_NAME = 'crm';

// API Routes
export const CRM_API_ROUTES = {
  CONTACTS: '/api/crm/contacts',
  COMPANIES: '/api/crm/companies',
  OPPORTUNITIES: '/api/crm/opportunities',
  ACTIVITIES: '/api/crm/activities',
  TASKS: '/api/crm/tasks',
  DOCUMENTS: '/api/crm/documents',
  PROPOSALS: '/api/crm/proposals',
  EMAIL_TEMPLATES: '/api/crm/email-templates',
  EMAIL_TRACKING: '/api/crm/email-tracking',
  SEQUENCES: '/api/crm/sequences',
  WORKFLOWS: '/api/crm/workflows',
  TAGS: '/api/crm/tags',
  TENANT_DOMAINS: '/api/crm/tenant-domains',
};
