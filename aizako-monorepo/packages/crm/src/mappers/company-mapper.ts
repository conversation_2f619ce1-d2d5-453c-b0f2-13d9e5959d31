/**
 * Company mapper
 *
 * This module provides functions to map between company domain entities and database models.
 * It handles the conversion of data structures, including:
 *
 * - Converting MongoDB ObjectIds to strings and vice versa
 * - Converting _id to id and vice versa
 * - Handling nested objects and arrays
 * - Applying any business rules during conversion
 *
 * The mapper follows the data mapper pattern, which separates domain entities
 * from their persistence mechanism, allowing each to evolve independently.
 *
 * @module mappers/company-mapper
 * @see {@link CompanyEntity} for the domain entity interface
 * @see {@link ICompany} for the database model interface
 * @see {@link ../docs/data-mapper-pattern.md} for more information on the data mapper pattern
 */
import mongoose from 'mongoose';
import { ICompany } from '../models/company';

/**
 * Company domain entity
 *
 * This interface represents a company in the domain model.
 * It is independent of the database implementation and focuses on
 * business concepts rather than persistence details.
 *
 * Key differences from the database model:
 * - Uses string IDs instead of MongoDB ObjectIds
 * - Has a more business-oriented structure
 * - Excludes database-specific fields like __v
 *
 * @interface CompanyEntity
 * @extends {TenantScopedEntity}
 */
export interface CompanyEntity {
  id: string;
  tenantId: string;
  name: string;
  website?: string;
  industry?: string;
  size?: string;
  status: string;
  owner?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  tags?: string[];
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Map a database model to a domain entity
 *
 * This function converts a MongoDB company document to a domain entity.
 * It handles the conversion of:
 * - MongoDB ObjectIds to strings
 * - _id to id
 * - Nested objects and arrays
 *
 * @param model MongoDB company document from the database
 * @returns Company domain entity for use in business logic
 *
 * @example
 * ```typescript
 * // Convert a MongoDB document to a domain entity
 * const companyDoc = await Company.findById(id);
 * const companyEntity = toEntity(companyDoc);
 * ```
 */
export function toEntity(model: ICompany): CompanyEntity {
  if (!model) return null as any;

  const entity: CompanyEntity = {
    id: model._id.toString(),
    tenantId: model.tenantId,
    name: model.name,
    website: model.website,
    industry: model.industry,
    size: model.size,
    status: model.status,
    address: model.address,
    tags: model.tags?.map(tag => tag.toString()),
    customFields: model.customFields,
    createdAt: model.createdAt,
    updatedAt: model.updatedAt,
  };

  // Convert ObjectId fields to strings
  if (model.owner) {
    entity.owner = model.owner.toString();
  }

  return entity;
}

/**
 * Map a domain entity to a database model
 *
 * This function converts a company domain entity to a MongoDB document.
 * It handles the conversion of:
 * - Strings to MongoDB ObjectIds
 * - id to _id
 * - Nested objects and arrays
 *
 * @param entity Company domain entity from business logic
 * @returns MongoDB company document for database operations
 *
 * @example
 * ```typescript
 * // Convert a domain entity to a MongoDB document
 * const companyEntity = {
 *   id: '123',
 *   name: 'Acme Inc',
 *   industry: 'Technology',
 *   tenantId: 'tenant-123'
 * };
 * const companyDoc = toModel(companyEntity);
 * await Company.create(companyDoc);
 * ```
 */
export function toModel(entity: Partial<CompanyEntity>): Partial<ICompany> {
  if (!entity) return null as any;

  const model: Partial<ICompany> = {
    tenantId: entity.tenantId,
    name: entity.name,
    website: entity.website,
    industry: entity.industry,
    size: entity.size,
    status: entity.status,
    address: entity.address,
    customFields: entity.customFields,
  };

  // Convert string IDs to ObjectIds
  if (entity.id) {
    model._id = new mongoose.Types.ObjectId(entity.id);
  }

  if (entity.owner) {
    model.owner = new mongoose.Types.ObjectId(entity.owner);
  }

  // Map tags
  if (entity.tags && entity.tags.length > 0) {
    model.tags = entity.tags.map(tag => new mongoose.Types.ObjectId(tag));
  }

  return model;
}

export default {
  toEntity,
  toModel,
};
