/**
 * Contact mapper
 *
 * This module provides functions to map between contact domain entities and database models.
 * It handles the conversion of data structures, including:
 *
 * - Converting MongoDB ObjectIds to strings and vice versa
 * - Converting _id to id and vice versa
 * - Handling nested objects and arrays
 * - Applying any business rules during conversion
 *
 * The mapper follows the data mapper pattern, which separates domain entities
 * from their persistence mechanism, allowing each to evolve independently.
 *
 * @module mappers/contact-mapper
 * @see {@link ContactEntity} for the domain entity interface
 * @see {@link IContact} for the database model interface
 * @see {@link ../docs/data-mapper-pattern.md} for more information on the data mapper pattern
 */
import mongoose from 'mongoose';
import { IContact } from '../models/contact';

/**
 * Contact domain entity
 *
 * This interface represents a contact in the domain model.
 * It is independent of the database implementation and focuses on
 * business concepts rather than persistence details.
 *
 * Key differences from the database model:
 * - Uses string IDs instead of MongoDB ObjectIds
 * - Has a more business-oriented structure
 * - Excludes database-specific fields like __v
 *
 * @interface ContactEntity
 * @extends {TenantScopedEntity}
 */
export interface ContactEntity {
  id: string;
  tenantId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  companyId?: string;
  status: string;
  source?: string;
  owner?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  tags?: string[];
  score?: {
    current: number;
    previous?: number;
    lastUpdated?: Date;
    factors?: Record<string, number>;
  };
  persona?: {
    type?: string;
    traits?: string[];
    preferences?: string[];
    aiGenerated?: boolean;
  };
  interactions?: {
    id: string;
    type: string;
    date: Date;
    notes?: string;
    userId?: string;
    metadata?: Record<string, any>;
  }[];
  lastContactedAt?: Date;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Map a database model to a domain entity
 *
 * This function converts a MongoDB contact document to a domain entity.
 * It handles the conversion of:
 * - MongoDB ObjectIds to strings
 * - _id to id
 * - Nested objects and arrays
 *
 * @param model MongoDB contact document from the database
 * @returns Contact domain entity for use in business logic
 *
 * @example
 * ```typescript
 * // Convert a MongoDB document to a domain entity
 * const contactDoc = await Contact.findById(id);
 * const contactEntity = toEntity(contactDoc);
 * ```
 */
export function toEntity(model: IContact): ContactEntity {
  if (!model) return null as any;

  const entity: ContactEntity = {
    id: model._id.toString(),
    tenantId: model.tenantId,
    firstName: model.firstName,
    lastName: model.lastName,
    email: model.email,
    phone: model.phone,
    jobTitle: model.jobTitle,
    status: model.status,
    source: model.source,
    address: model.address,
    tags: model.tags?.map(tag => tag.toString()),
    score: model.score,
    persona: model.persona,
    customFields: model.customFields,
    createdAt: model.createdAt,
    updatedAt: model.updatedAt,
  };

  // Convert ObjectId fields to strings
  if (model.companyId) {
    entity.companyId = model.companyId.toString();
  }

  if (model.owner) {
    entity.owner = model.owner.toString();
  }

  // Map interactions
  if (model.interactions && model.interactions.length > 0) {
    entity.interactions = model.interactions.map(interaction => ({
      id: interaction.id || interaction._id.toString(),
      type: interaction.type,
      date: interaction.date,
      notes: interaction.notes,
      userId: interaction.userId?.toString(),
      metadata: interaction.metadata,
    }));
  }

  // Map lastContactedAt
  if (model.lastContactedAt) {
    entity.lastContactedAt = model.lastContactedAt;
  }

  return entity;
}

/**
 * Map a domain entity to a database model
 *
 * This function converts a contact domain entity to a MongoDB document.
 * It handles the conversion of:
 * - Strings to MongoDB ObjectIds
 * - id to _id
 * - Nested objects and arrays
 *
 * @param entity Contact domain entity from business logic
 * @returns MongoDB contact document for database operations
 *
 * @example
 * ```typescript
 * // Convert a domain entity to a MongoDB document
 * const contactEntity = {
 *   id: '123',
 *   firstName: 'John',
 *   lastName: 'Doe',
 *   email: '<EMAIL>',
 *   tenantId: 'tenant-123'
 * };
 * const contactDoc = toModel(contactEntity);
 * await Contact.create(contactDoc);
 * ```
 */
export function toModel(entity: Partial<ContactEntity>): Partial<IContact> {
  if (!entity) return null as any;

  const model: Partial<IContact> = {
    tenantId: entity.tenantId,
    firstName: entity.firstName,
    lastName: entity.lastName,
    email: entity.email,
    phone: entity.phone,
    jobTitle: entity.jobTitle,
    status: entity.status,
    source: entity.source,
    address: entity.address,
    score: entity.score,
    persona: entity.persona,
    customFields: entity.customFields,
  };

  // Convert string IDs to ObjectIds
  if (entity.id) {
    model._id = new mongoose.Types.ObjectId(entity.id);
  }

  if (entity.companyId) {
    model.companyId = new mongoose.Types.ObjectId(entity.companyId);
  }

  if (entity.owner) {
    model.owner = new mongoose.Types.ObjectId(entity.owner);
  }

  // Map tags
  if (entity.tags && entity.tags.length > 0) {
    model.tags = entity.tags.map(tag => new mongoose.Types.ObjectId(tag));
  }

  // Map interactions
  if (entity.interactions && entity.interactions.length > 0) {
    model.interactions = entity.interactions.map(interaction => ({
      _id: interaction.id ? new mongoose.Types.ObjectId(interaction.id) : new mongoose.Types.ObjectId(),
      type: interaction.type,
      date: interaction.date,
      notes: interaction.notes,
      userId: interaction.userId ? new mongoose.Types.ObjectId(interaction.userId) : undefined,
      metadata: interaction.metadata,
    }));
  }

  // Map lastContactedAt
  if (entity.lastContactedAt) {
    model.lastContactedAt = entity.lastContactedAt;
  }

  return model;
}

export default {
  toEntity,
  toModel,
};
