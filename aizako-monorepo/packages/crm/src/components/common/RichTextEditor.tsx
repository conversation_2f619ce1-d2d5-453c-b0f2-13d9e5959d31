import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Toolbar,
  Divider,
  IconButton,
  Tooltip,
  ToggleButton,
  ToggleButtonGroup,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Typography,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import {
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatUnderlined as UnderlineIcon,
  FormatStrikethrough as StrikethroughIcon,
  FormatListBulleted as BulletListIcon,
  FormatListNumbered as NumberedListIcon,
  FormatQuote as QuoteIcon,
  Code as CodeIcon,
  Link as LinkIcon,
  Image as ImageIcon,
  FormatAlignLeft as AlignLeftIcon,
  FormatAlignCenter as AlignCenterIcon,
  FormatAlignRight as AlignRightIcon,
  FormatAlignJustify as AlignJustifyIcon,
  FormatColorText as TextColorIcon,
  FormatColorFill as BackgroundColorIcon,
  Title as HeadingIcon,
  FormatClear as ClearFormattingIcon,
  Undo as UndoIcon,
  Redo as RedoIcon
} from '@mui/icons-material';
import { Editor, EditorState, RichUtils, convertToRaw, convertFromHTML, ContentState, Modifier, AtomicBlockUtils } from 'draft-js';
import { stateToHTML } from 'draft-js-export-html';
import 'draft-js/dist/Draft.css';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minHeight?: number;
  maxHeight?: number;
  readOnly?: boolean;
}

/**
 * Rich Text Editor Component
 * 
 * This component provides a WYSIWYG editor for rich text content.
 * It supports formatting, lists, links, images, and more.
 */
const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Enter content...',
  minHeight = 200,
  maxHeight = 500,
  readOnly = false
}) => {
  // Editor state
  const [editorState, setEditorState] = useState(() => {
    if (value) {
      const blocksFromHTML = convertFromHTML(value);
      const contentState = ContentState.createFromBlockArray(
        blocksFromHTML.contentBlocks,
        blocksFromHTML.entityMap
      );
      return EditorState.createWithContent(contentState);
    }
    return EditorState.createEmpty();
  });
  
  // UI state
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [linkText, setLinkText] = useState('');
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [colorMenuAnchor, setColorMenuAnchor] = useState<null | HTMLElement>(null);
  const [colorType, setColorType] = useState<'text' | 'background'>('text');
  const [headingMenuAnchor, setHeadingMenuAnchor] = useState<null | HTMLElement>(null);
  
  // Editor ref
  const editorRef = useRef<Editor>(null);
  
  // Update HTML value when editor state changes
  useEffect(() => {
    const contentState = editorState.getCurrentContent();
    const html = stateToHTML(contentState);
    
    if (html !== value) {
      onChange(html);
    }
  }, [editorState, onChange, value]);
  
  // Update editor state when value changes externally
  useEffect(() => {
    if (!value) {
      setEditorState(EditorState.createEmpty());
      return;
    }
    
    const currentContent = editorState.getCurrentContent();
    const currentHtml = stateToHTML(currentContent);
    
    if (value !== currentHtml) {
      const blocksFromHTML = convertFromHTML(value);
      const contentState = ContentState.createFromBlockArray(
        blocksFromHTML.contentBlocks,
        blocksFromHTML.entityMap
      );
      setEditorState(EditorState.createWithContent(contentState));
    }
  }, [value]);
  
  // Handle editor state change
  const handleEditorChange = (state: EditorState) => {
    setEditorState(state);
  };
  
  // Focus the editor
  const focusEditor = () => {
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };
  
  // Toggle inline style
  const toggleInlineStyle = (style: string) => {
    handleEditorChange(RichUtils.toggleInlineStyle(editorState, style));
  };
  
  // Toggle block type
  const toggleBlockType = (blockType: string) => {
    handleEditorChange(RichUtils.toggleBlockType(editorState, blockType));
  };
  
  // Handle key command
  const handleKeyCommand = (command: string, editorState: EditorState) => {
    const newState = RichUtils.handleKeyCommand(editorState, command);
    
    if (newState) {
      handleEditorChange(newState);
      return 'handled';
    }
    
    return 'not-handled';
  };
  
  // Add link
  const addLink = () => {
    const selection = editorState.getSelection();
    const contentState = editorState.getCurrentContent();
    const contentStateWithEntity = contentState.createEntity(
      'LINK',
      'MUTABLE',
      { url: linkUrl }
    );
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
    
    let nextEditorState;
    
    if (selection.isCollapsed()) {
      // If no text is selected, insert the link text
      const textWithEntity = Modifier.insertText(
        contentState,
        selection,
        linkText,
        undefined,
        entityKey
      );
      nextEditorState = EditorState.push(
        editorState,
        textWithEntity,
        'insert-characters'
      );
    } else {
      // If text is selected, apply the link to the selection
      nextEditorState = EditorState.push(
        editorState,
        contentStateWithEntity,
        'apply-entity'
      );
      nextEditorState = RichUtils.toggleLink(
        nextEditorState,
        nextEditorState.getSelection(),
        entityKey
      );
    }
    
    handleEditorChange(nextEditorState);
    setLinkDialogOpen(false);
    setLinkUrl('');
    setLinkText('');
  };
  
  // Add image
  const addImage = () => {
    const contentState = editorState.getCurrentContent();
    const contentStateWithEntity = contentState.createEntity(
      'IMAGE',
      'IMMUTABLE',
      { src: imageUrl, alt: imageAlt }
    );
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
    const newEditorState = EditorState.set(
      editorState,
      { currentContent: contentStateWithEntity }
    );
    
    handleEditorChange(AtomicBlockUtils.insertAtomicBlock(
      newEditorState,
      entityKey,
      ' '
    ));
    
    setImageDialogOpen(false);
    setImageUrl('');
    setImageAlt('');
  };
  
  // Apply text color
  const applyColor = (color: string) => {
    const style = colorType === 'text' ? `color-${color}` : `bgcolor-${color}`;
    toggleInlineStyle(style);
    setColorMenuAnchor(null);
  };
  
  // Apply heading
  const applyHeading = (level: string) => {
    toggleBlockType(level);
    setHeadingMenuAnchor(null);
  };
  
  // Clear formatting
  const clearFormatting = () => {
    const selection = editorState.getSelection();
    const contentState = editorState.getCurrentContent();
    const styles = editorState.getCurrentInlineStyle();
    
    // Remove all inline styles
    let nextContentState = contentState;
    styles.forEach(style => {
      nextContentState = Modifier.removeInlineStyle(
        nextContentState,
        selection,
        style
      );
    });
    
    // Convert block type to unstyled
    const blockType = RichUtils.getCurrentBlockType(editorState);
    if (blockType !== 'unstyled') {
      nextContentState = Modifier.setBlockType(
        nextContentState,
        selection,
        'unstyled'
      );
    }
    
    // Update editor state
    const nextEditorState = EditorState.push(
      editorState,
      nextContentState,
      'change-block-type'
    );
    
    handleEditorChange(nextEditorState);
  };
  
  // Undo
  const handleUndo = () => {
    handleEditorChange(EditorState.undo(editorState));
  };
  
  // Redo
  const handleRedo = () => {
    handleEditorChange(EditorState.redo(editorState));
  };
  
  // Get current inline style
  const currentInlineStyle = editorState.getCurrentInlineStyle();
  
  // Get current block type
  const currentBlockType = RichUtils.getCurrentBlockType(editorState);
  
  // Color options
  const colors = [
    { name: 'Red', value: '#f44336' },
    { name: 'Pink', value: '#e91e63' },
    { name: 'Purple', value: '#9c27b0' },
    { name: 'Deep Purple', value: '#673ab7' },
    { name: 'Indigo', value: '#3f51b5' },
    { name: 'Blue', value: '#2196f3' },
    { name: 'Light Blue', value: '#03a9f4' },
    { name: 'Cyan', value: '#00bcd4' },
    { name: 'Teal', value: '#009688' },
    { name: 'Green', value: '#4caf50' },
    { name: 'Light Green', value: '#8bc34a' },
    { name: 'Lime', value: '#cddc39' },
    { name: 'Yellow', value: '#ffeb3b' },
    { name: 'Amber', value: '#ffc107' },
    { name: 'Orange', value: '#ff9800' },
    { name: 'Deep Orange', value: '#ff5722' },
    { name: 'Brown', value: '#795548' },
    { name: 'Grey', value: '#9e9e9e' },
    { name: 'Blue Grey', value: '#607d8b' },
    { name: 'Black', value: '#000000' }
  ];
  
  return (
    <Box sx={{ border: '1px solid #ddd', borderRadius: 1, overflow: 'hidden' }}>
      {/* Toolbar */}
      {!readOnly && (
        <>
          <Toolbar variant="dense" sx={{ bgcolor: 'background.paper' }}>
            {/* Text Formatting */}
            <ToggleButtonGroup size="small" sx={{ mr: 1 }}>
              <ToggleButton
                value="bold"
                selected={currentInlineStyle.has('BOLD')}
                onClick={() => toggleInlineStyle('BOLD')}
              >
                <Tooltip title="Bold">
                  <BoldIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="italic"
                selected={currentInlineStyle.has('ITALIC')}
                onClick={() => toggleInlineStyle('ITALIC')}
              >
                <Tooltip title="Italic">
                  <ItalicIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="underline"
                selected={currentInlineStyle.has('UNDERLINE')}
                onClick={() => toggleInlineStyle('UNDERLINE')}
              >
                <Tooltip title="Underline">
                  <UnderlineIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="strikethrough"
                selected={currentInlineStyle.has('STRIKETHROUGH')}
                onClick={() => toggleInlineStyle('STRIKETHROUGH')}
              >
                <Tooltip title="Strikethrough">
                  <StrikethroughIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Lists */}
            <ToggleButtonGroup size="small" sx={{ mr: 1 }}>
              <ToggleButton
                value="unordered-list"
                selected={currentBlockType === 'unordered-list-item'}
                onClick={() => toggleBlockType('unordered-list-item')}
              >
                <Tooltip title="Bullet List">
                  <BulletListIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="ordered-list"
                selected={currentBlockType === 'ordered-list-item'}
                onClick={() => toggleBlockType('ordered-list-item')}
              >
                <Tooltip title="Numbered List">
                  <NumberedListIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Alignment */}
            <ToggleButtonGroup size="small" sx={{ mr: 1 }}>
              <ToggleButton
                value="left"
                selected={currentBlockType === 'left-align'}
                onClick={() => toggleBlockType('left-align')}
              >
                <Tooltip title="Align Left">
                  <AlignLeftIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="center"
                selected={currentBlockType === 'center-align'}
                onClick={() => toggleBlockType('center-align')}
              >
                <Tooltip title="Align Center">
                  <AlignCenterIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="right"
                selected={currentBlockType === 'right-align'}
                onClick={() => toggleBlockType('right-align')}
              >
                <Tooltip title="Align Right">
                  <AlignRightIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="justify"
                selected={currentBlockType === 'justify-align'}
                onClick={() => toggleBlockType('justify-align')}
              >
                <Tooltip title="Justify">
                  <AlignJustifyIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Special Blocks */}
            <ToggleButtonGroup size="small" sx={{ mr: 1 }}>
              <ToggleButton
                value="blockquote"
                selected={currentBlockType === 'blockquote'}
                onClick={() => toggleBlockType('blockquote')}
              >
                <Tooltip title="Quote">
                  <QuoteIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton
                value="code-block"
                selected={currentBlockType === 'code-block'}
                onClick={() => toggleBlockType('code-block')}
              >
                <Tooltip title="Code Block">
                  <CodeIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Headings */}
            <Tooltip title="Headings">
              <IconButton
                size="small"
                onClick={(e) => setHeadingMenuAnchor(e.currentTarget)}
              >
                <HeadingIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            {/* Colors */}
            <Tooltip title="Text Color">
              <IconButton
                size="small"
                onClick={(e) => {
                  setColorMenuAnchor(e.currentTarget);
                  setColorType('text');
                }}
              >
                <TextColorIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Background Color">
              <IconButton
                size="small"
                onClick={(e) => {
                  setColorMenuAnchor(e.currentTarget);
                  setColorType('background');
                }}
              >
                <BackgroundColorIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Links and Images */}
            <Tooltip title="Insert Link">
              <IconButton
                size="small"
                onClick={() => setLinkDialogOpen(true)}
              >
                <LinkIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Insert Image">
              <IconButton
                size="small"
                onClick={() => setImageDialogOpen(true)}
              >
                <ImageIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Clear Formatting */}
            <Tooltip title="Clear Formatting">
              <IconButton
                size="small"
                onClick={clearFormatting}
              >
                <ClearFormattingIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Box sx={{ flexGrow: 1 }} />
            
            {/* Undo/Redo */}
            <Tooltip title="Undo">
              <IconButton
                size="small"
                onClick={handleUndo}
                disabled={editorState.getUndoStack().size === 0}
              >
                <UndoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Redo">
              <IconButton
                size="small"
                onClick={handleRedo}
                disabled={editorState.getRedoStack().size === 0}
              >
                <RedoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Toolbar>
          
          <Divider />
        </>
      )}
      
      {/* Editor */}
      <Box
        sx={{
          p: 2,
          minHeight,
          maxHeight: maxHeight || 'none',
          overflow: 'auto',
          bgcolor: 'background.paper',
          '& .DraftEditor-root': {
            height: '100%',
          },
          '& .public-DraftEditorPlaceholder-root': {
            color: 'text.disabled',
          },
        }}
        onClick={focusEditor}
      >
        <Editor
          ref={editorRef}
          editorState={editorState}
          onChange={handleEditorChange}
          handleKeyCommand={handleKeyCommand}
          placeholder={placeholder}
          readOnly={readOnly}
        />
      </Box>
      
      {/* Link Dialog */}
      <Dialog
        open={linkDialogOpen}
        onClose={() => setLinkDialogOpen(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Insert Link</DialogTitle>
        <DialogContent>
          <TextField
            label="URL"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            placeholder="https://example.com"
          />
          <TextField
            label="Text"
            value={linkText}
            onChange={(e) => setLinkText(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            placeholder="Link text"
            helperText="Leave empty to use the selected text"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLinkDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={addLink}
            variant="contained"
            disabled={!linkUrl}
          >
            Insert
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Image Dialog */}
      <Dialog
        open={imageDialogOpen}
        onClose={() => setImageDialogOpen(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Insert Image</DialogTitle>
        <DialogContent>
          <TextField
            label="Image URL"
            value={imageUrl}
            onChange={(e) => setImageUrl(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            placeholder="https://example.com/image.jpg"
          />
          <TextField
            label="Alt Text"
            value={imageAlt}
            onChange={(e) => setImageAlt(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            placeholder="Image description"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImageDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={addImage}
            variant="contained"
            disabled={!imageUrl}
          >
            Insert
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Color Menu */}
      <Menu
        anchorEl={colorMenuAnchor}
        open={Boolean(colorMenuAnchor)}
        onClose={() => setColorMenuAnchor(null)}
      >
        <Box sx={{ p: 1, width: 220 }}>
          <Typography variant="subtitle2" gutterBottom>
            {colorType === 'text' ? 'Text Color' : 'Background Color'}
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {colors.map((color) => (
              <Tooltip key={color.value} title={color.name}>
                <Box
                  sx={{
                    width: 24,
                    height: 24,
                    bgcolor: color.value,
                    borderRadius: 0.5,
                    cursor: 'pointer',
                    border: '1px solid #ddd',
                    '&:hover': {
                      opacity: 0.8,
                    },
                  }}
                  onClick={() => applyColor(color.value.replace('#', ''))}
                />
              </Tooltip>
            ))}
          </Box>
        </Box>
      </Menu>
      
      {/* Heading Menu */}
      <Menu
        anchorEl={headingMenuAnchor}
        open={Boolean(headingMenuAnchor)}
        onClose={() => setHeadingMenuAnchor(null)}
      >
        <MenuItem onClick={() => applyHeading('header-one')}>
          <Typography variant="h6">Heading 1</Typography>
        </MenuItem>
        <MenuItem onClick={() => applyHeading('header-two')}>
          <Typography variant="h6" sx={{ fontSize: '1.25rem' }}>Heading 2</Typography>
        </MenuItem>
        <MenuItem onClick={() => applyHeading('header-three')}>
          <Typography variant="h6" sx={{ fontSize: '1.1rem' }}>Heading 3</Typography>
        </MenuItem>
        <MenuItem onClick={() => applyHeading('header-four')}>
          <Typography variant="h6" sx={{ fontSize: '1rem' }}>Heading 4</Typography>
        </MenuItem>
        <MenuItem onClick={() => applyHeading('header-five')}>
          <Typography variant="h6" sx={{ fontSize: '0.9rem' }}>Heading 5</Typography>
        </MenuItem>
        <MenuItem onClick={() => applyHeading('header-six')}>
          <Typography variant="h6" sx={{ fontSize: '0.8rem' }}>Heading 6</Typography>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => applyHeading('unstyled')}>
          <Typography variant="body1">Normal Text</Typography>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default RichTextEditor;
