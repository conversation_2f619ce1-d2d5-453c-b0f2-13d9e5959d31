import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  Divider,
  Box,
  Chip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { KeyboardShortcut, formatKeyCombination } from '../../utils/keyboard-shortcuts';

interface KeyboardShortcutsDialogProps {
  open: boolean;
  onClose: () => void;
  shortcuts: KeyboardShortcut[];
}

/**
 * Keyboard Shortcuts Dialog Component
 * 
 * This component displays a dialog with keyboard shortcuts and their descriptions.
 */
const KeyboardShortcutsDialog: React.FC<KeyboardShortcutsDialogProps> = ({
  open,
  onClose,
  shortcuts
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // Group shortcuts by category
  const categories = {
    'General': shortcuts.filter(s => 
      s.description.includes('Save') || 
      s.description.includes('Preview') || 
      s.description.includes('Close')
    ),
    'Editing': shortcuts.filter(s => 
      s.description.includes('section') || 
      s.description.includes('content')
    ),
    'Actions': shortcuts.filter(s => 
      s.description.includes('Download') || 
      s.description.includes('Send')
    ),
    'Other': shortcuts.filter(s => 
      !s.description.includes('Save') && 
      !s.description.includes('Preview') && 
      !s.description.includes('Close') &&
      !s.description.includes('section') && 
      !s.description.includes('content') &&
      !s.description.includes('Download') && 
      !s.description.includes('Send')
    )
  };
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      aria-labelledby="keyboard-shortcuts-dialog-title"
    >
      <DialogTitle id="keyboard-shortcuts-dialog-title">
        Keyboard Shortcuts
      </DialogTitle>
      <DialogContent dividers>
        {Object.entries(categories).map(([category, categoryShortcuts]) => (
          categoryShortcuts.length > 0 && (
            <Box key={category} sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                {category}
              </Typography>
              <Divider sx={{ mb: 1 }} />
              <List dense disablePadding>
                {categoryShortcuts.map((shortcut, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemText
                      primary={shortcut.description}
                      secondary={
                        <Chip
                          label={formatKeyCombination(shortcut.key)}
                          size="small"
                          variant="outlined"
                          sx={{
                            fontFamily: 'monospace',
                            fontWeight: 'bold',
                            mt: 0.5
                          }}
                        />
                      }
                      primaryTypographyProps={{
                        variant: 'body2'
                      }}
                      sx={{
                        display: 'flex',
                        flexDirection: isMobile ? 'column' : 'row',
                        justifyContent: 'space-between',
                        alignItems: isMobile ? 'flex-start' : 'center'
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )
        ))}
        
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Note: On Mac, use ⌘ (Command) instead of Ctrl for most shortcuts.
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default KeyboardShortcutsDialog;
