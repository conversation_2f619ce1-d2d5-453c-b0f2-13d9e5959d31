import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  Typography,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Button,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  DateRange as DateRangeIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Email as EmailIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  AccessTime as TimeIcon,
  BarChart as ChartIcon
} from '@mui/icons-material';
import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, subDays, isAfter, isBefore, parseISO } from 'date-fns';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { useProposals } from '../../hooks/useProposals';
import { IProposal, IProposalAnalyticsEvent } from '../../types/proposals';

interface ProposalAnalyticsDashboardProps {
  tenantId: string;
}

/**
 * Proposal Analytics Dashboard Component
 * 
 * This component provides analytics and insights for proposals.
 * It displays metrics, charts, and tables for proposal performance.
 */
const ProposalAnalyticsDashboard: React.FC<ProposalAnalyticsDashboardProps> = ({
  tenantId
}) => {
  // State for proposals and analytics
  const [proposals, setProposals] = useState<IProposal[]>([]);
  const [analyticsEvents, setAnalyticsEvents] = useState<IProposalAnalyticsEvent[]>([]);
  const [filteredProposals, setFilteredProposals] = useState<IProposal[]>([]);
  
  // State for filters
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    subDays(new Date(), 30),
    new Date()
  ]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get proposals from hook
  const { getProposals } = useProposals();
  
  // Load proposals
  useEffect(() => {
    const loadProposals = async () => {
      setLoading(true);
      try {
        const result = await getProposals({
          limit: 100,
          sortBy: 'createdAt',
          sortOrder: 'desc'
        });
        
        setProposals(result.data);
        
        // Extract all analytics events
        const events: IProposalAnalyticsEvent[] = [];
        result.data.forEach(proposal => {
          if (proposal.analyticsEvents && proposal.analyticsEvents.length > 0) {
            events.push(...proposal.analyticsEvents);
          }
        });
        
        setAnalyticsEvents(events);
      } catch (err) {
        console.error('Error loading proposals:', err);
        setError('Failed to load proposals');
      } finally {
        setLoading(false);
      }
    };
    
    loadProposals();
  }, [getProposals]);
  
  // Apply filters
  useEffect(() => {
    let filtered = [...proposals];
    
    // Apply date range filter
    if (dateRange[0] && dateRange[1]) {
      filtered = filtered.filter(proposal => {
        const createdAt = new Date(proposal.createdAt);
        return isAfter(createdAt, dateRange[0]!) && isBefore(createdAt, dateRange[1]!);
      });
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(proposal => proposal.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(proposal => 
        proposal.title.toLowerCase().includes(query) ||
        (proposal.description && proposal.description.toLowerCase().includes(query))
      );
    }
    
    setFilteredProposals(filtered);
  }, [proposals, dateRange, statusFilter, searchQuery]);
  
  // Calculate metrics
  const totalProposals = filteredProposals.length;
  const sentProposals = filteredProposals.filter(p => p.status === 'sent').length;
  const viewedProposals = filteredProposals.filter(p => p.status === 'viewed').length;
  const acceptedProposals = filteredProposals.filter(p => p.status === 'accepted').length;
  const rejectedProposals = filteredProposals.filter(p => p.status === 'rejected').length;
  
  // Calculate conversion rates
  const viewRate = sentProposals > 0 ? (viewedProposals / sentProposals) * 100 : 0;
  const acceptanceRate = viewedProposals > 0 ? (acceptedProposals / viewedProposals) * 100 : 0;
  
  // Calculate total value
  const totalValue = filteredProposals.reduce((sum, proposal) => {
    return sum + (proposal.pricing?.total || 0);
  }, 0);
  
  // Calculate average time to accept
  const avgTimeToAccept = (() => {
    const acceptedWithDates = filteredProposals.filter(p => 
      p.status === 'accepted' && p.sentAt && p.acceptedAt
    );
    
    if (acceptedWithDates.length === 0) return 0;
    
    const totalHours = acceptedWithDates.reduce((sum, proposal) => {
      const sentAt = new Date(proposal.sentAt!);
      const acceptedAt = new Date(proposal.acceptedAt!);
      const diffMs = acceptedAt.getTime() - sentAt.getTime();
      const diffHours = diffMs / (1000 * 60 * 60);
      return sum + diffHours;
    }, 0);
    
    return totalHours / acceptedWithDates.length;
  })();
  
  // Prepare chart data
  const statusChartData = [
    { name: 'Draft', value: filteredProposals.filter(p => p.status === 'draft').length },
    { name: 'Sent', value: sentProposals },
    { name: 'Viewed', value: viewedProposals },
    { name: 'Accepted', value: acceptedProposals },
    { name: 'Rejected', value: rejectedProposals },
  ];
  
  const COLORS = ['#8884d8', '#83a6ed', '#8dd1e1', '#82ca9d', '#ff8042'];
  
  // Prepare timeline data
  const timelineData = (() => {
    if (dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0];
      const endDate = dateRange[1];
      const diffDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Group by week if more than 30 days
      if (diffDays > 30) {
        // Group by week logic
        return [];
      } else {
        // Group by day
        const data = [];
        for (let i = 0; i < diffDays; i++) {
          const date = new Date(startDate);
          date.setDate(date.getDate() + i);
          const dateStr = format(date, 'yyyy-MM-dd');
          
          const sent = filteredProposals.filter(p => 
            p.sentAt && format(new Date(p.sentAt), 'yyyy-MM-dd') === dateStr
          ).length;
          
          const viewed = filteredProposals.filter(p => 
            p.viewedAt && format(new Date(p.viewedAt), 'yyyy-MM-dd') === dateStr
          ).length;
          
          const accepted = filteredProposals.filter(p => 
            p.acceptedAt && format(new Date(p.acceptedAt), 'yyyy-MM-dd') === dateStr
          ).length;
          
          data.push({
            date: format(date, 'MMM dd'),
            sent,
            viewed,
            accepted,
          });
        }
        return data;
      }
    }
    return [];
  })();
  
  // Handle refresh
  const handleRefresh = async () => {
    setLoading(true);
    try {
      const result = await getProposals({
        limit: 100,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });
      
      setProposals(result.data);
      
      // Extract all analytics events
      const events: IProposalAnalyticsEvent[] = [];
      result.data.forEach(proposal => {
        if (proposal.analyticsEvents && proposal.analyticsEvents.length > 0) {
          events.push(...proposal.analyticsEvents);
        }
      });
      
      setAnalyticsEvents(events);
    } catch (err) {
      console.error('Error refreshing proposals:', err);
      setError('Failed to refresh proposals');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle export
  const handleExport = () => {
    // Create CSV content
    const headers = ['Title', 'Status', 'Created At', 'Sent At', 'Viewed At', 'Accepted At', 'Rejected At', 'Value'];
    const rows = filteredProposals.map(p => [
      p.title,
      p.status,
      p.createdAt ? format(new Date(p.createdAt), 'yyyy-MM-dd HH:mm') : '',
      p.sentAt ? format(new Date(p.sentAt), 'yyyy-MM-dd HH:mm') : '',
      p.viewedAt ? format(new Date(p.viewedAt), 'yyyy-MM-dd HH:mm') : '',
      p.acceptedAt ? format(new Date(p.acceptedAt), 'yyyy-MM-dd HH:mm') : '',
      p.rejectedAt ? format(new Date(p.rejectedAt), 'yyyy-MM-dd HH:mm') : '',
      p.pricing?.total || 0,
    ]);
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `proposal-analytics-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Render loading state
  if (loading && proposals.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={4}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h1">
          Proposal Analytics
        </Typography>
        
        <Box>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExport}
            sx={{ mr: 1 }}
          >
            Export
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>
      
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateRangePicker
                  value={dateRange}
                  onChange={(newValue) => setDateRange(newValue)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: 'outlined',
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <DateRangeIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                />
              </LocalizationProvider>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="sent">Sent</MenuItem>
                  <MenuItem value="viewed">Viewed</MenuItem>
                  <MenuItem value="accepted">Accepted</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search"
                variant="outlined"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by title or description"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      {/* Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Proposals
              </Typography>
              <Typography variant="h4">
                {totalProposals}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                View Rate
              </Typography>
              <Typography variant="h4">
                {viewRate.toFixed(1)}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {viewedProposals} of {sentProposals} sent proposals viewed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Acceptance Rate
              </Typography>
              <Typography variant="h4">
                {acceptanceRate.toFixed(1)}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {acceptedProposals} of {viewedProposals} viewed proposals accepted
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Value
              </Typography>
              <Typography variant="h4">
                ${totalValue.toLocaleString()}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                From {acceptedProposals} accepted proposals
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader title="Proposal Activity" />
            <Divider />
            <CardContent>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={timelineData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Bar dataKey="sent" name="Sent" fill="#8884d8" />
                    <Bar dataKey="viewed" name="Viewed" fill="#82ca9d" />
                    <Bar dataKey="accepted" name="Accepted" fill="#ffc658" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Proposal Status" />
            <Divider />
            <CardContent>
              <Box height={300} display="flex" justifyContent="center" alignItems="center">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={statusChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {statusChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Recent Proposals Table */}
      <Card>
        <CardHeader title="Recent Proposals" />
        <Divider />
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Sent</TableCell>
                <TableCell>Views</TableCell>
                <TableCell>Value</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredProposals.slice(0, 10).map((proposal) => (
                <TableRow key={proposal._id.toString()}>
                  <TableCell>{proposal.title}</TableCell>
                  <TableCell>
                    <Chip
                      label={proposal.status?.toUpperCase() || 'DRAFT'}
                      color={
                        proposal.status === 'accepted' ? 'success' :
                        proposal.status === 'rejected' ? 'error' :
                        proposal.status === 'sent' ? 'primary' :
                        proposal.status === 'viewed' ? 'info' :
                        'default'
                      }
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {format(new Date(proposal.createdAt), 'MMM dd, yyyy')}
                  </TableCell>
                  <TableCell>
                    {proposal.sentAt ? format(new Date(proposal.sentAt), 'MMM dd, yyyy') : '-'}
                  </TableCell>
                  <TableCell>
                    {proposal.viewCount || 0}
                  </TableCell>
                  <TableCell>
                    ${proposal.pricing?.total?.toLocaleString() || '0'}
                  </TableCell>
                </TableRow>
              ))}
              
              {filteredProposals.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No proposals found matching the filters
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>
    </Box>
  );
};

export default ProposalAnalyticsDashboard;
