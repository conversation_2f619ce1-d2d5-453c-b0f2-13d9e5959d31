import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Divider,
  Grid,
  IconButton,
  TextField,
  Typography,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  Tabs,
  Tab,
  Paper,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Save as SaveIcon,
  Download as DownloadIcon,
  Send as SendIcon,
  ContentCopy as DuplicateIcon,
  ArrowUpward as MoveUpIcon,
  ArrowDownward as MoveDownIcon,
  AutoAwesome as AIIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useProposals } from '../../hooks/useProposals';
import { IProposal, IProposalSection, ProposalDownloadOptions } from '../../types/proposals';
import ProposalSectionEditor from './ProposalSectionEditor';
import ProposalPreview from './ProposalPreview';
import ProposalPricingEditor from './ProposalPricingEditor';
import ProposalAIGenerator from './ProposalAIGenerator';
import ProposalSendDialog from './ProposalSendDialog';
import { v4 as uuidv4 } from 'uuid';

interface ProposalEditorProps {
  proposalId?: string;
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
  onSave?: (proposal: IProposal) => void;
  onCancel?: () => void;
}

/**
 * Proposal Editor Component
 *
 * This component provides a UI for creating and editing proposals.
 * It includes section management, real-time preview, and AI generation.
 */
const ProposalEditor: React.FC<ProposalEditorProps> = ({
  proposalId,
  opportunityId,
  companyId,
  contactIds,
  onSave,
  onCancel
}) => {
  // State for the proposal
  const [proposal, setProposal] = useState<Partial<IProposal>>({
    title: '',
    description: '',
    sections: [],
    status: 'draft',
    downloadEnabled: true,
    downloadFormats: ['pdf', 'docx', 'md'],
    publicAccessEnabled: true,
    emailEnabled: true,
  });

  // State for UI
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [aiDialogOpen, setAiDialogOpen] = useState(false);
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [downloadMenuAnchor, setDownloadMenuAnchor] = useState<null | HTMLElement>(null);
  const [sectionMenuAnchor, setSectionMenuAnchor] = useState<null | HTMLElement>(null);
  const [activeSectionId, setActiveSectionId] = useState<string | null>(null);

  // Get proposal methods from hook
  const {
    getProposalById,
    createProposal,
    updateProposal,
    downloadProposal
  } = useProposals();

  // Load proposal if editing an existing one
  useEffect(() => {
    const loadProposal = async () => {
      if (proposalId) {
        setLoading(true);
        try {
          const loadedProposal = await getProposalById(proposalId);
          setProposal(loadedProposal);
        } catch (err) {
          setError('Failed to load proposal');
          console.error(err);
        } finally {
          setLoading(false);
        }
      } else if (opportunityId || companyId || contactIds) {
        // Initialize with related entities if creating a new proposal
        setProposal(prev => ({
          ...prev,
          opportunityId,
          companyId,
          contactIds,
        }));
      }
    };

    loadProposal();
  }, [proposalId, opportunityId, companyId, contactIds, getProposalById]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle proposal field changes
  const handleProposalChange = (field: string, value: any) => {
    setProposal(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Add a new section
  const handleAddSection = () => {
    const newSection: IProposalSection = {
      id: uuidv4(),
      title: 'New Section',
      content: '',
      order: proposal.sections?.length || 0,
      type: 'text',
      isVisible: true
    };

    setProposal(prev => ({
      ...prev,
      sections: [...(prev.sections || []), newSection]
    }));
  };

  // Update a section
  const handleUpdateSection = (sectionId: string, updates: Partial<IProposalSection>) => {
    setProposal(prev => ({
      ...prev,
      sections: prev.sections?.map(section =>
        section.id === sectionId ? { ...section, ...updates } : section
      )
    }));
  };

  // Delete a section
  const handleDeleteSection = (sectionId: string) => {
    setProposal(prev => ({
      ...prev,
      sections: prev.sections?.filter(section => section.id !== sectionId)
        .map((section, index) => ({ ...section, order: index }))
    }));
  };

  // Duplicate a section
  const handleDuplicateSection = (sectionId: string) => {
    const sectionToDuplicate = proposal.sections?.find(section => section.id === sectionId);

    if (sectionToDuplicate) {
      const newSection: IProposalSection = {
        ...sectionToDuplicate,
        id: uuidv4(),
        title: `${sectionToDuplicate.title} (Copy)`,
        order: (proposal.sections?.length || 0)
      };

      setProposal(prev => ({
        ...prev,
        sections: [...(prev.sections || []), newSection]
      }));
    }
  };

  // Toggle section visibility
  const handleToggleSectionVisibility = (sectionId: string) => {
    setProposal(prev => ({
      ...prev,
      sections: prev.sections?.map(section =>
        section.id === sectionId
          ? { ...section, isVisible: !section.isVisible }
          : section
      )
    }));
  };

  // Move section up
  const handleMoveSectionUp = (sectionId: string) => {
    const sectionIndex = proposal.sections?.findIndex(section => section.id === sectionId) || 0;

    if (sectionIndex > 0) {
      const newSections = [...(proposal.sections || [])];
      const temp = newSections[sectionIndex];
      newSections[sectionIndex] = newSections[sectionIndex - 1];
      newSections[sectionIndex - 1] = temp;

      // Update order
      const updatedSections = newSections.map((section, index) => ({
        ...section,
        order: index
      }));

      setProposal(prev => ({
        ...prev,
        sections: updatedSections
      }));
    }
  };

  // Move section down
  const handleMoveSectionDown = (sectionId: string) => {
    const sectionIndex = proposal.sections?.findIndex(section => section.id === sectionId) || 0;

    if (sectionIndex < (proposal.sections?.length || 0) - 1) {
      const newSections = [...(proposal.sections || [])];
      const temp = newSections[sectionIndex];
      newSections[sectionIndex] = newSections[sectionIndex + 1];
      newSections[sectionIndex + 1] = temp;

      // Update order
      const updatedSections = newSections.map((section, index) => ({
        ...section,
        order: index
      }));

      setProposal(prev => ({
        ...prev,
        sections: updatedSections
      }));
    }
  };

  // Handle drag and drop reordering
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(proposal.sections || []);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order
    const updatedSections = items.map((section, index) => ({
      ...section,
      order: index
    }));

    setProposal(prev => ({
      ...prev,
      sections: updatedSections
    }));
  };

  // Save the proposal
  const handleSave = async () => {
    setSaving(true);
    setError(null);

    try {
      let savedProposal;

      if (proposalId) {
        // Update existing proposal
        savedProposal = await updateProposal(proposalId, proposal);
        setSuccess('Proposal updated successfully');
      } else {
        // Create new proposal
        savedProposal = await createProposal(proposal);
        setSuccess('Proposal created successfully');
      }

      if (onSave) {
        onSave(savedProposal);
      }
    } catch (err) {
      setError('Failed to save proposal');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  // Handle AI generation
  const handleAIGeneration = (generatedProposal: Partial<IProposal>) => {
    setProposal(prev => ({
      ...prev,
      ...generatedProposal,
      // Preserve existing ID and metadata if editing
      ...(proposalId ? { _id: proposalId } : {})
    }));
    setAiDialogOpen(false);
  };

  // Handle download
  const handleDownload = async (format: 'pdf' | 'docx' | 'md') => {
    setDownloadMenuAnchor(null);

    if (!proposalId) {
      setError('Please save the proposal before downloading');
      return;
    }

    try {
      setLoading(true);

      const options: ProposalDownloadOptions = {
        format,
        paperSize: 'a4',
        colorScheme: 'professional',
        includeHeader: true,
        includeFooter: true,
        includePageNumbers: true
      };

      const blob = await downloadProposal(proposalId, options);

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${proposal.title || 'proposal'}.${format}`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccess(`Proposal downloaded as ${format.toUpperCase()}`);
    } catch (err) {
      setError(`Failed to download proposal as ${format.toUpperCase()}`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Render the editor
  return (
    <Box sx={{ width: '100%' }}>
      {/* Tabs */}
      <Paper sx={{ mb: 2 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
          variant="fullWidth" // Full width tabs for better mobile experience
        >
          <Tab label="Edit" />
          <Tab label="Preview" />
        </Tabs>
      </Paper>

      {/* Edit Tab */}
      {activeTab === 0 && (
        <Grid container spacing={2}>
          {/* Header */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Proposal Details
                </Typography>
                <TextField
                  label="Title"
                  fullWidth
                  value={proposal.title || ''}
                  onChange={(e) => handleProposalChange('title', e.target.value)}
                  margin="normal"
                  variant="outlined"
                  inputProps={{ 'aria-label': 'Proposal title' }} // Accessibility improvement
                />
                <TextField
                  label="Description"
                  fullWidth
                  multiline
                  rows={3}
                  value={proposal.description || ''}
                  onChange={(e) => handleProposalChange('description', e.target.value)}
                  margin="normal"
                  variant="outlined"
                  inputProps={{ 'aria-label': 'Proposal description' }} // Accessibility improvement
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Sections */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box
                  display="flex"
                  flexDirection={{ xs: 'column', sm: 'row' }} // Stack vertically on mobile
                  justifyContent="space-between"
                  alignItems={{ xs: 'flex-start', sm: 'center' }} // Align left on mobile
                  mb={2}
                  gap={1} // Add gap for spacing when stacked
                >
                  <Typography variant="h6">
                    Sections
                  </Typography>
                  <Box
                    display="flex"
                    flexDirection={{ xs: 'column', sm: 'row' }} // Stack buttons on mobile
                    width={{ xs: '100%', sm: 'auto' }} // Full width on mobile
                    gap={1} // Add gap between buttons
                  >
                    <Button
                      startIcon={<AIIcon />}
                      variant="outlined"
                      color="secondary"
                      onClick={() => setAiDialogOpen(true)}
                      fullWidth={{ xs: true, sm: false }} // Full width on mobile
                      aria-label="Generate with AI" // Accessibility improvement
                    >
                      AI Generate
                    </Button>
                    <Button
                      startIcon={<AddIcon />}
                      variant="contained"
                      color="primary"
                      onClick={handleAddSection}
                      fullWidth={{ xs: true, sm: false }} // Full width on mobile
                      aria-label="Add section" // Accessibility improvement
                    >
                      Add Section
                    </Button>
                  </Box>
                </Box>

                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="sections">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                      >
                        {proposal.sections?.sort((a, b) => a.order - b.order).map((section, index) => (
                          <Draggable key={section.id} draggableId={section.id} index={index}>
                            {(provided) => (
                              <Card
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                sx={{ mb: 2, border: '1px solid #e0e0e0' }}
                              >
                                <CardContent>
                                  <Box display="flex" alignItems="center">
                                    <Box {...provided.dragHandleProps} sx={{ mr: 1, cursor: 'grab' }}>
                                      <DragIndicatorIcon />
                                    </Box>
                                    <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                                      {section.title}
                                      {!section.isVisible && (
                                        <Typography component="span" color="text.secondary" sx={{ ml: 1 }}>
                                          (Hidden)
                                        </Typography>
                                      )}
                                    </Typography>
                                    <Box>
                                      <Tooltip title="Move Up">
                                        <IconButton
                                          size="small"
                                          onClick={() => handleMoveSectionUp(section.id)}
                                          disabled={index === 0}
                                        >
                                          <MoveUpIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                      <Tooltip title="Move Down">
                                        <IconButton
                                          size="small"
                                          onClick={() => handleMoveSectionDown(section.id)}
                                          disabled={index === (proposal.sections?.length || 0) - 1}
                                        >
                                          <MoveDownIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                      <Tooltip title={section.isVisible ? "Hide Section" : "Show Section"}>
                                        <IconButton
                                          size="small"
                                          onClick={() => handleToggleSectionVisibility(section.id)}
                                        >
                                          {section.isVisible ? (
                                            <VisibilityIcon fontSize="small" />
                                          ) : (
                                            <VisibilityOffIcon fontSize="small" />
                                          )}
                                        </IconButton>
                                      </Tooltip>
                                      <Tooltip title="Duplicate">
                                        <IconButton
                                          size="small"
                                          onClick={() => handleDuplicateSection(section.id)}
                                        >
                                          <DuplicateIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                      <Tooltip title="Delete">
                                        <IconButton
                                          size="small"
                                          color="error"
                                          onClick={() => handleDeleteSection(section.id)}
                                        >
                                          <DeleteIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                    </Box>
                                  </Box>
                                  <Divider sx={{ my: 1 }} />
                                  <ProposalSectionEditor
                                    section={section}
                                    onChange={(updates) => handleUpdateSection(section.id, updates)}
                                  />
                                </CardContent>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>

                {(!proposal.sections || proposal.sections.length === 0) && (
                  <Typography color="text.secondary" align="center" sx={{ py: 4 }}>
                    No sections yet. Click "Add Section" to create one or use "AI Generate" to create a complete proposal.
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Pricing */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Pricing
                </Typography>
                <ProposalPricingEditor
                  pricing={proposal.pricing}
                  onChange={(pricing) => handleProposalChange('pricing', pricing)}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Terms */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Terms & Conditions
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={6}
                  value={proposal.terms || ''}
                  onChange={(e) => handleProposalChange('terms', e.target.value)}
                  margin="normal"
                  variant="outlined"
                  placeholder="Enter terms and conditions..."
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Actions */}
          <Grid item xs={12}>
            <Box
              display="flex"
              flexDirection={{ xs: 'column', sm: 'row' }} // Stack vertically on mobile
              justifyContent="space-between"
              gap={2} // Add gap for spacing when stacked
            >
              <Button
                variant="outlined"
                onClick={onCancel}
                fullWidth={{ xs: true, sm: false }} // Full width on mobile
                aria-label="Cancel editing" // Accessibility improvement
              >
                Cancel
              </Button>
              <Box
                display="flex"
                flexDirection={{ xs: 'column', sm: 'row' }} // Stack buttons on mobile
                width={{ xs: '100%', sm: 'auto' }} // Full width on mobile
                gap={2} // Add gap between buttons
              >
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={(e) => setDownloadMenuAnchor(e.currentTarget)}
                  disabled={!proposalId}
                  fullWidth={{ xs: true, sm: false }} // Full width on mobile
                  aria-label="Download proposal" // Accessibility improvement
                >
                  Download
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<SendIcon />}
                  onClick={() => setSendDialogOpen(true)}
                  disabled={!proposalId}
                  fullWidth={{ xs: true, sm: false }} // Full width on mobile
                  aria-label="Send proposal" // Accessibility improvement
                >
                  Send
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                  disabled={saving}
                  fullWidth={{ xs: true, sm: false }} // Full width on mobile
                  aria-label="Save proposal" // Accessibility improvement
                >
                  {saving ? 'Saving...' : 'Save'}
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      )}

      {/* Preview Tab */}
      {activeTab === 1 && (
        <ProposalPreview proposal={proposal as IProposal} />
      )}

      {/* AI Generation Dialog */}
      <Dialog
        open={aiDialogOpen}
        onClose={() => setAiDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Generate Proposal with AI</DialogTitle>
        <DialogContent>
          <ProposalAIGenerator
            opportunityId={opportunityId}
            companyId={companyId}
            contactIds={contactIds}
            onGenerate={handleAIGeneration}
            onCancel={() => setAiDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Send Dialog */}
      <Dialog
        open={sendDialogOpen}
        onClose={() => setSendDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Send Proposal</DialogTitle>
        <DialogContent>
          {proposalId && (
            <ProposalSendDialog
              proposalId={proposalId}
              onClose={() => setSendDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Download Menu */}
      <Menu
        anchorEl={downloadMenuAnchor}
        open={Boolean(downloadMenuAnchor)}
        onClose={() => setDownloadMenuAnchor(null)}
      >
        <MenuItem onClick={() => handleDownload('pdf')}>PDF</MenuItem>
        <MenuItem onClick={() => handleDownload('docx')}>DOCX</MenuItem>
        <MenuItem onClick={() => handleDownload('md')}>Markdown</MenuItem>
      </Menu>

      {/* Notifications */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>

      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 9999,
          }}
        >
          <CircularProgress color="primary" />
        </Box>
      )}
    </Box>
  );
};

export default ProposalEditor;
