import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@aizako/ui-kit';
import { IProposal } from '../../models/proposal';
import { formatCurrency } from '../../utils/formatters';

interface ProposalViewerProps {
  proposal: IProposal;
  isPublic?: boolean;
  onAccept?: () => void;
  onReject?: () => void;
  onDownload?: (format: string) => void;
}

/**
 * Proposal viewer component
 */
export const ProposalViewer: React.FC<ProposalViewerProps> = ({
  proposal,
  isPublic = false,
  onAccept,
  onReject,
  onDownload,
}) => {
  const [activeTab, setActiveTab] = useState('proposal');
  
  // Filter sections to only show visible ones
  const visibleSections = proposal.sections.filter(section => section.isVisible);
  
  // Format date
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString();
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold">{proposal.title}</h1>
          {proposal.description && (
            <p className="text-gray-600 mt-2">{proposal.description}</p>
          )}
        </div>
        
        {!isPublic && (
          <div className="flex space-x-2">
            {proposal.downloadEnabled && proposal.downloadFormats && proposal.downloadFormats.length > 0 && (
              <div className="relative inline-block">
                <Button variant="outline">
                  Download
                </Button>
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden group-hover:block">
                  <div className="py-1">
                    {proposal.downloadFormats.includes('pdf') && (
                      <button
                        onClick={() => onDownload && onDownload('pdf')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Download as PDF
                      </button>
                    )}
                    {proposal.downloadFormats.includes('docx') && (
                      <button
                        onClick={() => onDownload && onDownload('docx')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Download as DOCX
                      </button>
                    )}
                    {proposal.downloadFormats.includes('md') && (
                      <button
                        onClick={() => onDownload && onDownload('md')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Download as Markdown
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {isPublic && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            <span>Created: {formatDate(proposal.createdAt)}</span>
            {proposal.expiresAt && (
              <span className="ml-4">Expires: {formatDate(proposal.expiresAt)}</span>
            )}
          </div>
          
          <div className="flex space-x-2">
            {proposal.status === 'sent' && (
              <>
                <Button onClick={onAccept} variant="success">
                  Accept Proposal
                </Button>
                <Button onClick={onReject} variant="destructive">
                  Decline
                </Button>
              </>
            )}
            
            {proposal.downloadEnabled && proposal.downloadFormats && proposal.downloadFormats.length > 0 && (
              <div className="relative inline-block group">
                <Button variant="outline">
                  Download
                </Button>
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden group-hover:block">
                  <div className="py-1">
                    {proposal.downloadFormats.includes('pdf') && (
                      <button
                        onClick={() => onDownload && onDownload('pdf')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Download as PDF
                      </button>
                    )}
                    {proposal.downloadFormats.includes('docx') && (
                      <button
                        onClick={() => onDownload && onDownload('docx')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Download as DOCX
                      </button>
                    )}
                    {proposal.downloadFormats.includes('md') && (
                      <button
                        onClick={() => onDownload && onDownload('md')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Download as Markdown
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      
      {!isPublic && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="proposal">Proposal</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="proposal" className="mt-4">
            <ProposalContent
              proposal={proposal}
              visibleSections={visibleSections}
            />
          </TabsContent>
          
          <TabsContent value="details" className="mt-4">
            <Card className="p-4">
              <h2 className="text-xl font-semibold mb-4">Proposal Details</h2>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Status</h3>
                  <p>{proposal.status}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Created</h3>
                  <p>{formatDate(proposal.createdAt)}</p>
                </div>
                
                {proposal.sentAt && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Sent</h3>
                    <p>{formatDate(proposal.sentAt)}</p>
                  </div>
                )}
                
                {proposal.viewedAt && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">First Viewed</h3>
                    <p>{formatDate(proposal.viewedAt)}</p>
                  </div>
                )}
                
                {proposal.lastViewedAt && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Last Viewed</h3>
                    <p>{formatDate(proposal.lastViewedAt)}</p>
                  </div>
                )}
                
                {proposal.expiresAt && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Expires</h3>
                    <p>{formatDate(proposal.expiresAt)}</p>
                  </div>
                )}
                
                {proposal.acceptedAt && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Accepted</h3>
                    <p>{formatDate(proposal.acceptedAt)}</p>
                  </div>
                )}
                
                {proposal.rejectedAt && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Rejected</h3>
                    <p>{formatDate(proposal.rejectedAt)}</p>
                  </div>
                )}
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">View Count</h3>
                  <p>{proposal.viewCount}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Public Access</h3>
                  <p>{proposal.publicAccessEnabled ? 'Enabled' : 'Disabled'}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Downloads</h3>
                  <p>{proposal.downloadEnabled ? 'Enabled' : 'Disabled'}</p>
                </div>
                
                {proposal.downloadEnabled && proposal.downloadFormats && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Download Formats</h3>
                    <p>{proposal.downloadFormats.join(', ')}</p>
                  </div>
                )}
                
                {proposal.aiGenerated && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium text-gray-500">AI Generated</h3>
                    <p>Yes</p>
                  </div>
                )}
                
                {proposal.notes && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium text-gray-500">Internal Notes</h3>
                    <p>{proposal.notes}</p>
                  </div>
                )}
              </div>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics" className="mt-4">
            <Card className="p-4">
              <h2 className="text-xl font-semibold mb-4">Proposal Analytics</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">View Summary</h3>
                  <p>Total Views: {proposal.viewCount}</p>
                  {proposal.viewedAt && (
                    <p>First Viewed: {formatDate(proposal.viewedAt)}</p>
                  )}
                  {proposal.lastViewedAt && (
                    <p>Last Viewed: {formatDate(proposal.lastViewedAt)}</p>
                  )}
                </div>
                
                {proposal.analyticsEvents && proposal.analyticsEvents.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium">Event Timeline</h3>
                    <div className="space-y-2 mt-2">
                      {proposal.analyticsEvents.map((event, index) => (
                        <div key={index} className="flex items-start">
                          <div className="mr-2 mt-1">
                            <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                          </div>
                          <div>
                            <p className="text-sm">
                              <span className="font-medium">{event.eventType}</span>
                              {' - '}
                              {new Date(event.timestamp).toLocaleString()}
                            </p>
                            {event.duration && (
                              <p className="text-xs text-gray-500">
                                Duration: {event.duration} seconds
                              </p>
                            )}
                            {event.userAgent && (
                              <p className="text-xs text-gray-500">
                                {event.userAgent}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      )}
      
      {isPublic && (
        <ProposalContent
          proposal={proposal}
          visibleSections={visibleSections}
        />
      )}
    </div>
  );
};

interface ProposalContentProps {
  proposal: IProposal;
  visibleSections: IProposal['sections'];
}

/**
 * Proposal content component
 */
const ProposalContent: React.FC<ProposalContentProps> = ({
  proposal,
  visibleSections,
}) => {
  return (
    <div className="space-y-8">
      {visibleSections.map((section) => (
        <div key={section.id} className="space-y-4">
          <h2 className="text-xl font-semibold">{section.title}</h2>
          
          {section.type === 'text' && (
            <div className="prose max-w-none">
              {section.content.split('\n').map((paragraph, i) => (
                <p key={i}>{paragraph}</p>
              ))}
            </div>
          )}
          
          {section.type === 'pricing' && (
            <div>
              <p>{section.content}</p>
            </div>
          )}
          
          {section.type === 'timeline' && (
            <div>
              <p>{section.content}</p>
            </div>
          )}
          
          {section.type === 'team' && (
            <div>
              <p>{section.content}</p>
            </div>
          )}
          
          {section.type === 'testimonials' && (
            <div>
              <p>{section.content}</p>
            </div>
          )}
          
          {section.type === 'images' && (
            <div>
              <p>{section.content}</p>
            </div>
          )}
          
          {section.type === 'custom' && (
            <div>
              <p>{section.content}</p>
            </div>
          )}
        </div>
      ))}
      
      {proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Pricing</h2>
          
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {proposal.pricing.items.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{item.name}</div>
                    {item.description && (
                      <div className="text-sm text-gray-500">{item.description}</div>
                    )}
                    {item.isOptional && (
                      <div className="text-xs text-gray-500 italic">Optional</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatCurrency(item.unitPrice, proposal.pricing.currency)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(item.total, proposal.pricing.currency)}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr>
                <td colSpan={3} className="px-6 py-4 text-sm font-medium text-gray-900 text-right">
                  Subtotal
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatCurrency(proposal.pricing.subtotal, proposal.pricing.currency)}
                </td>
              </tr>
              
              {proposal.pricing.discount && proposal.pricing.discount > 0 && (
                <tr>
                  <td colSpan={3} className="px-6 py-4 text-sm font-medium text-gray-900 text-right">
                    Discount
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {formatCurrency(proposal.pricing.discount, proposal.pricing.currency)}
                  </td>
                </tr>
              )}
              
              {proposal.pricing.tax && proposal.pricing.tax > 0 && (
                <tr>
                  <td colSpan={3} className="px-6 py-4 text-sm font-medium text-gray-900 text-right">
                    Tax
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {formatCurrency(proposal.pricing.tax, proposal.pricing.currency)}
                  </td>
                </tr>
              )}
              
              <tr>
                <td colSpan={3} className="px-6 py-4 text-base font-bold text-gray-900 text-right">
                  Total
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-base font-bold text-gray-900">
                  {formatCurrency(proposal.pricing.total, proposal.pricing.currency)}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      )}
      
      {proposal.terms && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Terms & Conditions</h2>
          <div className="prose max-w-none">
            {proposal.terms.split('\n').map((paragraph, i) => (
              <p key={i}>{paragraph}</p>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProposalViewer;
