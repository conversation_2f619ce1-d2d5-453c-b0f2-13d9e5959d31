import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Tabs,
  Tab,
  Paper,
  CircularProgress,
  Button,
  Chip,
  useTheme,
  useMediaQuery,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
} from '@mui/material';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  DatePicker,
  LocalizationProvider,
} from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, subDays, isAfter, isBefore, parseISO } from 'date-fns';
import { 
  Visibility as ViewIcon, 
  Download as DownloadIcon,
  CheckCircle as AcceptIcon,
  Cancel as RejectIcon,
  TrendingUp as TrendingUpIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { ProposalService } from '../../../services/proposal-service';
import { formatCurrency, formatPercentage } from '../../../utils/formatters';

interface ProposalAnalyticsDashboardProps {
  tenantId: string;
}

/**
 * ProposalAnalyticsDashboard Component
 * 
 * This component displays analytics for proposals, including views, downloads,
 * acceptance rates, and other metrics.
 */
const ProposalAnalyticsDashboard: React.FC<ProposalAnalyticsDashboardProps> = ({
  tenantId,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  
  // State
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analytics, setAnalytics] = useState<any>(null);
  const [dateRange, setDateRange] = useState<{
    startDate: Date;
    endDate: Date;
  }>({
    startDate: subDays(new Date(), 30),
    endDate: new Date(),
  });
  const [filterStatus, setFilterStatus] = useState<string>('all');
  
  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await ProposalService.getTenantAnalytics(
          tenantId,
          dateRange.startDate,
          dateRange.endDate
        );
        
        setAnalytics(response);
      } catch (err) {
        console.error('Error fetching analytics:', err);
        setError('Failed to load analytics data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAnalytics();
  }, [tenantId, dateRange]);
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Handle date range change
  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      setDateRange(prev => ({
        ...prev,
        startDate: date,
      }));
    }
  };
  
  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      setDateRange(prev => ({
        ...prev,
        endDate: date,
      }));
    }
  };
  
  // Handle refresh
  const handleRefresh = () => {
    // Re-fetch analytics data
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await ProposalService.getTenantAnalytics(
          tenantId,
          dateRange.startDate,
          dateRange.endDate
        );
        
        setAnalytics(response);
      } catch (err) {
        console.error('Error fetching analytics:', err);
        setError('Failed to load analytics data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAnalytics();
  };
  
  // Prepare chart data
  const prepareViewsByDateChart = () => {
    if (!analytics || !analytics.viewsByDate) return [];
    
    return Object.entries(analytics.viewsByDate).map(([date, count]) => ({
      date,
      views: count,
    })).sort((a, b) => a.date.localeCompare(b.date));
  };
  
  const prepareProposalsByStatusChart = () => {
    if (!analytics || !analytics.proposalsByStatus) return [];
    
    return Object.entries(analytics.proposalsByStatus).map(([status, count]) => ({
      status,
      count,
    }));
  };
  
  const prepareTopProposalsChart = () => {
    if (!analytics || !analytics.topProposals) return [];
    
    return analytics.topProposals
      .filter((proposal: any) => {
        if (filterStatus === 'all') return true;
        return proposal.status === filterStatus;
      })
      .slice(0, 5)
      .map((proposal: any) => ({
        name: proposal.title,
        views: proposal.views,
        downloads: proposal.downloads,
      }));
  };
  
  // Render loading state
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  // Render empty state
  if (!analytics) {
    return (
      <Alert severity="info">
        No analytics data available. Create and share some proposals to see analytics.
      </Alert>
    );
  }
  
  // Chart colors
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main,
  ];
  
  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          Proposal Analytics
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Track the performance of your proposals and gain insights into customer engagement.
        </Typography>
      </Box>
      
      {/* Date Range Selector */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" flexDirection={{ xs: 'column', sm: 'row' }} alignItems="center" gap={2}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Start Date"
              value={dateRange.startDate}
              onChange={handleStartDateChange}
              slotProps={{ textField: { size: 'small', fullWidth: true } }}
            />
            <DatePicker
              label="End Date"
              value={dateRange.endDate}
              onChange={handleEndDateChange}
              slotProps={{ textField: { size: 'small', fullWidth: true } }}
            />
          </LocalizationProvider>
          <Button 
            variant="contained" 
            onClick={handleRefresh}
            startIcon={<RefreshIcon />}
            sx={{ ml: { xs: 0, sm: 2 } }}
          >
            Refresh
          </Button>
        </Box>
      </Paper>
      
      {/* Key Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Proposals
              </Typography>
              <Typography variant="h4">
                {analytics.totalProposals}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <Chip 
                  size="small" 
                  label={`${analytics.proposalsByStatus?.draft || 0} Draft`} 
                  sx={{ mr: 1 }}
                />
                <Chip 
                  size="small" 
                  label={`${analytics.proposalsByStatus?.sent || 0} Sent`} 
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Views
              </Typography>
              <Typography variant="h4">
                {analytics.totalViews}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <ViewIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {formatPercentage(analytics.viewRate)} View Rate
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Acceptance Rate
              </Typography>
              <Typography variant="h4">
                {formatPercentage(analytics.acceptanceRate)}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <AcceptIcon fontSize="small" color="success" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {analytics.totalAcceptances} Accepted
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Rejection Rate
              </Typography>
              <Typography variant="h4">
                {formatPercentage(analytics.rejectionRate)}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <RejectIcon fontSize="small" color="error" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {analytics.totalRejections} Rejected
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Overview" />
          <Tab label="Proposals" />
          <Tab label="Engagement" />
        </Tabs>
      </Paper>
      
      {/* Tab Content */}
      {activeTab === 0 && (
        <Grid container spacing={3}>
          {/* Views by Date */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Views by Date
                </Typography>
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={prepareViewsByDateChart()}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="views"
                        stroke={theme.palette.primary.main}
                        activeDot={{ r: 8 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Proposals by Status */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Proposals by Status
                </Typography>
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={prepareProposalsByStatusChart()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {prepareProposalsByStatusChart().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 1 && (
        <Grid container spacing={3}>
          {/* Top Proposals */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    Top Proposals
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <InputLabel id="status-filter-label">Status</InputLabel>
                    <Select
                      labelId="status-filter-label"
                      value={filterStatus}
                      label="Status"
                      onChange={(e) => setFilterStatus(e.target.value)}
                    >
                      <MenuItem value="all">All</MenuItem>
                      <MenuItem value="draft">Draft</MenuItem>
                      <MenuItem value="sent">Sent</MenuItem>
                      <MenuItem value="viewed">Viewed</MenuItem>
                      <MenuItem value="accepted">Accepted</MenuItem>
                      <MenuItem value="rejected">Rejected</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
                <Box height={400}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={prepareTopProposalsChart()}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="views" fill={theme.palette.primary.main} name="Views" />
                      <Bar dataKey="downloads" fill={theme.palette.secondary.main} name="Downloads" />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 2 && (
        <Grid container spacing={3}>
          {/* Engagement Metrics */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Engagement Metrics
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center" p={2}>
                      <Typography variant="h4" color="primary">
                        {formatPercentage(analytics.viewRate)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        View Rate
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center" p={2}>
                      <Typography variant="h4" color="secondary">
                        {analytics.totalDownloads}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Downloads
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center" p={2}>
                      <Typography variant="h4" color="success.main">
                        {formatPercentage(analytics.acceptanceRate)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Acceptance Rate
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center" p={2}>
                      <Typography variant="h4" color="error.main">
                        {formatPercentage(analytics.rejectionRate)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Rejection Rate
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ProposalAnalyticsDashboard;
