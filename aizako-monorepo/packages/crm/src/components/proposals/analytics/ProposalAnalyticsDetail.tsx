import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Tabs,
  Tab,
  Paper,
  CircularProgress,
  Button,
  Chip,
  useTheme,
  useMediaQuery,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Bar<PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  Visibility as ViewIcon,
  Download as DownloadIcon,
  CheckCircle as AcceptIcon,
  Cancel as RejectIcon,
  TrendingUp as TrendingUpIcon,
  Public as PublicIcon,
  AccessTime as TimeIcon,
  DevicesOther as DeviceIcon,
  Link as LinkIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { format, formatDistance } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { formatPercentage } from '../../../utils/formatters';

interface ProposalAnalyticsDetailProps {
  proposalId: string;
}

/**
 * ProposalAnalyticsDetail Component
 * 
 * This component displays detailed analytics for a single proposal.
 */
const ProposalAnalyticsDetail: React.FC<ProposalAnalyticsDetailProps> = ({
  proposalId,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // State
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analytics, setAnalytics] = useState<any>(null);
  const [proposal, setProposal] = useState<any>(null);
  
  // Fetch analytics data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposal details
        const proposalData = await ProposalService.getProposalById(proposalId);
        setProposal(proposalData);
        
        // Fetch analytics
        const analyticsData = await ProposalService.getProposalAnalytics(proposalId);
        setAnalytics(analyticsData);
      } catch (err) {
        console.error('Error fetching proposal analytics:', err);
        setError('Failed to load analytics data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [proposalId]);
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposal details
        const proposalData = await ProposalService.getProposalById(proposalId);
        setProposal(proposalData);
        
        // Fetch analytics
        const analyticsData = await ProposalService.getProposalAnalytics(proposalId);
        setAnalytics(analyticsData);
      } catch (err) {
        console.error('Error fetching proposal analytics:', err);
        setError('Failed to load analytics data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Prepare chart data
  const prepareViewsByDateChart = () => {
    if (!analytics || !analytics.viewsByDate) return [];
    
    return Object.entries(analytics.viewsByDate).map(([date, count]) => ({
      date,
      views: count,
    })).sort((a, b) => a.date.localeCompare(b.date));
  };
  
  const prepareViewsByHourChart = () => {
    if (!analytics || !analytics.viewsByHour) return [];
    
    return Object.entries(analytics.viewsByHour).map(([hour, count]) => ({
      hour: `${hour}:00`,
      views: count,
    })).sort((a, b) => a.hour.localeCompare(b.hour));
  };
  
  const prepareViewsByDeviceChart = () => {
    if (!analytics || !analytics.viewsByDevice) return [];
    
    return Object.entries(analytics.viewsByDevice).map(([device, count]) => ({
      device,
      count,
    }));
  };
  
  const prepareDownloadsByFormatChart = () => {
    if (!analytics || !analytics.downloadsByFormat) return [];
    
    return Object.entries(analytics.downloadsByFormat).map(([format, count]) => ({
      format,
      count,
    }));
  };
  
  // Render loading state
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  // Render empty state
  if (!analytics || !proposal) {
    return (
      <Alert severity="info">
        No analytics data available for this proposal.
      </Alert>
    );
  }
  
  // Chart colors
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main,
  ];
  
  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          {proposal.title}
        </Typography>
        <Box display="flex" alignItems="center" flexWrap="wrap" gap={1}>
          <Chip 
            label={proposal.status.toUpperCase()} 
            color={
              proposal.status === 'accepted' ? 'success' :
              proposal.status === 'rejected' ? 'error' :
              proposal.status === 'viewed' ? 'info' :
              proposal.status === 'sent' ? 'primary' :
              'default'
            }
            size="small"
          />
          <Typography variant="body2" color="text.secondary">
            Created: {format(new Date(proposal.createdAt), 'PPP')}
          </Typography>
          {proposal.sentAt && (
            <Typography variant="body2" color="text.secondary">
              Sent: {format(new Date(proposal.sentAt), 'PPP')}
            </Typography>
          )}
          <Button 
            variant="outlined" 
            size="small" 
            onClick={handleRefresh}
            startIcon={<RefreshIcon />}
            sx={{ ml: 'auto' }}
          >
            Refresh
          </Button>
        </Box>
      </Box>
      
      {/* Key Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Total Views
              </Typography>
              <Typography variant="h4">
                {analytics.views}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <ViewIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {analytics.uniqueViews} Unique Views
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Downloads
              </Typography>
              <Typography variant="h4">
                {analytics.downloads}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <DownloadIcon fontSize="small" color="secondary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {Object.keys(analytics.downloadsByFormat || {}).length} Formats
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Average View Time
              </Typography>
              <Typography variant="h4">
                {Math.round(analytics.averageViewDuration / 1000)} sec
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <TimeIcon fontSize="small" color="info" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {analytics.views > 0 ? formatPercentage(analytics.uniqueViews / analytics.views) : '0%'} Return Rate
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Public Link
              </Typography>
              <Box display="flex" alignItems="center">
                <Typography variant="h6" noWrap sx={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {proposal.publicToken}
                </Typography>
                <Tooltip title="Copy Link">
                  <IconButton 
                    size="small" 
                    onClick={() => {
                      navigator.clipboard.writeText(proposal.publicUrl);
                    }}
                  >
                    <LinkIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <Box display="flex" alignItems="center" mt={1}>
                <PublicIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {proposal.publicAccessEnabled ? 'Public Access Enabled' : 'Public Access Disabled'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant={isMobile ? "scrollable" : "fullWidth"}
          scrollButtons={isMobile ? "auto" : undefined}
        >
          <Tab label="Overview" />
          <Tab label="Views" />
          <Tab label="Downloads" />
          <Tab label="Events" />
        </Tabs>
      </Paper>
      
      {/* Tab Content */}
      {activeTab === 0 && (
        <Grid container spacing={3}>
          {/* Views by Date */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Views by Date
                </Typography>
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={prepareViewsByDateChart()}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <RechartsTooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="views"
                        stroke={theme.palette.primary.main}
                        activeDot={{ r: 8 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Views by Hour */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Views by Hour
                </Typography>
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={prepareViewsByHourChart()}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <RechartsTooltip />
                      <Legend />
                      <Bar dataKey="views" fill={theme.palette.primary.main} />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Views by Device */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Views by Device
                </Typography>
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={prepareViewsByDeviceChart()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                        nameKey="device"
                      >
                        {prepareViewsByDeviceChart().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Downloads by Format */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Downloads by Format
                </Typography>
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={prepareDownloadsByFormatChart()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                        nameKey="format"
                      >
                        {prepareDownloadsByFormatChart().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 1 && (
        <Grid container spacing={3}>
          {/* Views List */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  View History
                </Typography>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Date & Time</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Device</TableCell>
                        <TableCell>Referrer</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {analytics.events
                        .filter((event: any) => event.eventType === 'view')
                        .sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                        .map((event: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell>
                              {format(new Date(event.timestamp), 'PPp')}
                            </TableCell>
                            <TableCell>
                              {event.data.location || 'Unknown'}
                            </TableCell>
                            <TableCell>
                              {event.data.userAgent ? 
                                (event.data.userAgent.includes('iPhone') ? 'iPhone' :
                                event.data.userAgent.includes('Android') ? 'Android' :
                                event.data.userAgent.includes('Windows') ? 'Windows' :
                                event.data.userAgent.includes('Mac') ? 'Mac' :
                                'Other') : 'Unknown'}
                            </TableCell>
                            <TableCell>
                              {event.data.referrer || 'Direct'}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 2 && (
        <Grid container spacing={3}>
          {/* Downloads List */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Download History
                </Typography>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Date & Time</TableCell>
                        <TableCell>Format</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Device</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {analytics.events
                        .filter((event: any) => event.eventType === 'download')
                        .sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                        .map((event: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell>
                              {format(new Date(event.timestamp), 'PPp')}
                            </TableCell>
                            <TableCell>
                              {event.data.format || 'Unknown'}
                            </TableCell>
                            <TableCell>
                              {event.data.location || 'Unknown'}
                            </TableCell>
                            <TableCell>
                              {event.data.userAgent ? 
                                (event.data.userAgent.includes('iPhone') ? 'iPhone' :
                                event.data.userAgent.includes('Android') ? 'Android' :
                                event.data.userAgent.includes('Windows') ? 'Windows' :
                                event.data.userAgent.includes('Mac') ? 'Mac' :
                                'Other') : 'Unknown'}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 3 && (
        <Grid container spacing={3}>
          {/* All Events */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  All Events
                </Typography>
                <List>
                  {analytics.events
                    .sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                    .map((event: any, index: number) => (
                      <ListItem key={index} divider={index < analytics.events.length - 1}>
                        <ListItemIcon>
                          {event.eventType === 'view' ? <ViewIcon color="primary" /> :
                           event.eventType === 'download' ? <DownloadIcon color="secondary" /> :
                           event.eventType === 'accept' ? <AcceptIcon color="success" /> :
                           event.eventType === 'reject' ? <RejectIcon color="error" /> :
                           <InfoIcon />}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            event.eventType === 'view' ? 'Proposal Viewed' :
                            event.eventType === 'download' ? `Proposal Downloaded (${event.data.format})` :
                            event.eventType === 'accept' ? 'Proposal Accepted' :
                            event.eventType === 'reject' ? 'Proposal Rejected' :
                            'Event'
                          }
                          secondary={`${format(new Date(event.timestamp), 'PPp')} • ${formatDistance(new Date(event.timestamp), new Date(), { addSuffix: true })}`}
                        />
                        <ListItemSecondaryAction>
                          <Tooltip title={event.data.location || 'Unknown Location'}>
                            <IconButton edge="end" size="small">
                              <PublicIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ProposalAnalyticsDetail;
