import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  IconButton,
  TextField,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  InputAdornment,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { IProposalPricing, IProposalPricingItem } from '../../types/proposals';
import { formatCurrency } from '../../utils/formatters';
import { v4 as uuidv4 } from 'uuid';

interface ProposalPricingEditorProps {
  pricing?: IProposalPricing;
  onChange: (pricing: IProposalPricing) => void;
}

/**
 * Proposal Pricing Editor Component
 * 
 * This component provides a UI for editing proposal pricing.
 * It supports adding, editing, and removing pricing items.
 */
const ProposalPricingEditor: React.FC<ProposalPricingEditorProps> = ({
  pricing,
  onChange
}) => {
  // Initialize pricing with defaults if not provided
  const [pricingState, setPricingState] = useState<IProposalPricing>(
    pricing || {
      currency: 'USD',
      items: [],
      subtotal: 0,
      total: 0
    }
  );
  
  // Update pricing when props change
  useEffect(() => {
    if (pricing) {
      setPricingState(pricing);
    }
  }, [pricing]);
  
  // Calculate totals
  useEffect(() => {
    const subtotal = pricingState.items.reduce((sum, item) => sum + item.total, 0);
    const discount = pricingState.discount || 0;
    const tax = pricingState.tax || 0;
    const total = subtotal - discount + tax;
    
    setPricingState(prev => ({
      ...prev,
      subtotal,
      total
    }));
    
    // Notify parent component
    onChange({
      ...pricingState,
      subtotal,
      total
    });
  }, [pricingState.items, pricingState.discount, pricingState.tax]);
  
  // Add a new item
  const handleAddItem = () => {
    const newItem: IProposalPricingItem = {
      id: uuidv4(),
      name: 'New Item',
      quantity: 1,
      unitPrice: 0,
      total: 0
    };
    
    setPricingState(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));
  };
  
  // Update an item
  const handleUpdateItem = (id: string, field: keyof IProposalPricingItem, value: any) => {
    setPricingState(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };
          
          // Recalculate total if quantity or unitPrice changed
          if (field === 'quantity' || field === 'unitPrice') {
            updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
          }
          
          return updatedItem;
        }
        return item;
      })
    }));
  };
  
  // Delete an item
  const handleDeleteItem = (id: string) => {
    setPricingState(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id)
    }));
  };
  
  // Update currency
  const handleCurrencyChange = (currency: string) => {
    setPricingState(prev => ({
      ...prev,
      currency
    }));
  };
  
  // Update discount
  const handleDiscountChange = (discount: number) => {
    setPricingState(prev => ({
      ...prev,
      discount
    }));
  };
  
  // Update tax
  const handleTaxChange = (tax: number) => {
    setPricingState(prev => ({
      ...prev,
      tax
    }));
  };
  
  // Handle drag and drop reordering
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const items = Array.from(pricingState.items);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    setPricingState(prev => ({
      ...prev,
      items
    }));
  };
  
  return (
    <Box>
      {/* Currency Selection */}
      <Box mb={2}>
        <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Currency</InputLabel>
          <Select
            value={pricingState.currency}
            onChange={(e) => handleCurrencyChange(e.target.value as string)}
            label="Currency"
          >
            <MenuItem value="USD">USD ($)</MenuItem>
            <MenuItem value="EUR">EUR (€)</MenuItem>
            <MenuItem value="GBP">GBP (£)</MenuItem>
            <MenuItem value="CAD">CAD (C$)</MenuItem>
            <MenuItem value="AUD">AUD (A$)</MenuItem>
            <MenuItem value="JPY">JPY (¥)</MenuItem>
          </Select>
        </FormControl>
      </Box>
      
      {/* Items Table */}
      <TableContainer component={Paper} variant="outlined">
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell width="40px"></TableCell>
              <TableCell>Item</TableCell>
              <TableCell align="right" width="100px">Quantity</TableCell>
              <TableCell align="right" width="150px">Unit Price</TableCell>
              <TableCell align="right" width="150px">Total</TableCell>
              <TableCell width="50px"></TableCell>
            </TableRow>
          </TableHead>
          
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="pricing-items">
              {(provided) => (
                <TableBody
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                >
                  {pricingState.items.map((item, index) => (
                    <Draggable key={item.id} draggableId={item.id} index={index}>
                      {(provided) => (
                        <TableRow
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                        >
                          <TableCell {...provided.dragHandleProps}>
                            <DragIndicatorIcon fontSize="small" sx={{ cursor: 'grab' }} />
                          </TableCell>
                          
                          <TableCell>
                            <TextField
                              value={item.name}
                              onChange={(e) => handleUpdateItem(item.id, 'name', e.target.value)}
                              fullWidth
                              variant="outlined"
                              size="small"
                              placeholder="Item name"
                            />
                            <TextField
                              value={item.description || ''}
                              onChange={(e) => handleUpdateItem(item.id, 'description', e.target.value)}
                              fullWidth
                              variant="outlined"
                              size="small"
                              placeholder="Description (optional)"
                              sx={{ mt: 1 }}
                            />
                          </TableCell>
                          
                          <TableCell align="right">
                            <TextField
                              type="number"
                              value={item.quantity}
                              onChange={(e) => handleUpdateItem(item.id, 'quantity', Number(e.target.value))}
                              variant="outlined"
                              size="small"
                              inputProps={{ min: 1, step: 1 }}
                            />
                          </TableCell>
                          
                          <TableCell align="right">
                            <TextField
                              type="number"
                              value={item.unitPrice}
                              onChange={(e) => handleUpdateItem(item.id, 'unitPrice', Number(e.target.value))}
                              variant="outlined"
                              size="small"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    {pricingState.currency === 'USD' ? '$' :
                                     pricingState.currency === 'EUR' ? '€' :
                                     pricingState.currency === 'GBP' ? '£' :
                                     pricingState.currency === 'CAD' ? 'C$' :
                                     pricingState.currency === 'AUD' ? 'A$' :
                                     pricingState.currency === 'JPY' ? '¥' : ''}
                                  </InputAdornment>
                                ),
                              }}
                              inputProps={{ min: 0, step: 0.01 }}
                            />
                          </TableCell>
                          
                          <TableCell align="right">
                            <Typography variant="body2">
                              {formatCurrency(item.total, pricingState.currency)}
                            </Typography>
                          </TableCell>
                          
                          <TableCell>
                            <Tooltip title="Delete Item">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteItem(item.id)}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                  
                  {/* Add Item Button */}
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ border: 'none', pt: 2 }}>
                      <Button
                        startIcon={<AddIcon />}
                        onClick={handleAddItem}
                        variant="outlined"
                        size="small"
                      >
                        Add Item
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              )}
            </Droppable>
          </DragDropContext>
        </Table>
      </TableContainer>
      
      {/* Totals */}
      <Box mt={3} display="flex" flexDirection="column" alignItems="flex-end">
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="body1" sx={{ mr: 2, width: 100 }}>
            Subtotal:
          </Typography>
          <Typography variant="body1" fontWeight="medium">
            {formatCurrency(pricingState.subtotal, pricingState.currency)}
          </Typography>
        </Box>
        
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="body1" sx={{ mr: 2, width: 100 }}>
            Discount:
          </Typography>
          <TextField
            type="number"
            value={pricingState.discount || 0}
            onChange={(e) => handleDiscountChange(Number(e.target.value))}
            variant="outlined"
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {pricingState.currency === 'USD' ? '$' :
                   pricingState.currency === 'EUR' ? '€' :
                   pricingState.currency === 'GBP' ? '£' :
                   pricingState.currency === 'CAD' ? 'C$' :
                   pricingState.currency === 'AUD' ? 'A$' :
                   pricingState.currency === 'JPY' ? '¥' : ''}
                </InputAdornment>
              ),
            }}
            inputProps={{ min: 0, step: 0.01 }}
          />
        </Box>
        
        <Box display="flex" alignItems="center" mb={1}>
          <Typography variant="body1" sx={{ mr: 2, width: 100 }}>
            Tax:
          </Typography>
          <TextField
            type="number"
            value={pricingState.tax || 0}
            onChange={(e) => handleTaxChange(Number(e.target.value))}
            variant="outlined"
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {pricingState.currency === 'USD' ? '$' :
                   pricingState.currency === 'EUR' ? '€' :
                   pricingState.currency === 'GBP' ? '£' :
                   pricingState.currency === 'CAD' ? 'C$' :
                   pricingState.currency === 'AUD' ? 'A$' :
                   pricingState.currency === 'JPY' ? '¥' : ''}
                </InputAdornment>
              ),
            }}
            inputProps={{ min: 0, step: 0.01 }}
          />
        </Box>
        
        <Box display="flex" alignItems="center">
          <Typography variant="body1" fontWeight="bold" sx={{ mr: 2, width: 100 }}>
            Total:
          </Typography>
          <Typography variant="body1" fontWeight="bold">
            {formatCurrency(pricingState.total, pricingState.currency)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default ProposalPricingEditor;
