import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Email as EmailIcon,
  Refresh as RefreshIcon,
  OpenInNew as OpenInNewIcon,
  Visibility as ViewIcon,
  VisibilityOff as HiddenIcon,
  Send as SendIcon,
  Schedule as ScheduleIcon,
  CheckCircle as SentIcon,
  Error as ErrorIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { EmailTrackingService } from '../../../services/email-tracking-service';
import { formatPercentage } from '../../../utils/formatters';

interface ProposalEmailTrackerProps {
  proposalId: string;
  tenantId: string;
}

/**
 * ProposalEmailTracker Component
 * 
 * This component displays email tracking information for a proposal and allows
 * sending new emails with the proposal.
 */
const ProposalEmailTracker: React.FC<ProposalEmailTrackerProps> = ({
  proposalId,
  tenantId,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<any>(null);
  const [emailEvents, setEmailEvents] = useState<any[]>([]);
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [sendLoading, setSendLoading] = useState(false);
  const [sendError, setSendError] = useState<string | null>(null);
  const [emailTemplates, setEmailTemplates] = useState<any[]>([]);
  
  // Fetch proposal and email events
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposal
        const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
        setProposal(proposalData);
        
        // Fetch email events
        const eventsData = await EmailTrackingService.getEmailEventsByProposal(proposalId, tenantId);
        setEmailEvents(eventsData);
        
        // Fetch email templates
        const templatesData = await EmailTrackingService.getEmailTemplates(tenantId);
        setEmailTemplates(templatesData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [proposalId, tenantId]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch email events
        const eventsData = await EmailTrackingService.getEmailEventsByProposal(proposalId, tenantId);
        setEmailEvents(eventsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Handle send email
  const handleSendEmail = async (formData: any) => {
    try {
      setSendLoading(true);
      setSendError(null);
      
      // Send email
      await EmailTrackingService.sendProposalEmail({
        proposalId,
        ...formData,
      }, tenantId);
      
      // Refresh email events
      handleRefresh();
      
      // Close dialog
      setSendDialogOpen(false);
    } catch (err) {
      console.error('Error sending email:', err);
      setSendError('Failed to send email. Please try again.');
    } finally {
      setSendLoading(false);
    }
  };
  
  // Calculate email stats
  const calculateStats = () => {
    const totalEmails = emailEvents.filter(event => event.type === 'sent').length;
    const openedEmails = emailEvents.filter(event => event.type === 'opened').length;
    const clickedEmails = emailEvents.filter(event => event.type === 'clicked').length;
    
    const openRate = totalEmails > 0 ? openedEmails / totalEmails : 0;
    const clickRate = openedEmails > 0 ? clickedEmails / openedEmails : 0;
    
    return {
      totalEmails,
      openedEmails,
      clickedEmails,
      openRate,
      clickRate,
    };
  };
  
  // Group events by email
  const groupEventsByEmail = () => {
    const emailGroups: Record<string, any[]> = {};
    
    emailEvents.forEach(event => {
      if (!emailGroups[event.emailId]) {
        emailGroups[event.emailId] = [];
      }
      
      emailGroups[event.emailId].push(event);
    });
    
    return Object.values(emailGroups).map(events => {
      // Find the sent event
      const sentEvent = events.find(event => event.type === 'sent');
      
      if (!sentEvent) return null;
      
      // Find other events
      const openedEvents = events.filter(event => event.type === 'opened');
      const clickedEvents = events.filter(event => event.type === 'clicked');
      
      return {
        emailId: sentEvent.emailId,
        recipient: sentEvent.recipient,
        subject: sentEvent.subject,
        sentAt: sentEvent.timestamp,
        opened: openedEvents.length > 0,
        openedAt: openedEvents.length > 0 ? openedEvents[0].timestamp : null,
        openCount: openedEvents.length,
        clicked: clickedEvents.length > 0,
        clickedAt: clickedEvents.length > 0 ? clickedEvents[0].timestamp : null,
        clickCount: clickedEvents.length,
        events,
      };
    }).filter(Boolean).sort((a: any, b: any) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime());
  };
  
  const stats = calculateStats();
  const emailGroups = groupEventsByEmail();
  
  // Render loading state
  if (loading && !proposal) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">
          Email Tracking
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<SendIcon />}
            onClick={() => setSendDialogOpen(true)}
            size={isMobile ? "small" : "medium"}
          >
            Send Email
          </Button>
        </Box>
      </Box>
      
      {/* Stats */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Emails Sent
              </Typography>
              <Typography variant="h4">
                {stats.totalEmails}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <SendIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {proposal?.status === 'sent' ? 'Proposal Sent' : 'Proposal Not Sent'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Open Rate
              </Typography>
              <Typography variant="h4">
                {formatPercentage(stats.openRate)}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <ViewIcon fontSize="small" color="info" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {stats.openedEmails} Opens
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Click Rate
              </Typography>
              <Typography variant="h4">
                {formatPercentage(stats.clickRate)}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <OpenInNewIcon fontSize="small" color="success" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {stats.clickedEmails} Clicks
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Email List */}
      {emailGroups.length === 0 ? (
        <Alert severity="info">
          No emails have been sent for this proposal yet.
        </Alert>
      ) : (
        <Paper variant="outlined">
          <List disablePadding>
            {emailGroups.map((email: any, index: number) => (
              <React.Fragment key={email.emailId}>
                <ListItem>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center">
                        <EmailIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                        <Typography variant="subtitle1">
                          {email.subject}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Box mt={0.5}>
                        <Typography variant="body2">
                          To: {email.recipient}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Sent: {format(new Date(email.sentAt), 'PPp')}
                        </Typography>
                        <Box display="flex" alignItems="center" mt={0.5} flexWrap="wrap" gap={0.5}>
                          {email.opened ? (
                            <Chip 
                              icon={<ViewIcon />} 
                              label={`Opened ${email.openCount > 1 ? `(${email.openCount}x)` : ''}`} 
                              color="info" 
                              size="small" 
                            />
                          ) : (
                            <Chip 
                              icon={<HiddenIcon />} 
                              label="Not Opened" 
                              variant="outlined" 
                              size="small" 
                            />
                          )}
                          
                          {email.clicked ? (
                            <Chip 
                              icon={<OpenInNewIcon />} 
                              label={`Clicked ${email.clickCount > 1 ? `(${email.clickCount}x)` : ''}`} 
                              color="success" 
                              size="small" 
                            />
                          ) : (
                            <Chip 
                              icon={<OpenInNewIcon />} 
                              label="Not Clicked" 
                              variant="outlined" 
                              size="small" 
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="View Email">
                      <IconButton edge="end">
                        <OpenInNewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy Email ID">
                      <IconButton 
                        edge="end"
                        onClick={() => {
                          navigator.clipboard.writeText(email.emailId);
                        }}
                      >
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < emailGroups.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}
      
      {/* Send Email Dialog */}
      <Dialog
        open={sendDialogOpen}
        onClose={() => setSendDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Send Proposal Email</DialogTitle>
        <DialogContent>
          {sendError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {sendError}
            </Alert>
          )}
          
          <FormControl fullWidth margin="normal">
            <InputLabel>Email Template</InputLabel>
            <Select
              defaultValue=""
              label="Email Template"
              id="template"
            >
              <MenuItem value="">
                <em>No Template (Custom)</em>
              </MenuItem>
              {emailTemplates.map((template) => (
                <MenuItem key={template._id} value={template._id}>
                  {template.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <TextField
            label="To"
            fullWidth
            margin="normal"
            id="to"
            defaultValue={proposal?.contacts?.map((contact: any) => contact.email).join(', ') || ''}
          />
          
          <TextField
            label="Subject"
            fullWidth
            margin="normal"
            id="subject"
            defaultValue={`${proposal?.title} - Proposal`}
          />
          
          <TextField
            label="Message"
            fullWidth
            margin="normal"
            multiline
            rows={6}
            id="message"
            defaultValue={`Dear Client,

I'm pleased to share our proposal with you. Please review it at your convenience.

You can view the proposal by clicking the link below:
${proposal?.publicUrl}

Please let me know if you have any questions or would like to discuss any aspect of the proposal.

Best regards,
[Your Name]`}
          />
          
          <Box mt={2}>
            <FormControlLabel
              control={
                <Checkbox
                  defaultChecked
                  id="trackOpens"
                />
              }
              label="Track Opens"
            />
            <FormControlLabel
              control={
                <Checkbox
                  defaultChecked
                  id="trackClicks"
                />
              }
              label="Track Clicks"
            />
          </Box>
          
          <Box mt={2}>
            <FormControlLabel
              control={
                <Checkbox
                  id="schedule"
                />
              }
              label="Schedule Email"
            />
            <TextField
              label="Schedule Date"
              type="datetime-local"
              fullWidth
              margin="normal"
              id="scheduleDate"
              disabled
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSendDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={() => {
              const to = (document.getElementById('to') as HTMLInputElement).value;
              const subject = (document.getElementById('subject') as HTMLInputElement).value;
              const message = (document.getElementById('message') as HTMLTextAreaElement).value;
              const template = (document.getElementById('template') as HTMLSelectElement).value;
              const trackOpens = (document.getElementById('trackOpens') as HTMLInputElement).checked;
              const trackClicks = (document.getElementById('trackClicks') as HTMLInputElement).checked;
              const schedule = (document.getElementById('schedule') as HTMLInputElement).checked;
              const scheduleDate = (document.getElementById('scheduleDate') as HTMLInputElement).value;
              
              handleSendEmail({
                to,
                subject,
                message,
                templateId: template || undefined,
                trackOpens,
                trackClicks,
                schedule,
                scheduleDate: schedule ? scheduleDate : undefined,
              });
            }}
            disabled={sendLoading}
            startIcon={sendLoading ? <CircularProgress size={20} /> : <SendIcon />}
          >
            {sendLoading ? 'Sending...' : 'Send Email'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProposalEmailTracker;
