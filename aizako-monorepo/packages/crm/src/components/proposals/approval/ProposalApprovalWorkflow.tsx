import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  ListItemSecondaryAction,
  useTheme,
  useMediaQuery,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Edit as EditIcon,
  Comment as CommentIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  Send as SendIcon,
  Visibility as ViewIcon,
  Assignment as AssignmentIcon,
  Refresh as RefreshIcon,
  ArrowForward as NextIcon,
  ArrowBack as PrevIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { ProposalApprovalService } from '../../../services/proposal-approval-service';
import { useAuth } from '../../../hooks/useAuth';

interface ProposalApprovalWorkflowProps {
  proposalId: string;
  tenantId: string;
  onViewProposal?: (proposalId: string) => void;
  onEditProposal?: (proposalId: string) => void;
}

/**
 * ProposalApprovalWorkflow Component
 * 
 * This component displays and manages the approval workflow for a proposal.
 */
const ProposalApprovalWorkflow: React.FC<ProposalApprovalWorkflowProps> = ({
  proposalId,
  tenantId,
  onViewProposal,
  onEditProposal,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<any>(null);
  const [approvalWorkflow, setApprovalWorkflow] = useState<any>(null);
  const [approvers, setApprovers] = useState<any[]>([]);
  const [availableApprovers, setAvailableApprovers] = useState<any[]>([]);
  const [commentDialogOpen, setCommentDialogOpen] = useState(false);
  const [comment, setComment] = useState('');
  const [addApproverDialogOpen, setAddApproverDialogOpen] = useState(false);
  const [selectedApprover, setSelectedApprover] = useState('');
  const [approvalLevel, setApprovalLevel] = useState(1);
  
  // Fetch proposal and approval workflow
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposal
        const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
        setProposal(proposalData);
        
        // Fetch approval workflow
        const workflowData = await ProposalApprovalService.getApprovalWorkflow(proposalId, tenantId);
        setApprovalWorkflow(workflowData);
        
        // Fetch approvers
        const approversData = await ProposalApprovalService.getApprovers(tenantId);
        setAvailableApprovers(approversData);
        
        // Set current approvers
        if (workflowData?.approvers) {
          setApprovers(workflowData.approvers);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [proposalId, tenantId]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch approval workflow
        const workflowData = await ProposalApprovalService.getApprovalWorkflow(proposalId, tenantId);
        setApprovalWorkflow(workflowData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Handle approve
  const handleApprove = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalApprovalService.approveProposal(proposalId, tenantId, {
        comment,
      });
      
      // Refresh data
      handleRefresh();
      
      // Close dialog
      setCommentDialogOpen(false);
      setComment('');
    } catch (err) {
      console.error('Error approving proposal:', err);
      setError('Failed to approve proposal. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle reject
  const handleReject = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalApprovalService.rejectProposal(proposalId, tenantId, {
        comment,
      });
      
      // Refresh data
      handleRefresh();
      
      // Close dialog
      setCommentDialogOpen(false);
      setComment('');
    } catch (err) {
      console.error('Error rejecting proposal:', err);
      setError('Failed to reject proposal. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle add approver
  const handleAddApprover = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalApprovalService.addApprover(proposalId, tenantId, {
        approverId: selectedApprover,
        level: approvalLevel,
      });
      
      // Refresh data
      handleRefresh();
      
      // Close dialog
      setAddApproverDialogOpen(false);
      setSelectedApprover('');
      setApprovalLevel(1);
    } catch (err) {
      console.error('Error adding approver:', err);
      setError('Failed to add approver. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle remove approver
  const handleRemoveApprover = async (approverId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalApprovalService.removeApprover(proposalId, tenantId, {
        approverId,
      });
      
      // Refresh data
      handleRefresh();
    } catch (err) {
      console.error('Error removing approver:', err);
      setError('Failed to remove approver. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle start approval process
  const handleStartApprovalProcess = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalApprovalService.startApprovalProcess(proposalId, tenantId);
      
      // Refresh data
      handleRefresh();
    } catch (err) {
      console.error('Error starting approval process:', err);
      setError('Failed to start approval process. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle cancel approval process
  const handleCancelApprovalProcess = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalApprovalService.cancelApprovalProcess(proposalId, tenantId);
      
      // Refresh data
      handleRefresh();
    } catch (err) {
      console.error('Error canceling approval process:', err);
      setError('Failed to cancel approval process. Please try again.');
      setLoading(false);
    }
  };
  
  // Check if current user is an approver at the current level
  const isCurrentApprover = () => {
    if (!approvalWorkflow || !user) return false;
    
    const currentLevel = approvalWorkflow.currentLevel;
    const currentLevelApprovers = approvers.filter(approver => approver.level === currentLevel);
    
    return currentLevelApprovers.some(approver => approver.userId === user.id);
  };
  
  // Render loading state
  if (loading && !proposal) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">
          Approval Workflow
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button 
            variant="outlined" 
            startIcon={<ViewIcon />}
            onClick={() => onViewProposal && onViewProposal(proposalId)}
            sx={{ mr: 1 }}
          >
            View Proposal
          </Button>
          {approvalWorkflow?.status === 'rejected' && (
            <Button 
              variant="outlined" 
              startIcon={<EditIcon />}
              onClick={() => onEditProposal && onEditProposal(proposalId)}
              sx={{ mr: 1 }}
            >
              Edit Proposal
            </Button>
          )}
        </Box>
      </Box>
      
      {/* Proposal Info */}
      {proposal && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {proposal.title}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {proposal.description}
            </Typography>
            <Divider sx={{ my: 1 }} />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Status
                </Typography>
                <Chip 
                  label={proposal.status.toUpperCase()} 
                  color={
                    proposal.status === 'accepted' ? 'success' :
                    proposal.status === 'rejected' ? 'error' :
                    proposal.status === 'viewed' ? 'info' :
                    proposal.status === 'sent' ? 'primary' :
                    'default'
                  }
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Created By
                </Typography>
                <Typography variant="body2">
                  {proposal.createdBy?.name || 'Unknown'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Created At
                </Typography>
                <Typography variant="body2">
                  {format(new Date(proposal.createdAt), 'PPP')}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Last Updated
                </Typography>
                <Typography variant="body2">
                  {format(new Date(proposal.updatedAt), 'PPP')}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {/* Approval Workflow */}
      {!approvalWorkflow ? (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              No Approval Workflow
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              This proposal doesn't have an approval workflow yet. You can set up approvers and start the approval process.
            </Typography>
            <Box mt={2}>
              <Button 
                variant="contained" 
                startIcon={<AddIcon />}
                onClick={() => setAddApproverDialogOpen(true)}
                sx={{ mr: 1 }}
              >
                Add Approver
              </Button>
              <Button 
                variant="contained" 
                startIcon={<SendIcon />}
                onClick={handleStartApprovalProcess}
                disabled={approvers.length === 0}
              >
                Start Approval Process
              </Button>
            </Box>
          </CardContent>
        </Card>
      ) : (
        <Box>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Approval Status
                </Typography>
                <Chip 
                  label={approvalWorkflow.status.toUpperCase()} 
                  color={
                    approvalWorkflow.status === 'approved' ? 'success' :
                    approvalWorkflow.status === 'rejected' ? 'error' :
                    approvalWorkflow.status === 'in_progress' ? 'primary' :
                    'default'
                  }
                />
              </Box>
              
              <Stepper 
                activeStep={approvalWorkflow.currentLevel - 1} 
                orientation={isMobile ? "vertical" : "horizontal"}
              >
                {Array.from({ length: approvalWorkflow.totalLevels }, (_, i) => i + 1).map((level) => {
                  const levelApprovers = approvers.filter(approver => approver.level === level);
                  const isCompleted = level < approvalWorkflow.currentLevel;
                  const isCurrent = level === approvalWorkflow.currentLevel;
                  
                  return (
                    <Step key={level} completed={isCompleted}>
                      <StepLabel>Level {level}</StepLabel>
                      {isMobile && (
                        <StepContent>
                          <Typography variant="body2">
                            {levelApprovers.length} Approver{levelApprovers.length !== 1 ? 's' : ''}
                          </Typography>
                          {isCompleted && (
                            <Typography variant="body2" color="success.main">
                              Approved on {format(new Date(approvalWorkflow.levelCompletedAt[level - 1]), 'PPP')}
                            </Typography>
                          )}
                        </StepContent>
                      )}
                    </Step>
                  );
                })}
              </Stepper>
              
              {approvalWorkflow.status === 'in_progress' && (
                <Box mt={3}>
                  <Typography variant="subtitle1" gutterBottom>
                    Current Level: {approvalWorkflow.currentLevel}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Waiting for approval from level {approvalWorkflow.currentLevel} approvers.
                  </Typography>
                  
                  {isCurrentApprover() && (
                    <Box mt={2}>
                      <Button 
                        variant="contained" 
                        color="success"
                        startIcon={<ApproveIcon />}
                        onClick={() => setCommentDialogOpen(true)}
                        sx={{ mr: 1 }}
                      >
                        Approve
                      </Button>
                      <Button 
                        variant="contained" 
                        color="error"
                        startIcon={<RejectIcon />}
                        onClick={() => setCommentDialogOpen(true)}
                      >
                        Reject
                      </Button>
                    </Box>
                  )}
                </Box>
              )}
              
              {approvalWorkflow.status === 'approved' && (
                <Alert severity="success" sx={{ mt: 3 }}>
                  This proposal has been approved by all required approvers.
                </Alert>
              )}
              
              {approvalWorkflow.status === 'rejected' && (
                <Alert severity="error" sx={{ mt: 3 }}>
                  This proposal has been rejected. Please review the comments and make necessary changes.
                </Alert>
              )}
              
              {approvalWorkflow.status === 'in_progress' && (
                <Box mt={2} display="flex" justifyContent="flex-end">
                  <Button 
                    variant="outlined" 
                    color="error"
                    startIcon={<CancelIcon />}
                    onClick={handleCancelApprovalProcess}
                  >
                    Cancel Approval Process
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
          
          {/* Approvers */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Approvers
                </Typography>
                {approvalWorkflow.status !== 'approved' && approvalWorkflow.status !== 'rejected' && (
                  <Button 
                    variant="outlined" 
                    startIcon={<AddIcon />}
                    onClick={() => setAddApproverDialogOpen(true)}
                  >
                    Add Approver
                  </Button>
                )}
              </Box>
              
              {approvers.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No approvers assigned yet.
                </Typography>
              ) : (
                <List>
                  {Array.from({ length: approvalWorkflow.totalLevels }, (_, i) => i + 1).map((level) => {
                    const levelApprovers = approvers.filter(approver => approver.level === level);
                    
                    return levelApprovers.length > 0 ? (
                      <React.Fragment key={level}>
                        <Typography variant="subtitle2" gutterBottom>
                          Level {level}
                        </Typography>
                        {levelApprovers.map((approver) => (
                          <ListItem key={approver.userId}>
                            <ListItemAvatar>
                              <Avatar>
                                <PersonIcon />
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={approver.name}
                              secondary={approver.email}
                            />
                            <ListItemSecondaryAction>
                              {approver.status === 'approved' && (
                                <Tooltip title="Approved">
                                  <ApproveIcon color="success" />
                                </Tooltip>
                              )}
                              {approver.status === 'rejected' && (
                                <Tooltip title="Rejected">
                                  <RejectIcon color="error" />
                                </Tooltip>
                              )}
                              {approver.status === 'pending' && (
                                <Tooltip title="Pending">
                                  <CircularProgress size={20} />
                                </Tooltip>
                              )}
                              {approvalWorkflow.status !== 'approved' && approvalWorkflow.status !== 'rejected' && (
                                <Tooltip title="Remove Approver">
                                  <IconButton
                                    edge="end"
                                    onClick={() => handleRemoveApprover(approver.userId)}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </ListItemSecondaryAction>
                          </ListItem>
                        ))}
                      </React.Fragment>
                    ) : null;
                  })}
                </List>
              )}
            </CardContent>
          </Card>
          
          {/* Approval History */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Approval History
              </Typography>
              
              {approvalWorkflow.history?.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No approval history yet.
                </Typography>
              ) : (
                <Timeline position="alternate">
                  {approvalWorkflow.history?.map((event: any, index: number) => (
                    <TimelineItem key={index}>
                      <TimelineOppositeContent color="text.secondary">
                        {format(new Date(event.timestamp), 'PPp')}
                      </TimelineOppositeContent>
                      <TimelineSeparator>
                        <TimelineDot color={
                          event.action === 'approved' ? 'success' :
                          event.action === 'rejected' ? 'error' :
                          event.action === 'started' ? 'primary' :
                          event.action === 'canceled' ? 'warning' :
                          'grey'
                        }>
                          {event.action === 'approved' && <ApproveIcon />}
                          {event.action === 'rejected' && <RejectIcon />}
                          {event.action === 'started' && <SendIcon />}
                          {event.action === 'canceled' && <CancelIcon />}
                          {event.action === 'added_approver' && <PersonIcon />}
                          {event.action === 'removed_approver' && <DeleteIcon />}
                        </TimelineDot>
                        {index < approvalWorkflow.history.length - 1 && <TimelineConnector />}
                      </TimelineSeparator>
                      <TimelineContent>
                        <Paper elevation={3} sx={{ p: 2 }}>
                          <Typography variant="subtitle2">
                            {event.action === 'approved' && 'Approved'}
                            {event.action === 'rejected' && 'Rejected'}
                            {event.action === 'started' && 'Started Approval Process'}
                            {event.action === 'canceled' && 'Canceled Approval Process'}
                            {event.action === 'added_approver' && 'Added Approver'}
                            {event.action === 'removed_approver' && 'Removed Approver'}
                          </Typography>
                          <Typography variant="body2">
                            {event.user?.name || 'System'}
                          </Typography>
                          {event.comment && (
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                              "{event.comment}"
                            </Typography>
                          )}
                        </Paper>
                      </TimelineContent>
                    </TimelineItem>
                  ))}
                </Timeline>
              )}
            </CardContent>
          </Card>
        </Box>
      )}
      
      {/* Comment Dialog */}
      <Dialog
        open={commentDialogOpen}
        onClose={() => setCommentDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add Comment</DialogTitle>
        <DialogContent>
          <TextField
            label="Comment"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            fullWidth
            multiline
            rows={4}
            margin="normal"
            placeholder="Add your comments or feedback here..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCommentDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            color="error"
            onClick={handleReject}
          >
            Reject
          </Button>
          <Button 
            variant="contained" 
            color="success"
            onClick={handleApprove}
          >
            Approve
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Add Approver Dialog */}
      <Dialog
        open={addApproverDialogOpen}
        onClose={() => setAddApproverDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add Approver</DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="normal">
            <InputLabel>Approver</InputLabel>
            <Select
              value={selectedApprover}
              onChange={(e) => setSelectedApprover(e.target.value)}
              label="Approver"
            >
              {availableApprovers.map((approver) => (
                <MenuItem key={approver.id} value={approver.id}>
                  {approver.name} ({approver.email})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl fullWidth margin="normal">
            <InputLabel>Approval Level</InputLabel>
            <Select
              value={approvalLevel}
              onChange={(e) => setApprovalLevel(Number(e.target.value))}
              label="Approval Level"
            >
              {Array.from({ length: 5 }, (_, i) => i + 1).map((level) => (
                <MenuItem key={level} value={level}>
                  Level {level}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddApproverDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleAddApprover}
            disabled={!selectedApprover}
          >
            Add Approver
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProposalApprovalWorkflow;
