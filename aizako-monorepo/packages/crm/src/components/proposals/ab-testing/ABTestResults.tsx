import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Divider,
  CircularProgress,
  Alert,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Tooltip,
  IconButton,
  useTheme,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import {
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  EmojiEvents as TrophyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as FlatIcon,
} from '@mui/icons-material';
import { ABTestingService } from '../../../services/ab-testing-service';
import { formatPercentage } from '../../../utils/formatters';

interface ABTestResultsProps {
  testId: string;
  tenantId: string;
}

/**
 * ABTestResults Component
 * 
 * This component displays the results of an A/B test.
 */
const ABTestResults: React.FC<ABTestResultsProps> = ({
  testId,
  tenantId,
}) => {
  const theme = useTheme();
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<any>(null);
  
  // Fetch results
  useEffect(() => {
    const fetchResults = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await ABTestingService.getTestResults(testId, tenantId);
        setResults(response);
      } catch (err) {
        console.error('Error fetching A/B test results:', err);
        setError('Failed to load test results. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchResults();
  }, [testId, tenantId]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchResults = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await ABTestingService.getTestResults(testId, tenantId);
        setResults(response);
      } catch (err) {
        console.error('Error fetching A/B test results:', err);
        setError('Failed to load test results. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchResults();
  };
  
  // Prepare chart data
  const prepareViewsChart = () => {
    if (!results) return [];
    
    const data = [
      {
        name: 'Base',
        views: results.baseProposal.views,
        uniqueViews: results.baseProposal.uniqueViews,
      },
      ...results.variants.map((variant: any) => ({
        name: variant.name,
        views: variant.views,
        uniqueViews: variant.uniqueViews,
      })),
    ];
    
    return data;
  };
  
  const prepareAcceptanceChart = () => {
    if (!results) return [];
    
    const data = [
      {
        name: 'Base',
        acceptances: results.baseProposal.acceptances,
        rejections: results.baseProposal.rejections,
      },
      ...results.variants.map((variant: any) => ({
        name: variant.name,
        acceptances: variant.acceptances,
        rejections: variant.rejections,
      })),
    ];
    
    return data;
  };
  
  const prepareAcceptanceRateChart = () => {
    if (!results) return [];
    
    const data = [
      {
        name: 'Base',
        rate: results.baseProposal.acceptanceRate * 100,
        isWinner: false,
      },
      ...results.variants.map((variant: any) => ({
        name: variant.name,
        rate: variant.acceptanceRate * 100,
        isWinner: results.winner === variant.name,
      })),
    ];
    
    return data;
  };
  
  // Render loading state
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  // Render empty state
  if (!results) {
    return (
      <Alert severity="info">
        No results available for this test yet.
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Summary */}
      <Box mb={3}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={8}>
            <Typography variant="h6" gutterBottom>
              Test Summary
            </Typography>
            <Typography variant="body1">
              {results.winner ? (
                <Box display="flex" alignItems="center">
                  <TrophyIcon color="success" sx={{ mr: 1 }} />
                  <span>
                    Winner: <strong>{results.winner}</strong> with {formatPercentage(results.variants.find((v: any) => v.name === results.winner)?.acceptanceRate)} acceptance rate
                  </span>
                </Box>
              ) : (
                'No clear winner yet'
              )}
            </Typography>
            {results.winner && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Confidence: {results.confidence.toFixed(1)}%
              </Typography>
            )}
          </Grid>
          <Grid item xs={12} md={4}>
            <Box display="flex" justifyContent="flex-end">
              <Button 
                variant="outlined" 
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
              >
                Refresh Results
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
      
      {/* Charts */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Views
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={prepareViewsChart()}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Bar dataKey="views" name="Total Views" fill={theme.palette.primary.main} />
                    <Bar dataKey="uniqueViews" name="Unique Views" fill={theme.palette.secondary.main} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Acceptance Rate
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={prepareAcceptanceRateChart()}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis domain={[0, 100]} />
                    <RechartsTooltip formatter={(value) => [`${value.toFixed(1)}%`, 'Acceptance Rate']} />
                    <Bar dataKey="rate" name="Acceptance Rate">
                      {prepareAcceptanceRateChart().map((entry, index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={entry.isWinner ? theme.palette.success.main : theme.palette.primary.main} 
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Detailed Results */}
      <Typography variant="h6" gutterBottom>
        Detailed Results
      </Typography>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Variant</TableCell>
              <TableCell align="right">Views</TableCell>
              <TableCell align="right">Unique Views</TableCell>
              <TableCell align="right">Acceptances</TableCell>
              <TableCell align="right">Acceptance Rate</TableCell>
              <TableCell align="right">Improvement</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell>
                <Typography fontWeight="bold">Base</Typography>
              </TableCell>
              <TableCell align="right">{results.baseProposal.views}</TableCell>
              <TableCell align="right">{results.baseProposal.uniqueViews}</TableCell>
              <TableCell align="right">{results.baseProposal.acceptances}</TableCell>
              <TableCell align="right">{formatPercentage(results.baseProposal.acceptanceRate)}</TableCell>
              <TableCell align="right">-</TableCell>
            </TableRow>
            {results.variants.map((variant: any) => (
              <TableRow key={variant.name}>
                <TableCell>
                  <Box display="flex" alignItems="center">
                    {results.winner === variant.name && (
                      <Tooltip title="Winner">
                        <TrophyIcon color="success" sx={{ mr: 1 }} />
                      </Tooltip>
                    )}
                    {variant.name}
                  </Box>
                </TableCell>
                <TableCell align="right">{variant.views}</TableCell>
                <TableCell align="right">{variant.uniqueViews}</TableCell>
                <TableCell align="right">{variant.acceptances}</TableCell>
                <TableCell align="right">{formatPercentage(variant.acceptanceRate)}</TableCell>
                <TableCell align="right">
                  <Box display="flex" alignItems="center" justifyContent="flex-end">
                    {variant.improvement > 0 ? (
                      <TrendingUpIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                    ) : variant.improvement < 0 ? (
                      <TrendingDownIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                    ) : (
                      <FlatIcon color="action" fontSize="small" sx={{ mr: 0.5 }} />
                    )}
                    {variant.improvement > 0 ? '+' : ''}{variant.improvement.toFixed(1)}%
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Statistical Significance */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Statistical Significance
          </Typography>
          <Box display="flex" alignItems="center" mb={1}>
            <Typography variant="body1" sx={{ mr: 2 }}>
              Confidence: {results.confidence.toFixed(1)}%
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={results.confidence} 
              color={results.confidence >= 95 ? 'success' : 'primary'}
              sx={{ flexGrow: 1, height: 10, borderRadius: 5 }}
            />
          </Box>
          <Typography variant="body2" color="text.secondary">
            {results.confidence >= 95 ? (
              <Box display="flex" alignItems="center">
                <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 1 }} />
                The results are statistically significant (95% confidence level).
              </Box>
            ) : (
              'The test needs more data to reach statistical significance (95% confidence level).'
            )}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ABTestResults;
