import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Divider,
  IconButton,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Paper,
  FormHelperText,
  Tooltip,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Help as HelpIcon,
  Edit as EditIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { addDays } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import RichTextEditor from '../../common/RichTextEditor';

interface CreateABTestDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (testData: any) => void;
  proposals: any[];
}

/**
 * CreateABTestDialog Component
 * 
 * This component displays a dialog for creating a new A/B test.
 */
const CreateABTestDialog: React.FC<CreateABTestDialogProps> = ({
  open,
  onClose,
  onSubmit,
  proposals,
}) => {
  const theme = useTheme();
  
  // State
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [baseProposal, setBaseProposal] = useState<string>('');
  const [baseProposalData, setBaseProposalData] = useState<any>(null);
  const [testName, setTestName] = useState<string>('');
  const [testDescription, setTestDescription] = useState<string>('');
  const [variants, setVariants] = useState<Array<{
    name: string;
    description?: string;
    changes: Array<{
      sectionId: string;
      content: string;
    }>;
  }>>([]);
  const [trafficSplit, setTrafficSplit] = useState<Array<number>>([]);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(addDays(new Date(), 30));
  const [selectedSectionId, setSelectedSectionId] = useState<string>('');
  const [editingVariantIndex, setEditingVariantIndex] = useState<number>(-1);
  const [editingSectionContent, setEditingSectionContent] = useState<string>('');
  
  // Reset state when dialog opens
  useEffect(() => {
    if (open) {
      setActiveStep(0);
      setBaseProposal('');
      setBaseProposalData(null);
      setTestName('');
      setTestDescription('');
      setVariants([]);
      setTrafficSplit([]);
      setStartDate(new Date());
      setEndDate(addDays(new Date(), 30));
      setSelectedSectionId('');
      setEditingVariantIndex(-1);
      setEditingSectionContent('');
      setError(null);
    }
  }, [open]);
  
  // Update traffic split when variants change
  useEffect(() => {
    if (variants.length > 0) {
      // Calculate equal distribution
      const basePercentage = 100 / (variants.length + 1);
      const variantPercentage = basePercentage;
      
      setTrafficSplit([
        basePercentage,
        ...Array(variants.length).fill(variantPercentage),
      ]);
    } else {
      setTrafficSplit([100]);
    }
  }, [variants.length]);
  
  // Fetch base proposal data
  useEffect(() => {
    if (baseProposal) {
      const fetchProposal = async () => {
        try {
          setLoading(true);
          setError(null);
          
          const response = await ProposalService.getProposalById(baseProposal);
          setBaseProposalData(response);
        } catch (err) {
          console.error('Error fetching proposal:', err);
          setError('Failed to load proposal data. Please try again.');
          setBaseProposalData(null);
        } finally {
          setLoading(false);
        }
      };
      
      fetchProposal();
    } else {
      setBaseProposalData(null);
    }
  }, [baseProposal]);
  
  // Handle next step
  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };
  
  // Handle back step
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };
  
  // Handle add variant
  const handleAddVariant = () => {
    setVariants([
      ...variants,
      {
        name: `Variant ${variants.length + 1}`,
        description: '',
        changes: [],
      },
    ]);
  };
  
  // Handle delete variant
  const handleDeleteVariant = (index: number) => {
    setVariants(variants.filter((_, i) => i !== index));
  };
  
  // Handle edit variant
  const handleEditVariant = (index: number, field: string, value: string) => {
    const updatedVariants = [...variants];
    updatedVariants[index] = {
      ...updatedVariants[index],
      [field]: value,
    };
    setVariants(updatedVariants);
  };
  
  // Handle edit section content
  const handleEditSectionContent = (content: string) => {
    setEditingSectionContent(content);
  };
  
  // Handle save section content
  const handleSaveSectionContent = () => {
    if (editingVariantIndex === -1 || !selectedSectionId) return;
    
    const updatedVariants = [...variants];
    const variantIndex = editingVariantIndex;
    
    // Check if section already exists in changes
    const sectionIndex = updatedVariants[variantIndex].changes.findIndex(
      (change) => change.sectionId === selectedSectionId
    );
    
    if (sectionIndex !== -1) {
      // Update existing section
      updatedVariants[variantIndex].changes[sectionIndex].content = editingSectionContent;
    } else {
      // Add new section
      updatedVariants[variantIndex].changes.push({
        sectionId: selectedSectionId,
        content: editingSectionContent,
      });
    }
    
    setVariants(updatedVariants);
    setSelectedSectionId('');
    setEditingVariantIndex(-1);
    setEditingSectionContent('');
  };
  
  // Handle traffic split change
  const handleTrafficSplitChange = (index: number, value: number) => {
    const updatedTrafficSplit = [...trafficSplit];
    updatedTrafficSplit[index] = value;
    
    // Ensure total is 100%
    const total = updatedTrafficSplit.reduce((sum, val) => sum + val, 0);
    if (total !== 100) {
      // Adjust other values proportionally
      const adjustment = (100 - value) / (updatedTrafficSplit.length - 1);
      updatedTrafficSplit.forEach((_, i) => {
        if (i !== index) {
          updatedTrafficSplit[i] = adjustment;
        }
      });
    }
    
    setTrafficSplit(updatedTrafficSplit);
  };
  
  // Handle submit
  const handleSubmit = () => {
    // Validate
    if (!baseProposal) {
      setError('Please select a base proposal.');
      return;
    }
    
    if (!testName) {
      setError('Please enter a test name.');
      return;
    }
    
    if (variants.length === 0) {
      setError('Please add at least one variant.');
      return;
    }
    
    // Check if all variants have changes
    const emptyVariants = variants.filter((variant) => variant.changes.length === 0);
    if (emptyVariants.length > 0) {
      setError(`The following variants have no changes: ${emptyVariants.map((v) => v.name).join(', ')}`);
      return;
    }
    
    // Submit
    onSubmit({
      name: testName,
      description: testDescription,
      baseProposalId: baseProposal,
      variants,
      trafficSplit,
      startDate,
      endDate,
    });
  };
  
  // Get section content
  const getSectionContent = (sectionId: string) => {
    if (!baseProposalData) return '';
    
    const section = baseProposalData.sections.find((s: any) => s.id === sectionId);
    return section ? section.content : '';
  };
  
  // Get variant section content
  const getVariantSectionContent = (variantIndex: number, sectionId: string) => {
    if (variantIndex === -1) return '';
    
    const change = variants[variantIndex].changes.find((c) => c.sectionId === sectionId);
    return change ? change.content : getSectionContent(sectionId);
  };
  
  // Steps
  const steps = ['Select Base Proposal', 'Create Variants', 'Configure Test'];
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle>Create A/B Test</DialogTitle>
      <DialogContent>
        {/* Stepper */}
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {/* Error */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {/* Step Content */}
        {activeStep === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Select Base Proposal
            </Typography>
            <FormControl fullWidth margin="normal">
              <InputLabel>Base Proposal</InputLabel>
              <Select
                value={baseProposal}
                onChange={(e) => setBaseProposal(e.target.value)}
                label="Base Proposal"
              >
                <MenuItem value="">
                  <em>Select a proposal</em>
                </MenuItem>
                {proposals.map((proposal) => (
                  <MenuItem key={proposal._id} value={proposal._id}>
                    {proposal.title}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>
                Select the proposal you want to use as the base for your A/B test.
              </FormHelperText>
            </FormControl>
            
            {loading && (
              <Box display="flex" justifyContent="center" mt={2}>
                <CircularProgress />
              </Box>
            )}
            
            {baseProposalData && (
              <Box mt={3}>
                <Typography variant="subtitle1" gutterBottom>
                  Proposal Details
                </Typography>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="h6">{baseProposalData.title}</Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {baseProposalData.description}
                  </Typography>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="subtitle2" gutterBottom>
                    Sections: {baseProposalData.sections.length}
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={1}>
                    {baseProposalData.sections.map((section: any) => (
                      <Chip 
                        key={section.id} 
                        label={section.title} 
                        size="small" 
                      />
                    ))}
                  </Box>
                </Paper>
              </Box>
            )}
          </Box>
        )}
        
        {activeStep === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Create Variants
            </Typography>
            
            <TextField
              label="Test Name"
              value={testName}
              onChange={(e) => setTestName(e.target.value)}
              fullWidth
              margin="normal"
              required
            />
            
            <TextField
              label="Test Description"
              value={testDescription}
              onChange={(e) => setTestDescription(e.target.value)}
              fullWidth
              margin="normal"
              multiline
              rows={2}
            />
            
            <Box mt={3}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="subtitle1">
                  Variants
                </Typography>
                <Button 
                  variant="outlined" 
                  startIcon={<AddIcon />}
                  onClick={handleAddVariant}
                >
                  Add Variant
                </Button>
              </Box>
              
              {variants.length === 0 ? (
                <Alert severity="info">
                  Add at least one variant to continue.
                </Alert>
              ) : (
                <Grid container spacing={2}>
                  {variants.map((variant, index) => (
                    <Grid item xs={12} key={index}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                          <TextField
                            label="Variant Name"
                            value={variant.name}
                            onChange={(e) => handleEditVariant(index, 'name', e.target.value)}
                            size="small"
                            sx={{ width: '60%' }}
                          />
                          <Box>
                            <Tooltip title="Delete Variant">
                              <IconButton 
                                color="error"
                                onClick={() => handleDeleteVariant(index)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                        
                        <TextField
                          label="Variant Description"
                          value={variant.description}
                          onChange={(e) => handleEditVariant(index, 'description', e.target.value)}
                          fullWidth
                          margin="normal"
                          size="small"
                          multiline
                          rows={2}
                        />
                        
                        <Divider sx={{ my: 1 }} />
                        
                        <Typography variant="subtitle2" gutterBottom>
                          Section Changes
                        </Typography>
                        
                        {variant.changes.length === 0 ? (
                          <Typography variant="body2" color="text.secondary">
                            No changes yet. Select a section to modify.
                          </Typography>
                        ) : (
                          <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                            {variant.changes.map((change) => {
                              const section = baseProposalData?.sections.find((s: any) => s.id === change.sectionId);
                              return (
                                <Chip 
                                  key={change.sectionId} 
                                  label={section?.title || 'Unknown Section'} 
                                  color="primary"
                                  variant="outlined"
                                  onDelete={() => {
                                    const updatedVariants = [...variants];
                                    updatedVariants[index].changes = updatedVariants[index].changes.filter(
                                      (c) => c.sectionId !== change.sectionId
                                    );
                                    setVariants(updatedVariants);
                                  }}
                                />
                              );
                            })}
                          </Box>
                        )}
                        
                        <FormControl fullWidth margin="normal" size="small">
                          <InputLabel>Edit Section</InputLabel>
                          <Select
                            value=""
                            onChange={(e) => {
                              setSelectedSectionId(e.target.value);
                              setEditingVariantIndex(index);
                              setEditingSectionContent(getVariantSectionContent(index, e.target.value));
                            }}
                            label="Edit Section"
                          >
                            <MenuItem value="">
                              <em>Select a section</em>
                            </MenuItem>
                            {baseProposalData?.sections.map((section: any) => (
                              <MenuItem key={section.id} value={section.id}>
                                {section.title}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>
            
            {/* Section Editor */}
            {selectedSectionId && editingVariantIndex !== -1 && (
              <Box mt={3}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Edit Section: {baseProposalData?.sections.find((s: any) => s.id === selectedSectionId)?.title}
                  </Typography>
                  <Box mb={2}>
                    <RichTextEditor
                      value={editingSectionContent}
                      onChange={handleEditSectionContent}
                      placeholder="Enter section content..."
                    />
                  </Box>
                  <Box display="flex" justifyContent="flex-end" gap={1}>
                    <Button 
                      onClick={() => {
                        setSelectedSectionId('');
                        setEditingVariantIndex(-1);
                        setEditingSectionContent('');
                      }}
                    >
                      Cancel
                    </Button>
                    <Button 
                      variant="contained"
                      onClick={handleSaveSectionContent}
                    >
                      Save Changes
                    </Button>
                  </Box>
                </Paper>
              </Box>
            )}
          </Box>
        )}
        
        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Configure Test
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Start Date"
                    value={startDate}
                    onChange={(date) => date && setStartDate(date)}
                    slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="End Date"
                    value={endDate}
                    onChange={(date) => setEndDate(date)}
                    slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
                  />
                </LocalizationProvider>
              </Grid>
            </Grid>
            
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
              Traffic Split
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  label="Base Proposal"
                  value={trafficSplit[0]}
                  onChange={(e) => handleTrafficSplitChange(0, Number(e.target.value))}
                  type="number"
                  InputProps={{ inputProps: { min: 0, max: 100 } }}
                  fullWidth
                  margin="normal"
                  helperText={`${baseProposalData?.title || 'Base Proposal'}`}
                />
              </Grid>
              
              {variants.map((variant, index) => (
                <Grid item xs={12} key={index}>
                  <TextField
                    label={`Variant ${index + 1}`}
                    value={trafficSplit[index + 1]}
                    onChange={(e) => handleTrafficSplitChange(index + 1, Number(e.target.value))}
                    type="number"
                    InputProps={{ inputProps: { min: 0, max: 100 } }}
                    fullWidth
                    margin="normal"
                    helperText={variant.name}
                  />
                </Grid>
              ))}
            </Grid>
            
            <Alert severity="info" sx={{ mt: 3 }}>
              <Typography variant="body2">
                The traffic split determines what percentage of visitors will see each variant.
                The total should add up to 100%.
              </Typography>
            </Alert>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {activeStep > 0 && (
          <Button onClick={handleBack}>
            Back
          </Button>
        )}
        {activeStep < steps.length - 1 ? (
          <Button 
            variant="contained" 
            onClick={handleNext}
            disabled={
              (activeStep === 0 && !baseProposal) ||
              (activeStep === 1 && (variants.length === 0 || !testName))
            }
          >
            Next
          </Button>
        ) : (
          <Button 
            variant="contained" 
            onClick={handleSubmit}
          >
            Create Test
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CreateABTestDialog;
