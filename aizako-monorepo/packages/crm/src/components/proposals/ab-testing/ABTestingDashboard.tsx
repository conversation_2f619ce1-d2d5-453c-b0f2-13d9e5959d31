import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CardActions,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  <PERSON><PERSON>hart as ChartIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';
import { format, formatDistance } from 'date-fns';
import { ABTestingService } from '../../../services/ab-testing-service';
import { ProposalService } from '../../../services/proposal-service';
import { formatPercentage } from '../../../utils/formatters';
import ABTestResults from './ABTestResults';
import CreateABTestDialog from './CreateABTestDialog';

interface ABTestingDashboardProps {
  tenantId: string;
}

/**
 * ABTestingDashboard Component
 * 
 * This component displays a dashboard for managing A/B tests for proposals.
 */
const ABTestingDashboard: React.FC<ABTestingDashboardProps> = ({
  tenantId,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tests, setTests] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'active' | 'paused' | 'completed' | 'all'>('active');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [resultsDialogOpen, setResultsDialogOpen] = useState(false);
  const [selectedTest, setSelectedTest] = useState<any>(null);
  const [proposals, setProposals] = useState<any[]>([]);
  
  // Fetch tests
  useEffect(() => {
    const fetchTests = async () => {
      try {
        setLoading(true);
        setError(null);
        
        let response;
        if (activeTab === 'all') {
          response = await ABTestingService.getTests(tenantId);
        } else {
          response = await ABTestingService.getTests(tenantId, activeTab);
        }
        
        setTests(response);
        
        // Fetch proposals for the create dialog
        const proposalsResponse = await ProposalService.getProposals(tenantId, {
          status: ['draft', 'sent', 'viewed'],
          limit: 100,
          page: 1,
        });
        
        setProposals(proposalsResponse.proposals);
      } catch (err) {
        console.error('Error fetching A/B tests:', err);
        setError('Failed to load A/B tests. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTests();
  }, [tenantId, activeTab]);
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: 'active' | 'paused' | 'completed' | 'all') => {
    setActiveTab(newValue);
  };
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchTests = async () => {
      try {
        setLoading(true);
        setError(null);
        
        let response;
        if (activeTab === 'all') {
          response = await ABTestingService.getTests(tenantId);
        } else {
          response = await ABTestingService.getTests(tenantId, activeTab);
        }
        
        setTests(response);
      } catch (err) {
        console.error('Error fetching A/B tests:', err);
        setError('Failed to load A/B tests. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTests();
  };
  
  // Handle create test
  const handleCreateTest = async (testData: any) => {
    try {
      setLoading(true);
      setError(null);
      
      await ABTestingService.createTest(testData, tenantId);
      
      // Refresh tests
      handleRefresh();
      
      // Close dialog
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Error creating A/B test:', err);
      setError('Failed to create A/B test. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle edit test
  const handleEditTest = async (testData: any) => {
    try {
      setLoading(true);
      setError(null);
      
      await ABTestingService.updateTest(selectedTest._id, testData, tenantId);
      
      // Refresh tests
      handleRefresh();
      
      // Close dialog
      setEditDialogOpen(false);
    } catch (err) {
      console.error('Error updating A/B test:', err);
      setError('Failed to update A/B test. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle delete test
  const handleDeleteTest = async (deleteVariants: boolean) => {
    try {
      setLoading(true);
      setError(null);
      
      await ABTestingService.deleteTest(selectedTest._id, tenantId, deleteVariants);
      
      // Refresh tests
      handleRefresh();
      
      // Close dialog
      setDeleteDialogOpen(false);
    } catch (err) {
      console.error('Error deleting A/B test:', err);
      setError('Failed to delete A/B test. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle status change
  const handleStatusChange = async (testId: string, status: 'active' | 'paused' | 'completed') => {
    try {
      setLoading(true);
      setError(null);
      
      await ABTestingService.updateTest(testId, { status }, tenantId);
      
      // Refresh tests
      handleRefresh();
    } catch (err) {
      console.error('Error updating A/B test status:', err);
      setError('Failed to update A/B test status. Please try again.');
      setLoading(false);
    }
  };
  
  // Render loading state
  if (loading && tests.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          A/B Testing
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Create Test
          </Button>
        </Box>
      </Box>
      
      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant={isMobile ? "scrollable" : "fullWidth"}
          scrollButtons={isMobile ? "auto" : undefined}
        >
          <Tab label="Active" value="active" />
          <Tab label="Paused" value="paused" />
          <Tab label="Completed" value="completed" />
          <Tab label="All" value="all" />
        </Tabs>
      </Paper>
      
      {/* Tests Grid */}
      {tests.length === 0 ? (
        <Alert severity="info">
          No {activeTab !== 'all' ? activeTab : ''} A/B tests found. Create a new test to get started.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {tests.map((test) => (
            <Grid item xs={12} md={6} lg={4} key={test._id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="h6" noWrap sx={{ maxWidth: '70%' }}>
                      {test.name}
                    </Typography>
                    <Chip 
                      label={test.status.toUpperCase()} 
                      color={
                        test.status === 'active' ? 'success' :
                        test.status === 'paused' ? 'warning' :
                        'default'
                      }
                      size="small"
                    />
                  </Box>
                  
                  {test.description && (
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {test.description}
                    </Typography>
                  )}
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Typography variant="subtitle2" gutterBottom>
                    Base Proposal: {test.baseTrafficPercentage}% Traffic
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    Variants: {test.variants.length}
                  </Typography>
                  
                  <Box mt={1}>
                    {test.variants.map((variant: any) => (
                      <Chip 
                        key={variant.name}
                        label={`${variant.name}: ${variant.trafficPercentage}%`}
                        size="small"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                  </Box>
                  
                  <Typography variant="caption" display="block" color="text.secondary" mt={1}>
                    Started {formatDistance(new Date(test.startDate), new Date(), { addSuffix: true })}
                  </Typography>
                  
                  {test.endDate && (
                    <Typography variant="caption" display="block" color="text.secondary">
                      Ends {formatDistance(new Date(test.endDate), new Date(), { addSuffix: true })}
                    </Typography>
                  )}
                </CardContent>
                <CardActions>
                  <Tooltip title="View Results">
                    <IconButton 
                      onClick={() => {
                        setSelectedTest(test);
                        setResultsDialogOpen(true);
                      }}
                    >
                      <ChartIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit">
                    <IconButton 
                      onClick={() => {
                        setSelectedTest(test);
                        setEditDialogOpen(true);
                      }}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  {test.status === 'active' ? (
                    <Tooltip title="Pause">
                      <IconButton 
                        onClick={() => handleStatusChange(test._id, 'paused')}
                      >
                        <PauseIcon />
                      </IconButton>
                    </Tooltip>
                  ) : test.status === 'paused' ? (
                    <Tooltip title="Resume">
                      <IconButton 
                        onClick={() => handleStatusChange(test._id, 'active')}
                      >
                        <PlayIcon />
                      </IconButton>
                    </Tooltip>
                  ) : null}
                  {test.status !== 'completed' && (
                    <Tooltip title="Complete">
                      <IconButton 
                        onClick={() => handleStatusChange(test._id, 'completed')}
                      >
                        <CheckCircleIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Tooltip title="Delete">
                    <IconButton 
                      onClick={() => {
                        setSelectedTest(test);
                        setDeleteDialogOpen(true);
                      }}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
      
      {/* Create Dialog */}
      <CreateABTestDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateTest}
        proposals={proposals}
      />
      
      {/* Edit Dialog */}
      {selectedTest && (
        <Dialog
          open={editDialogOpen}
          onClose={() => setEditDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Edit A/B Test</DialogTitle>
          <DialogContent>
            <TextField
              label="Name"
              defaultValue={selectedTest.name}
              fullWidth
              margin="normal"
              id="name"
            />
            <TextField
              label="Description"
              defaultValue={selectedTest.description}
              fullWidth
              margin="normal"
              multiline
              rows={2}
              id="description"
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Status</InputLabel>
              <Select
                defaultValue={selectedTest.status}
                label="Status"
                id="status"
              >
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="paused">Paused</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
              </Select>
            </FormControl>
            
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
              Traffic Split
            </Typography>
            
            <TextField
              label="Base Proposal Traffic %"
              defaultValue={selectedTest.baseTrafficPercentage}
              type="number"
              fullWidth
              margin="normal"
              id="baseTrafficPercentage"
              InputProps={{ inputProps: { min: 0, max: 100 } }}
            />
            
            {selectedTest.variants.map((variant: any, index: number) => (
              <TextField
                key={variant.name}
                label={`${variant.name} Traffic %`}
                defaultValue={variant.trafficPercentage}
                type="number"
                fullWidth
                margin="normal"
                id={`variant-${index}`}
                InputProps={{ inputProps: { min: 0, max: 100 } }}
              />
            ))}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button 
              variant="contained" 
              onClick={() => {
                const name = (document.getElementById('name') as HTMLInputElement).value;
                const description = (document.getElementById('description') as HTMLInputElement).value;
                const status = (document.getElementById('status') as HTMLSelectElement).value as 'active' | 'paused' | 'completed';
                const baseTrafficPercentage = Number((document.getElementById('baseTrafficPercentage') as HTMLInputElement).value);
                
                const variants = selectedTest.variants.map((variant: any, index: number) => ({
                  name: variant.name,
                  trafficPercentage: Number((document.getElementById(`variant-${index}`) as HTMLInputElement).value),
                }));
                
                handleEditTest({
                  name,
                  description,
                  status,
                  baseTrafficPercentage,
                  variants,
                });
              }}
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      )}
      
      {/* Delete Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete A/B Test</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to delete this A/B test?
          </Typography>
          <FormControl fullWidth margin="normal">
            <InputLabel>Delete Variant Proposals</InputLabel>
            <Select
              defaultValue="false"
              label="Delete Variant Proposals"
              id="deleteVariants"
            >
              <MenuItem value="false">No, keep variant proposals</MenuItem>
              <MenuItem value="true">Yes, delete variant proposals</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            color="error"
            onClick={() => {
              const deleteVariants = (document.getElementById('deleteVariants') as HTMLSelectElement).value === 'true';
              handleDeleteTest(deleteVariants);
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Results Dialog */}
      {selectedTest && (
        <Dialog
          open={resultsDialogOpen}
          onClose={() => setResultsDialogOpen(false)}
          maxWidth="lg"
          fullWidth
        >
          <DialogTitle>
            Test Results: {selectedTest.name}
          </DialogTitle>
          <DialogContent>
            <ABTestResults testId={selectedTest._id} tenantId={tenantId} />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setResultsDialogOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};

export default ABTestingDashboard;
