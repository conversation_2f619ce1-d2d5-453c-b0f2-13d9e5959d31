import React, { useState } from 'react';
import { Button, Card, Input, Select, Badge, Pagination } from '@aizako/ui-kit';
import { IProposal } from '../../models/proposal';
import { formatCurrency } from '../../utils/formatters';

interface ProposalListProps {
  proposals: IProposal[];
  total: number;
  page: number;
  limit: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onViewProposal: (proposal: IProposal) => void;
  onEditProposal: (proposal: IProposal) => void;
  onDeleteProposal: (proposal: IProposal) => void;
  onSendProposal: (proposal: IProposal) => void;
  isLoading?: boolean;
}

/**
 * Proposal list component
 */
export const ProposalList: React.FC<ProposalListProps> = ({
  proposals,
  total,
  page,
  limit,
  onPageChange,
  onLimitChange,
  onViewProposal,
  onEditProposal,
  onDeleteProposal,
  onSendProposal,
  isLoading = false,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  // Format date
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString();
  };
  
  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'gray';
      case 'sent':
        return 'blue';
      case 'viewed':
        return 'purple';
      case 'accepted':
        return 'green';
      case 'rejected':
        return 'red';
      case 'expired':
        return 'yellow';
      default:
        return 'gray';
    }
  };
  
  // Filter proposals based on search term and status filter
  const filteredProposals = proposals.filter(proposal => {
    const matchesSearch = searchTerm === '' || 
      proposal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (proposal.description && proposal.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = statusFilter === 'all' || proposal.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search proposals..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="w-full sm:w-48">
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="draft">Draft</option>
            <option value="sent">Sent</option>
            <option value="viewed">Viewed</option>
            <option value="accepted">Accepted</option>
            <option value="rejected">Rejected</option>
            <option value="expired">Expired</option>
          </Select>
        </div>
      </div>
      
      {isLoading ? (
        <div className="text-center py-8">Loading proposals...</div>
      ) : filteredProposals.length === 0 ? (
        <div className="text-center py-8">No proposals found</div>
      ) : (
        <div className="space-y-4">
          {filteredProposals.map((proposal) => (
            <Card key={proposal._id.toString()} className="p-4">
              <div className="flex flex-col sm:flex-row justify-between">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <h3 className="text-lg font-medium">{proposal.title}</h3>
                    <Badge
                      variant={getStatusBadgeColor(proposal.status)}
                      className="ml-2"
                    >
                      {proposal.status}
                    </Badge>
                    {proposal.aiGenerated && (
                      <Badge variant="purple" className="ml-2">
                        AI Generated
                      </Badge>
                    )}
                  </div>
                  
                  {proposal.description && (
                    <p className="text-sm text-gray-600">{proposal.description}</p>
                  )}
                  
                  <div className="flex flex-wrap gap-x-4 gap-y-1 text-sm text-gray-500">
                    <div>Created: {formatDate(proposal.createdAt)}</div>
                    
                    {proposal.sentAt && (
                      <div>Sent: {formatDate(proposal.sentAt)}</div>
                    )}
                    
                    {proposal.viewedAt && (
                      <div>Viewed: {formatDate(proposal.viewedAt)}</div>
                    )}
                    
                    {proposal.expiresAt && (
                      <div>Expires: {formatDate(proposal.expiresAt)}</div>
                    )}
                    
                    {proposal.pricing && proposal.pricing.total > 0 && (
                      <div>
                        Value: {formatCurrency(proposal.pricing.total, proposal.pricing.currency)}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-row sm:flex-col justify-end gap-2 mt-4 sm:mt-0">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onViewProposal(proposal)}
                  >
                    View
                  </Button>
                  
                  {proposal.status === 'draft' && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onEditProposal(proposal)}
                      >
                        Edit
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="primary"
                        onClick={() => onSendProposal(proposal)}
                      >
                        Send
                      </Button>
                    </>
                  )}
                  
                  {proposal.status === 'draft' && (
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => onDeleteProposal(proposal)}
                    >
                      Delete
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
          
          <div className="flex justify-between items-center mt-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Show</span>
              <Select
                value={limit.toString()}
                onChange={(e) => onLimitChange(Number(e.target.value))}
                className="w-16"
              >
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </Select>
              <span className="text-sm text-gray-500">per page</span>
            </div>
            
            <Pagination
              currentPage={page}
              totalPages={Math.ceil(total / limit)}
              onPageChange={onPageChange}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProposalList;
