import React, { useState } from 'react';
import { Button, Card, Select, Checkbox, RadioGroup, RadioGroupItem, Label } from '@aizako/ui-kit';
import { IProposal } from '../../models/proposal';

interface ProposalDownloadOptionsProps {
  proposal: IProposal;
  onDownload: (format: string, options: {
    includeHeader: boolean;
    includeFooter: boolean;
    includeBranding: boolean;
    includePageNumbers: boolean;
    colorScheme: string;
    paperSize: string;
  }) => Promise<void>;
  isLoading?: boolean;
}

/**
 * Proposal download options component
 */
export const ProposalDownloadOptions: React.FC<ProposalDownloadOptionsProps> = ({
  proposal,
  onDownload,
  isLoading = false,
}) => {
  const [format, setFormat] = useState('pdf');
  const [includeHeader, setIncludeHeader] = useState(true);
  const [includeFooter, setIncludeFooter] = useState(true);
  const [includeBranding, setIncludeBranding] = useState(true);
  const [includePageNumbers, setIncludePageNumbers] = useState(true);
  const [colorScheme, setColorScheme] = useState('default');
  const [paperSize, setPaperSize] = useState('a4');
  
  // Handle download
  const handleDownload = async () => {
    await onDownload(format, {
      includeHeader,
      includeFooter,
      includeBranding,
      includePageNumbers,
      colorScheme,
      paperSize,
    });
  };
  
  // Check if format is available
  const isFormatAvailable = (formatToCheck: string): boolean => {
    if (!proposal.downloadFormats) return false;
    return proposal.downloadFormats.includes(formatToCheck);
  };
  
  return (
    <Card className="p-4">
      <h2 className="text-xl font-semibold mb-4">Download Options</h2>
      
      <div className="space-y-6">
        <div>
          <label htmlFor="format" className="block text-sm font-medium mb-1">Format</label>
          <Select
            id="format"
            value={format}
            onChange={(e) => setFormat(e.target.value)}
            className="w-full"
          >
            <option value="pdf" disabled={!isFormatAvailable('pdf')}>
              PDF {!isFormatAvailable('pdf') && '(Not Available)'}
            </option>
            <option value="docx" disabled={!isFormatAvailable('docx')}>
              DOCX {!isFormatAvailable('docx') && '(Not Available)'}
            </option>
            <option value="md" disabled={!isFormatAvailable('md')}>
              Markdown {!isFormatAvailable('md') && '(Not Available)'}
            </option>
          </Select>
        </div>
        
        {format === 'pdf' && (
          <>
            <div>
              <h3 className="text-md font-medium mb-2">Layout Options</h3>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <Checkbox
                    id="includeHeader"
                    checked={includeHeader}
                    onChange={(e) => setIncludeHeader(e.target.checked)}
                  />
                  <label htmlFor="includeHeader" className="ml-2 text-sm">
                    Include header with logo
                  </label>
                </div>
                
                <div className="flex items-center">
                  <Checkbox
                    id="includeFooter"
                    checked={includeFooter}
                    onChange={(e) => setIncludeFooter(e.target.checked)}
                  />
                  <label htmlFor="includeFooter" className="ml-2 text-sm">
                    Include footer with contact information
                  </label>
                </div>
                
                <div className="flex items-center">
                  <Checkbox
                    id="includeBranding"
                    checked={includeBranding}
                    onChange={(e) => setIncludeBranding(e.target.checked)}
                  />
                  <label htmlFor="includeBranding" className="ml-2 text-sm">
                    Include company branding and colors
                  </label>
                </div>
                
                <div className="flex items-center">
                  <Checkbox
                    id="includePageNumbers"
                    checked={includePageNumbers}
                    onChange={(e) => setIncludePageNumbers(e.target.checked)}
                  />
                  <label htmlFor="includePageNumbers" className="ml-2 text-sm">
                    Include page numbers
                  </label>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-md font-medium mb-2">Color Scheme</h3>
              
              <RadioGroup value={colorScheme} onValueChange={setColorScheme}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="default" id="colorDefault" />
                  <Label htmlFor="colorDefault">Default</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="professional" id="colorProfessional" />
                  <Label htmlFor="colorProfessional">Professional (Blue)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="creative" id="colorCreative" />
                  <Label htmlFor="colorCreative">Creative (Purple)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="modern" id="colorModern" />
                  <Label htmlFor="colorModern">Modern (Teal)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="classic" id="colorClassic" />
                  <Label htmlFor="colorClassic">Classic (Gray)</Label>
                </div>
              </RadioGroup>
            </div>
            
            <div>
              <h3 className="text-md font-medium mb-2">Paper Size</h3>
              
              <RadioGroup value={paperSize} onValueChange={setPaperSize}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="a4" id="sizeA4" />
                  <Label htmlFor="sizeA4">A4</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="letter" id="sizeLetter" />
                  <Label htmlFor="sizeLetter">US Letter</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="legal" id="sizeLegal" />
                  <Label htmlFor="sizeLegal">US Legal</Label>
                </div>
              </RadioGroup>
            </div>
          </>
        )}
        
        {format === 'docx' && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-700">
              The DOCX format will include editable content that can be modified in Microsoft Word or other compatible word processors. Basic styling and formatting will be preserved.
            </p>
          </div>
        )}
        
        {format === 'md' && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-700">
              The Markdown format provides plain text content with simple formatting that can be used in various Markdown-compatible applications and platforms.
            </p>
          </div>
        )}
        
        <div className="flex justify-end">
          <Button
            onClick={handleDownload}
            disabled={isLoading}
          >
            {isLoading ? 'Preparing Download...' : `Download as ${format.toUpperCase()}`}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ProposalDownloadOptions;
