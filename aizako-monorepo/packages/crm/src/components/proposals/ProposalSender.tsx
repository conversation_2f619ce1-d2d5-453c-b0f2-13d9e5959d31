import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Input, Textarea, Checkbox, Select } from '@aizako/ui-kit';
import { IProposal } from '../../models/proposal';
import { IContact } from '../../models/contact';

interface ProposalSenderProps {
  proposal: IProposal;
  contacts: IContact[];
  onSend: (emailData: {
    to: string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    message: string;
    includeLink: boolean;
    includeAttachment: boolean;
    attachmentFormat?: string;
    expiresAt?: Date;
  }) => Promise<void>;
  isLoading?: boolean;
}

/**
 * Proposal sender component
 */
export const ProposalSender: React.FC<ProposalSenderProps> = ({
  proposal,
  contacts,
  onSend,
  isLoading = false,
}) => {
  const [emailData, setEmailData] = useState({
    to: [] as string[],
    cc: [] as string[],
    bcc: [] as string[],
    subject: `Proposal: ${proposal.title}`,
    message: `Dear recipient,\n\nI'm pleased to share our proposal with you. Please review it at your convenience.\n\nBest regards,`,
    includeLink: true,
    includeAttachment: false,
    attachmentFormat: 'pdf',
    expiresAt: undefined as Date | undefined,
  });
  
  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEmailData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setEmailData(prev => ({
      ...prev,
      [name]: checked,
    }));
  };
  
  // Handle recipient selection
  const handleRecipientChange = (e: React.ChangeEvent<HTMLSelectElement>, type: 'to' | 'cc' | 'bcc') => {
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
    setEmailData(prev => ({
      ...prev,
      [type]: selectedOptions,
    }));
  };
  
  // Handle expiration date change
  const handleExpirationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    
    if (value === 'none') {
      setEmailData(prev => ({
        ...prev,
        expiresAt: undefined,
      }));
      return;
    }
    
    const days = parseInt(value, 10);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + days);
    
    setEmailData(prev => ({
      ...prev,
      expiresAt,
    }));
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSend(emailData);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Send Proposal</h2>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="to" className="block text-sm font-medium mb-1">To</label>
            <Select
              id="to"
              multiple
              value={emailData.to}
              onChange={(e) => handleRecipientChange(e, 'to')}
              required
              className="h-20"
            >
              {contacts.map((contact) => (
                <option key={contact._id.toString()} value={contact.email}>
                  {contact.firstName} {contact.lastName} ({contact.email})
                </option>
              ))}
            </Select>
            <p className="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple recipients</p>
          </div>
          
          <div>
            <label htmlFor="cc" className="block text-sm font-medium mb-1">CC</label>
            <Select
              id="cc"
              multiple
              value={emailData.cc}
              onChange={(e) => handleRecipientChange(e, 'cc')}
              className="h-20"
            >
              {contacts.map((contact) => (
                <option key={contact._id.toString()} value={contact.email}>
                  {contact.firstName} {contact.lastName} ({contact.email})
                </option>
              ))}
            </Select>
          </div>
          
          <div>
            <label htmlFor="bcc" className="block text-sm font-medium mb-1">BCC</label>
            <Select
              id="bcc"
              multiple
              value={emailData.bcc}
              onChange={(e) => handleRecipientChange(e, 'bcc')}
              className="h-20"
            >
              {contacts.map((contact) => (
                <option key={contact._id.toString()} value={contact.email}>
                  {contact.firstName} {contact.lastName} ({contact.email})
                </option>
              ))}
            </Select>
          </div>
          
          <div>
            <label htmlFor="subject" className="block text-sm font-medium mb-1">Subject</label>
            <Input
              id="subject"
              name="subject"
              value={emailData.subject}
              onChange={handleChange}
              required
            />
          </div>
          
          <div>
            <label htmlFor="message" className="block text-sm font-medium mb-1">Message</label>
            <Textarea
              id="message"
              name="message"
              value={emailData.message}
              onChange={handleChange}
              rows={5}
              required
            />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center">
              <Checkbox
                id="includeLink"
                name="includeLink"
                checked={emailData.includeLink}
                onChange={handleCheckboxChange}
              />
              <label htmlFor="includeLink" className="ml-2 text-sm">
                Include link to proposal
              </label>
            </div>
            
            <div className="flex items-center">
              <Checkbox
                id="includeAttachment"
                name="includeAttachment"
                checked={emailData.includeAttachment}
                onChange={handleCheckboxChange}
              />
              <label htmlFor="includeAttachment" className="ml-2 text-sm">
                Include proposal as attachment
              </label>
            </div>
            
            {emailData.includeAttachment && (
              <div className="ml-6">
                <label htmlFor="attachmentFormat" className="block text-sm font-medium mb-1">
                  Attachment Format
                </label>
                <Select
                  id="attachmentFormat"
                  name="attachmentFormat"
                  value={emailData.attachmentFormat}
                  onChange={handleChange}
                  className="w-full"
                >
                  <option value="pdf">PDF</option>
                  <option value="docx">DOCX</option>
                  {proposal.downloadFormats?.includes('md') && (
                    <option value="md">Markdown</option>
                  )}
                </Select>
              </div>
            )}
          </div>
          
          <div>
            <label htmlFor="expiresAt" className="block text-sm font-medium mb-1">
              Proposal Expiration
            </label>
            <Select
              id="expiresAt"
              onChange={handleExpirationChange}
              className="w-full"
              defaultValue="none"
            >
              <option value="none">No expiration</option>
              <option value="7">7 days</option>
              <option value="14">14 days</option>
              <option value="30">30 days</option>
              <option value="60">60 days</option>
              <option value="90">90 days</option>
            </Select>
          </div>
        </div>
      </Card>
      
      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Sending...' : 'Send Proposal'}
        </Button>
      </div>
    </form>
  );
};

export default ProposalSender;
