import React, { useState, useEffect } from 'react';
import { Button, Card, Input, Select, Textarea, Tabs, TabsContent, TabsList, TabsTrigger } from '@aizako/ui-kit';
import { IProposal, IProposalSection, IProposalPricingItem } from '../../models/proposal';
import { useAuth, useTenant } from '@aizako/core-lib';

interface ProposalFormProps {
  proposal?: Partial<IProposal>;
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
  onSubmit: (proposalData: Partial<IProposal>) => Promise<void>;
  isLoading?: boolean;
}

/**
 * Proposal form component
 */
export const ProposalForm: React.FC<ProposalFormProps> = ({
  proposal,
  opportunityId,
  companyId,
  contactIds,
  onSubmit,
  isLoading = false,
}) => {
  const { user } = useAuth();
  const { tenant } = useTenant();
  
  const [formData, setFormData] = useState<Partial<IProposal>>({
    title: '',
    description: '',
    opportunityId: opportunityId || '',
    companyId: companyId || '',
    contactIds: contactIds || [],
    sections: [],
    pricing: {
      currency: 'USD',
      items: [],
      subtotal: 0,
      total: 0,
    },
    terms: '',
    notes: '',
    owner: user?.id,
    createdBy: user?.id,
    tags: [],
    customFields: {},
    publicAccessEnabled: true,
    emailEnabled: true,
    downloadEnabled: true,
    downloadFormats: ['pdf', 'docx'],
    aiGenerated: false,
    ...proposal,
  });
  
  const [activeTab, setActiveTab] = useState('details');
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Calculate pricing totals when items change
  useEffect(() => {
    if (formData.pricing?.items) {
      const subtotal = formData.pricing.items.reduce((sum, item) => sum + item.total, 0);
      const discount = formData.pricing.discount || 0;
      const tax = formData.pricing.tax || 0;
      const total = subtotal - discount + tax;
      
      setFormData(prev => ({
        ...prev,
        pricing: {
          ...prev.pricing!,
          subtotal,
          total,
        },
      }));
    }
  }, [formData.pricing?.items, formData.pricing?.discount, formData.pricing?.tax]);
  
  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // Handle section change
  const handleSectionChange = (index: number, field: string, value: any) => {
    setFormData(prev => {
      const sections = [...(prev.sections || [])];
      sections[index] = {
        ...sections[index],
        [field]: value,
      };
      return {
        ...prev,
        sections,
      };
    });
  };
  
  // Add a new section
  const addSection = () => {
    const newSection: IProposalSection = {
      id: crypto.randomUUID(),
      title: 'New Section',
      content: '',
      order: formData.sections?.length || 0,
      type: 'text',
      isVisible: true,
    };
    
    setFormData(prev => ({
      ...prev,
      sections: [...(prev.sections || []), newSection],
    }));
  };
  
  // Remove a section
  const removeSection = (index: number) => {
    setFormData(prev => {
      const sections = [...(prev.sections || [])];
      sections.splice(index, 1);
      // Update order of remaining sections
      sections.forEach((section, i) => {
        section.order = i;
      });
      return {
        ...prev,
        sections,
      };
    });
  };
  
  // Handle pricing item change
  const handlePricingItemChange = (index: number, field: string, value: any) => {
    setFormData(prev => {
      const items = [...(prev.pricing?.items || [])];
      items[index] = {
        ...items[index],
        [field]: value,
      };
      
      // Recalculate total for this item
      if (field === 'quantity' || field === 'unitPrice' || field === 'discount') {
        const quantity = Number(items[index].quantity);
        const unitPrice = Number(items[index].unitPrice);
        const discount = Number(items[index].discount || 0);
        const tax = Number(items[index].tax || 0);
        
        const subtotal = quantity * unitPrice;
        const total = subtotal - discount + tax;
        
        items[index].total = total;
      }
      
      return {
        ...prev,
        pricing: {
          ...(prev.pricing || { currency: 'USD', subtotal: 0, total: 0 }),
          items,
        },
      };
    });
  };
  
  // Add a new pricing item
  const addPricingItem = () => {
    const newItem: IProposalPricingItem = {
      id: crypto.randomUUID(),
      name: 'New Item',
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0,
    };
    
    setFormData(prev => ({
      ...prev,
      pricing: {
        ...(prev.pricing || { currency: 'USD', subtotal: 0, total: 0 }),
        items: [...(prev.pricing?.items || []), newItem],
      },
    }));
  };
  
  // Remove a pricing item
  const removePricingItem = (index: number) => {
    setFormData(prev => {
      const items = [...(prev.pricing?.items || [])];
      items.splice(index, 1);
      
      return {
        ...prev,
        pricing: {
          ...(prev.pricing || { currency: 'USD', subtotal: 0, total: 0 }),
          items,
        },
      };
    });
  };
  
  // Generate proposal with AI
  const generateWithAI = async () => {
    if (!aiPrompt) return;
    
    setIsGenerating(true);
    
    try {
      // This would be replaced with an actual API call to generate content
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate AI-generated content
      const aiGeneratedProposal: Partial<IProposal> = {
        title: `Proposal for ${formData.companyId}`,
        description: 'AI-generated proposal based on your requirements',
        sections: [
          {
            id: crypto.randomUUID(),
            title: 'Executive Summary',
            content: `This proposal addresses the needs outlined in our discussions. We propose a comprehensive solution that will help you achieve your business objectives.`,
            order: 0,
            type: 'text',
            isVisible: true,
            aiGenerated: true,
          },
          {
            id: crypto.randomUUID(),
            title: 'Proposed Solution',
            content: `Based on your requirements, we recommend the following solution...`,
            order: 1,
            type: 'text',
            isVisible: true,
            aiGenerated: true,
          },
          {
            id: crypto.randomUUID(),
            title: 'Timeline',
            content: `The project will be completed in the following phases...`,
            order: 2,
            type: 'text',
            isVisible: true,
            aiGenerated: true,
          },
        ],
        pricing: {
          currency: 'USD',
          items: [
            {
              id: crypto.randomUUID(),
              name: 'Implementation',
              description: 'Initial setup and configuration',
              quantity: 1,
              unitPrice: 5000,
              total: 5000,
            },
            {
              id: crypto.randomUUID(),
              name: 'Monthly Subscription',
              description: 'Ongoing service and support',
              quantity: 12,
              unitPrice: 1000,
              total: 12000,
            },
          ],
          subtotal: 17000,
          total: 17000,
        },
        terms: 'Payment terms: 50% upfront, 50% upon completion',
        aiGenerated: true,
        aiPrompt,
      };
      
      setFormData(prev => ({
        ...prev,
        ...aiGeneratedProposal,
      }));
    } catch (error) {
      console.error('Error generating proposal with AI:', error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(formData);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="sections">Sections</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="ai">AI Generator</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="space-y-4 mt-4">
          <div>
            <label htmlFor="title" className="block text-sm font-medium">Title</label>
            <Input
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
            />
          </div>
          
          <div>
            <label htmlFor="description" className="block text-sm font-medium">Description</label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
            />
          </div>
          
          <div>
            <label htmlFor="terms" className="block text-sm font-medium">Terms & Conditions</label>
            <Textarea
              id="terms"
              name="terms"
              value={formData.terms}
              onChange={handleChange}
              rows={5}
            />
          </div>
          
          <div>
            <label htmlFor="notes" className="block text-sm font-medium">Internal Notes</label>
            <Textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="sections" className="space-y-4 mt-4">
          <Button type="button" onClick={addSection} variant="outline">Add Section</Button>
          
          {formData.sections?.map((section, index) => (
            <Card key={section.id} className="p-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Section {index + 1}</h3>
                <Button type="button" onClick={() => removeSection(index)} variant="destructive" size="sm">Remove</Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor={`section-${index}-title`} className="block text-sm font-medium">Title</label>
                  <Input
                    id={`section-${index}-title`}
                    value={section.title}
                    onChange={(e) => handleSectionChange(index, 'title', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor={`section-${index}-type`} className="block text-sm font-medium">Type</label>
                  <Select
                    id={`section-${index}-type`}
                    value={section.type}
                    onChange={(e) => handleSectionChange(index, 'type', e.target.value)}
                  >
                    <option value="text">Text</option>
                    <option value="pricing">Pricing</option>
                    <option value="timeline">Timeline</option>
                    <option value="team">Team</option>
                    <option value="testimonials">Testimonials</option>
                    <option value="images">Images</option>
                    <option value="custom">Custom</option>
                  </Select>
                </div>
                
                <div>
                  <label htmlFor={`section-${index}-content`} className="block text-sm font-medium">Content</label>
                  <Textarea
                    id={`section-${index}-content`}
                    value={section.content}
                    onChange={(e) => handleSectionChange(index, 'content', e.target.value)}
                    rows={5}
                    required
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id={`section-${index}-visible`}
                    checked={section.isVisible}
                    onChange={(e) => handleSectionChange(index, 'isVisible', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor={`section-${index}-visible`} className="text-sm">Visible in proposal</label>
                </div>
              </div>
            </Card>
          ))}
        </TabsContent>
        
        <TabsContent value="pricing" className="space-y-4 mt-4">
          <div>
            <label htmlFor="currency" className="block text-sm font-medium">Currency</label>
            <Select
              id="currency"
              value={formData.pricing?.currency || 'USD'}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                pricing: {
                  ...(prev.pricing || { items: [], subtotal: 0, total: 0 }),
                  currency: e.target.value,
                },
              }))}
            >
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
              <option value="CAD">CAD ($)</option>
              <option value="AUD">AUD ($)</option>
            </Select>
          </div>
          
          <Button type="button" onClick={addPricingItem} variant="outline">Add Item</Button>
          
          {formData.pricing?.items?.map((item, index) => (
            <Card key={item.id} className="p-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Item {index + 1}</h3>
                <Button type="button" onClick={() => removePricingItem(index)} variant="destructive" size="sm">Remove</Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor={`item-${index}-name`} className="block text-sm font-medium">Name</label>
                  <Input
                    id={`item-${index}-name`}
                    value={item.name}
                    onChange={(e) => handlePricingItemChange(index, 'name', e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor={`item-${index}-description`} className="block text-sm font-medium">Description</label>
                  <Textarea
                    id={`item-${index}-description`}
                    value={item.description || ''}
                    onChange={(e) => handlePricingItemChange(index, 'description', e.target.value)}
                    rows={2}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor={`item-${index}-quantity`} className="block text-sm font-medium">Quantity</label>
                    <Input
                      id={`item-${index}-quantity`}
                      type="number"
                      min="0"
                      step="1"
                      value={item.quantity}
                      onChange={(e) => handlePricingItemChange(index, 'quantity', Number(e.target.value))}
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor={`item-${index}-unitPrice`} className="block text-sm font-medium">Unit Price</label>
                    <Input
                      id={`item-${index}-unitPrice`}
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.unitPrice}
                      onChange={(e) => handlePricingItemChange(index, 'unitPrice', Number(e.target.value))}
                      required
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor={`item-${index}-discount`} className="block text-sm font-medium">Discount</label>
                    <Input
                      id={`item-${index}-discount`}
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.discount || 0}
                      onChange={(e) => handlePricingItemChange(index, 'discount', Number(e.target.value))}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor={`item-${index}-tax`} className="block text-sm font-medium">Tax</label>
                    <Input
                      id={`item-${index}-tax`}
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.tax || 0}
                      onChange={(e) => handlePricingItemChange(index, 'tax', Number(e.target.value))}
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor={`item-${index}-total`} className="block text-sm font-medium">Total</label>
                  <Input
                    id={`item-${index}-total`}
                    type="number"
                    value={item.total}
                    readOnly
                    disabled
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id={`item-${index}-optional`}
                    checked={item.isOptional || false}
                    onChange={(e) => handlePricingItemChange(index, 'isOptional', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor={`item-${index}-optional`} className="text-sm">Optional item</label>
                </div>
              </div>
            </Card>
          ))}
          
          <div className="mt-4 space-y-2">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>{formData.pricing?.currency} {formData.pricing?.subtotal?.toFixed(2)}</span>
            </div>
            
            <div>
              <label htmlFor="discount" className="block text-sm font-medium">Discount</label>
              <Input
                id="discount"
                type="number"
                min="0"
                step="0.01"
                value={formData.pricing?.discount || 0}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pricing: {
                    ...(prev.pricing || { items: [], subtotal: 0, total: 0 }),
                    discount: Number(e.target.value),
                  },
                }))}
              />
            </div>
            
            <div>
              <label htmlFor="tax" className="block text-sm font-medium">Tax</label>
              <Input
                id="tax"
                type="number"
                min="0"
                step="0.01"
                value={formData.pricing?.tax || 0}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pricing: {
                    ...(prev.pricing || { items: [], subtotal: 0, total: 0 }),
                    tax: Number(e.target.value),
                  },
                }))}
              />
            </div>
            
            <div className="flex justify-between font-bold">
              <span>Total:</span>
              <span>{formData.pricing?.currency} {formData.pricing?.total?.toFixed(2)}</span>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="settings" className="space-y-4 mt-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Sharing Options</h3>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="publicAccessEnabled"
                checked={formData.publicAccessEnabled}
                onChange={(e) => setFormData(prev => ({ ...prev, publicAccessEnabled: e.target.checked }))}
                className="mr-2"
              />
              <label htmlFor="publicAccessEnabled" className="text-sm">Enable public access via link</label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="emailEnabled"
                checked={formData.emailEnabled}
                onChange={(e) => setFormData(prev => ({ ...prev, emailEnabled: e.target.checked }))}
                className="mr-2"
              />
              <label htmlFor="emailEnabled" className="text-sm">Enable email sharing</label>
            </div>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Download Options</h3>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="downloadEnabled"
                checked={formData.downloadEnabled}
                onChange={(e) => setFormData(prev => ({ ...prev, downloadEnabled: e.target.checked }))}
                className="mr-2"
              />
              <label htmlFor="downloadEnabled" className="text-sm">Enable downloads</label>
            </div>
            
            {formData.downloadEnabled && (
              <div className="ml-6 space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="downloadPdf"
                    checked={formData.downloadFormats?.includes('pdf')}
                    onChange={(e) => {
                      const formats = [...(formData.downloadFormats || [])];
                      if (e.target.checked) {
                        if (!formats.includes('pdf')) formats.push('pdf');
                      } else {
                        const index = formats.indexOf('pdf');
                        if (index !== -1) formats.splice(index, 1);
                      }
                      setFormData(prev => ({ ...prev, downloadFormats: formats }));
                    }}
                    className="mr-2"
                  />
                  <label htmlFor="downloadPdf" className="text-sm">PDF</label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="downloadDocx"
                    checked={formData.downloadFormats?.includes('docx')}
                    onChange={(e) => {
                      const formats = [...(formData.downloadFormats || [])];
                      if (e.target.checked) {
                        if (!formats.includes('docx')) formats.push('docx');
                      } else {
                        const index = formats.indexOf('docx');
                        if (index !== -1) formats.splice(index, 1);
                      }
                      setFormData(prev => ({ ...prev, downloadFormats: formats }));
                    }}
                    className="mr-2"
                  />
                  <label htmlFor="downloadDocx" className="text-sm">DOCX</label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="downloadMd"
                    checked={formData.downloadFormats?.includes('md')}
                    onChange={(e) => {
                      const formats = [...(formData.downloadFormats || [])];
                      if (e.target.checked) {
                        if (!formats.includes('md')) formats.push('md');
                      } else {
                        const index = formats.indexOf('md');
                        if (index !== -1) formats.splice(index, 1);
                      }
                      setFormData(prev => ({ ...prev, downloadFormats: formats }));
                    }}
                    className="mr-2"
                  />
                  <label htmlFor="downloadMd" className="text-sm">Markdown</label>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="ai" className="space-y-4 mt-4">
          <div>
            <label htmlFor="aiPrompt" className="block text-sm font-medium">AI Prompt</label>
            <Textarea
              id="aiPrompt"
              value={aiPrompt}
              onChange={(e) => setAiPrompt(e.target.value)}
              placeholder="Describe the proposal you want to generate..."
              rows={5}
            />
          </div>
          
          <Button
            type="button"
            onClick={generateWithAI}
            disabled={isGenerating || !aiPrompt}
          >
            {isGenerating ? 'Generating...' : 'Generate Proposal'}
          </Button>
          
          {formData.aiGenerated && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-700">
                This proposal was generated with AI based on your prompt. You can edit any part of it as needed.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Proposal'}
        </Button>
      </div>
    </form>
  );
};

export default ProposalForm;
