import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  ButtonGroup,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import { IProposal } from '../../types/proposals';
import { formatCurrency, formatDate } from '../../utils/formatters';
import { sanitizeHtml } from '../../utils/sanitize';

interface ProposalPreviewProps {
  proposal: IProposal;
}

/**
 * Proposal Preview Component
 * 
 * This component provides a preview of how the proposal will look when exported.
 * It supports different view modes and formats.
 */
const ProposalPreview: React.FC<ProposalPreviewProps> = ({ proposal }) => {
  const theme = useTheme();
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile' | 'print'>('desktop');
  const [colorScheme, setColorScheme] = useState<'professional' | 'creative' | 'modern' | 'classic'>('professional');
  
  // Get primary color based on color scheme
  const getPrimaryColor = () => {
    switch (colorScheme) {
      case 'professional':
        return theme.palette.primary.main;
      case 'creative':
        return theme.palette.secondary.main;
      case 'modern':
        return theme.palette.info.main;
      case 'classic':
        return theme.palette.grey[800];
      default:
        return theme.palette.primary.main;
    }
  };
  
  // Get container width based on view mode
  const getContainerWidth = () => {
    switch (viewMode) {
      case 'desktop':
        return '100%';
      case 'mobile':
        return '375px';
      case 'print':
        return '210mm'; // A4 width
      default:
        return '100%';
    }
  };
  
  // Get container height based on view mode
  const getContainerHeight = () => {
    if (viewMode === 'print') {
      return '297mm'; // A4 height
    }
    return 'auto';
  };
  
  // Get container padding based on view mode
  const getContainerPadding = () => {
    switch (viewMode) {
      case 'desktop':
        return theme.spacing(4);
      case 'mobile':
        return theme.spacing(2);
      case 'print':
        return theme.spacing(4);
      default:
        return theme.spacing(4);
    }
  };
  
  // Render HTML content safely
  const renderHtml = (html: string) => {
    return <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(html) }} />;
  };
  
  // Primary color for the selected color scheme
  const primaryColor = getPrimaryColor();
  
  return (
    <Box>
      {/* Preview Controls */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <ButtonGroup variant="outlined" size="small">
          <Button
            variant={viewMode === 'desktop' ? 'contained' : 'outlined'}
            onClick={() => setViewMode('desktop')}
          >
            Desktop
          </Button>
          <Button
            variant={viewMode === 'mobile' ? 'contained' : 'outlined'}
            onClick={() => setViewMode('mobile')}
          >
            Mobile
          </Button>
          <Button
            variant={viewMode === 'print' ? 'contained' : 'outlined'}
            onClick={() => setViewMode('print')}
          >
            Print
          </Button>
        </ButtonGroup>
        
        <ButtonGroup variant="outlined" size="small">
          <Button
            variant={colorScheme === 'professional' ? 'contained' : 'outlined'}
            onClick={() => setColorScheme('professional')}
            sx={{ bgcolor: colorScheme === 'professional' ? theme.palette.primary.main : 'transparent' }}
          >
            Professional
          </Button>
          <Button
            variant={colorScheme === 'creative' ? 'contained' : 'outlined'}
            onClick={() => setColorScheme('creative')}
            sx={{ bgcolor: colorScheme === 'creative' ? theme.palette.secondary.main : 'transparent' }}
          >
            Creative
          </Button>
          <Button
            variant={colorScheme === 'modern' ? 'contained' : 'outlined'}
            onClick={() => setColorScheme('modern')}
            sx={{ bgcolor: colorScheme === 'modern' ? theme.palette.info.main : 'transparent' }}
          >
            Modern
          </Button>
          <Button
            variant={colorScheme === 'classic' ? 'contained' : 'outlined'}
            onClick={() => setColorScheme('classic')}
            sx={{ bgcolor: colorScheme === 'classic' ? theme.palette.grey[800] : 'transparent', color: colorScheme === 'classic' ? 'white' : 'inherit' }}
          >
            Classic
          </Button>
        </ButtonGroup>
      </Box>
      
      {/* Preview Container */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-start',
          bgcolor: theme.palette.grey[100],
          p: 2,
          minHeight: '80vh',
          borderRadius: 1,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            width: getContainerWidth(),
            height: getContainerHeight(),
            p: getContainerPadding(),
            overflow: 'auto',
            ...(viewMode === 'print' && {
              boxShadow: '0 0 10px rgba(0,0,0,0.1)',
            }),
          }}
        >
          {/* Header */}
          <Box
            sx={{
              borderBottom: `4px solid ${primaryColor}`,
              pb: 2,
              mb: 4,
            }}
          >
            <Typography
              variant="h4"
              component="h1"
              gutterBottom
              sx={{ color: primaryColor, fontWeight: 'bold' }}
            >
              {proposal.title || 'Untitled Proposal'}
            </Typography>
            
            {proposal.description && (
              <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                {proposal.description}
              </Typography>
            )}
            
            <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
              <Chip
                label={proposal.status?.toUpperCase() || 'DRAFT'}
                color={
                  proposal.status === 'accepted' ? 'success' :
                  proposal.status === 'rejected' ? 'error' :
                  proposal.status === 'sent' ? 'primary' :
                  proposal.status === 'viewed' ? 'info' :
                  'default'
                }
                size="small"
              />
              
              <Typography variant="body2" color="text.secondary">
                {formatDate(proposal.createdAt || new Date())}
              </Typography>
            </Box>
          </Box>
          
          {/* Sections */}
          {proposal.sections
            ?.filter(section => section.isVisible)
            .sort((a, b) => a.order - b.order)
            .map((section, index) => (
              <Box key={section.id} mb={4}>
                <Typography
                  variant="h5"
                  component="h2"
                  gutterBottom
                  sx={{
                    color: primaryColor,
                    borderBottom: `2px solid ${alpha(primaryColor, 0.3)}`,
                    pb: 1,
                  }}
                >
                  {section.title}
                </Typography>
                
                <Box sx={{ mt: 2 }}>
                  {renderHtml(section.content)}
                </Box>
              </Box>
            ))}
          
          {/* Pricing */}
          {proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (
            <Box mb={4}>
              <Typography
                variant="h5"
                component="h2"
                gutterBottom
                sx={{
                  color: primaryColor,
                  borderBottom: `2px solid ${alpha(primaryColor, 0.3)}`,
                  pb: 1,
                }}
              >
                Pricing
              </Typography>
              
              <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
                <Table>
                  <TableHead sx={{ bgcolor: alpha(primaryColor, 0.1) }}>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold' }}>Item</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Quantity</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Unit Price</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {proposal.pricing.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {item.name}
                          </Typography>
                          {item.description && (
                            <Typography variant="caption" color="text.secondary">
                              {item.description}
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell align="right">
                          {formatCurrency(item.unitPrice, proposal.pricing?.currency)}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(item.total, proposal.pricing?.currency)}
                        </TableCell>
                      </TableRow>
                    ))}
                    
                    {/* Subtotal */}
                    <TableRow>
                      <TableCell colSpan={3} align="right" sx={{ fontWeight: 'medium' }}>
                        Subtotal
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(proposal.pricing.subtotal, proposal.pricing.currency)}
                      </TableCell>
                    </TableRow>
                    
                    {/* Discount */}
                    {proposal.pricing.discount && proposal.pricing.discount > 0 && (
                      <TableRow>
                        <TableCell colSpan={3} align="right" sx={{ fontWeight: 'medium' }}>
                          Discount
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(proposal.pricing.discount, proposal.pricing.currency)}
                        </TableCell>
                      </TableRow>
                    )}
                    
                    {/* Tax */}
                    {proposal.pricing.tax && proposal.pricing.tax > 0 && (
                      <TableRow>
                        <TableCell colSpan={3} align="right" sx={{ fontWeight: 'medium' }}>
                          Tax
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(proposal.pricing.tax, proposal.pricing.currency)}
                        </TableCell>
                      </TableRow>
                    )}
                    
                    {/* Total */}
                    <TableRow>
                      <TableCell 
                        colSpan={3} 
                        align="right" 
                        sx={{ fontWeight: 'bold', color: primaryColor }}
                      >
                        Total
                      </TableCell>
                      <TableCell 
                        align="right"
                        sx={{ fontWeight: 'bold', color: primaryColor }}
                      >
                        {formatCurrency(proposal.pricing.total, proposal.pricing.currency)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
          
          {/* Terms */}
          {proposal.terms && (
            <Box mb={4}>
              <Typography
                variant="h5"
                component="h2"
                gutterBottom
                sx={{
                  color: primaryColor,
                  borderBottom: `2px solid ${alpha(primaryColor, 0.3)}`,
                  pb: 1,
                }}
              >
                Terms & Conditions
              </Typography>
              
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line', mt: 2 }}>
                {proposal.terms}
              </Typography>
            </Box>
          )}
          
          {/* Footer */}
          <Divider sx={{ mt: 4, mb: 2 }} />
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="caption" color="text.secondary">
              Generated on {formatDate(new Date())}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Confidential
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default ProposalPreview;
