import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Divider,
  Switch,
  FormControlLabel,
  Chip,
  CircularProgress,
  Alert,
  Stepper,
  Step,
  StepLabel,
  FormGroup,
  Checkbox,
  Grid,
  useTheme,
} from '@mui/material';
import {
  Description as DescriptionIcon,
  AutoAwesome as AIIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { formatCurrency } from '../../../utils/formatters';

interface CreateProposalFromOpportunityDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (proposalData: any) => void;
  opportunity: any;
}

/**
 * CreateProposalFromOpportunityDialog Component
 * 
 * This component displays a dialog for creating a new proposal from opportunity data.
 */
const CreateProposalFromOpportunityDialog: React.FC<CreateProposalFromOpportunityDialogProps> = ({
  open,
  onClose,
  onSubmit,
  opportunity,
}) => {
  const theme = useTheme();
  
  // State
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useAI, setUseAI] = useState(true);
  const [title, setTitle] = useState(`Proposal for ${opportunity?.company?.name || 'Client'}`);
  const [description, setDescription] = useState(`Proposal for ${opportunity?.name || 'Opportunity'}`);
  const [aiPrompt, setAiPrompt] = useState('');
  const [aiModel, setAiModel] = useState('claude-3-opus-20240229');
  const [includeSections, setIncludeSections] = useState({
    executiveSummary: true,
    solution: true,
    timeline: true,
    pricing: true,
    team: false,
    testimonials: false,
    terms: true,
  });
  const [includeContacts, setIncludeContacts] = useState<string[]>(
    opportunity?.contactIds || []
  );
  
  // Reset state when dialog opens
  React.useEffect(() => {
    if (open) {
      setActiveStep(0);
      setLoading(false);
      setError(null);
      setUseAI(true);
      setTitle(`Proposal for ${opportunity?.company?.name || 'Client'}`);
      setDescription(`Proposal for ${opportunity?.name || 'Opportunity'}`);
      setAiPrompt('');
      setAiModel('claude-3-opus-20240229');
      setIncludeSections({
        executiveSummary: true,
        solution: true,
        timeline: true,
        pricing: true,
        team: false,
        testimonials: false,
        terms: true,
      });
      setIncludeContacts(opportunity?.contactIds || []);
    }
  }, [open, opportunity]);
  
  // Handle next step
  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };
  
  // Handle back step
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };
  
  // Handle submit
  const handleSubmit = () => {
    // Validate
    if (!title) {
      setError('Please enter a proposal title.');
      return;
    }
    
    // Prepare data
    const proposalData: any = {
      title,
      description,
      status: 'draft',
      opportunityId: opportunity._id,
      companyId: opportunity.companyId,
      contactIds: includeContacts,
    };
    
    // Add AI data if using AI
    if (useAI) {
      proposalData.aiGenerated = true;
      proposalData.aiPrompt = aiPrompt || `Create a proposal for ${opportunity.name}`;
      proposalData.aiModel = aiModel;
      proposalData.includeSections = includeSections;
    }
    
    // Submit
    onSubmit(proposalData);
  };
  
  // Handle section toggle
  const handleSectionToggle = (section: string) => {
    setIncludeSections({
      ...includeSections,
      [section]: !includeSections[section as keyof typeof includeSections],
    });
  };
  
  // Handle contact toggle
  const handleContactToggle = (contactId: string) => {
    if (includeContacts.includes(contactId)) {
      setIncludeContacts(includeContacts.filter(id => id !== contactId));
    } else {
      setIncludeContacts([...includeContacts, contactId]);
    }
  };
  
  // Steps
  const steps = ['Basic Information', 'Content Options', 'Review'];
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>Create Proposal from Opportunity</DialogTitle>
      <DialogContent>
        {/* Stepper */}
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {/* Error */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {/* Step Content */}
        {activeStep === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            
            <TextField
              label="Proposal Title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              fullWidth
              margin="normal"
              required
            />
            
            <TextField
              label="Proposal Description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              fullWidth
              margin="normal"
              multiline
              rows={2}
            />
            
            <Box mt={3}>
              <Typography variant="subtitle1" gutterBottom>
                Opportunity Details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <BusinessIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {opportunity.company?.name || 'No Company'}
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center" mb={1}>
                    <MoneyIcon color="success" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {formatCurrency(opportunity.amount || 0)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <PersonIcon color="info" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {opportunity.contacts?.length || 0} Contacts
                    </Typography>
                  </Box>
                  <Chip 
                    label={opportunity.stage} 
                    color={
                      opportunity.stage === 'won' ? 'success' :
                      opportunity.stage === 'lost' ? 'error' :
                      'primary'
                    }
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
        )}
        
        {activeStep === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Content Options
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={useAI}
                  onChange={(e) => setUseAI(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box display="flex" alignItems="center">
                  <AIIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="body1">
                    Generate content with AI
                  </Typography>
                </Box>
              }
              sx={{ mb: 2 }}
            />
            
            {useAI && (
              <>
                <TextField
                  label="AI Prompt (Optional)"
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  fullWidth
                  margin="normal"
                  multiline
                  rows={3}
                  placeholder="Enter specific instructions for the AI to follow when generating the proposal..."
                  helperText="Leave blank to use default prompt based on opportunity details"
                />
                
                <FormControl fullWidth margin="normal">
                  <InputLabel>AI Model</InputLabel>
                  <Select
                    value={aiModel}
                    onChange={(e) => setAiModel(e.target.value)}
                    label="AI Model"
                  >
                    <MenuItem value="claude-3-opus-20240229">Claude 3 Opus (Highest Quality)</MenuItem>
                    <MenuItem value="claude-3-sonnet-20240229">Claude 3 Sonnet (Balanced)</MenuItem>
                    <MenuItem value="claude-3-haiku-20240307">Claude 3 Haiku (Fastest)</MenuItem>
                  </Select>
                </FormControl>
                
                <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                  Sections to Include
                </Typography>
                
                <FormGroup>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeSections.executiveSummary}
                            onChange={() => handleSectionToggle('executiveSummary')}
                          />
                        }
                        label="Executive Summary"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeSections.solution}
                            onChange={() => handleSectionToggle('solution')}
                          />
                        }
                        label="Solution"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeSections.timeline}
                            onChange={() => handleSectionToggle('timeline')}
                          />
                        }
                        label="Timeline"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeSections.pricing}
                            onChange={() => handleSectionToggle('pricing')}
                          />
                        }
                        label="Pricing"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeSections.team}
                            onChange={() => handleSectionToggle('team')}
                          />
                        }
                        label="Team"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeSections.testimonials}
                            onChange={() => handleSectionToggle('testimonials')}
                          />
                        }
                        label="Testimonials"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeSections.terms}
                            onChange={() => handleSectionToggle('terms')}
                          />
                        }
                        label="Terms & Conditions"
                      />
                    </Grid>
                  </Grid>
                </FormGroup>
              </>
            )}
            
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
              Include Contacts
            </Typography>
            
            {opportunity.contacts?.length > 0 ? (
              <FormGroup>
                <Grid container spacing={2}>
                  {opportunity.contacts.map((contact: any) => (
                    <Grid item xs={12} sm={6} key={contact._id}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={includeContacts.includes(contact._id)}
                            onChange={() => handleContactToggle(contact._id)}
                          />
                        }
                        label={`${contact.name} (${contact.title || 'No Title'})`}
                      />
                    </Grid>
                  ))}
                </Grid>
              </FormGroup>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No contacts associated with this opportunity.
              </Typography>
            )}
          </Box>
        )}
        
        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Proposal Details
                </Typography>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Title
                  </Typography>
                  <Typography variant="body1">
                    {title}
                  </Typography>
                </Box>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Description
                  </Typography>
                  <Typography variant="body1">
                    {description}
                  </Typography>
                </Box>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Content Generation
                  </Typography>
                  <Typography variant="body1">
                    {useAI ? 'AI-Generated' : 'Manual'}
                  </Typography>
                </Box>
                {useAI && (
                  <Box mb={2}>
                    <Typography variant="body2" color="text.secondary">
                      AI Model
                    </Typography>
                    <Typography variant="body1">
                      {aiModel === 'claude-3-opus-20240229' ? 'Claude 3 Opus' :
                       aiModel === 'claude-3-sonnet-20240229' ? 'Claude 3 Sonnet' :
                       'Claude 3 Haiku'}
                    </Typography>
                  </Box>
                )}
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Included Sections
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                  {useAI ? (
                    <>
                      {includeSections.executiveSummary && <Chip label="Executive Summary" />}
                      {includeSections.solution && <Chip label="Solution" />}
                      {includeSections.timeline && <Chip label="Timeline" />}
                      {includeSections.pricing && <Chip label="Pricing" />}
                      {includeSections.team && <Chip label="Team" />}
                      {includeSections.testimonials && <Chip label="Testimonials" />}
                      {includeSections.terms && <Chip label="Terms & Conditions" />}
                    </>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No sections will be pre-generated. You'll need to add them manually.
                    </Typography>
                  )}
                </Box>
                
                <Typography variant="subtitle1" gutterBottom>
                  Included Contacts
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {includeContacts.length > 0 ? (
                    opportunity.contacts
                      .filter((contact: any) => includeContacts.includes(contact._id))
                      .map((contact: any) => (
                        <Chip key={contact._id} label={contact.name} />
                      ))
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No contacts included.
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
            
            <Alert severity="info" sx={{ mt: 3 }}>
              <Typography variant="body2">
                {useAI ? 
                  'The proposal will be generated with AI based on the opportunity details. You can edit it after creation.' :
                  'You will need to manually add content to the proposal after creation.'}
              </Typography>
            </Alert>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {activeStep > 0 && (
          <Button onClick={handleBack}>
            Back
          </Button>
        )}
        {activeStep < steps.length - 1 ? (
          <Button 
            variant="contained" 
            onClick={handleNext}
            disabled={activeStep === 0 && !title}
          >
            Next
          </Button>
        ) : (
          <Button 
            variant="contained" 
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Create Proposal'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CreateProposalFromOpportunityDialog;
