import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  useTheme,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
  Avatar,
  ListItemAvatar,
  LinearProgress,
} from '@mui/material';
import {
  Assignment as TaskIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  CheckCircle as CompleteIcon,
  RadioButtonUnchecked as IncompleteIcon,
  Flag as PriorityIcon,
  Person as AssigneeIcon,
  CalendarToday as DueDateIcon,
  AccessTime as TimeIcon,
} from '@mui/icons-material';
import { format, addDays } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { TaskService } from '../../../services/task-service';
import { useAuth } from '../../../hooks/useAuth';

interface ProposalTaskIntegrationProps {
  proposalId: string;
  tenantId: string;
  onViewTask?: (taskId: string) => void;
}

/**
 * ProposalTaskIntegration Component
 * 
 * This component displays tasks associated with a proposal and allows
 * creating new tasks.
 */
const ProposalTaskIntegration: React.FC<ProposalTaskIntegrationProps> = ({
  proposalId,
  tenantId,
  onViewTask,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<any>(null);
  const [tasks, setTasks] = useState<any[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [createError, setCreateError] = useState<string | null>(null);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [taskPriority, setTaskPriority] = useState('medium');
  const [taskDueDate, setTaskDueDate] = useState('');
  const [taskAssignee, setTaskAssignee] = useState('');
  const [taskEstimatedTime, setTaskEstimatedTime] = useState(30);
  const [availableAssignees, setAvailableAssignees] = useState<any[]>([]);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  
  // Fetch proposal and tasks
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposal
        const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
        setProposal(proposalData);
        
        // Set default task title and description
        setTaskTitle(`Follow-up for ${proposalData.title}`);
        setTaskDescription(`Follow-up task for proposal: ${proposalData.title}`);
        
        // Set default due date (3 days from now)
        const dueDate = addDays(new Date(), 3);
        setTaskDueDate(dueDate.toISOString().split('T')[0]);
        
        // Fetch tasks
        const tasksData = await TaskService.getTasksByProposal(proposalId, tenantId);
        setTasks(tasksData);
        
        // Fetch available assignees
        const teamMembers = await TaskService.getTeamMembers(tenantId);
        setAvailableAssignees(teamMembers);
        
        // Set default assignee (current user)
        setTaskAssignee(user?.id || '');
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [proposalId, tenantId, user?.id]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch tasks
        const tasksData = await TaskService.getTasksByProposal(proposalId, tenantId);
        setTasks(tasksData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Handle create task
  const handleCreateTask = async () => {
    try {
      setCreateLoading(true);
      setCreateError(null);
      
      // Prepare task data
      const taskData = {
        title: taskTitle,
        description: taskDescription,
        proposalId,
        priority: taskPriority,
        dueDate: new Date(taskDueDate),
        assigneeId: taskAssignee,
        estimatedTime: taskEstimatedTime,
        status: 'todo',
      };
      
      // Create task
      await TaskService.createTask(taskData, tenantId);
      
      // Refresh tasks
      handleRefresh();
      
      // Close dialog
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Error creating task:', err);
      setCreateError('Failed to create task. Please try again.');
    } finally {
      setCreateLoading(false);
    }
  };
  
  // Handle update task
  const handleUpdateTask = async () => {
    try {
      setCreateLoading(true);
      setCreateError(null);
      
      if (!selectedTask) return;
      
      // Prepare task data
      const taskData = {
        title: taskTitle,
        description: taskDescription,
        priority: taskPriority,
        dueDate: new Date(taskDueDate),
        assigneeId: taskAssignee,
        estimatedTime: taskEstimatedTime,
      };
      
      // Update task
      await TaskService.updateTask(selectedTask._id, taskData, tenantId);
      
      // Refresh tasks
      handleRefresh();
      
      // Close dialog
      setEditDialogOpen(false);
    } catch (err) {
      console.error('Error updating task:', err);
      setCreateError('Failed to update task. Please try again.');
    } finally {
      setCreateLoading(false);
    }
  };
  
  // Handle delete task
  const handleDeleteTask = async (taskId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await TaskService.deleteTask(taskId, tenantId);
      
      // Refresh tasks
      handleRefresh();
    } catch (err) {
      console.error('Error deleting task:', err);
      setError('Failed to delete task. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle complete task
  const handleCompleteTask = async (taskId: string, isComplete: boolean) => {
    try {
      setLoading(true);
      setError(null);
      
      await TaskService.updateTask(taskId, {
        status: isComplete ? 'done' : 'todo',
      }, tenantId);
      
      // Refresh tasks
      handleRefresh();
    } catch (err) {
      console.error('Error updating task status:', err);
      setError('Failed to update task status. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle edit task
  const handleEditTask = (task: any) => {
    setSelectedTask(task);
    setTaskTitle(task.title);
    setTaskDescription(task.description || '');
    setTaskPriority(task.priority);
    setTaskDueDate(task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '');
    setTaskAssignee(task.assigneeId || '');
    setTaskEstimatedTime(task.estimatedTime || 30);
    setEditDialogOpen(true);
  };
  
  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };
  
  // Get assignee name
  const getAssigneeName = (assigneeId: string) => {
    const assignee = availableAssignees.find(a => a._id === assigneeId);
    return assignee ? assignee.name : 'Unassigned';
  };
  
  // Render loading state
  if (loading && !proposal) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">
          Tasks
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            size={isMobile ? "small" : "medium"}
          >
            Create Task
          </Button>
        </Box>
      </Box>
      
      {/* Tasks List */}
      {tasks.length === 0 ? (
        <Alert severity="info">
          No tasks created for this proposal. Click "Create Task" to create a new task.
        </Alert>
      ) : (
        <List>
          {tasks.map((task) => (
            <Paper key={task._id} sx={{ mb: 2 }}>
              <ListItem
                secondaryAction={
                  <Box>
                    <Tooltip title={task.status === 'done' ? 'Mark as Incomplete' : 'Mark as Complete'}>
                      <IconButton
                        edge="end"
                        onClick={() => handleCompleteTask(task._id, task.status !== 'done')}
                      >
                        {task.status === 'done' ? (
                          <CompleteIcon color="success" />
                        ) : (
                          <IncompleteIcon />
                        )}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Task">
                      <IconButton
                        edge="end"
                        onClick={() => handleEditTask(task)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Task">
                      <IconButton
                        edge="end"
                        color="error"
                        onClick={() => handleDeleteTask(task._id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                }
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: task.status === 'done' ? theme.palette.success.main : theme.palette.primary.main }}>
                    <TaskIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography
                      variant="subtitle1"
                      sx={{
                        textDecoration: task.status === 'done' ? 'line-through' : 'none',
                      }}
                    >
                      {task.title}
                    </Typography>
                  }
                  secondary={
                    <Box>
                      {task.description && (
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {task.description}
                        </Typography>
                      )}
                      <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
                        <Chip
                          icon={<PriorityIcon />}
                          label={`${task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority`}
                          size="small"
                          sx={{
                            bgcolor: `${getPriorityColor(task.priority)}20`,
                            color: getPriorityColor(task.priority),
                          }}
                        />
                        <Chip
                          icon={<AssigneeIcon />}
                          label={getAssigneeName(task.assigneeId)}
                          size="small"
                        />
                        {task.dueDate && (
                          <Chip
                            icon={<DueDateIcon />}
                            label={`Due: ${format(new Date(task.dueDate), 'PPP')}`}
                            size="small"
                            color={new Date(task.dueDate) < new Date() && task.status !== 'done' ? 'error' : 'default'}
                          />
                        )}
                        {task.estimatedTime && (
                          <Chip
                            icon={<TimeIcon />}
                            label={`${task.estimatedTime} min`}
                            size="small"
                          />
                        )}
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
              {task.progress !== undefined && (
                <Box px={2} pb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                    <Typography variant="caption" color="text.secondary">
                      Progress
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {task.progress}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={task.progress}
                    color={task.status === 'done' ? 'success' : 'primary'}
                  />
                </Box>
              )}
            </Paper>
          ))}
        </List>
      )}
      
      {/* Create Task Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create Task</DialogTitle>
        <DialogContent>
          {createError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {createError}
            </Alert>
          )}
          
          <TextField
            label="Task Title"
            value={taskTitle}
            onChange={(e) => setTaskTitle(e.target.value)}
            fullWidth
            margin="normal"
            required
          />
          
          <TextField
            label="Description"
            value={taskDescription}
            onChange={(e) => setTaskDescription(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={2}
          />
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={taskPriority}
                  onChange={(e) => setTaskPriority(e.target.value)}
                  label="Priority"
                >
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Due Date"
                type="date"
                value={taskDueDate}
                onChange={(e) => setTaskDueDate(e.target.value)}
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Assignee</InputLabel>
                <Select
                  value={taskAssignee}
                  onChange={(e) => setTaskAssignee(e.target.value)}
                  label="Assignee"
                >
                  <MenuItem value="">Unassigned</MenuItem>
                  {availableAssignees.map((assignee) => (
                    <MenuItem key={assignee._id} value={assignee._id}>
                      {assignee.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Estimated Time (minutes)</InputLabel>
                <Select
                  value={taskEstimatedTime}
                  onChange={(e) => setTaskEstimatedTime(Number(e.target.value))}
                  label="Estimated Time (minutes)"
                >
                  <MenuItem value={15}>15 minutes</MenuItem>
                  <MenuItem value={30}>30 minutes</MenuItem>
                  <MenuItem value={45}>45 minutes</MenuItem>
                  <MenuItem value={60}>1 hour</MenuItem>
                  <MenuItem value={90}>1.5 hours</MenuItem>
                  <MenuItem value={120}>2 hours</MenuItem>
                  <MenuItem value={180}>3 hours</MenuItem>
                  <MenuItem value={240}>4 hours</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleCreateTask}
            disabled={createLoading || !taskTitle}
            startIcon={createLoading ? <CircularProgress size={20} /> : null}
          >
            {createLoading ? 'Creating...' : 'Create Task'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Edit Task Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Edit Task</DialogTitle>
        <DialogContent>
          {createError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {createError}
            </Alert>
          )}
          
          <TextField
            label="Task Title"
            value={taskTitle}
            onChange={(e) => setTaskTitle(e.target.value)}
            fullWidth
            margin="normal"
            required
          />
          
          <TextField
            label="Description"
            value={taskDescription}
            onChange={(e) => setTaskDescription(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={2}
          />
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={taskPriority}
                  onChange={(e) => setTaskPriority(e.target.value)}
                  label="Priority"
                >
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Due Date"
                type="date"
                value={taskDueDate}
                onChange={(e) => setTaskDueDate(e.target.value)}
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Assignee</InputLabel>
                <Select
                  value={taskAssignee}
                  onChange={(e) => setTaskAssignee(e.target.value)}
                  label="Assignee"
                >
                  <MenuItem value="">Unassigned</MenuItem>
                  {availableAssignees.map((assignee) => (
                    <MenuItem key={assignee._id} value={assignee._id}>
                      {assignee.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Estimated Time (minutes)</InputLabel>
                <Select
                  value={taskEstimatedTime}
                  onChange={(e) => setTaskEstimatedTime(Number(e.target.value))}
                  label="Estimated Time (minutes)"
                >
                  <MenuItem value={15}>15 minutes</MenuItem>
                  <MenuItem value={30}>30 minutes</MenuItem>
                  <MenuItem value={45}>45 minutes</MenuItem>
                  <MenuItem value={60}>1 hour</MenuItem>
                  <MenuItem value={90}>1.5 hours</MenuItem>
                  <MenuItem value={120}>2 hours</MenuItem>
                  <MenuItem value={180}>3 hours</MenuItem>
                  <MenuItem value={240}>4 hours</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleUpdateTask}
            disabled={createLoading || !taskTitle}
            startIcon={createLoading ? <CircularProgress size={20} /> : null}
          >
            {createLoading ? 'Updating...' : 'Update Task'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProposalTaskIntegration;
