import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  useTheme,
  useMediaQuery,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Receipt as InvoiceIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  OpenInNew as OpenInNewIcon,
  AttachMoney as MoneyIcon,
  Send as SendIcon,
  CheckCircle as PaidIcon,
  Cancel as UnpaidIcon,
  Link as LinkIcon,
  ContentCopy as CopyIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { InvoiceService } from '../../../services/invoice-service';
import { formatCurrency } from '../../../utils/formatters';

interface ProposalInvoiceIntegrationProps {
  proposalId: string;
  tenantId: string;
  onViewInvoice?: (invoiceId: string) => void;
}

/**
 * ProposalInvoiceIntegration Component
 * 
 * This component displays invoices associated with a proposal and allows
 * creating new invoices from the proposal data.
 */
const ProposalInvoiceIntegration: React.FC<ProposalInvoiceIntegrationProps> = ({
  proposalId,
  tenantId,
  onViewInvoice,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<any>(null);
  const [invoices, setInvoices] = useState<any[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [createError, setCreateError] = useState<string | null>(null);
  const [invoiceTitle, setInvoiceTitle] = useState('');
  const [invoiceDescription, setInvoiceDescription] = useState('');
  const [dueDate, setDueDate] = useState<string>('');
  const [includeAllItems, setIncludeAllItems] = useState(true);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  
  // Fetch proposal and invoices
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposal
        const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
        setProposal(proposalData);
        
        // Set default invoice title and description
        setInvoiceTitle(`Invoice for ${proposalData.title}`);
        setInvoiceDescription(`Invoice for proposal: ${proposalData.title}`);
        
        // Set default due date (30 days from now)
        const date = new Date();
        date.setDate(date.getDate() + 30);
        setDueDate(date.toISOString().split('T')[0]);
        
        // Fetch invoices
        const invoicesData = await InvoiceService.getInvoicesByProposal(proposalId, tenantId);
        setInvoices(invoicesData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [proposalId, tenantId]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch invoices
        const invoicesData = await InvoiceService.getInvoicesByProposal(proposalId, tenantId);
        setInvoices(invoicesData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Handle create invoice
  const handleCreateInvoice = async () => {
    try {
      setCreateLoading(true);
      setCreateError(null);
      
      // Prepare invoice data
      const invoiceData = {
        title: invoiceTitle,
        description: invoiceDescription,
        proposalId,
        dueDate: new Date(dueDate),
        items: includeAllItems
          ? proposal.pricing?.items
          : proposal.pricing?.items.filter((item: any) => selectedItems.includes(item.id)),
        companyId: proposal.companyId,
        contactIds: proposal.contactIds,
      };
      
      // Create invoice
      await InvoiceService.createInvoice(invoiceData, tenantId);
      
      // Refresh invoices
      handleRefresh();
      
      // Close dialog
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Error creating invoice:', err);
      setCreateError('Failed to create invoice. Please try again.');
    } finally {
      setCreateLoading(false);
    }
  };
  
  // Handle item selection
  const handleItemSelection = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };
  
  // Render loading state
  if (loading && !proposal) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">
          Invoices
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            size={isMobile ? "small" : "medium"}
            disabled={!proposal?.pricing?.items || proposal.pricing.items.length === 0}
          >
            Create Invoice
          </Button>
        </Box>
      </Box>
      
      {/* Proposal Summary */}
      {proposal && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              Proposal Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Title
                </Typography>
                <Typography variant="body1" noWrap>
                  {proposal.title}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Status
                </Typography>
                <Chip 
                  label={proposal.status.toUpperCase()} 
                  color={
                    proposal.status === 'accepted' ? 'success' :
                    proposal.status === 'rejected' ? 'error' :
                    proposal.status === 'viewed' ? 'info' :
                    proposal.status === 'sent' ? 'primary' :
                    'default'
                  }
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Total Amount
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {proposal.pricing?.total ? formatCurrency(proposal.pricing.total) : 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body1">
                  {format(new Date(proposal.createdAt), 'PPP')}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {/* Invoices List */}
      {invoices.length === 0 ? (
        <Alert severity="info">
          No invoices found for this proposal. {proposal?.pricing?.items && proposal.pricing.items.length > 0 ? 'Click "Create Invoice" to create a new invoice.' : 'This proposal does not have any pricing items to invoice.'}
        </Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Invoice</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoices.map((invoice) => (
                <TableRow key={invoice._id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <InvoiceIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                      <Typography variant="body1">
                        {invoice.title}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={invoice.status.toUpperCase()} 
                      color={
                        invoice.status === 'paid' ? 'success' :
                        invoice.status === 'overdue' ? 'error' :
                        invoice.status === 'sent' ? 'primary' :
                        'default'
                      }
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {formatCurrency(invoice.total)}
                  </TableCell>
                  <TableCell>
                    {format(new Date(invoice.dueDate), 'PPP')}
                  </TableCell>
                  <TableCell>
                    {format(new Date(invoice.createdAt), 'PPP')}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Invoice">
                      <IconButton
                        size="small"
                        onClick={() => onViewInvoice && onViewInvoice(invoice._id)}
                      >
                        <OpenInNewIcon />
                      </IconButton>
                    </Tooltip>
                    {invoice.status === 'draft' && (
                      <Tooltip title="Send Invoice">
                        <IconButton
                          size="small"
                          onClick={() => {
                            // Handle send invoice
                          }}
                        >
                          <SendIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    {invoice.publicUrl && (
                      <Tooltip title="Copy Public Link">
                        <IconButton
                          size="small"
                          onClick={() => {
                            navigator.clipboard.writeText(invoice.publicUrl);
                          }}
                        >
                          <LinkIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Create Invoice Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create Invoice from Proposal</DialogTitle>
        <DialogContent>
          {createError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {createError}
            </Alert>
          )}
          
          <TextField
            label="Invoice Title"
            value={invoiceTitle}
            onChange={(e) => setInvoiceTitle(e.target.value)}
            fullWidth
            margin="normal"
            required
          />
          
          <TextField
            label="Invoice Description"
            value={invoiceDescription}
            onChange={(e) => setInvoiceDescription(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={2}
          />
          
          <TextField
            label="Due Date"
            type="date"
            value={dueDate}
            onChange={(e) => setDueDate(e.target.value)}
            fullWidth
            margin="normal"
            required
            InputLabelProps={{
              shrink: true,
            }}
          />
          
          <Box mt={3}>
            <Typography variant="subtitle1" gutterBottom>
              Invoice Items
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={includeAllItems}
                  onChange={(e) => setIncludeAllItems(e.target.checked)}
                  color="primary"
                />
              }
              label="Include all items from proposal"
            />
            
            {!includeAllItems && proposal?.pricing?.items && (
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox"></TableCell>
                      <TableCell>Item</TableCell>
                      <TableCell>Quantity</TableCell>
                      <TableCell>Unit Price</TableCell>
                      <TableCell>Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {proposal.pricing.items.map((item: any) => (
                      <TableRow key={item.id}>
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={selectedItems.includes(item.id)}
                            onChange={() => handleItemSelection(item.id)}
                          />
                        </TableCell>
                        <TableCell>{item.name}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                        <TableCell>{formatCurrency(item.total)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            
            <Box mt={2}>
              <Typography variant="subtitle2" gutterBottom>
                Total Amount:
              </Typography>
              <Typography variant="h6" color="primary">
                {formatCurrency(
                  includeAllItems
                    ? proposal?.pricing?.total || 0
                    : proposal?.pricing?.items
                        .filter((item: any) => selectedItems.includes(item.id))
                        .reduce((sum: number, item: any) => sum + item.total, 0) || 0
                )}
              </Typography>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleCreateInvoice}
            disabled={createLoading || !invoiceTitle || !dueDate || (!includeAllItems && selectedItems.length === 0)}
            startIcon={createLoading ? <CircularProgress size={20} /> : null}
          >
            {createLoading ? 'Creating...' : 'Create Invoice'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProposalInvoiceIntegration;
