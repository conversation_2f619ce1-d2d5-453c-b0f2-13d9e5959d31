import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  useTheme,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Event as CalendarIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  OpenInNew as OpenInNewIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Videocam as VideoIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  AccessTime as TimeIcon,
  Notifications as NotificationIcon,
} from '@mui/icons-material';
import { format, addMinutes, addHours } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { CalendarService } from '../../../services/calendar-service';
import { useAuth } from '../../../hooks/useAuth';

interface ProposalCalendarIntegrationProps {
  proposalId: string;
  tenantId: string;
  onViewEvent?: (eventId: string) => void;
}

/**
 * ProposalCalendarIntegration Component
 * 
 * This component displays calendar events associated with a proposal and allows
 * scheduling follow-up meetings.
 */
const ProposalCalendarIntegration: React.FC<ProposalCalendarIntegrationProps> = ({
  proposalId,
  tenantId,
  onViewEvent,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<any>(null);
  const [events, setEvents] = useState<any[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [createError, setCreateError] = useState<string | null>(null);
  const [eventTitle, setEventTitle] = useState('');
  const [eventDescription, setEventDescription] = useState('');
  const [eventType, setEventType] = useState('meeting');
  const [eventLocation, setEventLocation] = useState('');
  const [eventStartDate, setEventStartDate] = useState('');
  const [eventStartTime, setEventStartTime] = useState('');
  const [eventDuration, setEventDuration] = useState(30);
  const [eventAttendees, setEventAttendees] = useState<string[]>([]);
  const [availableAttendees, setAvailableAttendees] = useState<any[]>([]);
  const [sendInvites, setSendInvites] = useState(true);
  const [addVideoConference, setAddVideoConference] = useState(false);
  const [addReminder, setAddReminder] = useState(true);
  const [reminderTime, setReminderTime] = useState(15);
  
  // Fetch proposal and events
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposal
        const proposalData = await ProposalService.getProposalById(proposalId, tenantId);
        setProposal(proposalData);
        
        // Set default event title and description
        setEventTitle(`Follow-up for ${proposalData.title}`);
        setEventDescription(`Follow-up meeting to discuss proposal: ${proposalData.title}`);
        
        // Set default start date and time (next business day at 10 AM)
        const now = new Date();
        const nextDay = new Date(now);
        nextDay.setDate(now.getDate() + 1);
        // Skip to Monday if it's Friday
        if (nextDay.getDay() === 6) nextDay.setDate(nextDay.getDate() + 2);
        if (nextDay.getDay() === 0) nextDay.setDate(nextDay.getDate() + 1);
        nextDay.setHours(10, 0, 0, 0);
        
        setEventStartDate(nextDay.toISOString().split('T')[0]);
        setEventStartTime('10:00');
        
        // Fetch events
        const eventsData = await CalendarService.getEventsByProposal(proposalId, tenantId);
        setEvents(eventsData);
        
        // Fetch available attendees
        const contacts = proposalData.contacts || [];
        const teamMembers = await CalendarService.getTeamMembers(tenantId);
        
        setAvailableAttendees([
          ...contacts.map((contact: any) => ({
            id: contact._id,
            name: contact.name,
            email: contact.email,
            type: 'contact',
          })),
          ...teamMembers.map((member: any) => ({
            id: member._id,
            name: member.name,
            email: member.email,
            type: 'team',
          })),
        ]);
        
        // Set default attendees (proposal contacts and current user)
        setEventAttendees([
          ...contacts.map((contact: any) => contact._id),
          user?.id || '',
        ].filter(Boolean));
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [proposalId, tenantId, user?.id]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch events
        const eventsData = await CalendarService.getEventsByProposal(proposalId, tenantId);
        setEvents(eventsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Handle create event
  const handleCreateEvent = async () => {
    try {
      setCreateLoading(true);
      setCreateError(null);
      
      // Calculate end time
      const startDateTime = new Date(`${eventStartDate}T${eventStartTime}`);
      const endDateTime = addMinutes(startDateTime, eventDuration);
      
      // Calculate reminder time
      const reminderDateTime = addMinutes(startDateTime, -reminderTime);
      
      // Prepare event data
      const eventData = {
        title: eventTitle,
        description: eventDescription,
        proposalId,
        start: startDateTime.toISOString(),
        end: endDateTime.toISOString(),
        location: eventLocation,
        type: eventType,
        attendees: eventAttendees.map(id => {
          const attendee = availableAttendees.find(a => a.id === id);
          return {
            id,
            name: attendee?.name || '',
            email: attendee?.email || '',
            type: attendee?.type || 'contact',
          };
        }),
        sendInvites,
        addVideoConference,
        reminder: addReminder ? {
          time: reminderDateTime.toISOString(),
          method: 'email',
        } : undefined,
      };
      
      // Create event
      await CalendarService.createEvent(eventData, tenantId);
      
      // Refresh events
      handleRefresh();
      
      // Close dialog
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Error creating event:', err);
      setCreateError('Failed to create event. Please try again.');
    } finally {
      setCreateLoading(false);
    }
  };
  
  // Handle delete event
  const handleDeleteEvent = async (eventId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await CalendarService.deleteEvent(eventId, tenantId);
      
      // Refresh events
      handleRefresh();
    } catch (err) {
      console.error('Error deleting event:', err);
      setError('Failed to delete event. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle attendee selection
  const handleAttendeeSelection = (attendeeId: string) => {
    if (eventAttendees.includes(attendeeId)) {
      setEventAttendees(eventAttendees.filter(id => id !== attendeeId));
    } else {
      setEventAttendees([...eventAttendees, attendeeId]);
    }
  };
  
  // Render loading state
  if (loading && !proposal) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">
          Calendar Events
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            size={isMobile ? "small" : "medium"}
          >
            Schedule Follow-up
          </Button>
        </Box>
      </Box>
      
      {/* Events List */}
      {events.length === 0 ? (
        <Alert severity="info">
          No events scheduled for this proposal. Click "Schedule Follow-up" to create a new event.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {events.map((event) => (
            <Grid item xs={12} sm={6} md={4} key={event._id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                    <Box display="flex" alignItems="center">
                      {event.type === 'meeting' && <PeopleIcon color="primary" sx={{ mr: 1 }} />}
                      {event.type === 'call' && <PhoneIcon color="primary" sx={{ mr: 1 }} />}
                      {event.type === 'task' && <CalendarIcon color="primary" sx={{ mr: 1 }} />}
                      <Typography variant="h6" noWrap sx={{ maxWidth: '180px' }}>
                        {event.title}
                      </Typography>
                    </Box>
                    <IconButton 
                      size="small" 
                      color="error"
                      onClick={() => handleDeleteEvent(event._id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {event.description}
                  </Typography>
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Box display="flex" alignItems="center" mb={1}>
                    <TimeIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                    <Typography variant="body2">
                      {format(new Date(event.start), 'PPP')}
                    </Typography>
                  </Box>
                  
                  <Box display="flex" alignItems="center" mb={1}>
                    <TimeIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                    <Typography variant="body2">
                      {format(new Date(event.start), 'p')} - {format(new Date(event.end), 'p')}
                    </Typography>
                  </Box>
                  
                  {event.location && (
                    <Box display="flex" alignItems="center" mb={1}>
                      {event.videoConferenceUrl ? (
                        <VideoIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                      ) : (
                        <LocationIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                      )}
                      <Typography variant="body2" noWrap sx={{ maxWidth: '200px' }}>
                        {event.location}
                      </Typography>
                    </Box>
                  )}
                  
                  <Box display="flex" alignItems="center">
                    <PeopleIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                    <Typography variant="body2">
                      {event.attendees?.length || 0} Attendees
                    </Typography>
                  </Box>
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    startIcon={<OpenInNewIcon />}
                    onClick={() => onViewEvent && onViewEvent(event._id)}
                  >
                    View
                  </Button>
                  <Button 
                    size="small" 
                    startIcon={<EditIcon />}
                  >
                    Edit
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
      
      {/* Create Event Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Schedule Follow-up</DialogTitle>
        <DialogContent>
          {createError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {createError}
            </Alert>
          )}
          
          <TextField
            label="Event Title"
            value={eventTitle}
            onChange={(e) => setEventTitle(e.target.value)}
            fullWidth
            margin="normal"
            required
          />
          
          <TextField
            label="Description"
            value={eventDescription}
            onChange={(e) => setEventDescription(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={2}
          />
          
          <FormControl fullWidth margin="normal">
            <InputLabel>Event Type</InputLabel>
            <Select
              value={eventType}
              onChange={(e) => setEventType(e.target.value)}
              label="Event Type"
            >
              <MenuItem value="meeting">Meeting</MenuItem>
              <MenuItem value="call">Call</MenuItem>
              <MenuItem value="task">Task</MenuItem>
            </Select>
          </FormControl>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Date"
                type="date"
                value={eventStartDate}
                onChange={(e) => setEventStartDate(e.target.value)}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Time"
                type="time"
                value={eventStartTime}
                onChange={(e) => setEventStartTime(e.target.value)}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Duration</InputLabel>
                <Select
                  value={eventDuration}
                  onChange={(e) => setEventDuration(Number(e.target.value))}
                  label="Duration"
                >
                  <MenuItem value={15}>15 minutes</MenuItem>
                  <MenuItem value={30}>30 minutes</MenuItem>
                  <MenuItem value={45}>45 minutes</MenuItem>
                  <MenuItem value={60}>1 hour</MenuItem>
                  <MenuItem value={90}>1.5 hours</MenuItem>
                  <MenuItem value={120}>2 hours</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Location"
                value={eventLocation}
                onChange={(e) => setEventLocation(e.target.value)}
                fullWidth
                placeholder="Office, Zoom, etc."
              />
            </Grid>
          </Grid>
          
          <Box mt={3}>
            <Typography variant="subtitle1" gutterBottom>
              Attendees
            </Typography>
            
            <Paper variant="outlined" sx={{ p: 2, maxHeight: '200px', overflow: 'auto' }}>
              <List dense>
                {availableAttendees.map((attendee) => (
                  <ListItem key={attendee.id}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={eventAttendees.includes(attendee.id)}
                          onChange={() => handleAttendeeSelection(attendee.id)}
                        />
                      }
                      label={
                        <Box>
                          <Typography variant="body2">
                            {attendee.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {attendee.email} ({attendee.type === 'contact' ? 'Contact' : 'Team Member'})
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Box>
          
          <Box mt={3}>
            <Typography variant="subtitle1" gutterBottom>
              Options
            </Typography>
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={sendInvites}
                  onChange={(e) => setSendInvites(e.target.checked)}
                />
              }
              label="Send calendar invites to attendees"
            />
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={addVideoConference}
                  onChange={(e) => setAddVideoConference(e.target.checked)}
                />
              }
              label="Add video conferencing link"
            />
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={addReminder}
                  onChange={(e) => setAddReminder(e.target.checked)}
                />
              }
              label="Add reminder"
            />
            
            {addReminder && (
              <FormControl fullWidth margin="normal">
                <InputLabel>Reminder Time</InputLabel>
                <Select
                  value={reminderTime}
                  onChange={(e) => setReminderTime(Number(e.target.value))}
                  label="Reminder Time"
                >
                  <MenuItem value={5}>5 minutes before</MenuItem>
                  <MenuItem value={10}>10 minutes before</MenuItem>
                  <MenuItem value={15}>15 minutes before</MenuItem>
                  <MenuItem value={30}>30 minutes before</MenuItem>
                  <MenuItem value={60}>1 hour before</MenuItem>
                  <MenuItem value={120}>2 hours before</MenuItem>
                  <MenuItem value={1440}>1 day before</MenuItem>
                </Select>
              </FormControl>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleCreateEvent}
            disabled={createLoading || !eventTitle || !eventStartDate || !eventStartTime || eventAttendees.length === 0}
            startIcon={createLoading ? <CircularProgress size={20} /> : null}
          >
            {createLoading ? 'Scheduling...' : 'Schedule Event'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProposalCalendarIntegration;
