import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Add as AddIcon,
  Description as DescriptionIcon,
  Link as LinkIcon,
  OpenInNew as OpenInNewIcon,
  Refresh as RefreshIcon,
  AttachMoney as MoneyIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Email as EmailIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { OpportunityService } from '../../../services/opportunity-service';
import { formatCurrency } from '../../../utils/formatters';
import CreateProposalFromOpportunityDialog from './CreateProposalFromOpportunityDialog';

interface OpportunityProposalIntegrationProps {
  opportunityId: string;
  tenantId: string;
  onCreateProposal?: (proposalId: string) => void;
  onViewProposal?: (proposalId: string) => void;
}

/**
 * OpportunityProposalIntegration Component
 * 
 * This component displays proposals associated with an opportunity and allows
 * creating new proposals from the opportunity data.
 */
const OpportunityProposalIntegration: React.FC<OpportunityProposalIntegrationProps> = ({
  opportunityId,
  tenantId,
  onCreateProposal,
  onViewProposal,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposals, setProposals] = useState<any[]>([]);
  const [opportunity, setOpportunity] = useState<any>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  
  // Fetch proposals and opportunity data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch opportunity
        const opportunityData = await OpportunityService.getOpportunityById(opportunityId, tenantId);
        setOpportunity(opportunityData);
        
        // Fetch proposals
        const proposalsData = await ProposalService.getProposalsByOpportunity(opportunityId, tenantId);
        setProposals(proposalsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [opportunityId, tenantId]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch opportunity
        const opportunityData = await OpportunityService.getOpportunityById(opportunityId, tenantId);
        setOpportunity(opportunityData);
        
        // Fetch proposals
        const proposalsData = await ProposalService.getProposalsByOpportunity(opportunityId, tenantId);
        setProposals(proposalsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Handle create proposal
  const handleCreateProposal = async (proposalData: any) => {
    try {
      setLoading(true);
      setError(null);
      
      // Create proposal
      const proposal = await ProposalService.createProposal({
        ...proposalData,
        opportunityId,
        companyId: opportunity.companyId,
        contactIds: opportunity.contactIds,
      }, tenantId);
      
      // Refresh proposals
      handleRefresh();
      
      // Close dialog
      setCreateDialogOpen(false);
      
      // Callback
      if (onCreateProposal) {
        onCreateProposal(proposal._id);
      }
    } catch (err) {
      console.error('Error creating proposal:', err);
      setError('Failed to create proposal. Please try again.');
      setLoading(false);
    }
  };
  
  // Render loading state
  if (loading && !opportunity) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">
          Proposals
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            size={isMobile ? "small" : "medium"}
          >
            Create Proposal
          </Button>
        </Box>
      </Box>
      
      {/* Opportunity Summary */}
      {opportunity && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              Opportunity Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box display="flex" alignItems="center">
                  <BusinessIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="body2" noWrap>
                    {opportunity.company?.name || 'No Company'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box display="flex" alignItems="center">
                  <MoneyIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    {formatCurrency(opportunity.amount || 0)}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box display="flex" alignItems="center">
                  <PersonIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    {opportunity.contacts?.length || 0} Contacts
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Chip 
                  label={opportunity.stage} 
                  color={
                    opportunity.stage === 'won' ? 'success' :
                    opportunity.stage === 'lost' ? 'error' :
                    'primary'
                  }
                  size="small"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {/* Proposals List */}
      {proposals.length === 0 ? (
        <Alert severity="info">
          No proposals found for this opportunity. Create a new proposal to get started.
        </Alert>
      ) : (
        <Paper variant="outlined">
          <List disablePadding>
            {proposals.map((proposal, index) => (
              <React.Fragment key={proposal._id}>
                <ListItem
                  button
                  onClick={() => onViewProposal && onViewProposal(proposal._id)}
                >
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center">
                        <DescriptionIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                        <Typography variant="subtitle1">
                          {proposal.title}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Box mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          Created: {format(new Date(proposal.createdAt), 'PPP')}
                        </Typography>
                        <Box display="flex" alignItems="center" mt={0.5} flexWrap="wrap" gap={0.5}>
                          <Chip 
                            label={proposal.status.toUpperCase()} 
                            color={
                              proposal.status === 'accepted' ? 'success' :
                              proposal.status === 'rejected' ? 'error' :
                              proposal.status === 'viewed' ? 'info' :
                              proposal.status === 'sent' ? 'primary' :
                              'default'
                            }
                            size="small"
                          />
                          {proposal.aiGenerated && (
                            <Chip label="AI Generated" size="small" />
                          )}
                          {proposal.sections?.length > 0 && (
                            <Chip label={`${proposal.sections.length} Sections`} size="small" />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="View Proposal">
                      <IconButton
                        edge="end"
                        onClick={(e) => {
                          e.stopPropagation();
                          onViewProposal && onViewProposal(proposal._id);
                        }}
                      >
                        <OpenInNewIcon />
                      </IconButton>
                    </Tooltip>
                    {proposal.publicAccessEnabled && (
                      <Tooltip title="Copy Public Link">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigator.clipboard.writeText(proposal.publicUrl);
                          }}
                        >
                          <LinkIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    {proposal.status === 'draft' && (
                      <Tooltip title="Send Proposal">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle send proposal
                          }}
                        >
                          <EmailIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </ListItemSecondaryAction>
                </ListItem>
                {index < proposals.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}
      
      {/* Create Dialog */}
      {opportunity && (
        <CreateProposalFromOpportunityDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          onSubmit={handleCreateProposal}
          opportunity={opportunity}
        />
      )}
    </Box>
  );
};

export default OpportunityProposalIntegration;
