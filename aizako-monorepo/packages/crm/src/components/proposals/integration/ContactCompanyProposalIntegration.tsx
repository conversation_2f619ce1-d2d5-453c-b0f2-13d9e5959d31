import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Description as DescriptionIcon,
  OpenInNew as OpenInNewIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Link as LinkIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { CompanyService } from '../../../services/company-service';
import { ContactService } from '../../../services/contact-service';
import CreateProposalDialog from '../CreateProposalDialog';

interface ContactCompanyProposalIntegrationProps {
  entityId: string;
  entityType: 'contact' | 'company';
  tenantId: string;
  onCreateProposal?: (proposalId: string) => void;
  onViewProposal?: (proposalId: string) => void;
}

/**
 * ContactCompanyProposalIntegration Component
 * 
 * This component displays proposals associated with a contact or company and allows
 * creating new proposals.
 */
const ContactCompanyProposalIntegration: React.FC<ContactCompanyProposalIntegrationProps> = ({
  entityId,
  entityType,
  tenantId,
  onCreateProposal,
  onViewProposal,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposals, setProposals] = useState<any[]>([]);
  const [entity, setEntity] = useState<any>(null);
  const [relatedEntities, setRelatedEntities] = useState<any[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected'>('all');
  
  // Fetch proposals and entity data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch entity
        if (entityType === 'contact') {
          const contactData = await ContactService.getContactById(entityId, tenantId);
          setEntity(contactData);
          
          // Fetch related companies
          if (contactData.companyId) {
            const companyData = await CompanyService.getCompanyById(contactData.companyId, tenantId);
            setRelatedEntities([companyData]);
          }
          
          // Fetch proposals
          const proposalsData = await ProposalService.getProposalsByContact(entityId, tenantId);
          setProposals(proposalsData);
        } else {
          const companyData = await CompanyService.getCompanyById(entityId, tenantId);
          setEntity(companyData);
          
          // Fetch related contacts
          const contactsData = await ContactService.getContactsByCompany(entityId, tenantId);
          setRelatedEntities(contactsData);
          
          // Fetch proposals
          const proposalsData = await ProposalService.getProposalsByCompany(entityId, tenantId);
          setProposals(proposalsData);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [entityId, entityType, tenantId]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch proposals
        if (entityType === 'contact') {
          const proposalsData = await ProposalService.getProposalsByContact(entityId, tenantId);
          setProposals(proposalsData);
        } else {
          const proposalsData = await ProposalService.getProposalsByCompany(entityId, tenantId);
          setProposals(proposalsData);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  };
  
  // Handle create proposal
  const handleCreateProposal = async (proposalData: any) => {
    try {
      setLoading(true);
      setError(null);
      
      // Add entity data to proposal
      if (entityType === 'contact') {
        proposalData.contactIds = [entityId];
        if (entity.companyId) {
          proposalData.companyId = entity.companyId;
        }
      } else {
        proposalData.companyId = entityId;
        if (proposalData.includeAllContacts) {
          proposalData.contactIds = relatedEntities.map(contact => contact._id);
          delete proposalData.includeAllContacts;
        }
      }
      
      // Create proposal
      const proposal = await ProposalService.createProposal(proposalData, tenantId);
      
      // Refresh proposals
      handleRefresh();
      
      // Close dialog
      setCreateDialogOpen(false);
      
      // Callback
      if (onCreateProposal) {
        onCreateProposal(proposal._id);
      }
    } catch (err) {
      console.error('Error creating proposal:', err);
      setError('Failed to create proposal. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: 'all' | 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected') => {
    setActiveTab(newValue);
  };
  
  // Filter proposals by status
  const filteredProposals = activeTab === 'all'
    ? proposals
    : proposals.filter(proposal => proposal.status === activeTab);
  
  // Render loading state
  if (loading && !entity) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">
          Proposals
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            size={isMobile ? "small" : "medium"}
          >
            Create Proposal
          </Button>
        </Box>
      </Box>
      
      {/* Entity Summary */}
      {entity && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              {entityType === 'contact' ? 'Contact' : 'Company'} Summary
            </Typography>
            <Grid container spacing={2}>
              {entityType === 'contact' ? (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box display="flex" alignItems="center">
                      <PersonIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="body2" noWrap>
                        {entity.name}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box display="flex" alignItems="center">
                      <EmailIcon color="info" sx={{ mr: 1 }} />
                      <Typography variant="body2" noWrap>
                        {entity.email || 'No Email'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box display="flex" alignItems="center">
                      <BusinessIcon color="secondary" sx={{ mr: 1 }} />
                      <Typography variant="body2" noWrap>
                        {relatedEntities[0]?.name || 'No Company'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Chip 
                      label={entity.status || 'Active'} 
                      color="primary"
                      size="small"
                    />
                  </Grid>
                </>
              ) : (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box display="flex" alignItems="center">
                      <BusinessIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="body2" noWrap>
                        {entity.name}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box display="flex" alignItems="center">
                      <PersonIcon color="info" sx={{ mr: 1 }} />
                      <Typography variant="body2">
                        {relatedEntities.length} Contacts
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Industry
                    </Typography>
                    <Typography variant="body2">
                      {entity.industry || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Size
                    </Typography>
                    <Typography variant="body2">
                      {entity.size || 'N/A'}
                    </Typography>
                  </Grid>
                </>
              )}
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant={isMobile ? "scrollable" : "fullWidth"}
          scrollButtons={isMobile ? "auto" : undefined}
        >
          <Tab label="All" value="all" />
          <Tab label="Draft" value="draft" />
          <Tab label="Sent" value="sent" />
          <Tab label="Viewed" value="viewed" />
          <Tab label="Accepted" value="accepted" />
          <Tab label="Rejected" value="rejected" />
        </Tabs>
      </Paper>
      
      {/* Proposals List */}
      {filteredProposals.length === 0 ? (
        <Alert severity="info">
          No {activeTab !== 'all' ? activeTab : ''} proposals found for this {entityType}.
        </Alert>
      ) : (
        <Paper variant="outlined">
          <List disablePadding>
            {filteredProposals.map((proposal, index) => (
              <React.Fragment key={proposal._id}>
                <ListItem
                  button
                  onClick={() => onViewProposal && onViewProposal(proposal._id)}
                >
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center">
                        <DescriptionIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                        <Typography variant="subtitle1">
                          {proposal.title}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Box mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          Created: {format(new Date(proposal.createdAt), 'PPP')}
                        </Typography>
                        <Box display="flex" alignItems="center" mt={0.5} flexWrap="wrap" gap={0.5}>
                          <Chip 
                            label={proposal.status.toUpperCase()} 
                            color={
                              proposal.status === 'accepted' ? 'success' :
                              proposal.status === 'rejected' ? 'error' :
                              proposal.status === 'viewed' ? 'info' :
                              proposal.status === 'sent' ? 'primary' :
                              'default'
                            }
                            size="small"
                          />
                          {proposal.aiGenerated && (
                            <Chip label="AI Generated" size="small" />
                          )}
                          {proposal.sections?.length > 0 && (
                            <Chip label={`${proposal.sections.length} Sections`} size="small" />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="View Proposal">
                      <IconButton
                        edge="end"
                        onClick={(e) => {
                          e.stopPropagation();
                          onViewProposal && onViewProposal(proposal._id);
                        }}
                      >
                        <OpenInNewIcon />
                      </IconButton>
                    </Tooltip>
                    {proposal.publicAccessEnabled && (
                      <Tooltip title="Copy Public Link">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigator.clipboard.writeText(proposal.publicUrl);
                          }}
                        >
                          <LinkIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    {proposal.status === 'draft' && (
                      <Tooltip title="Send Proposal">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle send proposal
                          }}
                        >
                          <EmailIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </ListItemSecondaryAction>
                </ListItem>
                {index < filteredProposals.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}
      
      {/* Create Dialog */}
      {entity && (
        <CreateProposalDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          onSubmit={handleCreateProposal}
          initialData={{
            title: `Proposal for ${entity.name}`,
            description: `Proposal for ${entity.name}`,
            ...(entityType === 'company' && { 
              includeAllContacts: true,
              contacts: relatedEntities 
            }),
          }}
        />
      )}
    </Box>
  );
};

export default ContactCompanyProposalIntegration;
