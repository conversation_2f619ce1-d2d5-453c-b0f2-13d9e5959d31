import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  FormControl,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  Grid,
  Autocomplete,
  FormGroup,
  Paper,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  Send as SendIcon,
  ContentCopy as CopyIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useProposals } from '../../hooks/useProposals';
import { ProposalSendOptions } from '../../types/proposals';
import { useContacts } from '../../hooks/useContacts';
import { IContact } from '../../types/contacts';

interface ProposalSendDialogProps {
  proposalId: string;
  onClose: () => void;
}

/**
 * Proposal Send Dialog Component
 * 
 * This component provides a UI for sending proposals via email.
 * It allows users to specify recipients, message, and attachment options.
 */
const ProposalSendDialog: React.FC<ProposalSendDialogProps> = ({
  proposalId,
  onClose
}) => {
  // Get proposal and contacts
  const { getProposalById, sendProposal } = useProposals();
  const { getContacts } = useContacts();
  
  // State for the proposal
  const [proposal, setProposal] = useState<any>(null);
  const [contacts, setContacts] = useState<IContact[]>([]);
  
  // State for the send options
  const [sendOptions, setSendOptions] = useState<ProposalSendOptions>({
    to: [],
    cc: [],
    bcc: [],
    subject: '',
    message: '',
    includeDownloadLink: true,
    includeViewLink: true,
    includeAttachments: false,
    attachmentFormats: ['pdf'],
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
  });
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [publicLink, setPublicLink] = useState<string>('');
  
  // Load proposal and contacts
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load proposal
        const loadedProposal = await getProposalById(proposalId);
        setProposal(loadedProposal);
        
        // Set default subject and message
        setSendOptions(prev => ({
          ...prev,
          subject: `Proposal: ${loadedProposal.title}`,
          message: `Dear recipient,\n\nI'm pleased to share our proposal "${loadedProposal.title}" with you.\n\nPlease review the attached proposal and let me know if you have any questions or would like to discuss further.\n\nBest regards,\n[Your Name]`
        }));
        
        // Generate public link
        if (loadedProposal.publicToken) {
          const baseUrl = window.location.origin;
          setPublicLink(`${baseUrl}/proposals/public/${loadedProposal.publicToken}`);
        }
        
        // Load contacts
        if (loadedProposal.contactIds && loadedProposal.contactIds.length > 0) {
          const loadedContacts = await getContacts({
            ids: loadedProposal.contactIds as string[]
          });
          setContacts(loadedContacts.data);
          
          // Set default recipients
          const contactEmails = loadedContacts.data
            .filter(contact => contact.email)
            .map(contact => contact.email as string);
          
          setSendOptions(prev => ({
            ...prev,
            to: contactEmails
          }));
        }
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load proposal data');
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [proposalId, getProposalById, getContacts]);
  
  // Handle send options change
  const handleSendOptionsChange = (field: keyof ProposalSendOptions, value: any) => {
    setSendOptions(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Handle attachment format toggle
  const handleAttachmentFormatToggle = (format: 'pdf' | 'docx') => {
    setSendOptions(prev => {
      const formats = [...(prev.attachmentFormats || [])];
      
      if (formats.includes(format)) {
        return {
          ...prev,
          attachmentFormats: formats.filter(f => f !== format)
        };
      } else {
        return {
          ...prev,
          attachmentFormats: [...formats, format]
        };
      }
    });
  };
  
  // Copy public link to clipboard
  const handleCopyLink = () => {
    navigator.clipboard.writeText(publicLink);
    setSuccess('Link copied to clipboard');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccess(null);
    }, 3000);
  };
  
  // Send the proposal
  const handleSend = async () => {
    if (sendOptions.to.length === 0) {
      setError('Please add at least one recipient');
      return;
    }
    
    setSending(true);
    setError(null);
    
    try {
      await sendProposal(proposalId, sendOptions);
      setSuccess('Proposal sent successfully');
      
      // Close the dialog after 2 seconds
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Error sending proposal:', err);
      setError('Failed to send proposal');
    } finally {
      setSending(false);
    }
  };
  
  // Render loading state
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={4}>
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (!proposal) {
    return (
      <Box p={4}>
        <Alert severity="error">
          {error || 'Failed to load proposal'}
        </Alert>
        <Box display="flex" justifyContent="flex-end" mt={2}>
          <Button onClick={onClose}>Close</Button>
        </Box>
      </Box>
    );
  }
  
  return (
    <Box p={2}>
      {/* Header */}
      <Typography variant="h6" gutterBottom>
        Send "{proposal.title}"
      </Typography>
      
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Success Alert */}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      
      <Grid container spacing={3}>
        {/* Left Column - Email Details */}
        <Grid item xs={12} md={7}>
          {/* Recipients */}
          <Typography variant="subtitle2" gutterBottom>
            Recipients
          </Typography>
          
          <Autocomplete
            multiple
            options={contacts.map(contact => contact.email || '')}
            value={sendOptions.to}
            onChange={(_, value) => handleSendOptionsChange('to', value)}
            renderInput={(params) => (
              <TextField
                {...params}
                label="To"
                variant="outlined"
                margin="normal"
                fullWidth
                required
              />
            )}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  label={option}
                  {...getTagProps({ index })}
                  size="small"
                />
              ))
            }
          />
          
          <Autocomplete
            multiple
            options={contacts.map(contact => contact.email || '')}
            value={sendOptions.cc}
            onChange={(_, value) => handleSendOptionsChange('cc', value)}
            renderInput={(params) => (
              <TextField
                {...params}
                label="CC"
                variant="outlined"
                margin="normal"
                fullWidth
              />
            )}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  label={option}
                  {...getTagProps({ index })}
                  size="small"
                />
              ))
            }
          />
          
          <Autocomplete
            multiple
            options={contacts.map(contact => contact.email || '')}
            value={sendOptions.bcc}
            onChange={(_, value) => handleSendOptionsChange('bcc', value)}
            renderInput={(params) => (
              <TextField
                {...params}
                label="BCC"
                variant="outlined"
                margin="normal"
                fullWidth
              />
            )}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  label={option}
                  {...getTagProps({ index })}
                  size="small"
                />
              ))
            }
          />
          
          {/* Subject */}
          <TextField
            label="Subject"
            value={sendOptions.subject}
            onChange={(e) => handleSendOptionsChange('subject', e.target.value)}
            fullWidth
            variant="outlined"
            margin="normal"
            required
          />
          
          {/* Message */}
          <TextField
            label="Message"
            value={sendOptions.message}
            onChange={(e) => handleSendOptionsChange('message', e.target.value)}
            fullWidth
            multiline
            rows={8}
            variant="outlined"
            margin="normal"
          />
          
          {/* Expiration Date */}
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DateTimePicker
              label="Expires At"
              value={sendOptions.expiresAt}
              onChange={(date) => handleSendOptionsChange('expiresAt', date)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  margin: 'normal',
                  variant: 'outlined',
                  InputProps: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <CalendarIcon />
                      </InputAdornment>
                    ),
                  },
                },
              }}
            />
          </LocalizationProvider>
        </Grid>
        
        {/* Right Column - Options */}
        <Grid item xs={12} md={5}>
          {/* Public Link */}
          {publicLink && (
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Public Link
              </Typography>
              <TextField
                value={publicLink}
                fullWidth
                variant="outlined"
                size="small"
                InputProps={{
                  readOnly: true,
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        edge="end"
                        onClick={handleCopyLink}
                        size="small"
                      >
                        <CopyIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                This link can be shared with anyone to view the proposal.
              </Typography>
            </Paper>
          )}
          
          {/* Options */}
          <Paper variant="outlined" sx={{ p: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Email Options
            </Typography>
            
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={sendOptions.includeViewLink}
                    onChange={(e) => handleSendOptionsChange('includeViewLink', e.target.checked)}
                  />
                }
                label="Include View Link"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={sendOptions.includeDownloadLink}
                    onChange={(e) => handleSendOptionsChange('includeDownloadLink', e.target.checked)}
                  />
                }
                label="Include Download Link"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={sendOptions.includeAttachments}
                    onChange={(e) => handleSendOptionsChange('includeAttachments', e.target.checked)}
                  />
                }
                label="Include Attachments"
              />
            </FormGroup>
            
            {sendOptions.includeAttachments && (
              <Box mt={2}>
                <Typography variant="body2" gutterBottom>
                  Attachment Formats
                </Typography>
                
                <FormGroup row>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={sendOptions.attachmentFormats?.includes('pdf')}
                        onChange={() => handleAttachmentFormatToggle('pdf')}
                      />
                    }
                    label="PDF"
                  />
                  
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={sendOptions.attachmentFormats?.includes('docx')}
                        onChange={() => handleAttachmentFormatToggle('docx')}
                      />
                    }
                    label="DOCX"
                  />
                </FormGroup>
              </Box>
            )}
          </Paper>
          
          {/* Proposal Info */}
          <Paper variant="outlined" sx={{ p: 2, mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Proposal Information
            </Typography>
            
            <Box>
              <Typography variant="body2" color="text.secondary">
                Status
              </Typography>
              <Chip
                label={proposal.status?.toUpperCase() || 'DRAFT'}
                color={
                  proposal.status === 'accepted' ? 'success' :
                  proposal.status === 'rejected' ? 'error' :
                  proposal.status === 'sent' ? 'primary' :
                  proposal.status === 'viewed' ? 'info' :
                  'default'
                }
                size="small"
                sx={{ mt: 0.5 }}
              />
            </Box>
            
            <Divider sx={{ my: 1.5 }} />
            
            <Box>
              <Typography variant="body2" color="text.secondary">
                Download Formats
              </Typography>
              <Box sx={{ mt: 0.5 }}>
                {proposal.downloadFormats?.map((format: string) => (
                  <Chip
                    key={format}
                    label={format.toUpperCase()}
                    size="small"
                    sx={{ mr: 0.5 }}
                  />
                ))}
                {(!proposal.downloadFormats || proposal.downloadFormats.length === 0) && (
                  <Typography variant="body2" color="text.secondary">
                    No download formats enabled
                  </Typography>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
      
      {/* Actions */}
      <Box display="flex" justifyContent="flex-end" mt={4}>
        <Button
          variant="outlined"
          onClick={onClose}
          sx={{ mr: 2 }}
          disabled={sending}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSend}
          disabled={sending || sendOptions.to.length === 0}
          startIcon={sending ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
        >
          {sending ? 'Sending...' : 'Send Proposal'}
        </Button>
      </Box>
    </Box>
  );
};

export default ProposalSendDialog;
