import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  FormControl,
  FormLabel,
  FormGroup,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Alert,
  Paper,
  Divider,
  Select,
  MenuItem,
  InputLabel,
  Grid,
  Card,
  CardContent,
  useMediaQuery,
  useTheme
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { IOpportunity } from '../../models/opportunity';
import { ICompany } from '../../models/company';
import { IContact } from '../../models/contact';

interface ProposalAIGeneratorMUIProps {
  opportunity?: IOpportunity;
  company?: ICompany;
  contacts?: IContact[];
  onGenerate: (generatedContent: {
    title: string;
    description: string;
    sections: Array<{
      title: string;
      content: string;
      type: string;
      order: number;
    }>;
    pricing?: {
      items: Array<{
        name: string;
        description: string;
        quantity: number;
        unitPrice: number;
        total: number;
      }>;
    };
    terms: string;
    aiPrompt: string;
    aiModel: string;
  }) => void;
}

/**
 * AI-powered proposal generator component using Material UI
 * 
 * This component provides a responsive UI for generating proposals with AI.
 */
const ProposalAIGeneratorMUI: React.FC<ProposalAIGeneratorMUIProps> = ({
  opportunity,
  company,
  contacts,
  onGenerate,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  
  // State
  const [prompt, setPrompt] = useState('');
  const [model, setModel] = useState('claude-3-opus-20240229');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Section toggles
  const [includeExecutiveSummary, setIncludeExecutiveSummary] = useState(true);
  const [includeSolution, setIncludeSolution] = useState(true);
  const [includeTimeline, setIncludeTimeline] = useState(true);
  const [includePricing, setIncludePricing] = useState(true);
  const [includeTeam, setIncludeTeam] = useState(false);
  const [includeTestimonials, setIncludeTestimonials] = useState(false);
  const [includeTerms, setIncludeTerms] = useState(true);
  
  // Generate a default prompt based on opportunity, company, and contacts
  const generateDefaultPrompt = () => {
    let defaultPrompt = 'Generate a professional business proposal';
    
    if (opportunity) {
      defaultPrompt += ` for the opportunity "${opportunity.name}"`;
      
      if (opportunity.description) {
        defaultPrompt += ` which involves ${opportunity.description}`;
      }
      
      if (opportunity.amount) {
        defaultPrompt += ` with an approximate value of $${opportunity.amount.toLocaleString()}`;
      }
    }
    
    if (company) {
      defaultPrompt += ` for ${company.name}`;
      
      if (company.industry) {
        defaultPrompt += ` in the ${company.industry} industry`;
      }
    }
    
    defaultPrompt += '. Include the following sections:';
    
    if (includeExecutiveSummary) {
      defaultPrompt += '\n- Executive Summary';
    }
    
    if (includeSolution) {
      defaultPrompt += '\n- Proposed Solution';
    }
    
    if (includeTimeline) {
      defaultPrompt += '\n- Project Timeline';
    }
    
    if (includePricing) {
      defaultPrompt += '\n- Pricing and Investment';
    }
    
    if (includeTeam) {
      defaultPrompt += '\n- Our Team';
    }
    
    if (includeTestimonials) {
      defaultPrompt += '\n- Client Testimonials';
    }
    
    if (includeTerms) {
      defaultPrompt += '\n- Terms and Conditions';
    }
    
    defaultPrompt += '\n\nMake the proposal persuasive, professional, and tailored to the client\'s needs.';
    
    return defaultPrompt;
  };
  
  // Handle prompt generation
  const handleGeneratePrompt = () => {
    setPrompt(generateDefaultPrompt());
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!prompt) {
      setError('Please enter a prompt');
      return;
    }
    
    setIsGenerating(true);
    setError(null);
    
    try {
      // This would be replaced with an actual API call to generate content
      // For now, we'll just simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock generated content
      const generatedContent = {
        title: opportunity ? `Proposal: ${opportunity.name}` : 'Business Proposal',
        description: 'A comprehensive solution tailored to your specific needs and objectives.',
        sections: [
          {
            title: 'Executive Summary',
            content: 'This proposal outlines our comprehensive solution designed to address your specific challenges and help you achieve your business objectives. Based on our understanding of your requirements, we have developed a tailored approach that leverages our expertise and proven methodologies to deliver exceptional results.',
            type: 'text',
            order: 0,
          },
          {
            title: 'Proposed Solution',
            content: 'Our solution combines innovative technology with industry best practices to provide a scalable, efficient, and effective approach to addressing your needs. We will implement a phased approach that minimizes disruption while maximizing value at each stage of the project.',
            type: 'text',
            order: 1,
          },
          {
            title: 'Project Timeline',
            content: 'The project will be executed in three phases over a 12-week period:\n\nPhase 1 (Weeks 1-4): Discovery and Planning\n- Requirements gathering and analysis\n- Solution design and architecture\n- Project plan finalization\n\nPhase 2 (Weeks 5-10): Implementation\n- Development and configuration\n- Integration with existing systems\n- Quality assurance and testing\n\nPhase 3 (Weeks 11-12): Deployment and Training\n- System deployment\n- User training and documentation\n- Post-implementation support',
            type: 'timeline',
            order: 2,
          },
        ],
        pricing: {
          items: [
            {
              name: 'Discovery and Planning',
              description: 'Requirements gathering, solution design, and project planning',
              quantity: 1,
              unitPrice: 5000,
              total: 5000,
            },
            {
              name: 'Implementation',
              description: 'Development, configuration, integration, and testing',
              quantity: 1,
              unitPrice: 15000,
              total: 15000,
            },
            {
              name: 'Deployment and Training',
              description: 'System deployment, user training, and documentation',
              quantity: 1,
              unitPrice: 5000,
              total: 5000,
            },
            {
              name: 'Monthly Support',
              description: 'Ongoing technical support and maintenance',
              quantity: 12,
              unitPrice: 1000,
              total: 12000,
            },
          ],
        },
        terms: 'Payment Terms:\n- 50% due upon project initiation\n- 25% due upon completion of Phase 2\n- 25% due upon project completion\n\nProject Timeline:\n- Project will commence within 2 weeks of proposal acceptance\n- Estimated completion time is 12 weeks from project start date\n\nWarranty:\n- 90-day warranty on all deliverables\n- Support included for 12 months from project completion',
        aiPrompt: prompt,
        aiModel: model,
      };
      
      onGenerate(generatedContent);
    } catch (error) {
      console.error('Error generating proposal:', error);
      setError('Failed to generate proposal. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };
  
  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Grid container spacing={{ xs: 2, md: 3 }}>
        {/* Sections to Include - Appears first on mobile */}
        <Grid item xs={12} md={5} order={{ xs: 1, md: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Sections to Include
          </Typography>
          <Paper variant="outlined" sx={{ p: 2 }}>
            <FormControl component="fieldset" fullWidth>
              <FormGroup>
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={6} md={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeExecutiveSummary}
                          onChange={(e) => setIncludeExecutiveSummary(e.target.checked)}
                          aria-label="Include executive summary"
                        />
                      }
                      label="Executive Summary"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeSolution}
                          onChange={(e) => setIncludeSolution(e.target.checked)}
                          aria-label="Include proposed solution"
                        />
                      }
                      label="Proposed Solution"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeTimeline}
                          onChange={(e) => setIncludeTimeline(e.target.checked)}
                          aria-label="Include project timeline"
                        />
                      }
                      label="Project Timeline"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includePricing}
                          onChange={(e) => setIncludePricing(e.target.checked)}
                          aria-label="Include pricing"
                        />
                      }
                      label="Pricing"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeTeam}
                          onChange={(e) => setIncludeTeam(e.target.checked)}
                          aria-label="Include team members"
                        />
                      }
                      label="Team Members"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeTestimonials}
                          onChange={(e) => setIncludeTestimonials(e.target.checked)}
                          aria-label="Include client testimonials"
                        />
                      }
                      label="Client Testimonials"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeTerms}
                          onChange={(e) => setIncludeTerms(e.target.checked)}
                          aria-label="Include terms and conditions"
                        />
                      }
                      label="Terms & Conditions"
                    />
                  </Grid>
                </Grid>
              </FormGroup>
            </FormControl>
          </Paper>
        </Grid>
        
        {/* Prompt and Model - Appears second on mobile */}
        <Grid item xs={12} md={7} order={{ xs: 2, md: 1 }}>
          {/* Model Selection */}
          <FormControl fullWidth margin="normal">
            <InputLabel id="ai-model-label">AI Model</InputLabel>
            <Select
              labelId="ai-model-label"
              value={model}
              onChange={(e) => setModel(e.target.value)}
              label="AI Model"
              aria-label="Select AI model"
            >
              <MenuItem value="claude-3-opus-20240229">Claude 3 Opus (Highest Quality)</MenuItem>
              <MenuItem value="claude-3-sonnet-20240229">Claude 3 Sonnet (Balanced)</MenuItem>
              <MenuItem value="claude-3-haiku-20240307">Claude 3 Haiku (Fastest)</MenuItem>
            </Select>
          </FormControl>
          
          {/* Generate Default Prompt Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, mb: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleGeneratePrompt}
              aria-label="Generate default prompt"
            >
              Generate Default Prompt
            </Button>
          </Box>
          
          {/* Prompt */}
          <Typography variant="subtitle2" gutterBottom>
            AI Prompt
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={{ xs: 6, md: 8 }}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Describe what you want to include in your proposal. For example: 'Create a proposal for a web development project for a small e-commerce business. The project includes website design, development, and 6 months of maintenance. The budget is around $15,000.'"
            variant="outlined"
            margin="normal"
            required
            inputProps={{ 'aria-label': 'AI prompt' }}
          />
          
          {/* Context Information */}
          {(opportunity || company || contacts) && (
            <Paper variant="outlined" sx={{ p: 2, mt: 2, bgcolor: 'background.paper' }}>
              <Typography variant="subtitle2" gutterBottom>
                Context Information
              </Typography>
              
              {opportunity && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" fontWeight="medium">
                    Opportunity:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {opportunity.name}
                    {opportunity.description && ` - ${opportunity.description}`}
                    {opportunity.amount && ` - $${opportunity.amount.toLocaleString()}`}
                  </Typography>
                </Box>
              )}
              
              {company && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" fontWeight="medium">
                    Company:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {company.name}
                    {company.industry && ` - ${company.industry}`}
                  </Typography>
                </Box>
              )}
              
              {contacts && contacts.length > 0 && (
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    Contacts:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {contacts.map(contact => contact.name).join(', ')}
                  </Typography>
                </Box>
              )}
            </Paper>
          )}
        </Grid>
      </Grid>
      
      {/* Submit Button */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'flex-end', 
        mt: 4,
        position: 'sticky',
        bottom: { xs: 0, md: 'auto' },
        backgroundColor: { xs: 'background.paper', md: 'transparent' },
        padding: { xs: 2, md: 0 },
        borderTop: { xs: `1px solid ${theme.palette.divider}`, md: 'none' },
        zIndex: 10,
        width: '100%'
      }}>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={isGenerating || !prompt}
          startIcon={isGenerating ? <CircularProgress size={20} color="inherit" /> : <AIIcon />}
          fullWidth={isMobile}
          aria-label="Generate proposal"
        >
          {isGenerating ? 'Generating...' : 'Generate Proposal'}
        </Button>
      </Box>
    </Box>
  );
};

export default ProposalAIGeneratorMUI;
