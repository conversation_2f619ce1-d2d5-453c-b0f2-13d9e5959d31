import React from 'react';
import { Card } from '@aizako/ui-kit';
import { IProposal, IProposalAnalyticsEvent } from '../../models/proposal';
import { formatDate } from '../../utils/formatters';

interface ProposalAnalyticsProps {
  proposal: IProposal;
}

/**
 * Proposal analytics component
 */
export const ProposalAnalytics: React.FC<ProposalAnalyticsProps> = ({
  proposal,
}) => {
  // Group events by date
  const eventsByDate = proposal.analyticsEvents?.reduce((acc, event) => {
    const date = new Date(event.timestamp).toLocaleDateString();
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(event);
    return acc;
  }, {} as Record<string, IProposalAnalyticsEvent[]>) || {};
  
  // Calculate total view time
  const totalViewTime = proposal.analyticsEvents?.reduce((total, event) => {
    return total + (event.duration || 0);
  }, 0) || 0;
  
  // Calculate average view time
  const viewEvents = proposal.analyticsEvents?.filter(event => event.eventType === 'view') || [];
  const averageViewTime = viewEvents.length > 0
    ? (viewEvents.reduce((total, event) => total + (event.duration || 0), 0) / viewEvents.length)
    : 0;
  
  // Calculate section views
  const sectionViews = proposal.analyticsEvents?.filter(event => event.eventType === 'section_view') || [];
  const sectionViewCounts = sectionViews.reduce((acc, event) => {
    const sectionId = event.sectionId;
    if (sectionId) {
      if (!acc[sectionId]) {
        acc[sectionId] = 0;
      }
      acc[sectionId]++;
    }
    return acc;
  }, {} as Record<string, number>);
  
  // Get section names
  const sectionNames = proposal.sections.reduce((acc, section) => {
    acc[section.id] = section.title;
    return acc;
  }, {} as Record<string, string>);
  
  // Calculate device breakdown
  const deviceCounts = proposal.analyticsEvents?.reduce((acc, event) => {
    const device = event.device || 'Unknown';
    if (!acc[device]) {
      acc[device] = 0;
    }
    acc[device]++;
    return acc;
  }, {} as Record<string, number>) || {};
  
  // Calculate browser breakdown
  const browserCounts = proposal.analyticsEvents?.reduce((acc, event) => {
    let browser = 'Unknown';
    
    if (event.userAgent) {
      if (event.userAgent.includes('Chrome')) browser = 'Chrome';
      else if (event.userAgent.includes('Firefox')) browser = 'Firefox';
      else if (event.userAgent.includes('Safari')) browser = 'Safari';
      else if (event.userAgent.includes('Edge')) browser = 'Edge';
      else if (event.userAgent.includes('MSIE') || event.userAgent.includes('Trident')) browser = 'Internet Explorer';
    }
    
    if (!acc[browser]) {
      acc[browser] = 0;
    }
    acc[browser]++;
    return acc;
  }, {} as Record<string, number>) || {};
  
  return (
    <div className="space-y-6">
      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Proposal Analytics</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Total Views</h3>
            <p className="text-2xl font-bold">{proposal.viewCount || 0}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Total View Time</h3>
            <p className="text-2xl font-bold">{Math.round(totalViewTime / 60)} min</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Avg. View Time</h3>
            <p className="text-2xl font-bold">{Math.round(averageViewTime)} sec</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Status</h3>
            <p className="text-2xl font-bold capitalize">{proposal.status}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium mb-3">View Timeline</h3>
            
            {Object.keys(eventsByDate).length > 0 ? (
              <div className="space-y-4">
                {Object.entries(eventsByDate).map(([date, events]) => (
                  <div key={date} className="border-b border-gray-200 pb-3">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">{date}</h4>
                    
                    <div className="space-y-2">
                      {events.map((event, index) => (
                        <div key={index} className="flex items-start">
                          <div className="mr-2 mt-1">
                            <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                          </div>
                          <div>
                            <p className="text-sm">
                              <span className="font-medium capitalize">{event.eventType.replace('_', ' ')}</span>
                              {' - '}
                              {new Date(event.timestamp).toLocaleTimeString()}
                            </p>
                            {event.duration && (
                              <p className="text-xs text-gray-500">
                                Duration: {event.duration} seconds
                              </p>
                            )}
                            {event.device && (
                              <p className="text-xs text-gray-500">
                                Device: {event.device}
                              </p>
                            )}
                            {event.location && (
                              <p className="text-xs text-gray-500">
                                Location: {event.location}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No view data available</p>
            )}
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-3">Section Engagement</h3>
            
            {Object.keys(sectionViewCounts).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(sectionViewCounts).map(([sectionId, count]) => (
                  <div key={sectionId} className="flex items-center">
                    <div className="w-full">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">{sectionNames[sectionId] || `Section ${sectionId}`}</span>
                        <span className="text-sm text-gray-500">{count} views</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${Math.min(100, (count / Math.max(...Object.values(sectionViewCounts))) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No section engagement data available</p>
            )}
            
            <h3 className="text-lg font-medium mt-6 mb-3">Device Breakdown</h3>
            
            {Object.keys(deviceCounts).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(deviceCounts).map(([device, count]) => (
                  <div key={device} className="flex items-center">
                    <div className="w-full">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">{device}</span>
                        <span className="text-sm text-gray-500">{count} events</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${Math.min(100, (count / Math.max(...Object.values(deviceCounts))) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No device data available</p>
            )}
            
            <h3 className="text-lg font-medium mt-6 mb-3">Browser Breakdown</h3>
            
            {Object.keys(browserCounts).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(browserCounts).map(([browser, count]) => (
                  <div key={browser} className="flex items-center">
                    <div className="w-full">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">{browser}</span>
                        <span className="text-sm text-gray-500">{count} events</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full"
                          style={{ width: `${Math.min(100, (count / Math.max(...Object.values(browserCounts))) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No browser data available</p>
            )}
          </div>
        </div>
        
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-3">Proposal History</h3>
          
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="mr-2 mt-1">
                <div className="h-3 w-3 rounded-full bg-blue-500"></div>
              </div>
              <div>
                <p className="text-sm">
                  <span className="font-medium">Created</span>
                  {' - '}
                  {formatDate(proposal.createdAt)}
                </p>
                {proposal.createdBy && (
                  <p className="text-xs text-gray-500">
                    By: {proposal.createdBy.toString()}
                  </p>
                )}
              </div>
            </div>
            
            {proposal.sentAt && (
              <div className="flex items-start">
                <div className="mr-2 mt-1">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                </div>
                <div>
                  <p className="text-sm">
                    <span className="font-medium">Sent</span>
                    {' - '}
                    {formatDate(proposal.sentAt)}
                  </p>
                  {proposal.sentBy && (
                    <p className="text-xs text-gray-500">
                      By: {proposal.sentBy.toString()}
                    </p>
                  )}
                </div>
              </div>
            )}
            
            {proposal.viewedAt && (
              <div className="flex items-start">
                <div className="mr-2 mt-1">
                  <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <p className="text-sm">
                    <span className="font-medium">First Viewed</span>
                    {' - '}
                    {formatDate(proposal.viewedAt)}
                  </p>
                </div>
              </div>
            )}
            
            {proposal.acceptedAt && (
              <div className="flex items-start">
                <div className="mr-2 mt-1">
                  <div className="h-3 w-3 rounded-full bg-green-600"></div>
                </div>
                <div>
                  <p className="text-sm">
                    <span className="font-medium">Accepted</span>
                    {' - '}
                    {formatDate(proposal.acceptedAt)}
                  </p>
                  {proposal.acceptedBy && (
                    <p className="text-xs text-gray-500">
                      By: {proposal.acceptedBy.toString()}
                    </p>
                  )}
                </div>
              </div>
            )}
            
            {proposal.rejectedAt && (
              <div className="flex items-start">
                <div className="mr-2 mt-1">
                  <div className="h-3 w-3 rounded-full bg-red-500"></div>
                </div>
                <div>
                  <p className="text-sm">
                    <span className="font-medium">Rejected</span>
                    {' - '}
                    {formatDate(proposal.rejectedAt)}
                  </p>
                  {proposal.rejectedBy && (
                    <p className="text-xs text-gray-500">
                      By: {proposal.rejectedBy.toString()}
                    </p>
                  )}
                  {proposal.rejectionReason && (
                    <p className="text-xs text-gray-500">
                      Reason: {proposal.rejectionReason}
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProposalAnalytics;
