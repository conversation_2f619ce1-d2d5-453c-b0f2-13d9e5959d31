import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Button,
  Divider,
  useTheme,
  useMediaQuery,
  Skeleton,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Collapse,
  Pagination
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { IProposalSection } from '../../types/proposals';

interface ProposalSectionListProps {
  sections: IProposalSection[];
  loading?: boolean;
  onEditSection: (sectionId: string) => void;
  onDeleteSection: (sectionId: string) => void;
  onToggleSectionVisibility: (sectionId: string) => void;
  onAddSection: () => void;
  onSectionClick: (sectionId: string) => void;
  selectedSectionId?: string;
  pageSize?: number;
}

/**
 * ProposalSectionList Component
 * 
 * This component displays a paginated list of proposal sections with lazy loading.
 */
const ProposalSectionList: React.FC<ProposalSectionListProps> = ({
  sections,
  loading = false,
  onEditSection,
  onDeleteSection,
  onToggleSectionVisibility,
  onAddSection,
  onSectionClick,
  selectedSectionId,
  pageSize = 5
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // State for pagination
  const [page, setPage] = useState(1);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  
  // Calculate total pages
  const totalPages = Math.ceil(sections.length / pageSize);
  
  // Get current page sections
  const getCurrentPageSections = useCallback(() => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return sections.slice(startIndex, endIndex);
  }, [sections, page, pageSize]);
  
  const [visibleSections, setVisibleSections] = useState<IProposalSection[]>(getCurrentPageSections());
  
  // Update visible sections when page or sections change
  useEffect(() => {
    setVisibleSections(getCurrentPageSections());
  }, [page, sections, getCurrentPageSections]);
  
  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    // Scroll to top of list
    const listElement = document.getElementById('proposal-section-list');
    if (listElement) {
      listElement.scrollTop = 0;
    }
  };
  
  // Toggle section expansion
  const toggleSectionExpansion = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };
  
  // Render loading skeleton
  if (loading) {
    return (
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">Sections</Typography>
          <Skeleton variant="rectangular" width={100} height={36} />
        </Box>
        {[...Array(3)].map((_, index) => (
          <Box key={index} mb={2}>
            <Skeleton variant="rectangular" height={60} />
          </Box>
        ))}
      </Box>
    );
  }
  
  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">
          Sections ({sections.length})
        </Typography>
        <Button
          startIcon={<AddIcon />}
          variant="contained"
          color="primary"
          onClick={onAddSection}
          size={isMobile ? "small" : "medium"}
        >
          Add Section
        </Button>
      </Box>
      
      <Paper variant="outlined" id="proposal-section-list">
        <List disablePadding>
          {visibleSections.length === 0 ? (
            <ListItem>
              <ListItemText
                primary="No sections"
                secondary="Click 'Add Section' to create a new section"
              />
            </ListItem>
          ) : (
            visibleSections.map((section) => (
              <React.Fragment key={section.id}>
                <ListItem
                  button
                  selected={selectedSectionId === section.id}
                  onClick={() => onSectionClick(section.id)}
                  sx={{
                    borderLeft: selectedSectionId === section.id 
                      ? `4px solid ${theme.palette.primary.main}` 
                      : '4px solid transparent',
                    opacity: section.isVisible ? 1 : 0.6,
                    bgcolor: selectedSectionId === section.id 
                      ? alpha(theme.palette.primary.main, 0.1) 
                      : 'transparent',
                  }}
                >
                  <ListItemText
                    primary={
                      <Typography
                        variant="subtitle1"
                        fontWeight={selectedSectionId === section.id ? 'bold' : 'normal'}
                        sx={{ 
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: { xs: '150px', sm: '250px', md: '300px' }
                        }}
                      >
                        {section.title}
                      </Typography>
                    }
                    secondary={
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ 
                          display: '-webkit-box',
                          WebkitLineClamp: 1,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {section.type.charAt(0).toUpperCase() + section.type.slice(1)} section
                        {section.aiGenerated && ' • AI Generated'}
                      </Typography>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box display="flex">
                      <Tooltip title={section.isVisible ? "Hide section" : "Show section"}>
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            onToggleSectionVisibility(section.id);
                          }}
                          size="small"
                        >
                          {section.isVisible ? <VisibilityIcon /> : <VisibilityOffIcon />}
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Edit section">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditSection(section.id);
                          }}
                          size="small"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Delete section">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDeleteSection(section.id);
                          }}
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title={expandedSections[section.id] ? "Collapse" : "Expand"}>
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleSectionExpansion(section.id);
                          }}
                          size="small"
                        >
                          {expandedSections[section.id] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                
                <Collapse in={expandedSections[section.id]} timeout="auto" unmountOnExit>
                  <Box p={2} bgcolor={alpha(theme.palette.background.default, 0.5)}>
                    <Typography
                      variant="body2"
                      sx={{
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {section.content.replace(/<[^>]*>?/gm, '')}
                    </Typography>
                  </Box>
                </Collapse>
                
                <Divider />
              </React.Fragment>
            ))
          )}
        </List>
        
        {totalPages > 1 && (
          <Box display="flex" justifyContent="center" p={2}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={handlePageChange}
              color="primary"
              size={isMobile ? "small" : "medium"}
            />
          </Box>
        )}
      </Paper>
    </Box>
  );
};

// Helper function to create alpha color
function alpha(color: string, opacity: number): string {
  return color + Math.round(opacity * 255).toString(16).padStart(2, '0');
}

export default ProposalSectionList;
