import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  Tabs,
  Tab,
} from '@mui/material';
import {
  CompareArrows as CompareIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as ViewIcon,
  Timeline as TimelineIcon,
  AttachMoney as MoneyIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalService } from '../../../services/proposal-service';
import { formatCurrency } from '../../../utils/formatters';
import DiffViewer from 'react-diff-viewer';

interface ProposalComparisonViewProps {
  tenantId: string;
  initialProposalIds?: string[];
  onViewProposal?: (proposalId: string) => void;
}

/**
 * ProposalComparisonView Component
 * 
 * This component displays a comparison view of multiple proposals.
 */
const ProposalComparisonView: React.FC<ProposalComparisonViewProps> = ({
  tenantId,
  initialProposalIds = [],
  onViewProposal,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [proposals, setProposals] = useState<any[]>([]);
  const [selectedProposalIds, setSelectedProposalIds] = useState<string[]>(initialProposalIds);
  const [selectedProposals, setSelectedProposals] = useState<any[]>([]);
  const [availableProposals, setAvailableProposals] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [diffDialogOpen, setDiffDialogOpen] = useState(false);
  const [diffSection, setDiffSection] = useState<{
    title: string;
    left: string;
    right: string;
  } | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  
  // Fetch proposals
  useEffect(() => {
    const fetchProposals = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch all proposals
        const response = await ProposalService.getProposals(tenantId, {
          limit: 100,
          page: 1,
        });
        
        setAvailableProposals(response.proposals);
        
        // Fetch selected proposals
        if (selectedProposalIds.length > 0) {
          const selectedProposalsData = await Promise.all(
            selectedProposalIds.map(id => ProposalService.getProposalById(id, tenantId))
          );
          
          setSelectedProposals(selectedProposalsData);
        }
      } catch (err) {
        console.error('Error fetching proposals:', err);
        setError('Failed to load proposals. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProposals();
  }, [tenantId, selectedProposalIds]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchProposals = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch all proposals
        const response = await ProposalService.getProposals(tenantId, {
          limit: 100,
          page: 1,
        });
        
        setAvailableProposals(response.proposals);
        
        // Fetch selected proposals
        if (selectedProposalIds.length > 0) {
          const selectedProposalsData = await Promise.all(
            selectedProposalIds.map(id => ProposalService.getProposalById(id, tenantId))
          );
          
          setSelectedProposals(selectedProposalsData);
        }
      } catch (err) {
        console.error('Error fetching proposals:', err);
        setError('Failed to load proposals. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProposals();
  };
  
  // Handle add proposal
  const handleAddProposal = (proposalId: string) => {
    if (selectedProposalIds.includes(proposalId)) return;
    
    setSelectedProposalIds([...selectedProposalIds, proposalId]);
    setAddDialogOpen(false);
  };
  
  // Handle remove proposal
  const handleRemoveProposal = (proposalId: string) => {
    setSelectedProposalIds(selectedProposalIds.filter(id => id !== proposalId));
  };
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Handle view diff
  const handleViewDiff = (sectionTitle: string, leftContent: string, rightContent: string) => {
    setDiffSection({
      title: sectionTitle,
      left: leftContent,
      right: rightContent,
    });
    setDiffDialogOpen(true);
  };
  
  // Filter available proposals
  const filteredAvailableProposals = availableProposals.filter(proposal => {
    if (selectedProposalIds.includes(proposal._id)) return false;
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        proposal.title.toLowerCase().includes(query) ||
        proposal.description?.toLowerCase().includes(query) ||
        proposal.status.toLowerCase().includes(query)
      );
    }
    
    return true;
  });
  
  // Render loading state
  if (loading && selectedProposals.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Proposal Comparison
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setAddDialogOpen(true)}
          >
            Add Proposal
          </Button>
        </Box>
      </Box>
      
      {/* Selected Proposals */}
      {selectedProposals.length === 0 ? (
        <Alert severity="info">
          No proposals selected for comparison. Click "Add Proposal" to select proposals to compare.
        </Alert>
      ) : (
        <Box>
          {/* Proposal Cards */}
          <Grid container spacing={3} mb={3}>
            {selectedProposals.map((proposal) => (
              <Grid item xs={12} sm={6} md={4} key={proposal._id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                      <Typography variant="h6" noWrap sx={{ maxWidth: '200px' }}>
                        {proposal.title}
                      </Typography>
                      <IconButton 
                        size="small" 
                        color="error"
                        onClick={() => handleRemoveProposal(proposal._id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {proposal.description}
                    </Typography>
                    
                    <Divider sx={{ my: 1 }} />
                    
                    <Box display="flex" flexWrap="wrap" gap={0.5} mb={1}>
                      <Chip 
                        label={proposal.status.toUpperCase()} 
                        color={
                          proposal.status === 'accepted' ? 'success' :
                          proposal.status === 'rejected' ? 'error' :
                          proposal.status === 'viewed' ? 'info' :
                          proposal.status === 'sent' ? 'primary' :
                          'default'
                        }
                        size="small"
                      />
                      {proposal.sections?.length > 0 && (
                        <Chip 
                          label={`${proposal.sections.length} Sections`} 
                          size="small" 
                        />
                      )}
                    </Box>
                    
                    <Typography variant="caption" display="block" color="text.secondary">
                      Created {format(new Date(proposal.createdAt), 'PPP')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
          
          {/* Comparison Tabs */}
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant={isMobile ? "scrollable" : "fullWidth"}
              scrollButtons={isMobile ? "auto" : undefined}
            >
              <Tab label="Overview" icon={<DescriptionIcon />} />
              <Tab label="Content" icon={<CompareIcon />} />
              <Tab label="Pricing" icon={<MoneyIcon />} />
              <Tab label="Timeline" icon={<TimelineIcon />} />
            </Tabs>
          </Paper>
          
          {/* Tab Content */}
          {activeTab === 0 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Feature</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {proposal.title}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>Status</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        <Chip 
                          label={proposal.status.toUpperCase()} 
                          color={
                            proposal.status === 'accepted' ? 'success' :
                            proposal.status === 'rejected' ? 'error' :
                            proposal.status === 'viewed' ? 'info' :
                            proposal.status === 'sent' ? 'primary' :
                            'default'
                          }
                          size="small"
                        />
                      </TableCell>
                    ))}
                  </TableRow>
                  <TableRow>
                    <TableCell>Created</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {format(new Date(proposal.createdAt), 'PPP')}
                      </TableCell>
                    ))}
                  </TableRow>
                  <TableRow>
                    <TableCell>Last Updated</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {format(new Date(proposal.updatedAt), 'PPP')}
                      </TableCell>
                    ))}
                  </TableRow>
                  <TableRow>
                    <TableCell>Sections</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {proposal.sections?.length || 0}
                      </TableCell>
                    ))}
                  </TableRow>
                  <TableRow>
                    <TableCell>Total Price</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {proposal.pricing?.total ? formatCurrency(proposal.pricing.total) : 'N/A'}
                      </TableCell>
                    ))}
                  </TableRow>
                  <TableRow>
                    <TableCell>AI Generated</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {proposal.aiGenerated ? (
                          <CheckCircleIcon color="success" />
                        ) : (
                          <CancelIcon color="error" />
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                  <TableRow>
                    <TableCell>Actions</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        <Tooltip title="View Proposal">
                          <IconButton
                            size="small"
                            onClick={() => onViewProposal && onViewProposal(proposal._id)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Download">
                          <IconButton
                            size="small"
                          >
                            <DownloadIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          )}
          
          {activeTab === 1 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Section</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {proposal.title}
                      </TableCell>
                    ))}
                    <TableCell>Compare</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* Get all unique section titles */}
                  {Array.from(
                    new Set(
                      selectedProposals.flatMap(proposal => 
                        proposal.sections?.map(section => section.title) || []
                      )
                    )
                  ).map((sectionTitle) => (
                    <TableRow key={sectionTitle}>
                      <TableCell>{sectionTitle}</TableCell>
                      {selectedProposals.map((proposal) => {
                        const section = proposal.sections?.find(s => s.title === sectionTitle);
                        return (
                          <TableCell key={proposal._id}>
                            {section ? (
                              <Box>
                                <Typography variant="body2" noWrap sx={{ maxWidth: '200px' }}>
                                  {section.content.substring(0, 50)}...
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {section.type}
                                </Typography>
                              </Box>
                            ) : (
                              <Typography variant="body2" color="text.secondary">
                                Not included
                              </Typography>
                            )}
                          </TableCell>
                        );
                      })}
                      <TableCell>
                        {selectedProposals.length === 2 && (
                          <Tooltip title="Compare Content">
                            <IconButton
                              size="small"
                              onClick={() => {
                                const leftProposal = selectedProposals[0];
                                const rightProposal = selectedProposals[1];
                                const leftSection = leftProposal.sections?.find(s => s.title === sectionTitle);
                                const rightSection = rightProposal.sections?.find(s => s.title === sectionTitle);
                                
                                handleViewDiff(
                                  sectionTitle,
                                  leftSection?.content || '',
                                  rightSection?.content || ''
                                );
                              }}
                            >
                              <CompareIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          
          {activeTab === 2 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Item</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {proposal.title}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* Get all unique pricing item names */}
                  {Array.from(
                    new Set(
                      selectedProposals.flatMap(proposal => 
                        proposal.pricing?.items?.map(item => item.name) || []
                      )
                    )
                  ).map((itemName) => (
                    <TableRow key={itemName}>
                      <TableCell>{itemName}</TableCell>
                      {selectedProposals.map((proposal) => {
                        const item = proposal.pricing?.items?.find(i => i.name === itemName);
                        return (
                          <TableCell key={proposal._id}>
                            {item ? (
                              <Box>
                                <Typography variant="body2">
                                  {formatCurrency(item.unitPrice)} x {item.quantity}
                                </Typography>
                                <Typography variant="body1" fontWeight="bold">
                                  {formatCurrency(item.total)}
                                </Typography>
                              </Box>
                            ) : (
                              <Typography variant="body2" color="text.secondary">
                                Not included
                              </Typography>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Subtotal</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id} sx={{ fontWeight: 'bold' }}>
                        {proposal.pricing?.subtotal ? formatCurrency(proposal.pricing.subtotal) : 'N/A'}
                      </TableCell>
                    ))}
                  </TableRow>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Total</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id} sx={{ fontWeight: 'bold' }}>
                        {proposal.pricing?.total ? formatCurrency(proposal.pricing.total) : 'N/A'}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          )}
          
          {activeTab === 3 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Timeline</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id}>
                        {proposal.title}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* Get all unique timeline milestone names */}
                  {Array.from(
                    new Set(
                      selectedProposals.flatMap(proposal => 
                        proposal.timeline?.milestones?.map(milestone => milestone.name) || []
                      )
                    )
                  ).map((milestoneName) => (
                    <TableRow key={milestoneName}>
                      <TableCell>{milestoneName}</TableCell>
                      {selectedProposals.map((proposal) => {
                        const milestone = proposal.timeline?.milestones?.find(m => m.name === milestoneName);
                        return (
                          <TableCell key={proposal._id}>
                            {milestone ? (
                              <Box>
                                <Typography variant="body2">
                                  {milestone.duration} {milestone.durationUnit}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {milestone.description}
                                </Typography>
                              </Box>
                            ) : (
                              <Typography variant="body2" color="text.secondary">
                                Not included
                              </Typography>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Total Duration</TableCell>
                    {selectedProposals.map((proposal) => (
                      <TableCell key={proposal._id} sx={{ fontWeight: 'bold' }}>
                        {proposal.timeline?.totalDuration ? 
                          `${proposal.timeline.totalDuration} ${proposal.timeline.durationUnit}` : 
                          'N/A'}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Box>
      )}
      
      {/* Add Proposal Dialog */}
      <Dialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Add Proposal to Comparison</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            placeholder="Search proposals..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            margin="normal"
          />
          
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Title</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredAvailableProposals.map((proposal) => (
                  <TableRow key={proposal._id}>
                    <TableCell>{proposal.title}</TableCell>
                    <TableCell>
                      <Chip 
                        label={proposal.status.toUpperCase()} 
                        color={
                          proposal.status === 'accepted' ? 'success' :
                          proposal.status === 'rejected' ? 'error' :
                          proposal.status === 'viewed' ? 'info' :
                          proposal.status === 'sent' ? 'primary' :
                          'default'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {format(new Date(proposal.createdAt), 'PPP')}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={() => handleAddProposal(proposal._id)}
                      >
                        Add
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>
      
      {/* Diff Dialog */}
      <Dialog
        open={diffDialogOpen}
        onClose={() => setDiffDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Compare Section: {diffSection?.title}
        </DialogTitle>
        <DialogContent>
          {diffSection && (
            <DiffViewer
              oldValue={diffSection.left}
              newValue={diffSection.right}
              splitView={true}
              leftTitle={selectedProposals[0]?.title || 'Left'}
              rightTitle={selectedProposals[1]?.title || 'Right'}
              useDarkTheme={theme.palette.mode === 'dark'}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDiffDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProposalComparisonView;
