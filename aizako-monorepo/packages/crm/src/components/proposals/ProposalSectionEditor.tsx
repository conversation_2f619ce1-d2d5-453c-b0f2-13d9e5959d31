import React, { useState } from 'react';
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Collapse,
  Paper
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  AutoAwesome as AIIcon
} from '@mui/icons-material';
import { IProposalSection } from '../../types/proposals';
import RichTextEditor from '../common/RichTextEditor';
import { ProposalService } from '../../services/proposal-service';

interface ProposalSectionEditorProps {
  section: IProposalSection;
  onChange: (updates: Partial<IProposalSection>) => void;
}

/**
 * Proposal Section Editor Component
 * 
 * This component provides a UI for editing a proposal section.
 * It supports different section types and AI-powered content generation.
 */
const ProposalSectionEditor: React.FC<ProposalSectionEditorProps> = ({
  section,
  onChange
}) => {
  const [expanded, setExpanded] = useState(true);
  const [aiPrompt, setAiPrompt] = useState('');
  const [aiDialogOpen, setAiDialogOpen] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Handle section field changes
  const handleChange = (field: string, value: any) => {
    onChange({ [field]: value });
  };
  
  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };
  
  // Generate content with AI
  const generateWithAI = async () => {
    if (!aiPrompt) {
      setError('Please enter a prompt');
      return;
    }
    
    setGenerating(true);
    setError(null);
    
    try {
      const generatedSection = await ProposalService.generateProposalSectionWithAI(
        section.type,
        aiPrompt,
        'claude-3-opus-20240229'
      );
      
      onChange({
        content: generatedSection.content,
        aiGenerated: true
      });
      
      setAiDialogOpen(false);
      setAiPrompt('');
    } catch (err) {
      setError('Failed to generate content');
      console.error(err);
    } finally {
      setGenerating(false);
    }
  };
  
  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <TextField
          label="Section Title"
          value={section.title}
          onChange={(e) => handleChange('title', e.target.value)}
          fullWidth
          variant="outlined"
          size="small"
          sx={{ mr: 2 }}
        />
        
        <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Type</InputLabel>
          <Select
            value={section.type}
            onChange={(e) => handleChange('type', e.target.value)}
            label="Type"
          >
            <MenuItem value="text">Text</MenuItem>
            <MenuItem value="timeline">Timeline</MenuItem>
            <MenuItem value="team">Team</MenuItem>
            <MenuItem value="testimonials">Testimonials</MenuItem>
            <MenuItem value="images">Images</MenuItem>
            <MenuItem value="custom">Custom</MenuItem>
          </Select>
        </FormControl>
        
        <IconButton onClick={toggleExpanded} size="small" sx={{ ml: 1 }}>
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
      </Box>
      
      <Collapse in={expanded}>
        <Box sx={{ mt: 2 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="subtitle2">
              Content
            </Typography>
            <Tooltip title="Generate with AI">
              <Button
                startIcon={<AIIcon />}
                size="small"
                variant="outlined"
                color="secondary"
                onClick={() => setAiDialogOpen(true)}
              >
                AI Generate
              </Button>
            </Tooltip>
          </Box>
          
          {/* Rich Text Editor for content */}
          <RichTextEditor
            value={section.content}
            onChange={(value) => handleChange('content', value)}
            placeholder="Enter section content..."
            minHeight={200}
          />
          
          {/* AI Generation Dialog */}
          <Collapse in={aiDialogOpen}>
            <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.paper' }}>
              <Typography variant="subtitle2" gutterBottom>
                Generate Content with AI
              </Typography>
              <TextField
                label="Prompt"
                fullWidth
                multiline
                rows={3}
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                placeholder="Describe what you want to generate..."
                variant="outlined"
                margin="normal"
                error={!!error}
                helperText={error}
              />
              <Box display="flex" justifyContent="flex-end" mt={1}>
                <Button
                  variant="outlined"
                  onClick={() => setAiDialogOpen(false)}
                  sx={{ mr: 1 }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={generateWithAI}
                  disabled={generating || !aiPrompt}
                >
                  {generating ? 'Generating...' : 'Generate'}
                </Button>
              </Box>
            </Paper>
          </Collapse>
        </Box>
      </Collapse>
    </Box>
  );
};

export default ProposalSectionEditor;
