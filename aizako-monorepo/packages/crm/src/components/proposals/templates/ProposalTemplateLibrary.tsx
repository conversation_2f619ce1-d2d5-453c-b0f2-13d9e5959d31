import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  Menu,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Add as AddIcon,
  Description as DescriptionIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Category as CategoryIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Share as ShareIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ProposalTemplateService } from '../../../services/proposal-template-service';
import CreateTemplateDialog from './CreateTemplateDialog';
import { useAuth } from '../../../hooks/useAuth';

interface ProposalTemplateLibraryProps {
  tenantId: string;
  onSelectTemplate?: (templateId: string) => void;
  onCreateFromTemplate?: (templateId: string) => void;
  onViewTemplate?: (templateId: string) => void;
}

/**
 * ProposalTemplateLibrary Component
 * 
 * This component displays a library of proposal templates and allows
 * creating, editing, and using templates.
 */
const ProposalTemplateLibrary: React.FC<ProposalTemplateLibraryProps> = ({
  tenantId,
  onSelectTemplate,
  onCreateFromTemplate,
  onViewTemplate,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<any[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'all' | 'my' | 'shared' | 'starred'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  
  // Fetch templates
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await ProposalTemplateService.getTemplates(tenantId);
        setTemplates(response);
        
        // Extract categories
        const uniqueCategories = Array.from(
          new Set(response.map((template: any) => template.category))
        ).filter(Boolean) as string[];
        
        setCategories(uniqueCategories);
      } catch (err) {
        console.error('Error fetching templates:', err);
        setError('Failed to load templates. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTemplates();
  }, [tenantId]);
  
  // Filter templates
  useEffect(() => {
    let filtered = [...templates];
    
    // Filter by tab
    if (activeTab === 'my') {
      filtered = filtered.filter(template => template.createdBy === user?.id);
    } else if (activeTab === 'shared') {
      filtered = filtered.filter(template => template.isShared);
    } else if (activeTab === 'starred') {
      filtered = filtered.filter(template => template.isStarred);
    }
    
    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(template => template.category === categoryFilter);
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        template =>
          template.name.toLowerCase().includes(query) ||
          template.description.toLowerCase().includes(query)
      );
    }
    
    setFilteredTemplates(filtered);
  }, [templates, activeTab, categoryFilter, searchQuery, user?.id]);
  
  // Handle refresh
  const handleRefresh = () => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await ProposalTemplateService.getTemplates(tenantId);
        setTemplates(response);
        
        // Extract categories
        const uniqueCategories = Array.from(
          new Set(response.map((template: any) => template.category))
        ).filter(Boolean) as string[];
        
        setCategories(uniqueCategories);
      } catch (err) {
        console.error('Error fetching templates:', err);
        setError('Failed to load templates. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTemplates();
  };
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: 'all' | 'my' | 'shared' | 'starred') => {
    setActiveTab(newValue);
  };
  
  // Handle create template
  const handleCreateTemplate = async (templateData: any) => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalTemplateService.createTemplate({
        ...templateData,
        tenantId,
      });
      
      // Refresh templates
      handleRefresh();
      
      // Close dialog
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Error creating template:', err);
      setError('Failed to create template. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle edit template
  const handleEditTemplate = async (templateData: any) => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalTemplateService.updateTemplate(selectedTemplate._id, {
        ...templateData,
        tenantId,
      });
      
      // Refresh templates
      handleRefresh();
      
      // Close dialog
      setEditDialogOpen(false);
    } catch (err) {
      console.error('Error updating template:', err);
      setError('Failed to update template. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle delete template
  const handleDeleteTemplate = async (templateId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalTemplateService.deleteTemplate(templateId, tenantId);
      
      // Refresh templates
      handleRefresh();
      
      // Close menu
      setMenuAnchorEl(null);
    } catch (err) {
      console.error('Error deleting template:', err);
      setError('Failed to delete template. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle star template
  const handleStarTemplate = async (templateId: string, isStarred: boolean) => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalTemplateService.updateTemplate(templateId, {
        isStarred: !isStarred,
        tenantId,
      });
      
      // Refresh templates
      handleRefresh();
      
      // Close menu
      setMenuAnchorEl(null);
    } catch (err) {
      console.error('Error starring template:', err);
      setError('Failed to star template. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle share template
  const handleShareTemplate = async (templateId: string, isShared: boolean) => {
    try {
      setLoading(true);
      setError(null);
      
      await ProposalTemplateService.updateTemplate(templateId, {
        isShared: !isShared,
        tenantId,
      });
      
      // Refresh templates
      handleRefresh();
      
      // Close menu
      setMenuAnchorEl(null);
    } catch (err) {
      console.error('Error sharing template:', err);
      setError('Failed to share template. Please try again.');
      setLoading(false);
    }
  };
  
  // Handle menu open
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, templateId: string) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedTemplateId(templateId);
  };
  
  // Handle menu close
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedTemplateId(null);
  };
  
  // Render loading state
  if (loading && templates.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button 
          variant="outlined" 
          size="small" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
          sx={{ ml: 2 }}
        >
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Proposal Templates
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Create Template
          </Button>
        </Box>
      </Box>
      
      {/* Search and Filter */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
              }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth size="small">
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="Category"
                startAdornment={<CategoryIcon color="action" sx={{ mr: 1 }} />}
              >
                <MenuItem value="all">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>
      
      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant={isMobile ? "scrollable" : "fullWidth"}
          scrollButtons={isMobile ? "auto" : undefined}
        >
          <Tab label="All Templates" value="all" />
          <Tab label="My Templates" value="my" />
          <Tab label="Shared Templates" value="shared" />
          <Tab label="Starred Templates" value="starred" />
        </Tabs>
      </Paper>
      
      {/* Templates Grid */}
      {filteredTemplates.length === 0 ? (
        <Alert severity="info">
          No templates found. {activeTab !== 'all' && 'Try changing your filters or '} 
          <Button 
            color="primary" 
            size="small" 
            onClick={() => setCreateDialogOpen(true)}
          >
            create a new template
          </Button>.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {filteredTemplates.map((template) => (
            <Grid item xs={12} sm={6} md={4} key={template._id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                    <Box>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '200px' }}>
                        {template.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {template.description}
                      </Typography>
                    </Box>
                    <Box>
                      {template.isStarred && (
                        <StarIcon color="warning" />
                      )}
                    </Box>
                  </Box>
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Box display="flex" flexWrap="wrap" gap={0.5} mb={1}>
                    {template.category && (
                      <Chip 
                        label={template.category} 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                      />
                    )}
                    {template.isShared && (
                      <Chip 
                        icon={<ShareIcon />} 
                        label="Shared" 
                        size="small" 
                      />
                    )}
                    <Chip 
                      label={`${template.sections?.length || 0} Sections`} 
                      size="small" 
                    />
                  </Box>
                  
                  <Typography variant="caption" display="block" color="text.secondary">
                    Created {format(new Date(template.createdAt), 'PPP')}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    startIcon={<ViewIcon />}
                    onClick={() => onViewTemplate && onViewTemplate(template._id)}
                  >
                    View
                  </Button>
                  <Button 
                    size="small" 
                    startIcon={<CopyIcon />}
                    onClick={() => onCreateFromTemplate && onCreateFromTemplate(template._id)}
                  >
                    Use
                  </Button>
                  <Box flexGrow={1} />
                  <IconButton 
                    size="small"
                    onClick={(e) => handleMenuOpen(e, template._id)}
                  >
                    <MoreIcon />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
      
      {/* Template Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        {selectedTemplateId && (
          <>
            <MenuItem onClick={() => {
              const template = templates.find(t => t._id === selectedTemplateId);
              setSelectedTemplate(template);
              setEditDialogOpen(true);
              handleMenuClose();
            }}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Edit Template</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => {
              const template = templates.find(t => t._id === selectedTemplateId);
              handleStarTemplate(selectedTemplateId, template.isStarred);
            }}>
              <ListItemIcon>
                {templates.find(t => t._id === selectedTemplateId)?.isStarred ? (
                  <StarBorderIcon fontSize="small" />
                ) : (
                  <StarIcon fontSize="small" />
                )}
              </ListItemIcon>
              <ListItemText>
                {templates.find(t => t._id === selectedTemplateId)?.isStarred ? 'Unstar' : 'Star'} Template
              </ListItemText>
            </MenuItem>
            <MenuItem onClick={() => {
              const template = templates.find(t => t._id === selectedTemplateId);
              handleShareTemplate(selectedTemplateId, template.isShared);
            }}>
              <ListItemIcon>
                <ShareIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>
                {templates.find(t => t._id === selectedTemplateId)?.isShared ? 'Unshare' : 'Share'} Template
              </ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => handleDeleteTemplate(selectedTemplateId)}>
              <ListItemIcon>
                <DeleteIcon fontSize="small" color="error" />
              </ListItemIcon>
              <ListItemText sx={{ color: theme.palette.error.main }}>
                Delete Template
              </ListItemText>
            </MenuItem>
          </>
        )}
      </Menu>
      
      {/* Create Dialog */}
      <CreateTemplateDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateTemplate}
        categories={categories}
      />
      
      {/* Edit Dialog */}
      {selectedTemplate && (
        <CreateTemplateDialog
          open={editDialogOpen}
          onClose={() => setEditDialogOpen(false)}
          onSubmit={handleEditTemplate}
          categories={categories}
          initialData={selectedTemplate}
          isEditing
        />
      )}
    </Box>
  );
};

export default ProposalTemplateLibrary;
