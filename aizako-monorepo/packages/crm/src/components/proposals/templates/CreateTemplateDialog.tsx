import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Grid,
  FormControlLabel,
  Switch,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  Stepper,
  Step,
  StepLabel,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  ArrowUpward as MoveUpIcon,
  ArrowDownward as MoveDownIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Category as CategoryIcon,
  Share as ShareIcon,
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { v4 as uuidv4 } from 'uuid';
import RichTextEditor from '../../common/RichTextEditor';

interface CreateTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (templateData: any) => void;
  categories: string[];
  initialData?: any;
  isEditing?: boolean;
}

/**
 * CreateTemplateDialog Component
 * 
 * This component displays a dialog for creating or editing a proposal template.
 */
const CreateTemplateDialog: React.FC<CreateTemplateDialogProps> = ({
  open,
  onClose,
  onSubmit,
  categories,
  initialData,
  isEditing = false,
}) => {
  const theme = useTheme();
  
  // State
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [isShared, setIsShared] = useState(false);
  const [sections, setSections] = useState<Array<{
    id: string;
    title: string;
    type: string;
    content: string;
    order: number;
    isVisible: boolean;
  }>>([]);
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const [editingSectionTitle, setEditingSectionTitle] = useState('');
  const [editingSectionType, setEditingSectionType] = useState('text');
  const [editingSectionContent, setEditingSectionContent] = useState('');
  
  // Reset state when dialog opens or initialData changes
  useEffect(() => {
    if (open) {
      if (initialData) {
        setName(initialData.name || '');
        setDescription(initialData.description || '');
        setCategory(initialData.category || '');
        setIsShared(initialData.isShared || false);
        setSections(initialData.sections || []);
      } else {
        setName('');
        setDescription('');
        setCategory('');
        setNewCategory('');
        setIsShared(false);
        setSections([]);
      }
      
      setActiveStep(0);
      setLoading(false);
      setError(null);
      setEditingSectionId(null);
      setEditingSectionTitle('');
      setEditingSectionType('text');
      setEditingSectionContent('');
    }
  }, [open, initialData]);
  
  // Handle next step
  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };
  
  // Handle back step
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };
  
  // Handle submit
  const handleSubmit = () => {
    // Validate
    if (!name) {
      setError('Please enter a template name.');
      return;
    }
    
    if (sections.length === 0) {
      setError('Please add at least one section.');
      return;
    }
    
    // Prepare data
    const templateData = {
      name,
      description,
      category: category === 'new' ? newCategory : category,
      isShared,
      sections: sections.map((section, index) => ({
        ...section,
        order: index,
      })),
    };
    
    // Submit
    onSubmit(templateData);
  };
  
  // Handle add section
  const handleAddSection = () => {
    const newSection = {
      id: uuidv4(),
      title: 'New Section',
      type: 'text',
      content: '',
      order: sections.length,
      isVisible: true,
    };
    
    setSections([...sections, newSection]);
    setEditingSectionId(newSection.id);
    setEditingSectionTitle(newSection.title);
    setEditingSectionType(newSection.type);
    setEditingSectionContent(newSection.content);
  };
  
  // Handle edit section
  const handleEditSection = (sectionId: string) => {
    const section = sections.find((s) => s.id === sectionId);
    
    if (section) {
      setEditingSectionId(section.id);
      setEditingSectionTitle(section.title);
      setEditingSectionType(section.type);
      setEditingSectionContent(section.content);
    }
  };
  
  // Handle save section
  const handleSaveSection = () => {
    if (!editingSectionId) return;
    
    const updatedSections = sections.map((section) => {
      if (section.id === editingSectionId) {
        return {
          ...section,
          title: editingSectionTitle,
          type: editingSectionType,
          content: editingSectionContent,
        };
      }
      return section;
    });
    
    setSections(updatedSections);
    setEditingSectionId(null);
    setEditingSectionTitle('');
    setEditingSectionType('text');
    setEditingSectionContent('');
  };
  
  // Handle delete section
  const handleDeleteSection = (sectionId: string) => {
    setSections(sections.filter((section) => section.id !== sectionId));
    
    if (editingSectionId === sectionId) {
      setEditingSectionId(null);
      setEditingSectionTitle('');
      setEditingSectionType('text');
      setEditingSectionContent('');
    }
  };
  
  // Handle toggle section visibility
  const handleToggleSectionVisibility = (sectionId: string) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            isVisible: !section.isVisible,
          };
        }
        return section;
      })
    );
  };
  
  // Handle move section up
  const handleMoveSectionUp = (index: number) => {
    if (index === 0) return;
    
    const updatedSections = [...sections];
    const temp = updatedSections[index];
    updatedSections[index] = updatedSections[index - 1];
    updatedSections[index - 1] = temp;
    
    setSections(updatedSections);
  };
  
  // Handle move section down
  const handleMoveSectionDown = (index: number) => {
    if (index === sections.length - 1) return;
    
    const updatedSections = [...sections];
    const temp = updatedSections[index];
    updatedSections[index] = updatedSections[index + 1];
    updatedSections[index + 1] = temp;
    
    setSections(updatedSections);
  };
  
  // Handle drag end
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    setSections(items);
  };
  
  // Steps
  const steps = ['Basic Information', 'Sections', 'Review'];
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {isEditing ? 'Edit Template' : 'Create Template'}
      </DialogTitle>
      <DialogContent>
        {/* Stepper */}
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {/* Error */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {/* Step Content */}
        {activeStep === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            
            <TextField
              label="Template Name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              fullWidth
              margin="normal"
              required
            />
            
            <TextField
              label="Description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              fullWidth
              margin="normal"
              multiline
              rows={3}
            />
            
            <FormControl fullWidth margin="normal">
              <InputLabel>Category</InputLabel>
              <Select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                label="Category"
              >
                <MenuItem value="">
                  <em>No Category</em>
                </MenuItem>
                {categories.map((cat) => (
                  <MenuItem key={cat} value={cat}>
                    {cat}
                  </MenuItem>
                ))}
                <MenuItem value="new">
                  <em>+ Add New Category</em>
                </MenuItem>
              </Select>
            </FormControl>
            
            {category === 'new' && (
              <TextField
                label="New Category"
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                fullWidth
                margin="normal"
                required
              />
            )}
            
            <FormControlLabel
              control={
                <Switch
                  checked={isShared}
                  onChange={(e) => setIsShared(e.target.checked)}
                  color="primary"
                />
              }
              label="Share with team"
              sx={{ mt: 2 }}
            />
          </Box>
        )}
        
        {activeStep === 1 && (
          <Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Template Sections
              </Typography>
              <Button 
                variant="outlined" 
                startIcon={<AddIcon />}
                onClick={handleAddSection}
              >
                Add Section
              </Button>
            </Box>
            
            {sections.length === 0 ? (
              <Alert severity="info">
                No sections added yet. Click "Add Section" to create your first section.
              </Alert>
            ) : (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="sections">
                  {(provided) => (
                    <List 
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      disablePadding
                    >
                      {sections.map((section, index) => (
                        <Draggable key={section.id} draggableId={section.id} index={index}>
                          {(provided) => (
                            <Paper
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              sx={{ mb: 2 }}
                            >
                              <ListItem>
                                <Box {...provided.dragHandleProps} sx={{ mr: 1 }}>
                                  <DragIcon />
                                </Box>
                                <ListItemText
                                  primary={section.title}
                                  secondary={`Type: ${section.type}`}
                                />
                                <ListItemSecondaryAction>
                                  <Tooltip title={section.isVisible ? 'Visible' : 'Hidden'}>
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleToggleSectionVisibility(section.id)}
                                    >
                                      {section.isVisible ? (
                                        <VisibilityIcon />
                                      ) : (
                                        <VisibilityOffIcon />
                                      )}
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Move Up">
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleMoveSectionUp(index)}
                                      disabled={index === 0}
                                    >
                                      <MoveUpIcon />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Move Down">
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleMoveSectionDown(index)}
                                      disabled={index === sections.length - 1}
                                    >
                                      <MoveDownIcon />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleEditSection(section.id)}
                                    >
                                      <EditIcon />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleDeleteSection(section.id)}
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </Tooltip>
                                </ListItemSecondaryAction>
                              </ListItem>
                            </Paper>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </List>
                  )}
                </Droppable>
              </DragDropContext>
            )}
            
            {editingSectionId && (
              <Box mt={3}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Edit Section
                  </Typography>
                  
                  <TextField
                    label="Section Title"
                    value={editingSectionTitle}
                    onChange={(e) => setEditingSectionTitle(e.target.value)}
                    fullWidth
                    margin="normal"
                    required
                  />
                  
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Section Type</InputLabel>
                    <Select
                      value={editingSectionType}
                      onChange={(e) => setEditingSectionType(e.target.value)}
                      label="Section Type"
                    >
                      <MenuItem value="text">Text</MenuItem>
                      <MenuItem value="pricing">Pricing</MenuItem>
                      <MenuItem value="timeline">Timeline</MenuItem>
                      <MenuItem value="team">Team</MenuItem>
                      <MenuItem value="testimonials">Testimonials</MenuItem>
                      <MenuItem value="images">Images</MenuItem>
                      <MenuItem value="custom">Custom</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <Box mt={2} mb={2}>
                    <Typography variant="subtitle2" gutterBottom>
                      Section Content
                    </Typography>
                    <RichTextEditor
                      value={editingSectionContent}
                      onChange={setEditingSectionContent}
                      placeholder="Enter section content..."
                    />
                  </Box>
                  
                  <Box display="flex" justifyContent="flex-end" gap={1}>
                    <Button 
                      onClick={() => {
                        setEditingSectionId(null);
                        setEditingSectionTitle('');
                        setEditingSectionType('text');
                        setEditingSectionContent('');
                      }}
                    >
                      Cancel
                    </Button>
                    <Button 
                      variant="contained"
                      onClick={handleSaveSection}
                    >
                      Save Section
                    </Button>
                  </Box>
                </Paper>
              </Box>
            )}
          </Box>
        )}
        
        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Template
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Template Details
                </Typography>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Name
                  </Typography>
                  <Typography variant="body1">
                    {name}
                  </Typography>
                </Box>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Description
                  </Typography>
                  <Typography variant="body1">
                    {description || 'No description'}
                  </Typography>
                </Box>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Category
                  </Typography>
                  <Typography variant="body1">
                    {category === 'new' ? newCategory : category || 'No category'}
                  </Typography>
                </Box>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Sharing
                  </Typography>
                  <Typography variant="body1">
                    {isShared ? 'Shared with team' : 'Private'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Sections ({sections.length})
                </Typography>
                <List disablePadding>
                  {sections.map((section, index) => (
                    <ListItem key={section.id} divider={index < sections.length - 1}>
                      <ListItemText
                        primary={`${index + 1}. ${section.title}`}
                        secondary={`Type: ${section.type} | ${section.isVisible ? 'Visible' : 'Hidden'}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </Grid>
            
            <Alert severity="info" sx={{ mt: 3 }}>
              <Typography variant="body2">
                This template will be available for creating new proposals. You can edit it later if needed.
              </Typography>
            </Alert>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {activeStep > 0 && (
          <Button onClick={handleBack}>
            Back
          </Button>
        )}
        {activeStep < steps.length - 1 ? (
          <Button 
            variant="contained" 
            onClick={handleNext}
            disabled={activeStep === 0 && !name}
          >
            Next
          </Button>
        ) : (
          <Button 
            variant="contained" 
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : (isEditing ? 'Save Template' : 'Create Template')}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CreateTemplateDialog;
