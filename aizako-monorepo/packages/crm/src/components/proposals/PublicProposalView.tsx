import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  useTheme,
  alpha,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Snackbar
} from '@mui/material';
import {
  Download as DownloadIcon,
  ThumbUp as AcceptIcon,
  ThumbDown as RejectIcon,
  Share as ShareIcon,
  Print as PrintIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { proposalsApi } from '../../api/client/proposals';
import { IProposal, ProposalDownloadOptions } from '../../types/proposals';
import { formatCurrency, formatDate } from '../../utils/formatters';
import { sanitizeHtml } from '../../utils/sanitize';

/**
 * Public Proposal View Component
 * 
 * This component provides a public view of a proposal.
 * It allows recipients to view, download, accept, or reject the proposal.
 */
const PublicProposalView: React.FC = () => {
  const theme = useTheme();
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  
  // State for the proposal
  const [proposal, setProposal] = useState<IProposal | null>(null);
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [downloadMenuAnchor, setDownloadMenuAnchor] = useState<null | HTMLElement>(null);
  const [shareMenuAnchor, setShareMenuAnchor] = useState<null | HTMLElement>(null);
  const [acceptDialogOpen, setAcceptDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [notification, setNotification] = useState<{ message: string; severity: 'success' | 'error' } | null>(null);
  
  // Load proposal
  useEffect(() => {
    const loadProposal = async () => {
      if (!token) return;
      
      setLoading(true);
      try {
        const loadedProposal = await proposalsApi.getProposalByToken(token);
        setProposal(loadedProposal);
        
        // Record view
        await proposalsApi.recordView(token, {
          referrer: document.referrer,
          userAgent: navigator.userAgent,
          screenSize: `${window.innerWidth}x${window.innerHeight}`,
          timestamp: new Date().toISOString()
        });
      } catch (err) {
        console.error('Error loading proposal:', err);
        setError('The proposal could not be found or has expired.');
      } finally {
        setLoading(false);
      }
    };
    
    loadProposal();
  }, [token]);
  
  // Handle download
  const handleDownload = async (format: 'pdf' | 'docx' | 'md') => {
    setDownloadMenuAnchor(null);
    
    if (!token || !proposal) return;
    
    try {
      setLoading(true);
      
      const options: ProposalDownloadOptions = {
        format,
        paperSize: 'a4',
        colorScheme: 'professional',
        includeHeader: true,
        includeFooter: true,
        includePageNumbers: true
      };
      
      const blob = await proposalsApi.downloadPublicProposal(token, options);
      
      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${proposal.title || 'proposal'}.${format}`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      setNotification({
        message: `Proposal downloaded as ${format.toUpperCase()}`,
        severity: 'success'
      });
    } catch (err) {
      console.error(`Failed to download proposal as ${format.toUpperCase()}:`, err);
      setNotification({
        message: `Failed to download proposal as ${format.toUpperCase()}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Handle share
  const handleShare = (method: 'copy' | 'email') => {
    setShareMenuAnchor(null);
    
    if (!token) return;
    
    const url = window.location.href;
    
    if (method === 'copy') {
      navigator.clipboard.writeText(url);
      setNotification({
        message: 'Link copied to clipboard',
        severity: 'success'
      });
    } else if (method === 'email') {
      const subject = encodeURIComponent(`Proposal: ${proposal?.title || 'Shared Proposal'}`);
      const body = encodeURIComponent(`I'd like to share this proposal with you: ${url}`);
      window.location.href = `mailto:?subject=${subject}&body=${body}`;
    }
  };
  
  // Handle print
  const handlePrint = () => {
    window.print();
  };
  
  // Handle accept
  const handleAccept = async () => {
    if (!token || !proposal) return;
    
    try {
      setLoading(true);
      await proposalsApi.acceptProposal(proposal._id.toString());
      
      setAcceptDialogOpen(false);
      setNotification({
        message: 'Proposal accepted successfully',
        severity: 'success'
      });
      
      // Reload the proposal
      const updatedProposal = await proposalsApi.getProposalByToken(token);
      setProposal(updatedProposal);
    } catch (err) {
      console.error('Error accepting proposal:', err);
      setNotification({
        message: 'Failed to accept proposal',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Handle reject
  const handleReject = async () => {
    if (!token || !proposal) return;
    
    try {
      setLoading(true);
      await proposalsApi.rejectProposal(proposal._id.toString(), rejectionReason);
      
      setRejectDialogOpen(false);
      setRejectionReason('');
      setNotification({
        message: 'Proposal rejected',
        severity: 'success'
      });
      
      // Reload the proposal
      const updatedProposal = await proposalsApi.getProposalByToken(token);
      setProposal(updatedProposal);
    } catch (err) {
      console.error('Error rejecting proposal:', err);
      setNotification({
        message: 'Failed to reject proposal',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Render HTML content safely
  const renderHtml = (html: string) => {
    return <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(html) }} />;
  };
  
  // Render loading state
  if (loading && !proposal) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error || !proposal) {
    return (
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" color="error" gutterBottom>
            Proposal Not Found
          </Typography>
          <Typography variant="body1" paragraph>
            {error || 'The proposal you are looking for could not be found or has expired.'}
          </Typography>
          <Button variant="contained" onClick={() => navigate('/')}>
            Return Home
          </Button>
        </Paper>
      </Container>
    );
  }
  
  return (
    <Box>
      {/* Header */}
      <AppBar position="static" color="default" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {proposal.title}
          </Typography>
          
          <Box>
            {proposal.downloadEnabled && (
              <IconButton
                color="inherit"
                onClick={(e) => setDownloadMenuAnchor(e.currentTarget)}
              >
                <DownloadIcon />
              </IconButton>
            )}
            
            <IconButton
              color="inherit"
              onClick={(e) => setShareMenuAnchor(e.currentTarget)}
            >
              <ShareIcon />
            </IconButton>
            
            <IconButton
              color="inherit"
              onClick={handlePrint}
            >
              <PrintIcon />
            </IconButton>
            
            {proposal.status !== 'accepted' && proposal.status !== 'rejected' && (
              <>
                <IconButton
                  color="primary"
                  onClick={() => setAcceptDialogOpen(true)}
                >
                  <AcceptIcon />
                </IconButton>
                
                <IconButton
                  color="error"
                  onClick={() => setRejectDialogOpen(true)}
                >
                  <RejectIcon />
                </IconButton>
              </>
            )}
          </Box>
        </Toolbar>
      </AppBar>
      
      {/* Status Banner */}
      {(proposal.status === 'accepted' || proposal.status === 'rejected') && (
        <Box
          sx={{
            bgcolor: proposal.status === 'accepted' ? 'success.main' : 'error.main',
            color: 'white',
            py: 1,
            textAlign: 'center',
          }}
        >
          <Typography variant="subtitle1">
            {proposal.status === 'accepted' ? 'This proposal has been accepted' : 'This proposal has been rejected'}
            {proposal.status === 'accepted' && proposal.acceptedAt && (
              <> on {formatDate(proposal.acceptedAt)}</>
            )}
            {proposal.status === 'rejected' && proposal.rejectedAt && (
              <> on {formatDate(proposal.rejectedAt)}</>
            )}
          </Typography>
        </Box>
      )}
      
      {/* Content */}
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper sx={{ p: 4, mb: 4 }}>
          {/* Header */}
          <Box
            sx={{
              borderBottom: `4px solid ${theme.palette.primary.main}`,
              pb: 2,
              mb: 4,
            }}
          >
            <Typography
              variant="h4"
              component="h1"
              gutterBottom
              sx={{ color: theme.palette.primary.main, fontWeight: 'bold' }}
            >
              {proposal.title}
            </Typography>
            
            {proposal.description && (
              <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                {proposal.description}
              </Typography>
            )}
            
            <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
              <Chip
                label={proposal.status?.toUpperCase() || 'DRAFT'}
                color={
                  proposal.status === 'accepted' ? 'success' :
                  proposal.status === 'rejected' ? 'error' :
                  proposal.status === 'sent' ? 'primary' :
                  proposal.status === 'viewed' ? 'info' :
                  'default'
                }
                size="small"
              />
              
              <Typography variant="body2" color="text.secondary">
                {formatDate(proposal.createdAt)}
              </Typography>
            </Box>
          </Box>
          
          {/* Sections */}
          {proposal.sections
            ?.filter(section => section.isVisible)
            .sort((a, b) => a.order - b.order)
            .map((section, index) => (
              <Box key={section.id} mb={4}>
                <Typography
                  variant="h5"
                  component="h2"
                  gutterBottom
                  sx={{
                    color: theme.palette.primary.main,
                    borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                    pb: 1,
                  }}
                >
                  {section.title}
                </Typography>
                
                <Box sx={{ mt: 2 }}>
                  {renderHtml(section.content)}
                </Box>
              </Box>
            ))}
          
          {/* Pricing */}
          {proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (
            <Box mb={4}>
              <Typography
                variant="h5"
                component="h2"
                gutterBottom
                sx={{
                  color: theme.palette.primary.main,
                  borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                  pb: 1,
                }}
              >
                Pricing
              </Typography>
              
              <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
                <Table>
                  <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold' }}>Item</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Quantity</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Unit Price</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {proposal.pricing.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {item.name}
                          </Typography>
                          {item.description && (
                            <Typography variant="caption" color="text.secondary">
                              {item.description}
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell align="right">
                          {formatCurrency(item.unitPrice, proposal.pricing?.currency)}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(item.total, proposal.pricing?.currency)}
                        </TableCell>
                      </TableRow>
                    ))}
                    
                    {/* Subtotal */}
                    <TableRow>
                      <TableCell colSpan={3} align="right" sx={{ fontWeight: 'medium' }}>
                        Subtotal
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(proposal.pricing.subtotal, proposal.pricing.currency)}
                      </TableCell>
                    </TableRow>
                    
                    {/* Discount */}
                    {proposal.pricing.discount && proposal.pricing.discount > 0 && (
                      <TableRow>
                        <TableCell colSpan={3} align="right" sx={{ fontWeight: 'medium' }}>
                          Discount
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(proposal.pricing.discount, proposal.pricing.currency)}
                        </TableCell>
                      </TableRow>
                    )}
                    
                    {/* Tax */}
                    {proposal.pricing.tax && proposal.pricing.tax > 0 && (
                      <TableRow>
                        <TableCell colSpan={3} align="right" sx={{ fontWeight: 'medium' }}>
                          Tax
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(proposal.pricing.tax, proposal.pricing.currency)}
                        </TableCell>
                      </TableRow>
                    )}
                    
                    {/* Total */}
                    <TableRow>
                      <TableCell 
                        colSpan={3} 
                        align="right" 
                        sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}
                      >
                        Total
                      </TableCell>
                      <TableCell 
                        align="right"
                        sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}
                      >
                        {formatCurrency(proposal.pricing.total, proposal.pricing.currency)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
          
          {/* Terms */}
          {proposal.terms && (
            <Box mb={4}>
              <Typography
                variant="h5"
                component="h2"
                gutterBottom
                sx={{
                  color: theme.palette.primary.main,
                  borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                  pb: 1,
                }}
              >
                Terms & Conditions
              </Typography>
              
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line', mt: 2 }}>
                {proposal.terms}
              </Typography>
            </Box>
          )}
          
          {/* Footer */}
          <Divider sx={{ mt: 4, mb: 2 }} />
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="caption" color="text.secondary">
              Generated on {formatDate(proposal.createdAt)}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Confidential
            </Typography>
          </Box>
        </Paper>
        
        {/* Action Buttons */}
        {proposal.status !== 'accepted' && proposal.status !== 'rejected' && (
          <Box display="flex" justifyContent="center" gap={2} mb={4}>
            <Button
              variant="contained"
              color="primary"
              size="large"
              startIcon={<AcceptIcon />}
              onClick={() => setAcceptDialogOpen(true)}
            >
              Accept Proposal
            </Button>
            
            <Button
              variant="outlined"
              color="error"
              size="large"
              startIcon={<RejectIcon />}
              onClick={() => setRejectDialogOpen(true)}
            >
              Decline Proposal
            </Button>
          </Box>
        )}
      </Container>
      
      {/* Download Menu */}
      <Menu
        anchorEl={downloadMenuAnchor}
        open={Boolean(downloadMenuAnchor)}
        onClose={() => setDownloadMenuAnchor(null)}
      >
        {proposal.downloadFormats?.includes('pdf') && (
          <MenuItem onClick={() => handleDownload('pdf')}>
            Download as PDF
          </MenuItem>
        )}
        {proposal.downloadFormats?.includes('docx') && (
          <MenuItem onClick={() => handleDownload('docx')}>
            Download as DOCX
          </MenuItem>
        )}
        {proposal.downloadFormats?.includes('md') && (
          <MenuItem onClick={() => handleDownload('md')}>
            Download as Markdown
          </MenuItem>
        )}
      </Menu>
      
      {/* Share Menu */}
      <Menu
        anchorEl={shareMenuAnchor}
        open={Boolean(shareMenuAnchor)}
        onClose={() => setShareMenuAnchor(null)}
      >
        <MenuItem onClick={() => handleShare('copy')}>
          Copy Link
        </MenuItem>
        <MenuItem onClick={() => handleShare('email')}>
          Share via Email
        </MenuItem>
      </Menu>
      
      {/* Accept Dialog */}
      <Dialog
        open={acceptDialogOpen}
        onClose={() => setAcceptDialogOpen(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Accept Proposal</DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Are you sure you want to accept this proposal?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            By accepting, you agree to the terms and conditions outlined in the proposal.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAcceptDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleAccept}
            disabled={loading}
          >
            {loading ? 'Processing...' : 'Accept'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Reject Dialog */}
      <Dialog
        open={rejectDialogOpen}
        onClose={() => setRejectDialogOpen(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Decline Proposal</DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Are you sure you want to decline this proposal?
          </Typography>
          <TextField
            label="Reason (Optional)"
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            fullWidth
            multiline
            rows={3}
            margin="normal"
            variant="outlined"
            placeholder="Please provide a reason for declining..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={handleReject}
            disabled={loading}
          >
            {loading ? 'Processing...' : 'Decline'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Notification */}
      <Snackbar
        open={!!notification}
        autoHideDuration={6000}
        onClose={() => setNotification(null)}
        message={notification?.message}
      >
        <Alert
          onClose={() => setNotification(null)}
          severity={notification?.severity}
          sx={{ width: '100%' }}
        >
          {notification?.message}
        </Alert>
      </Snackbar>
      
      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 9999,
          }}
        >
          <CircularProgress color="primary" />
        </Box>
      )}
    </Box>
  );
};

export default PublicProposalView;
