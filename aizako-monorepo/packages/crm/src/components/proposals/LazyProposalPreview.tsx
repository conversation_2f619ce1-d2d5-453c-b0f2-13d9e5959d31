import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  CircularProgress,
  useTheme,
  alpha,
  Skeleton,
  Button
} from '@mui/material';
import { IProposal, IProposalSection } from '../../types/proposals';
import { formatCurrency, formatDate } from '../../utils/formatters';
import { sanitizeHtml } from '../../utils/sanitize';

interface LazyProposalPreviewProps {
  proposal: IProposal;
  colorScheme?: string;
  viewMode?: 'desktop' | 'mobile' | 'print';
  sectionLoadCount?: number;
}

/**
 * LazyProposalPreview Component
 * 
 * This component renders a proposal preview with lazy loading for sections.
 * It only renders sections that are visible in the viewport to improve performance.
 */
const LazyProposalPreview: React.FC<LazyProposalPreviewProps> = ({
  proposal,
  colorScheme = 'professional',
  viewMode = 'desktop',
  sectionLoadCount = 3
}) => {
  const theme = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  
  // State for visible sections
  const [visibleSections, setVisibleSections] = useState<IProposalSection[]>([]);
  const [loadedSectionCount, setLoadedSectionCount] = useState(sectionLoadCount);
  const [isLoading, setIsLoading] = useState(false);
  
  // Get visible sections
  const getVisibleSections = useCallback(() => {
    if (!proposal.sections) return [];
    
    // Filter visible sections and sort by order
    const filteredSections = proposal.sections
      .filter(section => section.isVisible)
      .sort((a, b) => a.order - b.order);
    
    // Return only the loaded sections
    return filteredSections.slice(0, loadedSectionCount);
  }, [proposal.sections, loadedSectionCount]);
  
  // Update visible sections when proposal or loadedSectionCount changes
  useEffect(() => {
    setVisibleSections(getVisibleSections());
  }, [proposal, loadedSectionCount, getVisibleSections]);
  
  // Handle scroll to load more sections
  const handleScroll = useCallback(() => {
    if (!containerRef.current || isLoading) return;
    
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    const scrollPosition = scrollTop + clientHeight;
    const scrollThreshold = scrollHeight * 0.8; // 80% of scroll height
    
    if (scrollPosition >= scrollThreshold) {
      // Check if we have more sections to load
      const totalVisibleSections = proposal.sections
        .filter(section => section.isVisible)
        .length;
      
      if (loadedSectionCount < totalVisibleSections) {
        setIsLoading(true);
        
        // Simulate loading delay
        setTimeout(() => {
          setLoadedSectionCount(prev => prev + sectionLoadCount);
          setIsLoading(false);
        }, 500);
      }
    }
  }, [isLoading, loadedSectionCount, proposal.sections, sectionLoadCount]);
  
  // Add scroll event listener
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);
  
  // Get color scheme styles
  const getColorScheme = () => {
    switch (colorScheme) {
      case 'professional':
        return {
          primary: theme.palette.primary.main,
          secondary: theme.palette.grey[800],
          background: theme.palette.background.paper,
          text: theme.palette.text.primary,
          accent: theme.palette.primary.light,
        };
      case 'creative':
        return {
          primary: theme.palette.secondary.main,
          secondary: theme.palette.secondary.dark,
          background: theme.palette.background.paper,
          text: theme.palette.text.primary,
          accent: theme.palette.secondary.light,
        };
      case 'modern':
        return {
          primary: theme.palette.info.main,
          secondary: theme.palette.grey[800],
          background: theme.palette.background.paper,
          text: theme.palette.text.primary,
          accent: theme.palette.info.light,
        };
      case 'classic':
        return {
          primary: theme.palette.grey[800],
          secondary: theme.palette.grey[600],
          background: theme.palette.background.paper,
          text: theme.palette.text.primary,
          accent: theme.palette.grey[400],
        };
      default:
        return {
          primary: theme.palette.primary.main,
          secondary: theme.palette.grey[800],
          background: theme.palette.background.paper,
          text: theme.palette.text.primary,
          accent: theme.palette.primary.light,
        };
    }
  };
  
  const colors = getColorScheme();
  
  // Get container width based on view mode
  const getContainerWidth = () => {
    switch (viewMode) {
      case 'desktop':
        return '100%';
      case 'mobile':
        return '375px';
      case 'print':
        return '8.5in';
      default:
        return '100%';
    }
  };
  
  // Render HTML content safely
  const renderHtml = (html: string) => {
    return <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(html) }} />;
  };
  
  // Calculate total visible sections
  const totalVisibleSections = proposal.sections
    .filter(section => section.isVisible)
    .length;
  
  // Calculate remaining sections
  const remainingSections = totalVisibleSections - visibleSections.length;
  
  return (
    <Box
      ref={containerRef}
      sx={{
        width: getContainerWidth(),
        maxHeight: '80vh',
        overflowY: 'auto',
        mx: 'auto',
        boxShadow: viewMode === 'print' ? 'none' : 3,
        bgcolor: colors.background,
        p: { xs: 2, sm: 4 },
        '@media print': {
          width: '100%',
          maxHeight: 'none',
          overflow: 'visible',
          boxShadow: 'none',
          p: 0,
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          borderBottom: `4px solid ${colors.primary}`,
          pb: 2,
          mb: 4,
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          sx={{ color: colors.primary, fontWeight: 'bold' }}
        >
          {proposal.title}
        </Typography>
        
        {proposal.description && (
          <Typography variant="subtitle1" color="text.secondary" gutterBottom>
            {proposal.description}
          </Typography>
        )}
        
        <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
          <Typography variant="caption" color="text.secondary">
            {formatDate(proposal.createdAt)}
          </Typography>
          
          <Typography variant="caption" color="text.secondary">
            {proposal.status?.toUpperCase() || 'DRAFT'}
          </Typography>
        </Box>
      </Box>
      
      {/* Sections */}
      {visibleSections.map((section) => (
        <Box key={section.id} mb={4}>
          <Typography
            variant="h5"
            component="h2"
            gutterBottom
            sx={{
              color: colors.primary,
              borderBottom: `2px solid ${alpha(colors.primary, 0.3)}`,
              pb: 1,
            }}
          >
            {section.title}
          </Typography>
          
          <Box sx={{ mt: 2 }}>
            {renderHtml(section.content)}
          </Box>
        </Box>
      ))}
      
      {/* Loading indicator */}
      {isLoading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress color="primary" />
        </Box>
      )}
      
      {/* Load more button */}
      {remainingSections > 0 && !isLoading && (
        <Box display="flex" justifyContent="center" my={4}>
          <Button
            variant="outlined"
            onClick={() => {
              setIsLoading(true);
              setTimeout(() => {
                setLoadedSectionCount(prev => prev + sectionLoadCount);
                setIsLoading(false);
              }, 500);
            }}
          >
            Load {Math.min(remainingSections, sectionLoadCount)} more sections ({remainingSections} remaining)
          </Button>
        </Box>
      )}
      
      {/* Pricing */}
      {proposal.pricing && proposal.pricing.items && proposal.pricing.items.length > 0 && (
        <Box mb={4}>
          <Typography
            variant="h5"
            component="h2"
            gutterBottom
            sx={{
              color: colors.primary,
              borderBottom: `2px solid ${alpha(colors.primary, 0.3)}`,
              pb: 1,
            }}
          >
            Pricing
          </Typography>
          
          <Paper variant="outlined" sx={{ mt: 2 }}>
            <Box p={2}>
              {proposal.pricing.items.map((item) => (
                <Box
                  key={item.id}
                  display="flex"
                  justifyContent="space-between"
                  py={1}
                  borderBottom={`1px solid ${theme.palette.divider}`}
                >
                  <Box>
                    <Typography variant="body1" fontWeight="medium">
                      {item.name}
                    </Typography>
                    {item.description && (
                      <Typography variant="body2" color="text.secondary">
                        {item.description}
                      </Typography>
                    )}
                  </Box>
                  <Box textAlign="right">
                    <Typography variant="body2" color="text.secondary">
                      {item.quantity} x {formatCurrency(item.unitPrice, proposal.pricing.currency)}
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {formatCurrency(item.total, proposal.pricing.currency)}
                    </Typography>
                  </Box>
                </Box>
              ))}
              
              <Box
                display="flex"
                justifyContent="space-between"
                py={2}
                mt={2}
                borderTop={`2px solid ${theme.palette.divider}`}
              >
                <Typography variant="h6">Total</Typography>
                <Typography variant="h6" color={colors.primary}>
                  {formatCurrency(proposal.pricing.total, proposal.pricing.currency)}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Box>
      )}
      
      {/* Terms */}
      {proposal.terms && (
        <Box mb={4}>
          <Typography
            variant="h5"
            component="h2"
            gutterBottom
            sx={{
              color: colors.primary,
              borderBottom: `2px solid ${alpha(colors.primary, 0.3)}`,
              pb: 1,
            }}
          >
            Terms & Conditions
          </Typography>
          
          <Typography variant="body2" sx={{ whiteSpace: 'pre-line', mt: 2 }}>
            {proposal.terms}
          </Typography>
        </Box>
      )}
      
      {/* Footer */}
      <Divider sx={{ mt: 4, mb: 2 }} />
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="caption" color="text.secondary">
          Generated on {formatDate(proposal.createdAt)}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Confidential
        </Typography>
      </Box>
    </Box>
  );
};

export default LazyProposalPreview;
