import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Textarea, Select, Checkbox, Spinner } from '@aizako/ui-kit';
import { IOpportunity } from '../../models/opportunity';
import { ICompany } from '../../models/company';
import { IContact } from '../../models/contact';

interface ProposalAIGeneratorProps {
  opportunity?: IOpportunity;
  company?: ICompany;
  contacts?: IContact[];
  onGenerate: (generatedContent: {
    title: string;
    description: string;
    sections: Array<{
      title: string;
      content: string;
      type: string;
      order: number;
    }>;
    pricing?: {
      items: Array<{
        name: string;
        description: string;
        quantity: number;
        unitPrice: number;
        total: number;
      }>;
    };
    terms: string;
    aiPrompt: string;
    aiModel: string;
  }) => void;
}

/**
 * AI-powered proposal generator component
 */
export const ProposalAIGenerator: React.FC<ProposalAIGeneratorProps> = ({
  opportunity,
  company,
  contacts,
  onGenerate,
}) => {
  const [prompt, setPrompt] = useState('');
  const [model, setModel] = useState('claude-3-opus');
  const [isGenerating, setIsGenerating] = useState(false);
  const [includeExecutiveSummary, setIncludeExecutiveSummary] = useState(true);
  const [includeSolution, setIncludeSolution] = useState(true);
  const [includeTimeline, setIncludeTimeline] = useState(true);
  const [includePricing, setIncludePricing] = useState(true);
  const [includeTeam, setIncludeTeam] = useState(false);
  const [includeTestimonials, setIncludeTestimonials] = useState(false);
  const [includeTerms, setIncludeTerms] = useState(true);
  
  // Generate a default prompt based on opportunity, company, and contacts
  const generateDefaultPrompt = () => {
    let defaultPrompt = 'Generate a professional business proposal';
    
    if (opportunity) {
      defaultPrompt += ` for the opportunity "${opportunity.name}"`;
      
      if (opportunity.description) {
        defaultPrompt += ` which involves ${opportunity.description}`;
      }
      
      if (opportunity.amount) {
        defaultPrompt += ` with an approximate value of $${opportunity.amount.toLocaleString()}`;
      }
    }
    
    if (company) {
      defaultPrompt += ` for ${company.name}`;
      
      if (company.industry) {
        defaultPrompt += ` in the ${company.industry} industry`;
      }
    }
    
    defaultPrompt += '. Include the following sections:';
    
    if (includeExecutiveSummary) {
      defaultPrompt += '\n- Executive Summary';
    }
    
    if (includeSolution) {
      defaultPrompt += '\n- Proposed Solution';
    }
    
    if (includeTimeline) {
      defaultPrompt += '\n- Project Timeline';
    }
    
    if (includePricing) {
      defaultPrompt += '\n- Pricing and Investment';
    }
    
    if (includeTeam) {
      defaultPrompt += '\n- Our Team';
    }
    
    if (includeTestimonials) {
      defaultPrompt += '\n- Client Testimonials';
    }
    
    if (includeTerms) {
      defaultPrompt += '\n- Terms and Conditions';
    }
    
    defaultPrompt += '\n\nMake the proposal persuasive, professional, and tailored to the client\'s needs.';
    
    return defaultPrompt;
  };
  
  // Handle prompt generation
  const handleGeneratePrompt = () => {
    setPrompt(generateDefaultPrompt());
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!prompt) {
      return;
    }
    
    setIsGenerating(true);
    
    try {
      // This would be replaced with an actual API call to generate content
      // For now, we'll just simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock generated content
      const generatedContent = {
        title: opportunity ? `Proposal: ${opportunity.name}` : 'Business Proposal',
        description: 'A comprehensive solution tailored to your specific needs and objectives.',
        sections: [
          {
            title: 'Executive Summary',
            content: 'This proposal outlines our comprehensive solution designed to address your specific challenges and help you achieve your business objectives. Based on our understanding of your requirements, we have developed a tailored approach that leverages our expertise and proven methodologies to deliver exceptional results.',
            type: 'text',
            order: 0,
          },
          {
            title: 'Proposed Solution',
            content: 'Our solution combines innovative technology with industry best practices to provide a scalable, efficient, and effective approach to addressing your needs. We will implement a phased approach that minimizes disruption while maximizing value at each stage of the project.',
            type: 'text',
            order: 1,
          },
          {
            title: 'Project Timeline',
            content: 'The project will be executed in three phases over a 12-week period:\n\nPhase 1 (Weeks 1-4): Discovery and Planning\n- Requirements gathering and analysis\n- Solution design and architecture\n- Project plan finalization\n\nPhase 2 (Weeks 5-10): Implementation\n- Development and configuration\n- Integration with existing systems\n- Quality assurance and testing\n\nPhase 3 (Weeks 11-12): Deployment and Training\n- System deployment\n- User training and documentation\n- Post-implementation support',
            type: 'timeline',
            order: 2,
          },
        ],
        pricing: {
          items: [
            {
              name: 'Discovery and Planning',
              description: 'Requirements gathering, solution design, and project planning',
              quantity: 1,
              unitPrice: 5000,
              total: 5000,
            },
            {
              name: 'Implementation',
              description: 'Development, configuration, integration, and testing',
              quantity: 1,
              unitPrice: 15000,
              total: 15000,
            },
            {
              name: 'Deployment and Training',
              description: 'System deployment, user training, and documentation',
              quantity: 1,
              unitPrice: 5000,
              total: 5000,
            },
            {
              name: 'Monthly Support',
              description: 'Ongoing technical support and maintenance',
              quantity: 12,
              unitPrice: 1000,
              total: 12000,
            },
          ],
        },
        terms: 'Payment Terms:\n- 50% due upon project initiation\n- 25% due upon completion of Phase 2\n- 25% due upon project completion\n\nProject Timeline:\n- Project will commence within 2 weeks of proposal acceptance\n- Estimated completion time is 12 weeks from project start date\n\nWarranty:\n- 90-day warranty on all deliverables\n- Support included for 12 months from project completion',
        aiPrompt: prompt,
        aiModel: model,
      };
      
      onGenerate(generatedContent);
    } catch (error) {
      console.error('Error generating proposal:', error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">AI Proposal Generator</h2>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="model" className="block text-sm font-medium mb-1">AI Model</label>
            <Select
              id="model"
              value={model}
              onChange={(e) => setModel(e.target.value)}
              className="w-full"
            >
              <option value="claude-3-opus">Claude 3 Opus (Most Powerful)</option>
              <option value="claude-3-sonnet">Claude 3 Sonnet (Balanced)</option>
              <option value="claude-3-haiku">Claude 3 Haiku (Fastest)</option>
            </Select>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-md font-medium">Include Sections</h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <div className="flex items-center">
                <Checkbox
                  id="includeExecutiveSummary"
                  checked={includeExecutiveSummary}
                  onChange={(e) => setIncludeExecutiveSummary(e.target.checked)}
                />
                <label htmlFor="includeExecutiveSummary" className="ml-2 text-sm">
                  Executive Summary
                </label>
              </div>
              
              <div className="flex items-center">
                <Checkbox
                  id="includeSolution"
                  checked={includeSolution}
                  onChange={(e) => setIncludeSolution(e.target.checked)}
                />
                <label htmlFor="includeSolution" className="ml-2 text-sm">
                  Proposed Solution
                </label>
              </div>
              
              <div className="flex items-center">
                <Checkbox
                  id="includeTimeline"
                  checked={includeTimeline}
                  onChange={(e) => setIncludeTimeline(e.target.checked)}
                />
                <label htmlFor="includeTimeline" className="ml-2 text-sm">
                  Project Timeline
                </label>
              </div>
              
              <div className="flex items-center">
                <Checkbox
                  id="includePricing"
                  checked={includePricing}
                  onChange={(e) => setIncludePricing(e.target.checked)}
                />
                <label htmlFor="includePricing" className="ml-2 text-sm">
                  Pricing
                </label>
              </div>
              
              <div className="flex items-center">
                <Checkbox
                  id="includeTeam"
                  checked={includeTeam}
                  onChange={(e) => setIncludeTeam(e.target.checked)}
                />
                <label htmlFor="includeTeam" className="ml-2 text-sm">
                  Team Members
                </label>
              </div>
              
              <div className="flex items-center">
                <Checkbox
                  id="includeTestimonials"
                  checked={includeTestimonials}
                  onChange={(e) => setIncludeTestimonials(e.target.checked)}
                />
                <label htmlFor="includeTestimonials" className="ml-2 text-sm">
                  Client Testimonials
                </label>
              </div>
              
              <div className="flex items-center">
                <Checkbox
                  id="includeTerms"
                  checked={includeTerms}
                  onChange={(e) => setIncludeTerms(e.target.checked)}
                />
                <label htmlFor="includeTerms" className="ml-2 text-sm">
                  Terms & Conditions
                </label>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={handleGeneratePrompt}
            >
              Generate Default Prompt
            </Button>
          </div>
          
          <div>
            <label htmlFor="prompt" className="block text-sm font-medium mb-1">
              AI Prompt
              <span className="text-xs text-gray-500 ml-2">
                (Describe the proposal you want to generate)
              </span>
            </label>
            <Textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={8}
              required
              placeholder="Generate a professional business proposal for..."
            />
          </div>
          
          {opportunity && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <h4 className="text-sm font-medium text-blue-800">Opportunity Context</h4>
              <p className="text-xs text-blue-700 mt-1">
                Name: {opportunity.name}<br />
                {opportunity.description && <>Description: {opportunity.description}<br /></>}
                {opportunity.amount && <>Value: ${opportunity.amount.toLocaleString()}<br /></>}
                {opportunity.stage && <>Stage: {opportunity.stage}<br /></>}
              </p>
            </div>
          )}
          
          {company && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <h4 className="text-sm font-medium text-green-800">Company Context</h4>
              <p className="text-xs text-green-700 mt-1">
                Name: {company.name}<br />
                {company.industry && <>Industry: {company.industry}<br /></>}
                {company.size && <>Size: {company.size}<br /></>}
                {company.website && <>Website: {company.website}<br /></>}
              </p>
            </div>
          )}
        </div>
      </Card>
      
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={isGenerating || !prompt}
        >
          {isGenerating ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Generating Proposal...
            </>
          ) : (
            'Generate Proposal'
          )}
        </Button>
      </div>
    </form>
  );
};

export default ProposalAIGenerator;
