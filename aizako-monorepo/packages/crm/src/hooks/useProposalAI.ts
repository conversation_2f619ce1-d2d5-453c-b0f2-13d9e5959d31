import { useState, useCallback } from 'react';
import { useTenant } from './useTenant';
import { IProposal } from '../models/proposal';
import { CRM_API_ROUTES } from '../api';

interface UseProposalAIOptions {
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
}

interface GenerateProposalParams {
  prompt: string;
  model: string;
  includeSections: {
    executiveSummary: boolean;
    solution: boolean;
    timeline: boolean;
    pricing: boolean;
    team: boolean;
    testimonials: boolean;
    terms: boolean;
  };
}

interface UseProposalAIResult {
  generateProposal: (params: GenerateProposalParams) => Promise<Partial<IProposal>>;
  generateProposalSection: (sectionType: string, prompt: string, model: string) => Promise<{
    title: string;
    content: string;
    type: string;
  }>;
  isGenerating: boolean;
  error: Error | null;
}

/**
 * Hook for AI proposal generation
 */
export function useProposalAI(options: UseProposalAIOptions = {}): UseProposalAIResult {
  const { tenant } = useTenant();
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const { opportunityId, companyId, contactIds } = options;

  // Generate a complete proposal with AI
  const generateProposal = useCallback(async (params: GenerateProposalParams): Promise<Partial<IProposal>> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch(`${CRM_API_ROUTES.PROPOSALS}/ai/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': tenant.id,
        },
        body: JSON.stringify({
          prompt: params.prompt,
          model: params.model,
          includeSections: params.includeSections,
          opportunityId,
          companyId,
          contactIds,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate proposal: ${response.statusText}`);
      }

      const generatedProposal = await response.json();
      return generatedProposal;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      console.error('Error generating proposal with AI:', err);
      throw err;
    } finally {
      setIsGenerating(false);
    }
  }, [tenant, opportunityId, companyId, contactIds]);

  // Generate a single proposal section with AI
  const generateProposalSection = useCallback(async (
    sectionType: string,
    prompt: string,
    model: string
  ): Promise<{ title: string; content: string; type: string }> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch(`${CRM_API_ROUTES.PROPOSALS}/ai/generate-section`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': tenant.id,
        },
        body: JSON.stringify({
          sectionType,
          prompt,
          model,
          opportunityId,
          companyId,
          contactIds,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate section: ${response.statusText}`);
      }

      const generatedSection = await response.json();
      return generatedSection;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      console.error('Error generating section with AI:', err);
      throw err;
    } finally {
      setIsGenerating(false);
    }
  }, [tenant, opportunityId, companyId, contactIds]);

  return {
    generateProposal,
    generateProposalSection,
    isGenerating,
    error,
  };
}

export default useProposalAI;
