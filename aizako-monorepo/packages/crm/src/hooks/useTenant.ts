import { useState, useEffect, useCallback } from 'react';
import { getTenantId, setTenantId } from '../utils/auth';

export interface Tenant {
  id: string;
  name: string;
  domain?: string;
  plan?: string;
  features?: string[];
}

interface UseTenantResult {
  tenant: Tenant | null;
  setTenant: (tenant: Tenant) => void;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook for managing tenant information
 */
export function useTenant(): UseTenantResult {
  const [tenant, setTenantState] = useState<Tenant | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Load tenant from local storage on mount
  useEffect(() => {
    const loadTenant = async () => {
      try {
        const tenantId = getTenantId();
        
        if (!tenantId) {
          setIsLoading(false);
          return;
        }

        // In a real implementation, we would fetch tenant details from the API
        // For now, we'll just create a simple tenant object
        setTenantState({
          id: tenantId,
          name: 'Demo Tenant',
        });
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load tenant'));
        console.error('Error loading tenant:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadTenant();
  }, []);

  // Set tenant and save to local storage
  const setTenant = useCallback((newTenant: Tenant) => {
    setTenantState(newTenant);
    setTenantId(newTenant.id);
  }, []);

  return {
    tenant,
    setTenant,
    isLoading,
    error,
  };
}

export default useTenant;
