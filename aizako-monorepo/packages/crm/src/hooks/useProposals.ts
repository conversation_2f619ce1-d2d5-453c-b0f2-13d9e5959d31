import { useState, useEffect, useCallback } from 'react';
import { useTenant } from './useTenant';
import * as ProposalTypes from '../types/proposals';
import { proposalsApi } from '../api/client/proposals';
import { ApiError } from '../api/client/api-client';

// Use namespaced types
type IProposal = ProposalTypes.Types.IProposal;
type CreateProposalRequest = ProposalTypes.Types.CreateProposalRequest;
type UpdateProposalRequest = ProposalTypes.Types.UpdateProposalRequest;
type ProposalListResponse = ProposalTypes.Types.ProposalListResponse;

// Define custom types for options
type ProposalSendOptions = {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  message: string;
  includeLink?: boolean;
  includeAttachment?: boolean;
  attachmentFormat?: 'pdf' | 'docx' | 'md';
  expiresAt?: Date;
};

type ProposalDownloadOptions = {
  format?: 'pdf' | 'docx' | 'md';
  includeHeader?: boolean;
  includeFooter?: boolean;
  includeBranding?: boolean;
  includePageNumbers?: boolean;
  colorScheme?: string;
  paperSize?: 'a4' | 'letter' | 'legal';
  skipCache?: 'true' | 'false';
};

interface UseProposalsOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: string;
  opportunityId?: string;
  companyId?: string;
  contactId?: string;
  search?: string;
}

/**
 * Result of the useProposals hook
 */
interface UseProposalsResult {
  /**
   * List of proposals
   */
  proposals: IProposal[];

  /**
   * Total number of proposals
   */
  total: number;

  /**
   * Current page
   */
  page: number;

  /**
   * Items per page
   */
  limit: number;

  /**
   * Whether proposals are being loaded
   */
  isLoading: boolean;

  /**
   * Error if any
   */
  error: Error | null;

  /**
   * Refetch proposals
   */
  refetch: () => Promise<void>;

  /**
   * Create a new proposal
   * @param proposalData Proposal data
   * @returns Created proposal
   */
  createProposal: (proposalData: CreateProposalRequest) => Promise<IProposal>;

  /**
   * Update a proposal
   * @param id Proposal ID
   * @param proposalData Proposal data
   * @returns Updated proposal
   */
  updateProposal: (id: string, proposalData: UpdateProposalRequest) => Promise<IProposal>;

  /**
   * Delete a proposal
   * @param id Proposal ID
   * @returns Success status
   */
  deleteProposal: (id: string) => Promise<boolean>;

  /**
   * Get a proposal by ID
   * @param id Proposal ID
   * @returns Proposal
   */
  getProposalById: (id: string) => Promise<IProposal>;

  /**
   * Send a proposal
   * @param id Proposal ID
   * @param options Send options
   * @returns Updated proposal
   */
  sendProposal: (id: string, options: ProposalSendOptions) => Promise<IProposal>;

  /**
   * Download a proposal
   * @param id Proposal ID
   * @param options Download options
   * @returns Blob data
   */
  downloadProposal: (id: string, options: ProposalDownloadOptions) => Promise<Blob>;
}

/**
 * Hook for managing proposals
 *
 * This hook provides methods for fetching, creating, updating, and deleting proposals.
 * It also provides methods for sending and downloading proposals.
 *
 * @param options Options for fetching proposals
 * @returns Proposal management methods and state
 *
 * @example
 * ```tsx
 * const {
 *   proposals,
 *   total,
 *   isLoading,
 *   createProposal
 * } = useProposals({
 *   page: 1,
 *   limit: 10
 * });
 * ```
 */
export function useProposals(options: UseProposalsOptions = {}): UseProposalsResult {
  const { tenant } = useTenant();
  const [proposals, setProposals] = useState<IProposal[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    status,
    opportunityId,
    companyId,
    contactId,
    search,
  } = options;

  // Set tenant ID for API client when tenant changes
  useEffect(() => {
    if (tenant) {
      proposalsApi.setTenantId(tenant.id);
    }
  }, [tenant]);

  // Fetch proposals
  const fetchProposals = useCallback(async () => {
    if (!tenant) return;

    setIsLoading(true);
    setError(null);

    try {
      // Use the API client to fetch proposals
      const result = await proposalsApi.getProposals({
        page,
        limit,
        sortBy,
        sortOrder,
        status,
        opportunityId,
        companyId,
        contactId: contactId,
        search,
      });

      setProposals(result.data);
      setTotal(result.total);
    } catch (err) {
      const apiError = err instanceof ApiError
        ? new Error(err.message)
        : err instanceof Error
          ? err
          : new Error('An unknown error occurred');

      setError(apiError);
      console.error('Error fetching proposals:', err);
    } finally {
      setIsLoading(false);
    }
  }, [tenant, page, limit, sortBy, sortOrder, status, opportunityId, companyId, contactId, search]);

  // Fetch proposals on mount and when dependencies change
  useEffect(() => {
    fetchProposals();
  }, [fetchProposals]);

  // Create a new proposal
  const createProposal = async (proposalData: CreateProposalRequest): Promise<IProposal> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    try {
      // Use the API client to create a proposal
      const newProposal = await proposalsApi.createProposal(proposalData);

      // Refetch proposals to update the list
      fetchProposals();

      return newProposal;
    } catch (err) {
      console.error('Error creating proposal:', err);
      throw err;
    }
  };

  // Update a proposal
  const updateProposal = async (id: string, proposalData: UpdateProposalRequest): Promise<IProposal> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    try {
      // Use the API client to update a proposal
      const updatedProposal = await proposalsApi.updateProposal(id, proposalData);

      // Refetch proposals to update the list
      fetchProposals();

      return updatedProposal;
    } catch (err) {
      console.error('Error updating proposal:', err);
      throw err;
    }
  };

  // Delete a proposal
  const deleteProposal = async (id: string): Promise<boolean> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    try {
      // Use the API client to delete a proposal
      await proposalsApi.deleteProposal(id);

      // Refetch proposals to update the list
      fetchProposals();

      return true;
    } catch (err) {
      console.error('Error deleting proposal:', err);
      throw err;
    }
  };

  // Get a proposal by ID
  const getProposalById = async (id: string): Promise<IProposal> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    try {
      // Use the API client to get a proposal by ID
      return await proposalsApi.getProposalById(id);
    } catch (err) {
      console.error('Error getting proposal:', err);
      throw err;
    }
  };

  // Send a proposal
  const sendProposal = async (id: string, options: ProposalSendOptions): Promise<IProposal> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    try {
      // Use the API client to send a proposal
      const sentProposal = await proposalsApi.sendProposal(id, options);

      // Refetch proposals to update the list
      fetchProposals();

      return sentProposal;
    } catch (err) {
      console.error('Error sending proposal:', err);
      throw err;
    }
  };

  // Download a proposal
  const downloadProposal = async (id: string, options: ProposalDownloadOptions): Promise<Blob> => {
    if (!tenant) {
      throw new Error('Tenant is required');
    }

    try {
      // Use the API client to download a proposal
      return await proposalsApi.downloadProposal(id, options);
    } catch (err) {
      console.error('Error downloading proposal:', err);
      throw err;
    }
  };

  return {
    proposals,
    total,
    page,
    limit,
    isLoading,
    error,
    refetch: fetchProposals,
    createProposal,
    updateProposal,
    deleteProposal,
    getProposalById,
    sendProposal,
    downloadProposal,
  };
}

export default useProposals;
