import { API_BASE_URL } from '../../config';
import MongoApiClient from './mongo-api-client';

/**
 * API Client Factory
 * 
 * This factory creates the appropriate API client based on the database type.
 * It supports both MongoDB and PostgreSQL.
 */
export class ApiClientFactory {
  /**
   * Create an API client
   * @param baseURL API base URL
   * @param databaseType Database type (mongodb or postgresql)
   * @returns API client
   */
  static createApiClient(baseURL: string = API_BASE_URL, databaseType: 'mongodb' | 'postgresql' = 'mongodb') {
    if (databaseType === 'mongodb') {
      return new MongoApiClient(baseURL);
    } else {
      // For now, we'll use the MongoDB client for PostgreSQL as well
      // In a real application, we would create a PostgreSQL-specific client
      return new MongoApiClient(baseURL);
    }
  }
  
  /**
   * Get the database type from environment variables or configuration
   * @returns Database type
   */
  static getDatabaseType(): 'mongodb' | 'postgresql' {
    // Check environment variables
    if (typeof process !== 'undefined' && process.env) {
      if (process.env.DATABASE_TYPE === 'postgresql') {
        return 'postgresql';
      }
      if (process.env.MONGODB_ENABLED === 'true') {
        return 'mongodb';
      }
    }
    
    // Check browser environment
    if (typeof window !== 'undefined') {
      // Check for a global configuration or localStorage
      const storedType = localStorage.getItem('databaseType');
      if (storedType === 'postgresql') {
        return 'postgresql';
      }
    }
    
    // Default to MongoDB
    return 'mongodb';
  }
  
  /**
   * Get an API client based on the current environment
   * @param baseURL API base URL
   * @returns API client
   */
  static getApiClient(baseURL: string = API_BASE_URL) {
    const databaseType = this.getDatabaseType();
    return this.createApiClient(baseURL, databaseType);
  }
}

export default ApiClientFactory;
