import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { API_BASE_URL } from '../../config';
import { createAuthHeaders } from '../../utils/auth';
import { PaginatedResponse, PaginationParams } from '../../types/pagination';

/**
 * MongoDB API Client for CRM
 * 
 * This client provides methods for interacting with the CRM API
 * when using MongoDB as the database.
 */
export class MongoApiClient {
  private axiosInstance: AxiosInstance;
  
  constructor(baseURL: string = API_BASE_URL) {
    this.axiosInstance = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    // Add request interceptor to include auth headers
    this.axiosInstance.interceptors.request.use((config) => {
      const headers = createAuthHeaders();
      config.headers = { ...config.headers, ...headers };
      return config;
    });
  }
  
  /**
   * Generic method to get a paginated list of resources
   * @param endpoint API endpoint
   * @param params Pagination parameters
   * @returns Paginated response
   */
  async getPaginatedList<T>(
    endpoint: string,
    params?: PaginationParams
  ): Promise<PaginatedResponse<T>> {
    const response = await this.axiosInstance.get(endpoint, { params });
    return response.data;
  }
  
  /**
   * Generic method to get a single resource by ID
   * @param endpoint API endpoint
   * @param id Resource ID
   * @returns Resource
   */
  async getById<T>(endpoint: string, id: string): Promise<T> {
    const response = await this.axiosInstance.get(`${endpoint}/${id}`);
    return response.data;
  }
  
  /**
   * Generic method to create a resource
   * @param endpoint API endpoint
   * @param data Resource data
   * @returns Created resource
   */
  async create<T>(endpoint: string, data: any): Promise<T> {
    const response = await this.axiosInstance.post(endpoint, data);
    return response.data;
  }
  
  /**
   * Generic method to update a resource
   * @param endpoint API endpoint
   * @param id Resource ID
   * @param data Resource data
   * @returns Updated resource
   */
  async update<T>(endpoint: string, id: string, data: any): Promise<T> {
    const response = await this.axiosInstance.put(`${endpoint}/${id}`, data);
    return response.data;
  }
  
  /**
   * Generic method to delete a resource
   * @param endpoint API endpoint
   * @param id Resource ID
   * @returns Deleted resource
   */
  async delete<T>(endpoint: string, id: string): Promise<T> {
    const response = await this.axiosInstance.delete(`${endpoint}/${id}`);
    return response.data;
  }
  
  /**
   * Generic method to search resources
   * @param endpoint API endpoint
   * @param query Search query
   * @param params Pagination parameters
   * @returns Paginated response
   */
  async search<T>(
    endpoint: string,
    query: string,
    params?: PaginationParams
  ): Promise<PaginatedResponse<T>> {
    const response = await this.axiosInstance.get(`${endpoint}/search`, {
      params: { query, ...params },
    });
    return response.data;
  }
  
  /**
   * Generic method to perform a custom action
   * @param endpoint API endpoint
   * @param action Action name
   * @param data Action data
   * @returns Action result
   */
  async performAction<T>(endpoint: string, action: string, data?: any): Promise<T> {
    const response = await this.axiosInstance.post(`${endpoint}/${action}`, data);
    return response.data;
  }
  
  /**
   * Get contacts
   * @param params Pagination parameters
   * @returns Paginated contacts
   */
  async getContacts(params?: PaginationParams) {
    return this.getPaginatedList('/api/crm/contacts', params);
  }
  
  /**
   * Get contact by ID
   * @param id Contact ID
   * @returns Contact
   */
  async getContact(id: string) {
    return this.getById('/api/crm/contacts', id);
  }
  
  /**
   * Create contact
   * @param data Contact data
   * @returns Created contact
   */
  async createContact(data: any) {
    return this.create('/api/crm/contacts', data);
  }
  
  /**
   * Update contact
   * @param id Contact ID
   * @param data Contact data
   * @returns Updated contact
   */
  async updateContact(id: string, data: any) {
    return this.update('/api/crm/contacts', id, data);
  }
  
  /**
   * Delete contact
   * @param id Contact ID
   * @returns Deleted contact
   */
  async deleteContact(id: string) {
    return this.delete('/api/crm/contacts', id);
  }
  
  /**
   * Search contacts
   * @param query Search query
   * @param params Pagination parameters
   * @returns Paginated contacts
   */
  async searchContacts(query: string, params?: PaginationParams) {
    return this.search('/api/crm/contacts', query, params);
  }
  
  /**
   * Get companies
   * @param params Pagination parameters
   * @returns Paginated companies
   */
  async getCompanies(params?: PaginationParams) {
    return this.getPaginatedList('/api/crm/companies', params);
  }
  
  /**
   * Get company by ID
   * @param id Company ID
   * @returns Company
   */
  async getCompany(id: string) {
    return this.getById('/api/crm/companies', id);
  }
  
  /**
   * Create company
   * @param data Company data
   * @returns Created company
   */
  async createCompany(data: any) {
    return this.create('/api/crm/companies', data);
  }
  
  /**
   * Update company
   * @param id Company ID
   * @param data Company data
   * @returns Updated company
   */
  async updateCompany(id: string, data: any) {
    return this.update('/api/crm/companies', id, data);
  }
  
  /**
   * Delete company
   * @param id Company ID
   * @returns Deleted company
   */
  async deleteCompany(id: string) {
    return this.delete('/api/crm/companies', id);
  }
  
  /**
   * Search companies
   * @param query Search query
   * @param params Pagination parameters
   * @returns Paginated companies
   */
  async searchCompanies(query: string, params?: PaginationParams) {
    return this.search('/api/crm/companies', query, params);
  }
  
  // Add similar methods for opportunities, activities, tasks, etc.
}

export default MongoApiClient;
