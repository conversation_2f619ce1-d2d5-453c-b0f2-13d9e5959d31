import { apiClient } from './api-client';
import { CRM_API_ROUTES } from '../../api';
import {
  IProposal,
  CreateProposalRequest,
  UpdateProposalRequest,
  ProposalResponse,
  ProposalListResponse,
  ProposalDownloadOptions,
  ProposalSendOptions,
  ProposalAIGenerationOptions,
} from '../../types/proposals';

/**
 * Proposals API client
 *
 * This module provides methods for interacting with the proposals API.
 */
export const proposalsApi = {
  /**
   * Set the tenant ID for all requests
   * @param tenantId Tenant ID
   */
  setTenantId: (tenantId: string): void => {
    apiClient.setTenantId(tenantId);
  },
  /**
   * Get a list of proposals
   * @param params Query parameters
   * @returns Proposal list response
   */
  getProposals: async (params: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    status?: string;
    opportunityId?: string;
    companyId?: string;
    contactId?: string;
    search?: string;
  } = {}): Promise<ProposalListResponse> => {
    return apiClient.get(CRM_API_ROUTES.PROPOSALS, { params });
  },

  /**
   * Get a proposal by ID
   * @param id Proposal ID
   * @returns Proposal
   */
  getProposalById: async (id: string): Promise<ProposalResponse> => {
    return apiClient.get(`${CRM_API_ROUTES.PROPOSALS}/${id}`);
  },

  /**
   * Get a proposal by public token
   * @param token Public token
   * @returns Proposal
   */
  getProposalByToken: async (token: string): Promise<ProposalResponse> => {
    return apiClient.get(`${CRM_API_ROUTES.PROPOSALS}/public/${token}`);
  },

  /**
   * Create a new proposal
   * @param data Proposal data
   * @returns Created proposal
   */
  createProposal: async (data: CreateProposalRequest): Promise<ProposalResponse> => {
    return apiClient.post(CRM_API_ROUTES.PROPOSALS, data);
  },

  /**
   * Update a proposal
   * @param id Proposal ID
   * @param data Proposal data
   * @returns Updated proposal
   */
  updateProposal: async (id: string, data: UpdateProposalRequest): Promise<ProposalResponse> => {
    return apiClient.put(`${CRM_API_ROUTES.PROPOSALS}/${id}`, data);
  },

  /**
   * Delete a proposal
   * @param id Proposal ID
   * @returns Success status
   */
  deleteProposal: async (id: string): Promise<{ message: string }> => {
    return apiClient.delete(`${CRM_API_ROUTES.PROPOSALS}/${id}`);
  },

  /**
   * Send a proposal
   * @param id Proposal ID
   * @param options Send options
   * @returns Updated proposal
   */
  sendProposal: async (id: string, options: ProposalSendOptions): Promise<ProposalResponse> => {
    return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/${id}/send`, options);
  },

  /**
   * Accept a proposal
   * @param id Proposal ID
   * @returns Updated proposal
   */
  acceptProposal: async (id: string): Promise<ProposalResponse> => {
    return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/${id}/accept`, {});
  },

  /**
   * Reject a proposal
   * @param id Proposal ID
   * @param reason Rejection reason
   * @returns Updated proposal
   */
  rejectProposal: async (id: string, reason: string): Promise<ProposalResponse> => {
    return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/${id}/reject`, { reason });
  },

  /**
   * Record a view for a public proposal
   * @param token Public token
   * @param analyticsData Analytics data
   * @returns Success status
   */
  recordView: async (token: string, analyticsData: any = {}): Promise<{ success: boolean }> => {
    return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/public/${token}/view`, analyticsData);
  },

  /**
   * Download a proposal
   * @param id Proposal ID
   * @param options Download options
   * @returns Blob data
   */
  downloadProposal: async (id: string, options: ProposalDownloadOptions): Promise<Blob> => {
    const { format, ...otherOptions } = options;
    return apiClient.downloadFile(`${CRM_API_ROUTES.PROPOSALS}/${id}/download?format=${format}`, {
      params: otherOptions,
    });
  },

  /**
   * Download a public proposal
   * @param token Public token
   * @param options Download options
   * @returns Blob data
   */
  downloadPublicProposal: async (token: string, options: ProposalDownloadOptions): Promise<Blob> => {
    const { format, ...otherOptions } = options;
    return apiClient.downloadFile(`${CRM_API_ROUTES.PROPOSALS}/public/${token}/download?format=${format}`, {
      params: otherOptions,
    });
  },

  /**
   * Generate a proposal with AI
   * @param options AI generation options
   * @returns Generated proposal data
   */
  generateProposal: async (options: ProposalAIGenerationOptions): Promise<Partial<IProposal>> => {
    return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/ai/generate`, options);
  },

  /**
   * Generate a proposal section with AI
   * @param sectionType Section type
   * @param prompt Prompt
   * @param model Model
   * @param context Additional context
   * @returns Generated section
   */
  generateProposalSection: async (
    sectionType: string,
    prompt: string,
    model: string,
    context: {
      opportunityId?: string;
      companyId?: string;
      contactIds?: string[];
    } = {}
  ): Promise<{ title: string; content: string; type: string }> => {
    return apiClient.post(`${CRM_API_ROUTES.PROPOSALS}/ai/generate-section`, {
      sectionType,
      prompt,
      model,
      ...context,
    });
  },
};

export default proposalsApi;
