import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * API error class
 */
export class ApiError extends Error {
  status: number;
  data: any;
  
  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * API client options
 */
export interface ApiClientOptions {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
}

/**
 * API client class
 * 
 * This class provides a wrapper around axios for making API requests.
 * It handles common tasks like setting headers, error handling, and response parsing.
 */
export class ApiClient {
  private client: AxiosInstance;
  private tenantId: string | null = null;
  private authToken: string | null = null;
  
  /**
   * Create a new API client
   * @param options API client options
   */
  constructor(options: ApiClientOptions = {}) {
    this.client = axios.create({
      baseURL: options.baseURL || '',
      timeout: options.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    
    // Add request interceptor for auth and tenant headers
    this.client.interceptors.request.use((config) => {
      // Add tenant header if available
      if (this.tenantId) {
        config.headers['X-Tenant-ID'] = this.tenantId;
      }
      
      // Add auth header if available
      if (this.authToken) {
        config.headers['Authorization'] = `Bearer ${this.authToken}`;
      }
      
      return config;
    });
    
    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          const status = error.response.status;
          const data = error.response.data;
          const message = data?.message || error.message;
          
          throw new ApiError(message, status, data);
        } else if (error.request) {
          // The request was made but no response was received
          throw new ApiError('No response received from server', 0);
        } else {
          // Something happened in setting up the request that triggered an Error
          throw new ApiError(error.message, 0);
        }
      }
    );
  }
  
  /**
   * Set the tenant ID for all requests
   * @param tenantId Tenant ID
   */
  setTenantId(tenantId: string): void {
    this.tenantId = tenantId;
  }
  
  /**
   * Set the auth token for all requests
   * @param token Auth token
   */
  setAuthToken(token: string): void {
    this.authToken = token;
  }
  
  /**
   * Make a GET request
   * @param url URL to request
   * @param config Request config
   * @returns Response data
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, config);
    return response.data;
  }
  
  /**
   * Make a POST request
   * @param url URL to request
   * @param data Request data
   * @param config Request config
   * @returns Response data
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }
  
  /**
   * Make a PUT request
   * @param url URL to request
   * @param data Request data
   * @param config Request config
   * @returns Response data
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config);
    return response.data;
  }
  
  /**
   * Make a PATCH request
   * @param url URL to request
   * @param data Request data
   * @param config Request config
   * @returns Response data
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.patch<T>(url, data, config);
    return response.data;
  }
  
  /**
   * Make a DELETE request
   * @param url URL to request
   * @param config Request config
   * @returns Response data
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);
    return response.data;
  }
  
  /**
   * Download a file
   * @param url URL to request
   * @param config Request config
   * @returns Blob data
   */
  async downloadFile(url: string, config?: AxiosRequestConfig): Promise<Blob> {
    const response = await this.client.get(url, {
      ...config,
      responseType: 'blob',
    });
    return response.data;
  }
}

/**
 * Create a new API client instance
 * @param options API client options
 * @returns API client instance
 */
export function createApiClient(options: ApiClientOptions = {}): ApiClient {
  return new ApiClient(options);
}

/**
 * Default API client instance
 */
export const apiClient = createApiClient();
