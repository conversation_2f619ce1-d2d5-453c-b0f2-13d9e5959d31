import request from 'supertest';
import express from 'express';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { v4 as uuidv4 } from 'uuid';
import proposalRoutes from '../../routes/proposal-routes';
import proposalAiRoutes from '../../routes/proposal-ai';
import { errorHandler, notFoundHandler } from '../../middleware/error-handler';
import { Proposal } from '../../../models/proposal';
import { AIService } from '../../../services/ai-service';
import { DocumentGenerationService } from '../../../services/document-generation-service';

// Mock the AI service
jest.mock('../../../services/ai-service', () => ({
  AIService: {
    generateProposal: jest.fn(),
    generateProposalSection: jest.fn(),
  },
}));

// Mock the document generation service
jest.mock('../../../services/document-generation-service', () => ({
  DocumentGenerationService: {
    generateDocument: jest.fn(),
  },
}));

// Mock the tenant access middleware
jest.mock('@aizako/core-lib', () => ({
  tenantAccessMiddleware: (req: any, res: any, next: any) => {
    req.tenantId = 'test-tenant-id';
    req.userId = 'test-user-id';
    next();
  },
}));

describe('Proposal API Integration Tests', () => {
  let app: express.Application;
  let mongoServer: MongoMemoryServer;
  let testProposalId: string;
  
  // Set up the Express app and MongoDB memory server
  beforeAll(async () => {
    // Create a MongoDB memory server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    // Connect to the in-memory database
    await mongoose.connect(mongoUri);
    
    // Create the Express app
    app = express();
    app.use(express.json());
    
    // Add the proposal routes
    app.use('/api/crm/proposals', proposalRoutes);
    app.use('/api/crm/proposals/ai', proposalAiRoutes);
    
    // Add error handling middleware
    app.use(notFoundHandler);
    app.use(errorHandler);
    
    // Create a test proposal
    const proposal = new Proposal({
      title: 'Test Proposal',
      description: 'Test Description',
      status: 'draft',
      tenantId: 'test-tenant-id',
      sections: [
        {
          id: uuidv4(),
          title: 'Test Section',
          content: 'Test Content',
          order: 0,
          type: 'text',
          isVisible: true,
        },
      ],
      pricing: {
        currency: 'USD',
        items: [
          {
            id: uuidv4(),
            name: 'Test Item',
            description: 'Test Item Description',
            quantity: 1,
            unitPrice: 100,
            total: 100,
          },
        ],
        subtotal: 100,
        total: 100,
      },
      terms: 'Test Terms',
      publicToken: 'test-public-token',
      publicUrl: '/proposals/public/test-public-token',
      publicAccessEnabled: true,
      downloadEnabled: true,
      downloadFormats: ['pdf', 'docx', 'md'],
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    const savedProposal = await proposal.save();
    testProposalId = savedProposal._id.toString();
  });
  
  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  // Test getting all proposals
  describe('GET /api/crm/proposals', () => {
    it('should return a list of proposals', async () => {
      const response = await request(app)
        .get('/api/crm/proposals')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('title', 'Test Proposal');
    });
    
    it('should filter proposals by status', async () => {
      const response = await request(app)
        .get('/api/crm/proposals?status=draft')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('status', 'draft');
    });
    
    it('should paginate results', async () => {
      const response = await request(app)
        .get('/api/crm/proposals?page=1&limit=10')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('page', 1);
      expect(response.body).toHaveProperty('limit', 10);
      expect(response.body).toHaveProperty('totalPages');
      expect(response.body).toHaveProperty('hasNextPage');
      expect(response.body).toHaveProperty('hasPrevPage');
    });
  });
  
  // Test getting a proposal by ID
  describe('GET /api/crm/proposals/:id', () => {
    it('should return a proposal by ID', async () => {
      const response = await request(app)
        .get(`/api/crm/proposals/${testProposalId}`)
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('_id', testProposalId);
      expect(response.body).toHaveProperty('title', 'Test Proposal');
      expect(response.body).toHaveProperty('description', 'Test Description');
      expect(response.body).toHaveProperty('status', 'draft');
      expect(response.body).toHaveProperty('sections');
      expect(response.body.sections).toBeInstanceOf(Array);
      expect(response.body.sections.length).toBe(1);
    });
    
    it('should return 404 for non-existent proposal', async () => {
      await request(app)
        .get('/api/crm/proposals/nonexistentid')
        .expect(404);
    });
  });
  
  // Test getting a proposal by public token
  describe('GET /api/crm/proposals/public/:token', () => {
    it('should return a proposal by public token', async () => {
      const response = await request(app)
        .get('/api/crm/proposals/public/test-public-token')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('title', 'Test Proposal');
      expect(response.body).toHaveProperty('description', 'Test Description');
      expect(response.body).toHaveProperty('sections');
      expect(response.body.sections).toBeInstanceOf(Array);
    });
    
    it('should return 404 for non-existent token', async () => {
      await request(app)
        .get('/api/crm/proposals/public/nonexistenttoken')
        .expect(404);
    });
  });
  
  // Test creating a proposal
  describe('POST /api/crm/proposals', () => {
    it('should create a new proposal', async () => {
      const newProposal = {
        title: 'New Test Proposal',
        description: 'New Test Description',
        status: 'draft',
        sections: [
          {
            id: uuidv4(),
            title: 'New Test Section',
            content: 'New Test Content',
            order: 0,
            type: 'text',
            isVisible: true,
          },
        ],
        pricing: {
          currency: 'USD',
          items: [
            {
              id: uuidv4(),
              name: 'New Test Item',
              description: 'New Test Item Description',
              quantity: 1,
              unitPrice: 200,
              total: 200,
            },
          ],
          subtotal: 200,
          total: 200,
        },
        terms: 'New Test Terms',
      };
      
      const response = await request(app)
        .post('/api/crm/proposals')
        .send(newProposal)
        .expect('Content-Type', /json/)
        .expect(201);
      
      expect(response.body).toHaveProperty('_id');
      expect(response.body).toHaveProperty('title', 'New Test Proposal');
      expect(response.body).toHaveProperty('description', 'New Test Description');
      expect(response.body).toHaveProperty('status', 'draft');
      expect(response.body).toHaveProperty('tenantId', 'test-tenant-id');
      expect(response.body).toHaveProperty('publicToken');
      expect(response.body).toHaveProperty('publicUrl');
    });
    
    it('should return 400 for invalid proposal data', async () => {
      const invalidProposal = {
        // Missing required fields
        description: 'Invalid Test Description',
      };
      
      await request(app)
        .post('/api/crm/proposals')
        .send(invalidProposal)
        .expect(400);
    });
  });
  
  // Test updating a proposal
  describe('PUT /api/crm/proposals/:id', () => {
    it('should update an existing proposal', async () => {
      const updateData = {
        title: 'Updated Test Proposal',
        description: 'Updated Test Description',
      };
      
      const response = await request(app)
        .put(`/api/crm/proposals/${testProposalId}`)
        .send(updateData)
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('_id', testProposalId);
      expect(response.body).toHaveProperty('title', 'Updated Test Proposal');
      expect(response.body).toHaveProperty('description', 'Updated Test Description');
    });
    
    it('should return 404 for non-existent proposal', async () => {
      await request(app)
        .put('/api/crm/proposals/nonexistentid')
        .send({ title: 'Updated Title' })
        .expect(404);
    });
  });
  
  // Test downloading a proposal
  describe('GET /api/crm/proposals/:id/download', () => {
    beforeEach(() => {
      // Mock the document generation service
      (DocumentGenerationService.generateDocument as jest.Mock).mockResolvedValue(
        Buffer.from('Test document content')
      );
    });
    
    it('should download a proposal as PDF', async () => {
      const response = await request(app)
        .get(`/api/crm/proposals/${testProposalId}/download?format=pdf`)
        .expect('Content-Type', /pdf/)
        .expect('Content-Disposition', /attachment/)
        .expect(200);
      
      expect(DocumentGenerationService.generateDocument).toHaveBeenCalled();
      expect(response.body).toBeDefined();
    });
    
    it('should download a proposal as DOCX', async () => {
      const response = await request(app)
        .get(`/api/crm/proposals/${testProposalId}/download?format=docx`)
        .expect('Content-Type', /vnd.openxmlformats-officedocument.wordprocessingml.document/)
        .expect('Content-Disposition', /attachment/)
        .expect(200);
      
      expect(DocumentGenerationService.generateDocument).toHaveBeenCalled();
      expect(response.body).toBeDefined();
    });
    
    it('should download a proposal as Markdown', async () => {
      (DocumentGenerationService.generateDocument as jest.Mock).mockResolvedValue(
        '# Test Markdown Content'
      );
      
      const response = await request(app)
        .get(`/api/crm/proposals/${testProposalId}/download?format=md`)
        .expect('Content-Type', /markdown/)
        .expect('Content-Disposition', /attachment/)
        .expect(200);
      
      expect(DocumentGenerationService.generateDocument).toHaveBeenCalled();
      expect(response.text).toBe('# Test Markdown Content');
    });
    
    it('should return 404 for non-existent proposal', async () => {
      await request(app)
        .get('/api/crm/proposals/nonexistentid/download?format=pdf')
        .expect(404);
    });
  });
  
  // Test sending a proposal
  describe('POST /api/crm/proposals/:id/send', () => {
    it('should send a proposal', async () => {
      const sendOptions = {
        to: ['<EMAIL>'],
        subject: 'Test Subject',
        message: 'Test Message',
        includeDownloadLink: true,
        includeViewLink: true,
      };
      
      const response = await request(app)
        .post(`/api/crm/proposals/${testProposalId}/send`)
        .send(sendOptions)
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('_id', testProposalId);
      expect(response.body).toHaveProperty('status', 'sent');
      expect(response.body).toHaveProperty('sentAt');
      expect(response.body).toHaveProperty('sentBy', 'test-user-id');
    });
    
    it('should return 404 for non-existent proposal', async () => {
      await request(app)
        .post('/api/crm/proposals/nonexistentid/send')
        .send({ to: ['<EMAIL>'] })
        .expect(404);
    });
    
    it('should return 400 for invalid send options', async () => {
      const invalidOptions = {
        // Missing required fields
        subject: 'Test Subject',
      };
      
      await request(app)
        .post(`/api/crm/proposals/${testProposalId}/send`)
        .send(invalidOptions)
        .expect(400);
    });
  });
  
  // Test AI generation
  describe('POST /api/crm/proposals/ai/generate', () => {
    beforeEach(() => {
      // Mock the AI service
      (AIService.generateProposal as jest.Mock).mockResolvedValue({
        title: 'AI Generated Proposal',
        description: 'AI Generated Description',
        sections: [
          {
            id: uuidv4(),
            title: 'AI Generated Section',
            content: 'AI Generated Content',
            order: 0,
            type: 'text',
            isVisible: true,
          },
        ],
        pricing: {
          currency: 'USD',
          items: [
            {
              id: uuidv4(),
              name: 'AI Generated Item',
              description: 'AI Generated Item Description',
              quantity: 1,
              unitPrice: 300,
              total: 300,
            },
          ],
          subtotal: 300,
          total: 300,
        },
        terms: 'AI Generated Terms',
      });
    });
    
    it('should generate a proposal with AI', async () => {
      const aiOptions = {
        prompt: 'Generate a proposal for a web development project',
        model: 'claude-3-opus-20240229',
        includeSections: {
          executiveSummary: true,
          solution: true,
          timeline: true,
          pricing: true,
          terms: true,
        },
      };
      
      const response = await request(app)
        .post('/api/crm/proposals/ai/generate')
        .send(aiOptions)
        .expect('Content-Type', /json/)
        .expect(201);
      
      expect(AIService.generateProposal).toHaveBeenCalledWith(aiOptions);
      expect(response.body).toHaveProperty('title', 'AI Generated Proposal');
      expect(response.body).toHaveProperty('description', 'AI Generated Description');
      expect(response.body).toHaveProperty('sections');
      expect(response.body.sections).toBeInstanceOf(Array);
      expect(response.body).toHaveProperty('tenantId', 'test-tenant-id');
    });
    
    it('should return 400 for invalid AI options', async () => {
      const invalidOptions = {
        // Missing required fields
        model: 'claude-3-opus-20240229',
      };
      
      await request(app)
        .post('/api/crm/proposals/ai/generate')
        .send(invalidOptions)
        .expect(400);
    });
  });
  
  // Test AI section generation
  describe('POST /api/crm/proposals/ai/generate-section', () => {
    beforeEach(() => {
      // Mock the AI service
      (AIService.generateProposalSection as jest.Mock).mockResolvedValue({
        title: 'AI Generated Section',
        content: 'AI Generated Content',
        type: 'text',
      });
    });
    
    it('should generate a proposal section with AI', async () => {
      const aiOptions = {
        sectionType: 'text',
        prompt: 'Generate a section about web development',
        model: 'claude-3-opus-20240229',
      };
      
      const response = await request(app)
        .post('/api/crm/proposals/ai/generate-section')
        .send(aiOptions)
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(AIService.generateProposalSection).toHaveBeenCalledWith(
        'text',
        'Generate a section about web development',
        'claude-3-opus-20240229',
        {}
      );
      expect(response.body).toHaveProperty('title', 'AI Generated Section');
      expect(response.body).toHaveProperty('content', 'AI Generated Content');
      expect(response.body).toHaveProperty('type', 'text');
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('order', 0);
      expect(response.body).toHaveProperty('isVisible', true);
      expect(response.body).toHaveProperty('aiGenerated', true);
    });
    
    it('should return 400 for invalid AI options', async () => {
      const invalidOptions = {
        // Missing required fields
        prompt: 'Generate a section about web development',
      };
      
      await request(app)
        .post('/api/crm/proposals/ai/generate-section')
        .send(invalidOptions)
        .expect(400);
    });
  });
  
  // Test deleting a proposal
  describe('DELETE /api/crm/proposals/:id', () => {
    it('should delete a proposal', async () => {
      const response = await request(app)
        .delete(`/api/crm/proposals/${testProposalId}`)
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('message', 'Proposal deleted successfully');
      
      // Verify the proposal is deleted
      await request(app)
        .get(`/api/crm/proposals/${testProposalId}`)
        .expect(404);
    });
    
    it('should return 404 for non-existent proposal', async () => {
      await request(app)
        .delete('/api/crm/proposals/nonexistentid')
        .expect(404);
    });
  });
});
