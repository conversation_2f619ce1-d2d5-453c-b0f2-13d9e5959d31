import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

// Extend the NodeJS global interface to include our custom properties
declare global {
  namespace NodeJS {
    interface Global {
      mongoServer: MongoMemoryServer;
    }
  }
}

// Set up MongoDB memory server
export const setupTestDB = async (): Promise<void> => {
  // Create a MongoDB memory server
  const mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  // Connect to the in-memory database
  await mongoose.connect(mongoUri);
  
  // Store the server instance for later cleanup
  (global as any).mongoServer = mongoServer;
};

// Clean up MongoDB memory server
export const teardownTestDB = async (): Promise<void> => {
  // Disconnect from the database
  await mongoose.disconnect();
  
  // Stop the MongoDB memory server
  if ((global as any).mongoServer) {
    await (global as any).mongoServer.stop();
  }
};

// Clear all collections between tests
export const clearDatabase = async (): Promise<void> => {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
};

// Mock the tenant access middleware
export const mockTenantAccessMiddleware = (req: any, res: any, next: any) => {
  req.tenantId = 'test-tenant-id';
  req.userId = 'test-user-id';
  next();
};

// Mock the rate limiter middleware
export const mockRateLimiterMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the validation middleware
export const mockValidationMiddleware = (schema: any) => (req: any, res: any, next: any) => {
  next();
};

// Mock the error handler middleware
export const mockErrorHandlerMiddleware = (err: any, req: any, res: any, next: any) => {
  res.status(err.statusCode || 500).json({
    message: err.message || 'Internal Server Error',
  });
};

// Mock the not found handler middleware
export const mockNotFoundHandlerMiddleware = (req: any, res: any) => {
  res.status(404).json({
    message: 'Not Found',
  });
};

// Mock the authentication middleware
export const mockAuthMiddleware = (req: any, res: any, next: any) => {
  req.user = {
    id: 'test-user-id',
    email: '<EMAIL>',
    role: 'admin',
  };
  next();
};

// Mock the authorization middleware
export const mockAuthorizationMiddleware = (roles: string[]) => (req: any, res: any, next: any) => {
  next();
};

// Mock the logging middleware
export const mockLoggingMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the request ID middleware
export const mockRequestIdMiddleware = (req: any, res: any, next: any) => {
  req.id = 'test-request-id';
  next();
};

// Mock the CORS middleware
export const mockCorsMiddleware = (req: any, res: any, next: any) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  next();
};

// Mock the compression middleware
export const mockCompressionMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the body parser middleware
export const mockBodyParserMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the helmet middleware
export const mockHelmetMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the express-validator middleware
export const mockExpressValidatorMiddleware = (req: any, res: any, next: any) => {
  req.validationErrors = () => null;
  next();
};

// Mock the multer middleware
export const mockMulterMiddleware = (req: any, res: any, next: any) => {
  req.file = {
    fieldname: 'file',
    originalname: 'test.jpg',
    encoding: '7bit',
    mimetype: 'image/jpeg',
    buffer: Buffer.from('test'),
    size: 4,
  };
  next();
};

// Mock the passport middleware
export const mockPassportMiddleware = (req: any, res: any, next: any) => {
  req.isAuthenticated = () => true;
  req.user = {
    id: 'test-user-id',
    email: '<EMAIL>',
    role: 'admin',
  };
  next();
};

// Mock the session middleware
export const mockSessionMiddleware = (req: any, res: any, next: any) => {
  req.session = {
    id: 'test-session-id',
    destroy: (callback: (err?: any) => void) => callback(),
  };
  next();
};

// Mock the flash middleware
export const mockFlashMiddleware = (req: any, res: any, next: any) => {
  req.flash = (type: string, message: string) => {};
  next();
};

// Mock the cookie parser middleware
export const mockCookieParserMiddleware = (req: any, res: any, next: any) => {
  req.cookies = {
    token: 'test-token',
  };
  next();
};

// Mock the JWT middleware
export const mockJwtMiddleware = (req: any, res: any, next: any) => {
  req.user = {
    id: 'test-user-id',
    email: '<EMAIL>',
    role: 'admin',
  };
  next();
};

// Mock the GraphQL middleware
export const mockGraphQLMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the WebSocket middleware
export const mockWebSocketMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the Redis middleware
export const mockRedisMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the cache middleware
export const mockCacheMiddleware = (req: any, res: any, next: any) => {
  next();
};

// Mock the rate limiter middleware
export const mockRateLimiter = (req: any, res: any, next: any) => {
  next();
};

// Mock the validation middleware
export const mockValidation = (schema: any) => (req: any, res: any, next: any) => {
  next();
};

// Mock the error handler middleware
export const mockErrorHandler = (err: any, req: any, res: any, next: any) => {
  res.status(err.statusCode || 500).json({
    message: err.message || 'Internal Server Error',
  });
};

// Mock the not found handler middleware
export const mockNotFoundHandler = (req: any, res: any) => {
  res.status(404).json({
    message: 'Not Found',
  });
};
