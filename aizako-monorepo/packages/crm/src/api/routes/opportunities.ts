import { Router } from 'express';
import { OpportunityService } from '../../services/opportunity-service';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { createPaginationResult } from '../../types/pagination';

const router = Router();

/**
 * @route GET /api/crm/opportunities
 * @desc Get all opportunities with pagination
 * @access Private
 */
router.get('/', tenantAccessMiddleware, async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = req.query;
    
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const { opportunities, total } = await OpportunityService.getOpportunities(
      {
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        ...filters,
      },
      tenantId
    );
    
    const result = createPaginationResult(
      opportunities,
      total,
      Number(page),
      Number(limit)
    );
    
    return res.json(result);
  } catch (error) {
    console.error('Error getting opportunities:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/opportunities/:id
 * @desc Get opportunity by ID
 * @access Private
 */
router.get('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const opportunity = await OpportunityService.getOpportunityById(id, tenantId);
    
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json(opportunity);
  } catch (error) {
    console.error('Error getting opportunity by ID:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/opportunities
 * @desc Create a new opportunity
 * @access Private
 */
router.post('/', tenantAccessMiddleware, async (req, res) => {
  try {
    const opportunityData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const opportunity = await OpportunityService.createOpportunity(opportunityData, tenantId);
    
    return res.status(201).json(opportunity);
  } catch (error) {
    console.error('Error creating opportunity:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/crm/opportunities/:id
 * @desc Update an opportunity
 * @access Private
 */
router.put('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const opportunityData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const opportunity = await OpportunityService.updateOpportunity(id, opportunityData, tenantId);
    
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json(opportunity);
  } catch (error) {
    console.error('Error updating opportunity:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/crm/opportunities/:id
 * @desc Delete an opportunity
 * @access Private
 */
router.delete('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const deleted = await OpportunityService.deleteOpportunity(id, tenantId);
    
    if (!deleted) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json({ message: 'Opportunity deleted successfully' });
  } catch (error) {
    console.error('Error deleting opportunity:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/opportunities/:id/stage
 * @desc Update an opportunity's stage
 * @access Private
 */
router.post('/:id/stage', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const { stage, reason } = req.body;
    const tenantId = req.tenantId;
    const userId = req.userId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }
    
    if (!stage) {
      return res.status(400).json({ message: 'Stage is required' });
    }
    
    const opportunity = await OpportunityService.updateStage(id, stage, userId, reason, tenantId);
    
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json(opportunity);
  } catch (error) {
    console.error('Error updating opportunity stage:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/opportunities/:id/interactions
 * @desc Add an interaction to an opportunity
 * @access Private
 */
router.post('/:id/interactions', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const interactionData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const opportunity = await OpportunityService.addInteraction(id, interactionData, tenantId);
    
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json(opportunity);
  } catch (error) {
    console.error('Error adding interaction to opportunity:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/opportunities/:id/objections
 * @desc Add an objection to an opportunity
 * @access Private
 */
router.post('/:id/objections', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const objectionData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const opportunity = await OpportunityService.addObjection(id, objectionData, tenantId);
    
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json(opportunity);
  } catch (error) {
    console.error('Error adding objection to opportunity:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/opportunities/:id/score
 * @desc Update an opportunity's score
 * @access Private
 */
router.post('/:id/score', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const scoreData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const opportunity = await OpportunityService.updateScore(id, scoreData, tenantId);
    
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json(opportunity);
  } catch (error) {
    console.error('Error updating opportunity score:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/opportunities/:id/insights
 * @desc Update an opportunity's insights
 * @access Private
 */
router.post('/:id/insights', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const insightsData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const opportunity = await OpportunityService.updateInsights(id, insightsData, tenantId);
    
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    
    return res.json(opportunity);
  } catch (error) {
    console.error('Error updating opportunity insights:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

export default router;
