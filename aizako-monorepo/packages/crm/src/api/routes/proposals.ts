import { Router } from 'express';
import { ProposalService } from '../../services/proposal-service';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { createPaginationResult } from '../../types/pagination';

const router = Router();

/**
 * @route GET /api/crm/proposals
 * @desc Get all proposals with pagination
 * @access Private
 */
router.get('/', tenantAccessMiddleware, async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = req.query;

    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const { proposals, total } = await ProposalService.getProposals(
      {
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        ...filters,
      },
      tenantId
    );

    const result = createPaginationResult(
      proposals,
      total,
      Number(page),
      Number(limit)
    );

    return res.json(result);
  } catch (error) {
    console.error('Error getting proposals:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/proposals/:id
 * @desc Get proposal by ID
 * @access Private
 */
router.get('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const proposal = await ProposalService.getProposalById(id, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error getting proposal by ID:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/proposals/public/:token
 * @desc Get proposal by public token (public access)
 * @access Public
 */
router.get('/public/:token', async (req, res) => {
  try {
    const { token } = req.params;

    const proposal = await ProposalService.getProposalByToken(token);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found or access denied' });
    }

    // Record view
    const analyticsEvent = {
      eventType: 'view',
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
    };

    await ProposalService.recordView(proposal._id.toString(), analyticsEvent);

    // Return a sanitized version of the proposal for public viewing
    const publicProposal = {
      _id: proposal._id,
      title: proposal.title,
      description: proposal.description,
      status: proposal.status,
      sections: proposal.sections.filter(section => section.isVisible),
      pricing: proposal.pricing,
      terms: proposal.terms,
      downloadEnabled: proposal.downloadEnabled,
      downloadFormats: proposal.downloadFormats,
      createdAt: proposal.createdAt,
      updatedAt: proposal.updatedAt,
    };

    return res.json(publicProposal);
  } catch (error) {
    console.error('Error getting proposal by token:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/proposals
 * @desc Create a new proposal
 * @access Private
 */
router.post('/', tenantAccessMiddleware, async (req, res) => {
  try {
    const proposalData = req.body;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const proposal = await ProposalService.createProposal(proposalData, tenantId);

    return res.status(201).json(proposal);
  } catch (error) {
    console.error('Error creating proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/crm/proposals/:id
 * @desc Update a proposal
 * @access Private
 */
router.put('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const proposalData = req.body;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const proposal = await ProposalService.updateProposal(id, proposalData, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error updating proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/crm/proposals/:id
 * @desc Delete a proposal
 * @access Private
 */
router.delete('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const deleted = await ProposalService.deleteProposal(id, tenantId);

    if (!deleted) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json({ message: 'Proposal deleted successfully' });
  } catch (error) {
    console.error('Error deleting proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/proposals/:id/send
 * @desc Send a proposal
 * @access Private
 */
router.post('/:id/send', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;
    const userId = req.userId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const proposal = await ProposalService.sendProposal(id, userId, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error sending proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/proposals/:id/accept
 * @desc Accept a proposal
 * @access Private
 */
router.post('/:id/accept', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;
    const userId = req.userId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const proposal = await ProposalService.acceptProposal(id, userId, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error accepting proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/proposals/:id/reject
 * @desc Reject a proposal
 * @access Private
 */
router.post('/:id/reject', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const tenantId = req.tenantId;
    const userId = req.userId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const proposal = await ProposalService.rejectProposal(id, userId, reason, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error rejecting proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/proposals/public/:token/view
 * @desc Record a view for a public proposal
 * @access Public
 */
router.post('/public/:token/view', async (req, res) => {
  try {
    const { token } = req.params;
    const analyticsData = req.body;

    const proposal = await ProposalService.getProposalByToken(token);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found or access denied' });
    }

    // Record view
    const analyticsEvent = {
      eventType: 'view',
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      ...analyticsData,
    };

    await ProposalService.recordView(proposal._id.toString(), analyticsEvent);

    return res.json({ success: true });
  } catch (error) {
    console.error('Error recording proposal view:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/proposals/:id/download
 * @desc Download a proposal in the specified format
 * @access Private
 */
router.get('/:id/download', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const { format = 'pdf' } = req.query;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    // Validate format
    if (!['pdf', 'docx', 'md'].includes(format as string)) {
      return res.status(400).json({ message: 'Invalid format. Supported formats: pdf, docx, md' });
    }

    const proposal = await ProposalService.getProposalById(id, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    // Check if the requested format is enabled for this proposal
    if (!proposal.downloadEnabled || !proposal.downloadFormats?.includes(format as string)) {
      return res.status(403).json({ message: `Download in ${format} format is not enabled for this proposal` });
    }

    // This would be replaced with actual document generation
    // For now, we'll just return a simple text file
    const content = `
Proposal: ${proposal.title}
Description: ${proposal.description || ''}
Status: ${proposal.status}

Sections:
${proposal.sections.filter(section => section.isVisible).map(section => `
${section.title}
${section.content}
`).join('\n')}

${proposal.terms ? `Terms & Conditions:
${proposal.terms}` : ''}
    `;

    // Set appropriate content type and filename
    let contentType = 'text/plain';
    let fileExtension = format;

    if (format === 'pdf') {
      contentType = 'application/pdf';
    } else if (format === 'docx') {
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (format === 'md') {
      contentType = 'text/markdown';
    }

    const sanitizedTitle = proposal.title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const filename = `proposal_${sanitizedTitle}.${fileExtension}`;

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    return res.send(content);
  } catch (error) {
    console.error('Error downloading proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/proposals/public/:token/download
 * @desc Download a public proposal in the specified format
 * @access Public
 */
router.get('/public/:token/download', async (req, res) => {
  try {
    const { token } = req.params;
    const { format = 'pdf' } = req.query;

    // Validate format
    if (!['pdf', 'docx', 'md'].includes(format as string)) {
      return res.status(400).json({ message: 'Invalid format. Supported formats: pdf, docx, md' });
    }

    const proposal = await ProposalService.getProposalByToken(token);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found or access denied' });
    }

    // Check if the requested format is enabled for this proposal
    if (!proposal.downloadEnabled || !proposal.downloadFormats?.includes(format as string)) {
      return res.status(403).json({ message: `Download in ${format} format is not enabled for this proposal` });
    }

    // Record download event
    const analyticsEvent = {
      eventType: 'download',
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      format: format as string,
    };

    await ProposalService.recordView(proposal._id.toString(), analyticsEvent);

    // This would be replaced with actual document generation
    // For now, we'll just return a simple text file
    const content = `
Proposal: ${proposal.title}
Description: ${proposal.description || ''}
Status: ${proposal.status}

Sections:
${proposal.sections.filter(section => section.isVisible).map(section => `
${section.title}
${section.content}
`).join('\n')}

${proposal.terms ? `Terms & Conditions:
${proposal.terms}` : ''}
    `;

    // Set appropriate content type and filename
    let contentType = 'text/plain';
    let fileExtension = format;

    if (format === 'pdf') {
      contentType = 'application/pdf';
    } else if (format === 'docx') {
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (format === 'md') {
      contentType = 'text/markdown';
    }

    const sanitizedTitle = proposal.title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const filename = `proposal_${sanitizedTitle}.${fileExtension}`;

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    return res.send(content);
  } catch (error) {
    console.error('Error downloading public proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

export default router;
