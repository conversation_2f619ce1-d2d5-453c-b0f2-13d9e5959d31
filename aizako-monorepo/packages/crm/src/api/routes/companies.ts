import { Router } from 'express';
import { CompanyService } from '../../services/company-service';
import { tenantAccessMiddleware } from '@aizako/core-lib';
import { createPaginationResult } from '../../types/pagination';

const router = Router();

/**
 * @route GET /api/crm/companies
 * @desc Get all companies with pagination
 * @access Private
 */
router.get('/', tenantAccessMiddleware, async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = req.query;
    
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const { companies, total } = await CompanyService.getCompanies(
      {
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        ...filters,
      },
      tenantId
    );
    
    const result = createPaginationResult(
      companies,
      total,
      Number(page),
      Number(limit)
    );
    
    return res.json(result);
  } catch (error) {
    console.error('Error getting companies:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/companies/:id
 * @desc Get company by ID
 * @access Private
 */
router.get('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const company = await CompanyService.getCompanyById(id, tenantId);
    
    if (!company) {
      return res.status(404).json({ message: 'Company not found' });
    }
    
    return res.json(company);
  } catch (error) {
    console.error('Error getting company by ID:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/companies
 * @desc Create a new company
 * @access Private
 */
router.post('/', tenantAccessMiddleware, async (req, res) => {
  try {
    const companyData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const company = await CompanyService.createCompany(companyData, tenantId);
    
    return res.status(201).json(company);
  } catch (error) {
    console.error('Error creating company:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/crm/companies/:id
 * @desc Update a company
 * @access Private
 */
router.put('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const companyData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const company = await CompanyService.updateCompany(id, companyData, tenantId);
    
    if (!company) {
      return res.status(404).json({ message: 'Company not found' });
    }
    
    return res.json(company);
  } catch (error) {
    console.error('Error updating company:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/crm/companies/:id
 * @desc Delete a company
 * @access Private
 */
router.delete('/:id', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const deleted = await CompanyService.deleteCompany(id, tenantId);
    
    if (!deleted) {
      return res.status(404).json({ message: 'Company not found' });
    }
    
    return res.json({ message: 'Company deleted successfully' });
  } catch (error) {
    console.error('Error deleting company:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/companies/:id/interactions
 * @desc Add an interaction to a company
 * @access Private
 */
router.post('/:id/interactions', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const interactionData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const company = await CompanyService.addInteraction(id, interactionData, tenantId);
    
    if (!company) {
      return res.status(404).json({ message: 'Company not found' });
    }
    
    return res.json(company);
  } catch (error) {
    console.error('Error adding interaction to company:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/companies/:id/score
 * @desc Update a company's score
 * @access Private
 */
router.post('/:id/score', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const scoreData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const company = await CompanyService.updateScore(id, scoreData, tenantId);
    
    if (!company) {
      return res.status(404).json({ message: 'Company not found' });
    }
    
    return res.json(company);
  } catch (error) {
    console.error('Error updating company score:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/companies/:id/insights
 * @desc Update a company's insights
 * @access Private
 */
router.post('/:id/insights', tenantAccessMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const insightsData = req.body;
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }
    
    const company = await CompanyService.updateInsights(id, insightsData, tenantId);
    
    if (!company) {
      return res.status(404).json({ message: 'Company not found' });
    }
    
    return res.json(company);
  } catch (error) {
    console.error('Error updating company insights:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

export default router;
