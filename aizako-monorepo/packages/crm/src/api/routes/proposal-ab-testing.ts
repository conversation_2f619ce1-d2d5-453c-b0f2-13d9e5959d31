import { Router } from 'express';
import { tenantAccessMiddleware } from '../middleware/tenant-middleware';
import { validateRequest } from '../middleware/validation';
import { ABTestingService } from '../../services/ab-testing-service';
import { ProposalService } from '../../services/proposal-service';
import { z } from 'zod';
import { CreateABTestSchema, UpdateABTestSchema } from '../../types/ab-testing';

// Extend the Express Request type to include tenantId and userId
declare global {
  namespace Express {
    interface Request {
      tenantId?: string;
      userId?: string;
    }
  }
}

// Create router
const router = Router();

/**
 * @route POST /api/crm/proposals/ab-testing/tests
 * @desc Create a new A/B test
 * @access Private
 */
router.post(
  '/ab-testing/tests',
  tenantAccessMiddleware,
  validateRequest(CreateABTestSchema),
  async (req, res) => {
    try {
      const options = {
        ...req.body,
        startDate: req.body.startDate ? new Date(req.body.startDate) : undefined,
        endDate: req.body.endDate ? new Date(req.body.endDate) : undefined,
      };
      const tenantId = req.tenantId;

      if (!tenantId) {
        return res.status(400).json({ message: 'Tenant ID is required' });
      }

      // Check if the base proposal exists and belongs to the tenant
      const baseProposal = await ProposalService.getProposalById(options.baseProposalId, tenantId);

      if (!baseProposal) {
        return res.status(404).json({ message: 'Base proposal not found' });
      }

      // Create the A/B test
      const abTest = await ABTestingService.createTest(options, tenantId);

      return res.status(201).json(abTest);
    } catch (error) {
      console.error('Error creating A/B test:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  }
);

/**
 * @route GET /api/crm/proposals/ab-testing/tests
 * @desc Get all A/B tests for a tenant
 * @access Private
 */
router.get(
  '/ab-testing/tests',
  tenantAccessMiddleware,
  validateRequest(
    z.object({
      query: z.object({
        status: z.enum(['active', 'paused', 'completed']).optional(),
      }),
    })
  ),
  async (req, res) => {
    try {
      const { status } = req.query;
      const tenantId = req.tenantId;

      if (!tenantId) {
        return res.status(400).json({ message: 'Tenant ID is required' });
      }

      // Get A/B tests
      const tests = await ABTestingService.getTests(
        tenantId,
        status as 'active' | 'paused' | 'completed' | undefined
      );

      return res.json(tests);
    } catch (error) {
      console.error('Error getting A/B tests:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  }
);

/**
 * @route GET /api/crm/proposals/ab-testing/tests/:id
 * @desc Get an A/B test by ID
 * @access Private
 */
router.get(
  '/ab-testing/tests/:id',
  tenantAccessMiddleware,
  validateRequest(
    z.object({
      params: z.object({
        id: z.string(),
      }),
    })
  ),
  async (req, res) => {
    try {
      const { id } = req.params;
      const tenantId = req.tenantId;

      if (!tenantId) {
        return res.status(400).json({ message: 'Tenant ID is required' });
      }

      // Get the A/B test
      const test = await ABTestingService.getTest(id, tenantId);

      if (!test) {
        return res.status(404).json({ message: 'A/B test not found' });
      }

      return res.json(test);
    } catch (error) {
      console.error('Error getting A/B test:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  }
);

/**
 * @route PUT /api/crm/proposals/ab-testing/tests/:id
 * @desc Update an A/B test
 * @access Private
 */
router.put(
  '/ab-testing/tests/:id',
  tenantAccessMiddleware,
  validateRequest(
    z.object({
      params: z.object({
        id: z.string(),
      }),
      body: UpdateABTestSchema,
    })
  ),
  async (req, res) => {
    try {
      const { id } = req.params;
      const updates = {
        ...req.body,
        endDate: req.body.endDate ? new Date(req.body.endDate) : undefined,
      };
      const tenantId = req.tenantId;

      if (!tenantId) {
        return res.status(400).json({ message: 'Tenant ID is required' });
      }

      // Update the A/B test
      const test = await ABTestingService.updateTest(id, updates, tenantId);

      if (!test) {
        return res.status(404).json({ message: 'A/B test not found' });
      }

      return res.json(test);
    } catch (error) {
      console.error('Error updating A/B test:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  }
);

/**
 * @route DELETE /api/crm/proposals/ab-testing/tests/:id
 * @desc Delete an A/B test
 * @access Private
 */
router.delete(
  '/ab-testing/tests/:id',
  tenantAccessMiddleware,
  validateRequest(
    z.object({
      params: z.object({
        id: z.string(),
      }),
      query: z.object({
        deleteVariants: z.enum(['true', 'false']).optional(),
      }),
    })
  ),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { deleteVariants } = req.query;
      const tenantId = req.tenantId;

      if (!tenantId) {
        return res.status(400).json({ message: 'Tenant ID is required' });
      }

      // Delete the A/B test
      const success = await ABTestingService.deleteTest(
        id,
        tenantId,
        deleteVariants === 'true'
      );

      if (!success) {
        return res.status(404).json({ message: 'A/B test not found' });
      }

      return res.json({ message: 'A/B test deleted successfully' });
    } catch (error) {
      console.error('Error deleting A/B test:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  }
);

/**
 * @route GET /api/crm/proposals/ab-testing/tests/:id/results
 * @desc Get A/B test results
 * @access Private
 */
router.get(
  '/ab-testing/tests/:id/results',
  tenantAccessMiddleware,
  validateRequest(
    z.object({
      params: z.object({
        id: z.string(),
      }),
    })
  ),
  async (req, res) => {
    try {
      const { id } = req.params;
      const tenantId = req.tenantId;

      if (!tenantId) {
        return res.status(400).json({ message: 'Tenant ID is required' });
      }

      // Get test results
      const results = await ABTestingService.getTestResults(id, tenantId);

      return res.json(results);
    } catch (error) {
      console.error('Error getting A/B test results:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  }
);

/**
 * @route GET /api/crm/proposals/ab-testing/tests/:id/proposal
 * @desc Get a proposal for an A/B test
 * @access Public
 */
router.get(
  '/ab-testing/tests/:id/proposal',
  validateRequest(
    z.object({
      params: z.object({
        id: z.string(),
      }),
    })
  ),
  async (req, res) => {
    try {
      const { id } = req.params;
      const tenantId = req.query.tenantId as string;

      if (!tenantId) {
        return res.status(400).json({ message: 'Tenant ID is required' });
      }

      // Get a proposal for the A/B test
      const proposalId = await ABTestingService.getTestProposal(id, tenantId);

      return res.json({ proposalId });
    } catch (error) {
      console.error('Error getting A/B test proposal:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  }
);

export default router;
