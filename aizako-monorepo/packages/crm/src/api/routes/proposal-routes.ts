import { Router } from 'express';
// Import the tenantAccessMiddleware from our local implementation
import { tenantAccessMiddleware } from '../middleware/tenant-middleware';
import { ProposalService } from '../../services/proposal-service';
import { createPaginationResult } from '../../types/pagination';
import { validateRequest, validateRequestMultiple } from '../middleware/validation';
import { createTenantRateLimiter } from '../middleware/rate-limiter';
import { errorHandler } from '../middleware/error-handler';
import {
  CreateProposalSchema,
  UpdateProposalSchema,
  ProposalDownloadOptionsSchema,
  ProposalSendOptionsSchema,
} from '../../types/proposals/schemas';
import { ProposalDownloadOptions } from '../../types/proposals';
import { z } from 'zod';
import proposalVersionsRoutes from './proposal-versions';
import proposalAnalyticsRoutes from './proposal-analytics';
import proposalABTestingRoutes from './proposal-ab-testing';

// Extend the Express Request type to include tenantId and userId
declare global {
  namespace Express {
    interface Request {
      tenantId?: string;
      userId?: string;
    }
  }
}

// Create router
const router = Router();

// Use version history routes
router.use('/', proposalVersionsRoutes);

// Use analytics routes
router.use('/', proposalAnalyticsRoutes);

// Use A/B testing routes
router.use('/', proposalABTestingRoutes);

// Create rate limiter for public routes
const publicRateLimiter = createTenantRateLimiter({
  points: 100,
  duration: 60,
  keyPrefix: 'proposal_public',
});

/**
 * @route GET /api/crm/proposals
 * @desc Get all proposals with pagination
 * @access Private
 */
router.get('/', tenantAccessMiddleware, validateRequest(
  z.object({
    page: z.coerce.number().int().positive().optional().default(1),
    limit: z.coerce.number().int().positive().optional().default(10),
    sortBy: z.string().optional().default('createdAt'),
    sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
    status: z.string().optional(),
    opportunityId: z.string().optional(),
    companyId: z.string().optional(),
    contactId: z.string().optional(),
    search: z.string().optional(),
  }),
  'query'
), async (req, res) => {
  try {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      ...filters
    } = req.query;

    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const { proposals, total } = await ProposalService.getProposals(
      {
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        ...filters,
      },
      tenantId
    );

    const result = createPaginationResult(
      proposals,
      total,
      Number(page),
      Number(limit)
    );

    return res.json(result);
  } catch (error) {
    console.error('Error getting proposals:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/proposals/:id
 * @desc Get proposal by ID
 * @access Private
 */
router.get('/:id', tenantAccessMiddleware, validateRequest(
  z.object({
    id: z.string(),
  }),
  'params'
), async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const proposal = await ProposalService.getProposalById(id, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error getting proposal by ID:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/proposals/public/:token
 * @desc Get proposal by public token (public access)
 * @access Public
 */
router.get('/public/:token', publicRateLimiter, validateRequest(
  z.object({
    token: z.string(),
  }),
  'params'
), async (req, res) => {
  try {
    const { token } = req.params;

    const proposal = await ProposalService.getProposalByToken(token);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found or access denied' });
    }

    // Record view
    const analyticsEvent = {
      eventType: 'view',
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
    };

    if (proposal._id) {
      await ProposalService.recordView(proposal._id.toString(), analyticsEvent);
    }

    // Return a sanitized version of the proposal for public viewing
    const publicProposal = {
      _id: proposal._id,
      title: proposal.title,
      description: proposal.description,
      status: proposal.status,
      sections: proposal.sections.filter(section => section.isVisible),
      pricing: proposal.pricing,
      terms: proposal.terms,
      downloadEnabled: proposal.downloadEnabled,
      downloadFormats: proposal.downloadFormats,
      createdAt: proposal.createdAt,
      updatedAt: proposal.updatedAt,
    };

    return res.json(publicProposal);
  } catch (error) {
    console.error('Error getting proposal by token:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/proposals
 * @desc Create a new proposal
 * @access Private
 */
router.post('/', tenantAccessMiddleware, validateRequest(CreateProposalSchema), async (req, res) => {
  try {
    const proposalData = req.body;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const proposal = await ProposalService.createProposal(proposalData, tenantId);

    return res.status(201).json(proposal);
  } catch (error) {
    console.error('Error creating proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/crm/proposals/:id
 * @desc Update a proposal
 * @access Private
 */
router.put('/:id', tenantAccessMiddleware, validateRequestMultiple({
  params: z.object({
    id: z.string(),
  }),
  body: UpdateProposalSchema.extend({
    versionComment: z.string().optional(),
  }),
}), async (req, res) => {
  try {
    const { id } = req.params;
    const { versionComment, ...proposalData } = req.body;
    const tenantId = req.tenantId;
    const userId = req.userId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const proposal = await ProposalService.updateProposal(
      id,
      proposalData,
      tenantId,
      userId,
      versionComment
    );

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error updating proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/crm/proposals/:id
 * @desc Delete a proposal
 * @access Private
 */
router.delete('/:id', tenantAccessMiddleware, validateRequest(
  z.object({
    id: z.string(),
  }),
  'params'
), async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const deleted = await ProposalService.deleteProposal(id, tenantId);

    if (!deleted) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json({ message: 'Proposal deleted successfully' });
  } catch (error) {
    console.error('Error deleting proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/crm/proposals/:id/send
 * @desc Send a proposal
 * @access Private
 */
router.post('/:id/send', tenantAccessMiddleware, validateRequestMultiple({
  params: z.object({
    id: z.string(),
  }),
  body: ProposalSendOptionsSchema,
}), async (req, res) => {
  try {
    const { id } = req.params;
    const emailData = req.body;
    const tenantId = req.tenantId;
    const userId = req.userId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const proposal = await ProposalService.sendProposal(id, userId, tenantId, emailData);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    return res.json(proposal);
  } catch (error) {
    console.error('Error sending proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/crm/proposals/:id/download
 * @desc Download a proposal in the specified format
 * @access Private
 */
router.get('/:id/download', tenantAccessMiddleware, validateRequestMultiple({
  params: z.object({
    id: z.string(),
  }),
  query: ProposalDownloadOptionsSchema,
}), async (req, res) => {
  try {
    const { id } = req.params;
    // Parse download options with proper typing
    const options: ProposalDownloadOptions = {
      format: (req.query.format as 'pdf' | 'docx' | 'md') || 'pdf',
      includeHeader: req.query.includeHeader !== 'false',
      includeFooter: req.query.includeFooter !== 'false',
      includeBranding: req.query.includeBranding !== 'false',
      includePageNumbers: req.query.includePageNumbers !== 'false',
      colorScheme: req.query.colorScheme?.toString() || 'default',
      paperSize: (req.query.paperSize as 'a4' | 'letter' | 'legal') || 'a4',
      skipCache: req.query.skipCache as 'true' | 'false' | undefined,
    };
    const tenantId = req.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: 'Tenant ID is required' });
    }

    const proposal = await ProposalService.getProposalById(id, tenantId);

    if (!proposal) {
      return res.status(404).json({ message: 'Proposal not found' });
    }

    // Check if the requested format is enabled for this proposal
    if (!proposal.downloadEnabled || !proposal.downloadFormats?.includes(options.format)) {
      return res.status(403).json({ message: `Download in ${options.format} format is not enabled for this proposal` });
    }

    // Generate the document (use cache by default)
    const document = await ProposalService.generateProposalDocument(
      proposal,
      options,
      // Skip cache if explicitly requested in the query
      options.skipCache === 'true'
    );

    // Set appropriate content type and filename
    let contentType = 'text/plain';
    let fileExtension = options.format;

    if (options.format === 'pdf') {
      contentType = 'application/pdf';
    } else if (options.format === 'docx') {
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (options.format === 'md') {
      contentType = 'text/markdown';
    }

    const sanitizedTitle = proposal.title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const filename = `proposal_${sanitizedTitle}.${fileExtension}`;

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    return res.send(document);
  } catch (error) {
    console.error('Error downloading proposal:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

export default router;
