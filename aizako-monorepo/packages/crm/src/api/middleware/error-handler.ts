import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { MongoError } from 'mongodb';
import mongoose from 'mongoose';
import {
  AppError,
  NotFoundError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  ConflictError,
  DatabaseError,
  formatError,
  toAppError
} from '../../utils/errors';
import { logger } from '../../utils/logger';

/**
 * Error handler middleware
 *
 * This middleware handles errors and sends appropriate responses.
 * It handles different types of errors and formats them consistently.
 *
 * @param err Error object
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Convert error to AppError
  const appError = toAppError(err);

  // Log error
  if (appError.isOperational) {
    // Operational errors are expected and don't need stack traces
    logger.warn({
      message: `Operational error: ${appError.message}`,
      errorCode: appError.errorCode,
      statusCode: appError.statusCode,
      path: req.path,
      method: req.method,
      ...(req.user ? { userId: req.user.id } : {}),
      ...(req.tenantId ? { tenantId: req.tenantId } : {}),
    });
  } else {
    // Programming errors are unexpected and need stack traces
    logger.error({
      message: `Unhandled error: ${appError.message}`,
      errorCode: appError.errorCode,
      statusCode: appError.statusCode,
      path: req.path,
      method: req.method,
      stack: appError.stack,
      ...(req.user ? { userId: req.user.id } : {}),
      ...(req.tenantId ? { tenantId: req.tenantId } : {}),
    });
  }

  // Format error for response
  const formattedError = formatError(appError);

  // Send response
  res.status(appError.statusCode).json(formattedError);
};

/**
 * Not found middleware
 *
 * This middleware handles 404 errors for routes that don't exist.
 *
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const error = new NotFoundError(
    `Not found: ${req.method} ${req.originalUrl}`,
    'ROUTE_NOT_FOUND'
  );
  next(error);
};

/**
 * Async handler wrapper
 *
 * This utility wraps async route handlers to catch errors
 * and pass them to the error handler middleware.
 *
 * @param fn Async route handler function
 * @returns Wrapped route handler
 *
 * @example
 * ```typescript
 * router.get('/users', asyncHandler(async (req, res) => {
 *   const users = await userService.getUsers();
 *   res.json({ users });
 * }));
 * ```
 */
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Zod validation error handler
 *
 * This utility handles Zod validation errors and formats them
 * into a consistent response format.
 *
 * @param error Zod error
 * @returns Formatted error object
 */
export function handleZodError(error: ZodError) {
  const details = error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message,
  }));

  return {
    success: false,
    error: 'VALIDATION_ERROR',
    message: 'Validation failed',
    details,
  };
}

export default {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  handleZodError,
};
