import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to check if a module is available for a tenant
 * @param module Module name to check
 * @returns Express middleware function
 */
export function moduleMiddleware(module: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    // In a real implementation, this would check if the module is available for the tenant
    // For now, we'll just allow all modules
    next();
  };
}
