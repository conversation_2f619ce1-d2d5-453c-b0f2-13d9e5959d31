import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to validate tenant access
 * 
 * This middleware can be used in two ways:
 * 1. With a tenant service: tenantAccessMiddleware(tenantService)
 * 2. As a standalone middleware: tenantAccessMiddleware()
 * 
 * When used as a standalone middleware, it only validates that a tenant ID
 * is present in the request headers and attaches it to the request object.
 * 
 * @param tenantService Optional tenant service to validate user access to tenant
 */
export function tenantAccessMiddleware(tenantService?: any) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Extract tenant ID from request
      const tenantId = req.headers['x-tenant-id'] as string;

      if (!tenantId) {
        res.status(401).json({
          success: false,
          error: 'Tenant ID is required',
          message: 'Please provide a tenant ID in the x-tenant-id header'
        });
        return;
      }

      // Attach tenant ID to request
      (req as any).tenantId = tenantId;

      // If no tenant service is provided, skip access validation
      if (!tenantService) {
        next();
        return;
      }

      // Get user ID from authenticated user
      const userId = (req as any).user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          message: 'You must be authenticated to access this resource'
        });
        return;
      }

      // Check if user has access to tenant
      const hasAccess = await tenantService.checkUserTenantAccess(userId, tenantId);

      if (!hasAccess) {
        res.status(403).json({
          success: false,
          error: 'Access denied',
          message: 'You do not have access to this tenant'
        });
        return;
      }

      // Continue to next middleware
      next();
    } catch (error: any) {
      console.error('Error in tenant access middleware:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message || 'An error occurred while validating tenant access'
      });
    }
  };
}

/**
 * Middleware to ensure tenant isolation
 * This middleware extracts the tenant ID from the request headers
 * and attaches it to the request object.
 */
export function tenantIsolationMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Extract tenant ID from request
  const tenantId = req.headers['x-tenant-id'] as string;

  if (!tenantId) {
    res.status(401).json({
      success: false,
      error: 'Tenant ID is required',
      message: 'Please provide a tenant ID in the x-tenant-id header'
    });
    return;
  }

  // Attach tenant ID to request
  (req as any).tenantId = tenantId;

  // Continue to next middleware
  next();
}
