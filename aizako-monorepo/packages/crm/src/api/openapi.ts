/**
 * OpenAPI configuration for Aizako CRM API
 * 
 * This module configures Swagger/OpenAPI documentation for the API.
 */
import { OpenAPIRegistry } from 'zod-to-openapi';
import { extendZodWithOpenApi } from 'zod-to-openapi';
import { z } from 'zod';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

// Extend Zod with OpenAPI functionality
extendZodWithOpenApi(z);

// Create OpenAPI registry
export const registry = new OpenAPIRegistry();

// Define common schemas
const ErrorResponse = registry.register(
  'ErrorResponse',
  z.object({
    success: z.boolean().default(false),
    error: z.string().describe('Error code'),
    message: z.string().describe('Human-readable error message'),
    details: z.array(z.any()).optional().describe('Additional error details'),
  })
);

const PaginationParams = registry.register(
  'PaginationParams',
  z.object({
    page: z.number().int().min(1).default(1).describe('Page number'),
    pageSize: z.number().int().min(1).max(100).default(20).describe('Items per page'),
    sort: z.string().optional().describe('Field to sort by'),
    direction: z.enum(['asc', 'desc']).default('asc').describe('Sort direction'),
  })
);

const PaginationResponse = registry.register(
  'PaginationResponse',
  z.object({
    page: z.number().int().describe('Current page number'),
    pageSize: z.number().int().describe('Items per page'),
    totalItems: z.number().int().describe('Total number of items'),
    totalPages: z.number().int().describe('Total number of pages'),
  })
);

// Define API documentation
const apiDocumentation = {
  openapi: '3.0.0',
  info: {
    title: 'Aizako CRM API',
    version: '1.0.0',
    description: 'API documentation for Aizako CRM',
    license: {
      name: 'Proprietary',
      url: 'https://aizako.com',
    },
    contact: {
      name: 'Aizako Support',
      url: 'https://aizako.com/support',
      email: '<EMAIL>',
    },
  },
  servers: [
    {
      url: '/api',
      description: 'API server',
    },
  ],
  tags: [
    {
      name: 'Auth',
      description: 'Authentication endpoints',
    },
    {
      name: 'Contacts',
      description: 'Contact management endpoints',
    },
    {
      name: 'Companies',
      description: 'Company management endpoints',
    },
    {
      name: 'Opportunities',
      description: 'Opportunity management endpoints',
    },
    {
      name: 'Activities',
      description: 'Activity management endpoints',
    },
    {
      name: 'Proposals',
      description: 'Proposal management endpoints',
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
    schemas: {
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            default: false,
          },
          error: {
            type: 'string',
            description: 'Error code',
          },
          message: {
            type: 'string',
            description: 'Human-readable error message',
          },
          details: {
            type: 'array',
            items: {
              type: 'object',
            },
            description: 'Additional error details',
          },
        },
        required: ['success', 'error', 'message'],
      },
    },
    parameters: {
      tenantId: {
        name: 'x-tenant-id',
        in: 'header',
        description: 'Tenant ID',
        required: true,
        schema: {
          type: 'string',
        },
      },
    },
    responses: {
      UnauthorizedError: {
        description: 'Access token is missing or invalid',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      ForbiddenError: {
        description: 'Forbidden access',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      NotFoundError: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      ValidationError: {
        description: 'Validation failed',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      ServerError: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
};

// Generate OpenAPI specification
export const swaggerSpec = swaggerJsdoc({
  definition: apiDocumentation,
  apis: ['./src/api/routes/*.ts'], // Path to API routes
});

/**
 * Set up Swagger UI
 * @param app Express application
 */
export function setupSwagger(app: Express): void {
  // Serve Swagger UI
  app.use(
    '/api-docs',
    swaggerUi.serve,
    swaggerUi.setup(swaggerSpec, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
    })
  );
  
  // Serve OpenAPI specification as JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
}

export default {
  registry,
  swaggerSpec,
  setupSwagger,
};
