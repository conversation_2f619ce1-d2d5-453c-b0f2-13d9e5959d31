/**
 * Base repository interface
 *
 * This interface defines the common operations that all repositories should implement.
 * It follows the repository pattern, which abstracts the data access layer from the business logic.
 *
 * The repository pattern provides several benefits:
 * 1. Decouples business logic from data access
 * 2. Centralizes data access logic
 * 3. Makes testing easier with mock repositories
 * 4. Provides a consistent interface for data access
 *
 * Each repository implementation should:
 * - Implement all methods defined in this interface
 * - Use data mappers to convert between domain entities and database models
 * - Handle errors consistently
 * - Enforce tenant isolation
 *
 * @see {@link IRepository} for the base repository interface
 * @see {@link MongoDBRepository} for the MongoDB implementation
 * @see {@link ../docs/repository-pattern.md} for more information on the repository pattern
 * @see {@link ../docs/data-mapper-pattern.md} for more information on the data mapper pattern
 */
import { TenantScopedEntity } from '../types/shared';
import { QueryOptions } from '../types/query';

/**
 * Base repository interface
 *
 * This interface defines the standard operations to be performed on a domain entity.
 * All repository implementations should implement this interface to provide
 * a consistent API for data access.
 *
 * @template T Entity type - The domain entity type this repository manages
 * @template K ID type - The type of the entity's ID (defaults to string)
 *
 * @example
 * ```typescript
 * // Define a repository interface for contacts
 * interface IContactRepository extends IRepository<ContactEntity> {
 *   findByEmail(email: string, tenantId: string): Promise<ContactEntity | null>;
 * }
 *
 * // Implement the repository
 * class ContactRepository extends MongoDBRepository<ContactEntity, any> implements IContactRepository {
 *   constructor() {
 *     super(Contact, 'Contact');
 *     this.setMapper(contactMapper);
 *   }
 *
 *   async findByEmail(email: string, tenantId: string): Promise<ContactEntity | null> {
 *     // Implementation...
 *   }
 * }
 * ```
 */
export interface IRepository<T extends TenantScopedEntity, K = string> {
  /**
   * Find an entity by ID
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns Entity or null if not found
   */
  findById(id: K, tenantId: string): Promise<T | null>;

  /**
   * Find all entities that match the filter
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of entities
   */
  findAll(tenantId: string, options?: QueryOptions): Promise<T[]>;

  /**
   * Find all entities that match the filter with pagination
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   */
  findAllPaginated(
    tenantId: string,
    options?: QueryOptions
  ): Promise<{ items: T[]; total: number; page: number; limit: number; totalPages: number }>;

  /**
   * Create a new entity
   * @param entity Entity to create
   * @returns Created entity
   */
  create(entity: Partial<T>): Promise<T>;

  /**
   * Update an entity
   * @param id Entity ID
   * @param entity Entity data to update
   * @param tenantId Tenant ID
   * @returns Updated entity or null if not found
   */
  update(id: K, entity: Partial<T>, tenantId: string): Promise<T | null>;

  /**
   * Delete an entity
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  delete(id: K, tenantId: string): Promise<boolean>;

  /**
   * Count entities that match the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Count of matching entities
   */
  count(tenantId: string, filter?: Record<string, any>): Promise<number>;

  /**
   * Check if an entity exists
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if exists, false otherwise
   */
  exists(id: K, tenantId: string): Promise<boolean>;

  /**
   * Find one entity that matches the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Entity or null if not found
   */
  findOne(tenantId: string, filter: Record<string, any>): Promise<T | null>;
}

export default IRepository;
