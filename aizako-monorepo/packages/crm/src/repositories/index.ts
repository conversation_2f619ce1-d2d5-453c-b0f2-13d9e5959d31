/**
 * Repository exports
 */

// Export base repository interfaces
export * from './base-repository';
export * from './mongodb-repository';

// Export specific repositories
export * from './contact-repository';
export * from './company-repository';

// Export repository instances
import ContactRepository from './contact-repository';
import CompanyRepository from './company-repository';

// Create repository instances
export const contactRepository = new ContactRepository();
export const companyRepository = new CompanyRepository();

// Export default repositories object
export default {
  contactRepository,
  companyRepository,
};
