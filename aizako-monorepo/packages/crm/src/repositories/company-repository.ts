/**
 * Company repository
 *
 * This repository handles data access for companies.
 */
import { MongoDBRepository } from './mongodb-repository';
import { Company, ICompany } from '../models/company';
import { QueryOptions } from '../types/query';
import { withTransaction } from '../utils/transactions';
import { logger } from '../utils/logger';
import { DatabaseError } from '../utils/errors';
import companyMapper, { CompanyEntity } from '../mappers/company-mapper';

/**
 * Company repository interface
 */
export interface ICompanyRepository {
  /**
   * Find companies by industry
   * @param industry Industry
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Companies
   */
  findByIndustry(industry: string, tenantId: string, options?: QueryOptions): Promise<CompanyEntity[]>;

  /**
   * Find company by name
   * @param name Company name
   * @param tenantId Tenant ID
   * @returns Company or null if not found
   */
  findByName(name: string, tenantId: string): Promise<CompanyEntity | null>;

  /**
   * Search companies by name or industry
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated companies
   */
  search(
    query: string,
    tenantId: string,
    options?: QueryOptions
  ): Promise<{ items: CompanyEntity[]; total: number; page: number; limit: number; totalPages: number }>;

  /**
   * Add a tag to a company
   * @param companyId Company ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated company
   */
  addTag(companyId: string, tagId: string, tenantId: string): Promise<CompanyEntity | null>;

  /**
   * Remove a tag from a company
   * @param companyId Company ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated company
   */
  removeTag(companyId: string, tagId: string, tenantId: string): Promise<CompanyEntity | null>;
}

/**
 * Company repository implementation
 */
export class CompanyRepository extends MongoDBRepository<CompanyEntity, any> implements ICompanyRepository {
  /**
   * Create a new company repository
   */
  constructor() {
    super(Company, 'Company');
    this.setMapper(companyMapper);
  }

  /**
   * Find companies by industry
   * @param industry Industry
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Companies
   */
  async findByIndustry(industry: string, tenantId: string, options: QueryOptions = {}): Promise<ICompany[]> {
    try {
      const { sort = {}, projection = {} } = options;

      const query = Company.find({
        industry,
        tenantId,
      });

      if (Object.keys(sort).length > 0) {
        query.sort(sort);
      }

      if (Object.keys(projection).length > 0) {
        query.select(projection);
      }

      const docs = await query.exec();

      return docs.map(doc => this.toEntity(doc));
    } catch (error) {
      logger.error('Error finding companies by industry:', { error, industry, tenantId });
      throw new DatabaseError(`Error finding companies by industry: ${error.message}`);
    }
  }

  /**
   * Find company by name
   * @param name Company name
   * @param tenantId Tenant ID
   * @returns Company or null if not found
   */
  async findByName(name: string, tenantId: string): Promise<ICompany | null> {
    try {
      const doc = await Company.findOne({
        name,
        tenantId,
      });

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error('Error finding company by name:', { error, name, tenantId });
      throw new DatabaseError(`Error finding company by name: ${error.message}`);
    }
  }

  /**
   * Search companies by name or industry
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated companies
   */
  async search(
    query: string,
    tenantId: string,
    options: QueryOptions = {}
  ): Promise<{ items: ICompany[]; total: number; page: number; limit: number; totalPages: number }> {
    try {
      const {
        sort = {},
        projection = {},
        page = 1,
        limit = 20,
      } = options;

      // Create search filter
      const searchFilter = {
        tenantId,
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { industry: { $regex: query, $options: 'i' } },
          { website: { $regex: query, $options: 'i' } },
        ],
      };

      const [docs, total] = await Promise.all([
        Company.find(searchFilter)
          .sort(sort)
          .select(projection)
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        Company.countDocuments(searchFilter),
      ]);

      const items = docs.map(doc => this.toEntity(doc));
      const totalPages = Math.ceil(total / limit);

      return {
        items,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      logger.error('Error searching companies:', { error, query, tenantId });
      throw new DatabaseError(`Error searching companies: ${error.message}`);
    }
  }

  /**
   * Add a tag to a company
   * @param companyId Company ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated company
   */
  async addTag(companyId: string, tagId: string, tenantId: string): Promise<ICompany | null> {
    try {
      const doc = await Company.findOneAndUpdate(
        { _id: companyId, tenantId },
        { $addToSet: { tags: tagId } },
        { new: true }
      );

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error('Error adding tag to company:', { error, companyId, tagId, tenantId });
      throw new DatabaseError(`Error adding tag to company: ${error.message}`);
    }
  }

  /**
   * Remove a tag from a company
   * @param companyId Company ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated company
   */
  async removeTag(companyId: string, tagId: string, tenantId: string): Promise<ICompany | null> {
    try {
      const doc = await Company.findOneAndUpdate(
        { _id: companyId, tenantId },
        { $pull: { tags: tagId } },
        { new: true }
      );

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error('Error removing tag from company:', { error, companyId, tagId, tenantId });
      throw new DatabaseError(`Error removing tag from company: ${error.message}`);
    }
  }
}

export default CompanyRepository;
