/**
 * Contact repository
 *
 * This repository handles data access for contacts.
 */
import { MongoDBRepository } from './mongodb-repository';
import { Contact, IContact } from '../models/contact';
import { QueryOptions } from '../types/query';
import { withTransaction } from '../utils/transactions';
import { logger } from '../utils/logger';
import { DatabaseError } from '../utils/errors';
import contactMapper, { ContactEntity } from '../mappers/contact-mapper';

/**
 * Contact repository interface
 */
export interface IContactRepository {
  /**
   * Find contacts by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Contacts
   */
  findByCompanyId(companyId: string, tenantId: string, options?: QueryOptions): Promise<ContactEntity[]>;

  /**
   * Find contacts by email
   * @param email Email
   * @param tenantId Tenant ID
   * @returns Contact or null if not found
   */
  findByEmail(email: string, tenantId: string): Promise<ContactEntity | null>;

  /**
   * Search contacts by name or email
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated contacts
   */
  search(
    query: string,
    tenantId: string,
    options?: QueryOptions
  ): Promise<{ items: ContactEntity[]; total: number; page: number; limit: number; totalPages: number }>;

  /**
   * Add a tag to a contact
   * @param contactId Contact ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  addTag(contactId: string, tagId: string, tenantId: string): Promise<ContactEntity | null>;

  /**
   * Remove a tag from a contact
   * @param contactId Contact ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  removeTag(contactId: string, tagId: string, tenantId: string): Promise<ContactEntity | null>;
}

/**
 * Contact repository implementation
 */
export class ContactRepository extends MongoDBRepository<ContactEntity, any> implements IContactRepository {
  /**
   * Create a new contact repository
   */
  constructor() {
    super(Contact, 'Contact');
    this.setMapper(contactMapper);
  }

  /**
   * Find contacts by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Contacts
   */
  async findByCompanyId(companyId: string, tenantId: string, options: QueryOptions = {}): Promise<IContact[]> {
    try {
      const { sort = {}, projection = {} } = options;

      const query = Contact.find({
        companyId,
        tenantId,
      });

      if (Object.keys(sort).length > 0) {
        query.sort(sort);
      }

      if (Object.keys(projection).length > 0) {
        query.select(projection);
      }

      const docs = await query.exec();

      return docs.map(doc => this.toEntity(doc));
    } catch (error) {
      logger.error('Error finding contacts by company ID:', { error, companyId, tenantId });
      throw new DatabaseError(`Error finding contacts by company ID: ${error.message}`);
    }
  }

  /**
   * Find contacts by email
   * @param email Email
   * @param tenantId Tenant ID
   * @returns Contact or null if not found
   */
  async findByEmail(email: string, tenantId: string): Promise<IContact | null> {
    try {
      const doc = await Contact.findOne({
        email,
        tenantId,
      });

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error('Error finding contact by email:', { error, email, tenantId });
      throw new DatabaseError(`Error finding contact by email: ${error.message}`);
    }
  }

  /**
   * Search contacts by name or email
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated contacts
   */
  async search(
    query: string,
    tenantId: string,
    options: QueryOptions = {}
  ): Promise<{ items: IContact[]; total: number; page: number; limit: number; totalPages: number }> {
    try {
      const {
        sort = {},
        projection = {},
        page = 1,
        limit = 20,
      } = options;

      // Create search filter
      const searchFilter = {
        tenantId,
        $or: [
          { firstName: { $regex: query, $options: 'i' } },
          { lastName: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
        ],
      };

      const [docs, total] = await Promise.all([
        Contact.find(searchFilter)
          .sort(sort)
          .select(projection)
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        Contact.countDocuments(searchFilter),
      ]);

      const items = docs.map(doc => this.toEntity(doc));
      const totalPages = Math.ceil(total / limit);

      return {
        items,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      logger.error('Error searching contacts:', { error, query, tenantId });
      throw new DatabaseError(`Error searching contacts: ${error.message}`);
    }
  }

  /**
   * Add a tag to a contact
   * @param contactId Contact ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  async addTag(contactId: string, tagId: string, tenantId: string): Promise<IContact | null> {
    try {
      const doc = await Contact.findOneAndUpdate(
        { _id: contactId, tenantId },
        { $addToSet: { tags: tagId } },
        { new: true }
      );

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error('Error adding tag to contact:', { error, contactId, tagId, tenantId });
      throw new DatabaseError(`Error adding tag to contact: ${error.message}`);
    }
  }

  /**
   * Remove a tag from a contact
   * @param contactId Contact ID
   * @param tagId Tag ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   */
  async removeTag(contactId: string, tagId: string, tenantId: string): Promise<IContact | null> {
    try {
      const doc = await Contact.findOneAndUpdate(
        { _id: contactId, tenantId },
        { $pull: { tags: tagId } },
        { new: true }
      );

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error('Error removing tag from contact:', { error, contactId, tagId, tenantId });
      throw new DatabaseError(`Error removing tag from contact: ${error.message}`);
    }
  }
}

export default ContactRepository;
