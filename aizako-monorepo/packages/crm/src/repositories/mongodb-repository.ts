/**
 * MongoDB repository implementation
 *
 * This class implements the base repository interface for MongoDB using Mongoose.
 * It provides a generic implementation that can be used for any entity.
 *
 * Key features:
 * - Implements all methods defined in the IRepository interface
 * - Uses data mappers to convert between domain entities and database models
 * - Handles errors consistently with custom error classes
 * - Enforces tenant isolation for all operations
 * - Provides logging for all database operations
 *
 * Usage:
 * 1. Create a specific repository class that extends this class
 * 2. Pass the Mongoose model and entity name to the constructor
 * 3. Set a mapper to handle conversion between domain entities and database models
 * 4. Implement any domain-specific methods in the derived class
 *
 * @example
 * ```typescript
 * export class ContactRepository extends MongoDBRepository<ContactEntity, any> {
 *   constructor() {
 *     super(Contact, 'Contact');
 *     this.setMapper(contactMapper);
 *   }
 *
 *   async findByEmail(email: string, tenantId: string): Promise<ContactEntity | null> {
 *     try {
 *       const doc = await Contact.findOne({ email, tenantId });
 *       return doc ? this.toEntity(doc) : null;
 *     } catch (error) {
 *       logger.error('Error finding contact by email:', { error, email, tenantId });
 *       throw new DatabaseError(`Error finding contact by email: ${error.message}`);
 *     }
 *   }
 * }
 * ```
 *
 * @see {@link IRepository} for the base repository interface
 * @see {@link ../docs/repository-pattern.md} for more information on the repository pattern
 * @see {@link ../docs/data-mapper-pattern.md} for more information on the data mapper pattern
 */
import mongoose, { Document, Model } from 'mongoose';
import { IRepository } from './base-repository';
import { TenantScopedEntity } from '../types/shared';
import { QueryOptions } from '../types/query';
import { DatabaseError } from '../utils/errors';
import { withTransaction } from '../utils/transactions';
import { logger } from '../utils/logger';

/**
 * MongoDB repository implementation
 *
 * This class provides a concrete implementation of the IRepository interface for MongoDB.
 * It handles the conversion between domain entities and database models using data mappers.
 *
 * Features:
 * - Generic implementation that works with any Mongoose model
 * - Support for data mappers to handle entity conversion
 * - Consistent error handling and logging
 * - Tenant isolation for multi-tenant applications
 * - Pagination, sorting, and filtering support
 *
 * @template T Entity type - The domain entity type this repository manages
 * @template D Document type - The Mongoose document type for the database model
 *
 * @implements {IRepository<T>}
 */
export class MongoDBRepository<T extends TenantScopedEntity, D extends Document> implements IRepository<T> {
  /**
   * Mongoose model
   */
  protected model: Model<D>;

  /**
   * Entity name for logging
   */
  protected entityName: string;

  /**
   * Create a new MongoDB repository
   * @param model Mongoose model
   * @param entityName Entity name for logging
   */
  constructor(model: Model<D>, entityName: string) {
    this.model = model;
    this.entityName = entityName;
  }

  /**
   * Entity mapper for converting between domain entities and database models
   *
   * The mapper provides two methods:
   * - toEntity: Converts a database model to a domain entity
   * - toModel: Converts a domain entity to a database model
   */
  protected mapper: {
    /**
     * Convert a database model to a domain entity
     */
    toEntity: (model: any) => T;

    /**
     * Convert a domain entity to a database model
     */
    toModel: (entity: Partial<T>) => Partial<D>;
  };

  /**
   * Set the entity mapper for this repository
   *
   * This method allows setting a custom mapper for converting between
   * domain entities and database models. The mapper should provide
   * toEntity and toModel methods.
   *
   * @param mapper Entity mapper with toEntity and toModel methods
   *
   * @example
   * ```typescript
   * // In a repository constructor
   * constructor() {
   *   super(Contact, 'Contact');
   *   this.setMapper(contactMapper);
   * }
   * ```
   */
  protected setMapper(mapper: {
    toEntity: (model: any) => T;
    toModel: (entity: Partial<T>) => Partial<D>;
  }): void {
    this.mapper = mapper;
  }

  /**
   * Convert a Mongoose document to a domain entity
   *
   * This method converts a database document to a domain entity using the
   * configured mapper. If no mapper is provided, it uses a default implementation
   * that performs basic conversions like changing _id to id.
   *
   * @param doc Mongoose document to convert
   * @returns Domain entity representation of the document
   *
   * @example
   * ```typescript
   * // Inside a repository method
   * const doc = await this.model.findOne({ _id: id, tenantId });
   * return doc ? this.toEntity(doc) : null;
   * ```
   */
  protected toEntity(doc: D): T {
    if (!doc) return null as any;

    if (this.mapper) {
      return this.mapper.toEntity(doc);
    }

    // Default implementation if no mapper is provided
    const entity = doc.toObject();

    // Convert _id to id
    if (entity._id) {
      entity.id = entity._id.toString();
      delete entity._id;
    }

    // Convert other ObjectId fields to strings
    Object.keys(entity).forEach(key => {
      if (entity[key] instanceof mongoose.Types.ObjectId) {
        entity[key] = entity[key].toString();
      }
    });

    return entity as unknown as T;
  }

  /**
   * Convert a domain entity to a Mongoose document
   *
   * This method converts a domain entity to a database document using the
   * configured mapper. If no mapper is provided, it uses a default implementation
   * that performs basic conversions like changing id to _id.
   *
   * @param entity Domain entity to convert
   * @returns Database document representation of the entity
   *
   * @example
   * ```typescript
   * // Inside a repository method
   * const doc = this.toDocument(entity);
   * return await this.model.create(doc);
   * ```
   */
  protected toDocument(entity: Partial<T>): Partial<D> {
    if (!entity) return null as any;

    if (this.mapper) {
      return this.mapper.toModel(entity);
    }

    // Default implementation if no mapper is provided
    const doc = { ...entity } as any;

    // Convert id to _id if present
    if (doc.id) {
      doc._id = doc.id;
      delete doc.id;
    }

    return doc as Partial<D>;
  }

  /**
   * Find an entity by ID
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns Entity or null if not found
   */
  async findById(id: string, tenantId: string): Promise<T | null> {
    try {
      const doc = await this.model.findOne({
        _id: id,
        tenantId,
      });

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error(`Error finding ${this.entityName} by ID:`, { error, id, tenantId });
      throw new DatabaseError(`Error finding ${this.entityName} by ID: ${error.message}`);
    }
  }

  /**
   * Find all entities that match the filter
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of entities
   */
  async findAll(tenantId: string, options: QueryOptions = {}): Promise<T[]> {
    try {
      const { filter = {}, sort = {}, projection = {} } = options;

      const query = this.model.find({
        tenantId,
        ...filter,
      });

      if (Object.keys(sort).length > 0) {
        query.sort(sort);
      }

      if (Object.keys(projection).length > 0) {
        query.select(projection);
      }

      const docs = await query.exec();

      return docs.map(doc => this.toEntity(doc));
    } catch (error) {
      logger.error(`Error finding ${this.entityName} list:`, { error, tenantId, options });
      throw new DatabaseError(`Error finding ${this.entityName} list: ${error.message}`);
    }
  }

  /**
   * Find all entities that match the filter with pagination
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   */
  async findAllPaginated(
    tenantId: string,
    options: QueryOptions = {}
  ): Promise<{ items: T[]; total: number; page: number; limit: number; totalPages: number }> {
    try {
      const {
        filter = {},
        sort = {},
        projection = {},
        page = 1,
        limit = 20,
      } = options;

      const query = {
        tenantId,
        ...filter,
      };

      const [docs, total] = await Promise.all([
        this.model.find(query)
          .sort(sort)
          .select(projection)
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        this.model.countDocuments(query),
      ]);

      const items = docs.map(doc => this.toEntity(doc));
      const totalPages = Math.ceil(total / limit);

      return {
        items,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      logger.error(`Error finding paginated ${this.entityName} list:`, { error, tenantId, options });
      throw new DatabaseError(`Error finding paginated ${this.entityName} list: ${error.message}`);
    }
  }

  /**
   * Create a new entity
   * @param entity Entity to create
   * @returns Created entity
   */
  async create(entity: Partial<T>): Promise<T> {
    try {
      const doc = new this.model(this.toDocument(entity));
      const savedDoc = await doc.save();

      return this.toEntity(savedDoc);
    } catch (error) {
      logger.error(`Error creating ${this.entityName}:`, { error, entity });
      throw new DatabaseError(`Error creating ${this.entityName}: ${error.message}`);
    }
  }

  /**
   * Update an entity
   * @param id Entity ID
   * @param entity Entity data to update
   * @param tenantId Tenant ID
   * @returns Updated entity or null if not found
   */
  async update(id: string, entity: Partial<T>, tenantId: string): Promise<T | null> {
    try {
      const doc = await this.model.findOneAndUpdate(
        { _id: id, tenantId },
        { $set: this.toDocument(entity) },
        { new: true }
      );

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error(`Error updating ${this.entityName}:`, { error, id, entity, tenantId });
      throw new DatabaseError(`Error updating ${this.entityName}: ${error.message}`);
    }
  }

  /**
   * Delete an entity
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string, tenantId: string): Promise<boolean> {
    try {
      const result = await this.model.deleteOne({ _id: id, tenantId });

      return result.deletedCount > 0;
    } catch (error) {
      logger.error(`Error deleting ${this.entityName}:`, { error, id, tenantId });
      throw new DatabaseError(`Error deleting ${this.entityName}: ${error.message}`);
    }
  }

  /**
   * Count entities that match the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Count of matching entities
   */
  async count(tenantId: string, filter: Record<string, any> = {}): Promise<number> {
    try {
      return await this.model.countDocuments({
        tenantId,
        ...filter,
      });
    } catch (error) {
      logger.error(`Error counting ${this.entityName}:`, { error, tenantId, filter });
      throw new DatabaseError(`Error counting ${this.entityName}: ${error.message}`);
    }
  }

  /**
   * Check if an entity exists
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if exists, false otherwise
   */
  async exists(id: string, tenantId: string): Promise<boolean> {
    try {
      const count = await this.model.countDocuments({
        _id: id,
        tenantId,
      });

      return count > 0;
    } catch (error) {
      logger.error(`Error checking if ${this.entityName} exists:`, { error, id, tenantId });
      throw new DatabaseError(`Error checking if ${this.entityName} exists: ${error.message}`);
    }
  }

  /**
   * Find one entity that matches the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Entity or null if not found
   */
  async findOne(tenantId: string, filter: Record<string, any>): Promise<T | null> {
    try {
      const doc = await this.model.findOne({
        tenantId,
        ...filter,
      });

      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      logger.error(`Error finding one ${this.entityName}:`, { error, tenantId, filter });
      throw new DatabaseError(`Error finding one ${this.entityName}: ${error.message}`);
    }
  }
}

export default MongoDBRepository;
