/**
 * Proposal Types Module
 *
 * This module exports all types, schemas, and type guards related to proposals.
 */

// Import types, schemas, and guards
import * as Types from './types';
import * as Schemas from './schemas';
import * as Guards from './guards';
import * as Analytics from './analytics';

// Re-export everything with namespaces to avoid conflicts
export { Types, Schemas, Guards, Analytics };
