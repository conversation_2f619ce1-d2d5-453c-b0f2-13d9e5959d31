import { z } from 'zod';
import { ObjectId } from 'mongodb';
import {
  ProposalSchema,
  ProposalSectionSchema,
  ProposalPricingItemSchema,
  ProposalPricingSchema,
  ProposalAnalyticsEventSchema,
  CreateProposalSchema,
  UpdateProposalSchema,
  ProposalDownloadOptionsSchema,
  ProposalSendOptionsSchema,
  ProposalAIGenerationOptionsSchema,
} from './schemas';

/**
 * Proposal analytics event interface
 */
export type IProposalAnalyticsEvent = z.infer<typeof ProposalAnalyticsEventSchema>;

/**
 * Proposal section interface
 */
export type IProposalSection = z.infer<typeof ProposalSectionSchema>;

/**
 * Proposal pricing item interface
 */
export type IProposalPricingItem = z.infer<typeof ProposalPricingItemSchema>;

/**
 * Proposal pricing interface
 */
export type IProposalPricing = z.infer<typeof ProposalPricingSchema>;

/**
 * Proposal interface
 */
export type IProposal = z.infer<typeof ProposalSchema>;

/**
 * Create proposal request interface
 */
export type CreateProposalRequest = z.infer<typeof CreateProposalSchema>;

/**
 * Update proposal request interface
 */
export type UpdateProposalRequest = z.infer<typeof UpdateProposalSchema>;

/**
 * Proposal download options interface
 */
export type ProposalDownloadOptions = z.infer<typeof ProposalDownloadOptionsSchema>;

/**
 * Proposal send options interface
 */
export type ProposalSendOptions = z.infer<typeof ProposalSendOptionsSchema>;

/**
 * Proposal AI generation options interface
 */
export type ProposalAIGenerationOptions = z.infer<typeof ProposalAIGenerationOptionsSchema>;

/**
 * Proposal response interface
 */
export type ProposalResponse = IProposal;

/**
 * Proposal list response interface
 */
export type ProposalListResponse = {
  data: IProposal[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
};

/**
 * Type guard for proposal
 * @param obj Object to check
 * @returns True if the object is a proposal
 */
export function isProposal(obj: any): obj is IProposal {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.title === 'string' &&
    Array.isArray(obj.sections) &&
    typeof obj.status === 'string' &&
    ['draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired'].includes(obj.status)
  );
}

/**
 * Type guard for proposal section
 * @param obj Object to check
 * @returns True if the object is a proposal section
 */
export function isProposalSection(obj: any): obj is IProposalSection {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.content === 'string' &&
    typeof obj.order === 'number' &&
    typeof obj.type === 'string' &&
    ['text', 'pricing', 'timeline', 'team', 'testimonials', 'images', 'custom'].includes(obj.type)
  );
}

/**
 * Type guard for proposal pricing item
 * @param obj Object to check
 * @returns True if the object is a proposal pricing item
 */
export function isProposalPricingItem(obj: any): obj is IProposalPricingItem {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.quantity === 'number' &&
    typeof obj.unitPrice === 'number' &&
    typeof obj.total === 'number'
  );
}

/**
 * Type guard for proposal pricing
 * @param obj Object to check
 * @returns True if the object is a proposal pricing
 */
export function isProposalPricing(obj: any): obj is IProposalPricing {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.currency === 'string' &&
    Array.isArray(obj.items) &&
    typeof obj.subtotal === 'number' &&
    typeof obj.total === 'number'
  );
}

/**
 * Type guard for proposal analytics event
 * @param obj Object to check
 * @returns True if the object is a proposal analytics event
 */
export function isProposalAnalyticsEvent(obj: any): obj is IProposalAnalyticsEvent {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.eventType === 'string' &&
    obj.timestamp instanceof Date
  );
}
