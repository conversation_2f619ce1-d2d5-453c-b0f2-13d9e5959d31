import { z } from 'zod';
import { ObjectId } from 'mongodb';

/**
 * Zod schema for MongoDB ObjectId
 */
export const ObjectIdSchema = z.custom<ObjectId>((val) => {
  try {
    if (typeof val === 'string') {
      return ObjectId.isValid(val);
    }
    return val instanceof ObjectId;
  } catch {
    return false;
  }
}, 'Invalid ObjectId');

/**
 * Zod schema for proposal analytics event
 */
export const ProposalAnalyticsEventSchema = z.object({
  eventType: z.string(),
  timestamp: z.date(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  duration: z.number().optional(),
  device: z.string().optional(),
  location: z.string().optional(),
  sectionId: z.string().optional(),
  format: z.string().optional(),
});

/**
 * Zod schema for proposal section
 */
export const ProposalSectionSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  order: z.number().int().min(0),
  type: z.enum(['text', 'pricing', 'timeline', 'team', 'testimonials', 'images', 'custom']),
  isVisible: z.boolean().default(true),
  aiGenerated: z.boolean().optional(),
});

/**
 * Zod schema for proposal pricing item
 */
export const ProposalPricingItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  quantity: z.number().min(0),
  unitPrice: z.number().min(0),
  total: z.number().min(0),
  discount: z.number().min(0).optional(),
  tax: z.number().min(0).optional(),
  isOptional: z.boolean().optional(),
});

/**
 * Zod schema for proposal pricing
 */
export const ProposalPricingSchema = z.object({
  currency: z.string().default('USD'),
  items: z.array(ProposalPricingItemSchema),
  subtotal: z.number().min(0),
  total: z.number().min(0),
  discount: z.number().min(0).optional(),
  tax: z.number().min(0).optional(),
});

/**
 * Zod schema for proposal
 */
export const ProposalSchema = z.object({
  _id: z.union([z.string(), ObjectIdSchema]).optional(),
  tenantId: z.string(),
  title: z.string(),
  description: z.string().optional(),
  status: z.enum(['draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired']),
  sections: z.array(ProposalSectionSchema),
  pricing: ProposalPricingSchema.optional(),
  terms: z.string().optional(),
  notes: z.string().optional(),
  publicToken: z.string(),
  publicUrl: z.string(),
  publicAccessEnabled: z.boolean().default(true),
  emailEnabled: z.boolean().default(true),
  downloadEnabled: z.boolean().default(true),
  downloadFormats: z.array(z.enum(['pdf', 'docx', 'md'])).default(['pdf']),
  opportunityId: z.union([z.string(), ObjectIdSchema]).optional(),
  companyId: z.union([z.string(), ObjectIdSchema]).optional(),
  contactIds: z.array(z.union([z.string(), ObjectIdSchema])).optional(),
  owner: z.union([z.string(), ObjectIdSchema]),
  createdBy: z.union([z.string(), ObjectIdSchema]),
  updatedBy: z.union([z.string(), ObjectIdSchema]).optional(),
  sentAt: z.date().optional(),
  sentBy: z.union([z.string(), ObjectIdSchema]).optional(),
  viewedAt: z.date().optional(),
  lastViewedAt: z.date().optional(),
  viewCount: z.number().int().min(0).default(0),
  expiresAt: z.date().optional(),
  acceptedAt: z.date().optional(),
  acceptedBy: z.union([z.string(), ObjectIdSchema]).optional(),
  rejectedAt: z.date().optional(),
  rejectedBy: z.union([z.string(), ObjectIdSchema]).optional(),
  rejectionReason: z.string().optional(),
  analyticsEvents: z.array(ProposalAnalyticsEventSchema).optional(),
  tags: z.array(z.string()).default([]),
  customFields: z.record(z.any()).optional(),
  aiGenerated: z.boolean().default(false),
  aiPrompt: z.string().optional(),
  aiModel: z.string().optional(),
  aiConfidence: z.number().min(0).max(1).optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Zod schema for creating a proposal
 */
export const CreateProposalSchema = ProposalSchema.omit({
  _id: true,
  publicToken: true,
  publicUrl: true,
  viewCount: true,
  createdAt: true,
  updatedAt: true,
}).partial({
  tenantId: true,
  owner: true,
  createdBy: true,
});

/**
 * Zod schema for updating a proposal
 */
export const UpdateProposalSchema = ProposalSchema.omit({
  _id: true,
  tenantId: true,
  publicToken: true,
  publicUrl: true,
  createdAt: true,
  updatedAt: true,
  createdBy: true,
}).partial();

/**
 * Zod schema for proposal download options
 */
export const ProposalDownloadOptionsSchema = z.object({
  format: z.enum(['pdf', 'docx', 'md']).default('pdf'),
  includeHeader: z.boolean().default(true),
  includeFooter: z.boolean().default(true),
  includeBranding: z.boolean().default(true),
  includePageNumbers: z.boolean().default(true),
  colorScheme: z.string().default('default'),
  paperSize: z.enum(['a4', 'letter', 'legal']).default('a4'),
  skipCache: z.enum(['true', 'false']).optional(),
});

/**
 * Zod schema for proposal send options
 */
export const ProposalSendOptionsSchema = z.object({
  to: z.array(z.string().email()),
  cc: z.array(z.string().email()).optional(),
  bcc: z.array(z.string().email()).optional(),
  subject: z.string(),
  message: z.string(),
  includeLink: z.boolean().default(true),
  includeAttachment: z.boolean().default(false),
  attachmentFormat: z.enum(['pdf', 'docx', 'md']).optional(),
  expiresAt: z.date().optional(),
});

/**
 * Zod schema for proposal AI generation options
 */
export const ProposalAIGenerationOptionsSchema = z.object({
  prompt: z.string(),
  model: z.string(),
  includeSections: z.object({
    executiveSummary: z.boolean().default(true),
    solution: z.boolean().default(true),
    timeline: z.boolean().default(true),
    pricing: z.boolean().default(true),
    team: z.boolean().default(false),
    testimonials: z.boolean().default(false),
    terms: z.boolean().default(true),
  }),
  opportunityId: z.union([z.string(), ObjectIdSchema]).optional(),
  companyId: z.union([z.string(), ObjectIdSchema]).optional(),
  contactIds: z.array(z.union([z.string(), ObjectIdSchema])).optional(),
});
