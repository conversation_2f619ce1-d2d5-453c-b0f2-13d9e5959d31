import { Document } from 'mongoose';
import { z } from 'zod';

/**
 * Proposal analytics event interface
 */
export interface IProposalAnalyticsEvent extends Document {
  proposalId: mongoose.Types.ObjectId;
  eventType: string;
  data: Record<string, any>;
  timestamp: Date;
}

/**
 * Proposal analytics interface
 */
export interface IProposalAnalytics {
  views: number;
  uniqueViews: number;
  downloads: number;
  downloadsByFormat: Record<string, number>;
  viewsByDate: Record<string, number>;
  viewsByHour: Record<string, number>;
  viewsByLocation: Record<string, number>;
  viewsByReferrer: Record<string, number>;
  viewsByDevice: Record<string, number>;
  averageViewDuration: number;
  events: Array<{
    eventType: string;
    timestamp: Date;
    data: Record<string, any>;
  }>;
}

/**
 * Tenant analytics interface
 */
export interface ITenantAnalytics {
  totalProposals: number;
  totalViews: number;
  totalDownloads: number;
  totalAcceptances: number;
  totalRejections: number;
  viewRate: number;
  acceptanceRate: number;
  rejectionRate: number;
  proposalsByStatus: Record<string, number>;
  viewsByDate: Record<string, number>;
  acceptancesByDate: Record<string, number>;
  topProposals: Array<{
    proposalId: string;
    title: string;
    views: number;
    downloads: number;
    status: string;
  }>;
}

/**
 * Zod schema for proposal analytics event data
 */
export const ProposalAnalyticsEventDataSchema = z.record(z.any());

/**
 * Zod schema for proposal analytics event
 */
export const ProposalAnalyticsEventSchema = z.object({
  proposalId: z.string(),
  eventType: z.string(),
  data: ProposalAnalyticsEventDataSchema,
  timestamp: z.date(),
});

/**
 * Zod schema for proposal view event
 */
export const ProposalViewEventSchema = z.object({
  referrer: z.string().optional(),
  userAgent: z.string().optional(),
  screenSize: z.string().optional(),
  ipAddress: z.string().optional(),
  location: z.string().optional(),
  timestamp: z.string().datetime(),
});

/**
 * Zod schema for proposal download event
 */
export const ProposalDownloadEventSchema = z.object({
  format: z.string(),
  userAgent: z.string().optional(),
  ipAddress: z.string().optional(),
  location: z.string().optional(),
  timestamp: z.string().datetime(),
});

/**
 * Zod schema for proposal acceptance event
 */
export const ProposalAcceptanceEventSchema = z.object({
  acceptedBy: z.string().optional(),
  userAgent: z.string().optional(),
  ipAddress: z.string().optional(),
  location: z.string().optional(),
  timestamp: z.string().datetime(),
});

/**
 * Zod schema for proposal rejection event
 */
export const ProposalRejectionEventSchema = z.object({
  rejectedBy: z.string().optional(),
  rejectionReason: z.string().optional(),
  userAgent: z.string().optional(),
  ipAddress: z.string().optional(),
  location: z.string().optional(),
  timestamp: z.string().datetime(),
});

/**
 * Zod schema for proposal analytics
 */
export const ProposalAnalyticsSchema = z.object({
  views: z.number().int().min(0),
  uniqueViews: z.number().int().min(0),
  downloads: z.number().int().min(0),
  downloadsByFormat: z.record(z.number().int().min(0)),
  viewsByDate: z.record(z.number().int().min(0)),
  viewsByHour: z.record(z.number().int().min(0)),
  viewsByLocation: z.record(z.number().int().min(0)),
  viewsByReferrer: z.record(z.number().int().min(0)),
  viewsByDevice: z.record(z.number().int().min(0)),
  averageViewDuration: z.number().min(0),
  events: z.array(
    z.object({
      eventType: z.string(),
      timestamp: z.date(),
      data: z.record(z.any()),
    })
  ),
});

/**
 * Zod schema for tenant analytics
 */
export const TenantAnalyticsSchema = z.object({
  totalProposals: z.number().int().min(0),
  totalViews: z.number().int().min(0),
  totalDownloads: z.number().int().min(0),
  totalAcceptances: z.number().int().min(0),
  totalRejections: z.number().int().min(0),
  viewRate: z.number().min(0).max(1),
  acceptanceRate: z.number().min(0).max(1),
  rejectionRate: z.number().min(0).max(1),
  proposalsByStatus: z.record(z.number().int().min(0)),
  viewsByDate: z.record(z.number().int().min(0)),
  acceptancesByDate: z.record(z.number().int().min(0)),
  topProposals: z.array(
    z.object({
      proposalId: z.string(),
      title: z.string(),
      views: z.number().int().min(0),
      downloads: z.number().int().min(0),
      status: z.string(),
    })
  ),
});

/**
 * Type guard for proposal analytics event
 * @param obj Object to check
 * @returns True if the object is a proposal analytics event
 */
export function isProposalAnalyticsEvent(obj: any): obj is IProposalAnalyticsEvent {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.eventType === 'string' &&
    obj.timestamp instanceof Date
  );
}

/**
 * Type guard for proposal analytics
 * @param obj Object to check
 * @returns True if the object is proposal analytics
 */
export function isProposalAnalytics(obj: any): obj is IProposalAnalytics {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.views === 'number' &&
    typeof obj.uniqueViews === 'number' &&
    typeof obj.downloads === 'number' &&
    typeof obj.averageViewDuration === 'number' &&
    Array.isArray(obj.events)
  );
}

/**
 * Type guard for tenant analytics
 * @param obj Object to check
 * @returns True if the object is tenant analytics
 */
export function isTenantAnalytics(obj: any): obj is ITenantAnalytics {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.totalProposals === 'number' &&
    typeof obj.totalViews === 'number' &&
    typeof obj.totalDownloads === 'number' &&
    typeof obj.viewRate === 'number' &&
    typeof obj.acceptanceRate === 'number' &&
    typeof obj.rejectionRate === 'number' &&
    Array.isArray(obj.topProposals)
  );
}
