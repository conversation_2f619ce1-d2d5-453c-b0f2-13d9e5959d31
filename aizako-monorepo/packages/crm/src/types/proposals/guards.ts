/**
 * Type guards for proposal types
 */

import { 
  IProposal, 
  IProposalSection, 
  IProposalPricingItem, 
  IProposalPricing,
  IProposalAnalyticsEvent
} from './types';
import { IABTest } from '../ab-testing/types';

/**
 * Type guard for proposal analytics event
 * @param obj Object to check
 * @returns True if the object is a proposal analytics event
 */
export function isProposalAnalyticsEvent(obj: any): obj is IProposalAnalyticsEvent {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.eventType === 'string' &&
    obj.timestamp instanceof Date
  );
}

/**
 * Type guard for proposal section
 * @param obj Object to check
 * @returns True if the object is a proposal section
 */
export function isProposalSection(obj: any): obj is IProposalSection {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.content === 'string' &&
    typeof obj.order === 'number' &&
    typeof obj.type === 'string' &&
    ['text', 'pricing', 'timeline', 'team', 'testimonials', 'images', 'custom'].includes(obj.type) &&
    typeof obj.isVisible === 'boolean'
  );
}

/**
 * Type guard for proposal pricing item
 * @param obj Object to check
 * @returns True if the object is a proposal pricing item
 */
export function isProposalPricingItem(obj: any): obj is IProposalPricingItem {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.quantity === 'number' &&
    typeof obj.unitPrice === 'number' &&
    typeof obj.total === 'number'
  );
}

/**
 * Type guard for proposal pricing
 * @param obj Object to check
 * @returns True if the object is a proposal pricing
 */
export function isProposalPricing(obj: any): obj is IProposalPricing {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.currency === 'string' &&
    Array.isArray(obj.items) &&
    obj.items.every((item: any) => isProposalPricingItem(item)) &&
    typeof obj.subtotal === 'number' &&
    typeof obj.total === 'number'
  );
}

/**
 * Type guard for A/B test
 * @param obj Object to check
 * @returns True if the object is an A/B test
 */
export function isABTest(obj: any): obj is IABTest {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.name === 'string' &&
    typeof obj.baseProposalId === 'object' &&
    Array.isArray(obj.variants) &&
    obj.variants.every((variant: any) => 
      typeof variant === 'object' &&
      typeof variant.name === 'string' &&
      typeof variant.proposalId === 'object' &&
      typeof variant.trafficPercentage === 'number'
    ) &&
    typeof obj.baseTrafficPercentage === 'number' &&
    typeof obj.status === 'string' &&
    ['active', 'paused', 'completed'].includes(obj.status) &&
    obj.startDate instanceof Date
  );
}

/**
 * Type guard for array of proposal sections
 * @param obj Object to check
 * @returns True if the object is an array of proposal sections
 */
export function isProposalSectionArray(obj: any): obj is IProposalSection[] {
  return Array.isArray(obj) && obj.every(isProposalSection);
}

/**
 * Type guard for array of proposal pricing items
 * @param obj Object to check
 * @returns True if the object is an array of proposal pricing items
 */
export function isProposalPricingItemArray(obj: any): obj is IProposalPricingItem[] {
  return Array.isArray(obj) && obj.every(isProposalPricingItem);
}

/**
 * Type guard for array of A/B tests
 * @param obj Object to check
 * @returns True if the object is an array of A/B tests
 */
export function isABTestArray(obj: any): obj is IABTest[] {
  return Array.isArray(obj) && obj.every(isABTest);
}

/**
 * Type guard for array of proposal analytics events
 * @param obj Object to check
 * @returns True if the object is an array of proposal analytics events
 */
export function isProposalAnalyticsEventArray(obj: any): obj is IProposalAnalyticsEvent[] {
  return Array.isArray(obj) && obj.every(isProposalAnalyticsEvent);
}
