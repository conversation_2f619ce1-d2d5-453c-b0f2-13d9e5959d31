/**
 * Query options for repositories
 */

/**
 * Pagination options
 */
export interface PaginationOptions {
  /**
   * Page number (1-based)
   */
  page?: number;
  
  /**
   * Number of items per page
   */
  limit?: number;
}

/**
 * Sorting options
 */
export interface SortOptions {
  /**
   * Field to sort by
   */
  [field: string]: 1 | -1;
}

/**
 * Projection options
 */
export interface ProjectionOptions {
  /**
   * Fields to include or exclude
   */
  [field: string]: 0 | 1;
}

/**
 * Query options
 */
export interface QueryOptions {
  /**
   * Filter criteria
   */
  filter?: Record<string, any>;
  
  /**
   * Sorting options
   */
  sort?: SortOptions;
  
  /**
   * Projection options
   */
  projection?: ProjectionOptions;
  
  /**
   * Page number (1-based)
   */
  page?: number;
  
  /**
   * Number of items per page
   */
  limit?: number;
}

/**
 * Pagination result
 */
export interface PaginationResult<T> {
  /**
   * Items in the current page
   */
  items: T[];
  
  /**
   * Total number of items
   */
  total: number;
  
  /**
   * Current page number
   */
  page: number;
  
  /**
   * Number of items per page
   */
  limit: number;
  
  /**
   * Total number of pages
   */
  totalPages: number;
}

export default QueryOptions;
