import { z } from 'zod';

/**
 * AI Chat Message interface
 */
export interface IAIChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * AI Chat interface
 */
export interface IAIChat {
  tenantId: string;
  userId: string;
  title: string;
  contextType: 'contact' | 'company' | 'opportunity' | 'proposal' | 'general' | 'other';
  contextId?: string;
  messages: IAIChatMessage[];
  modelName: string;
  status: 'active' | 'archived';
  tags?: string[];
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * AI Chat Message Zod schema
 */
export const AIChatMessageSchema_Z = z.object({
  role: z.enum(['user', 'assistant', 'system']),
  content: z.string(),
  timestamp: z.date(),
  metadata: z.record(z.any()).optional(),
});

/**
 * AI Chat Zod schema
 */
export const AIChatSchema_Z = z.object({
  tenantId: z.string(),
  userId: z.string(),
  title: z.string(),
  contextType: z.enum(['contact', 'company', 'opportunity', 'proposal', 'general', 'other']),
  contextId: z.string().optional(),
  messages: z.array(AIChatMessageSchema_Z),
  modelName: z.string(),
  status: z.enum(['active', 'archived']),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Type guard for IAIChatMessage
 */
export function isAIChatMessage(obj: any): obj is IAIChatMessage {
  return AIChatMessageSchema_Z.safeParse(obj).success;
}

/**
 * Type guard for IAIChat
 */
export function isAIChat(obj: any): obj is IAIChat {
  return AIChatSchema_Z.safeParse(obj).success;
}

/**
 * AI Chat Creation DTO
 */
export interface AIChatCreateDTO {
  title: string;
  contextType: 'contact' | 'company' | 'opportunity' | 'proposal' | 'general' | 'other';
  contextId?: string;
  initialMessage?: string;
  model?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * AI Chat Message Creation DTO
 */
export interface AIChatMessageCreateDTO {
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
}

/**
 * AI Chat Update DTO
 */
export interface AIChatUpdateDTO {
  title?: string;
  status?: 'active' | 'archived';
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * AI Chat Response DTO
 */
export interface AIChatResponseDTO {
  id: string;
  tenantId: string;
  userId: string;
  title: string;
  contextType: 'contact' | 'company' | 'opportunity' | 'proposal' | 'general' | 'other';
  contextId?: string;
  messages: IAIChatMessage[];
  model: string;
  status: 'active' | 'archived';
  tags?: string[];
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * AI Chat List Response DTO
 */
export interface AIChatListResponseDTO {
  chats: AIChatResponseDTO[];
  total: number;
  page: number;
  limit: number;
}
