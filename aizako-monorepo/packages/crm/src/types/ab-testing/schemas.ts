import { z } from 'zod';
import { ObjectId } from 'mongodb';

/**
 * Zod schema for MongoDB ObjectId
 */
export const ObjectIdSchema = z.custom<ObjectId>((val) => {
  try {
    if (typeof val === 'string') {
      return ObjectId.isValid(val);
    }
    return val instanceof ObjectId;
  } catch {
    return false;
  }
}, 'Invalid ObjectId');

/**
 * Zod schema for A/B test variant
 */
export const ABTestVariantSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  proposalId: z.union([z.string(), ObjectIdSchema]),
  trafficPercentage: z.number().min(0).max(100),
});

/**
 * Zod schema for A/B test
 */
export const ABTestSchema = z.object({
  _id: z.union([z.string(), ObjectIdSchema]).optional(),
  tenantId: z.string(),
  name: z.string(),
  description: z.string().optional(),
  baseProposalId: z.union([z.string(), ObjectIdSchema]),
  variants: z.array(ABTestVariantSchema),
  baseTrafficPercentage: z.number().min(0).max(100),
  status: z.enum(['active', 'paused', 'completed']),
  startDate: z.date(),
  endDate: z.date().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Zod schema for A/B test section change
 */
export const ABTestSectionChangeSchema = z.object({
  sectionId: z.string(),
  content: z.string(),
});

/**
 * Zod schema for creating an A/B test variant
 */
export const CreateABTestVariantSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  changes: z.array(ABTestSectionChangeSchema),
});

/**
 * Zod schema for creating an A/B test
 */
export const CreateABTestSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  baseProposalId: z.string(),
  variants: z.array(CreateABTestVariantSchema),
  trafficSplit: z.array(z.number()).optional(),
  startDate: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
});

/**
 * Zod schema for updating an A/B test variant
 */
export const UpdateABTestVariantSchema = z.object({
  name: z.string(),
  trafficPercentage: z.number().min(0).max(100),
});

/**
 * Zod schema for updating an A/B test
 */
export const UpdateABTestSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  status: z.enum(['active', 'paused', 'completed']).optional(),
  baseTrafficPercentage: z.number().min(0).max(100).optional(),
  variants: z.array(UpdateABTestVariantSchema).optional(),
  endDate: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
});

/**
 * Zod schema for A/B test base proposal result
 */
export const ABTestBaseProposalResultSchema = z.object({
  proposalId: z.string(),
  views: z.number().int().min(0),
  uniqueViews: z.number().int().min(0),
  viewRate: z.number().min(0).max(1),
  acceptances: z.number().int().min(0),
  acceptanceRate: z.number().min(0).max(1),
  rejections: z.number().int().min(0),
  rejectionRate: z.number().min(0).max(1),
});

/**
 * Zod schema for A/B test variant result
 */
export const ABTestVariantResultSchema = z.object({
  name: z.string(),
  proposalId: z.string(),
  views: z.number().int().min(0),
  uniqueViews: z.number().int().min(0),
  viewRate: z.number().min(0).max(1),
  acceptances: z.number().int().min(0),
  acceptanceRate: z.number().min(0).max(1),
  rejections: z.number().int().min(0),
  rejectionRate: z.number().min(0).max(1),
  improvement: z.number(),
});

/**
 * Zod schema for A/B test results
 */
export const ABTestResultsSchema = z.object({
  testId: z.string(),
  name: z.string(),
  baseProposal: ABTestBaseProposalResultSchema,
  variants: z.array(ABTestVariantResultSchema),
  winner: z.string().nullable(),
  confidence: z.number().min(0).max(100),
});
