import { Document } from 'mongoose';
import { TenantScopedEntity } from '@aizako/core-lib';
import { z } from 'zod';
import { ABTestSchema } from './schemas';

/**
 * A/B test interface
 */
export interface IABTest extends Document, TenantScopedEntity {
  name: string;
  description?: string;
  baseProposalId: mongoose.Types.ObjectId;
  variants: Array<{
    name: string;
    description?: string;
    proposalId: mongoose.Types.ObjectId;
    trafficPercentage: number;
  }>;
  baseTrafficPercentage: number;
  status: 'active' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * A/B test results interface
 */
export interface IABTestResults {
  testId: string;
  name: string;
  baseProposal: {
    proposalId: string;
    views: number;
    uniqueViews: number;
    viewRate: number;
    acceptances: number;
    acceptanceRate: number;
    rejections: number;
    rejectionRate: number;
  };
  variants: Array<{
    name: string;
    proposalId: string;
    views: number;
    uniqueViews: number;
    viewRate: number;
    acceptances: number;
    acceptanceRate: number;
    rejections: number;
    rejectionRate: number;
    improvement: number;
  }>;
  winner: string | null;
  confidence: number;
}

/**
 * Create A/B test request interface
 */
export interface CreateABTestRequest {
  name: string;
  description?: string;
  baseProposalId: string;
  variants: Array<{
    name: string;
    description?: string;
    changes: Array<{
      sectionId: string;
      content: string;
    }>;
  }>;
  trafficSplit?: Array<number>;
  startDate?: Date;
  endDate?: Date;
}

/**
 * Update A/B test request interface
 */
export interface UpdateABTestRequest {
  name?: string;
  description?: string;
  status?: 'active' | 'paused' | 'completed';
  baseTrafficPercentage?: number;
  variants?: Array<{
    name: string;
    trafficPercentage: number;
  }>;
  endDate?: Date;
}

/**
 * Type guard for A/B test
 * @param obj Object to check
 * @returns True if the object is an A/B test
 */
export function isABTest(obj: any): obj is IABTest {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.name === 'string' &&
    typeof obj.baseProposalId === 'object' &&
    Array.isArray(obj.variants) &&
    obj.variants.every((variant: any) => 
      typeof variant === 'object' &&
      typeof variant.name === 'string' &&
      typeof variant.proposalId === 'object' &&
      typeof variant.trafficPercentage === 'number'
    ) &&
    typeof obj.baseTrafficPercentage === 'number' &&
    typeof obj.status === 'string' &&
    ['active', 'paused', 'completed'].includes(obj.status) &&
    obj.startDate instanceof Date
  );
}

/**
 * Type guard for A/B test results
 * @param obj Object to check
 * @returns True if the object is A/B test results
 */
export function isABTestResults(obj: any): obj is IABTestResults {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.testId === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.baseProposal === 'object' &&
    Array.isArray(obj.variants) &&
    (obj.winner === null || typeof obj.winner === 'string') &&
    typeof obj.confidence === 'number'
  );
}

/**
 * Type guard for array of A/B tests
 * @param obj Object to check
 * @returns True if the object is an array of A/B tests
 */
export function isABTestArray(obj: any): obj is IABTest[] {
  return Array.isArray(obj) && obj.every(isABTest);
}
