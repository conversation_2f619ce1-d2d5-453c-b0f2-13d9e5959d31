import { z } from 'zod';

/**
 * Attribution results interface
 */
export interface IAttributionResults {
  tenantId: string;
  opportunityId: string;
  contactId?: string;
  companyId?: string;
  value: number;
  currency: string;
  status: 'won' | 'lost' | 'open';
  attributionModel: 'first-touch' | 'last-touch' | 'linear' | 'position-based' | 'time-decay' | 'custom';
  touchpoints: Array<{
    id: string;
    type: string;
    name: string;
    timestamp: Date;
    channel: string;
    campaign?: string;
    source?: string;
    medium?: string;
    content?: string;
    term?: string;
    attribution: number;
    position: number;
    isFirstTouch: boolean;
    isLastTouch: boolean;
    isConversion: boolean;
    metadata?: Record<string, any>;
  }>;
  firstTouchChannel?: string;
  lastTouchChannel?: string;
  conversionChannel?: string;
  channelAttribution: Record<string, number>;
  campaignAttribution?: Record<string, number>;
  sourceAttribution?: Record<string, number>;
  mediumAttribution?: Record<string, number>;
  timeToConversion?: number;
  touchpointCount: number;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Attribution results Zod schema
 */
export const AttributionResultsSchema = z.object({
  tenantId: z.string(),
  opportunityId: z.string(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  value: z.number(),
  currency: z.string(),
  status: z.enum(['won', 'lost', 'open']),
  attributionModel: z.enum(['first-touch', 'last-touch', 'linear', 'position-based', 'time-decay', 'custom']),
  touchpoints: z.array(
    z.object({
      id: z.string(),
      type: z.string(),
      name: z.string(),
      timestamp: z.date(),
      channel: z.string(),
      campaign: z.string().optional(),
      source: z.string().optional(),
      medium: z.string().optional(),
      content: z.string().optional(),
      term: z.string().optional(),
      attribution: z.number(),
      position: z.number(),
      isFirstTouch: z.boolean(),
      isLastTouch: z.boolean(),
      isConversion: z.boolean(),
      metadata: z.record(z.any()).optional(),
    })
  ),
  firstTouchChannel: z.string().optional(),
  lastTouchChannel: z.string().optional(),
  conversionChannel: z.string().optional(),
  channelAttribution: z.record(z.number()),
  campaignAttribution: z.record(z.number()).optional(),
  sourceAttribution: z.record(z.number()).optional(),
  mediumAttribution: z.record(z.number()).optional(),
  timeToConversion: z.number().optional(),
  touchpointCount: z.number(),
  metadata: z.record(z.any()).optional(),
  createdAt: z.date(),
  updatedAt: z.date().optional(),
});

/**
 * Type guard for IAttributionResults
 * @param obj Object to check
 * @returns True if object is an IAttributionResults
 */
export function isAttributionResults(obj: any): obj is IAttributionResults {
  return AttributionResultsSchema.safeParse(obj).success;
}

/**
 * Attribution results create DTO
 */
export interface AttributionResultsCreateDTO {
  opportunityId: string;
  contactId?: string;
  companyId?: string;
  value: number;
  currency: string;
  status: 'won' | 'lost' | 'open';
  attributionModel: 'first-touch' | 'last-touch' | 'linear' | 'position-based' | 'time-decay' | 'custom';
  touchpoints: Array<{
    id: string;
    type: string;
    name: string;
    timestamp: Date;
    channel: string;
    campaign?: string;
    source?: string;
    medium?: string;
    content?: string;
    term?: string;
    metadata?: Record<string, any>;
  }>;
  metadata?: Record<string, any>;
}

/**
 * Attribution results update DTO
 */
export interface AttributionResultsUpdateDTO {
  value?: number;
  currency?: string;
  status?: 'won' | 'lost' | 'open';
  attributionModel?: 'first-touch' | 'last-touch' | 'linear' | 'position-based' | 'time-decay' | 'custom';
  touchpoints?: Array<{
    id: string;
    type: string;
    name: string;
    timestamp: Date;
    channel: string;
    campaign?: string;
    source?: string;
    medium?: string;
    content?: string;
    term?: string;
    metadata?: Record<string, any>;
  }>;
  metadata?: Record<string, any>;
}
