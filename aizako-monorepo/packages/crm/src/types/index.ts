/**
 * CRM Types Module
 *
 * This module exports all types, schemas, and type guards used in the CRM module.
 * It provides a centralized location for all type definitions to ensure consistency
 * across the codebase.
 */

// Export common types
export * from './pagination';
export * from './api';
export * from './shared';
export * from './ai-chat';
export * from './analytics-dataset';
export * from './analytics-event';
export * from './attribution-results';

// Export entity types
import * as Proposals from './proposals';
export { Proposals };

import * as ABTesting from './ab-testing';
export { ABTesting };
