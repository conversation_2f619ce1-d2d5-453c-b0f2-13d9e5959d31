/**
 * Shared types for the CRM module
 */

/**
 * Interface for entities that are scoped to a tenant
 */
export interface TenantScopedEntity {
  id?: string;
  tenantId: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Interface for entities that have timestamps
 */
export interface TimestampedEntity {
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for entities that have an owner
 */
export interface OwnedEntity {
  ownerId: string;
}

/**
 * Interface for entities that have tags
 */
export interface TaggedEntity {
  tags: string[];
}

/**
 * Interface for entities that have custom fields
 */
export interface CustomFieldsEntity {
  customFields: Record<string, any>;
}

/**
 * Interface for entities that have notes
 */
export interface NotedEntity {
  notes?: string;
}

/**
 * Interface for entities that have a status
 */
export interface StatusEntity<T extends string> {
  status: T;
}

/**
 * Interface for entities that have a name
 */
export interface NamedEntity {
  name: string;
}

/**
 * Interface for entities that have a description
 */
export interface DescribedEntity {
  description?: string;
}

/**
 * Interface for entities that have metadata
 */
export interface MetadataEntity {
  metadata?: Record<string, any>;
}

/**
 * Interface for entities that have AI-generated content
 */
export interface AIGeneratedEntity {
  aiGenerated: boolean;
  aiConfidence?: number;
}
