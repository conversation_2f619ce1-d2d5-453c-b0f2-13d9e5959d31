import { z } from 'zod';

/**
 * Analytics dataset interface
 */
export interface IAnalyticsDataset {
  tenantId: string;
  name: string;
  description?: string;
  dataType: 'timeseries' | 'categorical' | 'numerical' | 'relational';
  entityType: 'contact' | 'company' | 'opportunity' | 'proposal' | 'email' | 'sequence' | 'workflow' | 'user' | 'system';
  dimensions: string[];
  metrics: string[];
  filters?: Record<string, any>;
  refreshInterval?: number;
  lastRefreshed?: Date;
  status: 'active' | 'inactive' | 'processing' | 'error';
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Analytics dataset Zod schema
 */
export const AnalyticsDatasetSchema = z.object({
  tenantId: z.string(),
  name: z.string(),
  description: z.string().optional(),
  dataType: z.enum(['timeseries', 'categorical', 'numerical', 'relational']),
  entityType: z.enum(['contact', 'company', 'opportunity', 'proposal', 'email', 'sequence', 'workflow', 'user', 'system']),
  dimensions: z.array(z.string()),
  metrics: z.array(z.string()),
  filters: z.record(z.any()).optional(),
  refreshInterval: z.number().optional(),
  lastRefreshed: z.date().optional(),
  status: z.enum(['active', 'inactive', 'processing', 'error']),
  createdBy: z.string(),
  updatedBy: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date().optional(),
});

/**
 * Type guard for IAnalyticsDataset
 * @param obj Object to check
 * @returns True if object is an IAnalyticsDataset
 */
export function isAnalyticsDataset(obj: any): obj is IAnalyticsDataset {
  return AnalyticsDatasetSchema.safeParse(obj).success;
}

/**
 * Analytics dataset create DTO
 */
export interface AnalyticsDatasetCreateDTO {
  name: string;
  description?: string;
  dataType: 'timeseries' | 'categorical' | 'numerical' | 'relational';
  entityType: 'contact' | 'company' | 'opportunity' | 'proposal' | 'email' | 'sequence' | 'workflow' | 'user' | 'system';
  dimensions: string[];
  metrics: string[];
  filters?: Record<string, any>;
  refreshInterval?: number;
}

/**
 * Analytics dataset update DTO
 */
export interface AnalyticsDatasetUpdateDTO {
  name?: string;
  description?: string;
  dimensions?: string[];
  metrics?: string[];
  filters?: Record<string, any>;
  refreshInterval?: number;
  status?: 'active' | 'inactive';
}

/**
 * Analytics dataset query result
 */
export interface AnalyticsDatasetQueryResult {
  data: Record<string, any>[];
  metadata: {
    dimensions: string[];
    metrics: string[];
    filters?: Record<string, any>;
    totalCount: number;
    lastRefreshed: Date;
  };
}

/**
 * Analytics dataset query options
 */
export interface AnalyticsDatasetQueryOptions {
  dimensions?: string[];
  metrics?: string[];
  filters?: Record<string, any>;
  limit?: number;
  offset?: number;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  timeRange?: {
    start: Date;
    end: Date;
  };
}
