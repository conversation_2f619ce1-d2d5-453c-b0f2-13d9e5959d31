import { z } from 'zod';

/**
 * Analytics event interface
 */
export interface IAnalyticsEvent {
  tenantId: string;
  userId?: string;
  eventType: string;
  eventName: string;
  eventCategory: string;
  eventSource: string;
  eventTimestamp: Date;
  entityType?: string;
  entityId?: string;
  properties?: Record<string, any>;
  metadata?: Record<string, any>;
  sessionId?: string;
  clientInfo?: {
    ip?: string;
    userAgent?: string;
    device?: string;
    browser?: string;
    os?: string;
    referrer?: string;
    language?: string;
    screenSize?: string;
  };
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Analytics event Zod schema
 */
export const AnalyticsEventSchema = z.object({
  tenantId: z.string(),
  userId: z.string().optional(),
  eventType: z.string(),
  eventName: z.string(),
  eventCategory: z.string(),
  eventSource: z.string(),
  eventTimestamp: z.date(),
  entityType: z.string().optional(),
  entityId: z.string().optional(),
  properties: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
  sessionId: z.string().optional(),
  clientInfo: z.object({
    ip: z.string().optional(),
    userAgent: z.string().optional(),
    device: z.string().optional(),
    browser: z.string().optional(),
    os: z.string().optional(),
    referrer: z.string().optional(),
    language: z.string().optional(),
    screenSize: z.string().optional(),
  }).optional(),
  createdAt: z.date(),
  updatedAt: z.date().optional(),
});

/**
 * Type guard for IAnalyticsEvent
 * @param obj Object to check
 * @returns True if object is an IAnalyticsEvent
 */
export function isAnalyticsEvent(obj: any): obj is IAnalyticsEvent {
  return AnalyticsEventSchema.safeParse(obj).success;
}

/**
 * Analytics event create DTO
 */
export interface AnalyticsEventCreateDTO {
  userId?: string;
  eventType: string;
  eventName: string;
  eventCategory: string;
  eventSource: string;
  eventTimestamp?: Date;
  entityType?: string;
  entityId?: string;
  properties?: Record<string, any>;
  metadata?: Record<string, any>;
  sessionId?: string;
  clientInfo?: {
    ip?: string;
    userAgent?: string;
    device?: string;
    browser?: string;
    os?: string;
    referrer?: string;
    language?: string;
    screenSize?: string;
  };
}

/**
 * Analytics event query options
 */
export interface AnalyticsEventQueryOptions {
  eventTypes?: string[];
  eventNames?: string[];
  eventCategories?: string[];
  eventSources?: string[];
  entityTypes?: string[];
  entityIds?: string[];
  userIds?: string[];
  sessionIds?: string[];
  timeRange?: {
    start: Date;
    end: Date;
  };
  properties?: Record<string, any>;
  limit?: number;
  offset?: number;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}
