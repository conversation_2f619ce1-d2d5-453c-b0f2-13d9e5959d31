import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '../types/shared';

/**
 * Email template variable interface
 */
export interface IEmailTemplateVariable {
  name: string;
  description?: string;
  defaultValue?: string;
  required: boolean;
  type: 'text' | 'number' | 'date' | 'boolean' | 'contact' | 'company' | 'opportunity' | 'user';
}

/**
 * Email template A/B test variant interface
 */
export interface IEmailTemplateVariant {
  id: string;
  name: string;
  subject: string;
  content: string;
  isActive: boolean;
  weight: number; // 0-100, percentage of traffic
  performance?: {
    sends: number;
    opens: number;
    clicks: number;
    replies: number;
    bounces: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
    bounceRate: number;
  };
}

/**
 * Email template interface
 */
export interface IEmailTemplate extends Document, TenantScopedEntity {
  name: string;
  description?: string;
  category: string;
  subject: string;
  content: string;
  contentType: 'html' | 'markdown' | 'text';
  isActive: boolean;
  isSystem: boolean;
  isShared: boolean;
  variables: IEmailTemplateVariable[];
  variants?: IEmailTemplateVariant[];
  isAbTestEnabled: boolean;
  abTestWinner?: string; // ID of the winning variant
  abTestStatus?: 'running' | 'completed' | 'paused';
  abTestStartDate?: Date;
  abTestEndDate?: Date;
  abTestMetric?: 'opens' | 'clicks' | 'replies';
  abTestMinSampleSize?: number;
  abTestConfidenceLevel?: number;
  performance?: {
    sends: number;
    opens: number;
    clicks: number;
    replies: number;
    bounces: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
    bounceRate: number;
    lastUpdated: Date;
  };
  owner: mongoose.Types.ObjectId;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  lastUsedAt?: Date;
  tags: string[];
  customFields: Record<string, any>;
  // AI-generated content
  aiGenerated: boolean;
  aiPrompt?: string;
  aiModel?: string;
  aiConfidence?: number;
}

/**
 * Email template schema
 */
const EmailTemplateSchema = new Schema<IEmailTemplate>(
  {
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    category: { type: String, required: true, trim: true, index: true },
    subject: { type: String, required: true, trim: true },
    content: { type: String, required: true },
    contentType: {
      type: String,
      enum: ['html', 'markdown', 'text'],
      default: 'html'
    },
    isActive: { type: Boolean, default: true, index: true },
    isSystem: { type: Boolean, default: false },
    isShared: { type: Boolean, default: false },
    variables: [{
      name: { type: String, required: true, trim: true },
      description: { type: String },
      defaultValue: { type: String },
      required: { type: Boolean, default: false },
      type: {
        type: String,
        enum: ['text', 'number', 'date', 'boolean', 'contact', 'company', 'opportunity', 'user'],
        default: 'text'
      }
    }],
    variants: [{
      id: { type: String, required: true },
      name: { type: String, required: true, trim: true },
      subject: { type: String, required: true, trim: true },
      content: { type: String, required: true },
      isActive: { type: Boolean, default: true },
      weight: { type: Number, min: 0, max: 100, default: 50 },
      performance: {
        sends: { type: Number, default: 0 },
        opens: { type: Number, default: 0 },
        clicks: { type: Number, default: 0 },
        replies: { type: Number, default: 0 },
        bounces: { type: Number, default: 0 },
        openRate: { type: Number, min: 0, max: 100, default: 0 },
        clickRate: { type: Number, min: 0, max: 100, default: 0 },
        replyRate: { type: Number, min: 0, max: 100, default: 0 },
        bounceRate: { type: Number, min: 0, max: 100, default: 0 }
      }
    }],
    isAbTestEnabled: { type: Boolean, default: false },
    abTestWinner: { type: String }, // ID of the winning variant
    abTestStatus: {
      type: String,
      enum: ['running', 'completed', 'paused']
    },
    abTestStartDate: { type: Date },
    abTestEndDate: { type: Date },
    abTestMetric: {
      type: String,
      enum: ['opens', 'clicks', 'replies'],
      default: 'opens'
    },
    abTestMinSampleSize: { type: Number, min: 10, default: 100 },
    abTestConfidenceLevel: { type: Number, min: 0.8, max: 0.99, default: 0.95 },
    performance: {
      sends: { type: Number, default: 0 },
      opens: { type: Number, default: 0 },
      clicks: { type: Number, default: 0 },
      replies: { type: Number, default: 0 },
      bounces: { type: Number, default: 0 },
      openRate: { type: Number, min: 0, max: 100, default: 0 },
      clickRate: { type: Number, min: 0, max: 100, default: 0 },
      replyRate: { type: Number, min: 0, max: 100, default: 0 },
      bounceRate: { type: Number, min: 0, max: 100, default: 0 },
      lastUpdated: { type: Date, default: Date.now }
    },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    lastUsedAt: { type: Date },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // AI-generated content
    aiGenerated: { type: Boolean, default: false },
    aiPrompt: { type: String },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
EmailTemplateSchema.index({ name: 1, tenantId: 1 });
EmailTemplateSchema.index({ category: 1, tenantId: 1 });
EmailTemplateSchema.index({ isActive: 1, tenantId: 1 });
EmailTemplateSchema.index({ isSystem: 1, tenantId: 1 });
EmailTemplateSchema.index({ isShared: 1, tenantId: 1 });
EmailTemplateSchema.index({ owner: 1, tenantId: 1 });
EmailTemplateSchema.index({ lastUsedAt: 1, tenantId: 1 });
EmailTemplateSchema.index({ aiGenerated: 1, tenantId: 1 });

// Compound index for text search
EmailTemplateSchema.index({ tenantId: 1, name: 'text', description: 'text', subject: 'text' });

// Create the model
export const EmailTemplate: Model<IEmailTemplate> = mongoose.models.EmailTemplate || mongoose.model<IEmailTemplate>('EmailTemplate', EmailTemplateSchema);

export default EmailTemplate;
