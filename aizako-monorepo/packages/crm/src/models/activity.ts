import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '../types/shared';

/**
 * Activity participant interface
 */
export interface IActivityParticipant {
  id?: mongoose.Types.ObjectId;
  email?: string;
  name?: string;
  role?: string;
  status?: 'invited' | 'accepted' | 'declined' | 'tentative';
  responseTime?: Date;
  responseComment?: string;
}

/**
 * Activity outcome interface
 */
export interface IActivityOutcome {
  result: 'positive' | 'negative' | 'neutral';
  notes?: string;
  nextSteps?: string[];
  followUpActivity?: mongoose.Types.ObjectId;
  sentiment?: 'positive' | 'neutral' | 'negative';
  aiGenerated?: boolean;
  aiConfidence?: number;
}

/**
 * Activity interface
 */
export interface IActivity extends Document, TenantScopedEntity {
  type: 'call' | 'email' | 'meeting' | 'task' | 'note' | 'social' | 'chat' | 'sms' | 'other';
  subject: string;
  description?: string;
  date: Date;
  endDate?: Date;
  duration?: number;
  status: 'planned' | 'in_progress' | 'completed' | 'canceled' | 'rescheduled';
  priority?: 'low' | 'medium' | 'high';
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  opportunityId?: mongoose.Types.ObjectId;
  owner?: mongoose.Types.ObjectId;
  completedBy?: mongoose.Types.ObjectId;
  completedAt?: Date;
  reminderDate?: Date;
  reminderSent?: boolean;
  location?: {
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
    virtual?: boolean;
    meetingUrl?: string;
    meetingId?: string;
    meetingPassword?: string;
    provider?: 'zoom' | 'teams' | 'google_meet' | 'webex' | 'other';
  };
  participants?: IActivityParticipant[];
  outcome?: IActivityOutcome;
  source?: string;
  sourceId?: string;
  attachments?: Array<{
    name: string;
    type: string;
    url: string;
    size?: number;
    uploadedAt?: Date;
    uploadedBy?: mongoose.Types.ObjectId;
  }>;
  recurrence?: {
    pattern: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    daysOfWeek?: number[];
    dayOfMonth?: number;
    monthOfYear?: number;
    endDate?: Date;
    occurrences?: number;
    seriesId?: string;
  };
  isRecurring?: boolean;
  parentActivityId?: mongoose.Types.ObjectId;
  customFields: Record<string, any>;
  // AI-generated insights
  insights?: {
    summary?: string;
    keyPoints?: string[];
    actionItems?: string[];
    sentimentAnalysis?: {
      overall: 'positive' | 'neutral' | 'negative';
      score: number;
      topics?: Array<{
        topic: string;
        sentiment: 'positive' | 'neutral' | 'negative';
        score: number;
      }>;
    };
    aiConfidence?: number;
    lastUpdated?: Date;
  };
}

/**
 * Activity schema
 */
const ActivitySchema = new Schema<IActivity>(
  {
    tenantId: { type: String, required: true, index: true },
    type: {
      type: String,
      enum: ['call', 'email', 'meeting', 'task', 'note', 'social', 'chat', 'sms', 'other'],
      required: true,
      index: true
    },
    subject: { type: String, required: true, trim: true },
    description: { type: String },
    date: { type: Date, required: true, index: true },
    endDate: { type: Date },
    duration: { type: Number }, // in minutes
    status: {
      type: String,
      enum: ['planned', 'in_progress', 'completed', 'canceled', 'rescheduled'],
      default: 'planned',
      index: true
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    contactId: { type: Schema.Types.ObjectId, ref: 'Contact', index: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', index: true },
    opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', index: true },
    owner: { type: Schema.Types.ObjectId, ref: 'User', index: true },
    completedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    completedAt: { type: Date },
    reminderDate: { type: Date, index: true },
    reminderSent: { type: Boolean, default: false },
    location: {
      address: { type: String, trim: true },
      city: { type: String, trim: true },
      state: { type: String, trim: true },
      country: { type: String, trim: true },
      postalCode: { type: String, trim: true },
      virtual: { type: Boolean, default: false },
      meetingUrl: { type: String, trim: true },
      meetingId: { type: String, trim: true },
      meetingPassword: { type: String, trim: true },
      provider: {
        type: String,
        enum: ['zoom', 'teams', 'google_meet', 'webex', 'other']
      }
    },
    participants: [{
      id: { type: Schema.Types.ObjectId, ref: 'Contact' },
      email: { type: String, trim: true },
      name: { type: String, trim: true },
      role: { type: String, trim: true },
      status: {
        type: String,
        enum: ['invited', 'accepted', 'declined', 'tentative'],
        default: 'invited'
      },
      responseTime: { type: Date },
      responseComment: { type: String }
    }],
    outcome: {
      result: {
        type: String,
        enum: ['positive', 'negative', 'neutral']
      },
      notes: { type: String },
      nextSteps: [{ type: String }],
      followUpActivity: { type: Schema.Types.ObjectId, ref: 'Activity' },
      sentiment: {
        type: String,
        enum: ['positive', 'neutral', 'negative']
      },
      aiGenerated: { type: Boolean, default: false },
      aiConfidence: { type: Number, min: 0, max: 1 }
    },
    source: { type: String, trim: true },
    sourceId: { type: String, trim: true },
    attachments: [{
      name: { type: String, required: true, trim: true },
      type: { type: String, required: true, trim: true },
      url: { type: String, required: true, trim: true },
      size: { type: Number },
      uploadedAt: { type: Date, default: Date.now },
      uploadedBy: { type: Schema.Types.ObjectId, ref: 'User' }
    }],
    recurrence: {
      pattern: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'yearly']
      },
      interval: { type: Number, min: 1, default: 1 },
      daysOfWeek: [{ type: Number, min: 0, max: 6 }],
      dayOfMonth: { type: Number, min: 1, max: 31 },
      monthOfYear: { type: Number, min: 0, max: 11 },
      endDate: { type: Date },
      occurrences: { type: Number, min: 1 },
      seriesId: { type: String }
    },
    isRecurring: { type: Boolean, default: false },
    parentActivityId: { type: Schema.Types.ObjectId, ref: 'Activity' },
    customFields: { type: Schema.Types.Mixed, default: {} },
    // AI-generated insights
    insights: {
      summary: { type: String },
      keyPoints: [{ type: String }],
      actionItems: [{ type: String }],
      sentimentAnalysis: {
        overall: {
          type: String,
          enum: ['positive', 'neutral', 'negative']
        },
        score: { type: Number, min: -1, max: 1 },
        topics: [{
          topic: { type: String, required: true },
          sentiment: {
            type: String,
            enum: ['positive', 'neutral', 'negative'],
            required: true
          },
          score: { type: Number, min: -1, max: 1, required: true }
        }]
      },
      aiConfidence: { type: Number, min: 0, max: 1 },
      lastUpdated: { type: Date, default: Date.now }
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
ActivitySchema.index({ type: 1, tenantId: 1 });
ActivitySchema.index({ date: 1, tenantId: 1 });
ActivitySchema.index({ status: 1, tenantId: 1 });
ActivitySchema.index({ contactId: 1, tenantId: 1 });
ActivitySchema.index({ companyId: 1, tenantId: 1 });
ActivitySchema.index({ opportunityId: 1, tenantId: 1 });
ActivitySchema.index({ owner: 1, tenantId: 1 });
ActivitySchema.index({ reminderDate: 1, tenantId: 1 });
ActivitySchema.index({ 'participants.id': 1, tenantId: 1 });
ActivitySchema.index({ 'participants.email': 1, tenantId: 1 });
ActivitySchema.index({ isRecurring: 1, tenantId: 1 });
ActivitySchema.index({ parentActivityId: 1, tenantId: 1 });
ActivitySchema.index({ source: 1, sourceId: 1, tenantId: 1 });

// Compound index for text search
ActivitySchema.index({ tenantId: 1, subject: 'text', description: 'text' });

// Create the model
export const Activity: Model<IActivity> = mongoose.models.Activity || mongoose.model<IActivity>('Activity', ActivitySchema);
