import mongoose, { Schema, Document } from 'mongoose';
import { IRawEvent } from '../types/events-raw';

// Define the schema
const RawEventSchema = new Schema<IRawEvent & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    source: {
      type: String,
      required: true,
      index: true,
    },
    eventType: {
      type: String,
      required: true,
      index: true,
    },
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
      index: true,
    },
    data: {
      type: Schema.Types.Mixed,
      required: true,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    processed: {
      type: Boolean,
      default: false,
      index: true,
    },
    processedAt: {
      type: Date,
    },
    error: {
      type: String,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: { createdAt: true, updatedAt: false },
  }
);

// Create compound indexes
RawEventSchema.index({ tenantId: 1, source: 1, eventType: 1, timestamp: 1 });
RawEventSchema.index({ tenantId: 1, processed: 1, timestamp: 1 });

// Create the model
export const RawEventModel = mongoose.model<IRawEvent & Document>(
  'RawEvent',
  RawEventSchema
);
