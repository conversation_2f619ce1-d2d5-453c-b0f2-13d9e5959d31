import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '@aizako/core-lib';

/**
 * A/B test interface
 */
export interface IABTest extends Document, TenantScopedEntity {
  name: string;
  description?: string;
  baseProposalId: mongoose.Types.ObjectId;
  variants: Array<{
    name: string;
    description?: string;
    proposalId: mongoose.Types.ObjectId;
    trafficPercentage: number;
  }>;
  baseTrafficPercentage: number;
  status: 'active' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * A/B test schema
 */
const ABTestSchema = new Schema<IABTest>(
  {
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    description: { type: String },
    baseProposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', required: true },
    variants: [{
      name: { type: String, required: true },
      description: { type: String },
      proposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', required: true },
      trafficPercentage: { type: Number, required: true, min: 0, max: 100 },
    }],
    baseTrafficPercentage: { type: Number, required: true, min: 0, max: 100 },
    status: { 
      type: String, 
      required: true, 
      enum: ['active', 'paused', 'completed'],
      default: 'active',
      index: true,
    },
    startDate: { type: Date, required: true, default: Date.now },
    endDate: { type: Date },
  },
  {
    timestamps: true,
  }
);

// Create indexes
ABTestSchema.index({ tenantId: 1, baseProposalId: 1 });
ABTestSchema.index({ tenantId: 1, status: 1 });
ABTestSchema.index({ tenantId: 1, createdAt: -1 });

// Create the model
export const ABTest: Model<IABTest> = mongoose.models.ABTest || 
  mongoose.model<IABTest>('ABTest', ABTestSchema);

export default ABTest;
