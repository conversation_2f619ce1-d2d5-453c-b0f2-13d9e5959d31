import mongoose, { Schema, Document } from 'mongoose';
import { ICBICache } from '../types/cbi-cache';

// Define the schema
const CBICacheSchema = new Schema<ICBICache & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    queryHash: {
      type: String,
      required: true,
    },
    query: {
      type: String,
      required: true,
    },
    result: {
      type: Schema.Types.Mixed,
      required: true,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    expiresAt: {
      type: Date,
      required: true,
      index: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: { createdAt: true, updatedAt: false },
  }
);

// Create compound index
CBICacheSchema.index({ tenantId: 1, queryHash: 1 }, { unique: true });

// Create the model
export const CBICacheModel = mongoose.model<ICBICache & Document>(
  'CBICache',
  CBICacheSchema
);
