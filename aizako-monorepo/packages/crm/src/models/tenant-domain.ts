import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '../types/shared';

/**
 * DNS record interface
 */
export interface IDNSRecord {
  type: 'TXT' | 'CNAME' | 'MX' | 'A';
  host: string;
  value: string;
  ttl?: number;
  priority?: number; // For MX records
  verified: boolean;
  verifiedAt?: Date;
}

/**
 * Domain service configuration interface
 */
export interface IDomainServiceConfig {
  service: 'email' | 'tracking' | 'website' | 'auth' | 'api' | 'other';
  enabled: boolean;
  status: 'pending' | 'active' | 'failed' | 'disabled';
  requiredRecords: IDNSRecord[];
  lastCheckedAt?: Date;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

/**
 * Tenant domain interface
 */
export interface ITenantDomain extends TenantScopedEntity {
  domain: string;
  isPrimary: boolean;
  status: 'pending' | 'active' | 'failed' | 'disabled';
  verified: boolean;
  verificationMethod: 'dns' | 'file' | 'email';
  verificationToken?: string;
  verificationTokenExpiresAt?: Date;
  verifiedAt?: Date;
  dnsRecords: IDNSRecord[];
  services: IDomainServiceConfig[];
  // Email configuration
  emailConfig?: {
    provider: 'resend' | 'sendgrid' | 'mailgun' | 'ses' | 'smtp' | 'other';
    fromEmail: string;
    fromName?: string;
    replyToEmail?: string;
    bccEmail?: string;
    dkimEnabled?: boolean;
    spfEnabled?: boolean;
    dmarcEnabled?: boolean;
    trackingEnabled?: boolean;
    trackingDomain?: string;
    unsubscribeEnabled?: boolean;
    unsubscribeLink?: string;
    footerEnabled?: boolean;
    footerText?: string;
    logoEnabled?: boolean;
    logoUrl?: string;
    providerDomainId?: string;
    providerStatus?: string;
    providerSettings?: Record<string, any>;
  };
  // Tracking configuration
  trackingConfig?: {
    provider: 'resend' | 'sendgrid' | 'mailgun' | 'custom';
    domain: string;
    enabled: boolean;
    clickTracking: boolean;
    openTracking: boolean;
    linkRewriting: boolean;
    providerDomainId?: string;
    providerStatus?: string;
    providerSettings?: Record<string, any>;
  };
  notes?: string;
  tags: string[];
  customFields: Record<string, any>;
}

/**
 * Tenant domain schema
 */
const TenantDomainSchema = new Schema<ITenantDomain>(
  {
    tenantId: { type: String, required: true, index: true },
    domain: { type: String, required: true, trim: true, lowercase: true },
    isPrimary: { type: Boolean, default: false },
    status: {
      type: String,
      enum: ['pending', 'active', 'failed', 'disabled'],
      default: 'pending',
      index: true
    },
    verified: { type: Boolean, default: false, index: true },
    verificationMethod: {
      type: String,
      enum: ['dns', 'file', 'email'],
      default: 'dns'
    },
    verificationToken: { type: String },
    verificationTokenExpiresAt: { type: Date },
    verifiedAt: { type: Date },
    dnsRecords: [{
      type: {
        type: String,
        enum: ['TXT', 'CNAME', 'MX', 'A'],
        required: true
      },
      host: { type: String, required: true, trim: true },
      value: { type: String, required: true, trim: true },
      ttl: { type: Number },
      priority: { type: Number }, // For MX records
      verified: { type: Boolean, default: false },
      verifiedAt: { type: Date }
    }],
    services: [{
      service: {
        type: String,
        enum: ['email', 'tracking', 'website', 'auth', 'api', 'other'],
        required: true
      },
      enabled: { type: Boolean, default: false },
      status: {
        type: String,
        enum: ['pending', 'active', 'failed', 'disabled'],
        default: 'pending'
      },
      requiredRecords: [{
        type: {
          type: String,
          enum: ['TXT', 'CNAME', 'MX', 'A'],
          required: true
        },
        host: { type: String, required: true, trim: true },
        value: { type: String, required: true, trim: true },
        ttl: { type: Number },
        priority: { type: Number }, // For MX records
        verified: { type: Boolean, default: false },
        verifiedAt: { type: Date }
      }],
      lastCheckedAt: { type: Date },
      errorMessage: { type: String },
      metadata: { type: Schema.Types.Mixed }
    }],
    // Email configuration
    emailConfig: {
      provider: {
        type: String,
        enum: ['resend', 'sendgrid', 'mailgun', 'ses', 'smtp', 'other'],
        default: 'resend'
      },
      fromEmail: { type: String, trim: true, lowercase: true },
      fromName: { type: String, trim: true },
      replyToEmail: { type: String, trim: true, lowercase: true },
      bccEmail: { type: String, trim: true, lowercase: true },
      dkimEnabled: { type: Boolean, default: true },
      spfEnabled: { type: Boolean, default: true },
      dmarcEnabled: { type: Boolean, default: true },
      trackingEnabled: { type: Boolean, default: true },
      trackingDomain: { type: String, trim: true, lowercase: true },
      unsubscribeEnabled: { type: Boolean, default: true },
      unsubscribeLink: { type: String, trim: true },
      footerEnabled: { type: Boolean, default: true },
      footerText: { type: String },
      logoEnabled: { type: Boolean, default: false },
      logoUrl: { type: String, trim: true },
      providerDomainId: { type: String },
      providerStatus: { type: String },
      providerSettings: { type: Schema.Types.Mixed }
    },
    // Tracking configuration
    trackingConfig: {
      provider: {
        type: String,
        enum: ['resend', 'sendgrid', 'mailgun', 'custom'],
        default: 'resend'
      },
      domain: { type: String, trim: true, lowercase: true },
      enabled: { type: Boolean, default: true },
      clickTracking: { type: Boolean, default: true },
      openTracking: { type: Boolean, default: true },
      linkRewriting: { type: Boolean, default: true },
      providerDomainId: { type: String },
      providerStatus: { type: String },
      providerSettings: { type: Schema.Types.Mixed }
    },
    notes: { type: String },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
TenantDomainSchema.index({ domain: 1 }, { unique: true });
TenantDomainSchema.index({ tenantId: 1, isPrimary: 1 });
TenantDomainSchema.index({ tenantId: 1, status: 1 });
TenantDomainSchema.index({ tenantId: 1, verified: 1 });
TenantDomainSchema.index({ 'services.service': 1, 'services.status': 1, tenantId: 1 });
TenantDomainSchema.index({ 'emailConfig.provider': 1, tenantId: 1 });
TenantDomainSchema.index({ 'trackingConfig.provider': 1, tenantId: 1 });

// Create the model
export const TenantDomain: Model<ITenantDomain> = mongoose.models.TenantDomain || mongoose.model<ITenantDomain>('TenantDomain', TenantDomainSchema);

export default TenantDomain;
