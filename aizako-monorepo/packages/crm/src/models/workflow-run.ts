import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '@aizako/core-lib';

/**
 * Workflow run step interface
 */
export interface IWorkflowRunStep {
  id: string;
  actionId: string;
  name: string;
  type: string;
  startTime: Date;
  endTime?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped' | 'canceled';
  input?: Record<string, any>;
  output?: Record<string, any>;
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
  retryCount: number;
  lastRetryAt?: Date;
  executionTime?: number;
}

/**
 * Workflow run interface
 */
export interface IWorkflowRun extends Document, TenantScopedEntity {
  workflowId: mongoose.Types.ObjectId;
  workflowVersionId?: mongoose.Types.ObjectId;
  workflowName: string;
  runId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'canceled';
  trigger: {
    type: string;
    source: string;
    data?: Record<string, any>;
  };
  steps: IWorkflowRunStep[];
  input?: Record<string, any>;
  output?: Record<string, any>;
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
  startTime: Date;
  endTime?: Date;
  executionTime?: number;
  initiatedBy?: mongoose.Types.ObjectId;
  canceledBy?: mongoose.Types.ObjectId;
  canceledAt?: Date;
  cancelReason?: string;
  retryCount: number;
  lastRetryAt?: Date;
  isManualRun: boolean;
  isTestRun: boolean;
  linkedEntities?: {
    contactIds?: mongoose.Types.ObjectId[];
    companyIds?: mongoose.Types.ObjectId[];
    opportunityIds?: mongoose.Types.ObjectId[];
    activityIds?: mongoose.Types.ObjectId[];
    taskIds?: mongoose.Types.ObjectId[];
    documentIds?: mongoose.Types.ObjectId[];
    proposalIds?: mongoose.Types.ObjectId[];
    emailIds?: string[];
  };
  tags: string[];
  customFields: Record<string, any>;
}

/**
 * Workflow run schema
 */
const WorkflowRunSchema = new Schema<IWorkflowRun>(
  {
    tenantId: { type: String, required: true, index: true },
    workflowId: { type: Schema.Types.ObjectId, ref: 'Workflow', required: true, index: true },
    workflowVersionId: { type: Schema.Types.ObjectId, ref: 'WorkflowVersion' },
    workflowName: { type: String, required: true, trim: true },
    runId: { type: String, required: true, index: true },
    status: {
      type: String,
      enum: ['pending', 'running', 'completed', 'failed', 'canceled'],
      default: 'pending',
      index: true
    },
    trigger: {
      type: { type: String, required: true },
      source: { type: String, required: true },
      data: { type: Schema.Types.Mixed }
    },
    steps: [{
      id: { type: String, required: true },
      actionId: { type: String, required: true },
      name: { type: String, required: true },
      type: { type: String, required: true },
      startTime: { type: Date, required: true },
      endTime: { type: Date },
      status: {
        type: String,
        enum: ['pending', 'running', 'completed', 'failed', 'skipped', 'canceled'],
        required: true
      },
      input: { type: Schema.Types.Mixed },
      output: { type: Schema.Types.Mixed },
      error: {
        message: { type: String },
        stack: { type: String },
        code: { type: String }
      },
      retryCount: { type: Number, default: 0 },
      lastRetryAt: { type: Date },
      executionTime: { type: Number }
    }],
    input: { type: Schema.Types.Mixed },
    output: { type: Schema.Types.Mixed },
    error: {
      message: { type: String },
      stack: { type: String },
      code: { type: String }
    },
    startTime: { type: Date, required: true, index: true },
    endTime: { type: Date },
    executionTime: { type: Number },
    initiatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    canceledBy: { type: Schema.Types.ObjectId, ref: 'User' },
    canceledAt: { type: Date },
    cancelReason: { type: String },
    retryCount: { type: Number, default: 0 },
    lastRetryAt: { type: Date },
    isManualRun: { type: Boolean, default: false, index: true },
    isTestRun: { type: Boolean, default: false, index: true },
    linkedEntities: {
      contactIds: [{ type: Schema.Types.ObjectId, ref: 'Contact' }],
      companyIds: [{ type: Schema.Types.ObjectId, ref: 'Company' }],
      opportunityIds: [{ type: Schema.Types.ObjectId, ref: 'Opportunity' }],
      activityIds: [{ type: Schema.Types.ObjectId, ref: 'Activity' }],
      taskIds: [{ type: Schema.Types.ObjectId, ref: 'Task' }],
      documentIds: [{ type: Schema.Types.ObjectId, ref: 'Document' }],
      proposalIds: [{ type: Schema.Types.ObjectId, ref: 'Proposal' }],
      emailIds: [{ type: String }]
    },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
WorkflowRunSchema.index({ runId: 1, tenantId: 1 }, { unique: true });
WorkflowRunSchema.index({ workflowId: 1, tenantId: 1 });
WorkflowRunSchema.index({ workflowVersionId: 1, tenantId: 1 });
WorkflowRunSchema.index({ status: 1, tenantId: 1 });
WorkflowRunSchema.index({ startTime: 1, tenantId: 1 });
WorkflowRunSchema.index({ endTime: 1, tenantId: 1 });
WorkflowRunSchema.index({ 'trigger.type': 1, tenantId: 1 });
WorkflowRunSchema.index({ 'trigger.source': 1, tenantId: 1 });
WorkflowRunSchema.index({ initiatedBy: 1, tenantId: 1 });
WorkflowRunSchema.index({ isManualRun: 1, tenantId: 1 });
WorkflowRunSchema.index({ isTestRun: 1, tenantId: 1 });
WorkflowRunSchema.index({ 'linkedEntities.contactIds': 1, tenantId: 1 });
WorkflowRunSchema.index({ 'linkedEntities.companyIds': 1, tenantId: 1 });
WorkflowRunSchema.index({ 'linkedEntities.opportunityIds': 1, tenantId: 1 });

// Create the model
export const WorkflowRun: Model<IWorkflowRun> = mongoose.models.WorkflowRun || mongoose.model<IWorkflowRun>('WorkflowRun', WorkflowRunSchema);

export default WorkflowRun;
