import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Proposal analytics event interface
 */
export interface IProposalAnalyticsEvent extends Document {
  proposalId: mongoose.Types.ObjectId;
  eventType: string;
  data: Record<string, any>;
  timestamp: Date;
}

/**
 * Proposal analytics event schema
 */
const ProposalAnalyticsEventSchema = new Schema<IProposalAnalyticsEvent>(
  {
    proposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', required: true, index: true },
    eventType: { type: String, required: true, index: true },
    data: { type: Schema.Types.Mixed, default: {} },
    timestamp: { type: Date, default: Date.now, index: true },
  },
  {
    timestamps: false,
  }
);

// Create indexes
ProposalAnalyticsEventSchema.index({ proposalId: 1, eventType: 1, timestamp: 1 });

// Create the model
export const ProposalAnalyticsEvent: Model<IProposalAnalyticsEvent> = mongoose.models.ProposalAnalyticsEvent || 
  mongoose.model<IProposalAnalyticsEvent>('ProposalAnalyticsEvent', ProposalAnalyticsEventSchema);

export default ProposalAnalyticsEvent;
