import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '../types/shared';

/**
 * Tag interface
 */
export interface ITag extends Document, TenantScopedEntity {
  name: string;
  description?: string;
  color: string;
  category?: string;
  isSystem: boolean;
  isArchived: boolean;
  entityTypes: Array<'contact' | 'company' | 'opportunity' | 'activity' | 'task' | 'document' | 'proposal' | 'email' | 'sequence' | 'workflow'>;
  usageCount: {
    contact?: number;
    company?: number;
    opportunity?: number;
    activity?: number;
    task?: number;
    document?: number;
    proposal?: number;
    email?: number;
    sequence?: number;
    workflow?: number;
    total: number;
  };
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  customFields: Record<string, any>;
}

/**
 * Tag schema
 */
const TagSchema = new Schema<ITag>(
  {
    tenantId: { type: String, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    color: { type: String, default: '#6366F1' }, // Default indigo color
    category: { type: String, trim: true },
    isSystem: { type: Boolean, default: false },
    isArchived: { type: Boolean, default: false },
    entityTypes: [{
      type: String,
      enum: ['contact', 'company', 'opportunity', 'activity', 'task', 'document', 'proposal', 'email', 'sequence', 'workflow'],
      required: true
    }],
    usageCount: {
      contact: { type: Number, default: 0 },
      company: { type: Number, default: 0 },
      opportunity: { type: Number, default: 0 },
      activity: { type: Number, default: 0 },
      task: { type: Number, default: 0 },
      document: { type: Number, default: 0 },
      proposal: { type: Number, default: 0 },
      email: { type: Number, default: 0 },
      sequence: { type: Number, default: 0 },
      workflow: { type: Number, default: 0 },
      total: { type: Number, default: 0 }
    },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    customFields: { type: Schema.Types.Mixed, default: {} }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
TagSchema.index({ name: 1, tenantId: 1 }, { unique: true });
TagSchema.index({ category: 1, tenantId: 1 });
TagSchema.index({ isSystem: 1, tenantId: 1 });
TagSchema.index({ isArchived: 1, tenantId: 1 });
TagSchema.index({ 'entityTypes': 1, tenantId: 1 });
TagSchema.index({ 'usageCount.total': -1, tenantId: 1 });

// Compound index for text search
TagSchema.index({ tenantId: 1, name: 'text', description: 'text' });

// Create the model
export const Tag: Model<ITag> = mongoose.models.Tag || mongoose.model<ITag>('Tag', TagSchema);

export default Tag;
