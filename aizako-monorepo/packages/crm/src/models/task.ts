import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '../types/shared';

/**
 * Task interface
 */
export interface ITask extends Document, TenantScopedEntity {
  title: string;
  description?: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'deferred' | 'canceled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate?: Date;
  startDate?: Date;
  completedDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  progress?: number; // 0-100
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  opportunityId?: mongoose.Types.ObjectId;
  owner: mongoose.Types.ObjectId;
  assignedTo: mongoose.Types.ObjectId;
  completedBy?: mongoose.Types.ObjectId;
  parentTaskId?: mongoose.Types.ObjectId;
  subtasks?: Array<{
    id: string;
    title: string;
    status: 'not_started' | 'in_progress' | 'completed' | 'canceled';
    dueDate?: Date;
    completedDate?: Date;
    assignedTo?: mongoose.Types.ObjectId;
  }>;
  reminders?: Array<{
    id: string;
    time: Date;
    sent: boolean;
    sentAt?: Date;
    method: 'email' | 'notification' | 'sms';
  }>;
  recurrence?: {
    pattern: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    daysOfWeek?: number[];
    dayOfMonth?: number;
    monthOfYear?: number;
    endDate?: Date;
    occurrences?: number;
    seriesId?: string;
  };
  isRecurring: boolean;
  source: 'manual' | 'sequence' | 'workflow' | 'ai' | 'integration';
  sourceId?: string;
  activityId?: mongoose.Types.ObjectId;
  tags: string[];
  customFields: Record<string, any>;
  // AI-generated content
  aiGenerated: boolean;
  aiPrompt?: string;
  aiModel?: string;
  aiConfidence?: number;
  // AI-generated suggestions
  aiSuggestions?: Array<{
    id: string;
    type: 'next_step' | 'resource' | 'contact' | 'timing' | 'approach';
    content: string;
    confidence: number;
    accepted: boolean;
    acceptedAt?: Date;
    rejected: boolean;
    rejectedAt?: Date;
  }>;
}

/**
 * Task schema
 */
const TaskSchema = new Schema<ITask>(
  {
    tenantId: { type: String, required: true, index: true },
    title: { type: String, required: true, trim: true },
    description: { type: String },
    status: {
      type: String,
      enum: ['not_started', 'in_progress', 'completed', 'deferred', 'canceled'],
      default: 'not_started',
      index: true
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
      index: true
    },
    dueDate: { type: Date, index: true },
    startDate: { type: Date },
    completedDate: { type: Date },
    estimatedHours: { type: Number, min: 0 },
    actualHours: { type: Number, min: 0 },
    progress: { type: Number, min: 0, max: 100, default: 0 },
    contactId: { type: Schema.Types.ObjectId, ref: 'Contact', index: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', index: true },
    opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', index: true },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    assignedTo: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    completedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    parentTaskId: { type: Schema.Types.ObjectId, ref: 'Task', index: true },
    subtasks: [{
      id: { type: String, required: true },
      title: { type: String, required: true, trim: true },
      status: {
        type: String,
        enum: ['not_started', 'in_progress', 'completed', 'canceled'],
        default: 'not_started'
      },
      dueDate: { type: Date },
      completedDate: { type: Date },
      assignedTo: { type: Schema.Types.ObjectId, ref: 'User' }
    }],
    reminders: [{
      id: { type: String, required: true },
      time: { type: Date, required: true },
      sent: { type: Boolean, default: false },
      sentAt: { type: Date },
      method: {
        type: String,
        enum: ['email', 'notification', 'sms'],
        default: 'notification'
      }
    }],
    recurrence: {
      pattern: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'yearly']
      },
      interval: { type: Number, min: 1, default: 1 },
      daysOfWeek: [{ type: Number, min: 0, max: 6 }],
      dayOfMonth: { type: Number, min: 1, max: 31 },
      monthOfYear: { type: Number, min: 0, max: 11 },
      endDate: { type: Date },
      occurrences: { type: Number, min: 1 },
      seriesId: { type: String }
    },
    isRecurring: { type: Boolean, default: false, index: true },
    source: {
      type: String,
      enum: ['manual', 'sequence', 'workflow', 'ai', 'integration'],
      default: 'manual',
      index: true
    },
    sourceId: { type: String },
    activityId: { type: Schema.Types.ObjectId, ref: 'Activity' },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // AI-generated content
    aiGenerated: { type: Boolean, default: false, index: true },
    aiPrompt: { type: String },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 },
    // AI-generated suggestions
    aiSuggestions: [{
      id: { type: String, required: true },
      type: {
        type: String,
        enum: ['next_step', 'resource', 'contact', 'timing', 'approach'],
        required: true
      },
      content: { type: String, required: true },
      confidence: { type: Number, min: 0, max: 1, required: true },
      accepted: { type: Boolean, default: false },
      acceptedAt: { type: Date },
      rejected: { type: Boolean, default: false },
      rejectedAt: { type: Date }
    }]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
TaskSchema.index({ title: 1, tenantId: 1 });
TaskSchema.index({ status: 1, tenantId: 1 });
TaskSchema.index({ priority: 1, tenantId: 1 });
TaskSchema.index({ dueDate: 1, tenantId: 1 });
TaskSchema.index({ assignedTo: 1, tenantId: 1 });
TaskSchema.index({ owner: 1, tenantId: 1 });
TaskSchema.index({ 'reminders.time': 1, 'reminders.sent': 1, tenantId: 1 });
TaskSchema.index({ source: 1, sourceId: 1, tenantId: 1 });

// Compound index for text search
TaskSchema.index({ tenantId: 1, title: 'text', description: 'text' });

// Create the model
export const Task: Model<ITask> = mongoose.models.Task || mongoose.model<ITask>('Task', TaskSchema);

export default Task;
