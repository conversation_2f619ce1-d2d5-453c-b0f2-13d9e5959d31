import mongoose, { <PERSON>hem<PERSON>, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '../types/shared';

/**
 * Proposal section interface
 */
export interface IProposalSection {
  id: string;
  title: string;
  content: string;
  order: number;
  type: 'text' | 'pricing' | 'timeline' | 'team' | 'testimonials' | 'images' | 'custom';
  isVisible: boolean;
  metadata?: Record<string, any>;
  aiGenerated?: boolean;
  aiConfidence?: number;
}

/**
 * Proposal pricing item interface
 */
export interface IProposalPricingItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  discount?: number;
  tax?: number;
  total: number;
  isOptional?: boolean;
  metadata?: Record<string, any>;
}

/**
 * Proposal analytics event interface
 */
export interface IProposalAnalyticsEvent {
  eventType: 'view' | 'download' | 'share' | 'comment' | 'accept' | 'reject' | 'section_view';
  timestamp: Date;
  userId?: mongoose.Types.ObjectId;
  userEmail?: string;
  userName?: string;
  ipAddress?: string;
  userAgent?: string;
  duration?: number;
  sectionId?: string;
  metadata?: Record<string, any>;
}

/**
 * Proposal interface
 */
export interface IProposal extends TenantScopedEntity {
  title: string;
  description?: string;
  opportunityId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  contactIds: mongoose.Types.ObjectId[];
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired';
  expiresAt?: Date;
  sections: IProposalSection[];
  pricing?: {
    currency: string;
    items: IProposalPricingItem[];
    subtotal: number;
    discount?: number;
    tax?: number;
    total: number;
  };
  terms?: string;
  notes?: string;
  owner: mongoose.Types.ObjectId;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  sentAt?: Date;
  sentBy?: mongoose.Types.ObjectId;
  viewedAt?: Date;
  acceptedAt?: Date;
  acceptedBy?: mongoose.Types.ObjectId;
  rejectedAt?: Date;
  rejectedBy?: mongoose.Types.ObjectId;
  rejectionReason?: string;
  publicUrl: string;
  publicToken: string;
  publicAccessEnabled: boolean;
  emailEnabled: boolean;
  downloadEnabled: boolean;
  downloadFormats: ('pdf' | 'docx' | 'md')[];
  analyticsEvents?: IProposalAnalyticsEvent[];
  viewCount: number;
  lastViewedAt?: Date;
  comments?: Array<{
    id: string;
    text: string;
    userId: mongoose.Types.ObjectId;
    userName?: string;
    createdAt: Date;
    isInternal: boolean;
  }>;
  tags: string[];
  customFields: Record<string, any>;
  // AI-generated content
  aiGenerated: boolean;
  aiPrompt?: string;
  aiModel?: string;
  aiConfidence?: number;
}

/**
 * Proposal schema
 */
const ProposalSchema = new Schema<IProposal>(
  {
    tenantId: { type: String, required: true, index: true },
    title: { type: String, required: true, trim: true },
    description: { type: String },
    opportunityId: { type: Schema.Types.ObjectId, ref: 'Opportunity', index: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', index: true },
    contactIds: [{ type: Schema.Types.ObjectId, ref: 'Contact', index: true }],
    status: {
      type: String,
      enum: ['draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired'],
      default: 'draft',
      index: true
    },
    expiresAt: { type: Date },
    sections: [{
      id: { type: String, required: true },
      title: { type: String, required: true },
      content: { type: String, required: true },
      order: { type: Number, required: true },
      type: {
        type: String,
        enum: ['text', 'pricing', 'timeline', 'team', 'testimonials', 'images', 'custom'],
        default: 'text'
      },
      isVisible: { type: Boolean, default: true },
      metadata: { type: Schema.Types.Mixed },
      aiGenerated: { type: Boolean, default: false },
      aiConfidence: { type: Number, min: 0, max: 1 }
    }],
    pricing: {
      currency: { type: String, default: 'USD' },
      items: [{
        id: { type: String, required: true },
        name: { type: String, required: true },
        description: { type: String },
        quantity: { type: Number, required: true, min: 0 },
        unitPrice: { type: Number, required: true, min: 0 },
        discount: { type: Number, min: 0 },
        tax: { type: Number, min: 0 },
        total: { type: Number, required: true, min: 0 },
        isOptional: { type: Boolean, default: false },
        metadata: { type: Schema.Types.Mixed }
      }],
      subtotal: { type: Number, min: 0 },
      discount: { type: Number, min: 0 },
      tax: { type: Number, min: 0 },
      total: { type: Number, min: 0 }
    },
    terms: { type: String },
    notes: { type: String },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    sentAt: { type: Date },
    sentBy: { type: Schema.Types.ObjectId, ref: 'User' },
    viewedAt: { type: Date },
    acceptedAt: { type: Date },
    acceptedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    rejectedAt: { type: Date },
    rejectedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    rejectionReason: { type: String },
    publicUrl: { type: String, required: true, default: '' },
    publicToken: { type: String, required: true, default: '' },
    publicAccessEnabled: { type: Boolean, default: true },
    emailEnabled: { type: Boolean, default: true },
    downloadEnabled: { type: Boolean, default: true },
    downloadFormats: [{
      type: String,
      enum: ['pdf', 'docx', 'md'],
      default: ['pdf']
    }],
    analyticsEvents: [{
      eventType: {
        type: String,
        enum: ['view', 'download', 'share', 'comment', 'accept', 'reject', 'section_view'],
        required: true
      },
      timestamp: { type: Date, default: Date.now, required: true },
      userId: { type: Schema.Types.ObjectId, ref: 'User' },
      userEmail: { type: String },
      userName: { type: String },
      ipAddress: { type: String },
      userAgent: { type: String },
      duration: { type: Number },
      sectionId: { type: String },
      metadata: { type: Schema.Types.Mixed }
    }],
    viewCount: { type: Number, default: 0 },
    lastViewedAt: { type: Date },
    comments: [{
      id: { type: String, required: true },
      text: { type: String, required: true },
      userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
      userName: { type: String },
      createdAt: { type: Date, default: Date.now },
      isInternal: { type: Boolean, default: false }
    }],
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // AI-generated content
    aiGenerated: { type: Boolean, default: false },
    aiPrompt: { type: String },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
ProposalSchema.index({ title: 1, tenantId: 1 });
ProposalSchema.index({ status: 1, tenantId: 1 });
ProposalSchema.index({ expiresAt: 1, tenantId: 1 });
ProposalSchema.index({ publicToken: 1 });
ProposalSchema.index({ 'analyticsEvents.timestamp': -1 });
ProposalSchema.index({ aiGenerated: 1, tenantId: 1 });

// Compound index for text search
ProposalSchema.index({ tenantId: 1, title: 'text', description: 'text' });

// Create the model
export const Proposal: Model<IProposal> = mongoose.models.Proposal || mongoose.model<IProposal>('Proposal', ProposalSchema);

export default Proposal;
