import mongoose, { Schema, Document } from 'mongoose';
import { IEdge } from '../types/edge';

// Define the schema
const EdgeSchema = new Schema<IEdge & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    sourceId: {
      type: String,
      required: true,
      index: true,
    },
    sourceType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity', 'activity', 'document', 'email', 'other'],
      index: true,
    },
    targetId: {
      type: String,
      required: true,
      index: true,
    },
    targetType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity', 'activity', 'document', 'email', 'other'],
      index: true,
    },
    edgeType: {
      type: String,
      required: true,
      index: true,
    },
    weight: {
      type: Number,
      default: 1.0,
    },
    properties: {
      type: Schema.Types.Mixed,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create compound indexes
EdgeSchema.index({ tenantId: 1, sourceId: 1, sourceType: 1, edgeType: 1 });
EdgeSchema.index({ tenantId: 1, targetId: 1, targetType: 1, edgeType: 1 });
EdgeSchema.index(
  { tenantId: 1, sourceId: 1, sourceType: 1, targetId: 1, targetType: 1, edgeType: 1 },
  { unique: true }
);

// Create the model
export const EdgeModel = mongoose.model<IEdge & Document>(
  'Edge',
  EdgeSchema
);
