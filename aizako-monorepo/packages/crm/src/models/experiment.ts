import mongoose, { Schema, Document } from 'mongoose';
import { IExperiment } from '../types/experiment';

// Define the schema
const ExperimentSchema = new Schema<IExperiment & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    hypothesis: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ['draft', 'running', 'paused', 'completed', 'cancelled'],
      default: 'draft',
    },
    type: {
      type: String,
      enum: ['a/b', 'multivariate', 'feature-flag', 'bandit'],
      default: 'a/b',
    },
    variants: {
      type: [
        {
          name: String,
          description: String,
          key: String,
          weight: Number,
          isControl: Boolean,
        },
      ],
      required: true,
    },
    targetAudience: {
      type: {
        type: String,
        enum: ['all', 'percentage', 'segment'],
        default: 'all',
      },
      percentage: Number,
      segmentId: String,
    },
    goals: {
      type: [
        {
          name: String,
          description: String,
          eventType: String,
          eventProperties: Schema.Types.Mixed,
          isMainGoal: Boolean,
        },
      ],
      required: true,
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
    },
    results: {
      type: [
        {
          variantKey: String,
          impressions: Number,
          conversions: Schema.Types.Mixed,
          conversionRates: Schema.Types.Mixed,
          improvementRates: Schema.Types.Mixed,
          confidenceLevels: Schema.Types.Mixed,
        },
      ],
      default: [],
    },
    winner: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdBy: {
      type: String,
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
ExperimentSchema.index({ tenantId: 1, status: 1 });
ExperimentSchema.index({ tenantId: 1, type: 1 });
ExperimentSchema.index({ tenantId: 1, startDate: 1, endDate: 1 });

// Create the model
export const ExperimentModel = mongoose.model<IExperiment & Document>(
  'Experiment',
  ExperimentSchema
);
