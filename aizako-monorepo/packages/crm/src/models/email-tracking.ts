import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '../types/shared';

/**
 * Email tracking event interface
 */
export interface IEmailTrackingEvent {
  type: 'open' | 'click' | 'bounce' | 'spam' | 'unsubscribe' | 'delivery';
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    city?: string;
    region?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
  };
  device?: {
    type?: 'mobile' | 'tablet' | 'desktop' | 'other';
    browser?: string;
    os?: string;
  };
  url?: string; // For click events
  category?: string; // For bounce/spam events
  reason?: string; // For bounce/spam events
  metadata?: Record<string, any>;
}

/**
 * Email tracking interface
 */
export interface IEmailTracking extends Document, TenantScopedEntity {
  emailId: string; // Unique ID from email provider
  subject: string;
  sender: {
    email: string;
    name?: string;
    userId?: mongoose.Types.ObjectId;
  };
  recipients: Array<{
    email: string;
    name?: string;
    type: 'to' | 'cc' | 'bcc';
    contactId?: mongoose.Types.ObjectId;
  }>;
  sentAt: Date;
  status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'spam' | 'unsubscribed';
  openCount: number;
  clickCount: number;
  lastOpenedAt?: Date;
  lastClickedAt?: Date;
  events: IEmailTrackingEvent[];
  linkedObjects: {
    contactIds?: mongoose.Types.ObjectId[];
    companyIds?: mongoose.Types.ObjectId[];
    opportunityIds?: mongoose.Types.ObjectId[];
    activityIds?: mongoose.Types.ObjectId[];
    proposalIds?: mongoose.Types.ObjectId[];
    sequenceIds?: mongoose.Types.ObjectId[];
  };
  source: 'manual' | 'sequence' | 'campaign' | 'template' | 'api';
  sourceId?: string;
  templateId?: string;
  templateName?: string;
  campaignId?: string;
  campaignName?: string;
  sequenceId?: string;
  sequenceName?: string;
  sequenceStepId?: string;
  tags: string[];
  customFields: Record<string, any>;
  // Domain verification and branding
  domain?: string;
  isCustomDomain: boolean;
  trackingDomain?: string;
  // Analytics
  analytics?: {
    openRate?: number;
    clickRate?: number;
    bounceRate?: number;
    spamRate?: number;
    unsubscribeRate?: number;
    engagementScore?: number;
  };
}

/**
 * Email tracking schema
 */
const EmailTrackingSchema = new Schema<IEmailTracking>(
  {
    tenantId: { type: String, required: true, index: true },
    emailId: { type: String, required: true, index: true },
    subject: { type: String, required: true, trim: true },
    sender: {
      email: { type: String, required: true, trim: true, lowercase: true },
      name: { type: String, trim: true },
      userId: { type: Schema.Types.ObjectId, ref: 'User' }
    },
    recipients: [{
      email: { type: String, required: true, trim: true, lowercase: true },
      name: { type: String, trim: true },
      type: {
        type: String,
        enum: ['to', 'cc', 'bcc'],
        default: 'to'
      },
      contactId: { type: Schema.Types.ObjectId, ref: 'Contact' }
    }],
    sentAt: { type: Date, required: true, index: true },
    status: {
      type: String,
      enum: ['sent', 'delivered', 'opened', 'clicked', 'bounced', 'spam', 'unsubscribed'],
      default: 'sent',
      index: true
    },
    openCount: { type: Number, default: 0 },
    clickCount: { type: Number, default: 0 },
    lastOpenedAt: { type: Date },
    lastClickedAt: { type: Date },
    events: [{
      type: {
        type: String,
        enum: ['open', 'click', 'bounce', 'spam', 'unsubscribe', 'delivery'],
        required: true
      },
      timestamp: { type: Date, required: true, index: true },
      ipAddress: { type: String },
      userAgent: { type: String },
      location: {
        city: { type: String },
        region: { type: String },
        country: { type: String },
        latitude: { type: Number },
        longitude: { type: Number }
      },
      device: {
        type: {
          type: String,
          enum: ['mobile', 'tablet', 'desktop', 'other']
        },
        browser: { type: String },
        os: { type: String }
      },
      url: { type: String }, // For click events
      category: { type: String }, // For bounce/spam events
      reason: { type: String }, // For bounce/spam events
      metadata: { type: Schema.Types.Mixed }
    }],
    linkedObjects: {
      contactIds: [{ type: Schema.Types.ObjectId, ref: 'Contact' }],
      companyIds: [{ type: Schema.Types.ObjectId, ref: 'Company' }],
      opportunityIds: [{ type: Schema.Types.ObjectId, ref: 'Opportunity' }],
      activityIds: [{ type: Schema.Types.ObjectId, ref: 'Activity' }],
      proposalIds: [{ type: Schema.Types.ObjectId, ref: 'Proposal' }],
      sequenceIds: [{ type: Schema.Types.ObjectId, ref: 'Sequence' }]
    },
    source: {
      type: String,
      enum: ['manual', 'sequence', 'campaign', 'template', 'api'],
      default: 'manual',
      index: true
    },
    sourceId: { type: String },
    templateId: { type: String },
    templateName: { type: String },
    campaignId: { type: String },
    campaignName: { type: String },
    sequenceId: { type: String },
    sequenceName: { type: String },
    sequenceStepId: { type: String },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // Domain verification and branding
    domain: { type: String, trim: true, lowercase: true },
    isCustomDomain: { type: Boolean, default: false },
    trackingDomain: { type: String, trim: true, lowercase: true },
    // Analytics
    analytics: {
      openRate: { type: Number, min: 0, max: 100 },
      clickRate: { type: Number, min: 0, max: 100 },
      bounceRate: { type: Number, min: 0, max: 100 },
      spamRate: { type: Number, min: 0, max: 100 },
      unsubscribeRate: { type: Number, min: 0, max: 100 },
      engagementScore: { type: Number, min: 0, max: 100 }
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
EmailTrackingSchema.index({ 'sender.email': 1, tenantId: 1 });
EmailTrackingSchema.index({ 'recipients.email': 1, tenantId: 1 });
EmailTrackingSchema.index({ 'recipients.contactId': 1, tenantId: 1 });
EmailTrackingSchema.index({ 'linkedObjects.contactIds': 1, tenantId: 1 });
EmailTrackingSchema.index({ 'linkedObjects.companyIds': 1, tenantId: 1 });
EmailTrackingSchema.index({ 'linkedObjects.opportunityIds': 1, tenantId: 1 });
EmailTrackingSchema.index({ source: 1, sourceId: 1, tenantId: 1 });
EmailTrackingSchema.index({ templateId: 1, tenantId: 1 });
EmailTrackingSchema.index({ campaignId: 1, tenantId: 1 });
EmailTrackingSchema.index({ sequenceId: 1, tenantId: 1 });
EmailTrackingSchema.index({ domain: 1, tenantId: 1 });

// Compound index for text search
EmailTrackingSchema.index({ tenantId: 1, subject: 'text' });

// Create the model
export const EmailTracking: Model<IEmailTracking> = mongoose.models.EmailTracking || mongoose.model<IEmailTracking>('EmailTracking', EmailTrackingSchema);

export default EmailTracking;
