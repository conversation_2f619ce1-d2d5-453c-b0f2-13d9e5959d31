import mongoose, { Schema, Document } from 'mongoose';
import { IMarketingCampaign } from '../types/marketing-campaign';

// Define the schema
const MarketingCampaignSchema = new Schema<IMarketingCampaign & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    type: {
      type: String,
      required: true,
      enum: ['email', 'social', 'content', 'event', 'webinar', 'paid-search', 'paid-social', 'display', 'other'],
    },
    status: {
      type: String,
      required: true,
      enum: ['draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled'],
      default: 'draft',
    },
    budget: {
      amount: Number,
      currency: {
        type: String,
        default: 'USD',
      },
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
    },
    goals: {
      type: [
        {
          type: {
            type: String,
            enum: ['impressions', 'clicks', 'leads', 'opportunities', 'revenue'],
          },
          target: Number,
          actual: {
            type: Number,
            default: 0,
          },
        },
      ],
      default: [],
    },
    channels: {
      type: [String],
      default: [],
    },
    tags: {
      type: [String],
      default: [],
    },
    targetAudience: {
      type: Schema.Types.Mixed,
    },
    metrics: {
      impressions: {
        type: Number,
        default: 0,
      },
      clicks: {
        type: Number,
        default: 0,
      },
      leads: {
        type: Number,
        default: 0,
      },
      opportunities: {
        type: Number,
        default: 0,
      },
      revenue: {
        type: Number,
        default: 0,
      },
      cost: {
        type: Number,
        default: 0,
      },
      roi: {
        type: Number,
      },
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdBy: {
      type: String,
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
MarketingCampaignSchema.index({ tenantId: 1, status: 1 });
MarketingCampaignSchema.index({ tenantId: 1, type: 1 });
MarketingCampaignSchema.index({ tenantId: 1, startDate: 1, endDate: 1 });
MarketingCampaignSchema.index({ tenantId: 1, tags: 1 });

// Create the model
export const MarketingCampaignModel = mongoose.model<IMarketingCampaign & Document>(
  'MarketingCampaign',
  MarketingCampaignSchema
);
