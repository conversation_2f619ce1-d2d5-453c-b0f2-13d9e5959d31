import mongoose, { Schema, Document } from 'mongoose';
import { IAIChat, IAIChatMessage } from '../types/ai-chat';

// Define the message schema
const AIChatMessageSchema = new Schema<IAIChatMessage>(
  {
    role: {
      type: String,
      required: true,
      enum: ['user', 'assistant', 'system'],
    },
    content: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    _id: true,
    timestamps: false,
  }
);

// Define the chat schema
const AIChatSchema = new Schema<IAIChat>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    title: {
      type: String,
      required: true,
    },
    contextType: {
      type: String,
      enum: ['contact', 'company', 'opportunity', 'proposal', 'general', 'other'],
      default: 'general',
    },
    contextId: {
      type: String,
    },
    messages: {
      type: [AIChatMessageSchema],
      default: [],
    },
    modelName: {
      type: String,
      required: true,
      default: 'claude-3-opus',
    },
    status: {
      type: String,
      enum: ['active', 'archived'],
      default: 'active',
    },
    tags: {
      type: [String],
      default: [],
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
AIChatSchema.index({ tenantId: 1, userId: 1, createdAt: -1 });
AIChatSchema.index({ tenantId: 1, contextType: 1, contextId: 1 });
AIChatSchema.index({ tenantId: 1, tags: 1 });

// Create the model
export const AIChat = mongoose.models.AIChat || mongoose.model<IAIChat>(
  'AIChat',
  AIChatSchema
);

export default AIChat;
