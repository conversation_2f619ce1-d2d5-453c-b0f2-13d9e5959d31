import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '@aizako/core-lib';
import { IWorkflowTrigger, IWorkflowAction, IWorkflowCondition } from './workflow';

/**
 * Workflow version interface
 */
export interface IWorkflowVersion extends Document, TenantScopedEntity {
  workflowId: mongoose.Types.ObjectId;
  version: number;
  name: string;
  description?: string;
  trigger: IWorkflowTrigger;
  actions: IWorkflowAction[];
  conditions?: IWorkflowCondition[];
  conditionLogic?: 'and' | 'or' | 'custom';
  customConditionLogic?: string;
  isActive: boolean;
  isPublished: boolean;
  publishedAt?: Date;
  publishedBy?: mongoose.Types.ObjectId;
  executionCount: number;
  lastExecutedAt?: Date;
  successCount: number;
  failureCount: number;
  averageExecutionTime?: number;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  notes?: string;
  tags: string[];
  customFields: Record<string, any>;
  // AI-generated content
  aiGenerated: boolean;
  aiPrompt?: string;
  aiModel?: string;
  aiConfidence?: number;
  // Natural language definition
  nlDefinition?: string;
}

/**
 * Workflow version schema
 */
const WorkflowVersionSchema = new Schema<IWorkflowVersion>(
  {
    tenantId: { type: String, required: true, index: true },
    workflowId: { type: Schema.Types.ObjectId, ref: 'Workflow', required: true, index: true },
    version: { type: Number, required: true },
    name: { type: String, required: true, trim: true },
    description: { type: String },
    trigger: {
      type: {
        type: String,
        enum: ['event', 'schedule', 'webhook', 'manual', 'ai_recommendation'],
        required: true
      },
      // Event trigger
      eventType: { type: String },
      eventSource: { type: String },
      eventConditions: { type: Schema.Types.Mixed },
      // Schedule trigger
      schedule: {
        frequency: {
          type: String,
          enum: ['minutely', 'hourly', 'daily', 'weekly', 'monthly']
        },
        interval: { type: Number, min: 1 },
        startTime: { type: Date },
        endTime: { type: Date },
        daysOfWeek: [{ type: Number, min: 0, max: 6 }],
        dayOfMonth: { type: Number, min: 1, max: 31 },
        timezone: { type: String }
      },
      // Webhook trigger
      webhook: {
        endpoint: { type: String },
        method: {
          type: String,
          enum: ['GET', 'POST', 'PUT', 'DELETE']
        },
        headers: { type: Map, of: String },
        authType: {
          type: String,
          enum: ['none', 'basic', 'bearer', 'api_key']
        },
        authCredentials: { type: Map, of: String }
      },
      // AI recommendation trigger
      aiRecommendation: {
        type: {
          type: String,
          enum: ['contact_engagement', 'opportunity_risk', 'lead_scoring', 'custom']
        },
        threshold: { type: Number, min: 0, max: 1 },
        frequency: {
          type: String,
          enum: ['realtime', 'hourly', 'daily', 'weekly']
        }
      }
    },
    actions: [{
      id: { type: String, required: true },
      type: {
        type: String,
        enum: ['create_record', 'update_record', 'delete_record', 'send_email', 'create_task', 'create_activity', 'add_to_sequence', 'remove_from_sequence', 'webhook', 'custom', 'ai_action'],
        required: true
      },
      name: { type: String, required: true, trim: true },
      description: { type: String },
      order: { type: Number, required: true },
      // Record actions
      recordType: {
        type: String,
        enum: ['contact', 'company', 'opportunity', 'activity', 'task', 'document', 'proposal', 'email', 'sequence']
      },
      recordId: { type: String },
      recordData: { type: Schema.Types.Mixed },
      // Email action
      email: {
        templateId: { type: Schema.Types.ObjectId, ref: 'EmailTemplate' },
        subject: { type: String },
        body: { type: String },
        to: [{ type: String }],
        cc: [{ type: String }],
        bcc: [{ type: String }],
        attachments: [{
          name: { type: String, required: true },
          type: { type: String, required: true },
          content: { type: String, required: true }
        }]
      },
      // Task action
      task: {
        title: { type: String },
        description: { type: String },
        dueDate: { type: Schema.Types.Mixed }, // Date or string
        priority: {
          type: String,
          enum: ['low', 'medium', 'high', 'urgent']
        },
        assignedTo: { type: Schema.Types.ObjectId, ref: 'User' }
      },
      // Activity action
      activity: {
        type: {
          type: String,
          enum: ['call', 'email', 'meeting', 'task', 'note']
        },
        subject: { type: String },
        description: { type: String },
        date: { type: Schema.Types.Mixed }, // Date or string
        duration: { type: Number },
        assignedTo: { type: Schema.Types.ObjectId, ref: 'User' }
      },
      // Sequence action
      sequence: {
        sequenceId: { type: Schema.Types.ObjectId, ref: 'Sequence' },
        contactId: { type: Schema.Types.ObjectId, ref: 'Contact' }
      },
      // Webhook action
      webhook: {
        url: { type: String },
        method: {
          type: String,
          enum: ['GET', 'POST', 'PUT', 'DELETE']
        },
        headers: { type: Map, of: String },
        body: { type: Schema.Types.Mixed },
        timeout: { type: Number }
      },
      // Custom action
      custom: {
        functionName: { type: String },
        parameters: { type: Schema.Types.Mixed }
      },
      // AI action
      aiAction: {
        type: {
          type: String,
          enum: ['generate_email', 'summarize_interaction', 'recommend_next_step', 'score_lead', 'custom']
        },
        prompt: { type: String },
        model: { type: String },
        parameters: { type: Schema.Types.Mixed }
      },
      // Conditions for this action
      conditions: [{
        id: { type: String, required: true },
        type: {
          type: String,
          enum: ['data', 'time', 'user', 'custom'],
          required: true
        },
        field: { type: String },
        operator: {
          type: String,
          enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'exists', 'not_exists', 'between', 'custom'],
          required: true
        },
        value: { type: Schema.Types.Mixed },
        valueType: {
          type: String,
          enum: ['string', 'number', 'boolean', 'date', 'array', 'object']
        },
        customLogic: { type: String }
      }],
      conditionLogic: {
        type: String,
        enum: ['and', 'or', 'custom'],
        default: 'and'
      },
      customConditionLogic: { type: String },
      // Execution details
      isActive: { type: Boolean, default: true },
      executionCount: { type: Number, default: 0 },
      lastExecutedAt: { type: Date },
      successCount: { type: Number, default: 0 },
      failureCount: { type: Number, default: 0 },
      averageExecutionTime: { type: Number },
      // AI-generated content
      aiGenerated: { type: Boolean, default: false },
      aiPrompt: { type: String },
      aiModel: { type: String },
      aiConfidence: { type: Number, min: 0, max: 1 }
    }],
    conditions: [{
      id: { type: String, required: true },
      type: {
        type: String,
        enum: ['data', 'time', 'user', 'custom'],
        required: true
      },
      field: { type: String },
      operator: {
        type: String,
        enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'exists', 'not_exists', 'between', 'custom'],
        required: true
      },
      value: { type: Schema.Types.Mixed },
      valueType: {
        type: String,
        enum: ['string', 'number', 'boolean', 'date', 'array', 'object']
      },
      customLogic: { type: String }
    }],
    conditionLogic: {
      type: String,
      enum: ['and', 'or', 'custom'],
      default: 'and'
    },
    customConditionLogic: { type: String },
    isActive: { type: Boolean, default: true, index: true },
    isPublished: { type: Boolean, default: false, index: true },
    publishedAt: { type: Date },
    publishedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    executionCount: { type: Number, default: 0 },
    lastExecutedAt: { type: Date },
    successCount: { type: Number, default: 0 },
    failureCount: { type: Number, default: 0 },
    averageExecutionTime: { type: Number },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    notes: { type: String },
    tags: [{ type: String, trim: true }],
    customFields: { type: Schema.Types.Mixed, default: {} },
    // AI-generated content
    aiGenerated: { type: Boolean, default: false, index: true },
    aiPrompt: { type: String },
    aiModel: { type: String },
    aiConfidence: { type: Number, min: 0, max: 1 },
    // Natural language definition
    nlDefinition: { type: String }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes
WorkflowVersionSchema.index({ workflowId: 1, version: 1, tenantId: 1 }, { unique: true });
WorkflowVersionSchema.index({ workflowId: 1, isActive: 1, tenantId: 1 });
WorkflowVersionSchema.index({ workflowId: 1, isPublished: 1, tenantId: 1 });
WorkflowVersionSchema.index({ publishedAt: 1, tenantId: 1 });
WorkflowVersionSchema.index({ publishedBy: 1, tenantId: 1 });
WorkflowVersionSchema.index({ createdBy: 1, tenantId: 1 });
WorkflowVersionSchema.index({ aiGenerated: 1, tenantId: 1 });

// Compound index for text search
WorkflowVersionSchema.index({ tenantId: 1, name: 'text', description: 'text', nlDefinition: 'text' });

// Create the model
export const WorkflowVersion: Model<IWorkflowVersion> = mongoose.models.WorkflowVersion || mongoose.model<IWorkflowVersion>('WorkflowVersion', WorkflowVersionSchema);

export default WorkflowVersion;
