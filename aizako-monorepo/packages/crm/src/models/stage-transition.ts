import mongoose, { Schema, Document } from 'mongoose';
import { IStageTransition } from '../types/stage-transition';

// Define the schema
const StageTransitionSchema = new Schema<IStageTransition & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    opportunityId: {
      type: String,
      required: true,
      index: true,
    },
    fromStage: {
      type: String,
      required: true,
    },
    toStage: {
      type: String,
      required: true,
    },
    transitionDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
    daysInPreviousStage: {
      type: Number,
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
    reason: {
      type: String,
    },
    notes: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
StageTransitionSchema.index({ tenantId: 1, opportunityId: 1, transitionDate: 1 });
StageTransitionSchema.index({ tenantId: 1, fromStage: 1, toStage: 1 });
StageTransitionSchema.index({ tenantId: 1, transitionDate: 1 });

// Create the model
export const StageTransitionModel = mongoose.model<IStageTransition & Document>(
  'StageTransition',
  StageTransitionSchema
);
