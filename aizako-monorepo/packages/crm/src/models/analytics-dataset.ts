import mongoose, { Schema, Document } from 'mongoose';
import { IAnalyticsDataset } from '../types/analytics-dataset';

// Define the schema
const AnalyticsDatasetSchema = new Schema<IAnalyticsDataset>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    dataType: {
      type: String,
      required: true,
      enum: ['timeseries', 'categorical', 'numerical', 'relational'],
    },
    entityType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity', 'proposal', 'email', 'sequence', 'workflow', 'user', 'system'],
    },
    dimensions: {
      type: [String],
      default: [],
    },
    metrics: {
      type: [String],
      required: true,
    },
    filters: {
      type: Schema.Types.Mixed,
    },
    refreshInterval: {
      type: Number,
    },
    lastRefreshed: {
      type: Date,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'processing', 'error'],
      default: 'active',
    },
    createdBy: {
      type: String,
      required: true,
    },
    updatedBy: {
      type: String,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
AnalyticsDatasetSchema.index({ tenantId: 1, name: 1 }, { unique: true });
AnalyticsDatasetSchema.index({ tenantId: 1, entityType: 1 });
AnalyticsDatasetSchema.index({ tenantId: 1, dataType: 1 });
AnalyticsDatasetSchema.index({ tenantId: 1, status: 1 });

// Create the model
export const AnalyticsDataset = mongoose.models.AnalyticsDataset || mongoose.model<IAnalyticsDataset>(
  'AnalyticsDataset',
  AnalyticsDatasetSchema
);

export default AnalyticsDataset;
