import mongoose, { Schema, Document } from 'mongoose';
import { IAttributionResults } from '../types/attribution-results';

// Define the schema
const AttributionResultsSchema = new Schema<IAttributionResults>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    opportunityId: {
      type: String,
      required: true,
      index: true,
    },
    contactId: {
      type: String,
      index: true,
    },
    companyId: {
      type: String,
      index: true,
    },
    value: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: true,
      default: 'USD',
    },
    status: {
      type: String,
      required: true,
      enum: ['won', 'lost', 'open'],
      default: 'open',
    },
    attributionModel: {
      type: String,
      required: true,
      enum: ['first-touch', 'last-touch', 'linear', 'position-based', 'time-decay', 'custom'],
      default: 'linear',
    },
    touchpoints: [
      {
        id: { type: String, required: true },
        type: { type: String, required: true },
        name: { type: String, required: true },
        timestamp: { type: Date, required: true },
        channel: { type: String, required: true },
        campaign: { type: String },
        source: { type: String },
        medium: { type: String },
        content: { type: String },
        term: { type: String },
        attribution: { type: Number, required: true },
        position: { type: Number, required: true },
        isFirstTouch: { type: Boolean, required: true, default: false },
        isLastTouch: { type: Boolean, required: true, default: false },
        isConversion: { type: Boolean, required: true, default: false },
        metadata: { type: Schema.Types.Mixed },
      },
    ],
    firstTouchChannel: {
      type: String,
    },
    lastTouchChannel: {
      type: String,
    },
    conversionChannel: {
      type: String,
    },
    channelAttribution: {
      type: Schema.Types.Mixed,
      required: true,
      default: {},
    },
    campaignAttribution: {
      type: Schema.Types.Mixed,
    },
    sourceAttribution: {
      type: Schema.Types.Mixed,
    },
    mediumAttribution: {
      type: Schema.Types.Mixed,
    },
    timeToConversion: {
      type: Number,
    },
    touchpointCount: {
      type: Number,
      required: true,
      default: 0,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
AttributionResultsSchema.index({ tenantId: 1, attributionModel: 1 });
AttributionResultsSchema.index({ tenantId: 1, status: 1 });
AttributionResultsSchema.index({ tenantId: 1, opportunityId: 1 });
AttributionResultsSchema.index({ tenantId: 1, createdAt: 1 });

// Create the model
export const AttributionResults = mongoose.models.AttributionResults || mongoose.model<IAttributionResults>(
  'AttributionResults',
  AttributionResultsSchema
);

export default AttributionResults;
