import mongoose, { Schema, Document } from 'mongoose';
import { IFeatureFlag } from '../types/feature-flag';

// Define the schema
const FeatureFlagSchema = new Schema<IFeatureFlag & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    key: {
      type: String,
      required: true,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
    targetType: {
      type: String,
      enum: ['all', 'percentage', 'specific-users', 'specific-tenants'],
      default: 'all',
    },
    targetPercentage: {
      type: Number,
      min: 0,
      max: 100,
    },
    targetUsers: {
      type: [String],
      default: [],
    },
    targetTenants: {
      type: [String],
      default: [],
    },
    rules: {
      type: [
        {
          attribute: String,
          operator: {
            type: String,
            enum: ['equals', 'not-equals', 'contains', 'not-contains', 'greater-than', 'less-than'],
          },
          value: Schema.Types.Mixed,
        },
      ],
      default: [],
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdBy: {
      type: String,
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
FeatureFlagSchema.index({ tenantId: 1, key: 1 }, { unique: true });
FeatureFlagSchema.index({ tenantId: 1, enabled: 1 });
FeatureFlagSchema.index({ tenantId: 1, targetType: 1 });

// Create the model
export const FeatureFlagModel = mongoose.model<IFeatureFlag & Document>(
  'FeatureFlag',
  FeatureFlagSchema
);
