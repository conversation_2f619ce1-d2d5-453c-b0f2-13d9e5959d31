import mongoose, { Schema, Document } from 'mongoose';
import { z } from 'zod';

/**
 * Notification interface
 */
export interface INotification extends Document {
  tenantId: string;
  userId: string | null; // null for system-wide notifications
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  entityType?: string;
  entityId?: string;
  data?: Record<string, any>;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Notification schema
 */
const NotificationSchema = new Schema<INotification>(
  {
    tenantId: { type: String, required: true, index: true },
    userId: { type: String, index: true },
    title: { type: String, required: true },
    message: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: ['info', 'success', 'warning', 'error'],
      default: 'info',
    },
    read: { type: Boolean, required: true, default: false, index: true },
    entityType: { type: String },
    entityId: { type: String },
    data: { type: Schema.Types.Mixed },
    createdAt: { type: Date, default: Date.now, index: true },
    updatedAt: { type: Date },
  },
  {
    timestamps: { createdAt: false, updatedAt: true },
  }
);

// Compound indexes
NotificationSchema.index({ tenantId: 1, userId: 1, read: 1 });
NotificationSchema.index({ tenantId: 1, userId: 1, createdAt: -1 });
NotificationSchema.index({ entityType: 1, entityId: 1 });

/**
 * Notification Zod schema for validation
 */
export const NotificationSchema_Z = z.object({
  tenantId: z.string(),
  userId: z.string().nullable(),
  title: z.string(),
  message: z.string(),
  type: z.enum(['info', 'success', 'warning', 'error']),
  read: z.boolean(),
  entityType: z.string().optional(),
  entityId: z.string().optional(),
  data: z.record(z.any()).optional(),
  createdAt: z.date(),
  updatedAt: z.date().optional(),
});

/**
 * Type guard for INotification
 * @param obj Object to check
 * @returns True if object is an INotification
 */
export function isNotification(obj: any): obj is INotification {
  return NotificationSchema_Z.safeParse(obj).success;
}

/**
 * Notification model
 */
export const Notification = mongoose.models.Notification || mongoose.model<INotification>('Notification', NotificationSchema);

export default Notification;
