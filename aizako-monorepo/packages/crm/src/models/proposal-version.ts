import mongoose, { Schema, Document, Model } from 'mongoose';
import { TenantScopedEntity } from '@aizako/core-lib';
import { IProposal } from './proposal';

/**
 * Proposal version interface
 */
export interface IProposalVersion extends Document, TenantScopedEntity {
  proposalId: mongoose.Types.ObjectId;
  versionNumber: number;
  snapshot: Partial<IProposal>;
  changes: Array<{
    field: string;
    oldValue: any;
    newValue: any;
    type: 'add' | 'update' | 'delete';
  }>;
  createdBy: mongoose.Types.ObjectId;
  comment?: string;
  createdAt: Date;
}

/**
 * Proposal version schema
 */
const ProposalVersionSchema = new Schema<IProposalVersion>(
  {
    tenantId: { type: String, required: true, index: true },
    proposalId: { type: Schema.Types.ObjectId, ref: 'Proposal', required: true, index: true },
    versionNumber: { type: Number, required: true },
    snapshot: { type: Schema.Types.Mixed, required: true },
    changes: [{
      field: { type: String, required: true },
      oldValue: { type: Schema.Types.Mixed },
      newValue: { type: Schema.Types.Mixed },
      type: { type: String, enum: ['add', 'update', 'delete'], required: true }
    }],
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    comment: { type: String },
  },
  {
    timestamps: { createdAt: true, updatedAt: false },
  }
);

// Create indexes
ProposalVersionSchema.index({ proposalId: 1, versionNumber: 1 }, { unique: true });
ProposalVersionSchema.index({ tenantId: 1, proposalId: 1, createdAt: -1 });

// Create the model
export const ProposalVersion: Model<IProposalVersion> = mongoose.models.ProposalVersion || 
  mongoose.model<IProposalVersion>('ProposalVersion', ProposalVersionSchema);

export default ProposalVersion;
