import mongoose, { Schema, Document } from 'mongoose';
import { ISubscription } from '../types/subscription';

// Define the schema
const SubscriptionSchema = new Schema<ISubscription & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    planId: {
      type: String,
      required: true,
    },
    planName: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      required: true,
      enum: ['active', 'trialing', 'past_due', 'canceled', 'unpaid', 'paused'],
      default: 'active',
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
    },
    trialEndDate: {
      type: Date,
    },
    canceledAt: {
      type: Date,
    },
    cancelReason: {
      type: String,
    },
    billingCycle: {
      type: String,
      enum: ['monthly', 'quarterly', 'annual'],
      default: 'monthly',
    },
    price: {
      amount: {
        type: Number,
        required: true,
      },
      currency: {
        type: String,
        default: 'USD',
      },
    },
    paymentMethod: {
      type: {
        type: String,
        enum: ['credit_card', 'bank_transfer', 'paypal', 'other'],
      },
      last4: String,
      expiryMonth: Number,
      expiryYear: Number,
      brand: String,
    },
    features: {
      type: [String],
      default: [],
    },
    limits: {
      type: Map,
      of: Number,
      default: {},
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
SubscriptionSchema.index({ tenantId: 1, status: 1 });
SubscriptionSchema.index({ tenantId: 1, planId: 1 });
SubscriptionSchema.index({ tenantId: 1, startDate: 1, endDate: 1 });

// Create the model
export const SubscriptionModel = mongoose.model<ISubscription & Document>(
  'Subscription',
  SubscriptionSchema
);
