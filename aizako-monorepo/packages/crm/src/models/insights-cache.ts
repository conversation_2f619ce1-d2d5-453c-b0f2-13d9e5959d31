import mongoose, { Schema, Document } from 'mongoose';
import { IInsightsCache } from '../types/insights-cache';

// Define the schema
const InsightsCacheSchema = new Schema<IInsightsCache & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    entityType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity', 'global'],
      index: true,
    },
    entityId: {
      type: String,
      required: function() {
        return this.entityType !== 'global';
      },
      index: true,
    },
    insightType: {
      type: String,
      required: true,
      enum: [
        'summary',
        'trends',
        'recommendations',
        'risks',
        'opportunities',
        'sentiment',
        'competitive',
        'other',
      ],
      index: true,
    },
    data: {
      type: Schema.Types.Mixed,
      required: true,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    expiresAt: {
      type: Date,
      required: true,
      index: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create compound index
InsightsCacheSchema.index(
  { tenantId: 1, entityType: 1, entityId: 1, insightType: 1 },
  { unique: true }
);

// Create the model
export const InsightsCacheModel = mongoose.model<IInsightsCache & Document>(
  'InsightsCache',
  InsightsCacheSchema
);
