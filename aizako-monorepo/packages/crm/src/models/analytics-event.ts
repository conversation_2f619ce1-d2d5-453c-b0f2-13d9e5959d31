import mongoose, { Schema, Document } from 'mongoose';
import { IAnalyticsEvent } from '../types/analytics-event';

// Define the schema
const AnalyticsEventSchema = new Schema<IAnalyticsEvent>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      index: true,
    },
    eventType: {
      type: String,
      required: true,
      index: true,
    },
    eventName: {
      type: String,
      required: true,
    },
    eventCategory: {
      type: String,
      required: true,
      index: true,
    },
    eventSource: {
      type: String,
      required: true,
    },
    eventTimestamp: {
      type: Date,
      required: true,
      default: Date.now,
      index: true,
    },
    entityType: {
      type: String,
      index: true,
    },
    entityId: {
      type: String,
      index: true,
    },
    properties: {
      type: Schema.Types.Mixed,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    sessionId: {
      type: String,
      index: true,
    },
    clientInfo: {
      ip: { type: String },
      userAgent: { type: String },
      device: { type: String },
      browser: { type: String },
      os: { type: String },
      referrer: { type: String },
      language: { type: String },
      screenSize: { type: String },
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Create compound indexes
AnalyticsEventSchema.index({ tenantId: 1, eventType: 1, eventTimestamp: 1 });
AnalyticsEventSchema.index({ tenantId: 1, eventCategory: 1, eventTimestamp: 1 });
AnalyticsEventSchema.index({ tenantId: 1, entityType: 1, entityId: 1, eventTimestamp: 1 });
AnalyticsEventSchema.index({ tenantId: 1, userId: 1, eventTimestamp: 1 });
AnalyticsEventSchema.index({ tenantId: 1, sessionId: 1, eventTimestamp: 1 });

// Create the model
export const AnalyticsEvent = mongoose.models.AnalyticsEvent || mongoose.model<IAnalyticsEvent>(
  'AnalyticsEvent',
  AnalyticsEventSchema
);

export default AnalyticsEvent;
