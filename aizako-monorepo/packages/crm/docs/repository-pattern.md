# Repository Pattern Guide

This document explains the repository pattern implementation in the Aizako CRM project.

## Overview

The repository pattern is a design pattern that separates the logic that retrieves data from the database from the business logic that acts on the data. This separation helps to:

1. **Decouple business logic from data access**: Business logic doesn't need to know how data is stored or retrieved.
2. **Centralize data access logic**: All data access code is in one place, making it easier to maintain.
3. **Facilitate testing**: Business logic can be tested with mock repositories.
4. **Enable switching data sources**: The underlying data source can be changed without affecting business logic.

## Architecture

In Aizako CRM, the repository pattern is implemented with the following components:

### 1. Repository Interfaces

Repository interfaces define the contract that all repository implementations must follow. They specify the methods for CRUD operations and any domain-specific queries.

```typescript
// Base repository interface
export interface IRepository<T extends TenantScopedEntity, K = string> {
  findById(id: K, tenantId: string): Promise<T | null>;
  findAll(tenantId: string, options?: QueryOptions): Promise<T[]>;
  findAllPaginated(tenantId: string, options?: QueryOptions): Promise<PaginationResult<T>>;
  create(entity: Partial<T>): Promise<T>;
  update(id: K, entity: Partial<T>, tenantId: string): Promise<T | null>;
  delete(id: K, tenantId: string): Promise<boolean>;
  count(tenantId: string, filter?: Record<string, any>): Promise<number>;
  exists(id: K, tenantId: string): Promise<boolean>;
  findOne(tenantId: string, filter: Record<string, any>): Promise<T | null>;
}

// Domain-specific repository interface
export interface IContactRepository extends IRepository<IContact> {
  findByCompanyId(companyId: string, tenantId: string, options?: QueryOptions): Promise<IContact[]>;
  findByEmail(email: string, tenantId: string): Promise<IContact | null>;
  search(query: string, tenantId: string, options?: QueryOptions): Promise<PaginationResult<IContact>>;
  addTag(contactId: string, tagId: string, tenantId: string): Promise<IContact | null>;
  removeTag(contactId: string, tagId: string, tenantId: string): Promise<IContact | null>;
}
```

### 2. Repository Implementations

Repository implementations provide the actual data access logic. In Aizako CRM, we use MongoDB repositories that implement the repository interfaces.

```typescript
// Base MongoDB repository implementation
export class MongoDBRepository<T extends TenantScopedEntity, D extends Document> implements IRepository<T> {
  protected model: Model<D>;
  protected entityName: string;
  
  constructor(model: Model<D>, entityName: string) {
    this.model = model;
    this.entityName = entityName;
  }
  
  // Implementation of IRepository methods
  async findById(id: string, tenantId: string): Promise<T | null> {
    // Implementation
  }
  
  // Other method implementations...
}

// Domain-specific repository implementation
export class ContactRepository extends MongoDBRepository<IContact, any> implements IContactRepository {
  constructor() {
    super(Contact, 'Contact');
  }
  
  // Implementation of IContactRepository methods
  async findByCompanyId(companyId: string, tenantId: string, options: QueryOptions = {}): Promise<IContact[]> {
    // Implementation
  }
  
  // Other method implementations...
}
```

### 3. Data Mappers

Data mappers are responsible for converting between domain entities and database models. In Aizako CRM, this is handled by the `toEntity` and `toDocument` methods in the `MongoDBRepository` class.

```typescript
protected toEntity(doc: D): T {
  if (!doc) return null as any;
  
  const entity = doc.toObject();
  
  // Convert _id to id
  if (entity._id) {
    entity.id = entity._id.toString();
    delete entity._id;
  }
  
  // Convert other ObjectId fields to strings
  Object.keys(entity).forEach(key => {
    if (entity[key] instanceof mongoose.Types.ObjectId) {
      entity[key] = entity[key].toString();
    }
  });
  
  return entity as unknown as T;
}

protected toDocument(entity: Partial<T>): Partial<D> {
  const doc = { ...entity } as any;
  
  // Convert id to _id if present
  if (doc.id) {
    doc._id = doc.id;
    delete doc.id;
  }
  
  return doc as Partial<D>;
}
```

## Usage

### 1. Dependency Injection

Repositories are typically injected into services or controllers. In Aizako CRM, we use singleton instances of repositories.

```typescript
import { contactRepository, companyRepository } from '../repositories';

export class ContactService {
  constructor(
    private readonly contactRepo = contactRepository,
    private readonly companyRepo = companyRepository
  ) {}
  
  async getContactWithCompany(contactId: string, tenantId: string) {
    const contact = await this.contactRepo.findById(contactId, tenantId);
    if (!contact) return null;
    
    if (contact.companyId) {
      const company = await this.companyRepo.findById(contact.companyId, tenantId);
      return { ...contact, company };
    }
    
    return contact;
  }
}
```

### 2. Using Repositories in API Routes

```typescript
import { contactRepository } from '../repositories';
import { asyncHandler } from '../api/middleware/error-handler';

router.get('/contacts/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { tenantId } = req;
  
  const contact = await contactRepository.findById(id, tenantId);
  
  if (!contact) {
    throw new NotFoundError('Contact not found');
  }
  
  res.json({ success: true, contact });
}));
```

### 3. Using Repositories with Transactions

```typescript
import { contactRepository, companyRepository } from '../repositories';
import { withTransaction } from '../utils/transactions';

async function createContactWithCompany(contactData, companyData, tenantId) {
  return await withTransaction(async (session) => {
    // Create company
    const company = await companyRepository.create({
      ...companyData,
      tenantId,
    });
    
    // Create contact with company reference
    const contact = await contactRepository.create({
      ...contactData,
      companyId: company.id,
      tenantId,
    });
    
    return { contact, company };
  });
}
```

## Best Practices

### 1. Keep Repositories Focused

Each repository should focus on a single entity or a group of closely related entities. Don't create "god repositories" that handle everything.

### 2. Use Domain-Specific Methods

Add domain-specific methods to repositories to encapsulate common queries. For example, `findByEmail` for contacts or `findByIndustry` for companies.

### 3. Return Domain Entities, Not Database Models

Repositories should always return domain entities, not database models. This ensures that business logic doesn't depend on the database implementation.

### 4. Handle Errors Consistently

Use a consistent error handling strategy in repositories. In Aizako CRM, we use custom error classes and logging.

### 5. Use Transactions for Multi-Entity Operations

When an operation affects multiple entities, use transactions to ensure data consistency.

### 6. Implement Tenant Isolation

In a multi-tenant system like Aizako CRM, always include tenant isolation in repository methods.

## Testing Repositories

Repositories can be tested using a test database or mocks. In Aizako CRM, we use MongoDB Memory Server for testing.

```typescript
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { ContactRepository } from '../repositories/contact-repository';

describe('ContactRepository', () => {
  let mongoServer: MongoMemoryServer;
  let contactRepo: ContactRepository;
  
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    await mongoose.connect(mongoServer.getUri());
    contactRepo = new ContactRepository();
  });
  
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  it('should create a contact', async () => {
    const contact = await contactRepo.create({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      tenantId: 'test-tenant',
    });
    
    expect(contact).toHaveProperty('id');
    expect(contact.firstName).toBe('John');
    expect(contact.lastName).toBe('Doe');
    expect(contact.email).toBe('<EMAIL>');
    expect(contact.tenantId).toBe('test-tenant');
  });
  
  // More tests...
});
```

## Conclusion

The repository pattern is a powerful way to separate data access from business logic. By implementing this pattern in Aizako CRM, we've created a more maintainable, testable, and flexible codebase.
