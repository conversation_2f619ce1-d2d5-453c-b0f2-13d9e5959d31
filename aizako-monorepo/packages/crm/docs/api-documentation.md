# API Documentation

This document provides an overview of the Aizako CRM API documentation system.

## Overview

Aizako CRM uses OpenAPI (Swagger) for API documentation. The documentation is automatically generated from code annotations and Zod schemas, ensuring that it's always up-to-date with the actual implementation.

## Accessing the Documentation

When the server is running, you can access the API documentation at:

```
http://localhost:3000/api-docs
```

This provides an interactive UI where you can:

- Browse all available endpoints
- See request and response schemas
- Try out API calls directly from the browser
- View authentication requirements

## Documentation Structure

The API documentation is organized by tags, which group related endpoints:

- **Auth**: Authentication endpoints
- **Contacts**: Contact management endpoints
- **Companies**: Company management endpoints
- **Opportunities**: Opportunity management endpoints
- **Activities**: Activity management endpoints
- **Proposals**: Proposal management endpoints

## Authentication

All API endpoints require authentication using JWT tokens. In the Swagger UI, you can authenticate by:

1. Clicking the "Authorize" button
2. Entering your JWT token in the format: `Bearer <token>`
3. Clicking "Authorize"

## Common Parameters

Most endpoints require the following parameters:

- **x-tenant-id** (header): The tenant ID for multi-tenant isolation
- **Authorization** (header): JWT token in the format `Bearer <token>`

## Response Formats

All API responses follow a consistent format:

### Success Response

```json
{
  "success": true,
  "data": { ... },
  "pagination": { ... } // Optional, for paginated responses
}
```

### Error Response

```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human-readable error message",
  "details": [ ... ] // Optional, for validation errors
}
```

## Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | VALIDATION_ERROR | Input validation failed |
| 401 | AUTHENTICATION_ERROR | Authentication failed |
| 403 | AUTHORIZATION_ERROR | Not authorized to perform action |
| 404 | RESOURCE_NOT_FOUND | Resource not found |
| 409 | CONFLICT_ERROR | Resource conflict (e.g., duplicate key) |
| 429 | RATE_LIMIT_ERROR | Rate limit exceeded |
| 500 | INTERNAL_SERVER_ERROR | Internal server error |

## Pagination

Endpoints that return lists of items support pagination with the following query parameters:

- **page**: Page number (default: 1)
- **pageSize**: Number of items per page (default: 20, max: 100)
- **sort**: Field to sort by (e.g., `createdAt`)
- **direction**: Sort direction (`asc` or `desc`, default: `asc`)

Paginated responses include a `pagination` object:

```json
{
  "success": true,
  "data": [ ... ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 100,
    "totalPages": 5
  }
}
```

## Filtering

Many endpoints support filtering with query parameters. For example:

```
GET /api/contacts?status=active&search=john
```

## Documenting New Endpoints

When adding new endpoints, follow these guidelines to ensure they're properly documented:

### 1. Use Zod Schemas

Define request and response schemas using Zod and register them with the OpenAPI registry:

```typescript
// Define schema
const ContactSchema = registry.register(
  'Contact',
  z.object({
    id: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email().optional(),
    // ...
  })
);

// Define request schema
const CreateContactRequest = registry.register(
  'CreateContactRequest',
  z.object({
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email().optional(),
    // ...
  })
);
```

### 2. Add JSDoc Comments

Add JSDoc comments to your route handlers:

```typescript
/**
 * @openapi
 * /contacts:
 *   get:
 *     summary: Get all contacts
 *     description: Returns a paginated list of contacts
 *     tags: [Contacts]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of contacts
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ContactListResponse'
 */
router.get('/contacts', asyncHandler(async (req, res) => {
  // Implementation
}));
```

### 3. Use Validation Middleware

Use validation middleware to validate requests against Zod schemas:

```typescript
router.post(
  '/contacts',
  validateRequest(CreateContactRequest),
  asyncHandler(async (req, res) => {
    // Implementation
  })
);
```

## OpenAPI Specification

The full OpenAPI specification is available at:

```
http://localhost:3000/api-docs.json
```

This can be used with other tools that support OpenAPI, such as Postman or Insomnia.

## Generating Client SDKs

The OpenAPI specification can be used to generate client SDKs for various languages:

```bash
# Generate TypeScript client
npx openapi-typescript-codegen --input http://localhost:3000/api-docs.json --output ./clients/typescript

# Generate Python client
npx @openapitools/openapi-generator-cli generate -i http://localhost:3000/api-docs.json -g python -o ./clients/python
```

## Best Practices

1. **Keep Documentation Updated**: Always update the documentation when changing endpoints.

2. **Use Descriptive Summaries**: Provide clear summaries and descriptions for endpoints.

3. **Document All Parameters**: Document all parameters, including headers, query parameters, and request body.

4. **Include Examples**: Provide examples for request and response bodies.

5. **Use Tags**: Organize endpoints with appropriate tags.

6. **Document Error Responses**: Document all possible error responses.

7. **Use Schema References**: Use schema references to avoid duplication.

## Troubleshooting

If you encounter issues with the API documentation:

1. **Swagger UI Not Loading**: Check that the server is running and the `/api-docs` endpoint is accessible.

2. **Schema Validation Errors**: Ensure that your Zod schemas are correctly defined and registered.

3. **Missing Endpoints**: Check that your route handlers have the correct JSDoc comments.

4. **Authentication Issues**: Ensure that you're providing a valid JWT token in the Swagger UI.

## Further Reading

- [OpenAPI Specification](https://swagger.io/specification/)
- [Zod Documentation](https://zod.dev/)
- [Swagger UI Documentation](https://swagger.io/tools/swagger-ui/)
