# Proposal Generator Developer Guide

This guide provides technical documentation for developers working on the Aizako CRM Proposal Generator module. It covers the architecture, key components, APIs, and integration points.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Key Components](#key-components)
3. [Data Models](#data-models)
4. [API Endpoints](#api-endpoints)
5. [Services](#services)
6. [Integration Points](#integration-points)
7. [Testing](#testing)
8. [Performance Considerations](#performance-considerations)
9. [Security Considerations](#security-considerations)
10. [Extending the Module](#extending-the-module)

## Architecture Overview

The Proposal Generator module follows a layered architecture:

1. **Presentation Layer**: React components for the user interface
2. **Application Layer**: Services and hooks for business logic
3. **Data Access Layer**: API clients and data models
4. **Infrastructure Layer**: External services and utilities

### Technology Stack

- **Frontend**: React, Material UI, TypeScript
- **Backend**: Node.js, Express, TypeScript
- **Database**: MongoDB
- **AI Integration**: Claude API
- **Document Generation**: PDF-lib, docx

## Key Components

### Frontend Components

- **ProposalEditor**: Main component for creating and editing proposals
- **ProposalPreview**: Component for previewing proposals
- **ProposalPricingEditor**: Component for editing pricing information
- **ProposalAIGenerator**: Component for generating proposals with AI
- **ProposalSendDialog**: Component for sending proposals
- **ProposalAnalyticsDashboard**: Component for viewing proposal analytics
- **PublicProposalView**: Component for public proposal viewing

### Backend Components

- **ProposalController**: Handles API requests for proposals
- **ProposalService**: Implements business logic for proposals
- **DocumentGenerationService**: Generates documents in various formats
- **AIService**: Integrates with Claude API for AI generation

## Data Models

### Proposal

The main data model for proposals:

```typescript
interface IProposal {
  _id: ObjectId | string;
  tenantId: string;
  title: string;
  description?: string;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired';
  sections: IProposalSection[];
  pricing?: IProposalPricing;
  terms?: string;
  opportunityId?: ObjectId | string;
  companyId?: ObjectId | string;
  contactIds?: (ObjectId | string)[];
  owner?: ObjectId | string;
  createdBy?: ObjectId | string;
  publicToken?: string;
  publicUrl?: string;
  publicAccessEnabled?: boolean;
  downloadEnabled?: boolean;
  downloadFormats?: string[];
  viewCount?: number;
  analyticsEvents?: IProposalAnalyticsEvent[];
  aiGenerated?: boolean;
  aiPrompt?: string;
  aiModel?: string;
  sentAt?: Date;
  sentBy?: ObjectId | string;
  viewedAt?: Date;
  lastViewedAt?: Date;
  acceptedAt?: Date;
  acceptedBy?: ObjectId | string;
  rejectedAt?: Date;
  rejectedBy?: ObjectId | string;
  rejectionReason?: string;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Proposal Section

```typescript
interface IProposalSection {
  id: string;
  title: string;
  content: string;
  order: number;
  type: string;
  isVisible: boolean;
  aiGenerated?: boolean;
}
```

### Proposal Pricing

```typescript
interface IProposalPricing {
  currency: string;
  items: IProposalPricingItem[];
  subtotal: number;
  discount?: number;
  tax?: number;
  total: number;
}

interface IProposalPricingItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  total: number;
}
```

### Proposal Analytics Event

```typescript
interface IProposalAnalyticsEvent {
  eventType: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  data?: Record<string, any>;
}
```

## API Endpoints

### Proposal Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/crm/proposals` | Get all proposals |
| GET | `/api/crm/proposals/:id` | Get a proposal by ID |
| POST | `/api/crm/proposals` | Create a new proposal |
| PUT | `/api/crm/proposals/:id` | Update a proposal |
| DELETE | `/api/crm/proposals/:id` | Delete a proposal |
| GET | `/api/crm/proposals/public/:token` | Get a proposal by public token |
| POST | `/api/crm/proposals/:id/send` | Send a proposal |
| GET | `/api/crm/proposals/:id/download` | Download a proposal |
| POST | `/api/crm/proposals/:id/accept` | Accept a proposal |
| POST | `/api/crm/proposals/:id/reject` | Reject a proposal |
| POST | `/api/crm/proposals/public/:token/view` | Record a proposal view |

### AI Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/crm/proposals/ai/generate` | Generate a proposal with AI |
| POST | `/api/crm/proposals/ai/generate-section` | Generate a proposal section with AI |

## Services

### ProposalService

The `ProposalService` provides methods for working with proposals:

```typescript
class ProposalService {
  static async getProposals(params: ProposalQueryParams, tenantId: string): Promise<ProposalListResponse>;
  static async getProposalById(id: string, tenantId: string): Promise<IProposal | null>;
  static async getProposalByToken(token: string): Promise<IProposal | null>;
  static async createProposal(proposal: CreateProposalRequest, tenantId: string): Promise<IProposal>;
  static async updateProposal(id: string, updates: UpdateProposalRequest, tenantId: string): Promise<IProposal | null>;
  static async deleteProposal(id: string, tenantId: string): Promise<boolean>;
  static async sendProposal(id: string, userId: string, tenantId: string, options: ProposalSendOptions): Promise<IProposal | null>;
  static async acceptProposal(id: string): Promise<IProposal | null>;
  static async rejectProposal(id: string, reason?: string): Promise<IProposal | null>;
  static async recordView(token: string, data?: Record<string, any>): Promise<IProposal | null>;
  static async generateProposalDocument(proposal: IProposal, options: ProposalDownloadOptions): Promise<Buffer | string>;
  static async generateProposalWithAI(options: ProposalAIGenerationOptions, tenantId: string): Promise<IProposal>;
  static async generateProposalSectionWithAI(sectionType: string, prompt: string, model: string, context?: object): Promise<object>;
}
```

### DocumentGenerationService

The `DocumentGenerationService` provides methods for generating documents:

```typescript
class DocumentGenerationService {
  static async generateDocument(proposal: IProposal, options: ProposalDownloadOptions): Promise<Buffer | string>;
  static async generatePDF(proposal: IProposal, options: ProposalDownloadOptions): Promise<Buffer>;
  static async generateDOCX(proposal: IProposal, options: ProposalDownloadOptions): Promise<Buffer>;
  static async generateMarkdown(proposal: IProposal, options: ProposalDownloadOptions): Promise<string>;
}
```

### AIService

The `AIService` provides methods for interacting with AI models:

```typescript
class AIService {
  static async generateText(prompt: string, model?: string, options?: object): Promise<string>;
  static async generateProposal(options: ProposalAIGenerationOptions): Promise<Partial<IProposal>>;
  static async generateProposalSection(sectionType: string, prompt: string, model?: string, context?: object): Promise<object>;
}
```

## Integration Points

### Claude API Integration

The Proposal Generator integrates with the Claude API for AI-powered content generation. The integration is implemented in the `AIService` class.

### Email Integration

The Proposal Generator integrates with email services for sending proposals. This is implemented in the `ProposalService.sendProposal` method.

### Opportunity Integration

Proposals can be linked to opportunities in the CRM system. This integration is implemented through the `opportunityId` field in the `IProposal` interface.

### Company Integration

Proposals can be linked to companies in the CRM system. This integration is implemented through the `companyId` field in the `IProposal` interface.

### Contact Integration

Proposals can be linked to contacts in the CRM system. This integration is implemented through the `contactIds` field in the `IProposal` interface.

## Testing

### Unit Tests

Unit tests are implemented using Jest and focus on testing individual components and services in isolation.

### Integration Tests

Integration tests focus on testing the interaction between components and services. They use a MongoDB memory server for database testing.

### End-to-End Tests

End-to-end tests focus on testing the complete flow from the user interface to the database and back. They use Cypress for browser testing.

## Performance Considerations

### Document Generation

Document generation can be resource-intensive, especially for large proposals. Consider the following optimizations:

- Generate documents asynchronously and notify the user when they're ready
- Cache generated documents to avoid regenerating them unnecessarily
- Limit the size and complexity of proposals to prevent performance issues

### AI Generation

AI generation can be slow and expensive. Consider the following optimizations:

- Implement rate limiting to prevent abuse
- Cache AI-generated content to avoid regenerating it unnecessarily
- Provide feedback to the user during generation to indicate progress

## Security Considerations

### Multi-Tenant Isolation

Ensure that proposals are properly isolated between tenants. All database queries should include the `tenantId` field to prevent data leakage.

### Public Access Control

Proposals can be shared publicly using a token. Ensure that the token is sufficiently random and that only authorized proposals can be accessed publicly.

### Input Validation

Validate all user input to prevent injection attacks. Use Zod schemas for validation.

### HTML Sanitization

Sanitize HTML content in proposals to prevent XSS attacks. Use the `sanitizeHtml` utility.

## Extending the Module

### Adding New Section Types

To add a new section type:

1. Update the `IProposalSection` interface to include the new type
2. Add support for the new type in the `ProposalSectionEditor` component
3. Add support for the new type in the `ProposalPreview` component
4. Add support for the new type in the `DocumentGenerationService`

### Adding New Document Formats

To add a new document format:

1. Update the `ProposalDownloadOptions` interface to include the new format
2. Add a new method to the `DocumentGenerationService` for generating the format
3. Update the `generateDocument` method to support the new format
4. Add the new format to the download options in the UI

### Adding New AI Models

To add a new AI model:

1. Update the `AIService` to support the new model
2. Add the new model to the model selection dropdown in the UI
3. Update the documentation to include the new model
