# MongoDB Migrations Guide

This document explains how to use the MongoDB migration system in the Aizako CRM project.

## Overview

Aizako CRM uses [<PERSON>go<PERSON>](https://www.mongock.io/) for MongoDB schema migrations. Mongock provides a structured way to evolve your MongoDB database schema over time, with support for versioning, rollbacks, and tracking of applied migrations.

## Migration Files

Migration files are TypeScript classes located in the `src/migrations` directory. Each migration file follows a specific naming convention:

```
YYYYMMDDHHMMSS_description-of-migration.ts
```

For example:
```
20240601000000_initial-indexes.ts
```

## Creating a New Migration

To create a new migration, use the following command:

```bash
npm run migrate:create -- "description-of-migration"
```

For example:
```bash
npm run migrate:create -- "add-indexes-to-contacts"
```

This will create a new migration file with a timestamp and the provided description.

## Migration Structure

Each migration file exports a class with the following structure:

```typescript
export class MigrationNameMigration {
  // Order of migration execution (higher values run later)
  public static readonly order = 1;

  // Unique migration ID
  public static readonly id = 'timestamp-migration-name';

  // Author of the migration
  public static readonly author = 'your-name';

  // Execute the migration
  public async execute(db: Db): Promise<void> {
    // Migration logic goes here
  }

  // Rollback the migration
  public async rollback(db: Db): Promise<void> {
    // Rollback logic goes here
  }
}
```

## Running Migrations

To run all pending migrations, use the following command:

```bash
npm run migrate
```

This will:
1. Connect to MongoDB
2. Check which migrations have already been applied
3. Run any pending migrations in order
4. Update the migration log in the database

## Migration Best Practices

1. **Make migrations idempotent**: Migrations should be able to run multiple times without causing errors or duplicate data.

2. **Include rollback logic**: Always implement the `rollback` method to undo the changes made by the migration.

3. **Use transactions when possible**: If your MongoDB deployment supports transactions (replica sets), use them to ensure atomicity.

4. **Test migrations thoroughly**: Before running migrations in production, test them in a development or staging environment.

5. **Keep migrations small and focused**: Each migration should do one thing and do it well.

6. **Document migrations**: Include comments in your migration files explaining what they do and why.

## Common Migration Operations

### Creating Collections

```typescript
await db.createCollection('new_collection');
```

### Creating Indexes

```typescript
await db.collection('collection_name').createIndex({ field: 1 });

// Compound index
await db.collection('collection_name').createIndex({ field1: 1, field2: -1 });

// Unique index
await db.collection('collection_name').createIndex({ field: 1 }, { unique: true });
```

### Updating Documents

```typescript
// Update all documents
await db.collection('collection_name').updateMany(
  {}, // Filter
  { $set: { new_field: 'default_value' } } // Update
);

// Update specific documents
await db.collection('collection_name').updateMany(
  { status: 'active' }, // Filter
  { $set: { updated: true } } // Update
);
```

### Renaming Fields

```typescript
await db.collection('collection_name').updateMany(
  {}, // Filter
  { $rename: { 'old_field_name': 'new_field_name' } } // Rename
);
```

### Dropping Collections

```typescript
await db.dropCollection('collection_name');
```

## Troubleshooting

### Migration Lock Issues

If a migration fails and leaves a lock in the database, you may need to manually remove it:

```javascript
db.mongockLock.deleteMany({});
```

### Resetting Migration History

In development environments, you may want to reset the migration history:

```javascript
db.mongockChangeLog.deleteMany({});
```

**Warning**: Never do this in production unless you know exactly what you're doing.

## Further Reading

- [Mongock Documentation](https://www.mongock.io/documentation)
- [MongoDB Schema Design Best Practices](https://www.mongodb.com/blog/post/building-with-patterns-the-schema-versioning-pattern)
