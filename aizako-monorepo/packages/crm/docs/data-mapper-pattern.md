# Data Mapper Pattern Guide

This document explains the data mapper pattern implementation in the Aizako CRM project.

## Overview

The data mapper pattern is a design pattern that separates domain entities from their persistence mechanism. It provides a clear separation between the business logic and the database, allowing each to evolve independently.

In Aizako CRM, data mappers are responsible for:

1. **Converting database models to domain entities**: Transforming database-specific objects into business objects that the application can work with.
2. **Converting domain entities to database models**: Transforming business objects into a format that can be stored in the database.

## Architecture

The data mapper pattern works in conjunction with the repository pattern:

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Domain Entity  │◄────►│   Data Mapper   │◄────►│ Database Model  │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
        ▲                                                 ▲
        │                                                 │
        │                                                 │
        ▼                                                 ▼
┌─────────────────┐                              ┌─────────────────┐
│                 │                              │                 │
│    Service      │                              │   Repository    │
│                 │                              │                 │
└─────────────────┘                              └─────────────────┘
```

- **Domain Entities**: Business objects that represent the core concepts of the application.
- **Data Mappers**: Convert between domain entities and database models.
- **Database Models**: Mongoose schemas and documents that represent how data is stored.
- **Repositories**: Use data mappers to convert between domain entities and database models when performing CRUD operations.
- **Services**: Work with domain entities and use repositories for data access.

## Implementation

### Domain Entities

Domain entities are plain TypeScript interfaces that represent business objects. They are independent of the database and focus on the business rules and behavior.

```typescript
// Example: Contact domain entity
export interface ContactEntity {
  id: string;
  tenantId: string;
  firstName: string;
  lastName: string;
  email?: string;
  // Other business properties...
  createdAt: Date;
  updatedAt: Date;
}
```

### Data Mappers

Data mappers are responsible for converting between domain entities and database models. They handle the transformation of data structures, including:

- Converting `_id` to `id` and vice versa
- Converting ObjectId references to strings and vice versa
- Handling nested objects and arrays
- Applying any business rules during conversion

```typescript
// Example: Contact mapper
export function toEntity(model: IContact): ContactEntity {
  if (!model) return null as any;
  
  const entity: ContactEntity = {
    id: model._id.toString(),
    tenantId: model.tenantId,
    firstName: model.firstName,
    lastName: model.lastName,
    // Map other properties...
  };
  
  // Convert ObjectId fields to strings
  if (model.companyId) {
    entity.companyId = model.companyId.toString();
  }
  
  return entity;
}

export function toModel(entity: Partial<ContactEntity>): Partial<IContact> {
  if (!entity) return null as any;
  
  const model: Partial<IContact> = {
    tenantId: entity.tenantId,
    firstName: entity.firstName,
    lastName: entity.lastName,
    // Map other properties...
  };
  
  // Convert string IDs to ObjectIds
  if (entity.id) {
    model._id = new mongoose.Types.ObjectId(entity.id);
  }
  
  return model;
}
```

### Integration with Repositories

Repositories use data mappers to convert between domain entities and database models when performing CRUD operations:

```typescript
// Example: Using data mapper in a repository
export class ContactRepository extends MongoDBRepository<ContactEntity, any> {
  constructor() {
    super(Contact, 'Contact');
    this.setMapper(contactMapper);
  }
  
  // Repository methods work with domain entities
  async findByEmail(email: string, tenantId: string): Promise<ContactEntity | null> {
    try {
      const doc = await Contact.findOne({ email, tenantId });
      return doc ? this.toEntity(doc) : null;
    } catch (error) {
      // Error handling...
    }
  }
}
```

## Benefits

The data mapper pattern provides several benefits:

1. **Separation of Concerns**: Domain entities are separated from database models, allowing each to evolve independently.
2. **Flexibility**: Changes to the database schema don't require changes to the business logic, and vice versa.
3. **Testability**: Domain entities can be tested without a database, and repositories can be mocked for service tests.
4. **Maintainability**: Code is more organized and easier to understand, with clear responsibilities for each component.
5. **Type Safety**: Domain entities have well-defined types that represent the business domain.

## Best Practices

### 1. Keep Domain Entities Simple

Domain entities should focus on business concepts and be free from database-specific details:

```typescript
// Good: Simple domain entity
export interface ContactEntity {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
}

// Bad: Domain entity with database details
export interface ContactEntity {
  _id: ObjectId; // Database-specific
  firstName: string;
  lastName: string;
  email?: string;
  __v: number; // Database-specific
}
```

### 2. Handle Null Values

Always handle null or undefined values in mappers:

```typescript
export function toEntity(model: IContact): ContactEntity {
  if (!model) return null as any;
  
  // Mapping logic...
}
```

### 3. Convert IDs Consistently

Convert between string IDs and ObjectIds consistently:

```typescript
// In toEntity
if (model._id) {
  entity.id = model._id.toString();
}

// In toModel
if (entity.id) {
  model._id = new mongoose.Types.ObjectId(entity.id);
}
```

### 4. Handle Nested Objects

Handle nested objects and arrays carefully:

```typescript
// In toEntity
if (model.address) {
  entity.address = { ...model.address };
}

// In toModel
if (entity.address) {
  model.address = { ...entity.address };
}
```

### 5. Keep Mappers Focused

Each mapper should focus on a single entity type:

```typescript
// Good: Focused mapper
export const contactMapper = {
  toEntity,
  toModel,
};

// Bad: Mapper handling multiple entity types
export const mapper = {
  contactToEntity,
  contactToModel,
  companyToEntity,
  companyToModel,
};
```

## Example Usage

### Using Mappers in Repositories

```typescript
export class ContactRepository extends MongoDBRepository<ContactEntity, any> {
  constructor() {
    super(Contact, 'Contact');
    this.setMapper(contactMapper);
  }
}
```

### Using Repositories in Services

```typescript
export class ContactService {
  private readonly contactRepo: IContactRepository;
  
  constructor(contactRepo: IContactRepository = contactRepository) {
    this.contactRepo = contactRepo;
  }
  
  async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
    return await this.contactRepo.findById(id, tenantId);
  }
}
```

## Conclusion

The data mapper pattern is a powerful way to separate domain entities from database models. By implementing this pattern in Aizako CRM, we've created a more maintainable, flexible, and testable codebase.

Combined with the repository pattern, data mappers provide a clean separation of concerns that allows the business logic and data access layers to evolve independently.
