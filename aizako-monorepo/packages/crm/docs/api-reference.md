# API Reference

This document provides a comprehensive reference for the Aizako CRM API endpoints.

## Authentication

All API endpoints require authentication. The API uses JWT tokens for authentication.

### Headers

Include the following headers with all API requests:

```
Authorization: Bearer <jwt_token>
x-tenant-id: <tenant_id>
```

## Base URL

The base URL for all API endpoints is:

```
/api
```

## Endpoints

### Contacts

#### Get All Contacts

```
GET /api/contacts
```

Query Parameters:
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 20, max: 100)
- `sort` (optional): Field to sort by (e.g., `lastName`)
- `direction` (optional): Sort direction (`asc` or `desc`, default: `asc`)
- `search` (optional): Search term to filter contacts
- `status` (optional): Filter by status (`active`, `inactive`, etc.)

Response:
```json
{
  "success": true,
  "contacts": [
    {
      "id": "string",
      "firstName": "string",
      "lastName": "string",
      "email": "string",
      "phone": "string",
      "status": "string",
      "company": {
        "id": "string",
        "name": "string"
      },
      "createdAt": "string",
      "updatedAt": "string"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 100,
    "totalPages": 5
  }
}
```

#### Get Contact by ID

```
GET /api/contacts/:id
```

Response:
```json
{
  "success": true,
  "contact": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "status": "string",
    "company": {
      "id": "string",
      "name": "string"
    },
    "createdAt": "string",
    "updatedAt": "string"
  }
}
```

#### Create Contact

```
POST /api/contacts
```

Request Body:
```json
{
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "status": "string",
  "companyId": "string",
  "jobTitle": "string",
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "postalCode": "string",
    "country": "string"
  },
  "customFields": {}
}
```

Response:
```json
{
  "success": true,
  "contact": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "status": "string",
    "company": {
      "id": "string",
      "name": "string"
    },
    "createdAt": "string",
    "updatedAt": "string"
  }
}
```

#### Update Contact

```
PUT /api/contacts/:id
```

Request Body:
```json
{
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "status": "string",
  "companyId": "string",
  "jobTitle": "string",
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "postalCode": "string",
    "country": "string"
  },
  "customFields": {}
}
```

Response:
```json
{
  "success": true,
  "contact": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "status": "string",
    "company": {
      "id": "string",
      "name": "string"
    },
    "createdAt": "string",
    "updatedAt": "string"
  }
}
```

#### Delete Contact

```
DELETE /api/contacts/:id
```

Response:
```json
{
  "success": true,
  "message": "Contact deleted successfully"
}
```

### Companies

#### Get All Companies

```
GET /api/companies
```

Query Parameters:
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 20, max: 100)
- `sort` (optional): Field to sort by (e.g., `name`)
- `direction` (optional): Sort direction (`asc` or `desc`, default: `asc`)
- `search` (optional): Search term to filter companies
- `status` (optional): Filter by status (`active`, `inactive`, etc.)
- `industry` (optional): Filter by industry

Response:
```json
{
  "success": true,
  "companies": [
    {
      "id": "string",
      "name": "string",
      "industry": "string",
      "website": "string",
      "status": "string",
      "createdAt": "string",
      "updatedAt": "string"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 100,
    "totalPages": 5
  }
}
```

### Opportunities

#### Get All Opportunities

```
GET /api/opportunities
```

Query Parameters:
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 20, max: 100)
- `sort` (optional): Field to sort by (e.g., `value`)
- `direction` (optional): Sort direction (`asc` or `desc`, default: `asc`)
- `search` (optional): Search term to filter opportunities
- `stage` (optional): Filter by stage
- `contactId` (optional): Filter by contact ID
- `companyId` (optional): Filter by company ID

Response:
```json
{
  "success": true,
  "opportunities": [
    {
      "id": "string",
      "name": "string",
      "value": 0,
      "currency": "string",
      "stage": "string",
      "probability": 0,
      "closeDate": "string",
      "contact": {
        "id": "string",
        "firstName": "string",
        "lastName": "string"
      },
      "company": {
        "id": "string",
        "name": "string"
      },
      "createdAt": "string",
      "updatedAt": "string"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 100,
    "totalPages": 5
  }
}
```

### Proposals

#### Get All Proposals

```
GET /api/proposals
```

Query Parameters:
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 20, max: 100)
- `sort` (optional): Field to sort by (e.g., `createdAt`)
- `direction` (optional): Sort direction (`asc` or `desc`, default: `desc`)
- `status` (optional): Filter by status
- `opportunityId` (optional): Filter by opportunity ID

Response:
```json
{
  "success": true,
  "proposals": [
    {
      "id": "string",
      "title": "string",
      "status": "string",
      "opportunity": {
        "id": "string",
        "name": "string"
      },
      "createdAt": "string",
      "updatedAt": "string"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 100,
    "totalPages": 5
  }
}
```

## Error Responses

All API endpoints return a consistent error format:

```json
{
  "success": false,
  "error": "Error type",
  "message": "Human-readable error message",
  "details": [] // Optional array of detailed error information
}
```

Common error types:
- `ValidationError`: Request validation failed
- `NotFoundError`: Resource not found
- `AuthenticationError`: Authentication failed
- `AuthorizationError`: User not authorized to perform action
- `ServerError`: Internal server error
