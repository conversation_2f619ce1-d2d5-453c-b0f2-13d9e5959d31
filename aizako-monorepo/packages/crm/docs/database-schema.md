# Database Schema Documentation

This document describes the MongoDB database schema used in the Aizako CRM system.

## Overview

Aizako CRM uses MongoDB as its primary database. The schema is designed to support multi-tenancy, with each tenant's data isolated from others.

## Collections

### Users

The `users` collection stores user information.

```typescript
interface User {
  _id: ObjectId;
  username: string;
  email: string;
  passwordHash: string;
  fullName: string;
  role: string; // 'admin', 'user', etc.
  status: string; // 'active', 'inactive', etc.
  lastLogin?: Date;
  apiKeys?: {
    key: string;
    name: string;
    createdAt: Date;
    lastUsedAt?: Date;
  }[];
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ email: 1 }` (unique)
- `{ username: 1 }` (unique)
- `{ 'apiKeys.key': 1 }`
- `{ createdAt: -1 }`

### Tenants

The `tenants` collection stores tenant information.

```typescript
interface Tenant {
  _id: ObjectId;
  name: string;
  slug: string; // URL-friendly name
  ownerId: ObjectId; // Reference to users collection
  status: string; // 'active', 'inactive', etc.
  settings: {
    timezone?: string;
    locale?: string;
    branding?: {
      logo?: string;
      primaryColor?: string;
      accentColor?: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ slug: 1 }` (unique)
- `{ ownerId: 1 }`
- `{ status: 1 }`
- `{ createdAt: -1 }`

### UserTenants

The `usertenants` collection stores the relationship between users and tenants.

```typescript
interface UserTenant {
  _id: ObjectId;
  userId: ObjectId; // Reference to users collection
  tenantId: ObjectId; // Reference to tenants collection
  role: string; // 'owner', 'admin', 'member', 'guest'
  status: string; // 'active', 'invited', 'suspended'
  invitedBy?: ObjectId; // Reference to users collection
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ userId: 1, tenantId: 1 }` (unique)
- `{ tenantId: 1, role: 1 }`
- `{ status: 1 }`
- `{ createdAt: -1 }`

### Contacts

The `contacts` collection stores contact information.

```typescript
interface Contact {
  _id: ObjectId;
  tenantId: ObjectId; // Reference to tenants collection
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  companyId?: ObjectId; // Reference to companies collection
  status: string; // 'active', 'inactive', etc.
  source?: string; // How the contact was acquired
  owner?: ObjectId; // Reference to users collection
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  tags?: string[];
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ tenantId: 1 }`
- `{ email: 1, tenantId: 1 }`
- `{ lastName: 1, firstName: 1, tenantId: 1 }`
- `{ companyId: 1, tenantId: 1 }`
- `{ status: 1, tenantId: 1 }`
- `{ owner: 1, tenantId: 1 }`
- `{ tags: 1, tenantId: 1 }`
- `{ createdAt: -1 }`

### Companies

The `companies` collection stores company information.

```typescript
interface Company {
  _id: ObjectId;
  tenantId: ObjectId; // Reference to tenants collection
  name: string;
  website?: string;
  industry?: string;
  size?: string; // Number of employees
  status: string; // 'lead', 'customer', etc.
  owner?: ObjectId; // Reference to users collection
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  tags?: string[];
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ tenantId: 1 }`
- `{ name: 1, tenantId: 1 }`
- `{ industry: 1, tenantId: 1 }`
- `{ status: 1, tenantId: 1 }`
- `{ owner: 1, tenantId: 1 }`
- `{ tags: 1, tenantId: 1 }`
- `{ createdAt: -1 }`

### Opportunities

The `opportunities` collection stores sales opportunities.

```typescript
interface Opportunity {
  _id: ObjectId;
  tenantId: ObjectId; // Reference to tenants collection
  name: string;
  contactId?: ObjectId; // Reference to contacts collection
  companyId?: ObjectId; // Reference to companies collection
  value: number;
  currency: string;
  stage: string; // 'discovery', 'proposal', 'negotiation', etc.
  probability: number; // 0-100
  closeDate?: Date;
  owner?: ObjectId; // Reference to users collection
  tags?: string[];
  notes?: string;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ tenantId: 1 }`
- `{ contactId: 1, tenantId: 1 }`
- `{ companyId: 1, tenantId: 1 }`
- `{ stage: 1, tenantId: 1 }`
- `{ closeDate: 1, tenantId: 1 }`
- `{ value: -1, tenantId: 1 }`
- `{ owner: 1, tenantId: 1 }`
- `{ tags: 1, tenantId: 1 }`
- `{ createdAt: -1 }`

### Activities

The `activities` collection stores activities related to contacts and opportunities.

```typescript
interface Activity {
  _id: ObjectId;
  tenantId: ObjectId; // Reference to tenants collection
  type: string; // 'call', 'meeting', 'email', 'task', etc.
  subject: string;
  description?: string;
  contactId?: ObjectId; // Reference to contacts collection
  companyId?: ObjectId; // Reference to companies collection
  opportunityId?: ObjectId; // Reference to opportunities collection
  dueDate?: Date;
  completed: boolean;
  completedAt?: Date;
  owner?: ObjectId; // Reference to users collection
  priority?: string; // 'low', 'medium', 'high'
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ tenantId: 1 }`
- `{ contactId: 1, tenantId: 1 }`
- `{ companyId: 1, tenantId: 1 }`
- `{ opportunityId: 1, tenantId: 1 }`
- `{ type: 1, tenantId: 1 }`
- `{ dueDate: 1, tenantId: 1 }`
- `{ completed: 1, tenantId: 1 }`
- `{ owner: 1, tenantId: 1 }`
- `{ createdAt: -1 }`

### Interactions

The `interactions` collection stores interactions with contacts.

```typescript
interface Interaction {
  _id: ObjectId;
  tenantId: ObjectId; // Reference to tenants collection
  contactId: ObjectId; // Reference to contacts collection
  opportunityId?: ObjectId; // Reference to opportunities collection
  type: string; // 'email', 'call', 'meeting', 'social', etc.
  direction: string; // 'inbound', 'outbound'
  subject?: string;
  content?: string;
  date: Date;
  userId: ObjectId; // Reference to users collection
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ tenantId: 1 }`
- `{ contactId: 1, tenantId: 1 }`
- `{ opportunityId: 1, tenantId: 1 }`
- `{ type: 1, tenantId: 1 }`
- `{ date: -1, tenantId: 1 }`
- `{ userId: 1, tenantId: 1 }`
- `{ createdAt: -1 }`

### Proposals

The `proposals` collection stores sales proposals.

```typescript
interface Proposal {
  _id: ObjectId;
  tenantId: ObjectId; // Reference to tenants collection
  opportunityId: ObjectId; // Reference to opportunities collection
  title: string;
  content: string;
  status: string; // 'draft', 'sent', 'accepted', 'rejected'
  sentAt?: Date;
  viewedAt?: Date;
  acceptedAt?: Date;
  rejectedAt?: Date;
  expiresAt?: Date;
  createdBy: ObjectId; // Reference to users collection
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ tenantId: 1 }`
- `{ opportunityId: 1, tenantId: 1 }`
- `{ status: 1, tenantId: 1 }`
- `{ createdBy: 1, tenantId: 1 }`
- `{ createdAt: -1 }`

### EmailTracking

The `emailtrackings` collection stores email tracking information.

```typescript
interface EmailTracking {
  _id: ObjectId;
  tenantId: ObjectId; // Reference to tenants collection
  messageId: string;
  contactId?: ObjectId; // Reference to contacts collection
  opportunityId?: ObjectId; // Reference to opportunities collection
  subject: string;
  sentAt: Date;
  sentBy: ObjectId; // Reference to users collection
  recipients: string[];
  events: {
    type: string; // 'sent', 'delivered', 'opened', 'clicked', 'bounced'
    timestamp: Date;
    ip?: string;
    userAgent?: string;
    url?: string; // For click events
    metadata?: Record<string, any>;
  }[];
  createdAt: Date;
  updatedAt: Date;
}
```

Indexes:
- `{ tenantId: 1 }`
- `{ messageId: 1 }`
- `{ contactId: 1, tenantId: 1 }`
- `{ opportunityId: 1, tenantId: 1 }`
- `{ sentAt: -1, tenantId: 1 }`
- `{ sentBy: 1, tenantId: 1 }`
- `{ 'events.type': 1, tenantId: 1 }`
- `{ createdAt: -1 }`

## Multi-Tenancy

All collections include a `tenantId` field to ensure data isolation between tenants. All queries should include the tenant ID to prevent data leakage between tenants.

Example query pattern:
```javascript
db.collection.find({ tenantId: ObjectId("tenant_id_here"), ...otherFilters });
```

## Indexes

Indexes are crucial for query performance. All collections have indexes on the `tenantId` field and other frequently queried fields.

## Data Validation

MongoDB schema validation is used to ensure data integrity. Each collection has a validation schema that enforces required fields and data types.
