# Aizako CRM Architecture

This document describes the architecture of the Aizako CRM system.

## Overview

Aizako CRM follows a clean architecture approach, with clear separation of concerns between different layers of the application. The system is designed to be modular, maintainable, and scalable.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Presentation Layer                        │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   React UI  │  │  Next.js    │  │   API       │  │  CLI    │ │
│  │  Components │  │   Pages     │  │  Routes     │  │         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Application Layer                           │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │  Services   │  │  Use Cases  │  │ Controllers │  │ Handlers│ │
│  │             │  │             │  │             │  │         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Domain Layer                              │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Entities  │  │  Value      │  │  Domain     │  │ Events  │ │
│  │             │  │  Objects    │  │  Services   │  │         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                          │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ Repositories│  │  External   │  │  Database   │  │ Logging │ │
│  │             │  │   APIs      │  │   Access    │  │         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Layers

### Presentation Layer

The presentation layer is responsible for handling user interactions and displaying data. It includes:

- **React UI Components**: Reusable UI components
- **Next.js Pages**: Page components and routing
- **API Routes**: HTTP endpoints for the API
- **CLI**: Command-line interface for administrative tasks

### Application Layer

The application layer orchestrates the flow of data between the presentation and domain layers. It includes:

- **Services**: Business logic that coordinates multiple domain entities
- **Use Cases**: Specific business operations
- **Controllers**: Handle HTTP requests and responses
- **Handlers**: Process events and commands

### Domain Layer

The domain layer contains the business logic and rules. It includes:

- **Entities**: Core business objects (Contact, Company, Opportunity, etc.)
- **Value Objects**: Immutable objects that represent concepts in the domain
- **Domain Services**: Logic that doesn't naturally fit in entities
- **Events**: Domain events that represent state changes

### Infrastructure Layer

The infrastructure layer provides technical capabilities to support the other layers. It includes:

- **Repositories**: Data access abstractions following the repository pattern
- **Data Mappers**: Convert between domain entities and database models
- **External APIs**: Integration with external services
- **Database Access**: MongoDB connection and queries
- **Logging**: Application logging
- **Error Handling**: Centralized error handling system

## Key Components

### MongoDB Data Access

The system uses MongoDB as its primary database. Data access is abstracted through repositories, which implement the repository pattern, and data mappers, which implement the data-mapper pattern.

#### Repository Pattern

Repositories provide a consistent interface for data access and abstract away the database implementation details:

```typescript
// Repository interface
interface IContactRepository {
  findById(id: string, tenantId: string): Promise<ContactEntity | null>;
  findAll(tenantId: string, options?: QueryOptions): Promise<ContactEntity[]>;
  findAllPaginated(tenantId: string, options?: QueryOptions): Promise<PaginationResult<ContactEntity>>;
  create(entity: Partial<ContactEntity>): Promise<ContactEntity>;
  update(id: string, entity: Partial<ContactEntity>, tenantId: string): Promise<ContactEntity | null>;
  delete(id: string, tenantId: string): Promise<boolean>;
  findByEmail(email: string, tenantId: string): Promise<ContactEntity | null>;
  // Other domain-specific methods...
}

// Repository implementation
class ContactRepository extends MongoDBRepository<ContactEntity, any> implements IContactRepository {
  constructor() {
    super(Contact, 'Contact');
    this.setMapper(contactMapper);
  }

  async findByEmail(email: string, tenantId: string): Promise<ContactEntity | null> {
    // Implementation
  }

  // Other method implementations...
}
```

#### Data Mapper Pattern

Data mappers convert between domain entities and database models:

```typescript
// Domain entity
interface ContactEntity {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  tenantId: string;
  // Other business properties...
}

// Database model
interface IContact extends Document {
  _id: mongoose.Types.ObjectId;
  firstName: string;
  lastName: string;
  email?: string;
  tenantId: string;
  // Other database properties...
}

// Mapper functions
function toEntity(model: IContact): ContactEntity {
  if (!model) return null as any;

  return {
    id: model._id.toString(),
    firstName: model.firstName,
    lastName: model.lastName,
    email: model.email,
    tenantId: model.tenantId,
    // Map other properties...
  };
}

function toModel(entity: Partial<ContactEntity>): Partial<IContact> {
  if (!entity) return null as any;

  const model: Partial<IContact> = {
    firstName: entity.firstName,
    lastName: entity.lastName,
    email: entity.email,
    tenantId: entity.tenantId,
    // Map other properties...
  };

  if (entity.id) {
    model._id = new mongoose.Types.ObjectId(entity.id);
  }

  return model;
}
```

#### Transactions

For operations that affect multiple documents, the system uses MongoDB transactions:

```typescript
async createContactWithCompany(contactData, companyData, tenantId) {
  return await withTransaction(async (session) => {
    const company = await this.companyRepo.create({
      ...companyData,
      tenantId,
    });

    const contact = await this.contactRepo.create({
      ...contactData,
      companyId: company.id,
      tenantId,
    });

    return { contact, company };
  });
}
```

### Authentication and Authorization

Authentication is handled using Firebase Authentication. The system uses JWT tokens for API authentication and implements role-based access control.

```typescript
// Example middleware
const authenticateUser = async (req: Request, res: Response, next: NextFunction) => {
  // Implementation
};

const requireAdmin = async (req: Request, res: Response, next: NextFunction) => {
  // Implementation
};
```

### Multi-Tenancy

The system is designed to support multiple tenants, with data isolation between tenants. All data access includes tenant ID filtering.

```typescript
// Example tenant isolation
const tenantIsolationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Implementation
};
```

### API Layer

The API layer follows RESTful principles and uses Express.js for routing. It includes validation, error handling, and authentication middleware.

```typescript
// Example API route
router.get('/contacts', authenticateUser, validateQuery(querySchema), async (req, res) => {
  // Implementation
});
```

## Design Patterns

### Repository Pattern

The repository pattern abstracts data access and provides a consistent interface for working with domain entities. It separates the business logic from the data access logic, making the code more maintainable and testable.

```typescript
// Repository interface
interface IRepository<T> {
  findById(id: string, tenantId: string): Promise<T | null>;
  findAll(tenantId: string, options?: QueryOptions): Promise<T[]>;
  create(entity: Partial<T>): Promise<T>;
  update(id: string, entity: Partial<T>, tenantId: string): Promise<T | null>;
  delete(id: string, tenantId: string): Promise<boolean>;
}

// MongoDB implementation
class MongoDBRepository<T, D> implements IRepository<T> {
  // Implementation
}

// Specific repository
class ContactRepository extends MongoDBRepository<ContactEntity, any> {
  // Domain-specific methods
}
```

### Data Mapper Pattern

The data mapper pattern separates domain entities from their persistence mechanism. It provides a clear separation between the business logic and the database, allowing each to evolve independently.

```typescript
// Domain entity
interface ContactEntity {
  id: string;
  firstName: string;
  // Business properties
}

// Database model
interface IContact extends Document {
  _id: ObjectId;
  firstName: string;
  // Database properties
}

// Mapper functions
function toEntity(model: IContact): ContactEntity {
  // Convert database model to domain entity
}

function toModel(entity: ContactEntity): Partial<IContact> {
  // Convert domain entity to database model
}
```

### Dependency Injection

Services and repositories are injected into controllers and other components, promoting loose coupling and testability.

```typescript
// Service with injected repository
class ContactService {
  constructor(private contactRepo: IContactRepository = contactRepository) {
    // Dependency injection
  }

  async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
    return await this.contactRepo.findById(id, tenantId);
  }
}

// In tests, you can inject a mock repository
const mockRepo = { findById: jest.fn() };
const service = new ContactService(mockRepo);
```

### Factory Pattern

Factories are used to create complex objects, especially when multiple implementations are possible.

```typescript
// Repository factory
class RepositoryFactory {
  static createContactRepository(): IContactRepository {
    return new ContactRepository();
  }

  static createCompanyRepository(): ICompanyRepository {
    return new CompanyRepository();
  }
}
```

### Strategy Pattern

The strategy pattern is used for implementing different algorithms or approaches that can be selected at runtime.

```typescript
// Strategy interface
interface ScoringStrategy {
  calculateScore(contact: ContactEntity): number;
}

// Concrete strategies
class EngagementScoringStrategy implements ScoringStrategy {
  calculateScore(contact: ContactEntity): number {
    // Implementation
  }
}

class FitScoringStrategy implements ScoringStrategy {
  calculateScore(contact: ContactEntity): number {
    // Implementation
  }
}

// Context
class ContactScorer {
  constructor(private strategy: ScoringStrategy) {}

  score(contact: ContactEntity): number {
    return this.strategy.calculateScore(contact);
  }
}
```

### Observer Pattern

The observer pattern is used for event handling, allowing components to subscribe to events without tight coupling.

```typescript
// Event emitter
class ContactEventEmitter {
  private listeners: Map<string, Function[]> = new Map();

  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  emit(event: string, data: any): void {
    if (this.listeners.has(event)) {
      for (const listener of this.listeners.get(event)!) {
        listener(data);
      }
    }
  }
}

// Usage
const emitter = new ContactEventEmitter();
emitter.on('contact:created', (contact) => {
  // Handle contact created event
});
```

## Cross-Cutting Concerns

### Logging

The system uses a structured logging approach with different log levels (debug, info, warn, error).

### Error Handling

Errors are handled consistently across the application, with appropriate HTTP status codes and error messages.

### Validation

Input validation is performed using Zod schemas, ensuring that data meets the required format and constraints.

### Caching

Performance-critical data is cached to reduce database load and improve response times.

## Testing

The system includes comprehensive tests at different levels:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **End-to-End Tests**: Test the entire system from the user's perspective

## Deployment

The system is designed to be deployed as a set of Docker containers, with separate containers for the API, web UI, and background workers.

## Conclusion

The Aizako CRM architecture follows best practices for modern web applications, with a focus on maintainability, scalability, and testability. The clean architecture approach ensures that the system can evolve over time without becoming difficult to maintain.
