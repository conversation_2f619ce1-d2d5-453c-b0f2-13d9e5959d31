# MongoDB Transactions Guide

This document explains how to use MongoDB transactions in the Aizako CRM project.

## Overview

MongoDB transactions allow multiple operations to be executed atomically. This means that either all operations succeed or none of them do, ensuring data consistency.

Transactions are essential for operations that need to update multiple documents or collections atomically, such as:

- Creating a user and their profile
- Transferring funds between accounts
- Updating related documents

## Requirements

MongoDB transactions require a replica set. In production, MongoDB Atlas provides this automatically. For local development, you need to configure a replica set.

### Local Replica Set Setup

To use transactions locally, you need to run MongoDB as a replica set:

```bash
# Create data directories
mkdir -p ~/data/rs1 ~/data/rs2 ~/data/rs3

# Start MongoDB instances
mongod --replSet rs0 --port 27017 --dbpath ~/data/rs1
mongod --replSet rs0 --port 27018 --dbpath ~/data/rs2
mongod --replSet rs0 --port 27019 --dbpath ~/data/rs3

# Initialize replica set
mongo --port 27017
```

In the MongoDB shell:

```javascript
rs.initiate({
  _id: "rs0",
  members: [
    { _id: 0, host: "localhost:27017" },
    { _id: 1, host: "localhost:27018" },
    { _id: 2, host: "localhost:27019" }
  ]
});
```

## Using Transactions

Aizako CRM provides a `withTransaction` utility function that simplifies working with transactions:

```typescript
import { withTransaction } from '../utils/transactions';

// Example: Create a user and profile in a transaction
const result = await withTransaction(async (session) => {
  // Create user
  const user = await User.create([{ 
    name: 'John Doe',
    email: '<EMAIL>'
  }], { session });
  
  // Create profile
  const profile = await Profile.create([{
    userId: user[0]._id,
    bio: 'Software developer'
  }], { session });
  
  return { user: user[0], profile: profile[0] };
});
```

### Transaction Options

The `withTransaction` function accepts options to customize transaction behavior:

```typescript
const result = await withTransaction(
  async (session) => {
    // Transaction operations
  },
  {
    readConcern: 'majority',
    writeConcern: { w: 'majority' },
    maxRetries: 3,
    retryDelay: 100
  }
);
```

| Option | Description | Default |
|--------|-------------|---------|
| `readConcern` | Read concern level | `'majority'` |
| `writeConcern` | Write concern settings | `{ w: 'majority' }` |
| `maxRetries` | Maximum number of retry attempts | `3` |
| `retryDelay` | Delay between retries in milliseconds | `100` |

## Retry Logic

For operations that may fail due to transient errors (e.g., network issues, write conflicts), Aizako CRM provides a `withRetry` utility:

```typescript
import { withRetry } from '../utils/transactions';

// Example: Update a counter with retry logic
const updatedUser = await withRetry(async () => {
  return await User.findOneAndUpdate(
    { _id: userId },
    { $inc: { loginCount: 1 } },
    { new: true }
  );
});
```

## Best Practices

### 1. Keep Transactions Short

Transactions should be as short as possible to avoid holding locks for too long:

```typescript
// Good: Short transaction
await withTransaction(async (session) => {
  await User.create([userData], { session });
  await Profile.create([profileData], { session });
});

// Bad: Long transaction with external API call
await withTransaction(async (session) => {
  await User.create([userData], { session });
  await externalApiCall(); // Avoid this inside a transaction
  await Profile.create([profileData], { session });
});
```

### 2. Include All Related Operations

Make sure to include all related operations in the transaction:

```typescript
// Good: All related operations in one transaction
await withTransaction(async (session) => {
  const user = await User.create([userData], { session });
  await Profile.create([{ userId: user[0]._id, ...profileData }], { session });
  await Settings.create([{ userId: user[0]._id, ...settingsData }], { session });
});

// Bad: Missing related operations
const user = await User.create(userData);
await withTransaction(async (session) => {
  await Profile.create([{ userId: user._id, ...profileData }], { session });
  // Missing Settings creation
});
```

### 3. Handle Errors Properly

Always handle transaction errors properly:

```typescript
try {
  await withTransaction(async (session) => {
    // Transaction operations
  });
  // Success handling
} catch (error) {
  // Error handling
  logger.error('Transaction failed', { error });
  throw new DatabaseError('Failed to create user', 'USER_CREATION_FAILED');
}
```

### 4. Use Session for All Operations

Make sure to pass the session to all database operations:

```typescript
await withTransaction(async (session) => {
  // Good: Session passed to all operations
  const user = await User.findOne({ email }, { session });
  await User.updateOne({ _id: user._id }, { lastLogin: new Date() }, { session });
  
  // Bad: Missing session
  await LoginHistory.create({ userId: user._id }); // Missing session
});
```

### 5. Avoid Unnecessary Transactions

Not all operations need transactions:

```typescript
// Good: Single operation doesn't need a transaction
await User.updateOne({ _id: userId }, { lastLogin: new Date() });

// Unnecessary: Single operation in a transaction
await withTransaction(async (session) => {
  await User.updateOne({ _id: userId }, { lastLogin: new Date() }, { session });
});
```

## Common Use Cases

### 1. Creating Related Documents

```typescript
// Create a company and its initial contact
await withTransaction(async (session) => {
  const company = await Company.create([{
    name: 'Acme Inc',
    industry: 'Technology',
    tenantId
  }], { session });
  
  await Contact.create([{
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    companyId: company[0]._id,
    tenantId
  }], { session });
});
```

### 2. Updating Multiple Documents

```typescript
// Update opportunity stage and create activity
await withTransaction(async (session) => {
  await Opportunity.updateOne(
    { _id: opportunityId, tenantId },
    { stage: 'closed_won', closedAt: new Date() },
    { session }
  );
  
  await Activity.create([{
    type: 'opportunity_closed',
    opportunityId,
    tenantId,
    notes: 'Opportunity closed as won',
    createdBy: userId
  }], { session });
});
```

### 3. Deleting Related Documents

```typescript
// Delete a company and all its contacts
await withTransaction(async (session) => {
  await Contact.deleteMany({ companyId, tenantId }, { session });
  await Company.deleteOne({ _id: companyId, tenantId }, { session });
});
```

## Troubleshooting

### Transaction Errors

If you encounter transaction errors, check the following:

1. **Replica Set**: Make sure MongoDB is running as a replica set.
2. **Connection String**: Ensure the connection string includes `replicaSet` parameter.
3. **Write Concern**: Check if write concern is appropriate for your use case.
4. **Transaction Size**: Large transactions may time out; break them into smaller ones.
5. **Session Usage**: Ensure the session is passed to all operations in the transaction.

### Common Error Messages

- `Transaction numbers are only allowed on a replica set member`: MongoDB is not running as a replica set.
- `Cannot use transactions with unacknowledged write concern`: Write concern is set to `{ w: 0 }`.
- `Transaction 123 has been aborted`: Another operation in the transaction failed.
- `Transaction exceeds maximum size`: Transaction is too large; break it into smaller ones.
