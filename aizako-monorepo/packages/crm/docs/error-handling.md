# Error Handling Guide

This document explains the error handling system in the Aizako CRM project.

## Overview

Aizako CRM uses a centralized error handling system that provides consistent error responses across the application. The system includes:

1. Custom error classes
2. Error handling middleware
3. Utility functions for error conversion and formatting
4. Structured logging

## Error Classes

All custom errors extend the base `AppError` class, which provides common functionality:

```typescript
class AppError extends Error {
  public statusCode: number;
  public errorCode: string;
  public details?: any;
  public isOperational: boolean;
  
  constructor(
    message: string,
    statusCode: number = 500,
    errorCode: string = 'INTERNAL_SERVER_ERROR',
    details?: any,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}
```

### Available Error Classes

| Class | Status Code | Description |
|-------|------------|-------------|
| `NotFoundError` | 404 | Resource not found |
| `ValidationError` | 400 | Input validation failed |
| `AuthenticationError` | 401 | Authentication failed |
| `AuthorizationError` | 403 | Not authorized to perform action |
| `ConflictError` | 409 | Resource conflict (e.g., duplicate key) |
| `RateLimitError` | 429 | Rate limit exceeded |
| `DependencyError` | 502 | Service dependency error |
| `DatabaseError` | 500 | Database operation error |
| `NotImplementedError` | 501 | Feature not implemented |
| `TenantError` | 403 | Tenant-related error |
| `SubscriptionError` | 402 | Subscription-related error |

## Error Handling Middleware

The error handling middleware catches all errors thrown in route handlers and formats them into a consistent response format:

```typescript
function errorHandler(err, req, res, next) {
  // Convert error to AppError
  const appError = toAppError(err);
  
  // Log error
  if (appError.isOperational) {
    logger.warn({ /* error details */ });
  } else {
    logger.error({ /* error details with stack trace */ });
  }
  
  // Format error for response
  const formattedError = formatError(appError);
  
  // Send response
  res.status(appError.statusCode).json(formattedError);
}
```

## Error Response Format

All API errors are returned in a consistent format:

```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human-readable error message",
  "details": [] // Optional array of detailed error information
}
```

## Operational vs. Programming Errors

The error handling system distinguishes between two types of errors:

1. **Operational Errors**: Expected errors that occur during normal operation (e.g., validation errors, not found errors). These are logged at the `warn` level.

2. **Programming Errors**: Unexpected errors that indicate bugs in the code (e.g., null reference errors, type errors). These are logged at the `error` level with stack traces.

## Using the Error System

### Throwing Errors

```typescript
// Not found error
throw new NotFoundError('Contact not found', 'CONTACT_NOT_FOUND');

// Validation error
throw new ValidationError('Invalid input', 'INVALID_INPUT', [
  { path: 'email', message: 'Invalid email format' }
]);

// Authentication error
throw new AuthenticationError('Invalid token', 'INVALID_TOKEN');

// Authorization error
throw new AuthorizationError('Not authorized to access this resource', 'ACCESS_DENIED');
```

### Async Error Handling

Use the `asyncHandler` utility to catch errors in async route handlers:

```typescript
router.get('/contacts/:id', asyncHandler(async (req, res) => {
  const contact = await contactService.getContactById(req.params.id);
  
  if (!contact) {
    throw new NotFoundError('Contact not found', 'CONTACT_NOT_FOUND');
  }
  
  res.json({ success: true, contact });
}));
```

### Error Conversion

The `toAppError` utility converts various error types to `AppError` instances:

```typescript
// Convert any error to AppError
const appError = toAppError(error);
```

## Logging

The error handling system integrates with the logging system to provide structured logs for all errors:

```typescript
// Log error
logger.error({
  message: 'Failed to create contact',
  errorCode: 'DATABASE_ERROR',
  details: { email: '<EMAIL>' },
  stack: error.stack
});
```

## Best Practices

1. **Use Specific Error Classes**: Use the most specific error class for each error case.

2. **Include Error Codes**: Always include an error code that can be used for client-side error handling.

3. **Provide Detailed Messages**: Error messages should be clear and helpful for debugging.

4. **Use Async Handler**: Always wrap async route handlers with the `asyncHandler` utility.

5. **Validate Input**: Use Zod schemas to validate input and throw `ValidationError` for invalid input.

6. **Handle Edge Cases**: Consider all possible error cases and handle them appropriately.

7. **Log Contextual Information**: Include relevant context in error logs (e.g., user ID, tenant ID).

## Example Usage

```typescript
// Service function
async function createContact(data, tenantId) {
  try {
    // Validate input
    const validatedData = contactSchema.parse(data);
    
    // Check for duplicate email
    const existingContact = await Contact.findOne({ 
      email: validatedData.email, 
      tenantId 
    });
    
    if (existingContact) {
      throw new ConflictError(
        'Contact with this email already exists',
        'DUPLICATE_EMAIL'
      );
    }
    
    // Create contact
    const contact = await Contact.create({
      ...validatedData,
      tenantId
    });
    
    return contact;
  } catch (error) {
    // Convert Zod errors to ValidationError
    if (error instanceof z.ZodError) {
      throw new ValidationError(
        'Invalid contact data',
        'INVALID_CONTACT_DATA',
        error.errors
      );
    }
    
    // Re-throw other errors
    throw error;
  }
}

// Route handler
router.post('/contacts', asyncHandler(async (req, res) => {
  const contact = await createContact(req.body, req.tenantId);
  res.status(201).json({ success: true, contact });
}));
```
