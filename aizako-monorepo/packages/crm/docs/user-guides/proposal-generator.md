# Proposal Generator User Guide

The Aizako CRM Proposal Generator is a powerful tool that helps you create professional business proposals quickly and easily. This guide will walk you through all the features and functionality of the Proposal Generator.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Creating a New Proposal](#creating-a-new-proposal)
3. [Editing Proposal Details](#editing-proposal-details)
4. [Managing Sections](#managing-sections)
5. [Working with Pricing](#working-with-pricing)
6. [AI-Powered Generation](#ai-powered-generation)
7. [Previewing Your Proposal](#previewing-your-proposal)
8. [Downloading Proposals](#downloading-proposals)
9. [Sending Proposals](#sending-proposals)
10. [Tracking Proposal Analytics](#tracking-proposal-analytics)
11. [Keyboard Shortcuts](#keyboard-shortcuts)
12. [Accessibility Features](#accessibility-features)
13. [Troubleshooting](#troubleshooting)

## Getting Started

To access the Proposal Generator, navigate to the "Proposals" section in the main navigation menu of your Aizako CRM dashboard. This will take you to the Proposals dashboard where you can see all your existing proposals and create new ones.

## Creating a New Proposal

There are several ways to create a new proposal:

1. **From the Proposals Dashboard**: Click the "New Proposal" button in the top-right corner of the Proposals dashboard.

2. **From an Opportunity**: Open an opportunity and click the "Create Proposal" button in the opportunity details page. This will pre-fill the proposal with information from the opportunity.

3. **From a Company**: Open a company and click the "Create Proposal" button in the company details page. This will pre-fill the proposal with information from the company.

4. **From a Contact**: Open a contact and click the "Create Proposal" button in the contact details page. This will pre-fill the proposal with information from the contact.

## Editing Proposal Details

The Proposal Editor is divided into several sections:

### Basic Information

- **Title**: Enter a descriptive title for your proposal. This will be visible to your client.
- **Description**: Provide a brief overview of what the proposal is about.

### Proposal Status

Proposals can have one of the following statuses:

- **Draft**: The proposal is still being edited and has not been sent to the client.
- **Sent**: The proposal has been sent to the client but has not been viewed yet.
- **Viewed**: The client has viewed the proposal but has not accepted or rejected it.
- **Accepted**: The client has accepted the proposal.
- **Rejected**: The client has rejected the proposal.
- **Expired**: The proposal has expired and is no longer valid.

## Managing Sections

Proposals are organized into sections, which you can add, edit, reorder, and delete.

### Adding a Section

Click the "Add Section" button to add a new section to your proposal. You can choose from different section types:

- **Text**: A standard text section with rich text formatting.
- **Timeline**: A section for outlining project timelines.
- **Team**: A section for introducing team members.
- **Testimonials**: A section for showcasing client testimonials.
- **Images**: A section for adding images and diagrams.
- **Custom**: A custom section type for specialized content.

### Editing a Section

Each section has the following properties:

- **Title**: The section title, which will be displayed as a heading.
- **Content**: The main content of the section, which can include formatted text, lists, tables, and more.
- **Type**: The section type, which determines how the content is displayed.
- **Visibility**: Whether the section is visible in the final proposal.

### Reordering Sections

You can reorder sections by dragging and dropping them in the editor. Use the drag handle on the left side of each section to drag it to a new position.

### Deleting a Section

To delete a section, click the delete button (trash icon) in the section header.

## Working with Pricing

The Pricing section allows you to add and manage pricing items for your proposal.

### Adding Pricing Items

Click the "Add Item" button to add a new pricing item. Each item has the following properties:

- **Name**: The name of the item.
- **Description**: A description of what the item includes.
- **Quantity**: The quantity of the item.
- **Unit Price**: The price per unit.
- **Total**: The total price for the item (calculated automatically).

### Managing Pricing

You can also set the following pricing options:

- **Currency**: The currency for the proposal.
- **Discount**: An optional discount amount.
- **Tax**: An optional tax amount.

The subtotal and total are calculated automatically based on the items, discount, and tax.

## AI-Powered Generation

The Proposal Generator includes AI-powered features to help you create proposals quickly and easily.

### Generating a Complete Proposal

To generate a complete proposal with AI:

1. Click the "AI Generate" button in the Sections header.
2. Enter a prompt describing what you want to include in the proposal.
3. Select the AI model to use.
4. Choose which sections to include.
5. Click "Generate Proposal" to create the proposal.

The AI will generate a complete proposal based on your prompt and selected sections, including title, description, sections, pricing, and terms.

### Generating a Single Section

To generate a single section with AI:

1. Click the "AI Generate" button in the section editor.
2. Enter a prompt describing what you want to include in the section.
3. Click "Generate" to create the section content.

## Previewing Your Proposal

To preview your proposal, click the "Preview" tab at the top of the editor. This will show you how the proposal will look when viewed by the client.

### Preview Options

The preview includes several options:

- **View Mode**: Choose between Desktop, Mobile, or Print view.
- **Color Scheme**: Choose between Professional, Creative, Modern, or Classic color schemes.

## Downloading Proposals

To download a proposal, click the "Download" button in the editor toolbar. You can download the proposal in the following formats:

- **PDF**: A PDF document that can be printed or shared electronically.
- **DOCX**: A Microsoft Word document that can be edited further.
- **Markdown**: A plain text format with simple formatting.

## Sending Proposals

To send a proposal to a client, click the "Send" button in the editor toolbar. This will open the Send Proposal dialog, where you can:

- **Add Recipients**: Enter email addresses for the primary recipients, CC, and BCC.
- **Add a Subject**: Enter a subject for the email.
- **Add a Message**: Enter a message to accompany the proposal.
- **Set Options**: Choose whether to include a view link, download link, and attachments.
- **Set Expiration**: Set an expiration date for the proposal.

## Tracking Proposal Analytics

The Proposal Analytics dashboard provides insights into how your proposals are performing.

### Key Metrics

- **Total Proposals**: The total number of proposals created.
- **View Rate**: The percentage of sent proposals that have been viewed.
- **Acceptance Rate**: The percentage of viewed proposals that have been accepted.
- **Total Value**: The total value of accepted proposals.

### Charts and Graphs

- **Proposal Activity**: A chart showing proposal activity over time.
- **Proposal Status**: A chart showing the distribution of proposal statuses.

### Recent Proposals

A table showing your most recent proposals and their status.

## Keyboard Shortcuts

The Proposal Generator includes keyboard shortcuts to help you work more efficiently:

- **Ctrl+S** (or **⌘S** on Mac): Save the proposal.
- **Ctrl+P** (or **⌘P** on Mac): Preview the proposal.
- **Ctrl+D** (or **⌘D** on Mac): Download the proposal.
- **Ctrl+E** (or **⌘E** on Mac): Send the proposal.
- **Ctrl+N** (or **⌘N** on Mac): Add a new section.
- **Escape**: Close dialogs.

To view all available keyboard shortcuts, press **Ctrl+/** (or **⌘/** on Mac).

## Accessibility Features

The Proposal Generator is designed to be accessible to all users, including those who use screen readers or keyboard navigation.

### Screen Reader Support

- All interactive elements have appropriate ARIA labels.
- Live regions announce important changes.
- Form fields have proper labels and descriptions.

### Keyboard Navigation

- All interactive elements can be accessed using the keyboard.
- Focus indicators are visible and clear.
- Keyboard shortcuts are available for common actions.

## Troubleshooting

### Common Issues

#### Proposal Not Saving

If you're having trouble saving a proposal, try the following:

1. Check your internet connection.
2. Make sure all required fields are filled out.
3. Try refreshing the page and trying again.

#### AI Generation Not Working

If AI generation is not working, try the following:

1. Make sure your prompt is clear and specific.
2. Try using a different AI model.
3. Check if you have reached your AI generation limit for the day.

#### Proposal Not Sending

If you're having trouble sending a proposal, try the following:

1. Check that all recipient email addresses are valid.
2. Make sure the proposal has been saved.
3. Check your email sending limits.

### Getting Help

If you need further assistance, you can:

- Click the "Help" button in the top-right corner of the Proposal Generator.
- Contact <NAME_EMAIL>.
- Visit the Aizako CRM Help Center at https://help.aizako.com.
