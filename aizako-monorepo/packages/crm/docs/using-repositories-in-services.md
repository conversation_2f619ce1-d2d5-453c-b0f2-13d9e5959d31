# Using Repositories and Data Mappers in Services

This guide explains how to use repositories and data mappers in services within the Aizako CRM project.

## Overview

In Aizako CRM, we follow a layered architecture:

1. **Domain Layer**: Contains domain entities and business logic
2. **Repository Layer**: Handles data access and persistence
3. **Service Layer**: Orchestrates business operations using domain entities and repositories
4. **API Layer**: Exposes services to clients through HTTP endpoints

This guide focuses on how services should interact with repositories and domain entities.

## Service Structure

Services in Aizako CRM should follow these principles:

1. **Use Dependency Injection**: Inject repositories into services
2. **Work with Domain Entities**: Services should only work with domain entities, not database models
3. **Delegate Data Access**: Services should delegate all data access to repositories
4. **Handle Business Logic**: Services should focus on business logic and orchestration

## Example Service

Here's an example of a well-structured service:

```typescript
import { ContactEntity } from '../mappers/contact-mapper';
import { IContactRepository, contactRepository } from '../repositories';
import { logger } from '../utils/logger';

/**
 * Contact service
 * 
 * This service handles business operations related to contacts.
 */
export class ContactService {
  /**
   * Contact repository
   */
  private readonly contactRepo: IContactRepository;
  
  /**
   * Create a new contact service
   * @param contactRepo Contact repository
   */
  constructor(contactRepo: IContactRepository = contactRepository) {
    this.contactRepo = contactRepo;
  }
  
  /**
   * Get contact by ID
   * @param id Contact ID
   * @param tenantId Tenant ID
   * @returns Contact or null if not found
   */
  async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
    try {
      return await this.contactRepo.findById(id, tenantId);
    } catch (error) {
      logger.error('Error getting contact by ID:', { error, id, tenantId });
      throw error;
    }
  }
  
  /**
   * Create a new contact
   * @param contactData Contact data
   * @param tenantId Tenant ID
   * @returns Created contact
   */
  async createContact(contactData: Partial<ContactEntity>, tenantId: string): Promise<ContactEntity> {
    try {
      // Apply business rules
      const contact = {
        ...contactData,
        tenantId,
        status: contactData.status || 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Delegate to repository
      return await this.contactRepo.create(contact);
    } catch (error) {
      logger.error('Error creating contact:', { error, contactData, tenantId });
      throw error;
    }
  }
}
```

## Best Practices

### 1. Dependency Injection

Use dependency injection to make services testable:

```typescript
// Service constructor with default repository
constructor(contactRepo: IContactRepository = contactRepository) {
  this.contactRepo = contactRepo;
}

// In tests, you can inject a mock repository
const mockRepo = { findById: jest.fn() };
const service = new ContactService(mockRepo);
```

### 2. Working with Domain Entities

Services should work with domain entities, not database models:

```typescript
// Good: Using domain entities
async createContact(contactData: Partial<ContactEntity>, tenantId: string): Promise<ContactEntity> {
  // Implementation
}

// Bad: Using database models
async createContact(contactData: Partial<IContact>, tenantId: string): Promise<IContact> {
  // Implementation
}
```

### 3. Error Handling

Handle errors consistently in services:

```typescript
async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
  try {
    return await this.contactRepo.findById(id, tenantId);
  } catch (error) {
    logger.error('Error getting contact by ID:', { error, id, tenantId });
    throw error;
  }
}
```

### 4. Business Logic

Services should handle business logic, while repositories handle data access:

```typescript
async updateContactStatus(id: string, status: string, tenantId: string): Promise<ContactEntity | null> {
  try {
    // Get current contact
    const contact = await this.contactRepo.findById(id, tenantId);
    if (!contact) return null;
    
    // Business logic: Check if status change is allowed
    if (contact.status === 'deleted' && status !== 'deleted') {
      throw new BusinessError('Cannot change status of deleted contact');
    }
    
    // Apply status change
    const updatedContact = {
      ...contact,
      status,
      updatedAt: new Date(),
    };
    
    // Delegate to repository
    return await this.contactRepo.update(id, updatedContact, tenantId);
  } catch (error) {
    logger.error('Error updating contact status:', { error, id, status, tenantId });
    throw error;
  }
}
```

### 5. Transactions

For operations that affect multiple entities, use transactions:

```typescript
async createContactWithCompany(
  contactData: Partial<ContactEntity>,
  companyData: Partial<CompanyEntity>,
  tenantId: string
): Promise<{ contact: ContactEntity; company: CompanyEntity }> {
  try {
    return await withTransaction(async (session) => {
      // Create company
      const company = await this.companyRepo.create({
        ...companyData,
        tenantId,
      });
      
      // Create contact with company reference
      const contact = await this.contactRepo.create({
        ...contactData,
        companyId: company.id,
        tenantId,
      });
      
      return { contact, company };
    });
  } catch (error) {
    logger.error('Error creating contact with company:', { error, contactData, companyData, tenantId });
    throw error;
  }
}
```

### 6. Validation

Validate input data in services before passing it to repositories:

```typescript
async createContact(contactData: Partial<ContactEntity>, tenantId: string): Promise<ContactEntity> {
  try {
    // Validate required fields
    if (!contactData.firstName || !contactData.lastName) {
      throw new ValidationError('First name and last name are required');
    }
    
    // Validate email format
    if (contactData.email && !isValidEmail(contactData.email)) {
      throw new ValidationError('Invalid email format');
    }
    
    // Check for duplicate email
    if (contactData.email) {
      const existingContact = await this.contactRepo.findByEmail(contactData.email, tenantId);
      if (existingContact) {
        throw new ConflictError('Contact with this email already exists');
      }
    }
    
    // Create contact
    return await this.contactRepo.create({
      ...contactData,
      tenantId,
    });
  } catch (error) {
    logger.error('Error creating contact:', { error, contactData, tenantId });
    throw error;
  }
}
```

## Converting from Static to Instance Methods

If you're converting from static methods to instance methods, follow this pattern:

### Before (Static Methods)

```typescript
export class ContactService {
  static async getContactById(id: string, tenantId: string): Promise<IContact | null> {
    try {
      return await Contact.findOne({ _id: id, tenantId });
    } catch (error) {
      console.error('Error getting contact by ID:', error);
      throw error;
    }
  }
}

// Usage
const contact = await ContactService.getContactById(id, tenantId);
```

### After (Instance Methods with Repositories)

```typescript
export class ContactService {
  private readonly contactRepo: IContactRepository;
  
  constructor(contactRepo: IContactRepository = contactRepository) {
    this.contactRepo = contactRepo;
  }
  
  async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
    try {
      return await this.contactRepo.findById(id, tenantId);
    } catch (error) {
      logger.error('Error getting contact by ID:', { error, id, tenantId });
      throw error;
    }
  }
}

// Create service instance
export const contactService = new ContactService();

// Usage
const contact = await contactService.getContactById(id, tenantId);
```

## Conclusion

By following these guidelines, you'll create services that are:

1. **Testable**: Easy to test with mock repositories
2. **Maintainable**: Clear separation of concerns
3. **Flexible**: Can change database implementation without affecting business logic
4. **Consistent**: Follow the same patterns throughout the codebase

Remember that services should focus on business logic and orchestration, while repositories handle data access and persistence.
