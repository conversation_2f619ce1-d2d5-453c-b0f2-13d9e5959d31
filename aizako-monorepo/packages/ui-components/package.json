{"name": "@aizako/ui-components", "version": "0.1.0", "description": "Shared UI components for Aizako CRM", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "test": "jest"}, "keywords": ["ui", "components", "react", "material-ui", "<PERSON><PERSON><PERSON>"], "author": "Aizako", "license": "UNLICENSED", "dependencies": {"@aizako/ui-kit": "workspace:*", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@mui/x-date-pickers": "^6.19.8", "@mui/x-date-pickers-pro": "^6.19.8", "date-fns": "^3.6.0", "dompurify": "^3.0.11", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "recharts": "^2.12.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/dompurify": "^3.0.5", "@types/draft-js": "^0.11.18", "@types/jest": "^29.5.12", "@types/node": "^20.16.11", "@types/react": "^18.3.11", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.1", "eslint": "^8.57.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}