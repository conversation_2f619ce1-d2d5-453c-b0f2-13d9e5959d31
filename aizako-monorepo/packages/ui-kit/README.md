# Aizako UI Kit

The Aizako UI Kit (`@aizako/ui-kit`) is a shared UI component library used across all Aizako modules. It provides a consistent design system and reusable components.

## Installation

```bash
pnpm add @aizako/ui-kit
```

## Features

### Components

The UI Kit provides a set of reusable components:

```typescript
import { 
  Button, 
  Card, 
  Input, 
  Select, 
  Modal, 
  Tabs, 
  Table 
} from '@aizako/ui-kit';

function MyComponent() {
  return (
    <Card>
      <h2>My Component</h2>
      <Input placeholder="Enter your name" />
      <Button>Submit</Button>
    </Card>
  );
}
```

### Hooks

The UI Kit provides custom React hooks:

```typescript
import { 
  useMediaQuery, 
  useLocalStorage, 
  useDebounce 
} from '@aizako/ui-kit';

function MyComponent() {
  const isDesktop = useMediaQuery('(min-width: 1024px)');
  const [name, setName] = useLocalStorage('name', '');
  const debouncedName = useDebounce(name, 500);
  
  return (
    <div>
      <p>Is Desktop: {isDesktop ? 'Yes' : 'No'}</p>
      <input 
        value={name} 
        onChange={(e) => setName(e.target.value)} 
        placeholder="Enter your name" 
      />
      <p>Debounced Name: {debouncedName}</p>
    </div>
  );
}
```

### Styles

The UI Kit provides shared styles and themes:

```typescript
import { 
  theme, 
  globalStyles, 
  createTheme 
} from '@aizako/ui-kit';

// Use the default theme
console.log(theme.colors.primary);

// Create a custom theme
const customTheme = createTheme({
  colors: {
    primary: '#ff0000',
  },
});
```

### Utils

The UI Kit provides UI-related utilities:

```typescript
import { 
  cn, 
  formatDate, 
  truncateText 
} from '@aizako/ui-kit';

// Combine class names
const className = cn(
  'text-red-500',
  isActive && 'font-bold',
  { 'bg-blue-500': isHighlighted }
);

// Format a date
const formattedDate = formatDate(new Date(), 'yyyy-MM-dd');

// Truncate text
const truncated = truncateText('This is a long text that will be truncated', 20);
```

## Development

### Project Structure

```
/packages/ui-kit
  ├─ src
  │   ├─ components   (UI components)
  │   ├─ hooks        (Custom React hooks)
  │   ├─ styles       (Shared styles and themes)
  │   ├─ utils        (UI-related utilities)
  │   └─ index.ts     (Entry point)
  ├─ package.json
  ├─ tsconfig.json
  └─ tsup.config.ts
```

### Adding a new component

1. Create a new file in the `src/components` directory, e.g., `src/components/MyComponent.tsx`:

```typescript
import React from 'react';
import { cn } from '../utils';

export interface MyComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export function MyComponent({ className, children }: MyComponentProps) {
  return (
    <div className={cn('my-component', className)}>
      {children}
    </div>
  );
}
```

2. Export the component in the `src/components/index.ts` file:

```typescript
export * from './MyComponent';
```

### Building

```bash
pnpm build
```

This will build the library using tsup and output the result to the `dist` directory.

### Testing

```bash
pnpm test
```

This will run the tests using Vitest.

## License

Copyright (c) 2024 Aizako. All rights reserved.
