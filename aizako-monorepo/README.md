# Aizako Monorepo

This is the monorepo for Aizako's modular SaaS platform, containing all modules and applications.

## Structure

```
/packages
  ├─ crm             (CRM module)
  │  ├─ models       (MongoDB models)
  │  ├─ services     (Business logic)
  │  ├─ api          (API routes)
  │  ├─ components   (React components)
  │  └─ utils        (Utility functions)
  ├─ scribe          (Scribe module - to be integrated)
  ├─ invoicing       (Invoicing module - future)
  ├─ onboarding      (Onboarding module - future)
  ├─ ui-kit          (Shared design system)
  │  ├─ components   (UI components)
  │  ├─ hooks        (React hooks)
  │  ├─ styles       (Shared styles)
  │  └─ utils        (UI utilities)
  └─ core-lib        (Shared core libraries)
     ├─ auth         (Authentication)
     ├─ tenancy      (Multi-tenant infrastructure)
     ├─ database     (Database utilities)
     └─ types        (Shared types)
/apps
  ├─ demo-saas       (Demo SaaS application)
  │  ├─ pages        (React pages)
  │  ├─ components   (App-specific components)
  │  └─ hooks        (App-specific hooks)
  └─ cli             (CLI tool for module management)
     ├─ commands     (CLI commands)
     └─ utils        (CLI utilities)
```

## Documentation

For more detailed documentation, see:

- [Monorepo Architecture](./MONOREPO.md)
- [Core Library](./packages/core-lib/README.md)
- [UI Kit](./packages/ui-kit/README.md)
- [CRM Module](./packages/crm/README.md)
- [Demo SaaS App](./apps/demo-saas/README.md)
- [CLI Tool](./apps/cli/README.md)

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm 8+
- MongoDB (local or MongoDB Atlas)
- Firebase account (for authentication)

### Environment Setup

1. Create a Firebase project and enable Authentication
2. Create a MongoDB database (local or MongoDB Atlas)
3. Copy the `.env.example` files in each app directory to `.env` and update the values

### Installation

```bash
# Install dependencies
pnpm install

# Build all packages
pnpm build

# Start development server
pnpm dev
```

### Firebase Configuration

For authentication to work, you need to configure Firebase:

1. Create a Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. Enable Email/Password and Google authentication methods
3. Add a web app to your Firebase project
4. Copy the Firebase configuration to the `.env` file in the demo-saas app:

```
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_APP_ID=your-app-id
```

## Development

### Working on a Package

```bash
# Build a specific package
pnpm --filter @aizako/core-lib build

# Run tests for a specific package
pnpm --filter @aizako/crm test

# Start development mode for a specific package
pnpm --filter @aizako/ui-kit dev
```

### Working on an App

```bash
# Start the demo-saas app
pnpm --filter demo-saas dev

# Build the CLI app
pnpm --filter @aizako/cli build
```

### MongoDB Setup

The CRM module uses MongoDB for data storage. You can use a local MongoDB instance or MongoDB Atlas.

#### Local MongoDB

1. Install MongoDB locally
2. Start MongoDB server
3. Update the `.env` file with your MongoDB connection string:

```
MONGODB_URI=mongodb://localhost:27017/aizako
```

#### MongoDB Atlas

1. Create a MongoDB Atlas account at [https://www.mongodb.com/cloud/atlas](https://www.mongodb.com/cloud/atlas)
2. Create a new cluster
3. Create a database user
4. Get your connection string
5. Update the `.env` file with your MongoDB Atlas connection string:

```
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/aizako?retryWrites=true&w=majority
```

## Testing

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm --filter @aizako/crm test:coverage
```

## Features

### Core Library

- Firebase authentication
- Multi-tenant infrastructure
- MongoDB utilities
- Shared types and interfaces

### CRM Module

- Contact management
- Company management
- Opportunity management
- Activity tracking
- Email tracking
- Proposal generation
- AI-powered insights
- Graph-based lead scoring

### Demo SaaS App

- Authentication (login, register)
- Dashboard
- Tenant management
- Module integration

## License

Copyright (c) 2024 Aizako. All rights reserved.
