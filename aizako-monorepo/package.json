{"name": "aizako-monorepo", "version": "0.0.1", "private": true, "description": "Aizako Modular SaaS Monorepo", "type": "module", "scripts": {"dev": "pnpm --filter demo-saas dev", "build": "pnpm --filter \"*\" build", "test": "pnpm --filter \"*\" test", "lint": "pnpm --filter \"*\" lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "keywords": ["crm", "saas", "monorepo"], "author": "Aizako", "license": "UNLICENSED", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.4", "devDependencies": {"@changesets/cli": "^2.27.1", "@types/dompurify": "^3.2.0", "@types/mongodb-memory-server": "^2.3.0", "@types/supertest": "^6.0.3", "listr2": "^8.3.2", "ora": "^8.2.0", "prettier": "^3.2.5", "rate-limiter-flexible": "^7.1.0", "tsup": "^8.4.0", "turbo": "^2.0.0", "typescript": "^5.3.3"}}