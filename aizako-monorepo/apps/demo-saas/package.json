{"name": "demo-saas", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@aizako/core-lib": "workspace:*", "@aizako/ui-kit": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3"}, "devDependencies": {"@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.3.3", "vite": "^5.4.14", "vitest": "^1.3.1"}}