# Aizako Demo SaaS

This is a demo application that showcases the integration of Aizako's modular SaaS platform, including CRM, Scribe, Invoicing, and Onboarding modules.

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm 8+

### Installation

1. Clone the repository
2. Install dependencies:

```bash
pnpm install
```

3. Copy the `.env.example` file to `.env` and update the values:

```bash
cp .env.example .env
```

4. Start the development server:

```bash
pnpm dev
```

## Features

- Authentication with Firebase
- Tenant management
- Module integration
- Responsive UI with Tailwind CSS

## Available Scripts

- `pnpm dev` - Start the development server
- `pnpm build` - Build the application for production
- `pnpm preview` - Preview the production build
- `pnpm lint` - Lint the codebase
- `pnpm test` - Run tests
- `pnpm test:coverage` - Run tests with coverage

## License

Copyright (c) 2024 Aizako. All rights reserved.
