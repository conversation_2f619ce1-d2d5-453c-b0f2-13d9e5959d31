{"name": "@aizako/cli", "version": "0.0.1", "private": true, "type": "module", "bin": {"aizako": "./dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"commander": "^12.0.0", "inquirer": "^9.2.15", "chalk": "^5.3.0", "ora": "^8.0.1", "execa": "^8.0.1", "listr2": "^8.0.2", "conf": "^12.0.0"}, "devDependencies": {"@types/inquirer": "^9.0.7", "@types/node": "^20.16.11", "eslint": "^8.57.0", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.3.1"}}