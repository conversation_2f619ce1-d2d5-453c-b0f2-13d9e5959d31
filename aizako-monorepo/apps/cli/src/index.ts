#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { installCommand } from './commands/install';
import { infoCommand } from './commands/info';

const program = new Command();

// Set up the CLI
program
  .name('aizako')
  .description('Aizako CLI for managing modules')
  .version('0.0.1');

// Add commands
program
  .command('install')
  .description('Install Aizako modules')
  .argument('[modules...]', 'Modules to install (crm, scribe, invoicing, onboarding)')
  .option('-f, --force', 'Force installation even if module is already installed')
  .action(installCommand);

program
  .command('info')
  .description('Display information about installed modules')
  .action(infoCommand);

// Add more commands here

// Handle errors
program.exitOverride();

try {
  // Parse command line arguments
  program.parse(process.argv);
} catch (err) {
  console.error(chalk.red('Error:'), err.message);
  process.exit(1);
}
