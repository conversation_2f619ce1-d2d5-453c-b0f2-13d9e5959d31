# Aizako CLI

The Aizako CLI is a command-line tool for managing Aizako modules. It allows you to install, configure, and manage modules in your Aizako deployment.

## Installation

### From npm

```bash
npm install -g @aizako/cli
```

### From source

```bash
git clone https://github.com/Jpkay/aizako-crm.git
cd aizako-monorepo
pnpm install
pnpm --filter @aizako/cli build
pnpm --filter @aizako/cli link
```

## Usage

### Install modules

```bash
aizako install <modules...>
```

Install one or more Aizako modules. Available modules:

- `crm`: Customer Relationship Management module
- `scribe`: Content Management module
- `invoicing`: Invoicing module
- `onboarding`: Customer Onboarding module

Example:

```bash
aizako install crm scribe
```

Options:

- `-f, --force`: Force installation even if the module is already installed

### Get information about installed modules

```bash
aizako info
```

Display information about installed modules, including their version, status, and dependencies.

## Development

### Project Structure

```
/apps/cli
  ├─ src
  │   ├─ commands      (Command implementations)
  │   ├─ utils         (Utility functions)
  │   └─ index.ts      (Entry point)
  ├─ package.json
  ├─ tsconfig.json
  └─ tsup.config.ts
```

### Commands

Commands are implemented in the `src/commands` directory. Each command is a function that takes arguments and options and performs an action.

#### Adding a new command

1. Create a new file in the `src/commands` directory, e.g., `src/commands/my-command.ts`:

```typescript
export interface MyCommandOptions {
  option1?: boolean;
  option2?: string;
}

export async function myCommand(args: string[], options: MyCommandOptions): Promise<void> {
  // Implement your command here
}
```

2. Add the command to the `src/index.ts` file:

```typescript
import { myCommand } from './commands/my-command';

// ...

program
  .command('my-command')
  .description('Description of my command')
  .argument('[arg]', 'Description of the argument')
  .option('-o, --option1', 'Description of option1')
  .option('-p, --option2 <value>', 'Description of option2')
  .action(myCommand);
```

### Building

```bash
pnpm build
```

This will build the CLI using tsup and output the result to the `dist` directory.

### Testing

```bash
pnpm test
```

This will run the tests using Vitest.

## License

Copyright (c) 2024 Aizako. All rights reserved.
