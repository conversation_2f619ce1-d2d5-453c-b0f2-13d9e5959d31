import axios from 'axios';
import { mongoApiClient } from './mongo-api-client';

// Create axios instance
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to include credentials
api.interceptors.request.use(config => {
  config.withCredentials = true;
  return config;
});

/**
 * PostgreSQL-specific API client
 */
const postgresApiClient = {
  // Auth endpoints
  auth: {
    register: async (userData: any) => {
      const response = await api.post('/auth/register', userData);
      return response.data;
    },
    
    login: async (username: string, password: string) => {
      const response = await api.post('/auth/login', { username, password });
      return response.data;
    },
    
    logout: async () => {
      const response = await api.post('/auth/logout');
      return response.data;
    },
    
    me: async () => {
      const response = await api.get('/auth/me');
      return response.data;
    }
  },
  
  // Contact endpoints
  contacts: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/contacts', { params });
      return response.data;
    },
    
    getById: async (id: number) => {
      const response = await api.get(`/contacts/${id}`);
      return response.data;
    },
    
    create: async (contactData: any) => {
      const response = await api.post('/contacts', contactData);
      return response.data;
    },
    
    update: async (id: number, contactData: any) => {
      const response = await api.patch(`/contacts/${id}`, contactData);
      return response.data;
    },
    
    delete: async (id: number) => {
      const response = await api.delete(`/contacts/${id}`);
      return response.data;
    },
    
    enrich: async (id: number) => {
      const response = await api.post(`/ai/enrich-contact/${id}`);
      return response.data;
    }
  },
  
  // Company endpoints
  companies: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/companies', { params });
      return response.data;
    },
    
    getById: async (id: number) => {
      const response = await api.get(`/companies/${id}`);
      return response.data;
    },
    
    create: async (companyData: any) => {
      const response = await api.post('/companies', companyData);
      return response.data;
    },
    
    update: async (id: number, companyData: any) => {
      const response = await api.patch(`/companies/${id}`, companyData);
      return response.data;
    },
    
    delete: async (id: number) => {
      const response = await api.delete(`/companies/${id}`);
      return response.data;
    }
  },
  
  // Opportunity endpoints
  opportunities: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/opportunities', { params });
      return response.data;
    },
    
    getById: async (id: number) => {
      const response = await api.get(`/opportunities/${id}`);
      return response.data;
    },
    
    create: async (opportunityData: any) => {
      const response = await api.post('/opportunities', opportunityData);
      return response.data;
    },
    
    update: async (id: number, opportunityData: any) => {
      const response = await api.patch(`/opportunities/${id}`, opportunityData);
      return response.data;
    },
    
    delete: async (id: number) => {
      const response = await api.delete(`/opportunities/${id}`);
      return response.data;
    }
  },
  
  // Activity endpoints
  activities: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/activities', { params });
      return response.data;
    },
    
    create: async (activityData: any) => {
      const response = await api.post('/activities', activityData);
      return response.data;
    }
  },
  
  // Relationship endpoints
  relationships: {
    getAll: async () => {
      const response = await api.get('/relationships');
      return response.data;
    },
    
    create: async (relationshipData: any) => {
      const response = await api.post('/relationships', relationshipData);
      return response.data;
    }
  },
  
  // AI endpoints
  ai: {
    chat: async (message: string, context?: any) => {
      const response = await api.post('/ai/chat', { message, context });
      return response.data;
    },
    
    history: async () => {
      const response = await api.get('/ai/history');
      return response.data;
    },
    
    insights: async () => {
      const response = await api.get('/copilot/insights');
      return response.data;
    }
  },
  
  // Dashboard endpoints
  dashboard: {
    metrics: async () => {
      const response = await api.get('/dashboard/metrics');
      return response.data;
    }
  },
  
  // User endpoints
  user: {
    preferences: async () => {
      const response = await api.get('/user/preferences');
      return response.data;
    },
    
    updatePreferences: async (preferences: any) => {
      const response = await api.post('/user/preferences', { preferences });
      return response.data;
    }
  },
  
  // Insight endpoints
  insights: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/insights', { params });
      return response.data;
    },
    
    markAsRead: async (id: number) => {
      const response = await api.patch(`/insights/${id}/read`);
      return response.data;
    }
  },
  
  // System endpoints
  system: {
    status: async () => {
      const response = await api.get('/status');
      return response.data;
    },
    
    mongodbTest: async () => {
      const response = await api.get('/mongodb/test');
      return response.data;
    }
  }
};

/**
 * Check if MongoDB is enabled
 */
async function isMongoDBEnabled(): Promise<boolean> {
  try {
    const response = await api.get('/status');
    return response.data?.api?.mongodb === true;
  } catch (error) {
    console.error('Error checking MongoDB status:', error);
    return false;
  }
}

/**
 * Get the appropriate API client based on the database type
 */
export async function getApiClient() {
  const useMongoDb = await isMongoDBEnabled();
  
  if (useMongoDb) {
    console.log('Using MongoDB API client');
    return mongoApiClient;
  } else {
    console.log('Using PostgreSQL API client');
    return postgresApiClient;
  }
}

/**
 * Default export for backward compatibility
 */
export default postgresApiClient;
