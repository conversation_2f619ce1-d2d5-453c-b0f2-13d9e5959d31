import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to include credentials
api.interceptors.request.use(config => {
  config.withCredentials = true;
  return config;
});

/**
 * MongoDB-specific API client
 */
export const mongoApiClient = {
  // Auth endpoints
  auth: {
    register: async (userData: any) => {
      const response = await api.post('/auth/register', userData);
      return response.data;
    },

    login: async (username: string, password: string) => {
      const response = await api.post('/auth/login', { username, password });
      return response.data;
    },

    logout: async () => {
      const response = await api.post('/auth/logout');
      return response.data;
    },

    me: async () => {
      const response = await api.get('/auth/me');
      return response.data;
    },

    generateApiKey: async () => {
      const response = await api.post('/auth/api-key');
      return response.data;
    },

    revokeApiKey: async (apiKey: string) => {
      const response = await api.delete(`/auth/api-key/${apiKey}`);
      return response.data;
    }
  },

  // Contact endpoints
  contacts: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/contacts', { params });
      return response.data;
    },

    getById: async (id: string) => {
      const response = await api.get(`/contacts/${id}`);
      return response.data;
    },

    create: async (contactData: any) => {
      const response = await api.post('/contacts', contactData);
      return response.data;
    },

    update: async (id: string, contactData: any) => {
      const response = await api.patch(`/contacts/${id}`, contactData);
      return response.data;
    },

    delete: async (id: string) => {
      const response = await api.delete(`/contacts/${id}`);
      return response.data;
    },

    enrich: async (id: string) => {
      const response = await api.post(`/ai/enrich-contact/${id}`);
      return response.data;
    }
  },

  // Company endpoints
  companies: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/companies', { params });
      return response.data;
    },

    getById: async (id: string) => {
      const response = await api.get(`/companies/${id}`);
      return response.data;
    },

    create: async (companyData: any) => {
      const response = await api.post('/companies', companyData);
      return response.data;
    },

    update: async (id: string, companyData: any) => {
      const response = await api.patch(`/companies/${id}`, companyData);
      return response.data;
    },

    delete: async (id: string) => {
      const response = await api.delete(`/companies/${id}`);
      return response.data;
    }
  },

  // Opportunity endpoints
  opportunities: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/opportunities', { params });
      return response.data;
    },

    getById: async (id: string) => {
      const response = await api.get(`/opportunities/${id}`);
      return response.data;
    },

    create: async (opportunityData: any) => {
      const response = await api.post('/opportunities', opportunityData);
      return response.data;
    },

    update: async (id: string, opportunityData: any) => {
      const response = await api.patch(`/opportunities/${id}`, opportunityData);
      return response.data;
    },

    delete: async (id: string) => {
      const response = await api.delete(`/opportunities/${id}`);
      return response.data;
    },

    getDealBrief: async (id: string) => {
      const response = await api.get(`/opportunities/${id}/deal-brief`);
      return response.data;
    },

    getStageAnalysis: async (id: string) => {
      const response = await api.get(`/opportunities/${id}/stage-analysis`);
      return response.data;
    },

    getStageTransitions: async (id: string) => {
      const response = await api.get(`/opportunities/${id}/stage-transitions`);
      return response.data;
    }
  },

  // Stage transition endpoints
  stageTransitions: {
    applyTransition: async (id: string, approved: boolean) => {
      const response = await api.post(`/stage-transitions/${id}/apply`, { approved });
      return response.data;
    },

    analyzeAll: async (autoApply: boolean = false) => {
      const response = await api.post('/stage-transitions/analyze-all', { autoApply });
      return response.data;
    }
  },

  // Activity endpoints
  activities: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/activities', { params });
      return response.data;
    },

    create: async (activityData: any) => {
      const response = await api.post('/activities', activityData);
      return response.data;
    }
  },

  // Relationship endpoints
  relationships: {
    getAll: async () => {
      const response = await api.get('/relationships');
      return response.data;
    },

    create: async (relationshipData: any) => {
      const response = await api.post('/relationships', relationshipData);
      return response.data;
    }
  },

  // AI endpoints
  ai: {
    chat: async (message: string, context?: any) => {
      const response = await api.post('/ai/chat', { message, context });
      return response.data;
    },

    history: async () => {
      const response = await api.get('/ai/history');
      return response.data;
    },

    insights: async () => {
      const response = await api.get('/copilot/insights');
      return response.data;
    }
  },

  // Dashboard endpoints
  dashboard: {
    metrics: async () => {
      const response = await api.get('/dashboard/metrics');
      return response.data;
    }
  },

  // User endpoints
  user: {
    preferences: async () => {
      const response = await api.get('/user/preferences');
      return response.data;
    },

    updatePreferences: async (preferences: any) => {
      const response = await api.post('/user/preferences', { preferences });
      return response.data;
    }
  },

  // Insight endpoints
  insights: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/insights', { params });
      return response.data;
    },

    markAsRead: async (id: string) => {
      const response = await api.patch(`/insights/${id}/read`);
      return response.data;
    }
  },

  // Pipeline insights endpoints
  pipeline: {
    getInsights: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/pipeline/insights', { params });
      return response.data;
    },

    getOpportunityInsights: async (opportunityId: string) => {
      const response = await api.get(`/pipeline/insights/opportunity/${opportunityId}`);
      return response.data;
    },

    runChecks: async (checkType?: 'all' | 'stalled' | 'meetings') => {
      const response = await api.post('/pipeline/check', {}, {
        params: { type: checkType || 'all' }
      });
      return response.data;
    }
  },

  // Document endpoints
  documents: {
    getAll: async (params?: { limit?: number, offset?: number }) => {
      const response = await api.get('/documents', { params });
      return response.data;
    },

    getById: async (id: string) => {
      const response = await api.get(`/documents/${id}`);
      return response.data;
    },

    getByEntity: async (entityType: string, entityId: string) => {
      const response = await api.get(`/documents/entity/${entityType}/${entityId}`);
      return response.data;
    },

    create: async (formData: FormData) => {
      const response = await api.post('/documents', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    },

    update: async (id: string, documentData: any) => {
      const response = await api.patch(`/documents/${id}`, documentData);
      return response.data;
    },

    delete: async (id: string) => {
      const response = await api.delete(`/documents/${id}`);
      return response.data;
    },

    download: async (id: string) => {
      const response = await api.get(`/documents/${id}/content`, {
        responseType: 'blob'
      });
      return response.data;
    },

    search: async (query: string) => {
      const response = await api.get(`/documents/search/${query}`);
      return response.data;
    }
  },

  // Subscription endpoints
  subscription: {
    plans: {
      getAll: async () => {
        const response = await api.get('/subscription/plans');
        return response.data;
      },

      getById: async (id: string) => {
        const response = await api.get(`/subscription/plans/${id}`);
        return response.data;
      }
    },

    features: {
      getAll: async () => {
        const response = await api.get('/subscription/features');
        return response.data;
      }
    },

    tenant: {
      getCurrent: async () => {
        const response = await api.get('/subscription/tenant/current');
        return response.data;
      },

      getSubscription: async () => {
        const response = await api.get('/subscription/tenant/subscription');
        return response.data;
      },

      getUsage: async () => {
        const response = await api.get('/subscription/tenant/usage');
        return response.data;
      }
    }
  },

  // System endpoints
  system: {
    status: async () => {
      const response = await api.get('/status');
      return response.data;
    },

    mongodbTest: async () => {
      const response = await api.get('/mongodb/test');
      return response.data;
    }
  }
};
