import { IEmailTemplate } from '../types/email-template';
import { EmailTemplateModel } from '../models/email-template';

/**
 * Service for managing email templates
 */
export class EmailTemplateService {
  /**
   * Create a new email template
   * @param template Email template data
   * @param tenantId Tenant ID
   * @returns Created email template
   */
  static async createEmailTemplate(template: Omit<IEmailTemplate, '_id'>, tenantId: string): Promise<IEmailTemplate> {
    const newTemplate = new EmailTemplateModel({
      ...template,
      tenantId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newTemplate.save();
    return newTemplate.toObject();
  }

  /**
   * Get email templates with pagination and filtering
   * @param options Pagination and filtering options
   * @param tenantId Tenant ID
   * @returns Email templates and total count
   */
  static async getEmailTemplates(
    options: {
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: 'asc' | 'desc';
      category?: string;
      search?: string;
    },
    tenantId: string
  ): Promise<{ templates: IEmailTemplate[]; total: number }> {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      category,
      search,
    } = options;

    const query: any = { tenantId };

    if (category) query.category = category;

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { subject: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [templates, total] = await Promise.all([
      EmailTemplateModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      EmailTemplateModel.countDocuments(query),
    ]);

    return { templates, total };
  }

  /**
   * Get email template by ID
   * @param id Email template ID
   * @param tenantId Tenant ID
   * @returns Email template or null if not found
   */
  static async getEmailTemplateById(id: string, tenantId: string): Promise<IEmailTemplate | null> {
    const template = await EmailTemplateModel.findOne({ _id: id, tenantId }).lean();
    return template;
  }

  /**
   * Update email template
   * @param id Email template ID
   * @param templateData Email template data to update
   * @param tenantId Tenant ID
   * @returns Updated email template or null if not found
   */
  static async updateEmailTemplate(
    id: string,
    templateData: Partial<IEmailTemplate>,
    tenantId: string
  ): Promise<IEmailTemplate | null> {
    const template = await EmailTemplateModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        ...templateData,
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return template;
  }

  /**
   * Delete email template
   * @param id Email template ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteEmailTemplate(id: string, tenantId: string): Promise<boolean> {
    const result = await EmailTemplateModel.deleteOne({ _id: id, tenantId });
    return result.deletedCount > 0;
  }
}
