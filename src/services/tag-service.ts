import { ITag } from '../types/tag';
import { TagModel } from '../models/tag';

/**
 * Service for managing tags
 */
export class TagService {
  /**
   * Create a new tag
   * @param tag Tag data
   * @param tenantId Tenant ID
   * @returns Created tag
   */
  static async createTag(tag: Omit<ITag, '_id'>, tenantId: string): Promise<ITag> {
    const newTag = new TagModel({
      ...tag,
      tenantId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newTag.save();
    return newTag.toObject();
  }

  /**
   * Get tags with pagination and filtering
   * @param options Pagination and filtering options
   * @param tenantId Tenant ID
   * @returns Tags and total count
   */
  static async getTags(
    options: {
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: 'asc' | 'desc';
      category?: string;
      search?: string;
    },
    tenantId: string
  ): Promise<{ tags: ITag[]; total: number }> {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      category,
      search,
    } = options;

    const query: any = { tenantId };

    if (category) query.category = category;

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [tags, total] = await Promise.all([
      TagModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      TagModel.countDocuments(query),
    ]);

    return { tags, total };
  }

  /**
   * Get tag by ID
   * @param id Tag ID
   * @param tenantId Tenant ID
   * @returns Tag or null if not found
   */
  static async getTagById(id: string, tenantId: string): Promise<ITag | null> {
    const tag = await TagModel.findOne({ _id: id, tenantId }).lean();
    return tag;
  }

  /**
   * Update tag
   * @param id Tag ID
   * @param tagData Tag data to update
   * @param tenantId Tenant ID
   * @returns Updated tag or null if not found
   */
  static async updateTag(
    id: string,
    tagData: Partial<ITag>,
    tenantId: string
  ): Promise<ITag | null> {
    const tag = await TagModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        ...tagData,
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return tag;
  }

  /**
   * Delete tag
   * @param id Tag ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteTag(id: string, tenantId: string): Promise<boolean> {
    const result = await TagModel.deleteOne({ _id: id, tenantId });
    return result.deletedCount > 0;
  }
}
