import { IActivity } from '../types/activity';
import { ActivityModel } from '../models/activity';

/**
 * Service for managing activities
 */
export class ActivityService {
  /**
   * Create a new activity
   * @param activity Activity data
   * @param tenantId Tenant ID
   * @returns Created activity
   */
  static async createActivity(activity: Omit<IActivity, '_id'>, tenantId: string): Promise<IActivity> {
    const newActivity = new ActivityModel({
      ...activity,
      tenantId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newActivity.save();
    return newActivity.toObject();
  }

  /**
   * Get activities with pagination and filtering
   * @param options Pagination and filtering options
   * @param tenantId Tenant ID
   * @returns Activities and total count
   */
  static async getActivities(
    options: {
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: 'asc' | 'desc';
      contactId?: string;
      companyId?: string;
      opportunityId?: string;
      type?: string;
      startDate?: Date;
      endDate?: Date;
    },
    tenantId: string
  ): Promise<{ activities: IActivity[]; total: number }> {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      contactId,
      companyId,
      opportunityId,
      type,
      startDate,
      endDate,
    } = options;

    const query: any = { tenantId };

    if (contactId) query.contactId = contactId;
    if (companyId) query.companyId = companyId;
    if (opportunityId) query.opportunityId = opportunityId;
    if (type) query.type = type;

    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = startDate;
      if (endDate) query.createdAt.$lte = endDate;
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [activities, total] = await Promise.all([
      ActivityModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      ActivityModel.countDocuments(query),
    ]);

    return { activities, total };
  }

  /**
   * Get activity by ID
   * @param id Activity ID
   * @param tenantId Tenant ID
   * @returns Activity or null if not found
   */
  static async getActivityById(id: string, tenantId: string): Promise<IActivity | null> {
    const activity = await ActivityModel.findOne({ _id: id, tenantId }).lean();
    return activity;
  }

  /**
   * Update activity
   * @param id Activity ID
   * @param activityData Activity data to update
   * @param tenantId Tenant ID
   * @returns Updated activity or null if not found
   */
  static async updateActivity(
    id: string,
    activityData: Partial<IActivity>,
    tenantId: string
  ): Promise<IActivity | null> {
    const activity = await ActivityModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        ...activityData,
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return activity;
  }

  /**
   * Delete activity
   * @param id Activity ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteActivity(id: string, tenantId: string): Promise<boolean> {
    const result = await ActivityModel.deleteOne({ _id: id, tenantId });
    return result.deletedCount > 0;
  }
}
