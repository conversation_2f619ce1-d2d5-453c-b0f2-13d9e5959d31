import { ITenantDomain } from '../types/tenant-domain';
import { TenantDomainModel } from '../models/tenant-domain';

/**
 * Service for managing tenant domains
 */
export class TenantDomainService {
  /**
   * Create a new tenant domain
   * @param domain Tenant domain data
   * @param tenantId Tenant ID
   * @returns Created tenant domain
   */
  static async createTenantDomain(domain: Omit<ITenantDomain, '_id'>, tenantId: string): Promise<ITenantDomain> {
    const newDomain = new TenantDomainModel({
      ...domain,
      tenantId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newDomain.save();
    return newDomain.toObject();
  }

  /**
   * Get tenant domains
   * @param tenantId Tenant ID
   * @returns Tenant domains
   */
  static async getTenantDomains(tenantId: string): Promise<ITenantDomain[]> {
    const domains = await TenantDomainModel.find({ tenantId }).lean();
    return domains;
  }

  /**
   * Get tenant domain by ID
   * @param id Tenant domain ID
   * @param tenantId Tenant ID
   * @returns Tenant domain or null if not found
   */
  static async getTenantDomainById(id: string, tenantId: string): Promise<ITenantDomain | null> {
    const domain = await TenantDomainModel.findOne({ _id: id, tenantId }).lean();
    return domain;
  }

  /**
   * Get tenant domain by domain name
   * @param domainName Domain name
   * @returns Tenant domain or null if not found
   */
  static async getTenantDomainByName(domainName: string): Promise<ITenantDomain | null> {
    const domain = await TenantDomainModel.findOne({ domainName }).lean();
    return domain;
  }

  /**
   * Update tenant domain
   * @param id Tenant domain ID
   * @param domainData Tenant domain data to update
   * @param tenantId Tenant ID
   * @returns Updated tenant domain or null if not found
   */
  static async updateTenantDomain(
    id: string,
    domainData: Partial<ITenantDomain>,
    tenantId: string
  ): Promise<ITenantDomain | null> {
    const domain = await TenantDomainModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        ...domainData,
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return domain;
  }

  /**
   * Delete tenant domain
   * @param id Tenant domain ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteTenantDomain(id: string, tenantId: string): Promise<boolean> {
    const result = await TenantDomainModel.deleteOne({ _id: id, tenantId });
    return result.deletedCount > 0;
  }

  /**
   * Verify tenant domain
   * @param id Tenant domain ID
   * @param tenantId Tenant ID
   * @returns Updated tenant domain or null if not found
   */
  static async verifyTenantDomain(id: string, tenantId: string): Promise<ITenantDomain | null> {
    const domain = await TenantDomainModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        verified: true,
        verifiedAt: new Date(),
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return domain;
  }
}
