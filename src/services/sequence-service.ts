import { ISequence, ISequenceStep, ISequenceEnrollment } from '../types/sequence';
import { SequenceModel, SequenceEnrollmentModel } from '../models/sequence';

/**
 * Service for managing email sequences
 */
export class SequenceService {
  /**
   * Create a new sequence
   * @param sequence Sequence data
   * @param tenantId Tenant ID
   * @returns Created sequence
   */
  static async createSequence(sequence: Omit<ISequence, '_id'>, tenantId: string): Promise<ISequence> {
    const newSequence = new SequenceModel({
      ...sequence,
      tenantId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newSequence.save();
    return newSequence.toObject();
  }

  /**
   * Get sequences with pagination and filtering
   * @param options Pagination and filtering options
   * @param tenantId Tenant ID
   * @returns Sequences and total count
   */
  static async getSequences(
    options: {
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: 'asc' | 'desc';
      status?: 'active' | 'paused' | 'draft';
      search?: string;
    },
    tenantId: string
  ): Promise<{ sequences: ISequence[]; total: number }> {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      status,
      search,
    } = options;

    const query: any = { tenantId };

    if (status) query.status = status;

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [sequences, total] = await Promise.all([
      SequenceModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      SequenceModel.countDocuments(query),
    ]);

    return { sequences, total };
  }

  /**
   * Get sequence by ID
   * @param id Sequence ID
   * @param tenantId Tenant ID
   * @returns Sequence or null if not found
   */
  static async getSequenceById(id: string, tenantId: string): Promise<ISequence | null> {
    const sequence = await SequenceModel.findOne({ _id: id, tenantId }).lean();
    return sequence;
  }

  /**
   * Update sequence
   * @param id Sequence ID
   * @param sequenceData Sequence data to update
   * @param tenantId Tenant ID
   * @returns Updated sequence or null if not found
   */
  static async updateSequence(
    id: string,
    sequenceData: Partial<ISequence>,
    tenantId: string
  ): Promise<ISequence | null> {
    const sequence = await SequenceModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        ...sequenceData,
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return sequence;
  }

  /**
   * Delete sequence
   * @param id Sequence ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteSequence(id: string, tenantId: string): Promise<boolean> {
    const result = await SequenceModel.deleteOne({ _id: id, tenantId });
    return result.deletedCount > 0;
  }

  /**
   * Enroll contacts in a sequence
   * @param sequenceId Sequence ID
   * @param contactIds Contact IDs to enroll
   * @param userId User ID who initiated the enrollment
   * @param tenantId Tenant ID
   * @returns Number of contacts enrolled
   */
  static async enrollContacts(
    sequenceId: string,
    contactIds: string[],
    userId: string,
    tenantId: string
  ): Promise<number> {
    // Get the sequence
    const sequence = await SequenceModel.findOne({ _id: sequenceId, tenantId });
    
    if (!sequence) {
      throw new Error('Sequence not found');
    }

    // Create enrollment records
    const enrollments = contactIds.map(contactId => ({
      sequenceId,
      contactId,
      tenantId,
      userId,
      status: 'active',
      currentStepIndex: 0,
      nextStepDate: new Date(), // Start immediately
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    const result = await SequenceEnrollmentModel.insertMany(enrollments);
    return result.length;
  }
}
