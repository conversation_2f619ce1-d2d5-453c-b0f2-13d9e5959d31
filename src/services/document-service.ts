import { IDocument } from '../types/document';
import { DocumentModel } from '../models/document';

/**
 * Service for managing documents
 */
export class DocumentService {
  /**
   * Create a new document
   * @param document Document data
   * @param tenantId Tenant ID
   * @returns Created document
   */
  static async createDocument(document: Omit<IDocument, '_id'>, tenantId: string): Promise<IDocument> {
    const newDocument = new DocumentModel({
      ...document,
      tenantId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newDocument.save();
    return newDocument.toObject();
  }

  /**
   * Get documents with pagination and filtering
   * @param options Pagination and filtering options
   * @param tenantId Tenant ID
   * @returns Documents and total count
   */
  static async getDocuments(
    options: {
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: 'asc' | 'desc';
      contactId?: string;
      companyId?: string;
      opportunityId?: string;
      type?: string;
      search?: string;
    },
    tenantId: string
  ): Promise<{ documents: IDocument[]; total: number }> {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      contactId,
      companyId,
      opportunityId,
      type,
      search,
    } = options;

    const query: any = { tenantId };

    if (contactId) query.contactId = contactId;
    if (companyId) query.companyId = companyId;
    if (opportunityId) query.opportunityId = opportunityId;
    if (type) query.type = type;

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [documents, total] = await Promise.all([
      DocumentModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      DocumentModel.countDocuments(query),
    ]);

    return { documents, total };
  }

  /**
   * Get document by ID
   * @param id Document ID
   * @param tenantId Tenant ID
   * @returns Document or null if not found
   */
  static async getDocumentById(id: string, tenantId: string): Promise<IDocument | null> {
    const document = await DocumentModel.findOne({ _id: id, tenantId }).lean();
    return document;
  }

  /**
   * Update document
   * @param id Document ID
   * @param documentData Document data to update
   * @param tenantId Tenant ID
   * @returns Updated document or null if not found
   */
  static async updateDocument(
    id: string,
    documentData: Partial<IDocument>,
    tenantId: string
  ): Promise<IDocument | null> {
    const document = await DocumentModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        ...documentData,
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return document;
  }

  /**
   * Delete document
   * @param id Document ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteDocument(id: string, tenantId: string): Promise<boolean> {
    const result = await DocumentModel.deleteOne({ _id: id, tenantId });
    return result.deletedCount > 0;
  }
}
