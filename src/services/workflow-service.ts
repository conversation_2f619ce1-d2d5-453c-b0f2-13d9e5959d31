import { IWorkflow, IWorkflowExecution } from '../types/workflow';
import { WorkflowModel, WorkflowExecutionModel } from '../models/workflow';

/**
 * Service for managing workflows
 */
export class WorkflowService {
  /**
   * Create a new workflow
   * @param workflow Workflow data
   * @param tenantId Tenant ID
   * @returns Created workflow
   */
  static async createWorkflow(workflow: Omit<IWorkflow, '_id'>, tenantId: string): Promise<IWorkflow> {
    const newWorkflow = new WorkflowModel({
      ...workflow,
      tenantId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newWorkflow.save();
    return newWorkflow.toObject();
  }

  /**
   * Get workflows with pagination and filtering
   * @param options Pagination and filtering options
   * @param tenantId Tenant ID
   * @returns Workflows and total count
   */
  static async getWorkflows(
    options: {
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: 'asc' | 'desc';
      status?: 'active' | 'paused' | 'draft';
      search?: string;
    },
    tenantId: string
  ): Promise<{ workflows: IWorkflow[]; total: number }> {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      status,
      search,
    } = options;

    const query: any = { tenantId };

    if (status) query.status = status;

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [workflows, total] = await Promise.all([
      WorkflowModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      WorkflowModel.countDocuments(query),
    ]);

    return { workflows, total };
  }

  /**
   * Get workflow by ID
   * @param id Workflow ID
   * @param tenantId Tenant ID
   * @returns Workflow or null if not found
   */
  static async getWorkflowById(id: string, tenantId: string): Promise<IWorkflow | null> {
    const workflow = await WorkflowModel.findOne({ _id: id, tenantId }).lean();
    return workflow;
  }

  /**
   * Update workflow
   * @param id Workflow ID
   * @param workflowData Workflow data to update
   * @param tenantId Tenant ID
   * @returns Updated workflow or null if not found
   */
  static async updateWorkflow(
    id: string,
    workflowData: Partial<IWorkflow>,
    tenantId: string
  ): Promise<IWorkflow | null> {
    const workflow = await WorkflowModel.findOneAndUpdate(
      { _id: id, tenantId },
      {
        ...workflowData,
        updatedAt: new Date(),
      },
      { new: true }
    ).lean();

    return workflow;
  }

  /**
   * Delete workflow
   * @param id Workflow ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  static async deleteWorkflow(id: string, tenantId: string): Promise<boolean> {
    const result = await WorkflowModel.deleteOne({ _id: id, tenantId });
    return result.deletedCount > 0;
  }

  /**
   * Execute a workflow
   * @param workflowId Workflow ID
   * @param triggerData Trigger data
   * @param tenantId Tenant ID
   * @returns Workflow execution
   */
  static async executeWorkflow(
    workflowId: string,
    triggerData: any,
    tenantId: string
  ): Promise<IWorkflowExecution> {
    // Get the workflow
    const workflow = await WorkflowModel.findOne({ _id: workflowId, tenantId });
    
    if (!workflow) {
      throw new Error('Workflow not found');
    }

    // Create execution record
    const execution = new WorkflowExecutionModel({
      workflowId,
      tenantId,
      status: 'running',
      triggerData,
      steps: [],
      startedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await execution.save();
    return execution.toObject();
  }
}
