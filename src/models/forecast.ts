import mongoose, { Schema, Document } from 'mongoose';
import { IForecast } from '../types/forecast';

// Define the schema
const ForecastSchema = new Schema<IForecast & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    period: {
      type: String,
      required: true,
      enum: ['monthly', 'quarterly', 'yearly'],
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    targetAmount: {
      type: Number,
      required: true,
    },
    forecastAmount: {
      type: Number,
      required: true,
    },
    committedAmount: {
      type: Number,
      required: true,
    },
    bestCaseAmount: {
      type: Number,
      required: true,
    },
    closedAmount: {
      type: Number,
      default: 0,
    },
    pipelineAmount: {
      type: Number,
      default: 0,
    },
    opportunityCount: {
      type: Number,
      default: 0,
    },
    closedOpportunityCount: {
      type: Number,
      default: 0,
    },
    notes: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
ForecastSchema.index({ tenantId: 1, period: 1, startDate: 1, endDate: 1 });
ForecastSchema.index({ tenantId: 1, userId: 1, period: 1 });

// Create the model
export const ForecastModel = mongoose.model<IForecast & Document>(
  'Forecast',
  ForecastSchema
);
