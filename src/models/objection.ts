import mongoose, { Schema, Document } from 'mongoose';
import { IObjection } from '../types/objection';

// Define the schema
const ObjectionSchema = new Schema<IObjection & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    opportunityId: {
      type: String,
      required: true,
      index: true,
    },
    contactId: {
      type: String,
      index: true,
    },
    objectionType: {
      type: String,
      required: true,
      enum: ['price', 'features', 'timing', 'competition', 'budget', 'authority', 'need', 'other'],
    },
    description: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      required: true,
      enum: ['open', 'addressed', 'resolved', 'unresolved'],
      default: 'open',
    },
    response: {
      type: String,
    },
    aiSuggestedResponse: {
      type: String,
    },
    outcome: {
      type: String,
      enum: ['positive', 'neutral', 'negative'],
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdBy: {
      type: String,
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
    resolvedAt: {
      type: Date,
    },
    resolvedBy: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
ObjectionSchema.index({ tenantId: 1, objectionType: 1 });
ObjectionSchema.index({ tenantId: 1, status: 1 });

// Create the model
export const ObjectionModel = mongoose.model<IObjection & Document>(
  'Objection',
  ObjectionSchema
);
