import mongoose, { Schema, Document } from 'mongoose';
import { IInsight } from '../types/insight';

// Define the schema
const InsightSchema = new Schema<IInsight & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    entityType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity', 'activity', 'global'],
    },
    entityId: {
      type: String,
      required: function() {
        return this.entityType !== 'global';
      },
    },
    insightType: {
      type: String,
      required: true,
      enum: [
        'recommendation',
        'trend',
        'anomaly',
        'prediction',
        'summary',
        'opportunity',
        'risk',
        'sentiment',
        'other',
      ],
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium',
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.7,
    },
    status: {
      type: String,
      enum: ['new', 'viewed', 'actioned', 'dismissed'],
      default: 'new',
    },
    actionTaken: {
      type: String,
    },
    actionTakenAt: {
      type: Date,
    },
    actionTakenBy: {
      type: String,
    },
    expiresAt: {
      type: Date,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
InsightSchema.index({ tenantId: 1, entityType: 1, entityId: 1 });
InsightSchema.index({ tenantId: 1, insightType: 1 });
InsightSchema.index({ tenantId: 1, priority: 1 });
InsightSchema.index({ tenantId: 1, status: 1 });
InsightSchema.index({ tenantId: 1, createdAt: 1 });

// Create the model
export const InsightModel = mongoose.model<IInsight & Document>(
  'Insight',
  InsightSchema
);
