import mongoose, { Schema, Document } from 'mongoose';
import { IRelationship } from '../types/relationship';

// Define the schema
const RelationshipSchema = new Schema<IRelationship & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    sourceType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity'],
    },
    sourceId: {
      type: String,
      required: true,
    },
    targetType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity'],
    },
    targetId: {
      type: String,
      required: true,
    },
    relationshipType: {
      type: String,
      required: true,
    },
    strength: {
      type: Number,
      default: 1,
    },
    notes: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create compound indexes
RelationshipSchema.index({ tenantId: 1, sourceType: 1, sourceId: 1 });
RelationshipSchema.index({ tenantId: 1, targetType: 1, targetId: 1 });
RelationshipSchema.index({ tenantId: 1, relationshipType: 1 });

// Create unique compound index to prevent duplicate relationships
RelationshipSchema.index(
  { tenantId: 1, sourceType: 1, sourceId: 1, targetType: 1, targetId: 1, relationshipType: 1 },
  { unique: true }
);

// Create the model
export const RelationshipModel = mongoose.model<IRelationship & Document>(
  'Relationship',
  RelationshipSchema
);
