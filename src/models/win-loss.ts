import mongoose, { Schema, Document } from 'mongoose';
import { IWinLossAnalysis } from '../types/win-loss';

// Define the schema
const WinLossAnalysisSchema = new Schema<IWinLossAnalysis & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    opportunityId: {
      type: String,
      required: true,
      index: true,
    },
    result: {
      type: String,
      required: true,
      enum: ['won', 'lost'],
    },
    closedDate: {
      type: Date,
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    primaryReasons: {
      type: [String],
      required: true,
    },
    secondaryReasons: {
      type: [String],
    },
    competitorName: {
      type: String,
    },
    competitorStrengths: {
      type: [String],
    },
    competitorWeaknesses: {
      type: [String],
    },
    decisionMakers: {
      type: [String],
    },
    feedbackSummary: {
      type: String,
    },
    lessonsLearned: {
      type: String,
    },
    nextSteps: {
      type: String,
    },
    aiAnalysis: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdBy: {
      type: String,
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
WinLossAnalysisSchema.index({ tenantId: 1, result: 1 });
WinLossAnalysisSchema.index({ tenantId: 1, closedDate: 1 });
WinLossAnalysisSchema.index({ tenantId: 1, primaryReasons: 1 });

// Create the model
export const WinLossAnalysisModel = mongoose.model<IWinLossAnalysis & Document>(
  'WinLossAnalysis',
  WinLossAnalysisSchema
);
