import mongoose, { Schema, Document } from 'mongoose';
import { IAnalyticsEvent } from '../types/analytics-event';

// Define the schema
const AnalyticsEventSchema = new Schema<IAnalyticsEvent & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    eventType: {
      type: String,
      required: true,
      index: true,
    },
    entityType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity', 'proposal', 'email', 'sequence', 'workflow', 'user', 'system'],
      index: true,
    },
    entityId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      index: true,
    },
    sessionId: {
      type: String,
      index: true,
    },
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
      index: true,
    },
    properties: {
      type: Schema.Types.Mixed,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: { createdAt: true, updatedAt: false },
  }
);

// Create compound indexes
AnalyticsEventSchema.index({ tenantId: 1, eventType: 1, timestamp: 1 });
AnalyticsEventSchema.index({ tenantId: 1, entityType: 1, entityId: 1, timestamp: 1 });
AnalyticsEventSchema.index({ tenantId: 1, userId: 1, timestamp: 1 });

// Create the model
export const AnalyticsEventModel = mongoose.model<IAnalyticsEvent & Document>(
  'AnalyticsEvent',
  AnalyticsEventSchema
);
