import mongoose, { Schema, Document } from 'mongoose';
import { IFollowUp } from '../types/follow-up';

// Define the schema
const FollowUpSchema = new Schema<IFollowUp & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    entityType: {
      type: String,
      required: true,
      enum: ['contact', 'company', 'opportunity', 'task'],
    },
    entityId: {
      type: String,
      required: true,
    },
    dueDate: {
      type: Date,
      required: true,
      index: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium',
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'cancelled'],
      default: 'pending',
    },
    completedAt: {
      type: Date,
    },
    completedBy: {
      type: String,
    },
    reminderSent: {
      type: Boolean,
      default: false,
    },
    reminderSentAt: {
      type: Date,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
FollowUpSchema.index({ tenantId: 1, userId: 1, dueDate: 1 });
FollowUpSchema.index({ tenantId: 1, entityType: 1, entityId: 1 });
FollowUpSchema.index({ tenantId: 1, status: 1 });

// Create the model
export const FollowUpModel = mongoose.model<IFollowUp & Document>(
  'FollowUp',
  FollowUpSchema
);
