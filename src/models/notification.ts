import mongoose, { Schema, Document } from 'mongoose';
import { INotification } from '../types/notification';

// Define the schema
const NotificationSchema = new Schema<INotification & Document>(
  {
    tenantId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    type: {
      type: String,
      required: true,
      enum: [
        'task',
        'mention',
        'follow_up',
        'opportunity_update',
        'contact_update',
        'company_update',
        'system',
        'insight',
        'other',
      ],
    },
    title: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    entityType: {
      type: String,
      enum: ['contact', 'company', 'opportunity', 'task', 'activity', 'insight', 'other'],
    },
    entityId: {
      type: String,
    },
    priority: {
      type: String,
      enum: ['low', 'normal', 'high'],
      default: 'normal',
    },
    read: {
      type: Boolean,
      default: false,
    },
    readAt: {
      type: Date,
    },
    actionUrl: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes
NotificationSchema.index({ tenantId: 1, userId: 1, read: 1 });
NotificationSchema.index({ tenantId: 1, userId: 1, type: 1 });
NotificationSchema.index({ tenantId: 1, userId: 1, createdAt: 1 });

// Create the model
export const NotificationModel = mongoose.model<INotification & Document>(
  'Notification',
  NotificationSchema
);
