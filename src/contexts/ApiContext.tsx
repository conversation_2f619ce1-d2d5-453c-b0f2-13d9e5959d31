import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getApiClient } from '../api/api-client';

// Create context
const ApiContext = createContext<any>(null);

// API context provider props
interface ApiProviderProps {
  children: ReactNode;
}

/**
 * API context provider
 */
export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const [apiClient, setApiClient] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function initializeApiClient() {
      try {
        const client = await getApiClient();
        setApiClient(client);
        setLoading(false);
      } catch (error) {
        console.error('Error initializing API client:', error);
        setError('Failed to initialize API client');
        setLoading(false);
      }
    }

    initializeApiClient();
  }, []);

  if (loading) {
    return <div>Loading API client...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <ApiContext.Provider value={apiClient}>
      {children}
    </ApiContext.Provider>
  );
};

/**
 * Hook to use the API client
 */
export const useApi = () => {
  const apiClient = useContext(ApiContext);
  
  if (!apiClient) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  
  return apiClient;
};
