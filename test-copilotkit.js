// Simple test script to verify CopilotKit integration for Aizako CRM
// Run with: node test-copilotkit.js

const fetch = require('node-fetch');

async function testCopilotKit() {
  console.log('Testing CopilotKit integration...');

  try {
    // Test a simple chat request
    const response = await fetch('http://localhost:5000/api/copilotkit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'Show me a summary of my contacts in Aizako CRM'
          }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('CopilotKit response:');
    console.log(JSON.stringify(data, null, 2));

    // Test a tool call
    const toolResponse = await fetch('http://localhost:5000/api/copilotkit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tool_name: 'get_contacts',
        tool_parameters: {}
      })
    });

    if (!toolResponse.ok) {
      throw new Error(`HTTP error! Status: ${toolResponse.status}`);
    }

    const toolData = await toolResponse.json();
    console.log('\nCopilotKit tool response:');
    console.log(JSON.stringify(toolData, null, 2));

    console.log('\nTests completed successfully!');
  } catch (error) {
    console.error('Error testing CopilotKit integration:', error);
  }
}

testCopilotKit();
