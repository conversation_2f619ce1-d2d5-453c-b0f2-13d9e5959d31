// React testing setup
import '@testing-library/jest-dom';

// Mock the API utils
jest.mock('../client/src/api/api-utils', () => ({
  apiRequest: jest.fn().mockResolvedValue({}),
}), { virtual: true });

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Loader2: () => <div data-testid="loader-icon" />,
  Sparkles: () => <div data-testid="sparkles-icon" />,
  Download: () => <div data-testid="download-icon" />,
  FileText: () => <div data-testid="file-text-icon" />,
  FileCode: () => <div data-testid="file-code-icon" />,
  Send: () => <div data-testid="send-icon" />,
  Search: () => <div data-testid="search-icon" />,
  Plus: () => <div data-testid="plus-icon" />,
  Filter: () => <div data-testid="filter-icon" />,
  Check: () => <div data-testid="check-icon" />,
  X: () => <div data-testid="x-icon" />,
  ExternalLink: () => <div data-testid="external-link-icon" />,
}), { virtual: true });

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  })),
  usePathname: jest.fn(() => '/'),
  useSearchParams: jest.fn(() => ({ get: jest.fn() })),
}), { virtual: true });

// Mock hooks
jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(() => ({
    toast: jest.fn(),
  })),
}), { virtual: true });

// Set up global fetch mock
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
    ok: true,
    status: 200,
    headers: new Headers(),
  })
);

// Clean up mocks after each test
afterEach(() => {
  jest.clearAllMocks();
});
