import { describe, it, expect } from 'vitest';
import { companyFormSchema, contactFormSchema } from '@schemas/forms';

describe('Form Schemas', () => {
  describe('companyFormSchema', () => {
    it('should validate valid company data', () => {
      const validData = {
        name: 'Acme Inc',
        industry: 'Technology',
        website: 'https://acme.com',
        employees: 100,
        status: 'lead' as const,
        notes: 'This is a test company',
      };

      const result = companyFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should validate with minimal required fields', () => {
      const minimalData = {
        name: 'Acme Inc',
        status: 'lead' as const,
      };

      const result = companyFormSchema.safeParse(minimalData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toEqual('Acme Inc');
        expect(result.data.status).toEqual('lead');
      }
    });

    it('should reject invalid company data', () => {
      const invalidData = {
        // Missing required name
        industry: 'Technology',
        website: 'not-a-url',
        employees: -5, // Negative number
        status: 'invalid-status' as any,
      };

      const result = companyFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        // Check for specific error messages
        const formattedErrors = result.error.format();
        expect(formattedErrors.name?._errors).toBeDefined();
        expect(formattedErrors.status?._errors).toBeDefined();
      }
    });

    it('should handle empty strings for optional fields', () => {
      const dataWithEmptyStrings = {
        name: 'Acme Inc',
        status: 'lead' as const,
        industry: '',
        website: '',
        notes: '',
      };

      const result = companyFormSchema.safeParse(dataWithEmptyStrings);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.industry).toBe('');
        expect(result.data.website).toBe('');
        expect(result.data.notes).toBe('');
      }
    });
  });

  describe('contactFormSchema', () => {
    it('should validate valid contact data', () => {
      const validData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '************',
        title: 'CEO',
        status: 'lead' as const,
        notes: 'This is a test contact',
        source: 'Website',
      };

      const result = contactFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should validate with minimal required fields', () => {
      const minimalData = {
        firstName: 'John',
        lastName: 'Doe',
        status: 'lead' as const,
      };

      const result = contactFormSchema.safeParse(minimalData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.firstName).toEqual('John');
        expect(result.data.lastName).toEqual('Doe');
        expect(result.data.status).toEqual('lead');
      }
    });

    it('should reject invalid contact data', () => {
      const invalidData = {
        // Missing required lastName
        firstName: 'John',
        email: 'not-an-email',
        status: 'invalid-status' as any,
      };

      const result = contactFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        // Check for specific error messages
        const formattedErrors = result.error.format();
        expect(formattedErrors.lastName?._errors).toBeDefined();
        expect(formattedErrors.email?._errors).toBeDefined();
        expect(formattedErrors.status?._errors).toBeDefined();
      }
    });

    it('should handle empty strings for optional fields', () => {
      const dataWithEmptyStrings = {
        firstName: 'John',
        lastName: 'Doe',
        status: 'lead' as const,
        email: '',
        phone: '',
        title: '',
        notes: '',
        source: '',
      };

      const result = contactFormSchema.safeParse(dataWithEmptyStrings);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.email).toBe('');
        expect(result.data.phone).toBe('');
        expect(result.data.title).toBe('');
        expect(result.data.notes).toBe('');
        expect(result.data.source).toBe('');
      }
    });
  });
});
