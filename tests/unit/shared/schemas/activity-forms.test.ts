import { describe, it, expect } from 'vitest';
import { activityFormSchema } from '@schemas/forms';

describe('Activity Form Schema', () => {
  it('should validate valid activity data', () => {
    const validData = {
      type: 'task',
      title: 'Follow up with client',
      description: 'Discuss the proposal details',
      dueDate: new Date('2023-12-31'),
      priority: 'high' as const,
      contactId: '123',
      companyId: '456',
      opportunityId: '789',
      assignedTo: '101',
    };

    const result = activityFormSchema.safeParse(validData);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toEqual(validData);
    }
  });

  it('should validate with minimal required fields', () => {
    const minimalData = {
      type: 'task',
      title: 'Follow up with client',
    };

    const result = activityFormSchema.safeParse(minimalData);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.type).toEqual('task');
      expect(result.data.title).toEqual('Follow up with client');
    }
  });

  it('should reject invalid activity data', () => {
    const invalidData = {
      // Missing required type
      title: '', // Empty title
    };

    const result = activityFormSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      // Check for specific error messages
      const formattedErrors = result.error.format();
      expect(formattedErrors.type?._errors).toBeDefined();
      expect(formattedErrors.title?._errors).toBeDefined();
    }
  });

  it('should handle empty strings for optional fields', () => {
    const dataWithEmptyStrings = {
      type: 'task',
      title: 'Follow up with client',
      description: '',
    };

    const result = activityFormSchema.safeParse(dataWithEmptyStrings);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.description).toBe('');
    }
  });

  it('should validate with null values for optional fields', () => {
    const dataWithNulls = {
      type: 'task',
      title: 'Follow up with client',
      dueDate: null,
      contactId: null,
      companyId: null,
      opportunityId: null,
      assignedTo: null,
    };

    const result = activityFormSchema.safeParse(dataWithNulls);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.dueDate).toBeNull();
      expect(result.data.contactId).toBeNull();
      expect(result.data.companyId).toBeNull();
      expect(result.data.opportunityId).toBeNull();
      expect(result.data.assignedTo).toBeNull();
    }
  });

  it('should validate with string IDs for related entities', () => {
    const dataWithStringIds = {
      type: 'task',
      title: 'Follow up with client',
      contactId: '123',
      companyId: '456',
      opportunityId: '789',
      assignedTo: '101',
    };

    const result = activityFormSchema.safeParse(dataWithStringIds);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.contactId).toBe('123');
      expect(result.data.companyId).toBe('456');
      expect(result.data.opportunityId).toBe('789');
      expect(result.data.assignedTo).toBe('101');
    }
  });

  it('should validate with number IDs for related entities', () => {
    const dataWithNumberIds = {
      type: 'task',
      title: 'Follow up with client',
      contactId: 123,
      companyId: 456,
      opportunityId: 789,
      assignedTo: 101,
    };

    const result = activityFormSchema.safeParse(dataWithNumberIds);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.contactId).toBe(123);
      expect(result.data.companyId).toBe(456);
      expect(result.data.opportunityId).toBe(789);
      expect(result.data.assignedTo).toBe(101);
    }
  });

  it('should validate different priority values', () => {
    const priorities = ['low', 'medium', 'high'];

    priorities.forEach(priority => {
      const data = {
        type: 'task',
        title: 'Follow up with client',
        priority: priority as any,
      };

      const result = activityFormSchema.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.priority).toBe(priority);
      }
    });
  });

  it('should reject invalid priority values', () => {
    const data = {
      type: 'task',
      title: 'Follow up with client',
      priority: 'invalid-priority' as any,
    };

    const result = activityFormSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      const formattedErrors = result.error.format();
      expect(formattedErrors.priority?._errors).toBeDefined();
    }
  });

  it('should validate different activity types', () => {
    const types = ['task', 'call', 'meeting', 'email', 'note', 'other'];

    types.forEach(type => {
      const data = {
        type,
        title: 'Activity title',
      };

      const result = activityFormSchema.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.type).toBe(type);
      }
    });
  });
});
