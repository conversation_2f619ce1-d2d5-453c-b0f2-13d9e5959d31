import { describe, it, expect } from 'vitest';
import { opportunityFormSchema } from '@schemas/forms';

describe('Opportunity Form Schema', () => {
  it('should validate valid opportunity data', () => {
    const validData = {
      name: 'Enterprise Deal',
      value: 10000,
      currency: 'USD',
      stage: 'discovery' as const,
      closeDate: new Date('2023-12-31'),
      probability: 50,
      notes: 'This is a test opportunity',
      contactId: '123',
      companyId: '456',
    };

    const result = opportunityFormSchema.safeParse(validData);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toEqual(validData);
    }
  });

  it('should validate with minimal required fields', () => {
    const minimalData = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
    };

    const result = opportunityFormSchema.safeParse(minimalData);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.name).toEqual('Enterprise Deal');
      expect(result.data.value).toEqual(10000);
      expect(result.data.stage).toEqual('discovery');
    }
  });

  it('should reject invalid opportunity data', () => {
    const invalidData = {
      // Missing required name
      value: -5000, // Negative value
      stage: 'invalid-stage' as any,
    };

    const result = opportunityFormSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      // Check for specific error messages
      const formattedErrors = result.error.format();
      expect(formattedErrors.name?._errors).toBeDefined();
      expect(formattedErrors.value?._errors).toBeDefined();
      expect(formattedErrors.stage?._errors).toBeDefined();
    }
  });

  it('should handle empty strings for optional fields', () => {
    const dataWithEmptyStrings = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
      notes: '',
      currency: '',
    };

    const result = opportunityFormSchema.safeParse(dataWithEmptyStrings);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.notes).toBe('');
      // Currency should default to USD even if empty string is provided
      expect(result.data.currency).toBe('USD');
    }
  });

  it('should validate with null values for optional fields', () => {
    const dataWithNulls = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
      closeDate: null,
      probability: null,
      contactId: null,
      companyId: null,
    };

    const result = opportunityFormSchema.safeParse(dataWithNulls);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.closeDate).toBeNull();
      expect(result.data.probability).toBeNull();
      expect(result.data.contactId).toBeNull();
      expect(result.data.companyId).toBeNull();
    }
  });

  it('should validate with string IDs for contactId and companyId', () => {
    const dataWithStringIds = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
      contactId: '123',
      companyId: '456',
    };

    const result = opportunityFormSchema.safeParse(dataWithStringIds);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.contactId).toBe('123');
      expect(result.data.companyId).toBe('456');
    }
  });

  it('should validate with number IDs for contactId and companyId', () => {
    const dataWithNumberIds = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
      contactId: 123,
      companyId: 456,
    };

    const result = opportunityFormSchema.safeParse(dataWithNumberIds);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.contactId).toBe(123);
      expect(result.data.companyId).toBe(456);
    }
  });

  it('should validate different stage values', () => {
    const stages = [
      'discovery',
      'qualified',
      'proposal',
      'negotiation',
      'closed_won',
      'closed_lost',
    ];

    stages.forEach(stage => {
      const data = {
        name: 'Enterprise Deal',
        value: 10000,
        stage: stage as any,
      };

      const result = opportunityFormSchema.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.stage).toBe(stage);
      }
    });
  });

  it('should enforce probability range between 0 and 100', () => {
    // Valid probability
    const validData = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
      probability: 50,
    };

    const validResult = opportunityFormSchema.safeParse(validData);
    expect(validResult.success).toBe(true);

    // Too high probability
    const tooHighData = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
      probability: 101,
    };

    const tooHighResult = opportunityFormSchema.safeParse(tooHighData);
    expect(tooHighResult.success).toBe(false);

    // Negative probability
    const negativeData = {
      name: 'Enterprise Deal',
      value: 10000,
      stage: 'discovery' as const,
      probability: -1,
    };

    const negativeResult = opportunityFormSchema.safeParse(negativeData);
    expect(negativeResult.success).toBe(false);
  });
});
