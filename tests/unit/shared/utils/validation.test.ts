import { describe, it, expect } from 'vitest';
import { z } from 'zod';
import { validateData, validateOrThrow, formatZodError } from '@shared/utils/validation';

describe('Validation Utilities', () => {
  // Define a test schema
  const testSchema = z.object({
    name: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Invalid email address'),
    age: z.number().min(18, 'Must be at least 18 years old').optional(),
  });

  describe('validateData', () => {
    it('should validate valid data', () => {
      const data = {
        name: '<PERSON>',
        email: '<EMAIL>',
        age: 25,
      };

      const result = validateData(testSchema, data);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(data);
      expect(result.error).toBeUndefined();
    });

    it('should return error for invalid data', () => {
      const data = {
        name: '<PERSON>', // Too short
        email: 'not-an-email',
        age: 16, // Too young
      };

      const result = validateData(testSchema, data);
      
      expect(result.success).toBe(false);
      expect(result.data).toBeUndefined();
      expect(result.error).toBeDefined();
      expect(result.error).toContain('Name must be at least 2 characters');
      expect(result.error).toContain('Invalid email address');
      expect(result.error).toContain('Must be at least 18 years old');
    });

    it('should handle missing required fields', () => {
      const data = {
        name: 'John',
        // Missing email
      };

      const result = validateData(testSchema, data);
      
      expect(result.success).toBe(false);
      expect(result.data).toBeUndefined();
      expect(result.error).toBeDefined();
      expect(result.error).toContain('email');
      expect(result.error).toContain('required');
    });
  });

  describe('validateOrThrow', () => {
    it('should return validated data for valid input', () => {
      const data = {
        name: 'John',
        email: '<EMAIL>',
      };

      const result = validateOrThrow(testSchema, data);
      expect(result).toEqual(data);
    });

    it('should throw error for invalid data', () => {
      const data = {
        name: 'J',
        email: 'not-an-email',
      };

      expect(() => validateOrThrow(testSchema, data)).toThrow();
      expect(() => validateOrThrow(testSchema, data)).toThrow('Name must be at least 2 characters');
    });
  });

  describe('formatZodError', () => {
    it('should format Zod errors into readable message', () => {
      const data = {
        name: 'J',
        email: 'not-an-email',
        age: 16,
      };

      try {
        testSchema.parse(data);
      } catch (error) {
        if (error instanceof z.ZodError) {
          const message = formatZodError(error);
          expect(message).toContain('Name must be at least 2 characters');
          expect(message).toContain('Invalid email address');
          expect(message).toContain('Must be at least 18 years old');
        }
      }
    });
  });
});
