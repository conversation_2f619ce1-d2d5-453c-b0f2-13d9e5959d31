import { describe, it, expect } from 'vitest';
import {
  isUser,
  isContact,
  isCompany,
  isOpportunity,
  isActivity,
  isTenant,
  isArrayOf,
  isContactArray,
  isCompanyArray,
  hasProperties,
} from '@types/guards';

describe('Type Guards', () => {
  describe('isUser', () => {
    it('should return true for valid user', () => {
      const user = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        fullName: 'Test User',
      };

      expect(isUser(user)).toBe(true);
    });

    it('should return false for invalid user', () => {
      const invalidUser = {
        id: '1',
        // Missing required fields
      };

      expect(isUser(invalidUser)).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isUser(null)).toBe(false);
      expect(isUser(undefined)).toBe(false);
    });
  });

  describe('isContact', () => {
    it('should return true for valid contact', () => {
      const contact = {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'lead',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(isContact(contact)).toBe(true);
    });

    it('should return false for invalid contact', () => {
      const invalidContact = {
        id: '1',
        firstName: 'John',
        // Missing lastName and status
      };

      expect(isContact(invalidContact)).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isContact(null)).toBe(false);
      expect(isContact(undefined)).toBe(false);
    });
  });

  describe('isCompany', () => {
    it('should return true for valid company', () => {
      const company = {
        id: '1',
        name: 'Acme Inc',
        status: 'lead',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(isCompany(company)).toBe(true);
    });

    it('should return false for invalid company', () => {
      const invalidCompany = {
        id: '1',
        // Missing name and status
      };

      expect(isCompany(invalidCompany)).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isCompany(null)).toBe(false);
      expect(isCompany(undefined)).toBe(false);
    });
  });

  describe('isOpportunity', () => {
    it('should return true for valid opportunity', () => {
      const opportunity = {
        id: '1',
        name: 'New Deal',
        value: 10000,
        stage: 'discovery',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(isOpportunity(opportunity)).toBe(true);
    });

    it('should return false for invalid opportunity', () => {
      const invalidOpportunity = {
        id: '1',
        name: 'New Deal',
        // Missing value and stage
      };

      expect(isOpportunity(invalidOpportunity)).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isOpportunity(null)).toBe(false);
      expect(isOpportunity(undefined)).toBe(false);
    });
  });

  describe('isActivity', () => {
    it('should return true for valid activity', () => {
      const activity = {
        id: '1',
        type: 'call',
        title: 'Sales Call',
        completed: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(isActivity(activity)).toBe(true);
    });

    it('should return false for invalid activity', () => {
      const invalidActivity = {
        id: '1',
        type: 'call',
        // Missing title and completed
      };

      expect(isActivity(invalidActivity)).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isActivity(null)).toBe(false);
      expect(isActivity(undefined)).toBe(false);
    });
  });

  describe('isTenant', () => {
    it('should return true for valid tenant', () => {
      const tenant = {
        id: '1',
        name: 'Acme Organization',
        slug: 'acme',
        status: 'active',
        ownerId: '123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(isTenant(tenant)).toBe(true);
    });

    it('should return false for invalid tenant', () => {
      const invalidTenant = {
        id: '1',
        name: 'Acme Organization',
        // Missing slug and status
      };

      expect(isTenant(invalidTenant)).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isTenant(null)).toBe(false);
      expect(isTenant(undefined)).toBe(false);
    });
  });

  describe('isArrayOf', () => {
    it('should return true for array of valid items', () => {
      const contacts = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          status: 'lead',
        },
        {
          id: '2',
          firstName: 'Jane',
          lastName: 'Smith',
          status: 'customer',
        },
      ];

      expect(isArrayOf(contacts, isContact)).toBe(true);
    });

    it('should return false for array with invalid items', () => {
      const mixedArray = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          status: 'lead',
        },
        {
          id: '2',
          // Invalid contact - missing required fields
        },
      ];

      expect(isArrayOf(mixedArray, isContact)).toBe(false);
    });

    it('should return false for non-array', () => {
      const notArray = {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        status: 'lead',
      };

      expect(isArrayOf(notArray, isContact)).toBe(false);
    });
  });

  describe('isContactArray', () => {
    it('should return true for array of valid contacts', () => {
      const contacts = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          status: 'lead',
        },
        {
          id: '2',
          firstName: 'Jane',
          lastName: 'Smith',
          status: 'customer',
        },
      ];

      expect(isContactArray(contacts)).toBe(true);
    });

    it('should return false for array with invalid contacts', () => {
      const invalidContacts = [
        {
          id: '1',
          firstName: 'John',
          // Missing lastName and status
        },
      ];

      expect(isContactArray(invalidContacts)).toBe(false);
    });
  });

  describe('isCompanyArray', () => {
    it('should return true for array of valid companies', () => {
      const companies = [
        {
          id: '1',
          name: 'Acme Inc',
          status: 'lead',
        },
        {
          id: '2',
          name: 'Globex Corp',
          status: 'customer',
        },
      ];

      expect(isCompanyArray(companies)).toBe(true);
    });

    it('should return false for array with invalid companies', () => {
      const invalidCompanies = [
        {
          id: '1',
          // Missing name and status
        },
      ];

      expect(isCompanyArray(invalidCompanies)).toBe(false);
    });
  });

  describe('hasProperties', () => {
    it('should return true when object has all required properties', () => {
      const obj = {
        id: '1',
        name: 'Test',
        value: 100,
      };

      expect(hasProperties(obj, ['id', 'name', 'value'])).toBe(true);
    });

    it('should return false when object is missing required properties', () => {
      const obj = {
        id: '1',
        name: 'Test',
        // Missing value
      };

      expect(hasProperties(obj, ['id', 'name', 'value'])).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(hasProperties(null, ['id'])).toBe(false);
      expect(hasProperties(undefined, ['id'])).toBe(false);
    });
  });
});
