import { test, expect } from '@playwright/test';

test.describe('Contacts Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          fullName: 'Test User',
        }),
      });
    });

    // Mock contacts API
    await page.route('**/api/contacts', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          contacts: [
            {
              id: '1',
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              phone: '************',
              status: 'lead',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            {
              id: '2',
              firstName: '<PERSON>',
              lastName: 'Smith',
              email: '<EMAIL>',
              phone: '************',
              status: 'customer',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 2,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Navigate to contacts page
    await page.goto('/contacts');
  });

  test('should display contacts list', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Contacts');

    // Check that contacts are displayed
    await expect(page.getByText('John Doe')).toBeVisible();
    await expect(page.getByText('<EMAIL>')).toBeVisible();
    
    // Check status badges
    await expect(page.getByText('lead')).toBeVisible();
    await expect(page.getByText('customer')).toBeVisible();
  });

  test('should open contact form when add button is clicked', async ({ page }) => {
    // Mock create contact API
    await page.route('**/api/contacts', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          body: JSON.stringify({
            success: true,
            contact: {
              id: '3',
              firstName: 'Alice',
              lastName: 'Johnson',
              email: '<EMAIL>',
              status: 'prospect',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          }),
        });
      }
    });

    // Click add contact button
    await page.getByRole('button', { name: /add contact/i }).click();
    
    // Check that form is displayed
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByText('Add New Contact')).toBeVisible();
    
    // Fill out form
    await page.getByLabel('First Name').fill('Alice');
    await page.getByLabel('Last Name').fill('Johnson');
    await page.getByLabel('Email').fill('<EMAIL>');
    await page.getByLabel('Status').click();
    await page.getByText('Prospect').click();
    
    // Submit form
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check for success message
    await expect(page.getByText('Contact created successfully')).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    // Click add contact button
    await page.getByRole('button', { name: /add contact/i }).click();
    
    // Submit empty form
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check for validation errors
    await expect(page.getByText('First name is required')).toBeVisible();
    await expect(page.getByText('Last name is required')).toBeVisible();
    
    // Fill first name only
    await page.getByLabel('First Name').fill('Alice');
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check that last name error still shows
    await expect(page.getByText('Last name is required')).toBeVisible();
    
    // Fill invalid email
    await page.getByLabel('Last Name').fill('Johnson');
    await page.getByLabel('Email').fill('not-an-email');
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check for email validation error
    await expect(page.getByText('Invalid email address')).toBeVisible();
  });
});
