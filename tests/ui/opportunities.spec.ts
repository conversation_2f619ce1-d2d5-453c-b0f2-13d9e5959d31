import { test, expect } from '@playwright/test';

test.describe('Opportunities Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          fullName: 'Test User',
        }),
      });
    });

    // Mock opportunities API
    await page.route('**/api/opportunities', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          opportunities: [
            {
              id: '1',
              name: 'Enterprise Deal',
              value: 10000,
              currency: 'USD',
              stage: 'discovery',
              probability: 50,
              closeDate: new Date('2023-12-31').toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            {
              id: '2',
              name: 'SMB Deal',
              value: 5000,
              currency: 'EUR',
              stage: 'negotiation',
              probability: 75,
              closeDate: new Date('2023-10-15').toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 2,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Mock companies and contacts for form dropdowns
    await page.route('**/api/companies', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          companies: [
            { id: '1', name: 'Acme Inc' },
            { id: '2', name: 'Globex Corp' },
          ],
        }),
      });
    });

    await page.route('**/api/contacts', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          contacts: [
            { id: '1', firstName: 'John', lastName: 'Doe' },
            { id: '2', firstName: 'Jane', lastName: 'Smith' },
          ],
        }),
      });
    });

    // Navigate to opportunities page
    await page.goto('/opportunities');
  });

  test('should display opportunities list', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Opportunities');

    // Check that opportunities are displayed
    await expect(page.getByText('Enterprise Deal')).toBeVisible();
    await expect(page.getByText('SMB Deal')).toBeVisible();
    
    // Check values
    await expect(page.getByText('$10,000')).toBeVisible();
    await expect(page.getByText('€5,000')).toBeVisible();
    
    // Check stage badges
    await expect(page.getByText('discovery')).toBeVisible();
    await expect(page.getByText('negotiation')).toBeVisible();
  });

  test('should open opportunity form when add button is clicked', async ({ page }) => {
    // Mock create opportunity API
    await page.route('**/api/opportunities', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          body: JSON.stringify({
            success: true,
            opportunity: {
              id: '3',
              name: 'New Deal',
              value: 15000,
              currency: 'USD',
              stage: 'proposal',
              probability: 60,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          }),
        });
      }
    });

    // Click add opportunity button
    await page.getByRole('button', { name: /add opportunity/i }).click();
    
    // Check that form is displayed
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByText('Add New Opportunity')).toBeVisible();
    
    // Fill out form
    await page.getByLabel('Opportunity Name*').fill('New Deal');
    await page.getByLabel('Value*').fill('15000');
    await page.getByLabel('Stage*').click();
    await page.getByText('Proposal').click();
    await page.getByLabel('Probability (%)').fill('60');
    
    // Submit form
    await page.getByRole('button', { name: 'Add Opportunity' }).click();
    
    // Check for success message
    await expect(page.getByText('Opportunity created successfully')).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    // Click add opportunity button
    await page.getByRole('button', { name: /add opportunity/i }).click();
    
    // Submit empty form
    await page.getByRole('button', { name: 'Add Opportunity' }).click();
    
    // Check for validation errors
    await expect(page.getByText('Opportunity name is required')).toBeVisible();
    await expect(page.getByText('Value is required')).toBeVisible();
    
    // Fill invalid value
    await page.getByLabel('Opportunity Name*').fill('New Deal');
    await page.getByLabel('Value*').fill('-100');
    await page.getByRole('button', { name: 'Add Opportunity' }).click();
    
    // Check for value validation error
    await expect(page.getByText('Value must be positive')).toBeVisible();
    
    // Fill invalid probability
    await page.getByLabel('Value*').fill('15000');
    await page.getByLabel('Probability (%)').fill('101');
    await page.getByRole('button', { name: 'Add Opportunity' }).click();
    
    // Check for probability validation error
    await expect(page.getByText('Number must be less than or equal to 100')).toBeVisible();
  });

  test('should filter opportunities by stage', async ({ page }) => {
    // Mock filtered opportunities API response
    await page.route('**/api/opportunities?stage=discovery', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          opportunities: [
            {
              id: '1',
              name: 'Enterprise Deal',
              value: 10000,
              currency: 'USD',
              stage: 'discovery',
              probability: 50,
              closeDate: new Date('2023-12-31').toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 1,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Click on stage filter
    await page.getByText('Filter by Stage').click();
    await page.getByText('Discovery').click();
    
    // Check that only the discovery opportunity is displayed
    await expect(page.getByText('Enterprise Deal')).toBeVisible();
    await expect(page.getByText('SMB Deal')).not.toBeVisible();
  });

  test('should search opportunities', async ({ page }) => {
    // Mock search API response
    await page.route('**/api/opportunities?search=Enterprise', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          opportunities: [
            {
              id: '1',
              name: 'Enterprise Deal',
              value: 10000,
              currency: 'USD',
              stage: 'discovery',
              probability: 50,
              closeDate: new Date('2023-12-31').toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 1,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Enter search term
    await page.getByPlaceholder('Search opportunities...').fill('Enterprise');
    await page.keyboard.press('Enter');
    
    // Check that only the matching opportunity is displayed
    await expect(page.getByText('Enterprise Deal')).toBeVisible();
    await expect(page.getByText('SMB Deal')).not.toBeVisible();
  });
});
