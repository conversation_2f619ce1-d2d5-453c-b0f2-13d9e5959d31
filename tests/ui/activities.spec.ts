import { test, expect } from '@playwright/test';

test.describe('Activities Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          fullName: 'Test User',
        }),
      });
    });

    // Mock activities API
    await page.route('**/api/activities', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          activities: [
            {
              id: '1',
              type: 'task',
              title: 'Follow up with client',
              description: 'Discuss the proposal details',
              dueDate: new Date('2023-12-31').toISOString(),
              completed: false,
              priority: 'high',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            {
              id: '2',
              type: 'call',
              title: 'Sales call',
              description: 'Introductory call with prospect',
              dueDate: new Date('2023-12-15').toISOString(),
              completed: true,
              completedAt: new Date('2023-12-15').toISOString(),
              priority: 'medium',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 2,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Mock companies, contacts, and opportunities for form dropdowns
    await page.route('**/api/companies', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          companies: [
            { id: '1', name: 'Acme Inc' },
            { id: '2', name: 'Globex Corp' },
          ],
        }),
      });
    });

    await page.route('**/api/contacts', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          contacts: [
            { id: '1', firstName: 'John', lastName: 'Doe' },
            { id: '2', firstName: 'Jane', lastName: 'Smith' },
          ],
        }),
      });
    });

    await page.route('**/api/opportunities', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          opportunities: [
            { id: '1', name: 'Enterprise Deal' },
            { id: '2', name: 'SMB Deal' },
          ],
        }),
      });
    });

    // Navigate to activities page
    await page.goto('/activities');
  });

  test('should display activities list', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Activities');

    // Check that activities are displayed
    await expect(page.getByText('Follow up with client')).toBeVisible();
    await expect(page.getByText('Sales call')).toBeVisible();
    
    // Check activity types
    await expect(page.getByText('task')).toBeVisible();
    await expect(page.getByText('call')).toBeVisible();
    
    // Check priority badges
    await expect(page.getByText('high')).toBeVisible();
    await expect(page.getByText('medium')).toBeVisible();
    
    // Check completion status
    await expect(page.getByRole('checkbox').first()).not.toBeChecked();
    await expect(page.getByRole('checkbox').nth(1)).toBeChecked();
  });

  test('should open activity form when add button is clicked', async ({ page }) => {
    // Mock create activity API
    await page.route('**/api/activities', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          body: JSON.stringify({
            success: true,
            activity: {
              id: '3',
              type: 'meeting',
              title: 'Client meeting',
              description: 'Discuss project requirements',
              dueDate: new Date('2023-12-20').toISOString(),
              completed: false,
              priority: 'high',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          }),
        });
      }
    });

    // Click add activity button
    await page.getByRole('button', { name: /add activity/i }).click();
    
    // Check that form is displayed
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByText('Add New Activity')).toBeVisible();
    
    // Fill out form
    await page.getByLabel('Activity Type*').click();
    await page.getByText('Meeting').click();
    await page.getByLabel('Title*').fill('Client meeting');
    await page.getByLabel('Description').fill('Discuss project requirements');
    await page.getByLabel('Due Date').click();
    await page.getByText('20').click(); // Select the 20th day of the month
    await page.getByLabel('Priority').click();
    await page.getByText('High').click();
    
    // Submit form
    await page.getByRole('button', { name: 'Add Activity' }).click();
    
    // Check for success message
    await expect(page.getByText('Activity created successfully')).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    // Click add activity button
    await page.getByRole('button', { name: /add activity/i }).click();
    
    // Submit empty form
    await page.getByRole('button', { name: 'Add Activity' }).click();
    
    // Check for validation errors
    await expect(page.getByText('Title is required')).toBeVisible();
    
    // Fill invalid title (empty)
    await page.getByLabel('Title*').fill('');
    await page.getByRole('button', { name: 'Add Activity' }).click();
    
    // Check for title validation error
    await expect(page.getByText('Title is required')).toBeVisible();
  });

  test('should mark activity as complete', async ({ page }) => {
    // Mock complete activity API
    await page.route('**/api/activities/1/complete', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          activity: {
            id: '1',
            type: 'task',
            title: 'Follow up with client',
            description: 'Discuss the proposal details',
            dueDate: new Date('2023-12-31').toISOString(),
            completed: true,
            completedAt: new Date().toISOString(),
            priority: 'high',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        }),
      });
    });

    // Click the checkbox to mark activity as complete
    await page.getByRole('checkbox').first().click();
    
    // Check for success message
    await expect(page.getByText('Activity completed')).toBeVisible();
  });

  test('should filter activities by type', async ({ page }) => {
    // Mock filtered activities API response
    await page.route('**/api/activities?type=task', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          activities: [
            {
              id: '1',
              type: 'task',
              title: 'Follow up with client',
              description: 'Discuss the proposal details',
              dueDate: new Date('2023-12-31').toISOString(),
              completed: false,
              priority: 'high',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 1,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Click on type filter
    await page.getByText('Filter by Type').click();
    await page.getByText('Task').click();
    
    // Check that only the task activity is displayed
    await expect(page.getByText('Follow up with client')).toBeVisible();
    await expect(page.getByText('Sales call')).not.toBeVisible();
  });

  test('should filter activities by completion status', async ({ page }) => {
    // Mock filtered activities API response
    await page.route('**/api/activities?completed=true', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          activities: [
            {
              id: '2',
              type: 'call',
              title: 'Sales call',
              description: 'Introductory call with prospect',
              dueDate: new Date('2023-12-15').toISOString(),
              completed: true,
              completedAt: new Date('2023-12-15').toISOString(),
              priority: 'medium',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 1,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Click on completion status filter
    await page.getByText('Filter by Status').click();
    await page.getByText('Completed').click();
    
    // Check that only the completed activity is displayed
    await expect(page.getByText('Sales call')).toBeVisible();
    await expect(page.getByText('Follow up with client')).not.toBeVisible();
  });
});
