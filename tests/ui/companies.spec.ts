import { test, expect } from '@playwright/test';

test.describe('Companies Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          fullName: 'Test User',
        }),
      });
    });

    // Mock companies API
    await page.route('**/api/companies', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          companies: [
            {
              id: '1',
              name: 'Acme Inc',
              industry: 'Technology',
              website: 'https://acme.com',
              employees: 100,
              status: 'lead',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            {
              id: '2',
              name: 'Globex Corp',
              industry: 'Manufacturing',
              website: 'https://globex.com',
              employees: 500,
              status: 'customer',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 2,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Navigate to companies page
    await page.goto('/companies');
  });

  test('should display companies list', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Companies');

    // Check that companies are displayed
    await expect(page.getByText('Acme Inc')).toBeVisible();
    await expect(page.getByText('Globex Corp')).toBeVisible();
    
    // Check industry labels
    await expect(page.getByText('Technology')).toBeVisible();
    await expect(page.getByText('Manufacturing')).toBeVisible();
    
    // Check status badges
    await expect(page.getByText('lead')).toBeVisible();
    await expect(page.getByText('customer')).toBeVisible();
  });

  test('should open company form when add button is clicked', async ({ page }) => {
    // Mock create company API
    await page.route('**/api/companies', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          body: JSON.stringify({
            success: true,
            company: {
              id: '3',
              name: 'New Company',
              industry: 'Finance',
              website: 'https://newcompany.com',
              employees: 250,
              status: 'prospect',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          }),
        });
      }
    });

    // Click add company button
    await page.getByRole('button', { name: /add company/i }).click();
    
    // Check that form is displayed
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByText('Add New Company')).toBeVisible();
    
    // Fill out form
    await page.getByLabel('Name').fill('New Company');
    await page.getByLabel('Industry').fill('Finance');
    await page.getByLabel('Website').fill('https://newcompany.com');
    await page.getByLabel('Employees').fill('250');
    await page.getByLabel('Status').click();
    await page.getByText('Prospect').click();
    
    // Submit form
    await page.getByRole('button', { name: 'Add Company' }).click();
    
    // Check for success message
    await expect(page.getByText('Company created successfully')).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    // Click add company button
    await page.getByRole('button', { name: /add company/i }).click();
    
    // Submit empty form
    await page.getByRole('button', { name: 'Add Company' }).click();
    
    // Check for validation errors
    await expect(page.getByText('Company name is required')).toBeVisible();
    
    // Fill invalid website
    await page.getByLabel('Name').fill('Test Company');
    await page.getByLabel('Website').fill('not-a-url');
    await page.getByRole('button', { name: 'Add Company' }).click();
    
    // Check for website validation error
    await expect(page.getByText('Invalid website URL')).toBeVisible();
    
    // Fill invalid employees number
    await page.getByLabel('Website').fill('https://test.com');
    await page.getByLabel('Employees').fill('-10');
    await page.getByRole('button', { name: 'Add Company' }).click();
    
    // Check for employees validation error
    await expect(page.getByText('Number must be positive')).toBeVisible();
  });

  test('should filter companies by status', async ({ page }) => {
    // Mock filtered companies API response
    await page.route('**/api/companies?status=lead', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          companies: [
            {
              id: '1',
              name: 'Acme Inc',
              industry: 'Technology',
              website: 'https://acme.com',
              employees: 100,
              status: 'lead',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 1,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Click on status filter
    await page.getByText('Filter by Status').click();
    await page.getByText('Lead').click();
    
    // Check that only the lead company is displayed
    await expect(page.getByText('Acme Inc')).toBeVisible();
    await expect(page.getByText('Globex Corp')).not.toBeVisible();
  });

  test('should search companies', async ({ page }) => {
    // Mock search API response
    await page.route('**/api/companies?search=Acme', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          success: true,
          companies: [
            {
              id: '1',
              name: 'Acme Inc',
              industry: 'Technology',
              website: 'https://acme.com',
              employees: 100,
              status: 'lead',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ],
          total: 1,
          page: 1,
          pageSize: 20,
        }),
      });
    });

    // Enter search term
    await page.getByPlaceholder('Search companies...').fill('Acme');
    await page.keyboard.press('Enter');
    
    // Check that only the matching company is displayed
    await expect(page.getByText('Acme Inc')).toBeVisible();
    await expect(page.getByText('Globex Corp')).not.toBeVisible();
  });
});
