import '@testing-library/jest-dom';
import { vi } from 'vitest';
import React from 'react';

// Mock ResizeObserver
class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

// Add ResizeObserver to the global object
global.ResizeObserver = ResizeObserver;

// Mock the API utils
vi.mock('../client/src/api/api-utils', () => ({
  apiRequest: vi.fn().mockResolvedValue({}),
}));

// Mock lucide-react icons
vi.mock('lucide-react', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    Loader2: () => React.createElement('div', { 'data-testid': 'loader-icon' }),
    Sparkles: () => React.createElement('div', { 'data-testid': 'sparkles-icon' }),
    Download: () => React.createElement('div', { 'data-testid': 'download-icon' }),
    FileText: () => React.createElement('div', { 'data-testid': 'file-text-icon' }),
    FileCode: () => React.createElement('div', { 'data-testid': 'file-code-icon' }),
    Send: () => React.createElement('div', { 'data-testid': 'send-icon' }),
    Search: () => React.createElement('div', { 'data-testid': 'search-icon' }),
    Plus: () => React.createElement('div', { 'data-testid': 'plus-icon' }),
    Filter: () => React.createElement('div', { 'data-testid': 'filter-icon' }),
    Check: () => React.createElement('div', { 'data-testid': 'check-icon' }),
    X: () => React.createElement('div', { 'data-testid': 'x-icon' }),
    ExternalLink: () => React.createElement('div', { 'data-testid': 'external-link-icon' }),
    ChevronDown: () => React.createElement('div', { 'data-testid': 'chevron-down-icon' }),
  };
});

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  })),
  usePathname: vi.fn(() => '/'),
  useSearchParams: vi.fn(() => ({ get: vi.fn() })),
}), { virtual: true });

// Mock hooks
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn(),
  })),
}), { virtual: true });

// Set up global fetch mock
global.fetch = vi.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
    ok: true,
    status: 200,
    headers: new Headers(),
  })
);

// Clean up mocks after each test
afterEach(() => {
  vi.clearAllMocks();
});
