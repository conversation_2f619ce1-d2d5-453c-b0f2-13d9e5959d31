import { connectToMongoDB, disconnectFromMongoDB } from '../server/mongodb-connection';
import { 
  User, 
  Contact, 
  EmailTracking,
  EmailEventType
} from '../server/models/mongoose';
import emailTrackingService from '../server/services/email-tracking-service';
import mongoose from 'mongoose';

// Set MongoDB as enabled
process.env.MONGODB_ENABLED = 'true';

// Mock data
const mockUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'password123',
  fullName: 'Test User'
};

const mockContact = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  status: 'lead'
};

describe('Email Tracking Service', () => {
  let userId: string;
  let contactId: string;

  beforeAll(async () => {
    // Connect to MongoDB
    await connectToMongoDB();

    // Create test user
    const user = new User({
      ...mockUser,
      password: await User.hashPassword(mockUser.password)
    });
    await user.save();
    userId = user._id.toString();

    // Create test contact
    const contact = new Contact({
      ...mockContact,
      owner: user._id
    });
    await contact.save();
    contactId = contact._id.toString();
  });

  afterAll(async () => {
    // Clean up
    await User.deleteMany({});
    await Contact.deleteMany({});
    await EmailTracking.deleteMany({});

    // Disconnect from MongoDB
    await disconnectFromMongoDB();
  });

  it('should create a new email tracking record', async () => {
    const tracking = await emailTrackingService.createTracking({
      userId,
      contactId,
      messageId: 'test-message-id',
      subject: 'Test Subject',
      recipient: '<EMAIL>',
      sender: '<EMAIL>'
    });

    expect(tracking).toBeDefined();
    expect(tracking.userId.toString()).toBe(userId);
    expect(tracking.contactId?.toString()).toBe(contactId);
    expect(tracking.messageId).toBe('test-message-id');
    expect(tracking.subject).toBe('Test Subject');
    expect(tracking.recipient).toBe('<EMAIL>');
    expect(tracking.sender).toBe('<EMAIL>');
    expect(tracking.status).toBe(EmailEventType.SENT);
    expect(tracking.events.length).toBe(1);
    expect(tracking.events[0].type).toBe(EmailEventType.SENT);
    expect(tracking.pixelId).toBeDefined();
    expect(tracking.trackingEnabled).toBe(true);
    expect(tracking.linkTrackingEnabled).toBe(true);
    expect(tracking.attachmentTrackingEnabled).toBe(true);
  });

  it('should process an open event', async () => {
    // Create a tracking record
    const tracking = await emailTrackingService.createTracking({
      userId,
      contactId,
      messageId: 'test-message-id-2',
      subject: 'Test Subject 2',
      recipient: '<EMAIL>',
      sender: '<EMAIL>'
    });

    // Process open event
    const updatedTracking = await emailTrackingService.processOpenEvent(tracking.pixelId, {
      ip: '127.0.0.1',
      userAgent: 'test-user-agent',
      device: 'desktop'
    });

    expect(updatedTracking).toBeDefined();
    if (updatedTracking) {
      expect(updatedTracking.status).toBe(EmailEventType.OPENED);
      expect(updatedTracking.events.length).toBe(2);
      expect(updatedTracking.events[1].type).toBe(EmailEventType.OPENED);
      expect(updatedTracking.events[1].metadata.ip).toBe('127.0.0.1');
      expect(updatedTracking.events[1].metadata.userAgent).toBe('test-user-agent');
      expect(updatedTracking.events[1].metadata.device).toBe('desktop');
    }
  });

  it('should process a click event', async () => {
    // Create a tracking record
    const tracking = await emailTrackingService.createTracking({
      userId,
      contactId,
      messageId: 'test-message-id-3',
      subject: 'Test Subject 3',
      recipient: '<EMAIL>',
      sender: '<EMAIL>',
      customFields: {
        linkIds: ['test-link-id']
      }
    });

    // Process click event
    const updatedTracking = await emailTrackingService.processClickEvent('test-link-id', {
      ip: '127.0.0.1',
      userAgent: 'test-user-agent',
      device: 'desktop',
      url: 'https://example.com'
    });

    expect(updatedTracking).toBeDefined();
    if (updatedTracking) {
      expect(updatedTracking.status).toBe(EmailEventType.CLICKED);
      expect(updatedTracking.events.length).toBe(2);
      expect(updatedTracking.events[1].type).toBe(EmailEventType.CLICKED);
      expect(updatedTracking.events[1].metadata.ip).toBe('127.0.0.1');
      expect(updatedTracking.events[1].metadata.userAgent).toBe('test-user-agent');
      expect(updatedTracking.events[1].metadata.device).toBe('desktop');
      expect(updatedTracking.events[1].metadata.url).toBe('https://example.com');
      expect(updatedTracking.events[1].metadata.linkId).toBe('test-link-id');
    }
  });

  it('should process a reply event', async () => {
    // Create a tracking record
    const tracking = await emailTrackingService.createTracking({
      userId,
      contactId,
      messageId: 'test-message-id-4',
      subject: 'Test Subject 4',
      recipient: '<EMAIL>',
      sender: '<EMAIL>'
    });

    // Process reply event
    const updatedTracking = await emailTrackingService.processReplyEvent('test-message-id-4', {
      subject: 'Re: Test Subject 4',
      snippet: 'This is a test reply',
      timestamp: new Date()
    });

    expect(updatedTracking).toBeDefined();
    if (updatedTracking) {
      expect(updatedTracking.status).toBe(EmailEventType.REPLIED);
      expect(updatedTracking.events.length).toBe(2);
      expect(updatedTracking.events[1].type).toBe(EmailEventType.REPLIED);
      expect(updatedTracking.events[1].metadata.subject).toBe('Re: Test Subject 4');
      expect(updatedTracking.events[1].metadata.snippet).toBe('This is a test reply');
    }
  });

  it('should generate a tracking pixel', () => {
    const pixelId = 'test-pixel-id';
    const pixelHtml = emailTrackingService.generateTrackingPixel(pixelId);

    expect(pixelHtml).toContain('<img');
    expect(pixelHtml).toContain('src="');
    expect(pixelHtml).toContain(pixelId);
    expect(pixelHtml).toContain('width="1"');
    expect(pixelHtml).toContain('height="1"');
    expect(pixelHtml).toContain('style="display:none;"');
  });
});
