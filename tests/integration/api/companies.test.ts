import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import express from 'express';
import session from 'express-session';
import { Company } from '@models/mongoose';
import companyRoutes from '@server/routes/mongo-companies-routes';
import { CreateCompanyRequest, UpdateCompanyRequest } from '@types/api';

describe('Companies API', () => {
  let app: express.Express;
  let mongoServer: MongoMemoryServer;
  let testUserId: string;

  beforeAll(async () => {
    // Set up MongoDB memory server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create test user ID
    testUserId = new mongoose.Types.ObjectId().toString();

    // Set up Express app
    app = express();
    app.use(express.json());
    
    // Set up session middleware with mock user
    app.use(session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false,
    }));
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.session.userId = testUserId;
      next();
    });
    
    // Add company routes
    app.use('/api/companies', companyRoutes);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear companies collection before each test
    await Company.deleteMany({});
  });

  describe('GET /api/companies', () => {
    it('should return empty array when no companies exist', async () => {
      const response = await request(app).get('/api/companies');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.companies).toEqual([]);
      expect(response.body.total).toBe(0);
    });

    it('should return companies for the authenticated user', async () => {
      // Create test companies
      await Company.create([
        {
          name: 'Acme Inc',
          industry: 'Technology',
          website: 'https://acme.com',
          size: '100',
          status: 'lead',
          owner: testUserId,
        },
        {
          name: 'Globex Corp',
          industry: 'Manufacturing',
          website: 'https://globex.com',
          size: '500',
          status: 'customer',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/companies');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.companies.length).toBe(2);
      expect(response.body.total).toBe(2);
      
      // Check company data
      expect(response.body.companies[0].name).toBe('Acme Inc');
      expect(response.body.companies[0].industry).toBe('Technology');
      expect(response.body.companies[0].website).toBe('https://acme.com');
      expect(response.body.companies[0].employees).toBe(100);
      expect(response.body.companies[0].status).toBe('lead');
      
      expect(response.body.companies[1].name).toBe('Globex Corp');
      expect(response.body.companies[1].industry).toBe('Manufacturing');
      expect(response.body.companies[1].website).toBe('https://globex.com');
      expect(response.body.companies[1].employees).toBe(500);
      expect(response.body.companies[1].status).toBe('customer');
    });

    it('should filter companies by status', async () => {
      // Create test companies with different statuses
      await Company.create([
        {
          name: 'Acme Inc',
          industry: 'Technology',
          status: 'lead',
          owner: testUserId,
        },
        {
          name: 'Globex Corp',
          industry: 'Manufacturing',
          status: 'customer',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/companies?status=lead');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.companies.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.companies[0].name).toBe('Acme Inc');
      expect(response.body.companies[0].status).toBe('lead');
    });

    it('should filter companies by industry', async () => {
      // Create test companies with different industries
      await Company.create([
        {
          name: 'Acme Inc',
          industry: 'Technology',
          status: 'lead',
          owner: testUserId,
        },
        {
          name: 'Globex Corp',
          industry: 'Manufacturing',
          status: 'customer',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/companies?industry=Technology');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.companies.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.companies[0].name).toBe('Acme Inc');
      expect(response.body.companies[0].industry).toBe('Technology');
    });
  });

  describe('POST /api/companies', () => {
    it('should create a new company', async () => {
      const companyData: CreateCompanyRequest = {
        name: 'New Company',
        industry: 'Finance',
        website: 'https://newcompany.com',
        employees: 250,
        status: 'prospect',
        notes: 'Test notes',
      };

      const response = await request(app)
        .post('/api/companies')
        .send(companyData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.company).toBeDefined();
      expect(response.body.company.name).toBe('New Company');
      expect(response.body.company.industry).toBe('Finance');
      expect(response.body.company.website).toBe('https://newcompany.com');
      expect(response.body.company.employees).toBe(250);
      expect(response.body.company.status).toBe('prospect');
      
      // Verify company was saved to database
      const savedCompany = await Company.findOne({ name: 'New Company' });
      expect(savedCompany).toBeDefined();
      expect(savedCompany?.name).toBe('New Company');
      expect(savedCompany?.industry).toBe('Finance');
      expect(savedCompany?.size).toBe('250');
    });

    it('should reject invalid company data', async () => {
      const invalidData = {
        // Missing required name
        industry: 'Finance',
        status: 'invalid-status', // Invalid status
      };

      const response = await request(app)
        .post('/api/companies')
        .send(invalidData);
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  // Additional tests for GET /:id, PATCH /:id, and DELETE /:id would follow the same pattern
});
