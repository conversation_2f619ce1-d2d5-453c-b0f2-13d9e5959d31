import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import express from 'express';
import session from 'express-session';
import { Contact } from '@models/mongoose';
import contactRoutes from '@server/routes/mongo-contacts-routes';
import { CreateContactRequest, UpdateContactRequest } from '@types/api';

describe('Contacts API', () => {
  let app: express.Express;
  let mongoServer: MongoMemoryServer;
  let testUserId: string;

  beforeAll(async () => {
    // Set up MongoDB memory server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create test user ID
    testUserId = new mongoose.Types.ObjectId().toString();

    // Set up Express app
    app = express();
    app.use(express.json());
    
    // Set up session middleware with mock user
    app.use(session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false,
    }));
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.session.userId = testUserId;
      next();
    });
    
    // Add contact routes
    app.use('/api/contacts', contactRoutes);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear contacts collection before each test
    await Contact.deleteMany({});
  });

  describe('GET /api/contacts', () => {
    it('should return empty array when no contacts exist', async () => {
      const response = await request(app).get('/api/contacts');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.contacts).toEqual([]);
      expect(response.body.total).toBe(0);
    });

    it('should return contacts for the authenticated user', async () => {
      // Create test contacts
      await Contact.create([
        {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          status: 'lead',
          owner: testUserId,
        },
        {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          status: 'customer',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/contacts');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.contacts.length).toBe(2);
      expect(response.body.total).toBe(2);
      
      // Check contact data
      expect(response.body.contacts[0].firstName).toBe('John');
      expect(response.body.contacts[0].lastName).toBe('Doe');
      expect(response.body.contacts[0].email).toBe('<EMAIL>');
      expect(response.body.contacts[0].status).toBe('lead');
      
      expect(response.body.contacts[1].firstName).toBe('Jane');
      expect(response.body.contacts[1].lastName).toBe('Smith');
      expect(response.body.contacts[1].email).toBe('<EMAIL>');
      expect(response.body.contacts[1].status).toBe('customer');
    });

    it('should filter contacts by status', async () => {
      // Create test contacts with different statuses
      await Contact.create([
        {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          status: 'lead',
          owner: testUserId,
        },
        {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          status: 'customer',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/contacts?status=lead');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.contacts.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.contacts[0].firstName).toBe('John');
      expect(response.body.contacts[0].status).toBe('lead');
    });
  });

  describe('POST /api/contacts', () => {
    it('should create a new contact', async () => {
      const contactData: CreateContactRequest = {
        firstName: 'Alice',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '************',
        status: 'prospect',
        notes: 'Test notes',
      };

      const response = await request(app)
        .post('/api/contacts')
        .send(contactData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.contact).toBeDefined();
      expect(response.body.contact.firstName).toBe('Alice');
      expect(response.body.contact.lastName).toBe('Johnson');
      expect(response.body.contact.email).toBe('<EMAIL>');
      expect(response.body.contact.status).toBe('prospect');
      
      // Verify contact was saved to database
      const savedContact = await Contact.findOne({ email: '<EMAIL>' });
      expect(savedContact).toBeDefined();
      expect(savedContact?.firstName).toBe('Alice');
      expect(savedContact?.lastName).toBe('Johnson');
    });

    it('should reject invalid contact data', async () => {
      const invalidData = {
        // Missing required firstName
        lastName: 'Johnson',
        email: 'invalid-email', // Invalid email
      };

      const response = await request(app)
        .post('/api/contacts')
        .send(invalidData);
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  // Additional tests for GET /:id, PATCH /:id, and DELETE /:id would follow the same pattern
});
