import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import express from 'express';
import session from 'express-session';
import { Activity } from '@models/mongoose';
import activityRoutes from '@server/routes/mongo-activities-routes';
import { CreateActivityRequest, UpdateActivityRequest } from '@types/api';

describe('Activities API', () => {
  let app: express.Express;
  let mongoServer: MongoMemoryServer;
  let testUserId: string;

  beforeAll(async () => {
    // Set up MongoDB memory server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create test user ID
    testUserId = new mongoose.Types.ObjectId().toString();

    // Set up Express app
    app = express();
    app.use(express.json());
    
    // Set up session middleware with mock user
    app.use(session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false,
    }));
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.session.userId = testUserId;
      next();
    });
    
    // Add activity routes
    app.use('/api/activities', activityRoutes);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear activities collection before each test
    await Activity.deleteMany({});
  });

  describe('GET /api/activities', () => {
    it('should return empty array when no activities exist', async () => {
      const response = await request(app).get('/api/activities');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.activities).toEqual([]);
      expect(response.body.total).toBe(0);
    });

    it('should return activities for the authenticated user', async () => {
      // Create test activities
      await Activity.create([
        {
          type: 'task',
          title: 'Follow up with client',
          description: 'Discuss the proposal details',
          date: new Date('2023-12-31'),
          completed: false,
          priority: 'high',
          owner: testUserId,
        },
        {
          type: 'call',
          title: 'Sales call',
          description: 'Introductory call with prospect',
          date: new Date('2023-12-15'),
          completed: true,
          completedAt: new Date('2023-12-15'),
          priority: 'medium',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/activities');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.activities.length).toBe(2);
      expect(response.body.total).toBe(2);
      
      // Check activity data
      expect(response.body.activities[0].type).toBe('task');
      expect(response.body.activities[0].title).toBe('Follow up with client');
      expect(response.body.activities[0].description).toBe('Discuss the proposal details');
      expect(response.body.activities[0].priority).toBe('high');
      expect(response.body.activities[0].completed).toBe(false);
      
      expect(response.body.activities[1].type).toBe('call');
      expect(response.body.activities[1].title).toBe('Sales call');
      expect(response.body.activities[1].description).toBe('Introductory call with prospect');
      expect(response.body.activities[1].priority).toBe('medium');
      expect(response.body.activities[1].completed).toBe(true);
      expect(response.body.activities[1].completedAt).toBeDefined();
    });

    it('should filter activities by type', async () => {
      // Create test activities with different types
      await Activity.create([
        {
          type: 'task',
          title: 'Follow up with client',
          date: new Date(),
          completed: false,
          owner: testUserId,
        },
        {
          type: 'call',
          title: 'Sales call',
          date: new Date(),
          completed: false,
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/activities?type=task');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.activities.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.activities[0].type).toBe('task');
      expect(response.body.activities[0].title).toBe('Follow up with client');
    });

    it('should filter activities by completion status', async () => {
      // Create test activities with different completion statuses
      await Activity.create([
        {
          type: 'task',
          title: 'Incomplete task',
          date: new Date(),
          completed: false,
          owner: testUserId,
        },
        {
          type: 'task',
          title: 'Completed task',
          date: new Date(),
          completed: true,
          completedAt: new Date(),
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/activities?completed=true');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.activities.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.activities[0].title).toBe('Completed task');
      expect(response.body.activities[0].completed).toBe(true);
    });
  });

  describe('POST /api/activities', () => {
    it('should create a new activity', async () => {
      const activityData: CreateActivityRequest = {
        type: 'task',
        title: 'New task',
        description: 'Task description',
        dueDate: new Date().toISOString(),
        priority: 'medium',
      };

      const response = await request(app)
        .post('/api/activities')
        .send(activityData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.activity).toBeDefined();
      expect(response.body.activity.type).toBe('task');
      expect(response.body.activity.title).toBe('New task');
      expect(response.body.activity.description).toBe('Task description');
      expect(response.body.activity.priority).toBe('medium');
      expect(response.body.activity.completed).toBe(false);
      
      // Verify activity was saved to database
      const savedActivity = await Activity.findOne({ title: 'New task' });
      expect(savedActivity).toBeDefined();
      expect(savedActivity?.type).toBe('task');
      expect(savedActivity?.title).toBe('New task');
      expect(savedActivity?.description).toBe('Task description');
    });

    it('should reject invalid activity data', async () => {
      const invalidData = {
        // Missing required type
        title: '', // Empty title
      };

      const response = await request(app)
        .post('/api/activities')
        .send(invalidData);
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PATCH /api/activities/:id', () => {
    it('should update an existing activity', async () => {
      // Create test activity
      const activity = await Activity.create({
        type: 'task',
        title: 'Original task',
        description: 'Original description',
        date: new Date(),
        completed: false,
        owner: testUserId,
      });

      const updateData: UpdateActivityRequest = {
        id: activity._id.toString(),
        title: 'Updated task',
        description: 'Updated description',
        completed: true,
      };

      const response = await request(app)
        .patch(`/api/activities/${activity._id}`)
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.activity.title).toBe('Updated task');
      expect(response.body.activity.description).toBe('Updated description');
      expect(response.body.activity.completed).toBe(true);
      expect(response.body.activity.completedAt).toBeDefined();
      
      // Verify activity was updated in database
      const updatedActivity = await Activity.findById(activity._id);
      expect(updatedActivity?.title).toBe('Updated task');
      expect(updatedActivity?.description).toBe('Updated description');
      expect(updatedActivity?.completed).toBe(true);
      expect(updatedActivity?.completedAt).toBeDefined();
    });

    it('should return 404 for non-existent activity', async () => {
      const nonExistentId = new mongoose.Types.ObjectId().toString();
      
      const updateData: UpdateActivityRequest = {
        id: nonExistentId,
        title: 'Updated task',
      };

      const response = await request(app)
        .patch(`/api/activities/${nonExistentId}`)
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PATCH /api/activities/:id/complete', () => {
    it('should mark an activity as complete', async () => {
      // Create test activity
      const activity = await Activity.create({
        type: 'task',
        title: 'Task to complete',
        date: new Date(),
        completed: false,
        owner: testUserId,
      });

      const response = await request(app)
        .patch(`/api/activities/${activity._id}/complete`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.activity.completed).toBe(true);
      expect(response.body.activity.completedAt).toBeDefined();
      
      // Verify activity was updated in database
      const updatedActivity = await Activity.findById(activity._id);
      expect(updatedActivity?.completed).toBe(true);
      expect(updatedActivity?.completedAt).toBeDefined();
    });
  });

  describe('DELETE /api/activities/:id', () => {
    it('should delete an existing activity', async () => {
      // Create test activity
      const activity = await Activity.create({
        type: 'task',
        title: 'Task to delete',
        date: new Date(),
        completed: false,
        owner: testUserId,
      });

      const response = await request(app)
        .delete(`/api/activities/${activity._id}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      // Verify activity was deleted from database
      const deletedActivity = await Activity.findById(activity._id);
      expect(deletedActivity).toBeNull();
    });

    it('should return 404 for non-existent activity', async () => {
      const nonExistentId = new mongoose.Types.ObjectId().toString();
      
      const response = await request(app)
        .delete(`/api/activities/${nonExistentId}`);
      
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });
});
