import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import express from 'express';
import session from 'express-session';
import { Opportunity } from '@models/mongoose';
import opportunityRoutes from '@server/routes/mongo-opportunities-routes';
import { CreateOpportunityRequest, UpdateOpportunityRequest } from '@types/api';

describe('Opportunities API', () => {
  let app: express.Express;
  let mongoServer: MongoMemoryServer;
  let testUserId: string;

  beforeAll(async () => {
    // Set up MongoDB memory server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create test user ID
    testUserId = new mongoose.Types.ObjectId().toString();

    // Set up Express app
    app = express();
    app.use(express.json());
    
    // Set up session middleware with mock user
    app.use(session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false,
    }));
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.session.userId = testUserId;
      next();
    });
    
    // Add opportunity routes
    app.use('/api/opportunities', opportunityRoutes);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear opportunities collection before each test
    await Opportunity.deleteMany({});
  });

  describe('GET /api/opportunities', () => {
    it('should return empty array when no opportunities exist', async () => {
      const response = await request(app).get('/api/opportunities');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.opportunities).toEqual([]);
      expect(response.body.total).toBe(0);
    });

    it('should return opportunities for the authenticated user', async () => {
      // Create test opportunities
      await Opportunity.create([
        {
          name: 'Enterprise Deal',
          value: '10000',
          currency: 'USD',
          stage: 'discovery',
          probability: '50',
          owner: testUserId,
        },
        {
          name: 'SMB Deal',
          value: '5000',
          currency: 'EUR',
          stage: 'negotiation',
          probability: '75',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/opportunities');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.opportunities.length).toBe(2);
      expect(response.body.total).toBe(2);
      
      // Check opportunity data
      expect(response.body.opportunities[0].name).toBe('Enterprise Deal');
      expect(response.body.opportunities[0].value).toBe(10000);
      expect(response.body.opportunities[0].currency).toBe('USD');
      expect(response.body.opportunities[0].stage).toBe('discovery');
      expect(response.body.opportunities[0].probability).toBe(50);
      
      expect(response.body.opportunities[1].name).toBe('SMB Deal');
      expect(response.body.opportunities[1].value).toBe(5000);
      expect(response.body.opportunities[1].currency).toBe('EUR');
      expect(response.body.opportunities[1].stage).toBe('negotiation');
      expect(response.body.opportunities[1].probability).toBe(75);
    });

    it('should filter opportunities by stage', async () => {
      // Create test opportunities with different stages
      await Opportunity.create([
        {
          name: 'Enterprise Deal',
          value: '10000',
          stage: 'discovery',
          owner: testUserId,
        },
        {
          name: 'SMB Deal',
          value: '5000',
          stage: 'negotiation',
          owner: testUserId,
        },
      ]);

      const response = await request(app).get('/api/opportunities?stage=discovery');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.opportunities.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.opportunities[0].name).toBe('Enterprise Deal');
      expect(response.body.opportunities[0].stage).toBe('discovery');
    });
  });

  describe('POST /api/opportunities', () => {
    it('should create a new opportunity', async () => {
      const opportunityData: CreateOpportunityRequest = {
        name: 'New Opportunity',
        value: 15000,
        currency: 'USD',
        stage: 'proposal',
        probability: 60,
        closeDate: new Date().toISOString(),
        notes: 'Test notes',
      };

      const response = await request(app)
        .post('/api/opportunities')
        .send(opportunityData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.opportunity).toBeDefined();
      expect(response.body.opportunity.name).toBe('New Opportunity');
      expect(response.body.opportunity.value).toBe(15000);
      expect(response.body.opportunity.currency).toBe('USD');
      expect(response.body.opportunity.stage).toBe('proposal');
      expect(response.body.opportunity.probability).toBe(60);
      
      // Verify opportunity was saved to database
      const savedOpportunity = await Opportunity.findOne({ name: 'New Opportunity' });
      expect(savedOpportunity).toBeDefined();
      expect(savedOpportunity?.name).toBe('New Opportunity');
      expect(savedOpportunity?.value).toBe('15000');
      expect(savedOpportunity?.stage).toBe('proposal');
    });

    it('should reject invalid opportunity data', async () => {
      const invalidData = {
        // Missing required name
        value: -5000, // Negative value
        stage: 'invalid-stage', // Invalid stage
      };

      const response = await request(app)
        .post('/api/opportunities')
        .send(invalidData);
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PATCH /api/opportunities/:id', () => {
    it('should update an existing opportunity', async () => {
      // Create test opportunity
      const opportunity = await Opportunity.create({
        name: 'Original Opportunity',
        value: '10000',
        stage: 'discovery',
        owner: testUserId,
      });

      const updateData: UpdateOpportunityRequest = {
        id: opportunity._id.toString(),
        name: 'Updated Opportunity',
        value: 20000,
        stage: 'negotiation',
      };

      const response = await request(app)
        .patch(`/api/opportunities/${opportunity._id}`)
        .send(updateData);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.opportunity.name).toBe('Updated Opportunity');
      expect(response.body.opportunity.value).toBe(20000);
      expect(response.body.opportunity.stage).toBe('negotiation');
      
      // Verify opportunity was updated in database
      const updatedOpportunity = await Opportunity.findById(opportunity._id);
      expect(updatedOpportunity?.name).toBe('Updated Opportunity');
      expect(updatedOpportunity?.value).toBe('20000');
      expect(updatedOpportunity?.stage).toBe('negotiation');
    });

    it('should return 404 for non-existent opportunity', async () => {
      const nonExistentId = new mongoose.Types.ObjectId().toString();
      
      const updateData: UpdateOpportunityRequest = {
        id: nonExistentId,
        name: 'Updated Opportunity',
      };

      const response = await request(app)
        .patch(`/api/opportunities/${nonExistentId}`)
        .send(updateData);
      
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/opportunities/:id', () => {
    it('should delete an existing opportunity', async () => {
      // Create test opportunity
      const opportunity = await Opportunity.create({
        name: 'Test Opportunity',
        value: '10000',
        stage: 'discovery',
        owner: testUserId,
      });

      const response = await request(app)
        .delete(`/api/opportunities/${opportunity._id}`);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      // Verify opportunity was deleted from database
      const deletedOpportunity = await Opportunity.findById(opportunity._id);
      expect(deletedOpportunity).toBeNull();
    });

    it('should return 404 for non-existent opportunity', async () => {
      const nonExistentId = new mongoose.Types.ObjectId().toString();
      
      const response = await request(app)
        .delete(`/api/opportunities/${nonExistentId}`);
      
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });
});
