import { connectToMongoDB, disconnectFromMongoDB } from '../server/mongodb-connection';
import { 
  User, 
  EmailConfig,
  IEmailConfig
} from '../server/models/mongoose';
import resendService from '../server/services/resend-service';
import emailService from '../server/services/email-service';
import mongoose from 'mongoose';
import axios from 'axios';

// Set MongoDB as enabled
process.env.MONGODB_ENABLED = 'true';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock data
const mockUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'password123',
  fullName: 'Test User'
};

const mockEmailConfig = {
  provider: 'resend',
  isEnabled: true,
  fromName: 'Test User',
  fromEmail: '<EMAIL>',
  replyToEmail: '<EMAIL>',
  apiKey: 'test_api_key',
  providerSettings: {
    trackOpens: true,
    trackLinks: true
  }
};

describe('Resend Integration', () => {
  let userId: string;

  beforeAll(async () => {
    // Connect to MongoDB
    await connectToMongoDB();

    // Create test user
    const user = new User({
      ...mockUser,
      password: await User.hashPassword(mockUser.password)
    });
    await user.save();
    userId = user._id.toString();

    // Create email config
    const config = new EmailConfig({
      ...mockEmailConfig,
      userId: user._id
    });
    await config.save();
  });

  afterAll(async () => {
    // Clean up
    await User.deleteMany({});
    await EmailConfig.deleteMany({});

    // Disconnect from MongoDB
    await disconnectFromMongoDB();
  });

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });

  it('should initialize Resend service', async () => {
    // Mock successful API response
    mockedAxios.mockResolvedValueOnce({
      status: 200,
      data: {
        data: [
          { id: 'domain1', name: 'example.com' }
        ]
      }
    });

    const result = await resendService.initialize();
    expect(result).toBe(true);
    expect(mockedAxios).toHaveBeenCalledWith({
      method: 'GET',
      url: 'https://api.resend.com/domains',
      headers: {
        'Authorization': 'Bearer ',
        'Content-Type': 'application/json'
      },
      data: undefined
    });
  });

  it('should send an email via Resend', async () => {
    // Mock successful API response
    mockedAxios.mockResolvedValueOnce({
      status: 200,
      data: {
        id: 'email_123456789',
        from: 'Test User <<EMAIL>>',
        to: ['<EMAIL>'],
        created_at: new Date().toISOString()
      }
    });

    const result = await resendService.sendEmail({
      from: 'Test User <<EMAIL>>',
      to: '<EMAIL>',
      subject: 'Test Subject',
      html: '<p>Test content</p>',
      userId,
      trackOpens: true,
      trackLinks: true
    });

    expect(result.success).toBe(true);
    expect(result.messageId).toBe('email_123456789');
    expect(result.trackingId).toBeDefined();
    expect(mockedAxios).toHaveBeenCalledWith({
      method: 'POST',
      url: 'https://api.resend.com/emails',
      headers: {
        'Authorization': 'Bearer ',
        'Content-Type': 'application/json'
      },
      data: expect.any(String)
    });

    // Check that the request data contains the expected fields
    const requestData = JSON.parse(mockedAxios.mock.calls[0][0].data);
    expect(requestData.from).toBe('Test User <<EMAIL>>');
    expect(requestData.to).toEqual(['<EMAIL>']);
    expect(requestData.subject).toBe('Test Subject');
    expect(requestData.html).toBe('<p>Test content</p>');
    expect(requestData.headers).toBeDefined();
    expect(requestData.headers['X-Aizako-Message-ID']).toBeDefined();
    expect(requestData.track_opens).toBe(true);
    expect(requestData.track_clicks).toBe(true);
  });

  it('should process a webhook open event', async () => {
    // Create a mock tracking record first
    const messageId = 'test-message-id-webhook';
    
    // Mock the findByMessageId method
    jest.spyOn(resendService as any, 'extractMessageId').mockReturnValueOnce(messageId);
    
    // Mock the emailTrackingService.findByMessageId method
    const mockTracking = {
      _id: new mongoose.Types.ObjectId(),
      pixelId: 'test-pixel-id',
      messageId,
      userId: new mongoose.Types.ObjectId(userId),
      subject: 'Test Subject',
      recipient: '<EMAIL>',
      sender: '<EMAIL>',
      status: 'sent',
      events: [{ type: 'sent', timestamp: new Date(), metadata: {} }],
      save: jest.fn().mockResolvedValueOnce(true)
    };
    
    jest.spyOn(resendService as any, 'processOpenEvent').mockResolvedValueOnce(true);

    const result = await resendService.processWebhookEvent({
      type: 'email.opened',
      data: {
        email_id: 'email_123456789',
        recipient: '<EMAIL>',
        ip: '127.0.0.1',
        user_agent: 'Mozilla/5.0',
        headers: {
          'X-Aizako-Message-ID': messageId
        },
        geo: {
          city: 'New York',
          country: 'US',
          region: 'NY'
        },
        created_at: new Date().toISOString()
      }
    });

    expect(result).toBe(true);
  });

  it('should send an email using the email service', async () => {
    // Mock the resendService.sendEmail method
    jest.spyOn(resendService, 'sendEmail').mockResolvedValueOnce({
      success: true,
      messageId: 'email_123456789',
      trackingId: 'tracking_123456789'
    });

    const result = await emailService.sendEmail(userId, {
      to: '<EMAIL>',
      subject: 'Test Subject',
      html: '<p>Test content</p>',
      trackOpens: true,
      trackLinks: true
    });

    expect(result.success).toBe(true);
    expect(result.messageId).toBe('email_123456789');
    expect(result.trackingId).toBe('tracking_123456789');
  });
});
