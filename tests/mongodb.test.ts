import { connectToMongoDB, disconnectFromMongoDB } from '../server/mongodb-connection';
import { 
  User, 
  Contact, 
  Company, 
  Opportunity, 
  Activity, 
  SubscriptionPlan 
} from '../server/models/mongoose';
import { MongoAuthService } from '../server/auth/mongo-auth-service';
import { MongoAiAdapter } from '../server/ai/mongo-ai-adapter';
import { MongoDocumentStorage } from '../server/storage/mongo-document-storage';
import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';

// Set MongoDB as enabled
process.env.MONGODB_ENABLED = 'true';

// Mock data
const mockUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'password123',
  fullName: 'Test User'
};

const mockContact = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+****************',
  title: 'CEO',
  status: 'active'
};

const mockCompany = {
  name: 'Test Company',
  domain: 'testcompany.com',
  industry: 'Technology',
  size: '51-200',
  location: 'New York, NY',
  description: 'A test company',
  website: 'https://testcompany.com',
  status: 'customer'
};

const mockOpportunity = {
  name: 'Test Opportunity',
  value: 10000,
  currency: 'USD',
  stage: 'proposal',
  probability: 70,
  expectedCloseDate: new Date()
};

// Test suite
describe('MongoDB Implementation', () => {
  let userId: string;
  let contactId: string;
  let companyId: string;
  let opportunityId: string;
  let documentStorage: MongoDocumentStorage;

  // Connect to MongoDB before tests
  beforeAll(async () => {
    try {
      await connectToMongoDB();
      console.log('Connected to MongoDB for testing');
      
      // Initialize document storage
      documentStorage = new MongoDocumentStorage();
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  });

  // Disconnect from MongoDB after tests
  afterAll(async () => {
    try {
      // Clean up test data
      await User.deleteOne({ username: mockUser.username });
      await Contact.deleteOne({ _id: contactId });
      await Company.deleteOne({ _id: companyId });
      await Opportunity.deleteOne({ _id: opportunityId });
      
      await disconnectFromMongoDB();
      console.log('Disconnected from MongoDB');
    } catch (error) {
      console.error('Failed to disconnect from MongoDB:', error);
    }
  });

  // Test authentication service
  describe('Authentication Service', () => {
    test('should register a new user', async () => {
      const user = await MongoAuthService.registerUser(mockUser);
      userId = user._id.toString();
      
      expect(user).toBeDefined();
      expect(user.username).toBe(mockUser.username);
      expect(user.email).toBe(mockUser.email);
      expect(user.password).toBeUndefined(); // Password should not be returned
    });

    test('should login a user', async () => {
      const user = await MongoAuthService.loginUser(mockUser.username, mockUser.password);
      
      expect(user).toBeDefined();
      expect(user.username).toBe(mockUser.username);
      expect(user.email).toBe(mockUser.email);
      expect(user.password).toBeUndefined(); // Password should not be returned
    });

    test('should get a user by ID', async () => {
      const user = await MongoAuthService.getUserById(userId);
      
      expect(user).toBeDefined();
      expect(user.username).toBe(mockUser.username);
      expect(user.email).toBe(mockUser.email);
    });

    test('should generate and revoke API keys', async () => {
      const apiKey = await MongoAuthService.generateApiKey(userId);
      
      expect(apiKey).toBeDefined();
      expect(typeof apiKey).toBe('string');
      
      const success = await MongoAuthService.revokeApiKey(userId, apiKey);
      
      expect(success).toBe(true);
    });
  });

  // Test contact model
  describe('Contact Model', () => {
    test('should create a new contact', async () => {
      const contact = new Contact({
        ...mockContact,
        owner: userId
      });
      
      await contact.save();
      contactId = contact._id.toString();
      
      expect(contact).toBeDefined();
      expect(contact.firstName).toBe(mockContact.firstName);
      expect(contact.lastName).toBe(mockContact.lastName);
      expect(contact.email).toBe(mockContact.email);
    });

    test('should get a contact by ID', async () => {
      const contact = await Contact.findById(contactId);
      
      expect(contact).toBeDefined();
      expect(contact?.firstName).toBe(mockContact.firstName);
      expect(contact?.lastName).toBe(mockContact.lastName);
      expect(contact?.email).toBe(mockContact.email);
    });

    test('should update a contact', async () => {
      const updatedContact = await Contact.findByIdAndUpdate(
        contactId,
        { $set: { title: 'CTO' } },
        { new: true }
      );
      
      expect(updatedContact).toBeDefined();
      expect(updatedContact?.title).toBe('CTO');
    });
  });

  // Test company model
  describe('Company Model', () => {
    test('should create a new company', async () => {
      const company = new Company({
        ...mockCompany,
        owner: userId
      });
      
      await company.save();
      companyId = company._id.toString();
      
      expect(company).toBeDefined();
      expect(company.name).toBe(mockCompany.name);
      expect(company.industry).toBe(mockCompany.industry);
      expect(company.status).toBe(mockCompany.status);
    });

    test('should get a company by ID', async () => {
      const company = await Company.findById(companyId);
      
      expect(company).toBeDefined();
      expect(company?.name).toBe(mockCompany.name);
      expect(company?.industry).toBe(mockCompany.industry);
      expect(company?.status).toBe(mockCompany.status);
    });

    test('should update a company', async () => {
      const updatedCompany = await Company.findByIdAndUpdate(
        companyId,
        { $set: { size: '201-500' } },
        { new: true }
      );
      
      expect(updatedCompany).toBeDefined();
      expect(updatedCompany?.size).toBe('201-500');
    });
  });

  // Test opportunity model
  describe('Opportunity Model', () => {
    test('should create a new opportunity', async () => {
      const opportunity = new Opportunity({
        ...mockOpportunity,
        companyId,
        contactId,
        owner: userId
      });
      
      await opportunity.save();
      opportunityId = opportunity._id.toString();
      
      expect(opportunity).toBeDefined();
      expect(opportunity.name).toBe(mockOpportunity.name);
      expect(opportunity.value).toBe(mockOpportunity.value);
      expect(opportunity.stage).toBe(mockOpportunity.stage);
    });

    test('should get an opportunity by ID', async () => {
      const opportunity = await Opportunity.findById(opportunityId);
      
      expect(opportunity).toBeDefined();
      expect(opportunity?.name).toBe(mockOpportunity.name);
      expect(opportunity?.value).toBe(mockOpportunity.value);
      expect(opportunity?.stage).toBe(mockOpportunity.stage);
    });

    test('should update an opportunity', async () => {
      const updatedOpportunity = await Opportunity.findByIdAndUpdate(
        opportunityId,
        { $set: { probability: 80 } },
        { new: true }
      );
      
      expect(updatedOpportunity).toBeDefined();
      expect(updatedOpportunity?.probability).toBe(80);
    });
  });

  // Test activity model
  describe('Activity Model', () => {
    test('should create a new activity', async () => {
      const activity = new Activity({
        type: 'call',
        title: 'Initial call with John Doe',
        description: 'Discussed their current needs and pain points',
        date: new Date(),
        duration: 30,
        completed: true,
        contactId,
        companyId,
        opportunityId,
        owner: userId
      });
      
      await activity.save();
      
      expect(activity).toBeDefined();
      expect(activity.type).toBe('call');
      expect(activity.title).toBe('Initial call with John Doe');
      expect(activity.completed).toBe(true);
    });

    test('should get activities by contact', async () => {
      const activities = await Activity.find({ contactId });
      
      expect(activities).toBeDefined();
      expect(activities.length).toBeGreaterThan(0);
      expect(activities[0].contactId.toString()).toBe(contactId);
    });
  });

  // Test document storage
  describe('Document Storage', () => {
    let documentId: string;
    
    test('should create a new document', async () => {
      // Create a test file
      const testFilePath = path.join(__dirname, 'test-file.txt');
      fs.writeFileSync(testFilePath, 'This is a test file');
      
      const fileBuffer = fs.readFileSync(testFilePath);
      
      const documentData = {
        name: 'Test Document',
        description: 'A test document',
        entityType: 'contact',
        entityId: contactId,
        contentType: 'text/plain',
        originalFilename: 'test-file.txt',
        tags: ['test', 'document'],
        uploadedBy: userId
      };
      
      const document = await documentStorage.createDocument(documentData, fileBuffer);
      documentId = document._id.toString();
      
      expect(document).toBeDefined();
      expect(document.name).toBe('Test Document');
      expect(document.description).toBe('A test document');
      expect(document.entityType).toBe('contact');
      expect(document.entityId).toBe(contactId);
      
      // Clean up test file
      fs.unlinkSync(testFilePath);
    });

    test('should get a document by ID', async () => {
      const document = await documentStorage.getDocument(documentId);
      
      expect(document).toBeDefined();
      expect(document.name).toBe('Test Document');
      expect(document.description).toBe('A test document');
    });

    test('should get documents by entity', async () => {
      const documents = await documentStorage.getDocumentsByEntity('contact', contactId);
      
      expect(documents).toBeDefined();
      expect(documents.length).toBeGreaterThan(0);
      expect(documents[0].entityType).toBe('contact');
      expect(documents[0].entityId).toBe(contactId);
    });

    test('should update a document', async () => {
      const updatedDocument = await documentStorage.updateDocument(documentId, {
        name: 'Updated Test Document',
        description: 'An updated test document'
      });
      
      expect(updatedDocument).toBeDefined();
      expect(updatedDocument.name).toBe('Updated Test Document');
      expect(updatedDocument.description).toBe('An updated test document');
    });

    test('should get document content', async () => {
      const { buffer, contentType, filename } = await documentStorage.getDocumentContent(documentId);
      
      expect(buffer).toBeDefined();
      expect(contentType).toBe('text/plain');
      expect(filename).toBe('test-file.txt');
      expect(buffer.toString()).toBe('This is a test file');
    });

    test('should delete a document', async () => {
      const success = await documentStorage.deleteDocument(documentId);
      
      expect(success).toBe(true);
      
      // Verify document is deleted
      try {
        await documentStorage.getDocument(documentId);
        fail('Document should have been deleted');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });
});
