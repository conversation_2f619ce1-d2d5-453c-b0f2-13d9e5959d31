# Production Environment Configuration

# Node Environment
NODE_ENV=production

# Firebase Configuration
VITE_FIREBASE_API_KEY="AIzaSyBcLv9RAIdjkHvwMQHqI1lcZJMI6ignL7w"
VITE_FIREBASE_PROJECT_ID="kairo-54d5e"
VITE_FIREBASE_APP_ID="1:765119006353:web:6c64c720cbebd7067d2130"
VITE_FIREBASE_STORAGE_BUCKET="kairo-54d5e.appspot.com"
VITE_FIREBASE_MESSAGING_SENDER_ID="765119006353"
VITE_FIREBASE_AUTH_DOMAIN="kairo-54d5e.firebaseapp.com"

# Google Application Credentials
GOOGLE_APPLICATION_CREDENTIALS="/opt/aizako-crm-production-current/kairo-54d5e-firebase-adminsdk.json"

# MongoDB Configuration
MONGODB_URI="mongodb+srv://jp:ShakaSenghor189!@crm.aa5qnt1.mongodb.net/"
MONGODB_ENABLED=true

# Resend Configuration
RESEND_API_KEY=re_123456789
RESEND_API_URL=https://api.resend.com
RESEND_WEBHOOK_SECRET=whsec_123456789
TRACKING_BASE_URL=https://track.aizako.com
APP_BASE_URL=https://app.aizako.com

# AI Service Configuration
AI_SERVICE_URL=http://localhost:8000
VOYAGE_API_URL=https://api.voyageai.com/v1/chat/completions
VOYAGE_API_KEY=your-voyage-api-key

# API Keys
PERPLEXITY_API_KEY="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"
ANTHROPIC_API_KEY="************************************************************************************************************"
FREEPIK_API_KEY=FPSX5863423c5b0c4bc989af4c0a9f89fa23
FREEPIK_WEBHOOK_SECRET=f9900ecd7d4e4b91a2f8646c2f42fca3
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# GCS Configuration
GCS_BUCKET_NAME="images-cms"
GCS_PROJECT_ID="cms-tempo-455314-m2"

# Server Configuration
PORT=5000
SESSION_SECRET=your-production-session-secret-here

# Redis Configuration (for AI service)
REDIS_URL=redis://localhost:6379

# AWS Configuration (for deployment)
AWS_REGION=us-east-1
S3_BUCKET=aizako-crm-deployments

# Feature Flags
AUTO_APPLY_STAGE_CHANGES=true
USE_CBOR_COMPRESSION=true
