/** @type {import('ts-jest').JestConfigWithTsJest} */
export default {
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  testMatch: [
    '**/tests/**/*.test.ts',
    '**/client/src/**/__tests__/**/*.test.tsx',
    '**/client/src/**/__tests__/**/*.test.ts'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
      jsx: 'react-jsx',
      useESM: true,
    }],
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/client/src/$1',
    '^@shared/(.*)$': '<rootDir>/shared/$1',
    '^@types/(.*)$': '<rootDir>/shared/types/$1',
    '^@schemas/(.*)$': '<rootDir>/shared/schemas/$1',
    '^@server/(.*)$': '<rootDir>/server/$1',
    '^@models/(.*)$': '<rootDir>/server/models/$1',
  },
  setupFiles: [
    '<rootDir>/tests/setup.js',
    '<rootDir>/tests/setup-react.js'
  ],
  testTimeout: 30000,
  // Configure code coverage
  collectCoverage: true,
  coverageDirectory: './coverage/integration',
  coverageReporters: ['text', 'lcov', 'json', 'html'],
  collectCoverageFrom: [
    'server/**/*.{js,ts}',
    'client/src/**/*.{js,ts,jsx,tsx}',
    '!server/**/*.d.ts',
    '!client/src/**/*.d.ts',
    '!server/**/*.test.{js,ts}',
    '!client/src/**/*.test.{js,ts,jsx,tsx}',
    '!client/src/**/__tests__/**',
    '!server/mocks/**',
    '!server/dist/**',
    '!client/dist/**',
  ],
  // Set coverage thresholds
  coverageThreshold: {
    global: {
      statements: 75,
      branches: 65,
      functions: 75,
      lines: 75,
    },
  },
};
