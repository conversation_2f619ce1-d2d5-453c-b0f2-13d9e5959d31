import { User } from '../models/mongoose';
import bcrypt from 'bcrypt';
import mongoose from 'mongoose';

/**
 * MongoDB-specific authentication service
 */
export class MongoAuthService {
  /**
   * Register a new user
   */
  static async registerUser(userData: any): Promise<any> {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ username: userData.username });
      if (existingUser) {
        throw new Error('Username already exists');
      }

      const existingEmail = await User.findOne({ email: userData.email });
      if (existingEmail) {
        throw new Error('Email already exists');
      }

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // Create user with hashed password
      const user = new User({
        ...userData,
        password: hashedPassword
      });

      await user.save();

      // Return user without password
      const userObject = user.toObject();
      delete userObject.password;
      
      return userObject;
    } catch (error: any) {
      throw new Error(`Failed to register user: ${error.message}`);
    }
  }

  /**
   * Login a user
   */
  static async loginUser(username: string, password: string): Promise<any> {
    try {
      // Find user
      const user = await User.findOne({ username });
      if (!user) {
        throw new Error('Invalid credentials');
      }

      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new Error('Invalid credentials');
      }

      // Return user without password
      const userObject = user.toObject();
      delete userObject.password;
      
      return userObject;
    } catch (error: any) {
      throw new Error(`Failed to login: ${error.message}`);
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<any> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }
      
      const user = await User.findById(userId).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error: any) {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  /**
   * Get user by username
   */
  static async getUserByUsername(username: string): Promise<any> {
    try {
      const user = await User.findOne({ username }).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error: any) {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string): Promise<any> {
    try {
      const user = await User.findOne({ email }).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error: any) {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  /**
   * Update user
   */
  static async updateUser(userId: string, userData: any): Promise<any> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }
      
      // If password is being updated, hash it
      if (userData.password) {
        const salt = await bcrypt.genSalt(10);
        userData.password = await bcrypt.hash(userData.password, salt);
      }
      
      const user = await User.findByIdAndUpdate(
        userId,
        { $set: userData },
        { new: true }
      ).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error: any) {
      throw new Error(`Failed to update user: ${error.message}`);
    }
  }

  /**
   * Update user preferences
   */
  static async updateUserPreferences(userId: string, preferences: any): Promise<any> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }
      
      const user = await User.findByIdAndUpdate(
        userId,
        { $set: { preferences } },
        { new: true }
      ).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error: any) {
      throw new Error(`Failed to update user preferences: ${error.message}`);
    }
  }

  /**
   * Delete user
   */
  static async deleteUser(userId: string): Promise<boolean> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }
      
      const result = await User.deleteOne({ _id: userId });
      
      return result.deletedCount === 1;
    } catch (error: any) {
      throw new Error(`Failed to delete user: ${error.message}`);
    }
  }

  /**
   * Generate API key for user
   */
  static async generateApiKey(userId: string): Promise<string> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }
      
      // Generate a random API key
      const apiKey = `ak_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
      
      // Add API key to user
      await User.findByIdAndUpdate(
        userId,
        { 
          $push: { 
            apiKeys: {
              key: apiKey,
              name: 'API Key',
              createdAt: new Date(),
              lastUsedAt: null
            } 
          } 
        }
      );
      
      return apiKey;
    } catch (error: any) {
      throw new Error(`Failed to generate API key: ${error.message}`);
    }
  }

  /**
   * Revoke API key for user
   */
  static async revokeApiKey(userId: string, apiKey: string): Promise<boolean> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }
      
      // Remove API key from user
      const result = await User.updateOne(
        { _id: userId },
        { $pull: { apiKeys: { key: apiKey } } }
      );
      
      return result.modifiedCount === 1;
    } catch (error: any) {
      throw new Error(`Failed to revoke API key: ${error.message}`);
    }
  }

  /**
   * Validate API key
   */
  static async validateApiKey(apiKey: string): Promise<any> {
    try {
      // Find user with API key
      const user = await User.findOne({ 'apiKeys.key': apiKey }).select('-password');
      
      if (!user) {
        throw new Error('Invalid API key');
      }
      
      // Update last used timestamp
      await User.updateOne(
        { _id: user._id, 'apiKeys.key': apiKey },
        { $set: { 'apiKeys.$.lastUsedAt': new Date() } }
      );
      
      return user;
    } catch (error: any) {
      throw new Error(`Failed to validate API key: ${error.message}`);
    }
  }
}
