/**
 * MongoDB API client for server-side API calls
 */
import { getMongoClient } from '../mongodb';
import { toObjectId } from '../utils/mongodb-utils';

/**
 * MongoDB API client for server-side API calls
 */
export const mongoApiClient = {
  /**
   * Get all contacts
   */
  async getContacts(tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const contacts = await db.collection('contacts')
      .find({ tenantId })
      .toArray();

    return contacts;
  },

  /**
   * Get contact by ID
   */
  async getContactById(id: string, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const contact = await db.collection('contacts')
      .findOne({ _id: toObjectId(id), tenantId });

    return contact;
  },

  /**
   * Create contact
   */
  async createContact(data: any, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('contacts')
      .insertOne({ ...data, tenantId, createdAt: new Date(), updatedAt: new Date() });

    return { ...data, _id: result.insertedId, tenantId };
  },

  /**
   * Update contact
   */
  async updateContact(id: string, data: any, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('contacts')
      .updateOne(
        { _id: toObjectId(id), tenantId },
        { $set: { ...data, updatedAt: new Date() } }
      );

    return result.modifiedCount > 0;
  },

  /**
   * Delete contact
   */
  async deleteContact(id: string, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('contacts')
      .deleteOne({ _id: toObjectId(id), tenantId });

    return result.deletedCount > 0;
  },

  /**
   * Get all companies
   */
  async getCompanies(tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const companies = await db.collection('companies')
      .find({ tenantId })
      .toArray();

    return companies;
  },

  /**
   * Get company by ID
   */
  async getCompanyById(id: string, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const company = await db.collection('companies')
      .findOne({ _id: toObjectId(id), tenantId });

    return company;
  },

  /**
   * Create company
   */
  async createCompany(data: any, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('companies')
      .insertOne({ ...data, tenantId, createdAt: new Date(), updatedAt: new Date() });

    return { ...data, _id: result.insertedId, tenantId };
  },

  /**
   * Update company
   */
  async updateCompany(id: string, data: any, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('companies')
      .updateOne(
        { _id: toObjectId(id), tenantId },
        { $set: { ...data, updatedAt: new Date() } }
      );

    return result.modifiedCount > 0;
  },

  /**
   * Delete company
   */
  async deleteCompany(id: string, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('companies')
      .deleteOne({ _id: toObjectId(id), tenantId });

    return result.deletedCount > 0;
  },

  /**
   * Get all opportunities
   */
  async getOpportunities(tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const opportunities = await db.collection('opportunities')
      .find({ tenantId })
      .toArray();

    return opportunities;
  },

  /**
   * Get opportunity by ID
   */
  async getOpportunityById(id: string, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const opportunity = await db.collection('opportunities')
      .findOne({ _id: toObjectId(id), tenantId });

    return opportunity;
  },

  /**
   * Create opportunity
   */
  async createOpportunity(data: any, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('opportunities')
      .insertOne({ ...data, tenantId, createdAt: new Date(), updatedAt: new Date() });

    return { ...data, _id: result.insertedId, tenantId };
  },

  /**
   * Update opportunity
   */
  async updateOpportunity(id: string, data: any, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('opportunities')
      .updateOne(
        { _id: toObjectId(id), tenantId },
        { $set: { ...data, updatedAt: new Date() } }
      );

    return result.modifiedCount > 0;
  },

  /**
   * Delete opportunity
   */
  async deleteOpportunity(id: string, tenantId: string) {
    const client = await getMongoClient();
    const db = client.db();

    const result = await db.collection('opportunities')
      .deleteOne({ _id: toObjectId(id), tenantId });

    return result.deletedCount > 0;
  }
};
