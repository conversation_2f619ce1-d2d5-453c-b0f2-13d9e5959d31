/**
 * Repository implementation for Objection model
 */
import mongoose from 'mongoose';
import { BaseMongoRepository } from './repository';
import { Objection } from '../models/mongoose';
import { ObjectionDocument } from '../@types/mongoose-types';
import { toObjectId } from '../utils/mongodb-utils';
import { logger } from '../utils/logger';

/**
 * Objection entity interface
 */
export interface ObjectionEntity {
  id: string;
  name: string;
  category: string;
  description: string;
  tags: string[];
  isCommon: boolean;
  createdBy: string;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for Objection model
 */
export class ObjectionRepository extends BaseMongoRepository<ObjectionEntity, ObjectionDocument> {
  /**
   * Create a new ObjectionRepository
   */
  constructor() {
    super(Objection, 'Objection');
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected toEntity(doc: ObjectionDocument | null): ObjectionEntity | null {
    if (!doc) return null;

    return {
      id: doc._id.toString(),
      name: doc.name,
      category: doc.category,
      description: doc.description,
      tags: doc.tags || [],
      isCommon: doc.isCommon || false,
      createdBy: doc.createdBy.toString(),
      customFields: doc.customFields,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    };
  }

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected toDocument(entity: Partial<ObjectionEntity>): Partial<ObjectionDocument> {
    const doc: any = { ...entity };

    // Convert id to _id if present
    if (entity.id) {
      doc._id = toObjectId(entity.id);
      delete doc.id;
    }

    // Convert createdBy to ObjectId if present
    if (entity.createdBy) {
      doc.createdBy = toObjectId(entity.createdBy);
    }

    return doc;
  }

  /**
   * Find objections by category
   * @param category Category to filter by
   * @param tenantId Tenant ID
   * @returns Array of objections
   */
  async findByCategory(category: string, tenantId?: string): Promise<ObjectionEntity[]> {
    try {
      return this.findAll({ category }, tenantId);
    } catch (error) {
      logger.error(`Error finding objections by category ${category}:`, error);
      throw error;
    }
  }

  /**
   * Find common objections
   * @param tenantId Tenant ID
   * @returns Array of common objections
   */
  async findCommonObjections(tenantId?: string): Promise<ObjectionEntity[]> {
    try {
      return this.findAll({ isCommon: true }, tenantId);
    } catch (error) {
      logger.error('Error finding common objections:', error);
      throw error;
    }
  }

  /**
   * Find objections by text search
   * @param searchText Text to search for
   * @param tenantId Tenant ID
   * @returns Array of objections
   */
  async findByTextSearch(searchText: string, tenantId?: string): Promise<ObjectionEntity[]> {
    try {
      const filter: any = { $text: { $search: searchText } };
      if (tenantId) {
        filter.tenantId = tenantId;
      }

      const docs = await this.model.find(filter)
        .sort({ score: { $meta: 'textScore' } })
        .exec();

      return docs.map(doc => this.toEntity(doc as ObjectionDocument))
        .filter((entity): entity is ObjectionEntity => entity !== null);
    } catch (error) {
      logger.error(`Error finding objections by text search "${searchText}":`, error);
      throw error;
    }
  }

  /**
   * Find objections created by a user
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Array of objections
   */
  async findByCreatedBy(userId: string, tenantId?: string): Promise<ObjectionEntity[]> {
    try {
      return this.findAll({ createdBy: toObjectId(userId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding objections created by user ${userId}:`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const objectionRepository = new ObjectionRepository();
