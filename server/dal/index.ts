/**
 * Data Access Layer (DAL) exports
 *
 * This file exports all repository interfaces and implementations.
 */

// Export repository interface and base class
export * from './repository';

// Export entity repositories
export * from './objection-repository';
export * from './objection-response-repository';
export * from './contact-repository';
export * from './company-repository';
export * from './opportunity-repository';
export * from './ai-chat-repository';
export * from './activity-repository';

// Add more repository exports as they are created

// Export repository instances
import { ObjectionRepository } from './objection-repository';
import { ObjectionResponseRepository } from './objection-response-repository';
import { ContactRepository } from './contact-repository';
import { CompanyRepository } from './company-repository';
import { OpportunityRepository } from './opportunity-repository';
import { AiChatRepository } from './ai-chat-repository';
import { ActivityRepository } from './activity-repository';

// Create repository instances
export const objectionRepository = new ObjectionRepository();
export const objectionResponseRepository = new ObjectionResponseRepository();
export const contactRepository = new ContactRepository();
export const companyRepository = new CompanyRepository();
export const opportunityRepository = new OpportunityRepository();
export const aiChatRepository = new AiChatRepository();
export const activityRepository = new ActivityRepository();
