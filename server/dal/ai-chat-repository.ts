/**
 * Repository implementation for AiChat model
 *
 * This repository provides data access methods for AI chat conversations.
 * It follows the repository pattern to abstract the data access layer from business logic.
 */
import mongoose from 'mongoose';
import { BaseMongoRepository } from './repository';
import { AiChat } from '../models/mongoose';
import { MongoDocument } from '../@types/mongoose-types';
import { toObjectId } from '../utils/mongodb-utils';
import { logger } from '../utils/logger';
import { IAiChat, IMessage } from '../models/mongoose/ai-chat-model';

/**
 * Message entity interface
 */
export interface MessageEntity {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

/**
 * AiChat entity interface
 */
export interface AiChatEntity {
  id: string;
  userId: string;
  title: string;
  messages: MessageEntity[];
  context?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for AiChat model
 */
export class AiChatRepository extends BaseMongoRepository<AiChatEntity, mongoose.Document & IAiChat> {
  /**
   * Create a new AiChatRepository
   */
  constructor() {
    super(AiChat, 'AiChat');
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected toEntity(doc: (mongoose.Document & IAiChat) | null): AiChatEntity | null {
    if (!doc) return null;

    return {
      id: doc._id.toString(),
      userId: doc.userId.toString(),
      title: doc.title,
      messages: doc.messages.map(message => ({
        role: message.role,
        content: message.content,
        timestamp: message.timestamp
      })),
      context: doc.context,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    };
  }

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected toDocument(entity: Partial<AiChatEntity>): Partial<AiChatDocument> {
    const doc: any = {
      title: entity.title,
      messages: entity.messages?.map(message => ({
        role: message.role,
        content: message.content,
        timestamp: message.timestamp
      })),
      context: entity.context
    };

    if (entity.id) {
      doc._id = toObjectId(entity.id);
    }

    if (entity.userId) {
      doc.userId = toObjectId(entity.userId);
    }

    return doc;
  }

  /**
   * Find chats by user ID
   * @param userId User ID
   * @returns Array of chats
   */
  async findByUserId(userId: string): Promise<AiChatEntity[]> {
    try {
      const docs = await this.model.find({ userId: toObjectId(userId) })
        .sort({ updatedAt: -1 })
        .exec();

      return docs.map(doc => this.toEntity(doc)).filter(Boolean) as AiChatEntity[];
    } catch (error) {
      logger.error(`Error finding chats by user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Add a message to a chat
   * @param chatId Chat ID
   * @param message Message to add
   * @returns Updated chat
   */
  async addMessage(chatId: string, message: MessageEntity): Promise<AiChatEntity | null> {
    try {
      const doc = await this.model.findByIdAndUpdate(
        toObjectId(chatId),
        {
          $push: { messages: message },
          $set: { updatedAt: new Date() }
        },
        { new: true }
      ).exec();

      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error adding message to chat ${chatId}:`, error);
      throw error;
    }
  }

  /**
   * Search chats by title or content
   * @param userId User ID
   * @param query Search query
   * @returns Array of matching chats
   */
  async search(userId: string, query: string): Promise<AiChatEntity[]> {
    try {
      const docs = await this.model.find({
        userId: toObjectId(userId),
        $text: { $search: query }
      })
      .sort({ score: { $meta: 'textScore' } })
      .exec();

      return docs.map(doc => this.toEntity(doc)).filter(Boolean) as AiChatEntity[];
    } catch (error) {
      logger.error(`Error searching chats for user ${userId} with query "${query}":`, error);
      throw error;
    }
  }

  /**
   * Update chat title
   * @param chatId Chat ID
   * @param title New title
   * @returns Updated chat
   */
  async updateTitle(chatId: string, title: string): Promise<AiChatEntity | null> {
    try {
      const doc = await this.model.findByIdAndUpdate(
        toObjectId(chatId),
        { $set: { title, updatedAt: new Date() } },
        { new: true }
      ).exec();

      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error updating title for chat ${chatId}:`, error);
      throw error;
    }
  }

  /**
   * Update chat context
   * @param chatId Chat ID
   * @param context New context
   * @returns Updated chat
   */
  async updateContext(chatId: string, context: Record<string, any>): Promise<AiChatEntity | null> {
    try {
      const doc = await this.model.findByIdAndUpdate(
        toObjectId(chatId),
        { $set: { context, updatedAt: new Date() } },
        { new: true }
      ).exec();

      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error updating context for chat ${chatId}:`, error);
      throw error;
    }
  }
}

// Instance is created in the index.ts file
