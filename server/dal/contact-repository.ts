/**
 * Repository implementation for Contact model
 */
import mongoose from 'mongoose';
import { BaseMongoRepository } from './repository';
import { Contact } from '../models/mongoose';
import { ContactDocument } from '../@types/mongoose-types';
import { toObjectId } from '../utils/mongodb-utils';
import { logger } from '../utils/logger';

/**
 * Contact entity interface
 */
export interface ContactEntity {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  company?: string;
  companyId?: string;
  status: string;
  source?: string;
  owner?: string;
  tags: string[];
  notes?: string;
  lastActivity?: Date;
  customFields?: Record<string, any>;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for Contact model
 */
export class ContactRepository extends BaseMongoRepository<ContactEntity, ContactDocument> {
  /**
   * Create a new ContactRepository
   */
  constructor() {
    super(Contact, 'Contact');
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected toEntity(doc: ContactDocument | null): ContactEntity | null {
    if (!doc) return null;

    return {
      id: doc._id.toString(),
      firstName: doc.firstName,
      lastName: doc.lastName,
      email: doc.email,
      phone: doc.phone,
      title: doc.title,
      company: doc.company,
      companyId: doc.companyId ? doc.companyId.toString() : undefined,
      status: doc.status,
      source: doc.source,
      owner: doc.owner ? doc.owner.toString() : undefined,
      tags: doc.tags || [],
      notes: doc.notes,
      lastActivity: doc.lastActivity,
      customFields: doc.customFields,
      tenantId: doc.tenantId,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    };
  }

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected toDocument(entity: Partial<ContactEntity>): Partial<ContactDocument> {
    const doc: any = { ...entity };

    // Convert id to _id if present
    if (entity.id) {
      doc._id = toObjectId(entity.id);
      delete doc.id;
    }

    // Convert string IDs to ObjectIds
    if (entity.companyId) {
      doc.companyId = toObjectId(entity.companyId);
    }

    if (entity.owner) {
      doc.owner = toObjectId(entity.owner);
    }

    return doc;
  }

  /**
   * Find contacts by email
   * @param email Email to search for
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async findByEmail(email: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return this.findAll({ email }, tenantId);
    } catch (error) {
      logger.error(`Error finding contacts by email ${email}:`, error);
      throw error;
    }
  }

  /**
   * Find contacts by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async findByCompanyId(companyId: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return this.findAll({ companyId: toObjectId(companyId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding contacts by company ID ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Find contacts by status
   * @param status Status to filter by
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async findByStatus(status: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return this.findAll({ status }, tenantId);
    } catch (error) {
      logger.error(`Error finding contacts by status ${status}:`, error);
      throw error;
    }
  }

  /**
   * Find contacts by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async findByOwner(ownerId: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return this.findAll({ owner: toObjectId(ownerId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding contacts by owner ${ownerId}:`, error);
      throw error;
    }
  }

  /**
   * Find contacts by tag
   * @param tag Tag to filter by
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async findByTag(tag: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return this.findAll({ tags: tag }, tenantId);
    } catch (error) {
      logger.error(`Error finding contacts by tag ${tag}:`, error);
      throw error;
    }
  }

  /**
   * Search contacts by name or email
   * @param query Search query
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async search(query: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      const filter = {
        $or: [
          { firstName: { $regex: query, $options: 'i' } },
          { lastName: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } }
        ],
        tenantId
      };

      const docs = await this.model.find(filter).exec();
      return docs.map(doc => this.toEntity(doc as ContactDocument))
        .filter((entity): entity is ContactEntity => entity !== null);
    } catch (error) {
      logger.error(`Error searching contacts with query "${query}":`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const contactRepository = new ContactRepository();
