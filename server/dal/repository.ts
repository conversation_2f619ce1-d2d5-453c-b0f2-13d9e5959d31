/**
 * Repository interface for data access
 *
 * This interface defines the standard operations to be performed on a model.
 * It provides a consistent API for data access across the application.
 */

import mongoose from 'mongoose';
import { MongoDocument, MongoFilter } from '../@types/mongoose-types';
import { handleMongoDBError, DocumentNotFoundError } from '../utils/mongodb-errors';
import { logger } from '../utils/logger';

/**
 * Repository interface for data access
 * @template T The entity type
 */
export interface IRepository<T> {
  /**
   * Find an entity by ID
   * @param id Entity ID
   * @param tenantId Tenant ID for multi-tenancy
   * @returns Promise resolving to the entity or null if not found
   */
  findById(id: string, tenantId?: string): Promise<T | null>;

  /**
   * Find all entities matching a filter
   * @param filter Filter criteria
   * @param tenantId Tenant ID for multi-tenancy
   * @param options Query options (pagination, sorting)
   * @returns Promise resolving to an array of entities
   */
  findAll(
    filter?: Record<string, any>,
    tenantId?: string,
    options?: {
      skip?: number;
      limit?: number;
      sort?: Record<string, 1 | -1>;
    }
  ): Promise<T[]>;

  /**
   * Count entities matching a filter
   * @param filter Filter criteria
   * @param tenantId Tenant ID for multi-tenancy
   * @returns Promise resolving to the count
   */
  count(filter?: Record<string, any>, tenantId?: string): Promise<number>;

  /**
   * Create a new entity
   * @param data Entity data
   * @returns Promise resolving to the created entity
   */
  create(data: Partial<T>): Promise<T>;

  /**
   * Update an entity
   * @param id Entity ID
   * @param data Entity data to update
   * @param tenantId Tenant ID for multi-tenancy
   * @returns Promise resolving to the updated entity or null if not found
   */
  update(id: string, data: Partial<T>, tenantId?: string): Promise<T | null>;

  /**
   * Delete an entity
   * @param id Entity ID
   * @param tenantId Tenant ID for multi-tenancy
   * @returns Promise resolving to true if deleted, false if not found
   */
  delete(id: string, tenantId?: string): Promise<boolean>;

  /**
   * Find one entity matching a filter
   * @param filter Filter criteria
   * @param tenantId Tenant ID for multi-tenancy
   * @returns Promise resolving to the entity or null if not found
   */
  findOne(filter: Record<string, any>, tenantId?: string): Promise<T | null>;
}

/**
 * Base MongoDB repository implementation
 * @template T The entity type
 * @template D The document type
 */
export abstract class BaseMongoRepository<T, D extends mongoose.Document> implements IRepository<T> {
  /**
   * Mongoose model
   */
  protected model: mongoose.Model<D>;

  /**
   * Entity name for logging
   */
  protected entityName: string;

  /**
   * Create a new MongoDB repository
   * @param model Mongoose model
   * @param entityName Entity name for logging
   */
  constructor(model: mongoose.Model<D>, entityName: string) {
    this.model = model;
    this.entityName = entityName;
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected abstract toEntity(doc: D | null): T | null;

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected abstract toDocument(entity: Partial<T>): Partial<D>;

  /**
   * Add tenant filter to a query
   * @param filter Filter to add tenant filter to
   * @param tenantId Tenant ID
   * @returns Filter with tenant filter
   */
  protected addTenantFilter(filter: Record<string, any>, tenantId?: string): Record<string, any> {
    if (!tenantId) return filter;
    return { ...filter, tenantId };
  }

  /**
   * Find an entity by ID
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns Entity or null if not found
   */
  async findById(id: string, tenantId?: string): Promise<T | null> {
    try {
      const filter: any = { _id: new mongoose.Types.ObjectId(id) };
      if (tenantId) {
        filter.tenantId = tenantId;
      }

      const doc = await this.model.findOne(filter);
      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error finding ${this.entityName} by ID ${id}:`, error);
      handleMongoDBError(error, this.entityName);
    }
  }

  /**
   * Find all entities matching a filter
   * @param filter Filter criteria
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of entities
   */
  async findAll(
    filter: Record<string, any> = {},
    tenantId?: string,
    options: {
      skip?: number;
      limit?: number;
      sort?: Record<string, 1 | -1>;
    } = {}
  ): Promise<T[]> {
    try {
      const query = this.model.find(this.addTenantFilter(filter, tenantId));

      if (options.skip !== undefined) {
        query.skip(options.skip);
      }

      if (options.limit !== undefined) {
        query.limit(options.limit);
      }

      if (options.sort) {
        query.sort(options.sort);
      }

      const docs = await query.exec();
      return docs.map(doc => this.toEntity(doc)).filter((entity): entity is T => entity !== null);
    } catch (error) {
      logger.error(`Error finding ${this.entityName} entities:`, error);
      handleMongoDBError(error, this.entityName);
    }
  }

  /**
   * Count entities matching a filter
   * @param filter Filter criteria
   * @param tenantId Tenant ID
   * @returns Count
   */
  async count(filter: Record<string, any> = {}, tenantId?: string): Promise<number> {
    try {
      return await this.model.countDocuments(this.addTenantFilter(filter, tenantId));
    } catch (error) {
      logger.error(`Error counting ${this.entityName} entities:`, error);
      handleMongoDBError(error, this.entityName);
    }
  }

  /**
   * Create a new entity
   * @param data Entity data
   * @returns Created entity
   */
  async create(data: Partial<T>): Promise<T> {
    try {
      const doc = new this.model(this.toDocument(data));
      const savedDoc = await doc.save();
      const entity = this.toEntity(savedDoc);

      if (!entity) {
        throw new Error(`Failed to create ${this.entityName}`);
      }

      return entity;
    } catch (error) {
      logger.error(`Error creating ${this.entityName}:`, error);
      handleMongoDBError(error, this.entityName);
    }
  }

  /**
   * Update an entity
   * @param id Entity ID
   * @param data Entity data to update
   * @param tenantId Tenant ID
   * @returns Updated entity or null if not found
   */
  async update(id: string, data: Partial<T>, tenantId?: string): Promise<T | null> {
    try {
      const filter: any = { _id: new mongoose.Types.ObjectId(id) };
      if (tenantId) {
        filter.tenantId = tenantId;
      }

      const doc = await this.model.findOneAndUpdate(
        filter,
        this.toDocument(data),
        { new: true }
      );

      if (!doc) {
        logger.warn(`${this.entityName} with ID ${id} not found for update`);
        return null;
      }

      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error updating ${this.entityName} with ID ${id}:`, error);
      handleMongoDBError(error, this.entityName);
    }
  }

  /**
   * Delete an entity
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string, tenantId?: string): Promise<boolean> {
    try {
      const filter: any = { _id: new mongoose.Types.ObjectId(id) };
      if (tenantId) {
        filter.tenantId = tenantId;
      }

      const result = await this.model.deleteOne(filter);

      if (result.deletedCount === 0) {
        logger.warn(`${this.entityName} with ID ${id} not found for deletion`);
      }

      return result.deletedCount > 0;
    } catch (error) {
      logger.error(`Error deleting ${this.entityName} with ID ${id}:`, error);
      handleMongoDBError(error, this.entityName);
    }
  }

  /**
   * Find one entity matching a filter
   * @param filter Filter criteria
   * @param tenantId Tenant ID
   * @returns Entity or null if not found
   */
  async findOne(filter: Record<string, any>, tenantId?: string): Promise<T | null> {
    try {
      const doc = await this.model.findOne(this.addTenantFilter(filter, tenantId));
      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error finding ${this.entityName} with filter:`, filter, error);
      handleMongoDBError(error, this.entityName);
    }
  }

  /**
   * Find an entity by ID and throw an error if not found
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns Entity
   * @throws DocumentNotFoundError if the entity is not found
   */
  async findByIdOrThrow(id: string, tenantId?: string): Promise<T> {
    const entity = await this.findById(id, tenantId);

    if (!entity) {
      logger.warn(`${this.entityName} with ID ${id} not found`);
      throw new DocumentNotFoundError(this.entityName, id);
    }

    return entity;
  }

  /**
   * Find one entity matching a filter and throw an error if not found
   * @param filter Filter criteria
   * @param tenantId Tenant ID
   * @returns Entity
   * @throws DocumentNotFoundError if the entity is not found
   */
  async findOneOrThrow(filter: Record<string, any>, tenantId?: string): Promise<T> {
    const entity = await this.findOne(filter, tenantId);

    if (!entity) {
      logger.warn(`${this.entityName} not found with filter:`, filter);
      throw new DocumentNotFoundError(this.entityName, JSON.stringify(filter));
    }

    return entity;
  }
}
