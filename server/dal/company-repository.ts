/**
 * Repository implementation for Company model
 */
import mongoose from 'mongoose';
import { BaseMongoRepository } from './repository';
import { Company } from '../models/mongoose';
import { CompanyDocument } from '../@types/mongoose-types';
import { toObjectId } from '../utils/mongodb-utils';
import { logger } from '../utils/logger';

/**
 * Company entity interface
 */
export interface CompanyEntity {
  id: string;
  name: string;
  domain?: string;
  industry?: string;
  size?: string;
  location?: string;
  description?: string;
  website?: string;
  status: string;
  owner?: string;
  tags: string[];
  notes?: string;
  lastActivity?: Date;
  customFields?: Record<string, any>;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for Company model
 */
export class CompanyRepository extends BaseMongoRepository<CompanyEntity, CompanyDocument> {
  /**
   * Create a new CompanyRepository
   */
  constructor() {
    super(Company, 'Company');
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected toEntity(doc: CompanyDocument | null): CompanyEntity | null {
    if (!doc) return null;

    return {
      id: doc._id.toString(),
      name: doc.name,
      domain: doc.domain,
      industry: doc.industry,
      size: doc.size,
      location: doc.location,
      description: doc.description,
      website: doc.website,
      status: doc.status,
      owner: doc.owner ? doc.owner.toString() : undefined,
      tags: doc.tags || [],
      notes: doc.notes,
      lastActivity: doc.lastActivity,
      customFields: doc.customFields,
      tenantId: doc.tenantId,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    };
  }

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected toDocument(entity: Partial<CompanyEntity>): Partial<CompanyDocument> {
    const doc: any = { ...entity };

    // Convert id to _id if present
    if (entity.id) {
      doc._id = toObjectId(entity.id);
      delete doc.id;
    }

    // Convert string IDs to ObjectIds
    if (entity.owner) {
      doc.owner = toObjectId(entity.owner);
    }

    return doc;
  }

  /**
   * Find companies by domain
   * @param domain Domain to search for
   * @param tenantId Tenant ID
   * @returns Array of companies
   */
  async findByDomain(domain: string, tenantId: string): Promise<CompanyEntity[]> {
    try {
      return this.findAll({ domain }, tenantId);
    } catch (error) {
      logger.error(`Error finding companies by domain ${domain}:`, error);
      throw error;
    }
  }

  /**
   * Find companies by industry
   * @param industry Industry to filter by
   * @param tenantId Tenant ID
   * @returns Array of companies
   */
  async findByIndustry(industry: string, tenantId: string): Promise<CompanyEntity[]> {
    try {
      return this.findAll({ industry }, tenantId);
    } catch (error) {
      logger.error(`Error finding companies by industry ${industry}:`, error);
      throw error;
    }
  }

  /**
   * Find companies by status
   * @param status Status to filter by
   * @param tenantId Tenant ID
   * @returns Array of companies
   */
  async findByStatus(status: string, tenantId: string): Promise<CompanyEntity[]> {
    try {
      return this.findAll({ status }, tenantId);
    } catch (error) {
      logger.error(`Error finding companies by status ${status}:`, error);
      throw error;
    }
  }

  /**
   * Find companies by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @returns Array of companies
   */
  async findByOwner(ownerId: string, tenantId: string): Promise<CompanyEntity[]> {
    try {
      return this.findAll({ owner: toObjectId(ownerId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding companies by owner ${ownerId}:`, error);
      throw error;
    }
  }

  /**
   * Find companies by tag
   * @param tag Tag to filter by
   * @param tenantId Tenant ID
   * @returns Array of companies
   */
  async findByTag(tag: string, tenantId: string): Promise<CompanyEntity[]> {
    try {
      return this.findAll({ tags: tag }, tenantId);
    } catch (error) {
      logger.error(`Error finding companies by tag ${tag}:`, error);
      throw error;
    }
  }

  /**
   * Search companies by name or domain
   * @param query Search query
   * @param tenantId Tenant ID
   * @returns Array of companies
   */
  async search(query: string, tenantId: string): Promise<CompanyEntity[]> {
    try {
      const filter = {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { domain: { $regex: query, $options: 'i' } }
        ],
        tenantId
      };

      const docs = await this.model.find(filter).exec();
      return docs.map(doc => this.toEntity(doc as CompanyDocument))
        .filter((entity): entity is CompanyEntity => entity !== null);
    } catch (error) {
      logger.error(`Error searching companies with query "${query}":`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const companyRepository = new CompanyRepository();
