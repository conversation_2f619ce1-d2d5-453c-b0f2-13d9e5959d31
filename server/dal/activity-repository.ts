/**
 * Repository implementation for Activity model
 * 
 * This repository provides data access methods for activities.
 * It follows the repository pattern to abstract the data access layer from business logic.
 */
import mongoose from 'mongoose';
import { BaseMongoRepository } from './repository';
import { Activity } from '../models/mongoose';
import { toObjectId } from '../utils/mongodb-utils';
import { logger } from '../utils/logger';
import { IActivity } from '../models/mongoose/activity-model';

/**
 * Activity entity interface
 */
export interface ActivityEntity {
  id: string;
  title: string;
  type: string;
  description?: string;
  timestamp: Date;
  createdBy?: string;
  contactId?: string;
  companyId?: string;
  opportunityId?: string;
  customFields?: Record<string, any>;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for Activity model
 */
export class ActivityRepository extends BaseMongoRepository<ActivityEntity, mongoose.Document & IActivity> {
  /**
   * Create a new ActivityRepository
   */
  constructor() {
    super(Activity, 'Activity');
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected toEntity(doc: (mongoose.Document & IActivity) | null): ActivityEntity | null {
    if (!doc) return null;

    return {
      id: doc._id.toString(),
      title: doc.title,
      type: doc.type,
      description: doc.description,
      timestamp: doc.timestamp,
      createdBy: doc.createdBy?.toString(),
      contactId: doc.contactId?.toString(),
      companyId: doc.companyId?.toString(),
      opportunityId: doc.opportunityId?.toString(),
      customFields: doc.customFields,
      tenantId: doc.tenantId?.toString() || '',
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    };
  }

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected toDocument(entity: Partial<ActivityEntity>): Partial<mongoose.Document & IActivity> {
    const doc: any = {
      title: entity.title,
      type: entity.type,
      description: entity.description,
      timestamp: entity.timestamp,
      customFields: entity.customFields
    };

    if (entity.id) {
      doc._id = toObjectId(entity.id);
    }

    if (entity.createdBy) {
      doc.createdBy = toObjectId(entity.createdBy);
    }

    if (entity.contactId) {
      doc.contactId = toObjectId(entity.contactId);
    }

    if (entity.companyId) {
      doc.companyId = toObjectId(entity.companyId);
    }

    if (entity.opportunityId) {
      doc.opportunityId = toObjectId(entity.opportunityId);
    }

    if (entity.tenantId) {
      doc.tenantId = toObjectId(entity.tenantId);
    }

    return doc;
  }

  /**
   * Find activities by contact ID
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @returns Array of activities
   */
  async findByContactId(contactId: string, tenantId: string): Promise<ActivityEntity[]> {
    try {
      const docs = await this.model.find({
        contactId: toObjectId(contactId),
        tenantId: toObjectId(tenantId)
      })
      .sort({ timestamp: -1 })
      .exec();
      
      return docs.map(doc => this.toEntity(doc)).filter(Boolean) as ActivityEntity[];
    } catch (error) {
      logger.error(`Error finding activities by contact ID ${contactId}:`, error);
      throw error;
    }
  }

  /**
   * Find activities by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @returns Array of activities
   */
  async findByCompanyId(companyId: string, tenantId: string): Promise<ActivityEntity[]> {
    try {
      const docs = await this.model.find({
        companyId: toObjectId(companyId),
        tenantId: toObjectId(tenantId)
      })
      .sort({ timestamp: -1 })
      .exec();
      
      return docs.map(doc => this.toEntity(doc)).filter(Boolean) as ActivityEntity[];
    } catch (error) {
      logger.error(`Error finding activities by company ID ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Find activities by opportunity ID
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Array of activities
   */
  async findByOpportunityId(opportunityId: string, tenantId: string): Promise<ActivityEntity[]> {
    try {
      const docs = await this.model.find({
        opportunityId: toObjectId(opportunityId),
        tenantId: toObjectId(tenantId)
      })
      .sort({ timestamp: -1 })
      .exec();
      
      return docs.map(doc => this.toEntity(doc)).filter(Boolean) as ActivityEntity[];
    } catch (error) {
      logger.error(`Error finding activities by opportunity ID ${opportunityId}:`, error);
      throw error;
    }
  }

  /**
   * Find activities by type
   * @param type Activity type
   * @param tenantId Tenant ID
   * @returns Array of activities
   */
  async findByType(type: string, tenantId: string): Promise<ActivityEntity[]> {
    try {
      const docs = await this.model.find({
        type,
        tenantId: toObjectId(tenantId)
      })
      .sort({ timestamp: -1 })
      .exec();
      
      return docs.map(doc => this.toEntity(doc)).filter(Boolean) as ActivityEntity[];
    } catch (error) {
      logger.error(`Error finding activities by type ${type}:`, error);
      throw error;
    }
  }

  /**
   * Find recent activities
   * @param tenantId Tenant ID
   * @param limit Maximum number of activities to return
   * @returns Array of activities
   */
  async findRecent(tenantId: string, limit: number = 10): Promise<ActivityEntity[]> {
    try {
      const docs = await this.model.find({
        tenantId: toObjectId(tenantId)
      })
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
      
      return docs.map(doc => this.toEntity(doc)).filter(Boolean) as ActivityEntity[];
    } catch (error) {
      logger.error(`Error finding recent activities:`, error);
      throw error;
    }
  }
}
