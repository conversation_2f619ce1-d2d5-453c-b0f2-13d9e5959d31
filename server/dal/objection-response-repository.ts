/**
 * Repository implementation for ObjectionResponse model
 */
import mongoose from 'mongoose';
import { BaseMongoRepository } from './repository';
import { ObjectionResponse } from '../models/mongoose';
import { ObjectionResponseDocument } from '../@types/mongoose-types';
import { toObjectId } from '../utils/mongodb-utils';
import { logger } from '../utils/logger';

/**
 * ObjectionResponse entity interface
 */
export interface ObjectionResponseEntity {
  id: string;
  objectionId: string;
  response: string;
  context: string;
  effectiveness: number;
  usedCount: number;
  successCount: number;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
  isAIGenerated: boolean;
  createdBy: string;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for ObjectionResponse model
 */
export class ObjectionResponseRepository extends BaseMongoRepository<ObjectionResponseEntity, ObjectionResponseDocument> {
  /**
   * Create a new ObjectionResponseRepository
   */
  constructor() {
    super(ObjectionResponse, 'ObjectionResponse');
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected toEntity(doc: ObjectionResponseDocument | null): ObjectionResponseEntity | null {
    if (!doc) return null;

    return {
      id: doc._id.toString(),
      objectionId: doc.objectionId.toString(),
      response: doc.response,
      context: doc.context || '',
      effectiveness: doc.effectiveness || 0,
      usedCount: doc.usedCount || 0,
      successCount: doc.successCount || 0,
      opportunityId: doc.opportunityId ? doc.opportunityId.toString() : undefined,
      contactId: doc.contactId ? doc.contactId.toString() : undefined,
      companyId: doc.companyId ? doc.companyId.toString() : undefined,
      isAIGenerated: doc.isAIGenerated || false,
      createdBy: doc.createdBy.toString(),
      customFields: doc.customFields,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    };
  }

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected toDocument(entity: Partial<ObjectionResponseEntity>): Partial<ObjectionResponseDocument> {
    const doc: any = { ...entity };

    // Convert id to _id if present
    if (entity.id) {
      doc._id = toObjectId(entity.id);
      delete doc.id;
    }

    // Convert string IDs to ObjectIds
    if (entity.objectionId) {
      doc.objectionId = toObjectId(entity.objectionId);
    }

    if (entity.opportunityId) {
      doc.opportunityId = toObjectId(entity.opportunityId);
    }

    if (entity.contactId) {
      doc.contactId = toObjectId(entity.contactId);
    }

    if (entity.companyId) {
      doc.companyId = toObjectId(entity.companyId);
    }

    if (entity.createdBy) {
      doc.createdBy = toObjectId(entity.createdBy);
    }

    return doc;
  }

  /**
   * Find responses for an objection
   * @param objectionId Objection ID
   * @param tenantId Tenant ID
   * @returns Array of responses
   */
  async findByObjectionId(objectionId: string, tenantId?: string): Promise<ObjectionResponseEntity[]> {
    try {
      return this.findAll({ objectionId: toObjectId(objectionId) }, tenantId, {
        sort: { effectiveness: -1, usedCount: -1 }
      });
    } catch (error) {
      logger.error(`Error finding responses for objection ${objectionId}:`, error);
      throw error;
    }
  }

  /**
   * Find responses by effectiveness
   * @param minEffectiveness Minimum effectiveness
   * @param tenantId Tenant ID
   * @returns Array of responses
   */
  async findByEffectiveness(minEffectiveness: number, tenantId?: string): Promise<ObjectionResponseEntity[]> {
    try {
      return this.findAll({ effectiveness: { $gte: minEffectiveness } }, tenantId, {
        sort: { effectiveness: -1 }
      });
    } catch (error) {
      logger.error(`Error finding responses with effectiveness >= ${minEffectiveness}:`, error);
      throw error;
    }
  }

  /**
   * Find AI-generated responses
   * @param tenantId Tenant ID
   * @returns Array of responses
   */
  async findAIGenerated(tenantId?: string): Promise<ObjectionResponseEntity[]> {
    try {
      return this.findAll({ isAIGenerated: true }, tenantId);
    } catch (error) {
      logger.error('Error finding AI-generated responses:', error);
      throw error;
    }
  }

  /**
   * Find responses for an opportunity
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Array of responses
   */
  async findByOpportunityId(opportunityId: string, tenantId?: string): Promise<ObjectionResponseEntity[]> {
    try {
      return this.findAll({ opportunityId: toObjectId(opportunityId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding responses for opportunity ${opportunityId}:`, error);
      throw error;
    }
  }

  /**
   * Record response usage
   * @param id Response ID
   * @param wasSuccessful Whether the response was successful
   * @param tenantId Tenant ID
   * @returns Updated response
   */
  async recordUsage(id: string, wasSuccessful: boolean = false, tenantId?: string): Promise<ObjectionResponseEntity | null> {
    try {
      const filter: any = { _id: toObjectId(id) };
      if (tenantId) {
        filter.tenantId = tenantId;
      }

      const update: any = { $inc: { usedCount: 1 } };
      if (wasSuccessful) {
        update.$inc.successCount = 1;
      }

      const doc = await this.model.findOneAndUpdate(filter, update, { new: true });
      return this.toEntity(doc as ObjectionResponseDocument);
    } catch (error) {
      logger.error(`Error recording usage for response ${id}:`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const objectionResponseRepository = new ObjectionResponseRepository();
