/**
 * Repository implementation for Opportunity model
 */
import mongoose from 'mongoose';
import { BaseMongoRepository } from './repository';
import { Opportunity } from '../models/mongoose';
import { OpportunityDocument } from '../@types/mongoose-types';
import { toObjectId } from '../utils/mongodb-utils';
import { logger } from '../utils/logger';

/**
 * Opportunity entity interface
 */
export interface OpportunityEntity {
  id: string;
  name: string;
  value: number;
  currency: string;
  stage: string;
  probability: number;
  expectedCloseDate?: Date;
  actualCloseDate?: Date;
  description?: string;
  contactId?: string;
  companyId?: string;
  owner?: string;
  tags: string[];
  notes?: string;
  lastActivity?: Date;
  customFields?: Record<string, any>;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for Opportunity model
 */
export class OpportunityRepository extends BaseMongoRepository<OpportunityEntity, OpportunityDocument> {
  /**
   * Create a new OpportunityRepository
   */
  constructor() {
    super(Opportunity, 'Opportunity');
  }

  /**
   * Convert a document to an entity
   * @param doc Document to convert
   * @returns Entity
   */
  protected toEntity(doc: OpportunityDocument | null): OpportunityEntity | null {
    if (!doc) return null;

    return {
      id: doc._id.toString(),
      name: doc.name,
      value: doc.value,
      currency: doc.currency,
      stage: doc.stage,
      probability: doc.probability,
      expectedCloseDate: doc.expectedCloseDate,
      actualCloseDate: doc.actualCloseDate,
      description: doc.description,
      contactId: doc.contactId ? doc.contactId.toString() : undefined,
      companyId: doc.companyId ? doc.companyId.toString() : undefined,
      owner: doc.owner ? doc.owner.toString() : undefined,
      tags: doc.tags || [],
      notes: doc.notes,
      lastActivity: doc.lastActivity,
      customFields: doc.customFields,
      tenantId: doc.tenantId,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    };
  }

  /**
   * Convert an entity to a document
   * @param entity Entity to convert
   * @returns Document
   */
  protected toDocument(entity: Partial<OpportunityEntity>): Partial<OpportunityDocument> {
    const doc: any = { ...entity };

    // Convert id to _id if present
    if (entity.id) {
      doc._id = toObjectId(entity.id);
      delete doc.id;
    }

    // Convert string IDs to ObjectIds
    if (entity.contactId) {
      doc.contactId = toObjectId(entity.contactId);
    }

    if (entity.companyId) {
      doc.companyId = toObjectId(entity.companyId);
    }

    if (entity.owner) {
      doc.owner = toObjectId(entity.owner);
    }

    return doc;
  }

  /**
   * Find opportunities by stage
   * @param stage Stage to filter by
   * @param tenantId Tenant ID
   * @returns Array of opportunities
   */
  async findByStage(stage: string, tenantId: string): Promise<OpportunityEntity[]> {
    try {
      return this.findAll({ stage }, tenantId);
    } catch (error) {
      logger.error(`Error finding opportunities by stage ${stage}:`, error);
      throw error;
    }
  }

  /**
   * Find opportunities by contact ID
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @returns Array of opportunities
   */
  async findByContactId(contactId: string, tenantId: string): Promise<OpportunityEntity[]> {
    try {
      return this.findAll({ contactId: toObjectId(contactId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding opportunities by contact ID ${contactId}:`, error);
      throw error;
    }
  }

  /**
   * Find opportunities by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @returns Array of opportunities
   */
  async findByCompanyId(companyId: string, tenantId: string): Promise<OpportunityEntity[]> {
    try {
      return this.findAll({ companyId: toObjectId(companyId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding opportunities by company ID ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Find opportunities by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @returns Array of opportunities
   */
  async findByOwner(ownerId: string, tenantId: string): Promise<OpportunityEntity[]> {
    try {
      return this.findAll({ owner: toObjectId(ownerId) }, tenantId);
    } catch (error) {
      logger.error(`Error finding opportunities by owner ${ownerId}:`, error);
      throw error;
    }
  }

  /**
   * Find opportunities by expected close date range
   * @param startDate Start date
   * @param endDate End date
   * @param tenantId Tenant ID
   * @returns Array of opportunities
   */
  async findByExpectedCloseDateRange(
    startDate: Date,
    endDate: Date,
    tenantId: string
  ): Promise<OpportunityEntity[]> {
    try {
      return this.findAll({
        expectedCloseDate: {
          $gte: startDate,
          $lte: endDate
        }
      }, tenantId);
    } catch (error) {
      logger.error(`Error finding opportunities by expected close date range:`, error);
      throw error;
    }
  }

  /**
   * Find opportunities by value range
   * @param minValue Minimum value
   * @param maxValue Maximum value
   * @param tenantId Tenant ID
   * @returns Array of opportunities
   */
  async findByValueRange(
    minValue: number,
    maxValue: number,
    tenantId: string
  ): Promise<OpportunityEntity[]> {
    try {
      return this.findAll({
        value: {
          $gte: minValue,
          $lte: maxValue
        }
      }, tenantId);
    } catch (error) {
      logger.error(`Error finding opportunities by value range:`, error);
      throw error;
    }
  }

  /**
   * Find opportunities by probability range
   * @param minProbability Minimum probability
   * @param maxProbability Maximum probability
   * @param tenantId Tenant ID
   * @returns Array of opportunities
   */
  async findByProbabilityRange(
    minProbability: number,
    maxProbability: number,
    tenantId: string
  ): Promise<OpportunityEntity[]> {
    try {
      return this.findAll({
        probability: {
          $gte: minProbability,
          $lte: maxProbability
        }
      }, tenantId);
    } catch (error) {
      logger.error(`Error finding opportunities by probability range:`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const opportunityRepository = new OpportunityRepository();
