import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Interface for chart specification
 */
export interface IChartSpec {
  type: string; // e.g., 'bar', 'line', 'pie', 'funnel', 'sankey', 'cohort'
  data: any;
  options: Record<string, any>;
}

/**
 * Interface for suggested experiment
 */
export interface ISuggestedExperiment {
  title: string;
  description: string;
  estimated_impact: string;
  confidence: number; // 0-100
  workflow_template?: Record<string, any>;
}

/**
 * Interface for CBI cache
 */
export interface ICBICache extends Document {
  tenant_id: mongoose.Types.ObjectId;
  question: string;
  question_hash: string;
  result_json: Record<string, any>;
  chart_spec: IChartSpec;
  narrative: string;
  root_cause_analysis?: string;
  suggested_experiments?: ISuggestedExperiment[];
  dataset_ref: string;
  query_type: string; // 'sql', 'cypher', 'vector'
  raw_query?: string;
  execution_time_ms: number;
  created_at: Date;
  expires_at: Date;
}

/**
 * Schema for chart specification
 */
const ChartSpecSchema = new Schema<IChartSpec>({
  type: {
    type: String,
    required: true,
    enum: ['bar', 'line', 'pie', 'funnel', 'sankey', 'cohort', 'forecast', 'anomaly_detection', 'what_if_analysis', 'trend_prediction'],
  },
  data: {
    type: Schema.Types.Mixed,
    required: true,
  },
  options: {
    type: Schema.Types.Mixed,
    default: {},
  },
});

/**
 * Schema for suggested experiment
 */
const SuggestedExperimentSchema = new Schema<ISuggestedExperiment>({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  estimated_impact: {
    type: String,
    required: true,
  },
  confidence: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
  },
  workflow_template: {
    type: Schema.Types.Mixed,
  },
});

/**
 * Schema for CBI cache
 */
const CBICacheSchema = new Schema<ICBICache>(
  {
    tenant_id: {
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
      required: true,
      index: true,
    },
    question: {
      type: String,
      required: true,
    },
    question_hash: {
      type: String,
      required: true,
      index: true,
    },
    result_json: {
      type: Schema.Types.Mixed,
      required: true,
    },
    chart_spec: {
      type: ChartSpecSchema,
      required: true,
    },
    narrative: {
      type: String,
      required: true,
    },
    root_cause_analysis: {
      type: String,
    },
    suggested_experiments: {
      type: [SuggestedExperimentSchema],
      default: [],
    },
    dataset_ref: {
      type: String,
      required: true,
    },
    query_type: {
      type: String,
      required: true,
      enum: ['sql', 'cypher', 'vector'],
    },
    raw_query: {
      type: String,
    },
    execution_time_ms: {
      type: Number,
      required: true,
      min: 0,
    },
    created_at: {
      type: Date,
      default: Date.now,
    },
    expires_at: {
      type: Date,
      required: true,
      index: true,
    },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: false,
    },
  }
);

// Compound indexes for efficient querying
CBICacheSchema.index({ tenant_id: 1, question_hash: 1 });
CBICacheSchema.index({ tenant_id: 1, dataset_ref: 1 });
CBICacheSchema.index({ tenant_id: 1, created_at: -1 });

// Create and export the model
export const CBICache: Model<ICBICache> = 
  mongoose.models.CBICache || 
  mongoose.model<ICBICache>('CBICache', CBICacheSchema);

export default CBICache;
