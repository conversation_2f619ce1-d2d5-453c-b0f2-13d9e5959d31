import mongoose, { Schema, Document, Model } from 'mongoose';
import { fromObjectId, toObjectId } from '../../utils/mongodb-utils';

/**
 * Interaction interface for Smart Interaction Timeline
 */
export interface IInteraction {
  id: string;
  type: 'email' | 'call' | 'meeting' | 'chat' | 'social' | 'note' | 'task' | 'other';
  source: string;
  sourceId?: string;
  timestamp: Date;
  summary: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  direction?: 'inbound' | 'outbound';
  participants?: Array<{
    id?: string;
    email?: string;
    name?: string;
    role?: string;
  }>;
  content?: {
    text?: string;
    html?: string;
    attachments?: Array<{
      name: string;
      type: string;
      url?: string;
    }>;
  };
  metadata?: Record<string, any>;
  nextAction?: {
    type: 'email' | 'call' | 'meeting' | 'task' | 'other';
    description: string;
    dueDate?: Date;
    priority?: 'low' | 'medium' | 'high';
  };
  aiGenerated: boolean;
  aiConfidence?: number;
}

export interface IContact extends Document {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  title?: string;
  companyId?: mongoose.Types.ObjectId;
  status: string;
  source?: string;
  owner: mongoose.Types.ObjectId;
  tags: string[];
  notes?: string;
  lastContactedAt?: Date;
  customFields: Record<string, any>;
  // AI enrichment data
  aiEnrichment?: Record<string, any>;
  // Created by user reference
  createdBy?: mongoose.Types.ObjectId;
  // Smart Interaction Timeline
  interactions?: IInteraction[];
  // AI-generated persona
  persona?: {
    summary?: string;
    communicationPreferences?: {
      preferredChannel?: 'email' | 'phone' | 'in-person' | 'video';
      bestTimeToContact?: string;
      responseTime?: 'fast' | 'medium' | 'slow';
    };
    interests?: string[];
    painPoints?: string[];
    decisionFactors?: string[];
    aiConfidence?: number;
    lastUpdated?: Date;
  };
  // Graph-based predictive lead scoring
  score?: {
    current: number;
    previous?: number;
    change?: number;
    lastUpdated: Date;
    factors: {
      interactions: number;
      sentiment: number;
      opportunities: number;
      engagement: number;
      recency: number;
      similarContacts: number;
    };
    conversionProbability: number;
    history?: Array<{
      value: number;
      timestamp: Date;
      factors?: Record<string, number>;
    }>;
  };
  createdAt: Date;
  updatedAt: Date;
}

const ContactSchema = new Schema<IContact>({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    index: true
  },
  phone: {
    type: String,
    trim: true
  },
  title: {
    type: String,
    trim: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'lead', 'customer', 'prospect'],
    default: 'active',
    index: true
  },
  source: {
    type: String,
    trim: true
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  notes: {
    type: String
  },
  lastContactedAt: {
    type: Date
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  },
  // AI enrichment data
  aiEnrichment: {
    type: Schema.Types.Mixed
  },
  // Created by user reference
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  // Smart Interaction Timeline
  interactions: [{
    id: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: ['email', 'call', 'meeting', 'chat', 'social', 'note', 'task', 'other']
    },
    source: { type: String, required: true },
    sourceId: { type: String },
    timestamp: { type: Date, required: true, index: true },
    summary: { type: String, required: true },
    sentiment: {
      type: String,
      enum: ['positive', 'neutral', 'negative']
    },
    direction: {
      type: String,
      enum: ['inbound', 'outbound']
    },
    participants: [{
      id: { type: String },
      email: { type: String },
      name: { type: String },
      role: { type: String }
    }],
    content: {
      text: { type: String },
      html: { type: String },
      attachments: [{
        name: { type: String, required: true },
        type: { type: String, required: true },
        url: { type: String }
      }]
    },
    metadata: { type: Schema.Types.Mixed, default: {} },
    nextAction: {
      type: {
        type: String,
        enum: ['email', 'call', 'meeting', 'task', 'other']
      },
      description: { type: String },
      dueDate: { type: Date },
      priority: {
        type: String,
        enum: ['low', 'medium', 'high']
      }
    },
    aiGenerated: { type: Boolean, required: true, default: false },
    aiConfidence: { type: Number, min: 0, max: 1 }
  }],
  // AI-generated persona
  persona: {
    summary: { type: String },
    communicationPreferences: {
      preferredChannel: {
        type: String,
        enum: ['email', 'phone', 'in-person', 'video']
      },
      bestTimeToContact: { type: String },
      responseTime: {
        type: String,
        enum: ['fast', 'medium', 'slow']
      }
    },
    interests: [{ type: String }],
    painPoints: [{ type: String }],
    decisionFactors: [{ type: String }],
    aiConfidence: { type: Number, min: 0, max: 1 },
    lastUpdated: { type: Date, default: Date.now }
  },
  // Graph-based predictive lead scoring
  score: {
    current: { type: Number, min: 0, max: 100 },
    previous: { type: Number, min: 0, max: 100 },
    change: { type: Number },
    lastUpdated: { type: Date },
    factors: {
      interactions: { type: Number, min: 0, max: 100, default: 0 },
      sentiment: { type: Number, min: 0, max: 100, default: 0 },
      opportunities: { type: Number, min: 0, max: 100, default: 0 },
      engagement: { type: Number, min: 0, max: 100, default: 0 },
      recency: { type: Number, min: 0, max: 100, default: 0 },
      similarContacts: { type: Number, min: 0, max: 100, default: 0 }
    },
    conversionProbability: { type: Number, min: 0, max: 100, default: 0 },
    history: [{
      value: { type: Number, min: 0, max: 100, required: true },
      timestamp: { type: Date, required: true },
      factors: { type: Schema.Types.Mixed }
    }]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full name
ContactSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Compound index for name search
ContactSchema.index({ firstName: 'text', lastName: 'text', email: 'text' });

// Index for interactions
ContactSchema.index({ 'interactions.timestamp': -1 });
ContactSchema.index({ 'interactions.type': 1, 'interactions.timestamp': -1 });
ContactSchema.index({ 'interactions.source': 1, 'interactions.sourceId': 1 });
ContactSchema.index({ 'interactions.sentiment': 1 });

// Index for scoring
ContactSchema.index({ 'score.current': -1 });
ContactSchema.index({ 'score.lastUpdated': -1 });

// Type for converting MongoDB document to shared type
export function mapContactToType(doc: IContact): any {
  return {
    id: fromObjectId(doc._id),
    firstName: doc.firstName,
    lastName: doc.lastName,
    fullName: `${doc.firstName} ${doc.lastName}`,
    email: doc.email,
    phone: doc.phone,
    title: doc.title,
    companyId: doc.companyId ? fromObjectId(doc.companyId) : undefined,
    status: doc.status,
    source: doc.source,
    owner: fromObjectId(doc.owner),
    tags: doc.tags,
    notes: doc.notes,
    lastContactedAt: doc.lastContactedAt?.toISOString(),
    customFields: doc.customFields,
    aiEnrichment: doc.aiEnrichment,
    createdBy: doc.createdBy ? fromObjectId(doc.createdBy) : undefined,
    interactions: doc.interactions,
    persona: doc.persona,
    score: doc.score,
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

// Create and export the model
export const Contact: Model<IContact> = mongoose.models.Contact || mongoose.model<IContact>('Contact', ContactSchema);

export default Contact;
