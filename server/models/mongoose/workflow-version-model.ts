import mongoose, { Document, Schema } from 'mongoose';
import { IWorkflowNode, IWorkflowEdge } from './workflow-model';

/**
 * Interface for Workflow Version Document
 */
export interface IWorkflowVersion extends Document {
  workflow_id: mongoose.Types.ObjectId;
  tenant_id: string;
  version: number;
  dsl_yaml: string;
  nodes: IWorkflowNode[];
  edges: IWorkflowEdge[];
  created_by: string;
  created_at: Date;
  comment?: string;
}

/**
 * Schema for Workflow Version
 */
const WorkflowVersionSchema = new Schema<IWorkflowVersion>(
  {
    workflow_id: {
      type: Schema.Types.ObjectId,
      ref: 'Workflow',
      required: true,
      index: true,
    },
    tenant_id: {
      type: String,
      required: true,
      index: true,
    },
    version: {
      type: Number,
      required: true,
    },
    dsl_yaml: {
      type: String,
      required: true,
    },
    nodes: [{
      id: String,
      type: {
        type: String,
        enum: ['trigger', 'action', 'condition'],
        required: true,
      },
      position: {
        x: Number,
        y: Number,
      },
      data: {
        type: Schema.Types.Mixed,
        required: true,
      },
    }],
    edges: [{
      id: String,
      source: String,
      target: String,
      label: String,
      condition: Schema.Types.Mixed,
    }],
    created_by: {
      type: String,
      required: true,
    },
    comment: {
      type: String,
    },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: false, // No updates allowed for versions
    },
  }
);

// Create a compound index for workflow_id and version
WorkflowVersionSchema.index({ workflow_id: 1, version: 1 }, { unique: true });

// Create and export the WorkflowVersion model
export const WorkflowVersion = mongoose.model<IWorkflowVersion>('WorkflowVersion', WorkflowVersionSchema);
