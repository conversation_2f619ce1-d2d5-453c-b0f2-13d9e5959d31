import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Forecast Factor Types
 */
export enum ForecastFactorType {
  STAKEHOLDER = 'stakeholder',
  SENTIMENT = 'sentiment',
  ACTIVITY = 'activity',
  STAGE_DURATION = 'stage_duration',
  COMPETITOR = 'competitor',
  BUDGET = 'budget',
  DECISION_MAKER = 'decision_maker',
  LEGAL_REVIEW = 'legal_review',
  TECHNICAL_REVIEW = 'technical_review',
  SIMILAR_DEALS = 'similar_deals',
  SEASONALITY = 'seasonality',
  CUSTOM = 'custom'
}

/**
 * Forecast Factor Interface
 */
export interface IForecastFactor {
  type: ForecastFactorType;
  name: string;
  impact: number; // -100 to 100, negative means negative impact
  description: string;
  metadata?: Record<string, any>;
}

/**
 * Deal Forecast Interface
 */
export interface IDealForecast extends Document {
  opportunityId: mongoose.Types.ObjectId;
  tenantId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  forecastDate: Date;
  predictedCloseDate: Date;
  originalCloseDate: Date;
  predictedAmount: number;
  originalAmount: number;
  currency: string;
  confidence: number; // 0-100
  probability: number; // 0-100
  factors: IForecastFactor[];
  slipReason?: string;
  riskLevel: 'low' | 'medium' | 'high';
  suggestedActions?: string[];
  previousForecasts?: {
    forecastDate: Date;
    predictedCloseDate: Date;
    predictedAmount: number;
    confidence: number;
    probability: number;
  }[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Team Forecast Interface
 */
export interface ITeamForecast extends Document {
  tenantId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  period: string; // e.g., 'Q2 2023', 'May 2023'
  startDate: Date;
  endDate: Date;
  forecastDate: Date;
  predictedAmount: number;
  targetAmount: number;
  currency: string;
  confidence: number; // 0-100
  dealCount: number;
  atRiskAmount: number;
  atRiskDealCount: number;
  upside: number;
  bestCaseAmount: number;
  worstCaseAmount: number;
  byStage: {
    stage: string;
    amount: number;
    dealCount: number;
    probability: number;
  }[];
  byRep: {
    userId: mongoose.Types.ObjectId;
    amount: number;
    dealCount: number;
    confidence: number;
    atRiskAmount: number;
    atRiskDealCount: number;
  }[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Forecast Factor Schema
 */
const ForecastFactorSchema = new Schema<IForecastFactor>({
  type: {
    type: String,
    required: true,
    enum: Object.values(ForecastFactorType)
  },
  name: {
    type: String,
    required: true
  },
  impact: {
    type: Number,
    required: true,
    min: -100,
    max: 100
  },
  description: {
    type: String,
    required: true
  },
  metadata: {
    type: Schema.Types.Mixed
  }
});

/**
 * Deal Forecast Schema
 */
const DealForecastSchema = new Schema<IDealForecast>({
  opportunityId: {
    type: Schema.Types.ObjectId,
    ref: 'Opportunity',
    required: true,
    index: true
  },
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  forecastDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  predictedCloseDate: {
    type: Date,
    required: true
  },
  originalCloseDate: {
    type: Date,
    required: true
  },
  predictedAmount: {
    type: Number,
    required: true
  },
  originalAmount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  confidence: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  probability: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  factors: [ForecastFactorSchema],
  slipReason: {
    type: String
  },
  riskLevel: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  suggestedActions: [{
    type: String
  }],
  previousForecasts: [{
    forecastDate: {
      type: Date,
      required: true
    },
    predictedCloseDate: {
      type: Date,
      required: true
    },
    predictedAmount: {
      type: Number,
      required: true
    },
    confidence: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    },
    probability: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    }
  }],
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

/**
 * Team Forecast Schema
 */
const TeamForecastSchema = new Schema<ITeamForecast>({
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  period: {
    type: String,
    required: true,
    index: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  forecastDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  predictedAmount: {
    type: Number,
    required: true
  },
  targetAmount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  confidence: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  dealCount: {
    type: Number,
    required: true,
    default: 0
  },
  atRiskAmount: {
    type: Number,
    required: true,
    default: 0
  },
  atRiskDealCount: {
    type: Number,
    required: true,
    default: 0
  },
  upside: {
    type: Number,
    required: true,
    default: 0
  },
  bestCaseAmount: {
    type: Number,
    required: true
  },
  worstCaseAmount: {
    type: Number,
    required: true
  },
  byStage: [{
    stage: {
      type: String,
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    dealCount: {
      type: Number,
      required: true
    },
    probability: {
      type: Number,
      required: true
    }
  }],
  byRep: [{
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    dealCount: {
      type: Number,
      required: true
    },
    confidence: {
      type: Number,
      required: true
    },
    atRiskAmount: {
      type: Number,
      required: true
    },
    atRiskDealCount: {
      type: Number,
      required: true
    }
  }],
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Indexes
DealForecastSchema.index({ opportunityId: 1, forecastDate: -1 });
DealForecastSchema.index({ userId: 1, forecastDate: -1 });
DealForecastSchema.index({ predictedCloseDate: 1 });
DealForecastSchema.index({ confidence: 1 });
DealForecastSchema.index({ riskLevel: 1 });

TeamForecastSchema.index({ tenantId: 1, period: 1 }, { unique: true });
TeamForecastSchema.index({ startDate: 1, endDate: 1 });
TeamForecastSchema.index({ forecastDate: -1 });

// Create and export the models
export const DealForecast: Model<IDealForecast> = mongoose.models.DealForecast || 
  mongoose.model<IDealForecast>('DealForecast', DealForecastSchema);

export const TeamForecast: Model<ITeamForecast> = mongoose.models.TeamForecast || 
  mongoose.model<ITeamForecast>('TeamForecast', TeamForecastSchema);

export default { DealForecast, TeamForecast };
