// Export all mongoose models
export { User } from './user-model';
export type { IUser } from './user-model';
export { Contact } from './contact-model';
export type { IContact } from './contact-model';
export { Company } from './company-model';
export type { ICompany } from './company-model';
export { Opportunity } from './opportunity-model';
export type { IOpportunity } from './opportunity-model';
export { Activity } from './activity-model';
export type { IActivity } from './activity-model';
export { Interaction } from './interaction-model';
export type { IInteraction } from './interaction-model';
export { Relationship } from './relationship-model';
export type { IRelationship } from './relationship-model';
export { AiChat } from './ai-chat-model';
export { Insight } from './insight-model';
export type { IInsight } from './insight-model';
export { DocumentModel } from './document-model';
export { Tag } from './tag-model';
export type { ITag } from './tag-model';
export { Task } from './task-model';
export type { ITask } from './task-model';
export { Notification } from './notification-model';
export type { INotification } from './notification-model';
export { Objection } from './objection-model';
export type { IObjection } from './objection-model';
export { ObjectionResponse } from './objection-response-model';
export type { IObjectionResponse } from './objection-response-model';
export { StageTransition } from './stage-transition-model';
export type { IStageTransition } from './stage-transition-model';
export { ProposalTemplate } from './proposal-template-model';
export type { IProposalTemplate } from './proposal-template-model';
export { Proposal } from './proposal-model';
export type { IProposal } from './proposal-model';
export { FollowUp, FollowUpTemplate } from './follow-up-model';
export type { IFollowUp, IFollowUpTemplate } from './follow-up-model';
export { WinLossAnalysis, WinLossFactor } from './win-loss-model';
export type { IWinLossAnalysis, IWinLossFactor } from './win-loss-model';
export { EmailConfig } from './email-config-model';
export type { IEmailConfig } from './email-config-model';
export { ProposalAnalytics } from './proposal-analytics-model';
export type { IProposalAnalytics } from './proposal-analytics-model';
export { EmailTracking, EmailEventType } from './email-tracking-model';
export type { IEmailTracking, IEmailEvent } from './email-tracking-model';
export { EmailTemplate } from './email-template-model';
export type { IEmailTemplate } from './email-template-model';
export { Sequence, SequenceStep, SequenceEnrollment, SequenceStepChannelType, SequenceStepTimingType } from './sequence-model';
export type { ISequence, ISequenceStep, ISequenceEnrollment } from './sequence-model';
export { DealForecast, TeamForecast, ForecastFactorType } from './forecast-model';
export { TenantDomain, DNSRecordType, DomainVerificationStatus } from './tenant-domain-model';
export { Edge } from './edge-model';
export type { IEdge } from './edge-model';

// Export analytics & reporting models
export { EventsRaw } from './events-raw-model';
export type { IEventsRaw } from './events-raw-model';
export { AttributionResults } from './attribution-results-model';
export { CBICache } from './cbi-cache-model';
export { AnalyticsDataset } from './analytics-dataset-model';
export { MarketingCampaign } from './marketing-campaign-model';

// Export workflow automation models
export {
  Workflow,
  WorkflowTriggerType,
  WorkflowActionType,
  WorkflowConditionType
} from './workflow-model';

export type {
  IWorkflow,
  IWorkflowNode,
  IWorkflowEdge,
  IWorkflowTrigger,
  IWorkflowAction,
  IWorkflowCondition
} from './workflow-model';
export { WorkflowRun } from './workflow-run-model';
export type { IWorkflowRun, IWorkflowStepExecution } from './workflow-run-model';
export { WorkflowVersion } from './workflow-version-model';
export type { IWorkflowVersion } from './workflow-version-model';
export { InsightsCache } from './insights-cache-model';

// Export subscription models
export {
  SubscriptionPlan,
  Tenant,
  TenantSubscription,
  Feature,
  TenantUsage,
  SubscriptionEvent,
  UserTenant
} from './subscription-models';

export type {
  ISubscriptionPlan,
  ITenant,
  ITenantSubscription,
  IFeature,
  ITenantUsage,
  ISubscriptionEvent,
  IUserTenant
} from './subscription-models';

// Export a function to initialize all models
export async function initializeModels() {
  // Import all models to ensure they are registered with Mongoose
  await Promise.all([
    import('./user-model'),
    import('./contact-model'),
    import('./company-model'),
    import('./opportunity-model'),
    import('./activity-model'),
    import('./interaction-model'),
    import('./relationship-model'),
    import('./ai-chat-model'),
    import('./insight-model'),
    import('./document-model'),
    import('./tag-model'),
    import('./task-model'),
    import('./notification-model'),
    import('./objection-model'),
    import('./objection-response-model'),
    import('./stage-transition-model'),
    import('./proposal-template-model'),
    import('./proposal-model'),
    import('./follow-up-model'),
    import('./win-loss-model'),
    import('./email-config-model'),
    import('./proposal-analytics-model'),
    import('./email-tracking-model'),
    import('./sequence-model'),
    import('./forecast-model'),
    import('./tenant-domain-model'),
    import('./edge-model'),
    import('./subscription-models'),
    // Import analytics & reporting models
    import('./events-raw-model'),
    import('./attribution-results-model'),
    import('./cbi-cache-model'),
    import('./analytics-dataset-model'),
    import('./marketing-campaign-model'),
    // Import workflow automation models
    import('./workflow-model'),
    import('./workflow-run-model'),
    import('./workflow-version-model'),
    import('./insights-cache-model')
  ]);

  console.log('All Mongoose models initialized');
}
