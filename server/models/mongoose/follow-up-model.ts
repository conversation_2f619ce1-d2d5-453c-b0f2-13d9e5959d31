import mongoose, { Document, Model, Schema } from 'mongoose';

// Follow-up model interface
export interface IFollowUp extends Document {
  title: string;
  description?: string;
  opportunityId?: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  activityId?: mongoose.Types.ObjectId;
  scheduledDate: Date;
  completedDate?: Date;
  status: 'pending' | 'completed' | 'cancelled';
  type: 'email' | 'call' | 'meeting' | 'task' | 'other';
  priority: 'low' | 'medium' | 'high';
  template?: string;
  content?: string;
  reminderDate?: Date;
  isAIGenerated: boolean;
  createdBy: mongoose.Types.ObjectId;
  tags: string[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Follow-up template interface
export interface IFollowUpTemplate extends Document {
  name: string;
  description?: string;
  category: string;
  type: 'email' | 'call' | 'meeting' | 'task' | 'other';
  content: string;
  variables: string[];
  isDefault: boolean;
  createdBy: mongoose.Types.ObjectId;
  tags: string[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Follow-up schema
const FollowUpSchema = new Schema<IFollowUp>({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  opportunityId: {
    type: Schema.Types.ObjectId,
    ref: 'Opportunity',
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    index: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  activityId: {
    type: Schema.Types.ObjectId,
    ref: 'Activity',
    index: true
  },
  scheduledDate: {
    type: Date,
    required: true,
    index: true
  },
  completedDate: {
    type: Date
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'completed', 'cancelled'],
    default: 'pending',
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['email', 'call', 'meeting', 'task', 'other'],
    default: 'email',
    index: true
  },
  priority: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high'],
    default: 'medium',
    index: true
  },
  template: {
    type: String
  },
  content: {
    type: String
  },
  reminderDate: {
    type: Date,
    index: true
  },
  isAIGenerated: {
    type: Boolean,
    required: true,
    default: false
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
FollowUpSchema.index({ title: 'text', description: 'text', content: 'text' });

// Follow-up template schema
const FollowUpTemplateSchema = new Schema<IFollowUpTemplate>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  category: {
    type: String,
    required: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['email', 'call', 'meeting', 'task', 'other'],
    default: 'email',
    index: true
  },
  content: {
    type: String,
    required: true
  },
  variables: [{
    type: String
  }],
  isDefault: {
    type: Boolean,
    default: false,
    index: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
FollowUpTemplateSchema.index({ name: 'text', description: 'text', content: 'text' });

// Create and export the models
export const FollowUp: Model<IFollowUp> = mongoose.models.FollowUp || mongoose.model<IFollowUp>('FollowUp', FollowUpSchema);
export const FollowUpTemplate: Model<IFollowUpTemplate> = mongoose.models.FollowUpTemplate || mongoose.model<IFollowUpTemplate>('FollowUpTemplate', FollowUpTemplateSchema);

export default { FollowUp, FollowUpTemplate };
