import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Interface for attribution results
 */
export interface IAttributionResults extends Document {
  tenant_id: mongoose.Types.ObjectId;
  window: string; // e.g., 'last_30_days', 'last_90_days', 'current_quarter'
  model_type: string; // e.g., 'markov', 'shapley'
  channel: string;
  campaign?: string;
  medium?: string;
  source?: string;
  creative?: string;
  keyword?: string;
  credit_pct: number; // Percentage of credit (0-100)
  cost: number; // Cost in currency
  revenue: number; // Revenue attributed in currency
  roi: number; // Return on investment (revenue/cost)
  conversions: number; // Number of conversions
  created_at: Date;
  updated_at: Date;
}

/**
 * Schema for attribution results
 */
const AttributionResultsSchema = new Schema<IAttributionResults>(
  {
    tenant_id: {
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
      required: true,
      index: true,
    },
    window: {
      type: String,
      required: true,
      index: true,
    },
    model_type: {
      type: String,
      required: true,
      enum: ['markov', 'shapley', 'first_touch', 'last_touch', 'linear'],
      index: true,
    },
    channel: {
      type: String,
      required: true,
      index: true,
    },
    campaign: {
      type: String,
      index: true,
    },
    medium: {
      type: String,
      index: true,
    },
    source: {
      type: String,
      index: true,
    },
    creative: {
      type: String,
      index: true,
    },
    keyword: {
      type: String,
      index: true,
    },
    credit_pct: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    cost: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    revenue: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    roi: {
      type: Number,
      required: true,
      default: 0,
    },
    conversions: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);

// Compound indexes for efficient querying
AttributionResultsSchema.index({ tenant_id: 1, window: 1, model_type: 1 });
AttributionResultsSchema.index({ tenant_id: 1, channel: 1, window: 1 });
AttributionResultsSchema.index({ tenant_id: 1, model_type: 1, roi: -1 });

// Create and export the model
export const AttributionResults: Model<IAttributionResults> = 
  mongoose.models.AttributionResults || 
  mongoose.model<IAttributionResults>('AttributionResults', AttributionResultsSchema);

export default AttributionResults;
