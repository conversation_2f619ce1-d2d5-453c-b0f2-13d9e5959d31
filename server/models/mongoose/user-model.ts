import mongoose, { Schema, Document, Model } from 'mongoose';
import bcrypt from 'bcrypt';

export interface IUser extends Document {
  username: string;
  email: string;
  password: string;
  fullName: string;
  avatar?: string;
  role: string;
  preferences: Record<string, any>;
  integrations?: {
    oauth?: {
      google?: {
        accessToken: string;
        refreshToken?: string;
        email?: string;
        expiresAt?: Date;
      };
      microsoft?: {
        accessToken: string;
        refreshToken?: string;
        email?: string;
        expiresAt?: Date;
      };
      calendly?: {
        accessToken: string;
        refreshToken?: string;
        personalUrl?: string;
        organizationUri?: string;
        expiresAt?: Date;
      };
      linkedin?: {
        accessToken: string;
        refreshToken?: string;
        expiresAt?: Date;
      };
      twitter?: {
        accessToken: string;
        refreshToken?: string;
        expiresAt?: Date;
      };
      facebook?: {
        accessToken: string;
        refreshToken?: string;
        expiresAt?: Date;
      };
    };
    calendly?: {
      apiKey: string;
      personalUrl?: string;
      organizationUri?: string;
    };
    telephony?: {
      twilio?: {
        accountSid: string;
        authToken: string;
        phoneNumber?: string;
      };
      vonage?: {
        apiKey: string;
        apiSecret: string;
        phoneNumber?: string;
      };
      plivo?: {
        authId: string;
        authToken: string;
        phoneNumber?: string;
      };
    };
    socialMedia?: {
      linkedin?: {
        username?: string;
        profileId?: string;
      };
      twitter?: {
        username?: string;
      };
      facebook?: {
        pageId?: string;
      };
      instagram?: {
        username?: string;
      };
    };
  };
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const UserSchema = new Schema<IUser>({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true
  },
  fullName: {
    type: String,
    required: true
  },
  avatar: {
    type: String
  },
  role: {
    type: String,
    default: 'user',
    enum: ['user', 'admin', 'superadmin']
  },
  preferences: {
    type: Schema.Types.Mixed,
    default: {}
  },
  integrations: {
    type: Schema.Types.Mixed,
    default: {}
  },
  lastLogin: {
    type: Date
  }
}, { timestamps: true });

// Pre-save hook to hash password
UserSchema.pre('save', async function(next) {
  const user = this;

  // Only hash the password if it has been modified (or is new)
  if (!user.isModified('password')) return next();

  try {
    // Generate a salt
    const salt = await bcrypt.genSalt(10);

    // Hash the password along with the new salt
    const hash = await bcrypt.hash(user.password, salt);

    // Override the cleartext password with the hashed one
    user.password = hash;
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw error;
  }
};

// Create and export the model
export const User: Model<IUser> = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);

export default User;
