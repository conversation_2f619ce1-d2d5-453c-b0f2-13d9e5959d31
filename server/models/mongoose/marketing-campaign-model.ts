import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Interface for campaign cost entry
 */
export interface ICampaignCost {
  date: Date;
  amount: number;
  currency: string;
  source: string; // 'manual', 'api'
}

/**
 * Interface for marketing campaign
 */
export interface IMarketingCampaign extends Document {
  tenant_id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  channel: string; // 'web', 'email', 'ads', 'social', 'voice', 'offline'
  platform?: string; // e.g., 'google', 'facebook', 'linkedin'
  utm_campaign?: string;
  utm_medium?: string;
  utm_source?: string;
  start_date: Date;
  end_date?: Date;
  status: string; // 'draft', 'active', 'paused', 'completed'
  budget: number;
  currency: string;
  costs: ICampaignCost[];
  total_cost: number;
  total_impressions: number;
  total_clicks: number;
  total_conversions: number;
  conversion_value: number;
  tags: string[];
  created_by: mongoose.Types.ObjectId;
  created_at: Date;
  updated_at: Date;
}

/**
 * Schema for campaign cost entry
 */
const CampaignCostSchema = new Schema<ICampaignCost>({
  date: {
    type: Date,
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 0,
  },
  currency: {
    type: String,
    required: true,
    default: 'USD',
  },
  source: {
    type: String,
    required: true,
    enum: ['manual', 'api'],
    default: 'manual',
  },
});

/**
 * Schema for marketing campaign
 */
const MarketingCampaignSchema = new Schema<IMarketingCampaign>(
  {
    tenant_id: {
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
      index: true,
    },
    description: {
      type: String,
      required: true,
    },
    channel: {
      type: String,
      required: true,
      enum: ['web', 'email', 'ads', 'social', 'voice', 'offline'],
      index: true,
    },
    platform: {
      type: String,
      index: true,
    },
    utm_campaign: {
      type: String,
      index: true,
    },
    utm_medium: {
      type: String,
      index: true,
    },
    utm_source: {
      type: String,
      index: true,
    },
    start_date: {
      type: Date,
      required: true,
      index: true,
    },
    end_date: {
      type: Date,
      index: true,
    },
    status: {
      type: String,
      required: true,
      enum: ['draft', 'active', 'paused', 'completed'],
      default: 'draft',
      index: true,
    },
    budget: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      required: true,
      default: 'USD',
    },
    costs: {
      type: [CampaignCostSchema],
      default: [],
    },
    total_cost: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    total_impressions: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    total_clicks: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    total_conversions: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    conversion_value: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    tags: {
      type: [String],
      default: [],
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);

// Compound indexes for efficient querying
MarketingCampaignSchema.index({ tenant_id: 1, status: 1 });
MarketingCampaignSchema.index({ tenant_id: 1, channel: 1, status: 1 });
MarketingCampaignSchema.index({ tenant_id: 1, start_date: 1, end_date: 1 });

// Create and export the model
export const MarketingCampaign: Model<IMarketingCampaign> = 
  mongoose.models.MarketingCampaign || 
  mongoose.model<IMarketingCampaign>('MarketingCampaign', MarketingCampaignSchema);

export default MarketingCampaign;
