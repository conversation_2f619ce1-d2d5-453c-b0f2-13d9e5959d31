import mongoose, { Schema, Document, Model } from 'mongoose';
import { fromObjectId, toObjectId } from '../../utils/mongodb-utils';

export interface IObjection extends Document {
  name: string;
  category: string;
  description: string;
  tags: string[];
  createdBy: mongoose.Types.ObjectId;
  isCommon: boolean;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const ObjectionSchema = new Schema<IObjection>({
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  category: {
    type: String,
    required: true,
    enum: ['price', 'product', 'competition', 'timing', 'authority', 'need', 'trust', 'other'],
    index: true
  },
  description: {
    type: String,
    required: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  isCommon: {
    type: Boolean,
    default: false,
    index: true
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
ObjectionSchema.index({ name: 'text', description: 'text' });

// Create and export the model
// Type for converting MongoDB document to shared type
export function mapObjectionToType(doc: IObjection): any {
  return {
    id: fromObjectId(doc._id),
    name: doc.name,
    category: doc.category,
    description: doc.description,
    tags: doc.tags,
    createdBy: fromObjectId(doc.createdBy),
    isCommon: doc.isCommon,
    customFields: doc.customFields,
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

export const Objection: Model<IObjection> = mongoose.models.Objection || mongoose.model<IObjection>('Objection', ObjectionSchema);

export default Objection;
