import mongoose, { Schema, Document, Model } from 'mongoose';
import { fromObjectId, toObjectId, toObjectIds } from '../../utils/mongodb-utils';

// Define our own types instead of importing from @types/experiments
export type ExperimentStatus = 'draft' | 'running' | 'paused' | 'completed' | 'archived';
export type ExperimentFilterType = 'user' | 'tenant' | 'location' | 'device' | 'custom';

export interface ExperimentVariant {
  key: string;
  name: string;
  description?: string;
  weight: number;
  config?: Record<string, any>;
}

export interface ExperimentFilter {
  type: ExperimentFilterType;
  value: any;
}

export interface ExperimentAudience {
  percentage: number;
  userIds?: string[];
  tenantIds?: string[];
  filters?: ExperimentFilter[];
}

export interface ExperimentGoal {
  key: string;
  name: string;
  description?: string;
  primary: boolean;
}

export interface Experiment {
  id: string;
  key: string;
  name: string;
  description?: string;
  status: ExperimentStatus;
  startDate?: string;
  endDate?: string;
  variants: ExperimentVariant[];
  audience: ExperimentAudience;
  goals: ExperimentGoal[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ExperimentExposure {
  timestamp: string;
  context?: Record<string, any>;
}

export interface ExperimentConversion {
  goalKey: string;
  timestamp: string;
  value?: number;
  metadata?: Record<string, any>;
}

export interface ExperimentResult {
  id: string;
  experimentId: string;
  userId: string;
  tenantId?: string;
  sessionId: string;
  variant: string;
  exposures: ExperimentExposure[];
  conversions: ExperimentConversion[];
  createdAt: string;
  updatedAt: string;
}

export interface IExperiment extends Document {
  key: string;
  name: string;
  description?: string;
  status: ExperimentStatus;
  startDate?: Date;
  endDate?: Date;
  variants: {
    key: string;
    name: string;
    description?: string;
    weight: number;
    config?: Record<string, any>;
  }[];
  audience: {
    percentage: number;
    userIds?: mongoose.Types.ObjectId[];
    tenantIds?: mongoose.Types.ObjectId[];
    filters?: {
      type: ExperimentFilterType;
      value: any;
    }[];
  };
  goals: {
    key: string;
    name: string;
    description?: string;
    primary: boolean;
  }[];
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface IExperimentResult extends Document {
  experimentId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  tenantId?: mongoose.Types.ObjectId;
  sessionId: string;
  variant: string;
  exposures: {
    timestamp: Date;
    context?: Record<string, any>;
  }[];
  conversions: {
    goalKey: string;
    timestamp: Date;
    value?: number;
    metadata?: Record<string, any>;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

// Type for converting MongoDB document to shared type
export function mapExperimentToType(doc: IExperiment): Experiment {
  return {
    id: fromObjectId(doc._id),
    key: doc.key,
    name: doc.name,
    description: doc.description,
    status: doc.status,
    startDate: doc.startDate?.toISOString(),
    endDate: doc.endDate?.toISOString(),
    variants: doc.variants.map(variant => ({
      key: variant.key,
      name: variant.name,
      description: variant.description,
      weight: variant.weight,
      config: variant.config,
    })),
    audience: {
      percentage: doc.audience.percentage,
      userIds: doc.audience.userIds?.map(id => fromObjectId(id)),
      tenantIds: doc.audience.tenantIds?.map(id => fromObjectId(id)),
      filters: doc.audience.filters as ExperimentFilter[],
    },
    goals: doc.goals.map(goal => ({
      key: goal.key,
      name: goal.name,
      description: goal.description,
      primary: goal.primary,
    })),
    createdBy: fromObjectId(doc.createdBy),
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

// Type for converting MongoDB document to shared type
export function mapExperimentResultToType(doc: IExperimentResult): ExperimentResult {
  return {
    id: fromObjectId(doc._id),
    experimentId: fromObjectId(doc.experimentId),
    userId: fromObjectId(doc.userId),
    tenantId: doc.tenantId ? fromObjectId(doc.tenantId) : undefined,
    sessionId: doc.sessionId,
    variant: doc.variant,
    exposures: doc.exposures.map(exposure => ({
      timestamp: exposure.timestamp.toISOString(),
      context: exposure.context,
    })),
    conversions: doc.conversions.map(conversion => ({
      goalKey: conversion.goalKey,
      timestamp: conversion.timestamp.toISOString(),
      value: conversion.value,
      metadata: conversion.metadata,
    })),
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

const ExperimentSchema = new Schema<IExperiment>(
  {
    key: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    status: {
      type: String,
      enum: ['draft', 'running', 'paused', 'completed', 'archived'],
      default: 'draft',
      index: true,
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
    },
    variants: [{
      key: {
        type: String,
        required: true,
        trim: true,
      },
      name: {
        type: String,
        required: true,
        trim: true,
      },
      description: {
        type: String,
        trim: true,
      },
      weight: {
        type: Number,
        required: true,
        min: 0,
        max: 100,
        default: 50,
      },
      config: {
        type: Schema.Types.Mixed,
      },
    }],
    audience: {
      percentage: {
        type: Number,
        min: 0,
        max: 100,
        default: 100,
      },
      userIds: [{
        type: Schema.Types.ObjectId,
        ref: 'User',
      }],
      tenantIds: [{
        type: Schema.Types.ObjectId,
        ref: 'Tenant',
      }],
      filters: [{
        type: {
          type: String,
          enum: ['user', 'tenant', 'location', 'device', 'custom'],
          required: true,
        },
        value: {
          type: Schema.Types.Mixed,
          required: true,
        },
      }],
    },
    goals: [{
      key: {
        type: String,
        required: true,
        trim: true,
      },
      name: {
        type: String,
        required: true,
        trim: true,
      },
      description: {
        type: String,
        trim: true,
      },
      primary: {
        type: Boolean,
        default: false,
      },
    }],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const ExperimentResultSchema = new Schema<IExperimentResult>(
  {
    experimentId: {
      type: Schema.Types.ObjectId,
      ref: 'Experiment',
      required: true,
      index: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    tenantId: {
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
      index: true,
    },
    sessionId: {
      type: String,
      required: true,
      index: true,
    },
    variant: {
      type: String,
      required: true,
      index: true,
    },
    exposures: [{
      timestamp: {
        type: Date,
        required: true,
        default: Date.now,
      },
      context: {
        type: Schema.Types.Mixed,
      },
    }],
    conversions: [{
      goalKey: {
        type: String,
        required: true,
        index: true,
      },
      timestamp: {
        type: Date,
        required: true,
        default: Date.now,
      },
      value: {
        type: Number,
      },
      metadata: {
        type: Schema.Types.Mixed,
      },
    }],
  },
  {
    timestamps: true,
  }
);

// Indexes
ExperimentSchema.index({ key: 1 }, { unique: true });
ExperimentSchema.index({ status: 1 });
ExperimentSchema.index({ startDate: 1, endDate: 1 });

ExperimentResultSchema.index({ experimentId: 1, userId: 1 });
ExperimentResultSchema.index({ experimentId: 1, variant: 1 });
ExperimentResultSchema.index({ experimentId: 1, 'conversions.goalKey': 1 });

// Export the models
const ExperimentModel: Model<IExperiment> = mongoose.models.Experiment || mongoose.model<IExperiment>('Experiment', ExperimentSchema);
const ExperimentResultModel: Model<IExperimentResult> = mongoose.models.ExperimentResult || mongoose.model<IExperimentResult>('ExperimentResult', ExperimentResultSchema);

export { ExperimentModel as Experiment, ExperimentResultModel as ExperimentResult };
