import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IInsight extends Document {
  title: string;
  description: string;
  type: string;
  importance: number; // 1-5 scale
  targetType: string;
  targetId: mongoose.Types.ObjectId;
  generatedBy: 'ai' | 'system' | 'user';
  createdBy?: mongoose.Types.ObjectId;
  isRead: boolean;
  actionTaken: boolean;
  actionDescription?: string;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const InsightSchema = new Schema<IInsight>({
  title: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String, 
    required: true
  },
  type: { 
    type: String, 
    required: true,
    enum: ['opportunity', 'risk', 'trend', 'suggestion', 'reminder', 'other'],
    index: true
  },
  importance: { 
    type: Number, 
    required: true,
    min: 1,
    max: 5,
    default: 3,
    index: true
  },
  targetType: { 
    type: String, 
    required: true,
    enum: ['contact', 'company', 'opportunity', 'user', 'global'],
    index: true
  },
  targetId: { 
    type: Schema.Types.ObjectId, 
    required: true,
    index: true
  },
  generatedBy: { 
    type: String, 
    required: true,
    enum: ['ai', 'system', 'user'],
    default: 'ai',
    index: true
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User'
  },
  isRead: { 
    type: Boolean, 
    required: true,
    default: false,
    index: true
  },
  actionTaken: { 
    type: Boolean, 
    required: true,
    default: false,
    index: true
  },
  actionDescription: { 
    type: String 
  },
  expiresAt: { 
    type: Date,
    index: true
  }
}, { 
  timestamps: true 
});

// Text index for search
InsightSchema.index({ title: 'text', description: 'text' });

// Compound indexes for efficient querying
InsightSchema.index({ targetType: 1, targetId: 1 });
InsightSchema.index({ importance: -1, createdAt: -1 });

// Create and export the model
export const Insight: Model<IInsight> = mongoose.models.Insight || mongoose.model<IInsight>('Insight', InsightSchema);

export default Insight;
