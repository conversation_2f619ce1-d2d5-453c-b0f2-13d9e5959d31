import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Email Template Category
 */
export enum EmailTemplateCategory {
  SALES = 'sales',
  MARKETING = 'marketing',
  SUPPORT = 'support',
  ONBOARDING = 'onboarding',
  FOLLOW_UP = 'follow_up',
  PROPOSAL = 'proposal',
  OTHER = 'other'
}

/**
 * Email Template Interface
 */
export interface IEmailTemplate extends Document {
  tenantId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  subject: string;
  body: string;
  plainText?: string;
  category: EmailTemplateCategory;
  tags: string[];
  isActive: boolean;
  isDefault?: boolean;
  isSystem?: boolean;
  variables: {
    name: string;
    description?: string;
    defaultValue?: string;
    required?: boolean;
  }[];
  metadata: {
    previewText?: string;
    fromName?: string;
    fromEmail?: string;
    replyTo?: string;
  };
  trackingSettings: {
    trackOpens: boolean;
    trackLinks: boolean;
  };
  abTestingEnabled?: boolean;
  abTestingVariants?: {
    name: string;
    subject?: string;
    body?: string;
    metadata?: Record<string, any>;
    weight: number;
  }[];
  stats?: {
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
    bounced: number;
    complained: number;
  };
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Email Template Schema
 */
const EmailTemplateSchema = new Schema<IEmailTemplate>({
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  description: {
    type: String,
    trim: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  body: {
    type: String,
    required: true
  },
  plainText: {
    type: String
  },
  category: {
    type: String,
    required: true,
    enum: Object.values(EmailTemplateCategory),
    default: EmailTemplateCategory.OTHER,
    index: true
  },
  tags: {
    type: [String],
    default: [],
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  isSystem: {
    type: Boolean,
    default: false
  },
  variables: [{
    name: {
      type: String,
      required: true
    },
    description: String,
    defaultValue: String,
    required: {
      type: Boolean,
      default: false
    }
  }],
  metadata: {
    previewText: String,
    fromName: String,
    fromEmail: String,
    replyTo: String
  },
  trackingSettings: {
    trackOpens: {
      type: Boolean,
      default: true
    },
    trackLinks: {
      type: Boolean,
      default: true
    }
  },
  abTestingEnabled: {
    type: Boolean,
    default: false
  },
  abTestingVariants: [{
    name: {
      type: String,
      required: true
    },
    subject: String,
    body: String,
    metadata: Schema.Types.Mixed,
    weight: {
      type: Number,
      default: 1,
      min: 0
    }
  }],
  stats: {
    sent: {
      type: Number,
      default: 0
    },
    opened: {
      type: Number,
      default: 0
    },
    clicked: {
      type: Number,
      default: 0
    },
    replied: {
      type: Number,
      default: 0
    },
    bounced: {
      type: Number,
      default: 0
    },
    complained: {
      type: Number,
      default: 0
    }
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Create indexes
EmailTemplateSchema.index({ tenantId: 1, name: 1 }, { unique: true });
EmailTemplateSchema.index({ tenantId: 1, category: 1 });
EmailTemplateSchema.index({ tenantId: 1, tags: 1 });
EmailTemplateSchema.index({ tenantId: 1, isActive: 1 });

// Text index for search
EmailTemplateSchema.index({ 
  name: 'text', 
  description: 'text', 
  subject: 'text', 
  body: 'text' 
});

// Create and export the model
export const EmailTemplate: Model<IEmailTemplate> = mongoose.models.EmailTemplate || 
  mongoose.model<IEmailTemplate>('EmailTemplate', EmailTemplateSchema);

export default EmailTemplate;
