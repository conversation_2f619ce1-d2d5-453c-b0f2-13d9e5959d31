import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IDocument extends Document {
  name: string;
  description?: string;
  fileType: string;
  mimeType: string;
  size: number; // in bytes
  url: string;
  path: string;
  isPublic: boolean;
  owner: mongoose.Types.ObjectId;
  relatedTo?: {
    type: string;
    id: mongoose.Types.ObjectId;
  };
  tags: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const DocumentSchema = new Schema<IDocument>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  fileType: {
    type: String,
    required: true,
    index: true
  },
  mimeType: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true,
    min: 0
  },
  url: {
    type: String,
    required: true
  },
  path: {
    type: String,
    required: true
  },
  isPublic: {
    type: Boolean,
    required: true,
    default: false,
    index: true
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  relatedTo: {
    type: {
      type: {
        type: String,
        enum: ['contact', 'company', 'opportunity', 'activity']
      },
      id: {
        type: Schema.Types.ObjectId
      }
    }
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
DocumentSchema.index({ name: 'text', description: 'text', tags: 'text' });

// Create and export the model
export const DocumentModel: Model<IDocument> = mongoose.models.Document || mongoose.model<IDocument>('Document', DocumentSchema);

export default DocumentModel;
