import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IProposalTemplate extends Document {
  name: string;
  description?: string;
  category: string;
  content: string; // HTML content
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    isRequired: boolean;
    order: number;
  }>;
  variables: string[]; // List of variable names used in the template
  tags: string[];
  isDefault: boolean;
  createdBy: mongoose.Types.ObjectId;
  usageCount: number;
  successCount: number;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const ProposalTemplateSchema = new Schema<IProposalTemplate>({
  name: { 
    type: String, 
    required: true,
    trim: true,
    index: true
  },
  description: { 
    type: String 
  },
  category: { 
    type: String, 
    required: true,
    enum: ['sales', 'service', 'partnership', 'custom'],
    default: 'sales',
    index: true
  },
  content: { 
    type: String, 
    required: true 
  },
  sections: [{
    id: { type: String, required: true },
    name: { type: String, required: true },
    type: { 
      type: String, 
      required: true,
      enum: ['text', 'pricing', 'timeline', 'team', 'testimonials', 'terms', 'custom'],
      default: 'text'
    },
    content: { type: String, required: true },
    isRequired: { type: Boolean, default: true },
    order: { type: Number, required: true }
  }],
  variables: [{ 
    type: String,
    trim: true
  }],
  tags: [{ 
    type: String,
    trim: true
  }],
  isDefault: {
    type: Boolean,
    default: false,
    index: true
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true,
    index: true
  },
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  successCount: {
    type: Number,
    default: 0,
    min: 0
  },
  customFields: { 
    type: Schema.Types.Mixed, 
    default: {} 
  }
}, { 
  timestamps: true 
});

// Text index for search
ProposalTemplateSchema.index({ name: 'text', description: 'text' });

// Create and export the model
export const ProposalTemplate: Model<IProposalTemplate> = mongoose.models.ProposalTemplate || mongoose.model<IProposalTemplate>('ProposalTemplate', ProposalTemplateSchema);

export default ProposalTemplate;
