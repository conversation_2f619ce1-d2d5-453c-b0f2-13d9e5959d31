import mongoose, { Schema, Document, Model } from 'mongoose';
import { 
  SubscriptionLimits, 
  FeatureFlags, 
  ModuleSettings, 
  BillingDetails, 
  TenantSettings 
} from '@shared/subscription-schema';

// Subscription Plan Schema
export interface ISubscriptionPlan extends Document {
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'archived';
  isDefault: boolean;
  sortOrder: number;
  price: number;
  currency: string;
  billingPeriod: 'monthly' | 'yearly' | 'custom';
  trialDays: number;
  limits: SubscriptionLimits;
  features: FeatureFlags;
  moduleSettings: ModuleSettings;
  createdAt: Date;
  updatedAt: Date;
}

const SubscriptionPlanSchema = new Schema<ISubscriptionPlan>({
  name: { type: String, required: true },
  description: { type: String },
  status: { type: String, enum: ['active', 'inactive', 'archived'], default: 'active' },
  isDefault: { type: Boolean, default: false },
  sortOrder: { type: Number, default: 0 },
  price: { type: Number, required: true },
  currency: { type: String, default: 'USD' },
  billingPeriod: { type: String, enum: ['monthly', 'yearly', 'custom'], default: 'monthly' },
  trialDays: { type: Number, default: 0 },
  limits: { type: Schema.Types.Mixed, required: true },
  features: { type: Schema.Types.Mixed, required: true },
  moduleSettings: { type: Schema.Types.Mixed, default: {} },
}, { timestamps: true });

// Tenant Schema
export interface ITenant extends Document {
  name: string;
  slug: string;
  ownerId: mongoose.Types.ObjectId;
  settings: TenantSettings;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

const TenantSchema = new Schema<ITenant>({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  ownerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  settings: { type: Schema.Types.Mixed, default: {} },
  status: { type: String, default: 'active' },
}, { timestamps: true });

// Tenant Subscription Schema
export interface ITenantSubscription extends Document {
  tenantId: string;
  planId: mongoose.Types.ObjectId;
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'expired';
  startDate: Date;
  endDate: Date;
  trialEndsAt?: Date;
  customLimits?: SubscriptionLimits;
  customFeatures?: FeatureFlags;
  billingDetails?: BillingDetails;
  createdAt: Date;
  updatedAt: Date;
  canceledAt?: Date;
}

const TenantSubscriptionSchema = new Schema<ITenantSubscription>({
  tenantId: { type: String, required: true },
  planId: { type: Schema.Types.ObjectId, ref: 'SubscriptionPlan', required: true },
  status: { type: String, enum: ['active', 'trialing', 'past_due', 'canceled', 'expired'], default: 'active' },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  trialEndsAt: { type: Date },
  customLimits: { type: Schema.Types.Mixed },
  customFeatures: { type: Schema.Types.Mixed },
  billingDetails: { type: Schema.Types.Mixed },
  canceledAt: { type: Date },
}, { timestamps: true });

// Feature Schema
export interface IFeature extends Document {
  key: string;
  name: string;
  description?: string;
  category: string;
  module: string;
  defaultValue: boolean;
  requiresRestart: boolean;
  uiComponent?: string;
  dependencies?: any;
  createdAt: Date;
  updatedAt: Date;
}

const FeatureSchema = new Schema<IFeature>({
  key: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String },
  category: { type: String, required: true },
  module: { type: String, required: true },
  defaultValue: { type: Boolean, default: false },
  requiresRestart: { type: Boolean, default: false },
  uiComponent: { type: String },
  dependencies: { type: Schema.Types.Mixed },
}, { timestamps: true });

// Tenant Usage Schema
export interface ITenantUsage extends Document {
  tenantId: string;
  period: string; // Format: YYYY-MM
  usage: Record<string, number>;
  featureUsage: Record<string, number>;
  updatedAt: Date;
}

const TenantUsageSchema = new Schema<ITenantUsage>({
  tenantId: { type: String, required: true },
  period: { type: String, required: true }, // Format: YYYY-MM
  usage: { type: Schema.Types.Mixed, required: true, default: {} },
  featureUsage: { type: Schema.Types.Mixed, required: true, default: {} },
}, { timestamps: true });

// Subscription Event Schema
export interface ISubscriptionEvent extends Document {
  type: string;
  payload: any;
  timestamp: Date;
}

const SubscriptionEventSchema = new Schema<ISubscriptionEvent>({
  type: { type: String, required: true },
  payload: { type: Schema.Types.Mixed, required: true },
  timestamp: { type: Date, default: Date.now },
});

// User-Tenant Relationship Schema
export interface IUserTenant extends Document {
  userId: mongoose.Types.ObjectId;
  tenantId: mongoose.Types.ObjectId;
  role: string; // 'owner', 'admin', 'member', 'guest'
  createdAt: Date;
  invitedBy?: mongoose.Types.ObjectId;
  status: string; // 'active', 'invited', 'suspended'
}

const UserTenantSchema = new Schema<IUserTenant>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  tenantId: { type: Schema.Types.ObjectId, ref: 'Tenant', required: true },
  role: { type: String, required: true },
  invitedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  status: { type: String, required: true },
}, { timestamps: true });

// Create and export models
export const SubscriptionPlan: Model<ISubscriptionPlan> = mongoose.models.SubscriptionPlan || mongoose.model<ISubscriptionPlan>('SubscriptionPlan', SubscriptionPlanSchema);
export const Tenant: Model<ITenant> = mongoose.models.Tenant || mongoose.model<ITenant>('Tenant', TenantSchema);
export const TenantSubscription: Model<ITenantSubscription> = mongoose.models.TenantSubscription || mongoose.model<ITenantSubscription>('TenantSubscription', TenantSubscriptionSchema);
export const Feature: Model<IFeature> = mongoose.models.Feature || mongoose.model<IFeature>('Feature', FeatureSchema);
export const TenantUsage: Model<ITenantUsage> = mongoose.models.TenantUsage || mongoose.model<ITenantUsage>('TenantUsage', TenantUsageSchema);
export const SubscriptionEvent: Model<ISubscriptionEvent> = mongoose.models.SubscriptionEvent || mongoose.model<ISubscriptionEvent>('SubscriptionEvent', SubscriptionEventSchema);
export const UserTenant: Model<IUserTenant> = mongoose.models.UserTenant || mongoose.model<IUserTenant>('UserTenant', UserTenantSchema);
