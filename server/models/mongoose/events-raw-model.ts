import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Interface for raw events data
 */
export interface IEventsRaw extends Document {
  tenant_id: mongoose.Types.ObjectId;
  visitor_id?: string;
  contact_id?: mongoose.Types.ObjectId;
  timestamp: Date;
  channel: string;
  campaign?: string;
  medium?: string;
  source?: string;
  event_type: string;
  meta_json: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

/**
 * Schema for raw events data
 */
const EventsRawSchema = new Schema<IEventsRaw>(
  {
    tenant_id: {
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
      required: true,
      index: true,
    },
    visitor_id: {
      type: String,
      index: true,
    },
    contact_id: {
      type: Schema.Types.ObjectId,
      ref: 'Contact',
      index: true,
    },
    timestamp: {
      type: Date,
      required: true,
      index: true,
    },
    channel: {
      type: String,
      required: true,
      index: true,
      enum: ['web', 'email', 'ads', 'social', 'voice', 'offline'],
    },
    campaign: {
      type: String,
      index: true,
    },
    medium: {
      type: String,
      index: true,
    },
    source: {
      type: String,
      index: true,
    },
    event_type: {
      type: String,
      required: true,
      index: true,
    },
    meta_json: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);

// Compound indexes for efficient querying
EventsRawSchema.index({ tenant_id: 1, timestamp: 1 });
EventsRawSchema.index({ tenant_id: 1, channel: 1, timestamp: 1 });
EventsRawSchema.index({ tenant_id: 1, contact_id: 1, timestamp: 1 });
EventsRawSchema.index({ tenant_id: 1, visitor_id: 1, timestamp: 1 });

// Create and export the model
export const EventsRaw: Model<IEventsRaw> = mongoose.models.EventsRaw || mongoose.model<IEventsRaw>('EventsRaw', EventsRawSchema);

export default EventsRaw;
