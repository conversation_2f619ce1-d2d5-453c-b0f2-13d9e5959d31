import mongoose, { Schema, Document, Model } from 'mongoose';

export interface ITag extends Document {
  name: string;
  color?: string;
  category?: string;
  description?: string;
  createdBy: mongoose.Types.ObjectId;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

const TagSchema = new Schema<ITag>({
  name: { 
    type: String, 
    required: true,
    trim: true,
    unique: true
  },
  color: { 
    type: String,
    default: '#6B7280' // Default gray color
  },
  category: { 
    type: String,
    trim: true,
    index: true
  },
  description: { 
    type: String 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  },
  usageCount: { 
    type: Number,
    default: 0,
    min: 0
  }
}, { 
  timestamps: true 
});

// Text index for search
TagSchema.index({ name: 'text', description: 'text' });

// Create and export the model
export const Tag: Model<ITag> = mongoose.models.Tag || mongoose.model<ITag>('Tag', TagSchema);

export default Tag;
