import mongoose, { Document, Schema } from 'mongoose';

/**
 * Interface for Chart Specification
 */
export interface IChartSpec {
  type: string;
  data: any;
  options: Record<string, any>;
  title?: string;
  subtitle?: string;
  interactive?: boolean;
  drilldown?: boolean;
}

/**
 * Enum for Chart Types
 */
export enum ChartType {
  // Basic charts
  BAR = 'bar',
  LINE = 'line',
  PIE = 'pie',
  FUNNEL = 'funnel',
  SCATTER = 'scatter',
  TABLE = 'table',

  // Advanced visualization types
  SANKEY = 'sankey',
  HEATMAP = 'heatmap',
  RADAR = 'radar',
  TREEMAP = 'treemap',
  BUBBLE = 'bubble',

  // Business-specific charts
  PIPELINE_WATERFALL = 'pipeline_waterfall',
  COHORT_ANALYSIS = 'cohort_analysis',
  WIN_LOSS_ANALYSIS = 'win_loss_analysis',
  SALES_VELOCITY = 'sales_velocity',
  FORECAST_COMPARISON = 'forecast_comparison',

  // Predictive charts
  FORECAST = 'forecast',
  ANOMALY_DETECTION = 'anomaly_detection',
  WHAT_IF_ANALYSIS = 'what_if_analysis',
  TREND_PREDICTION = 'trend_prediction'
}

/**
 * Interface for Recommended Play
 */
export interface IRecommendedPlay {
  id?: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  workflow_template?: string;
  action_url?: string;
  module_integration?: {
    module: RecommendedPlayModuleType;
    action: string;
    params: Record<string, any>;
  };
}

/**
 * Enum for Recommended Play Module Types
 */
export enum RecommendedPlayModuleType {
  // Core modules
  WORKFLOW = 'workflow',
  INSIGHTS = 'insights',

  // Integration with other CRM modules
  PROPOSAL_GENERATOR = 'proposal_generator',
  OBJECTION_HANDLER = 'objection_handler',
  FOLLOW_UP_COACH = 'follow_up_coach',
  WIN_LOSS_ANALYZER = 'win_loss_analyzer',
  MEETING_PREP = 'meeting_prep'
}

/**
 * Interface for Insights Cache Document
 */
export interface IInsightsCache extends Document {
  tenant_id: string;
  question: string;
  question_hash: string;
  dataset_ref: string;
  narrative: string;
  chart_spec: IChartSpec;
  why_it_matters: string[];
  recommended_plays: IRecommendedPlay[];
  sql_query?: string;
  graph_query?: string;
  feature_weights?: Record<string, number>;
  created_at: Date;
  updated_at: Date;
  expires_at: Date;
  refresh_count: number;
  last_refresh_at?: Date;
  feedback?: {
    helpful: boolean;
    comment?: string;
    submitted_by: string;
    submitted_at: Date;
  }[];
  isExpired(): boolean;
  refreshInsight(): Promise<IInsightsCache>;
}

/**
 * Schema for Insights Cache
 */
const InsightsCacheSchema = new Schema<IInsightsCache>(
  {
    tenant_id: {
      type: String,
      required: true,
      index: true,
    },
    question: {
      type: String,
      required: true,
    },
    question_hash: {
      type: String,
      required: true,
      index: true,
    },
    dataset_ref: {
      type: String,
      required: true,
    },
    narrative: {
      type: String,
      required: true,
    },
    chart_spec: {
      type: {
        type: String,
        required: true,
      },
      data: {
        type: Schema.Types.Mixed,
        required: true,
      },
      options: {
        type: Schema.Types.Mixed,
        required: true,
      },
      title: String,
      subtitle: String,
      interactive: Boolean,
      drilldown: Boolean,
    },
    why_it_matters: [String],
    recommended_plays: [{
      id: String,
      title: {
        type: String,
        required: true,
      },
      description: {
        type: String,
        required: true,
      },
      impact: {
        type: String,
        enum: ['high', 'medium', 'low'],
        required: true,
      },
      workflow_template: String,
      action_url: String,
      module_integration: {
        module: {
          type: String,
          enum: Object.values(RecommendedPlayModuleType),
        },
        action: String,
        params: {
          type: Map,
          of: Schema.Types.Mixed,
        },
      },
    }],
    sql_query: String,
    graph_query: String,
    feature_weights: {
      type: Map,
      of: Number,
    },
    expires_at: {
      type: Date,
      required: true,
      index: true,
    },
    refresh_count: {
      type: Number,
      default: 0,
    },
    last_refresh_at: Date,
    feedback: [{
      helpful: {
        type: Boolean,
        required: true,
      },
      comment: String,
      submitted_by: {
        type: String,
        required: true,
      },
      submitted_at: {
        type: Date,
        default: Date.now,
      },
    }],
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);

// Create a compound index for tenant_id and question_hash
InsightsCacheSchema.index({ tenant_id: 1, question_hash: 1 }, { unique: true });

/**
 * Check if the insight is expired
 */
InsightsCacheSchema.methods.isExpired = function(): boolean {
  return new Date() > this.expires_at;
};

/**
 * Refresh the insight
 * This is a placeholder - the actual implementation will be in the service
 */
InsightsCacheSchema.methods.refreshInsight = async function(): Promise<IInsightsCache> {
  // This will be implemented in the insights service
  // For now, just update the refresh count and timestamp
  this.refresh_count += 1;
  this.last_refresh_at = new Date();

  // Set new expiration date (24 hours from now)
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + 24);
  this.expires_at = expiresAt;

  return this.save();
};

// Create and export the InsightsCache model
export const InsightsCache = mongoose.model<IInsightsCache>('InsightsCache', InsightsCacheSchema);
