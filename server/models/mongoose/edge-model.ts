import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Edge Interface
 */
export interface IEdge extends Document {
  tenantId?: mongoose.Types.ObjectId;
  fromType: string;
  fromId: mongoose.Types.ObjectId;
  toType: string;
  toId: mongoose.Types.ObjectId;
  relationship: string;
  weight?: number;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Edge Schema
 */
const EdgeSchema = new Schema<IEdge>({
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  fromType: {
    type: String,
    required: true,
    index: true
  },
  fromId: {
    type: Schema.Types.ObjectId,
    required: true,
    index: true
  },
  toType: {
    type: String,
    required: true,
    index: true
  },
  toId: {
    type: Schema.Types.ObjectId,
    required: true,
    index: true
  },
  relationship: {
    type: String,
    required: true,
    index: true
  },
  weight: {
    type: Number,
    default: 1.0
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Create compound indexes
EdgeSchema.index({ tenantId: 1, fromId: 1, toId: 1, relationship: 1 }, { unique: true });
EdgeSchema.index({ tenantId: 1, fromType: 1, fromId: 1 });
EdgeSchema.index({ tenantId: 1, toType: 1, toId: 1 });

// Create and export the model
export const Edge: Model<IEdge> = mongoose.models.Edge || 
  mongoose.model<IEdge>('Edge', EdgeSchema);

export default Edge;
