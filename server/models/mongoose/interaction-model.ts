/**
 * Interaction model for MongoDB
 */
import mongoose, { Schema, Document } from 'mongoose';

export interface IInteraction extends Document {
  contactId: mongoose.Types.ObjectId;
  type: string;
  channel: string;
  direction: 'inbound' | 'outbound';
  timestamp: Date;
  metadata: Record<string, any>;
  score?: number;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}

const InteractionSchema = new Schema<IInteraction>(
  {
    contactId: { type: Schema.Types.ObjectId, ref: 'Contact', required: true },
    type: { type: String, required: true },
    channel: { type: String, required: true },
    direction: { type: String, enum: ['inbound', 'outbound'], required: true },
    timestamp: { type: Date, required: true },
    metadata: { type: Schema.Types.Mixed, default: {} },
    score: { type: Number },
    tenantId: { type: String, required: true }
  },
  {
    timestamps: true
  }
);

// Create indexes
InteractionSchema.index({ contactId: 1 });
InteractionSchema.index({ tenantId: 1 });
InteractionSchema.index({ timestamp: -1 });
InteractionSchema.index({ type: 1, channel: 1 });

// Create model
export const Interaction = mongoose.model<IInteraction>('Interaction', InteractionSchema);

export default Interaction;
