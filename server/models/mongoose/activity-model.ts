import mongoose, { Schema, Document, Model } from 'mongoose';
import { fromObjectId, toObjectId } from '../../utils/mongodb-utils';

export interface IActivity extends Document {
  type: string;
  title: string;
  description?: string;
  date: Date;
  duration?: number; // in minutes
  completed: boolean;
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  opportunityId?: mongoose.Types.ObjectId;
  owner: mongoose.Types.ObjectId;
  participants?: mongoose.Types.ObjectId[];
  location?: string;
  notes?: string;
  outcome?: string;
  customFields: Record<string, any>;
  // Additional fields for tracking events
  tenantId?: mongoose.Types.ObjectId;
  source?: string;
  data?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const ActivitySchema = new Schema<IActivity>({
  type: {
    type: String,
    required: true,
    enum: ['call', 'meeting', 'email', 'task', 'note', 'other', 'EMAIL_OPEN', 'EMAIL_CLICK', 'EMAIL_REPLY', 'EMAIL_BOUNCE', 'EMAIL_COMPLAINT'],
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  date: {
    type: Date,
    required: true,
    index: true
  },
  duration: {
    type: Number,
    min: 0
  },
  completed: {
    type: Boolean,
    required: true,
    default: false,
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    index: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  opportunityId: {
    type: Schema.Types.ObjectId,
    ref: 'Opportunity',
    index: true
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  participants: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  location: {
    type: String,
    trim: true
  },
  notes: {
    type: String
  },
  outcome: {
    type: String
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  },
  // Additional fields for tracking events
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  source: {
    type: String,
    index: true
  },
  data: {
    type: Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Text index for search
ActivitySchema.index({ title: 'text', description: 'text', notes: 'text' });

// Additional indexes for tracking events
ActivitySchema.index({ tenantId: 1, type: 1 });
ActivitySchema.index({ tenantId: 1, contactId: 1 });
ActivitySchema.index({ tenantId: 1, source: 1 });
ActivitySchema.index({ tenantId: 1, createdAt: -1 });

// Type for converting MongoDB document to shared type
export function mapActivityToType(doc: IActivity): any {
  return {
    id: fromObjectId(doc._id),
    type: doc.type,
    title: doc.title,
    description: doc.description,
    date: doc.date.toISOString(),
    duration: doc.duration,
    completed: doc.completed,
    contactId: doc.contactId ? fromObjectId(doc.contactId) : undefined,
    companyId: doc.companyId ? fromObjectId(doc.companyId) : undefined,
    opportunityId: doc.opportunityId ? fromObjectId(doc.opportunityId) : undefined,
    owner: fromObjectId(doc.owner),
    participants: doc.participants ? doc.participants.map(p => fromObjectId(p)) : [],
    location: doc.location,
    notes: doc.notes,
    outcome: doc.outcome,
    customFields: doc.customFields,
    tenantId: doc.tenantId ? fromObjectId(doc.tenantId) : undefined,
    source: doc.source,
    data: doc.data,
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

// Create and export the model
export const Activity: Model<IActivity> = mongoose.models.Activity || mongoose.model<IActivity>('Activity', ActivitySchema);

export default Activity;
