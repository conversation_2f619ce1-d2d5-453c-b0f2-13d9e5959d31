import mongoose, { Schema, Document, Model } from 'mongoose';
import { FeatureFlag, FeatureFlagRule, FeatureFlagRuleType } from '@types/feature-flags';
import { fromObjectId, toObjectId, toObjectIds } from '../../utils/mongodb-utils';

export interface IFeatureFlag extends Document {
  key: string;
  name: string;
  description?: string;
  enabled: boolean;
  enabledForUsers: mongoose.Types.ObjectId[];
  enabledForTenants: mongoose.Types.ObjectId[];
  enabledForPercentage: number;
  rules: {
    type: FeatureFlagRuleType;
    value: any;
  }[];
  tags: string[];
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Type for converting MongoDB document to shared type
export function mapFeatureFlagToType(doc: IFeatureFlag): FeatureFlag {
  return {
    id: fromObjectId(doc._id),
    key: doc.key,
    name: doc.name,
    description: doc.description,
    enabled: doc.enabled,
    enabledForUsers: doc.enabledForUsers.map(id => fromObjectId(id)),
    enabledForTenants: doc.enabledForTenants.map(id => fromObjectId(id)),
    enabledForPercentage: doc.enabledForPercentage,
    rules: doc.rules as FeatureFlagRule[],
    tags: doc.tags,
    createdBy: fromObjectId(doc.createdBy),
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

const FeatureFlagSchema = new Schema<IFeatureFlag>(
  {
    key: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    enabled: {
      type: Boolean,
      default: false,
    },
    enabledForUsers: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    enabledForTenants: [{
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
    }],
    enabledForPercentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    rules: [{
      type: {
        type: String,
        enum: ['user', 'tenant', 'date', 'time', 'location', 'custom'],
        required: true,
      },
      value: {
        type: Schema.Types.Mixed,
        required: true,
      },
    }],
    tags: [{
      type: String,
      trim: true,
    }],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
FeatureFlagSchema.index({ key: 1 }, { unique: true });
FeatureFlagSchema.index({ tags: 1 });
FeatureFlagSchema.index({ enabled: 1 });

export const FeatureFlag: Model<IFeatureFlag> = mongoose.models.FeatureFlag || mongoose.model<IFeatureFlag>('FeatureFlag', FeatureFlagSchema);
