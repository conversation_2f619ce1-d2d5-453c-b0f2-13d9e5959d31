import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IStageTransition extends Document {
  opportunityId: mongoose.Types.ObjectId;
  previousStage: string;
  newStage: string;
  confidence: number;
  explanation: string;
  keyIndicators: string[];
  appliedBy: 'ai' | 'user' | null;
  appliedAt: Date | null;
  reviewedBy: mongoose.Types.ObjectId | null;
  reviewedAt: Date | null;
  approved: boolean | null;
  createdAt: Date;
  updatedAt: Date;
}

const StageTransitionSchema = new Schema<IStageTransition>({
  opportunityId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Opportunity',
    required: true,
    index: true
  },
  previousStage: { 
    type: String, 
    required: true
  },
  newStage: { 
    type: String, 
    required: true
  },
  confidence: { 
    type: Number, 
    required: true,
    min: 0,
    max: 1
  },
  explanation: { 
    type: String, 
    required: true
  },
  keyIndicators: [{ 
    type: String
  }],
  appliedBy: { 
    type: String,
    enum: ['ai', 'user', null],
    default: null
  },
  appliedAt: { 
    type: Date,
    default: null
  },
  reviewedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    default: null
  },
  reviewedAt: { 
    type: Date,
    default: null
  },
  approved: { 
    type: Boolean,
    default: null
  }
}, { 
  timestamps: true 
});

// Create and export the model
export const StageTransition: Model<IStageTransition> = mongoose.models.StageTransition || 
  mongoose.model<IStageTransition>('StageTransition', StageTransitionSchema);

export default StageTransition;
