import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IRelationship extends Document {
  sourceType: string;
  sourceId: mongoose.Types.ObjectId;
  targetType: string;
  targetId: mongoose.Types.ObjectId;
  type: string;
  strength?: number; // 1-10 scale
  description?: string;
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const RelationshipSchema = new Schema<IRelationship>({
  sourceType: { 
    type: String, 
    required: true,
    enum: ['contact', 'company'],
    index: true
  },
  sourceId: { 
    type: Schema.Types.ObjectId, 
    required: true,
    index: true
  },
  targetType: { 
    type: String, 
    required: true,
    enum: ['contact', 'company'],
    index: true
  },
  targetId: { 
    type: Schema.Types.ObjectId, 
    required: true,
    index: true
  },
  type: { 
    type: String, 
    required: true,
    enum: ['colleague', 'manager', 'report', 'friend', 'family', 'partner', 'supplier', 'customer', 'competitor', 'investor', 'other'],
    index: true
  },
  strength: { 
    type: Number,
    min: 1,
    max: 10
  },
  description: { 
    type: String 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  }
}, { 
  timestamps: true 
});

// Compound indexes for efficient querying
RelationshipSchema.index({ sourceType: 1, sourceId: 1 });
RelationshipSchema.index({ targetType: 1, targetId: 1 });
RelationshipSchema.index({ sourceType: 1, sourceId: 1, targetType: 1, targetId: 1 }, { unique: true });

// Create and export the model
export const Relationship: Model<IRelationship> = mongoose.models.Relationship || mongoose.model<IRelationship>('Relationship', RelationshipSchema);

export default Relationship;
