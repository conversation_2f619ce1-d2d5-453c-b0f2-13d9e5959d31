import mongoose, { Document, Schema } from 'mongoose';

/**
 * Interface for Workflow Trigger
 */
export interface IWorkflowTrigger {
  type: string;
  config: Record<string, any>;
  description: string;
}

/**
 * Enum for Workflow Trigger Types
 */
export enum WorkflowTriggerType {
  // Basic triggers
  LEAD_SCORE_CHANGE = 'lead_score_change',
  EMAIL_EVENT = 'email_event',
  FORM_SUBMISSION = 'form_submission',
  RECORD_UPDATE = 'record_update',
  WEBHOOK = 'webhook',

  // Time-based triggers
  SCHEDULED = 'scheduled',
  ANNIVERSARY = 'anniversary',

  // Behavioral triggers
  WEBSITE_ACTIVITY = 'website_activity',
  PRODUCT_USAGE = 'product_usage',

  // External system triggers
  LINKEDIN_EVENT = 'linkedin_event',

  // Advanced CRM triggers
  RELATIONSHIP_CHANGE = 'relationship_change',
  PIPELINE_VELOCITY = 'pipeline_velocity',
  TEAM_PERFORMANCE = 'team_performance'
}

/**
 * Interface for Workflow Action
 */
export interface IWorkflowAction {
  type: string;
  config: Record<string, any>;
  description: string;
}

/**
 * Enum for Workflow Action Types
 */
export enum WorkflowActionType {
  // Basic actions
  SEND_EMAIL = 'send_email',
  UPDATE_RECORD = 'update_record',
  CREATE_TASK = 'create_task',
  CALL_WEBHOOK = 'call_webhook',

  // Communication actions
  SEND_SMS = 'send_sms',
  SCHEDULE_MEETING = 'schedule_meeting',
  CREATE_VIDEO_MESSAGE = 'create_video_message',

  // Data enrichment actions
  ENRICH_CONTACT = 'enrich_contact',
  SCORE_LEAD = 'score_lead',
  GENERATE_AI_CONTENT = 'generate_ai_content',

  // Integration actions
  CREATE_DOCUMENT = 'create_document',
  UPDATE_EXTERNAL_SYSTEM = 'update_external_system',
  POST_TO_LINKEDIN = 'post_to_linkedin',

  // Advanced CRM actions
  CREATE_RELATIONSHIP = 'create_relationship',
  ASSIGN_TERRITORY = 'assign_territory',
  FORECAST_UPDATE = 'forecast_update'
}

/**
 * Interface for Workflow Condition
 */
export interface IWorkflowCondition {
  type: string;
  config: Record<string, any>;
  description: string;
}

/**
 * Enum for Workflow Condition Types
 */
export enum WorkflowConditionType {
  // Basic conditions
  IF = 'if',
  WAIT_UNTIL = 'wait_until',
  FOR_EACH = 'for_each',

  // Advanced conditions
  SWITCH = 'switch',
  PARALLEL = 'parallel',
  RETRY = 'retry',
  DELAY = 'delay',

  // Data conditions
  DATA_EXISTS = 'data_exists',
  DATA_EQUALS = 'data_equals',
  DATA_GREATER_THAN = 'data_greater_than',
  DATA_LESS_THAN = 'data_less_than',

  // Time conditions
  TIME_OF_DAY = 'time_of_day',
  DAY_OF_WEEK = 'day_of_week',
  BUSINESS_HOURS = 'business_hours'
}

/**
 * Interface for Workflow Node
 */
export interface IWorkflowNode {
  id: string;
  type: 'trigger' | 'action' | 'condition';
  position: { x: number; y: number };
  data: IWorkflowTrigger | IWorkflowAction | IWorkflowCondition;
}

/**
 * Interface for Workflow Edge
 */
export interface IWorkflowEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  condition?: Record<string, any>;
}

/**
 * Interface for Workflow Document
 */
export interface IWorkflow extends Document {
  tenant_id: string;
  name: string;
  description?: string;
  dsl_yaml: string;
  nodes: IWorkflowNode[];
  edges: IWorkflowEdge[];
  version: number;
  status: 'draft' | 'active' | 'paused' | 'archived';
  created_by: string;
  updated_by: string;
  created_at: Date;
  updated_at: Date;
  original_prompt?: string;
  tags?: string[];
  simulation_results?: {
    run_at: Date;
    expected_volume: number;
    edge_cases: string[];
    performance_metrics: Record<string, any>;
  };
  publishWorkflow(): Promise<IWorkflow>;
  rollbackToVersion(version: number): Promise<IWorkflow>;
  duplicateWorkflow(newName: string): Promise<IWorkflow>;
}

/**
 * Schema for Workflow
 */
const WorkflowSchema = new Schema<IWorkflow>(
  {
    tenant_id: {
      type: String,
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    dsl_yaml: {
      type: String,
      required: true,
    },
    nodes: [{
      id: String,
      type: {
        type: String,
        enum: ['trigger', 'action', 'condition'],
        required: true,
      },
      position: {
        x: Number,
        y: Number,
      },
      data: {
        type: Schema.Types.Mixed,
        required: true,
      },
    }],
    edges: [{
      id: String,
      source: String,
      target: String,
      label: String,
      condition: Schema.Types.Mixed,
    }],
    version: {
      type: Number,
      default: 1,
    },
    status: {
      type: String,
      enum: ['draft', 'active', 'paused', 'archived'],
      default: 'draft',
    },
    created_by: {
      type: String,
      required: true,
    },
    updated_by: {
      type: String,
      required: true,
    },
    original_prompt: String,
    tags: [String],
    simulation_results: {
      run_at: Date,
      expected_volume: Number,
      edge_cases: [String],
      performance_metrics: Schema.Types.Mixed,
    },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);

/**
 * Publish workflow - creates a new version and sets status to active
 */
WorkflowSchema.methods.publishWorkflow = async function(): Promise<IWorkflow> {
  // Increment version
  this.version += 1;
  this.status = 'active';

  // Save the workflow
  return this.save();
};

/**
 * Rollback to a specific version
 */
WorkflowSchema.methods.rollbackToVersion = async function(version: number): Promise<IWorkflow> {
  // Find the workflow version history
  const WorkflowVersion = mongoose.model('WorkflowVersion');
  const versionDoc = await WorkflowVersion.findOne({
    workflow_id: this._id,
    version: version,
  });

  if (!versionDoc) {
    throw new Error(`Version ${version} not found`);
  }

  // Update the current workflow with the version data
  this.dsl_yaml = versionDoc.dsl_yaml;
  this.nodes = versionDoc.nodes;
  this.edges = versionDoc.edges;
  this.version += 1; // Still increment version for tracking

  // Save the workflow
  return this.save();
};

/**
 * Duplicate workflow
 */
WorkflowSchema.methods.duplicateWorkflow = async function(newName: string): Promise<IWorkflow> {
  // Create a new workflow document
  const Workflow = mongoose.model('Workflow');
  const newWorkflow = new Workflow({
    tenant_id: this.tenant_id,
    name: newName || `${this.name} (Copy)`,
    description: this.description,
    dsl_yaml: this.dsl_yaml,
    nodes: this.nodes,
    edges: this.edges,
    status: 'draft', // Always start as draft
    created_by: this.updated_by, // Use the current user
    updated_by: this.updated_by,
    original_prompt: this.original_prompt,
    tags: this.tags,
  });

  // Save the new workflow
  return newWorkflow.save();
};

// Create and export the Workflow model
export const Workflow = mongoose.model<IWorkflow>('Workflow', WorkflowSchema);
