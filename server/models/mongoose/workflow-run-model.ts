import mongoose, { Document, Schema } from 'mongoose';

/**
 * Interface for Workflow Step Execution
 */
export interface IWorkflowStepExecution {
  node_id: string;
  started_at: Date;
  completed_at?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  error?: string;
  result?: Record<string, any>;
  duration_ms?: number;
}

/**
 * Interface for Workflow Run Document
 */
export interface IWorkflowRun extends Document {
  workflow_id: mongoose.Types.ObjectId;
  tenant_id: string;
  trigger_event: {
    type: string;
    data: Record<string, any>;
  };
  start_ts: Date;
  end_ts?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  error_log?: string;
  step_executions: IWorkflowStepExecution[];
  context_data?: Record<string, any>;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
  duration_ms?: number;
  is_simulation: boolean;
  affected_records?: {
    type: string;
    id: string;
    action: string;
  }[];
}

/**
 * Schema for Workflow Run
 */
const WorkflowRunSchema = new Schema<IWorkflowRun>(
  {
    workflow_id: {
      type: Schema.Types.ObjectId,
      ref: 'Workflow',
      required: true,
      index: true,
    },
    tenant_id: {
      type: String,
      required: true,
      index: true,
    },
    trigger_event: {
      type: {
        type: String,
        required: true,
      },
      data: {
        type: Schema.Types.Mixed,
        required: true,
      },
    },
    start_ts: {
      type: Date,
      default: Date.now,
    },
    end_ts: {
      type: Date,
    },
    status: {
      type: String,
      enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
      default: 'pending',
    },
    error_log: {
      type: String,
    },
    step_executions: [{
      node_id: {
        type: String,
        required: true,
      },
      started_at: {
        type: Date,
        required: true,
      },
      completed_at: {
        type: Date,
      },
      status: {
        type: String,
        enum: ['pending', 'running', 'completed', 'failed', 'skipped'],
        required: true,
      },
      error: {
        type: String,
      },
      result: {
        type: Schema.Types.Mixed,
      },
      duration_ms: {
        type: Number,
      },
    }],
    context_data: {
      type: Schema.Types.Mixed,
    },
    created_by: {
      type: String,
    },
    duration_ms: {
      type: Number,
    },
    is_simulation: {
      type: Boolean,
      default: false,
    },
    affected_records: [{
      type: {
        type: String,
        required: true,
      },
      id: {
        type: String,
        required: true,
      },
      action: {
        type: String,
        required: true,
      },
    }],
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);

// Add index for querying recent runs
WorkflowRunSchema.index({ tenant_id: 1, start_ts: -1 });

// Add index for workflow performance analysis
WorkflowRunSchema.index({ workflow_id: 1, status: 1, duration_ms: 1 });

/**
 * Pre-save hook to calculate duration if end_ts is set
 */
WorkflowRunSchema.pre('save', function(next) {
  if (this.end_ts && this.start_ts) {
    this.duration_ms = this.end_ts.getTime() - this.start_ts.getTime();
  }
  next();
});

// Create and export the WorkflowRun model
export const WorkflowRun = mongoose.model<IWorkflowRun>('WorkflowRun', WorkflowRunSchema);
