import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Interface for dataset field
 */
export interface IDatasetField {
  name: string;
  display_name: string;
  data_type: string; // 'string', 'number', 'date', 'boolean'
  description: string;
  is_dimension: boolean;
  is_metric: boolean;
  format?: string; // e.g., 'currency', 'percentage', 'date'
  aggregation?: string; // e.g., 'sum', 'avg', 'count'
}

/**
 * Interface for analytics dataset
 */
export interface IAnalyticsDataset extends Document {
  tenant_id: mongoose.Types.ObjectId;
  name: string;
  display_name: string;
  description: string;
  source_type: string; // 'bigquery', 'mongodb', 'neo4j'
  source_config: Record<string, any>;
  fields: IDatasetField[];
  is_active: boolean;
  refresh_frequency: string; // 'hourly', 'daily', 'weekly'
  last_refreshed_at?: Date;
  created_at: Date;
  updated_at: Date;
}

/**
 * Schema for dataset field
 */
const DatasetFieldSchema = new Schema<IDatasetField>({
  name: {
    type: String,
    required: true,
  },
  display_name: {
    type: String,
    required: true,
  },
  data_type: {
    type: String,
    required: true,
    enum: ['string', 'number', 'date', 'boolean'],
  },
  description: {
    type: String,
    required: true,
  },
  is_dimension: {
    type: Boolean,
    required: true,
    default: false,
  },
  is_metric: {
    type: Boolean,
    required: true,
    default: false,
  },
  format: {
    type: String,
    enum: ['currency', 'percentage', 'date', 'number', 'text'],
  },
  aggregation: {
    type: String,
    enum: ['sum', 'avg', 'count', 'min', 'max', 'distinct'],
  },
});

/**
 * Schema for analytics dataset
 */
const AnalyticsDatasetSchema = new Schema<IAnalyticsDataset>(
  {
    tenant_id: {
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
      index: true,
    },
    display_name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    source_type: {
      type: String,
      required: true,
      enum: ['bigquery', 'mongodb', 'neo4j'],
    },
    source_config: {
      type: Schema.Types.Mixed,
      required: true,
    },
    fields: {
      type: [DatasetFieldSchema],
      required: true,
      default: [],
    },
    is_active: {
      type: Boolean,
      required: true,
      default: true,
    },
    refresh_frequency: {
      type: String,
      required: true,
      enum: ['hourly', 'daily', 'weekly'],
    },
    last_refreshed_at: {
      type: Date,
    },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);

// Compound indexes for efficient querying
AnalyticsDatasetSchema.index({ tenant_id: 1, name: 1 }, { unique: true });
AnalyticsDatasetSchema.index({ tenant_id: 1, is_active: 1 });

// Create and export the model
export const AnalyticsDataset: Model<IAnalyticsDataset> = 
  mongoose.models.AnalyticsDataset || 
  mongoose.model<IAnalyticsDataset>('AnalyticsDataset', AnalyticsDatasetSchema);

export default AnalyticsDataset;
