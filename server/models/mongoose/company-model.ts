import mongoose, { Schema, Document, Model } from 'mongoose';
import { fromObjectId, toObjectId } from '../../utils/mongodb-utils';

export interface ICompany extends Document {
  name: string;
  domain?: string;
  industry?: string;
  size?: string;
  location?: string;
  description?: string;
  website?: string;
  logo?: string;
  status: string;
  owner: mongoose.Types.ObjectId;
  tags: string[];
  notes?: string;
  customFields: Record<string, any>;
  employees?: number;
  aiEnrichment?: Record<string, any>;
  createdBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const CompanySchema = new Schema<ICompany>({
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  domain: {
    type: String,
    trim: true,
    lowercase: true
  },
  industry: {
    type: String,
    trim: true
  },
  size: {
    type: String,
    enum: ['1-10', '11-50', '51-200', '201-500', '501-1000', '1001-5000', '5001-10000', '10001+']
  },
  location: {
    type: String,
    trim: true
  },
  description: {
    type: String
  },
  website: {
    type: String,
    trim: true,
    lowercase: true
  },
  logo: {
    type: String
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'lead', 'customer', 'prospect'],
    default: 'active',
    index: true
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  notes: {
    type: String
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  },
  employees: {
    type: Number
  },
  aiEnrichment: {
    type: Schema.Types.Mixed
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Text index for search
CompanySchema.index({ name: 'text', industry: 'text', description: 'text' });

// Type for converting MongoDB document to shared type
export function mapCompanyToType(doc: ICompany): any {
  return {
    id: fromObjectId(doc._id),
    name: doc.name,
    domain: doc.domain,
    industry: doc.industry,
    size: doc.size,
    location: doc.location,
    description: doc.description,
    website: doc.website,
    logo: doc.logo,
    status: doc.status,
    owner: fromObjectId(doc.owner),
    tags: doc.tags,
    notes: doc.notes,
    customFields: doc.customFields,
    employees: doc.employees,
    aiEnrichment: doc.aiEnrichment,
    createdBy: doc.createdBy ? fromObjectId(doc.createdBy) : undefined,
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

// Create and export the model
export const Company: Model<ICompany> = mongoose.models.Company || mongoose.model<ICompany>('Company', CompanySchema);

export default Company;
