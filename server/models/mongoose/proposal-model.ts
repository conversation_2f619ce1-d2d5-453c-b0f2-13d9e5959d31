import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IProposal extends Document {
  name: string;
  description?: string;
  opportunityId: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  templateId?: mongoose.Types.ObjectId;
  content: string; // HTML content
  sections: Array<{
    id: string;
    name: string;
    type: string;
    content: string;
    order: number;
  }>;
  status: string;
  value: number;
  currency: string;
  validUntil?: Date;
  sentAt?: Date;
  viewedAt?: Date;
  acceptedAt?: Date;
  rejectedAt?: Date;
  documentUrl?: string;
  documentId?: mongoose.Types.ObjectId;
  format?: string;
  availableFormats?: string[];
  createdBy: mongoose.Types.ObjectId;
  tags: string[];
  notes?: string;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const ProposalSchema = new Schema<IProposal>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  opportunityId: {
    type: Schema.Types.ObjectId,
    ref: 'Opportunity',
    required: true,
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    index: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  templateId: {
    type: Schema.Types.ObjectId,
    ref: 'ProposalTemplate'
  },
  content: {
    type: String,
    required: true
  },
  sections: [{
    id: { type: String, required: true },
    name: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: ['text', 'pricing', 'timeline', 'team', 'testimonials', 'terms', 'custom'],
      default: 'text'
    },
    content: { type: String, required: true },
    order: { type: Number, required: true }
  }],
  status: {
    type: String,
    required: true,
    enum: ['draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired'],
    default: 'draft',
    index: true
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  validUntil: {
    type: Date
  },
  sentAt: {
    type: Date
  },
  viewedAt: {
    type: Date
  },
  acceptedAt: {
    type: Date
  },
  rejectedAt: {
    type: Date
  },
  documentUrl: {
    type: String
  },
  documentId: {
    type: Schema.Types.ObjectId,
    ref: 'Document'
  },
  format: {
    type: String,
    enum: ['html', 'pdf', 'docx', 'markdown', 'claude-html'],
    default: 'html'
  },
  availableFormats: [{
    type: String,
    enum: ['html', 'pdf', 'docx', 'markdown', 'claude-html']
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  notes: {
    type: String
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
ProposalSchema.index({ name: 'text', description: 'text', notes: 'text' });

// Create and export the model
export const Proposal: Model<IProposal> = mongoose.models.Proposal || mongoose.model<IProposal>('Proposal', ProposalSchema);

export default Proposal;
