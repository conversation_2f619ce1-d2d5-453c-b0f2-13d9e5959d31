import mongoose, { Schema, Document, Model } from 'mongoose';

export interface INotification extends Document {
  userId: mongoose.Types.ObjectId;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  link?: string;
  relatedTo?: {
    type: string;
    id: mongoose.Types.ObjectId;
  };
  metadata?: Record<string, any>;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const NotificationSchema = new Schema<INotification>({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true,
    index: true
  },
  title: { 
    type: String, 
    required: true,
    trim: true
  },
  message: { 
    type: String, 
    required: true
  },
  type: { 
    type: String, 
    required: true,
    enum: ['info', 'success', 'warning', 'error', 'task', 'mention', 'system'],
    default: 'info',
    index: true
  },
  isRead: { 
    type: Boolean, 
    required: true,
    default: false,
    index: true
  },
  link: { 
    type: String 
  },
  relatedTo: { 
    type: {
      type: { 
        type: String,
        enum: ['contact', 'company', 'opportunity', 'activity', 'task', 'insight']
      },
      id: { 
        type: Schema.Types.ObjectId 
      }
    }
  },
  metadata: { 
    type: Schema.Types.Mixed 
  },
  expiresAt: { 
    type: Date,
    index: true
  }
}, { 
  timestamps: true 
});

// Compound indexes for efficient querying
NotificationSchema.index({ userId: 1, isRead: 1 });
NotificationSchema.index({ userId: 1, createdAt: -1 });

// Create and export the model
export const Notification: Model<INotification> = mongoose.models.Notification || mongoose.model<INotification>('Notification', NotificationSchema);

export default Notification;
