import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * DNS Record Types
 */
export enum DNSRecordType {
  TXT = 'TXT',
  CNAME = 'CNAME',
  MX = 'MX',
  A = 'A',
  AAAA = 'AAAA'
}

/**
 * Domain Verification Status
 */
export enum DomainVerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  FAILED = 'failed'
}

/**
 * DNS Record Interface
 */
export interface IDNSRecord {
  type: DNSRecordType;
  host: string;
  value: string;
  priority?: number;
  ttl?: number;
}

/**
 * Tenant Domain Interface
 */
export interface ITenantDomain extends Document {
  tenantId: mongoose.Types.ObjectId;
  domain: string;
  resendDomainId?: string;
  trackingDomain?: string;
  webhookSecret?: string;
  verificationStatus: DomainVerificationStatus;
  verificationRecords: IDNSRecord[];
  trackingRecords?: IDNSRecord[];
  lastVerificationCheck?: Date;
  isDefault: boolean;
  isActive: boolean;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DNS Record Schema
 */
const DNSRecordSchema = new Schema<IDNSRecord>({
  type: {
    type: String,
    required: true,
    enum: Object.values(DNSRecordType)
  },
  host: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  priority: {
    type: Number
  },
  ttl: {
    type: Number
  }
});

/**
 * Tenant Domain Schema
 */
const TenantDomainSchema = new Schema<ITenantDomain>({
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    required: true,
    index: true
  },
  domain: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    index: true
  },
  resendDomainId: {
    type: String,
    index: true
  },
  trackingDomain: {
    type: String,
    trim: true,
    lowercase: true
  },
  webhookSecret: {
    type: String
  },
  verificationStatus: {
    type: String,
    required: true,
    enum: Object.values(DomainVerificationStatus),
    default: DomainVerificationStatus.PENDING,
    index: true
  },
  verificationRecords: [DNSRecordSchema],
  trackingRecords: [DNSRecordSchema],
  lastVerificationCheck: {
    type: Date
  },
  isDefault: {
    type: Boolean,
    default: false,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Compound index for tenant and domain
TenantDomainSchema.index({ tenantId: 1, domain: 1 }, { unique: true });

// Create and export the model
export const TenantDomain: Model<ITenantDomain> = mongoose.models.TenantDomain || 
  mongoose.model<ITenantDomain>('TenantDomain', TenantDomainSchema);

export default TenantDomain;
