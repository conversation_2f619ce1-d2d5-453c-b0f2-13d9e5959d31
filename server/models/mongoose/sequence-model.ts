import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Sequence Step Channel Types
 */
export enum SequenceStepChannelType {
  EMAIL = 'email',
  LINKEDIN = 'linkedin',
  CALL = 'call',
  SMS = 'sms',
  TASK = 'task',
  LINKEDIN_VOICE = 'linkedin_voice',
  LINKEDIN_INMAIL = 'linkedin_inmail',
  WHATSAPP = 'whatsapp',
  TELEGRAM = 'telegram'
}

/**
 * Sequence Step Timing Types
 */
export enum SequenceStepTimingType {
  DAYS_AFTER_PREVIOUS = 'days_after_previous',
  SPECIFIC_DATE = 'specific_date',
  DAYS_AFTER_START = 'days_after_start',
  IMMEDIATELY = 'immediately',
  WEEKDAY = 'weekday',
  AFTER_REPLY = 'after_reply',
  AFTER_OPEN = 'after_open',
  AFTER_CLICK = 'after_click',
  AFTER_NO_REPLY = 'after_no_reply'
}

/**
 * Sequence Step Interface
 */
export interface ISequenceStep extends Document {
  sequenceId: mongoose.Types.ObjectId;
  stepNumber: number;
  channel: SequenceStepChannelType;
  templateId?: mongoose.Types.ObjectId;
  content?: string;
  subject?: string;
  timing: {
    type: SequenceStepTimingType;
    value: number | string | Date;
    timeOfDay?: string;
  };
  aiDynamic: boolean;
  conditions?: {
    type: string;
    value: any;
  }[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Sequence Interface
 */
export interface ISequence extends Document {
  tenantId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  goal: string;
  targetConversionDays: number;
  isActive: boolean;
  isTemplate: boolean;
  policyVersion?: string;
  stats: {
    totalEnrolled: number;
    activeEnrolled: number;
    completedEnrolled: number;
    totalEmails: number;
    totalOpens: number;
    totalClicks: number;
    totalReplies: number;
    totalMeetingsBooked: number;
    conversionRate: number;
    averageCompletionDays: number;
  };
  tags: string[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Sequence Enrollment Interface
 */
export interface ISequenceEnrollment extends Document {
  sequenceId: mongoose.Types.ObjectId;
  contactId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  tenantId?: mongoose.Types.ObjectId;
  status: 'active' | 'paused' | 'completed' | 'stopped';
  currentStepNumber: number;
  startDate: Date;
  completionDate?: Date;
  pauseReason?: string;
  stopReason?: string;
  stepHistory: {
    stepNumber: number;
    executedAt: Date;
    status: 'success' | 'failed' | 'skipped';
    trackingId?: mongoose.Types.ObjectId;
    result?: string;
  }[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Sequence Step Schema
 */
const SequenceStepSchema = new Schema<ISequenceStep>({
  sequenceId: {
    type: Schema.Types.ObjectId,
    ref: 'Sequence',
    required: true,
    index: true
  },
  stepNumber: {
    type: Number,
    required: true,
    min: 1
  },
  channel: {
    type: String,
    required: true,
    enum: Object.values(SequenceStepChannelType)
  },
  templateId: {
    type: Schema.Types.ObjectId,
    ref: 'Template'
  },
  content: {
    type: String
  },
  subject: {
    type: String
  },
  timing: {
    type: {
      type: String,
      required: true,
      enum: Object.values(SequenceStepTimingType)
    },
    value: {
      type: Schema.Types.Mixed,
      required: true
    },
    timeOfDay: {
      type: String
    }
  },
  aiDynamic: {
    type: Boolean,
    default: false
  },
  conditions: [{
    type: {
      type: String
    },
    value: Schema.Types.Mixed
  }],
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

/**
 * Sequence Schema
 */
const SequenceSchema = new Schema<ISequence>({
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  goal: {
    type: String,
    required: true
  },
  targetConversionDays: {
    type: Number,
    default: 14
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  isTemplate: {
    type: Boolean,
    default: false,
    index: true
  },
  policyVersion: {
    type: String
  },
  stats: {
    totalEnrolled: {
      type: Number,
      default: 0
    },
    activeEnrolled: {
      type: Number,
      default: 0
    },
    completedEnrolled: {
      type: Number,
      default: 0
    },
    totalEmails: {
      type: Number,
      default: 0
    },
    totalOpens: {
      type: Number,
      default: 0
    },
    totalClicks: {
      type: Number,
      default: 0
    },
    totalReplies: {
      type: Number,
      default: 0
    },
    totalMeetingsBooked: {
      type: Number,
      default: 0
    },
    conversionRate: {
      type: Number,
      default: 0
    },
    averageCompletionDays: {
      type: Number,
      default: 0
    }
  },
  tags: [{
    type: String,
    trim: true
  }],
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

/**
 * Sequence Enrollment Schema
 */
const SequenceEnrollmentSchema = new Schema<ISequenceEnrollment>({
  sequenceId: {
    type: Schema.Types.ObjectId,
    ref: 'Sequence',
    required: true,
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    required: true,
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'paused', 'completed', 'stopped'],
    default: 'active',
    index: true
  },
  currentStepNumber: {
    type: Number,
    required: true,
    default: 1
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  completionDate: {
    type: Date
  },
  pauseReason: {
    type: String
  },
  stopReason: {
    type: String
  },
  stepHistory: [{
    stepNumber: {
      type: Number,
      required: true
    },
    executedAt: {
      type: Date,
      required: true
    },
    status: {
      type: String,
      required: true,
      enum: ['success', 'failed', 'skipped']
    },
    trackingId: {
      type: Schema.Types.ObjectId,
      ref: 'EmailTracking'
    },
    result: {
      type: String
    }
  }],
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Indexes
SequenceStepSchema.index({ sequenceId: 1, stepNumber: 1 }, { unique: true });
SequenceSchema.index({ userId: 1, name: 1 }, { unique: true });
SequenceSchema.index({ 'tags': 1 });
SequenceEnrollmentSchema.index({ sequenceId: 1, contactId: 1 }, { unique: true });
SequenceEnrollmentSchema.index({ status: 1, currentStepNumber: 1 });
SequenceEnrollmentSchema.index({ startDate: 1 });

// Create and export the models
export const SequenceStep: Model<ISequenceStep> = mongoose.models.SequenceStep || 
  mongoose.model<ISequenceStep>('SequenceStep', SequenceStepSchema);

export const Sequence: Model<ISequence> = mongoose.models.Sequence || 
  mongoose.model<ISequence>('Sequence', SequenceSchema);

export const SequenceEnrollment: Model<ISequenceEnrollment> = mongoose.models.SequenceEnrollment || 
  mongoose.model<ISequenceEnrollment>('SequenceEnrollment', SequenceEnrollmentSchema);

export default { Sequence, SequenceStep, SequenceEnrollment };
