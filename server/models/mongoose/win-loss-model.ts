import mongoose, { Document, Model, Schema } from 'mongoose';

// Win/Loss Analysis interface
export interface IWinLossAnalysis extends Document {
  title: string;
  description?: string;
  opportunityId: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  outcome: 'won' | 'lost';
  value: number;
  currency: string;
  closedDate: Date;
  stage: string;
  salesCycle: number; // in days
  keyFactors: Array<{
    factor: string;
    impact: 'positive' | 'negative';
    weight: number; // 1-10 scale
    description?: string;
  }>;
  competitorInfo?: {
    name?: string;
    strengths?: string[];
    weaknesses?: string[];
    pricingDifference?: number; // percentage difference
  };
  feedback?: string;
  learnings: string[];
  recommendations: string[];
  isAIGenerated: boolean;
  createdBy: mongoose.Types.ObjectId;
  tags: string[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Win/Loss Factor interface
export interface IWinLossFactor extends Document {
  name: string;
  description?: string;
  category: string;
  impact: 'positive' | 'negative' | 'neutral';
  frequency: number; // count of occurrences
  averageWeight: number; // average weight across analyses
  outcomeCorrelation: number; // -1 to 1 scale (negative to positive correlation)
  isActive: boolean;
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Win/Loss Analysis schema
const WinLossAnalysisSchema = new Schema<IWinLossAnalysis>({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  opportunityId: {
    type: Schema.Types.ObjectId,
    ref: 'Opportunity',
    required: true,
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    index: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  outcome: {
    type: String,
    required: true,
    enum: ['won', 'lost'],
    index: true
  },
  value: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  closedDate: {
    type: Date,
    required: true,
    index: true
  },
  stage: {
    type: String,
    required: true
  },
  salesCycle: {
    type: Number,
    required: true
  },
  keyFactors: [{
    factor: {
      type: String,
      required: true
    },
    impact: {
      type: String,
      required: true,
      enum: ['positive', 'negative']
    },
    weight: {
      type: Number,
      required: true,
      min: 1,
      max: 10
    },
    description: {
      type: String
    }
  }],
  competitorInfo: {
    name: {
      type: String
    },
    strengths: [{
      type: String
    }],
    weaknesses: [{
      type: String
    }],
    pricingDifference: {
      type: Number
    }
  },
  feedback: {
    type: String
  },
  learnings: [{
    type: String
  }],
  recommendations: [{
    type: String
  }],
  isAIGenerated: {
    type: Boolean,
    required: true,
    default: false
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
WinLossAnalysisSchema.index({ title: 'text', description: 'text', feedback: 'text' });

// Win/Loss Factor schema
const WinLossFactorSchema = new Schema<IWinLossFactor>({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  description: {
    type: String
  },
  category: {
    type: String,
    required: true,
    index: true
  },
  impact: {
    type: String,
    required: true,
    enum: ['positive', 'negative', 'neutral'],
    default: 'neutral'
  },
  frequency: {
    type: Number,
    required: true,
    default: 0
  },
  averageWeight: {
    type: Number,
    required: true,
    default: 5
  },
  outcomeCorrelation: {
    type: Number,
    required: true,
    default: 0,
    min: -1,
    max: 1
  },
  isActive: {
    type: Boolean,
    required: true,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Text index for search
WinLossFactorSchema.index({ name: 'text', description: 'text' });

// Create and export the models
export const WinLossAnalysis: Model<IWinLossAnalysis> = mongoose.models.WinLossAnalysis || 
  mongoose.model<IWinLossAnalysis>('WinLossAnalysis', WinLossAnalysisSchema);

export const WinLossFactor: Model<IWinLossFactor> = mongoose.models.WinLossFactor || 
  mongoose.model<IWinLossFactor>('WinLossFactor', WinLossFactorSchema);

export default { WinLossAnalysis, WinLossFactor };
