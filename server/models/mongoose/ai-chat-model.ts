import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export interface IAiChat extends Document {
  userId: mongoose.Types.ObjectId;
  title: string;
  messages: IMessage[];
  context?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const MessageSchema = new Schema<IMessage>({
  role: { 
    type: String, 
    required: true,
    enum: ['user', 'assistant', 'system']
  },
  content: { 
    type: String, 
    required: true
  },
  timestamp: { 
    type: Date, 
    default: Date.now
  }
});

const AiChatSchema = new Schema<IAiChat>({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true,
    index: true
  },
  title: { 
    type: String, 
    required: true,
    trim: true
  },
  messages: [MessageSchema],
  context: { 
    type: Schema.Types.Mixed 
  }
}, { 
  timestamps: true 
});

// Text index for search
AiChatSchema.index({ title: 'text', 'messages.content': 'text' });

// Create and export the model
export const AiChat: Model<IAiChat> = mongoose.models.AiChat || mongoose.model<IAiChat>('AiChat', AiChatSchema);

export default AiChat;
