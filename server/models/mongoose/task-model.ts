import mongoose, { Schema, Document, Model } from 'mongoose';

export interface ITask extends Document {
  title: string;
  description?: string;
  status: string;
  priority: string;
  dueDate?: Date;
  completedAt?: Date;
  assignedTo: mongoose.Types.ObjectId;
  assignedBy: mongoose.Types.ObjectId;
  relatedTo?: {
    type: string;
    id: mongoose.Types.ObjectId;
  };
  tags: string[];
  reminderAt?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const TaskSchema = new Schema<ITask>({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  status: {
    type: String,
    required: true,
    enum: ['todo', 'in_progress', 'completed', 'deferred', 'canceled'],
    default: 'todo',
    index: true
  },
  priority: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  },
  dueDate: {
    type: Date,
    index: true
  },
  completedAt: {
    type: Date
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  assignedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  relatedTo: {
    type: {
      type: {
        type: String,
        enum: ['contact', 'company', 'opportunity', 'activity']
      },
      id: {
        type: Schema.Types.ObjectId
      }
    }
  },
  tags: [{
    type: String,
    trim: true
  }],
  reminderAt: {
    type: Date,
    index: true
  },
  notes: {
    type: String
  }
}, {
  timestamps: true
});

// Text index for search
TaskSchema.index({ title: 'text', description: 'text', notes: 'text' });

// Compound indexes for efficient querying
TaskSchema.index({ assignedTo: 1, status: 1 });
TaskSchema.index({ assignedTo: 1, dueDate: 1 });
TaskSchema.index({ status: 1, dueDate: 1 });

// Create and export the model
export const Task: Model<ITask> = mongoose.models.Task || mongoose.model<ITask>('Task', TaskSchema);

export default Task;
