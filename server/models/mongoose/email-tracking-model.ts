import mongoose, { Schema, Document, Model } from 'mongoose';

/**
 * Email Tracking Event Types
 */
export enum EmailEventType {
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  ATTACHMENT_VIEWED = 'attachment_viewed',
  REPLIED = 'replied',
  BOUNCED = 'bounced',
  UNSUBSCRIBED = 'unsubscribed',
  SPAM = 'spam',
  ERROR = 'error'
}

/**
 * Email Event Interface
 */
export interface IEmailEvent {
  type: EmailEventType;
  timestamp: Date;
  metadata: {
    ip?: string;
    userAgent?: string;
    device?: string;
    location?: {
      city?: string;
      country?: string;
      region?: string;
    };
    url?: string;
    linkId?: string;
    attachmentId?: string;
    errorMessage?: string;
  };
}

/**
 * Email Tracking Interface
 */
export interface IEmailTracking extends Document {
  tenantId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  sequenceId?: mongoose.Types.ObjectId;
  sequenceStepId?: mongoose.Types.ObjectId;
  messageId: string;
  subject: string;
  recipient: string;
  sender: string;
  status: EmailEventType;
  events: IEmailEvent[];
  pixelId: string;
  trackingEnabled: boolean;
  linkTrackingEnabled: boolean;
  attachmentTrackingEnabled: boolean;
  replyDraftGenerated: boolean;
  replyDraftId?: string;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Email Event Schema
 */
const EmailEventSchema = new Schema<IEmailEvent>({
  type: {
    type: String,
    required: true,
    enum: Object.values(EmailEventType)
  },
  timestamp: {
    type: Date,
    required: true,
    default: Date.now
  },
  metadata: {
    ip: String,
    userAgent: String,
    device: String,
    location: {
      city: String,
      country: String,
      region: String
    },
    url: String,
    linkId: String,
    attachmentId: String,
    errorMessage: String
  }
});

/**
 * Email Tracking Schema
 */
const EmailTrackingSchema = new Schema<IEmailTracking>({
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    index: true
  },
  sequenceId: {
    type: Schema.Types.ObjectId,
    ref: 'Sequence',
    index: true
  },
  sequenceStepId: {
    type: Schema.Types.ObjectId,
    ref: 'SequenceStep',
    index: true
  },
  messageId: {
    type: String,
    required: true,
    index: true
  },
  subject: {
    type: String,
    required: true
  },
  recipient: {
    type: String,
    required: true,
    index: true
  },
  sender: {
    type: String,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: Object.values(EmailEventType),
    default: EmailEventType.SENT,
    index: true
  },
  events: [EmailEventSchema],
  pixelId: {
    type: String,
    required: true,
    index: true
  },
  trackingEnabled: {
    type: Boolean,
    default: true
  },
  linkTrackingEnabled: {
    type: Boolean,
    default: true
  },
  attachmentTrackingEnabled: {
    type: Boolean,
    default: true
  },
  replyDraftGenerated: {
    type: Boolean,
    default: false
  },
  replyDraftId: {
    type: String
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Indexes
EmailTrackingSchema.index({ 'events.timestamp': -1 });
EmailTrackingSchema.index({ 'events.type': 1, 'events.timestamp': -1 });
EmailTrackingSchema.index({ recipient: 1, status: 1 });
EmailTrackingSchema.index({ sequenceId: 1, status: 1 });

// Create and export the model
export const EmailTracking: Model<IEmailTracking> = mongoose.models.EmailTracking || 
  mongoose.model<IEmailTracking>('EmailTracking', EmailTrackingSchema);

export default EmailTracking;
