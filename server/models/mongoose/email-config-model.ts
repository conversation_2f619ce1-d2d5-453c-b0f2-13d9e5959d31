import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IEmailConfig extends Document {
  tenantId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses' | 'gmail' | 'outlook' | 'resend' | 'other';
  isEnabled: boolean;
  fromName: string;
  fromEmail: string;
  replyToEmail?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
  apiKey?: string;
  apiSecret?: string;
  region?: string;
  providerSettings?: Record<string, any>;
  templates: {
    proposalShare?: {
      subject: string;
      body: string;
    };
    proposalAccepted?: {
      subject: string;
      body: string;
    };
    proposalRejected?: {
      subject: string;
      body: string;
    };
  };
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const EmailConfigSchema = new Schema<IEmailConfig>({
  tenantId: {
    type: Schema.Types.ObjectId,
    ref: 'Tenant',
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  provider: {
    type: String,
    required: true,
    enum: ['smtp', 'sendgrid', 'mailgun', 'ses', 'gmail', 'outlook', 'resend', 'other'],
    default: 'smtp'
  },
  isEnabled: {
    type: Boolean,
    required: true,
    default: false
  },
  fromName: {
    type: String,
    required: true,
    trim: true
  },
  fromEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  replyToEmail: {
    type: String,
    trim: true,
    lowercase: true
  },
  smtpHost: {
    type: String,
    trim: true
  },
  smtpPort: {
    type: Number,
    min: 1,
    max: 65535
  },
  smtpUsername: {
    type: String,
    trim: true
  },
  smtpPassword: {
    type: String
  },
  smtpSecure: {
    type: Boolean,
    default: true
  },
  apiKey: {
    type: String
  },
  apiSecret: {
    type: String
  },
  region: {
    type: String
  },
  providerSettings: {
    type: Schema.Types.Mixed,
    default: {}
  },
  templates: {
    proposalShare: {
      subject: {
        type: String,
        default: "{{senderName}} has shared a proposal with you"
      },
      body: {
        type: String,
        default: `<p>Hello {{recipientName}},</p>
<p>{{senderName}} has shared a proposal with you: <strong>{{proposalName}}</strong></p>
<p>You can view the proposal by clicking the link below:</p>
<p><a href="{{proposalUrl}}">View Proposal</a></p>
<p>This link will expire on {{expirationDate}}.</p>
<p>{{message}}</p>
<p>Best regards,<br>{{senderName}}</p>`
      }
    },
    proposalAccepted: {
      subject: {
        type: String,
        default: "Proposal Accepted: {{proposalName}}"
      },
      body: {
        type: String,
        default: `<p>Hello {{recipientName}},</p>
<p>Great news! Your proposal <strong>{{proposalName}}</strong> has been accepted.</p>
<p>Next steps:</p>
<ol>
  <li>Schedule a follow-up meeting</li>
  <li>Prepare the contract</li>
  <li>Discuss implementation timeline</li>
</ol>
<p>Best regards,<br>{{senderName}}</p>`
      }
    },
    proposalRejected: {
      subject: {
        type: String,
        default: "Proposal Status Update: {{proposalName}}"
      },
      body: {
        type: String,
        default: `<p>Hello {{recipientName}},</p>
<p>Your proposal <strong>{{proposalName}}</strong> was not accepted at this time.</p>
<p>Reason: {{rejectionReason}}</p>
<p>Let's schedule a follow-up call to discuss next steps.</p>
<p>Best regards,<br>{{senderName}}</p>`
      }
    }
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Create and export the model
export const EmailConfig: Model<IEmailConfig> = mongoose.models.EmailConfig || mongoose.model<IEmailConfig>('EmailConfig', EmailConfigSchema);

export default EmailConfig;
