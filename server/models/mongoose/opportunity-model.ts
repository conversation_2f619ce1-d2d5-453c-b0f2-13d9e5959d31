import mongoose, { Schema, Document, Model } from 'mongoose';
import { fromObjectId, toObjectId } from '../../utils/mongodb-utils';

export interface IOpportunity extends Document {
  name: string;
  companyId?: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  value: number;
  currency: string;
  stage: string;
  probability: number;
  expectedCloseDate?: Date;
  actualCloseDate?: Date;
  source?: string;
  owner: mongoose.Types.ObjectId;
  description?: string;
  tags: string[];
  notes?: string;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const OpportunitySchema = new Schema<IOpportunity>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    index: true
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  stage: {
    type: String,
    required: true,
    enum: ['prospecting', 'qualification', 'needs_analysis', 'value_proposition', 'decision_makers', 'proposal', 'negotiation', 'closed_won', 'closed_lost'],
    default: 'prospecting',
    index: true
  },
  probability: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 0
  },
  expectedCloseDate: {
    type: Date
  },
  actualCloseDate: {
    type: Date
  },
  source: {
    type: String,
    trim: true
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  description: {
    type: String
  },
  tags: [{
    type: String,
    trim: true
  }],
  notes: {
    type: String
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
OpportunitySchema.index({ name: 'text', description: 'text' });

// Index for reporting
OpportunitySchema.index({ stage: 1, expectedCloseDate: 1 });
OpportunitySchema.index({ stage: 1, actualCloseDate: 1 });

// Type for converting MongoDB document to shared type
export function mapOpportunityToType(doc: IOpportunity): any {
  return {
    id: fromObjectId(doc._id),
    name: doc.name,
    companyId: doc.companyId ? fromObjectId(doc.companyId) : undefined,
    contactId: doc.contactId ? fromObjectId(doc.contactId) : undefined,
    value: doc.value,
    currency: doc.currency,
    stage: doc.stage,
    probability: doc.probability,
    expectedCloseDate: doc.expectedCloseDate?.toISOString(),
    actualCloseDate: doc.actualCloseDate?.toISOString(),
    source: doc.source,
    owner: fromObjectId(doc.owner),
    description: doc.description,
    tags: doc.tags,
    notes: doc.notes,
    customFields: doc.customFields,
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

// Create and export the model
export const Opportunity: Model<IOpportunity> = mongoose.models.Opportunity || mongoose.model<IOpportunity>('Opportunity', OpportunitySchema);

export default Opportunity;
