import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IProposalAnalytics extends Document {
  proposalId: mongoose.Types.ObjectId;
  views: Array<{
    timestamp: Date;
    ip?: string;
    userAgent?: string;
    referrer?: string;
    viewDuration?: number; // in seconds
    isUnique: boolean;
    shareToken?: string;
  }>;
  downloads: Array<{
    timestamp: Date;
    format: string;
    ip?: string;
    userAgent?: string;
    isUnique: boolean;
    shareToken?: string;
  }>;
  shares: Array<{
    timestamp: Date;
    method: 'link' | 'email' | 'social';
    platform?: string; // for social shares
    recipientEmail?: string; // for email shares
    shareToken: string;
    expiresAt: Date;
    sharedBy: mongoose.Types.ObjectId;
  }>;
  interactions: Array<{
    timestamp: Date;
    type: string;
    data: Record<string, any>;
    ip?: string;
    userAgent?: string;
    shareToken?: string;
  }>;
  totalViews: number;
  uniqueViews: number;
  totalDownloads: number;
  totalShares: number;
  averageViewDuration: number; // in seconds
  lastViewedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ProposalAnalyticsSchema = new Schema<IProposalAnalytics>({
  proposalId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Proposal',
    required: true,
    index: true
  },
  views: [{
    timestamp: { 
      type: Date, 
      required: true,
      default: Date.now
    },
    ip: { 
      type: String 
    },
    userAgent: { 
      type: String 
    },
    referrer: { 
      type: String 
    },
    viewDuration: { 
      type: Number,
      min: 0
    },
    isUnique: { 
      type: Boolean, 
      required: true,
      default: true
    },
    shareToken: { 
      type: String 
    }
  }],
  downloads: [{
    timestamp: { 
      type: Date, 
      required: true,
      default: Date.now
    },
    format: { 
      type: String, 
      required: true,
      enum: ['pdf', 'docx', 'markdown']
    },
    ip: { 
      type: String 
    },
    userAgent: { 
      type: String 
    },
    isUnique: { 
      type: Boolean, 
      required: true,
      default: true
    },
    shareToken: { 
      type: String 
    }
  }],
  shares: [{
    timestamp: { 
      type: Date, 
      required: true,
      default: Date.now
    },
    method: { 
      type: String, 
      required: true,
      enum: ['link', 'email', 'social']
    },
    platform: { 
      type: String 
    },
    recipientEmail: { 
      type: String 
    },
    shareToken: { 
      type: String, 
      required: true
    },
    expiresAt: { 
      type: Date, 
      required: true
    },
    sharedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User',
      required: true
    }
  }],
  interactions: [{
    timestamp: { 
      type: Date, 
      required: true,
      default: Date.now
    },
    type: { 
      type: String, 
      required: true
    },
    data: { 
      type: Schema.Types.Mixed, 
      default: {}
    },
    ip: { 
      type: String 
    },
    userAgent: { 
      type: String 
    },
    shareToken: { 
      type: String 
    }
  }],
  totalViews: { 
    type: Number, 
    required: true,
    default: 0,
    min: 0
  },
  uniqueViews: { 
    type: Number, 
    required: true,
    default: 0,
    min: 0
  },
  totalDownloads: { 
    type: Number, 
    required: true,
    default: 0,
    min: 0
  },
  totalShares: { 
    type: Number, 
    required: true,
    default: 0,
    min: 0
  },
  averageViewDuration: { 
    type: Number, 
    required: true,
    default: 0,
    min: 0
  },
  lastViewedAt: { 
    type: Date 
  }
}, { 
  timestamps: true 
});

// Create and export the model
export const ProposalAnalytics: Model<IProposalAnalytics> = mongoose.models.ProposalAnalytics || mongoose.model<IProposalAnalytics>('ProposalAnalytics', ProposalAnalyticsSchema);

export default ProposalAnalytics;
