import mongoose, { Schema, Model, Document } from 'mongoose';
import { fromObjectId, toObjectId, isValidObjectId } from '../../utils/mongodb-utils';

// Define our own types instead of importing from @types/analytics
export interface AnalyticsContext {
  page?: {
    url?: string;
    path?: string;
    title?: string;
    referrer?: string;
  };
  userAgent?: string;
  ip?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  device?: {
    type?: string;
    browser?: string;
    os?: string;
  };
}

export interface AnalyticsEvent {
  id: string;
  userId?: string;
  tenantId?: string;
  sessionId: string;
  eventType: string;
  eventName: string;
  properties: Record<string, any>;
  context: AnalyticsContext;
  timestamp: string;
}

export interface IAnalyticsEvent extends Document {
  userId?: mongoose.Types.ObjectId;
  tenantId?: mongoose.Types.ObjectId;
  sessionId: string;
  eventType: string;
  eventName: string;
  properties: Record<string, any>;
  context: {
    page?: {
      url?: string;
      path?: string;
      title?: string;
      referrer?: string;
    };
    userAgent?: string;
    ip?: string;
    location?: {
      country?: string;
      region?: string;
      city?: string;
    };
    device?: {
      type?: string;
      browser?: string;
      os?: string;
    };
  };
  timestamp: Date;
}

// Type for converting MongoDB document to shared type
export function mapAnalyticsEventToType(doc: IAnalyticsEvent): AnalyticsEvent {
  return {
    id: fromObjectId(doc._id),
    userId: doc.userId ? fromObjectId(doc.userId) : undefined,
    tenantId: doc.tenantId ? fromObjectId(doc.tenantId) : undefined,
    sessionId: doc.sessionId,
    eventType: doc.eventType,
    eventName: doc.eventName,
    properties: doc.properties,
    context: doc.context as AnalyticsContext,
    timestamp: doc.timestamp.toISOString(),
  };
}

const AnalyticsEventSchema = new Schema<IAnalyticsEvent>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      index: true,
    },
    tenantId: {
      type: Schema.Types.ObjectId,
      ref: 'Tenant',
      index: true,
    },
    sessionId: {
      type: String,
      required: true,
      index: true,
    },
    eventType: {
      type: String,
      required: true,
      index: true,
    },
    eventName: {
      type: String,
      required: true,
      index: true,
    },
    properties: {
      type: Schema.Types.Mixed,
      default: {},
    },
    context: {
      page: {
        url: String,
        path: String,
        title: String,
        referrer: String,
      },
      userAgent: String,
      ip: String,
      location: {
        country: String,
        region: String,
        city: String,
      },
      device: {
        type: String,
        browser: String,
        os: String,
      },
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true,
    },
  },
  {
    timestamps: false,
  }
);

// Indexes
AnalyticsEventSchema.index({ eventType: 1, eventName: 1 });
AnalyticsEventSchema.index({ timestamp: 1 });
AnalyticsEventSchema.index({ userId: 1, timestamp: 1 });
AnalyticsEventSchema.index({ tenantId: 1, timestamp: 1 });
AnalyticsEventSchema.index({ 'context.page.path': 1 });

// Export the model
const AnalyticsEventModel: Model<IAnalyticsEvent> = mongoose.models.AnalyticsEvent || mongoose.model<IAnalyticsEvent>('AnalyticsEvent', AnalyticsEventSchema);
export { AnalyticsEventModel as AnalyticsEvent };
