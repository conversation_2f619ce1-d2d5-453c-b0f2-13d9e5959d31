import mongoose, { Schema, Document, Model } from 'mongoose';
import { fromObjectId, toObjectId } from '../../utils/mongodb-utils';

export interface IObjectionResponse extends Document {
  objectionId: mongoose.Types.ObjectId;
  response: string;
  context: string;
  effectiveness: number; // 1-5 rating
  opportunityId?: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  createdBy: mongoose.Types.ObjectId;
  usedCount: number;
  successCount: number;
  isAIGenerated: boolean;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const ObjectionResponseSchema = new Schema<IObjectionResponse>({
  objectionId: {
    type: Schema.Types.ObjectId,
    ref: 'Objection',
    required: true,
    index: true
  },
  response: {
    type: String,
    required: true
  },
  context: {
    type: String,
    required: true
  },
  effectiveness: {
    type: Number,
    min: 1,
    max: 5,
    default: 3,
    index: true
  },
  opportunityId: {
    type: Schema.Types.ObjectId,
    ref: 'Opportunity',
    index: true
  },
  contactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    index: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    index: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  usedCount: {
    type: Number,
    default: 0,
    min: 0
  },
  successCount: {
    type: Number,
    default: 0,
    min: 0
  },
  isAIGenerated: {
    type: Boolean,
    default: false,
    index: true
  },
  customFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Text index for search
ObjectionResponseSchema.index({ response: 'text', context: 'text' });

// Type for converting MongoDB document to shared type
export function mapObjectionResponseToType(doc: IObjectionResponse): any {
  return {
    id: fromObjectId(doc._id),
    objectionId: fromObjectId(doc.objectionId),
    response: doc.response,
    context: doc.context,
    effectiveness: doc.effectiveness,
    opportunityId: doc.opportunityId ? fromObjectId(doc.opportunityId) : undefined,
    contactId: doc.contactId ? fromObjectId(doc.contactId) : undefined,
    companyId: doc.companyId ? fromObjectId(doc.companyId) : undefined,
    createdBy: fromObjectId(doc.createdBy),
    usedCount: doc.usedCount,
    successCount: doc.successCount,
    isAIGenerated: doc.isAIGenerated,
    customFields: doc.customFields,
    createdAt: doc.createdAt.toISOString(),
    updatedAt: doc.updatedAt.toISOString(),
  };
}

// Create and export the model
export const ObjectionResponse: Model<IObjectionResponse> = mongoose.models.ObjectionResponse || mongoose.model<IObjectionResponse>('ObjectionResponse', ObjectionResponseSchema);

export default ObjectionResponse;
