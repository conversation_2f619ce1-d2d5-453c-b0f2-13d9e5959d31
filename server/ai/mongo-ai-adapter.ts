import {
  Contact,
  Company,
  Opportunity,
  Activity,
  AiChat,
  Insight,
  User
} from '../models/mongoose';
import mongoose from 'mongoose';
import OpenAI from 'openai';
import { ChatCompletionMessageParam } from 'openai/resources/chat';
import { fromObjectId } from '../utils/mongodb-utils';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || process.env.CRM_OPENAI_API_KEY
});

/**
 * MongoDB-specific AI service adapter
 */
export class MongoAiAdapter {
  /**
   * Handle chat completion
   */
  static async handleChatCompletion(
    message: string,
    userId: string,
    context?: Record<string, any>
  ): Promise<{ response: string, chatId?: string }> {
    try {
      // Check if userId is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }

      // Get user
      const user = await User.findById(userId);

      if (!user) {
        throw new Error('User not found');
      }

      // Prepare system message
      const systemMessage: ChatCompletionMessageParam = {
        role: 'system',
        content: `You are an AI assistant for Aizako CRM. You help ${user.fullName} with their CRM tasks.
        Be concise, helpful, and professional. Current date: ${new Date().toISOString().split('T')[0]}`
      };

      // Prepare user message
      const userMessage: ChatCompletionMessageParam = {
        role: 'user',
        content: message
      };

      // Add context if provided
      if (context) {
        systemMessage.content += `\nContext: ${JSON.stringify(context)}`;
      }

      // Call OpenAI API
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [systemMessage, userMessage],
        temperature: 0.7,
        max_tokens: 500
      });

      const response = completion.choices[0]?.message?.content || 'Sorry, I could not generate a response.';

      // Save chat to database
      const chat = new AiChat({
        userId,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
        messages: [
          { role: 'user', content: message, timestamp: new Date() },
          { role: 'assistant', content: response, timestamp: new Date() }
        ],
        context
      });

      await chat.save();

      return { response, chatId: fromObjectId(chat._id) };
    } catch (error: any) {
      console.error('Error handling chat completion:', error);
      throw new Error(`Failed to process chat: ${error.message}`);
    }
  }

  /**
   * Generate insights for a user
   */
  static async generateInsights(userId: string): Promise<any[]> {
    try {
      // Check if userId is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid user ID format');
      }

      // Get user's data
      const [contacts, companies, opportunities, activities] = await Promise.all([
        Contact.find({ owner: userId }).limit(100),
        Company.find({ owner: userId }).limit(50),
        Opportunity.find({ owner: userId }).limit(50),
        Activity.find({ owner: userId }).sort({ date: -1 }).limit(100)
      ]);

      // Prepare data for OpenAI
      const data = {
        contacts: contacts.map(c => ({
          id: fromObjectId(c._id),
          name: `${c.firstName} ${c.lastName}`,
          email: c.email,
          company: c.companyId ? c.companyId.toString() : null,
          status: c.status
        })),
        companies: companies.map(c => ({
          id: fromObjectId(c._id),
          name: c.name,
          industry: c.industry,
          status: c.status
        })),
        opportunities: opportunities.map(o => ({
          id: fromObjectId(o._id),
          name: o.name,
          value: o.value,
          stage: o.stage,
          probability: o.probability,
          expectedCloseDate: o.expectedCloseDate
        })),
        activities: activities.map(a => ({
          id: fromObjectId(a._id),
          type: a.type,
          title: a.title,
          date: a.date,
          completed: a.completed,
          contactId: a.contactId ? a.contactId.toString() : null,
          companyId: a.companyId ? a.companyId.toString() : null,
          opportunityId: a.opportunityId ? a.opportunityId.toString() : null
        }))
      };

      // Call OpenAI API
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant for Aizako CRM. Generate 3-5 actionable insights based on the CRM data.
            Focus on opportunities that need attention, contacts that haven't been contacted recently, and potential deals.
            Format each insight as a JSON object with title, description, type (opportunity, risk, trend, suggestion, reminder),
            importance (1-5), and targetType/targetId if applicable. Current date: ${new Date().toISOString().split('T')[0]}`
          },
          {
            role: 'user',
            content: `Generate insights based on this CRM data: ${JSON.stringify(data)}`
          }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      const responseText = completion.choices[0]?.message?.content || '{"insights": []}';
      let insights: any[] = [];

      try {
        const parsedResponse = JSON.parse(responseText);
        insights = parsedResponse.insights || [];
      } catch (parseError) {
        console.error('Error parsing insights response:', parseError);
        insights = [];
      }

      // Save insights to database
      const savedInsights = await Promise.all(
        insights.map(async (insight) => {
          const newInsight = new Insight({
            title: insight.title,
            description: insight.description,
            type: insight.type,
            importance: insight.importance,
            targetType: insight.targetType,
            targetId: insight.targetId,
            generatedBy: 'ai',
            isRead: false,
            actionTaken: false,
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
          });

          await newInsight.save();
          return newInsight;
        })
      );

      return savedInsights;
    } catch (error: any) {
      console.error('Error generating insights:', error);
      throw new Error(`Failed to generate insights: ${error.message}`);
    }
  }

  /**
   * Enrich contact data with AI
   */
  static async enrichContactData(contactId: string): Promise<any> {
    try {
      // Check if contactId is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(contactId)) {
        throw new Error('Invalid contact ID format');
      }

      // Get contact with related data
      const contact = await Contact.findById(contactId);

      if (!contact) {
        throw new Error('Contact not found');
      }

      // Get related company if exists
      let company = null;
      if (contact.companyId && mongoose.Types.ObjectId.isValid(contact.companyId.toString())) {
        company = await Company.findById(contact.companyId);
      }

      // Get related activities
      const activities = await Activity.find({ contactId }).sort({ date: -1 }).limit(10);

      // Get related opportunities
      const opportunities = await Opportunity.find({ contactId }).limit(10);

      // Prepare data for OpenAI
      const data = {
        contact: {
          id: fromObjectId(contact._id),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title,
          status: contact.status,
          notes: contact.notes,
          tags: contact.tags
        },
        company: company ? {
          id: fromObjectId(company._id),
          name: company.name,
          industry: company.industry,
          size: company.size,
          website: company.website
        } : null,
        activities: activities.map(a => ({
          type: a.type,
          title: a.title,
          date: a.date,
          completed: a.completed,
          notes: a.notes
        })),
        opportunities: opportunities.map(o => ({
          name: o.name,
          value: o.value,
          stage: o.stage,
          probability: o.probability,
          expectedCloseDate: o.expectedCloseDate
        }))
      };

      // Call OpenAI API
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant for Aizako CRM. Analyze the contact data and provide enriched information.
            Include a summary of the contact, potential next steps, relationship strength, and any insights.
            Format the response as a JSON object. Current date: ${new Date().toISOString().split('T')[0]}`
          },
          {
            role: 'user',
            content: `Analyze this contact data: ${JSON.stringify(data)}`
          }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      const responseText = completion.choices[0]?.message?.content || '{}';
      let enrichedData: any = {};

      try {
        enrichedData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing enriched data response:', parseError);
        enrichedData = {};
      }

      return enrichedData;
    } catch (error: any) {
      console.error('Error enriching contact data:', error);
      throw new Error(`Failed to enrich contact data: ${error.message}`);
    }
  }
}
