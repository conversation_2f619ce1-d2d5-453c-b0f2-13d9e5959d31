import { Request, Response, NextFunction } from 'express';
import {
  User,
  UserTenant,
  TenantSubscription,
  SubscriptionPlan,
  Feature,
  TenantUsage
} from '../models/mongoose';
import mongoose from 'mongoose';
import { FeatureType, FeatureAccess } from '../utils/subscription-client';
import { AuthenticatedRequest } from './auth-middleware';

/**
 * Middleware to check if a user has access to a feature
 * @param featureKey The feature key to check
 */
export function requireFeature(featureKey: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.session.userId as string;

      if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(401).json({ message: "Invalid user ID format" });
      }

      // Get user's tenants
      const userTenants = await UserTenant.find({ userId });

      if (userTenants.length === 0) {
        return res.status(403).json({ message: "No tenant access" });
      }

      // Get the first tenant's subscription (in a real app, you might want to handle multiple tenants)
      const tenantId = userTenants[0].tenantId.toString();

      // Get tenant subscription
      const subscription = await TenantSubscription.findOne({ tenantId });

      if (!subscription) {
        return res.status(403).json({ message: "No subscription found" });
      }

      // Check if subscription is active
      if (subscription.status !== 'active' && subscription.status !== 'trialing') {
        return res.status(403).json({ message: "Subscription is not active" });
      }

      // Check if subscription is expired
      if (new Date(subscription.endDate) < new Date()) {
        return res.status(403).json({ message: "Subscription is expired" });
      }

      // Check custom features first
      if (subscription.customFeatures && featureKey in subscription.customFeatures) {
        if (subscription.customFeatures[featureKey]) {
          // Record feature usage
          await recordFeatureUsage(tenantId, featureKey);
          return next();
        } else {
          return res.status(403).json({
            message: `Feature '${featureKey}' is not available in your subscription plan`,
            feature: featureKey
          });
        }
      }

      // Get subscription plan
      const plan = await SubscriptionPlan.findById(subscription.planId);

      if (!plan) {
        return res.status(403).json({ message: "Subscription plan not found" });
      }

      // Check if feature is enabled in plan
      if (plan.features[featureKey]) {
        // Record feature usage
        await recordFeatureUsage(tenantId, featureKey);
        return next();
      }

      return res.status(403).json({
        message: `Feature '${featureKey}' is not available in your subscription plan`,
        feature: featureKey
      });
    } catch (error) {
      console.error(`Error checking feature entitlement for ${featureKey}:`, error);
      return res.status(500).json({ message: "Failed to check feature entitlement" });
    }
  };
}

/**
 * Middleware to check if a user is within resource limits
 * @param resourceType The resource type to check
 * @param getAmount Function to get the amount from the request
 */
export function checkResourceLimit(
  resourceType: string,
  getAmount: (req: Request) => number = () => 1
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.session.userId as string;

      if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(401).json({ message: "Invalid user ID format" });
      }

      // Get user's tenants
      const userTenants = await UserTenant.find({ userId });

      if (userTenants.length === 0) {
        return res.status(403).json({ message: "No tenant access" });
      }

      // Get the first tenant's subscription
      const tenantId = userTenants[0].tenantId.toString();

      // Get tenant subscription
      const subscription = await TenantSubscription.findOne({ tenantId });

      if (!subscription) {
        return res.status(403).json({ message: "No subscription found" });
      }

      // Check if subscription is active
      if (subscription.status !== 'active' && subscription.status !== 'trialing') {
        return res.status(403).json({ message: "Subscription is not active" });
      }

      // Check if subscription is expired
      if (new Date(subscription.endDate) < new Date()) {
        return res.status(403).json({ message: "Subscription is expired" });
      }

      // Get the amount from the request
      const amount = getAmount(req);

      // Get current usage
      const currentDate = new Date();
      const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
      const usage = await TenantUsage.findOne({ tenantId, period });

      const currentUsage = usage?.usage?.[resourceType] || 0;
      const totalAmount = currentUsage + amount;

      // Check custom limits first
      if (subscription.customLimits && resourceType in subscription.customLimits) {
        if (totalAmount <= subscription.customLimits[resourceType]) {
          // Record usage
          await recordUsage(tenantId, resourceType, amount);
          return next();
        } else {
          return res.status(403).json({
            message: `You have reached your ${resourceType} limit for your current subscription plan`,
            resourceType
          });
        }
      }

      // Get subscription plan
      const plan = await SubscriptionPlan.findById(subscription.planId);

      if (!plan) {
        return res.status(403).json({ message: "Subscription plan not found" });
      }

      // Check if within limits
      if (totalAmount <= (plan.limits[resourceType] || 0)) {
        // Record usage
        await recordUsage(tenantId, resourceType, amount);
        return next();
      }

      return res.status(403).json({
        message: `You have reached your ${resourceType} limit for your current subscription plan`,
        resourceType
      });
    } catch (error) {
      console.error(`Error checking resource limit for ${resourceType}:`, error);
      return res.status(500).json({ message: "Failed to check resource limit" });
    }
  };
}

/**
 * Record feature usage
 * @param tenantId Tenant ID
 * @param featureKey Feature key
 * @param amount Amount to record
 */
async function recordFeatureUsage(tenantId: string, featureKey: string, amount: number = 1): Promise<void> {
  try {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    // Get current usage for this period
    let usage = await TenantUsage.findOne({ tenantId, period });

    if (!usage) {
      // Create new usage record if none exists
      usage = await TenantUsage.create({
        tenantId,
        period,
        usage: {},
        featureUsage: { [featureKey]: amount }
      });
    } else {
      // Update existing usage record
      const currentFeatureUsage = usage.featureUsage || {};
      const updatedFeatureUsage = {
        ...currentFeatureUsage,
        [featureKey]: (currentFeatureUsage[featureKey] || 0) + amount
      };

      await TenantUsage.updateOne(
        { _id: usage._id },
        { $set: { featureUsage: updatedFeatureUsage, updatedAt: new Date() } }
      );
    }
  } catch (error) {
    console.error(`Error recording feature usage for ${featureKey}:`, error);
  }
}

/**
 * Record resource usage
 * @param tenantId Tenant ID
 * @param resourceType Resource type
 * @param amount Amount to record
 */
async function recordUsage(tenantId: string, resourceType: string, amount: number): Promise<void> {
  try {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    // Get current usage for this period
    let usage = await TenantUsage.findOne({ tenantId, period });

    if (!usage) {
      // Create new usage record if none exists
      usage = await TenantUsage.create({
        tenantId,
        period,
        usage: { [resourceType]: amount },
        featureUsage: {}
      });
    } else {
      // Update existing usage record
      const currentUsage = usage.usage || {};
      const updatedUsage = {
        ...currentUsage,
        [resourceType]: (currentUsage[resourceType] || 0) + amount
      };

      await TenantUsage.updateOne(
        { _id: usage._id },
        { $set: { usage: updatedUsage, updatedAt: new Date() } }
      );
    }
  } catch (error) {
    console.error(`Error recording resource usage for ${resourceType}:`, error);
  }
}

/**
 * Middleware to check if a tenant has access to a feature
 * @param feature Feature to check
 * @param requiredAccess Required access level
 */
export const checkFeatureEntitlement = (
  feature: FeatureType,
  requiredAccess: FeatureAccess
) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // Get tenant ID from request
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        return res.status(401).json({ error: 'Unauthorized: No tenant ID provided' });
      }

      // Get tenant subscription
      const subscription = await TenantSubscription.findOne({ tenantId });

      if (!subscription) {
        return res.status(403).json({ message: "No subscription found" });
      }

      // Check if subscription is active
      if (subscription.status !== 'active' && subscription.status !== 'trialing') {
        return res.status(403).json({ message: "Subscription is not active" });
      }

      // Check if subscription is expired
      if (new Date(subscription.endDate) < new Date()) {
        return res.status(403).json({ message: "Subscription is expired" });
      }

      // Get subscription plan
      const plan = await SubscriptionPlan.findById(subscription.planId);

      if (!plan) {
        return res.status(403).json({ message: "Subscription plan not found" });
      }

      // Check if feature is enabled in plan with required access level
      const featureKey = feature.toString();
      const accessLevel = plan.features[featureKey]?.accessLevel || 'none';

      // Convert access levels to numeric values for comparison
      const accessLevels = {
        'none': 0,
        'basic': 1,
        'standard': 2,
        'advanced': 3,
        'unlimited': 4
      };

      const requiredLevel = accessLevels[requiredAccess] || 0;
      const currentLevel = accessLevels[accessLevel] || 0;

      if (currentLevel >= requiredLevel) {
        // Record feature usage
        await recordFeatureUsage(tenantId, featureKey);
        return next();
      }

      return res.status(403).json({
        error: 'Forbidden: Subscription plan does not include this feature',
        feature,
        requiredAccess,
        currentAccess: accessLevel,
        upgradeUrl: '/settings/subscription'
      });
    } catch (error) {
      console.error('Error checking feature entitlement:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
};
