import { Request, Response, NextFunction } from 'express';
import { SubscriptionService } from '../subscription-service';
import { FeatureType, FeatureAccess } from '../utils/subscription-client';
import { subscriptionClient } from '../services/subscription-client';
import { AuthenticatedRequest } from './auth-middleware';

const subscriptionService = new SubscriptionService();

/**
 * Middleware to check if a user has access to a feature
 * @param featureKey The feature key to check
 */
export function requireFeature(featureKey: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.session.userId as number;

      if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      // Get user's tenants
      const userTenants = await subscriptionService.getUserTenants(userId);

      if (userTenants.length === 0) {
        return res.status(403).json({ message: "No tenant access" });
      }

      // Get the first tenant's subscription (in a real app, you might want to handle multiple tenants)
      const tenantId = userTenants[0].tenantId.toString();

      // Check if the feature is enabled
      const isEnabled = await subscriptionService.checkEntitlement(tenantId, featureKey);

      if (!isEnabled) {
        return res.status(403).json({
          message: "Feature not available in your subscription plan",
          feature: featureKey
        });
      }

      // Record feature usage
      await subscriptionService.recordFeatureUsage(tenantId, featureKey);

      next();
    } catch (error) {
      console.error(`Error checking feature entitlement for ${featureKey}:`, error);
      return res.status(500).json({ message: "Failed to check feature entitlement" });
    }
  };
}

/**
 * Middleware to check if a tenant has access to a feature
 * @param feature Feature to check
 * @param requiredAccess Required access level
 */
export const checkFeatureEntitlement = (
  feature: FeatureType,
  requiredAccess: FeatureAccess
) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // Get tenant ID from request
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        return res.status(401).json({ error: 'Unauthorized: No tenant ID provided' });
      }

      // Check if tenant has access to feature
      const hasAccess = await subscriptionClient.hasFeatureAccess(
        tenantId,
        feature,
        requiredAccess
      );

      if (!hasAccess) {
        return res.status(403).json({
          error: 'Forbidden: Subscription plan does not include this feature',
          feature,
          requiredAccess,
          upgradeUrl: '/settings/subscription'
        });
      }

      next();
    } catch (error) {
      console.error('Error checking feature entitlement:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
};

/**
 * Middleware to check if a user is within resource limits
 * @param resourceType The resource type to check
 * @param getAmount Function to get the amount from the request
 */
export function checkResourceLimit(
  resourceType: string,
  getAmount: (req: Request) => number = () => 1
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.session.userId as number;

      if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      // Get user's tenants
      const userTenants = await subscriptionService.getUserTenants(userId);

      if (userTenants.length === 0) {
        return res.status(403).json({ message: "No tenant access" });
      }

      // Get the first tenant's subscription
      const tenantId = userTenants[0].tenantId.toString();

      // Get the amount from the request
      const amount = getAmount(req);

      // Check if within limits
      const isWithinLimit = await subscriptionService.checkResourceLimit(tenantId, resourceType, amount);

      if (!isWithinLimit) {
        return res.status(403).json({
          message: `You have reached your ${resourceType} limit for your current subscription plan`,
          resourceType
        });
      }

      // Record usage
      await subscriptionService.recordUsage(tenantId, resourceType, amount);

      next();
    } catch (error) {
      console.error(`Error checking resource limit for ${resourceType}:`, error);
      return res.status(500).json({ message: "Failed to check resource limit" });
    }
  };
}
