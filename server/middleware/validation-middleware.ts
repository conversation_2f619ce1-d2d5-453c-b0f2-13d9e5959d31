/**
 * Validation middleware for Express routes
 */
import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { logger } from '../utils/logger';

/**
 * Validation error interface
 */
export interface ValidationError {
  field?: string;
  message: string;
}

/**
 * Middleware to validate request body against a Zod schema
 * @param schema Zod schema to validate against
 * @returns Express middleware function
 */
export const validateRequest = <T>(schema: z.ZodType<T>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Use safeParse instead of parse to get more detailed error information
      const result = schema.safeParse(req.body);

      if (!result.success) {
        // Format Zod error for response
        const errors: ValidationError[] = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }));

        logger.debug('Validation error in request body:', {
          path: req.path,
          errors,
          body: req.body
        });

        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors
        });
      }

      // Replace request body with validated data
      req.body = result.data;

      next();
    } catch (error) {
      // Handle unexpected errors
      logger.error('Unexpected error in body validation middleware:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        path: req.path
      });

      return res.status(500).json({
        success: false,
        message: 'Internal server error during validation'
      });
    }
  };
};

/**
 * Middleware to validate request query parameters against a Zod schema
 * @param schema Zod schema to validate against
 * @returns Express middleware function
 */
export const validateQuery = <T>(schema: z.ZodType<T>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Use safeParse instead of parse to get more detailed error information
      const result = schema.safeParse(req.query);

      if (!result.success) {
        // Format Zod error for response
        const errors: ValidationError[] = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }));

        logger.debug('Validation error in query parameters:', {
          path: req.path,
          errors,
          query: req.query
        });

        return res.status(400).json({
          success: false,
          message: 'Query validation error',
          errors
        });
      }

      // Replace request query with validated data
      req.query = result.data as any;

      next();
    } catch (error) {
      // Handle unexpected errors
      logger.error('Unexpected error in query validation middleware:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        path: req.path
      });

      return res.status(500).json({
        success: false,
        message: 'Internal server error during query validation'
      });
    }
  };
};

/**
 * Middleware to validate request parameters against a Zod schema
 * @param schema Zod schema to validate against
 * @returns Express middleware function
 */
export const validateParams = <T>(schema: z.ZodType<T>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Use safeParse instead of parse to get more detailed error information
      const result = schema.safeParse(req.params);

      if (!result.success) {
        // Format Zod error for response
        const errors: ValidationError[] = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }));

        logger.debug('Validation error in route parameters:', {
          path: req.path,
          errors,
          params: req.params
        });

        return res.status(400).json({
          success: false,
          message: 'Parameter validation error',
          errors
        });
      }

      // Replace request parameters with validated data
      req.params = result.data as any;

      next();
    } catch (error) {
      // Handle unexpected errors
      logger.error('Unexpected error in parameter validation middleware:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        path: req.path
      });

      return res.status(500).json({
        success: false,
        message: 'Internal server error during parameter validation'
      });
    }
  };
};

/**
 * Middleware to validate multiple parts of a request against Zod schemas
 * @param schemas Object containing schemas for body, query, and params
 * @returns Express middleware function
 */
export const validate = (schemas: {
  body?: z.ZodType<any>;
  query?: z.ZodType<any>;
  params?: z.ZodType<any>;
}) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors: ValidationError[] = [];

      // Validate body if schema provided
      if (schemas.body) {
        const bodyResult = schemas.body.safeParse(req.body);
        if (!bodyResult.success) {
          bodyResult.error.errors.forEach(error => {
            errors.push({
              field: `body.${error.path.join('.')}`,
              message: error.message,
            });
          });
        } else {
          req.body = bodyResult.data;
        }
      }

      // Validate query if schema provided
      if (schemas.query) {
        const queryResult = schemas.query.safeParse(req.query);
        if (!queryResult.success) {
          queryResult.error.errors.forEach(error => {
            errors.push({
              field: `query.${error.path.join('.')}`,
              message: error.message,
            });
          });
        } else {
          req.query = queryResult.data as any;
        }
      }

      // Validate params if schema provided
      if (schemas.params) {
        const paramsResult = schemas.params.safeParse(req.params);
        if (!paramsResult.success) {
          paramsResult.error.errors.forEach(error => {
            errors.push({
              field: `params.${error.path.join('.')}`,
              message: error.message,
            });
          });
        } else {
          req.params = paramsResult.data as any;
        }
      }

      // Return errors if any
      if (errors.length > 0) {
        logger.debug('Validation errors:', {
          path: req.path,
          errors,
          body: req.body,
          query: req.query,
          params: req.params
        });

        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors,
        });
      }

      next();
    } catch (error) {
      // Handle unexpected errors
      logger.error('Unexpected error in validation middleware:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        path: req.path
      });

      return res.status(500).json({
        success: false,
        message: 'Internal server error during validation'
      });
    }
  };
};
