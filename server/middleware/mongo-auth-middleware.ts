import { Request, Response, NextFunction } from 'express';
import { User } from '../models/mongoose';
import mongoose from 'mongoose';
import { isValidObjectId } from '../utils/mongodb-utils';

/**
 * Authentication middleware for MongoDB
 */
export async function authenticateUser(req: Request, res: Response, next: NextFunction): Promise<void> {
  // First check traditional session authentication
  if (req.session && req.session.userId) {
    try {
      // Verify that the user exists in the database
      const userId = req.session.userId;

      // Check if ID is valid ObjectId
      if (!isValidObjectId(userId.toString())) {
        req.session.destroy(() => {});
        res.status(401).json({ message: "Invalid user ID format" });
        return;
      }

      const user = await User.findById(userId);

      if (!user) {
        req.session.destroy(() => {});
        res.status(401).json({ message: "User not found" });
        return;
      }

      next();
      return;
    } catch (error) {
      console.error('Error authenticating user:', error);
      res.status(500).json({ message: "Authentication error" });
      return;
    }
  }

  // Then check for API key in Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);

    try {
      // Check if token is a valid API key
      const user = await User.findOne({ 'apiKeys.key': token });

      if (user) {
        // Set userId in session
        req.session.userId = user._id;
        next();
        return;
      }
    } catch (error) {
      console.error('Error authenticating API key:', error);
    }
  }

  res.status(401).json({ message: "Unauthorized" });
}
