/**
 * Authentication middleware for Express routes
 */
import { Request, Response, NextFunction } from 'express';
import { auth } from '../firebase';
import { getFirestore } from 'firebase-admin/firestore';

// Interface for authenticated request
export interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    tenantId: string;
    role: string;
  };
}

/**
 * Middleware to authenticate user with Firebase
 */
export const authenticateUser = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized: No token provided' });
    }

    // Extract token
    const token = authHeader.split('Bearer ')[1];

    // Verify token
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return res.status(401).json({ error: 'Unauthorized: Invalid token' });
    }

    // Get user from Firestore
    const db = getFirestore();
    const userDoc = await db.collection('users').doc(decodedToken.uid).get();

    if (!userDoc.exists) {
      return res.status(401).json({ error: 'Unauthorized: User not found' });
    }

    const userData = userDoc.data();

    // Get tenant ID from header or user data
    const tenantId = req.headers['x-tenant-id'] as string || userData?.tenantId;

    if (!tenantId) {
      return res.status(401).json({ error: 'Unauthorized: No tenant ID provided' });
    }

    // Set user in request
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email || '',
      tenantId,
      role: userData?.role || 'user',
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Unauthorized: Invalid token' });
  }
};

/**
 * Middleware to require admin role
 */
export const requireAdmin = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // First authenticate the user
    await authenticateUser(req, res, () => {
      // Check if user has admin role
      if (req.user?.role !== 'admin') {
        return res.status(403).json({ error: 'Forbidden: Admin access required' });
      }

      next();
    });
  } catch (error) {
    console.error('Admin authorization error:', error);
    return res.status(403).json({ error: 'Forbidden: Admin access required' });
  }
};

/**
 * Middleware to require tenant owner role
 */
export const requireTenantOwner = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // First authenticate the user
    await authenticateUser(req, res, async () => {
      // Get tenant from Firestore
      const db = getFirestore();
      const tenantDoc = await db.collection('tenants').doc(req.user?.tenantId || '').get();

      if (!tenantDoc.exists) {
        return res.status(404).json({ error: 'Tenant not found' });
      }

      const tenantData = tenantDoc.data();

      // Check if user is the tenant owner
      if (tenantData?.ownerId !== req.user?.uid && req.user?.role !== 'admin') {
        return res.status(403).json({ error: 'Forbidden: Tenant owner access required' });
      }

      next();
    });
  } catch (error) {
    console.error('Tenant owner authorization error:', error);
    return res.status(403).json({ error: 'Forbidden: Tenant owner access required' });
  }
};

/**
 * Middleware to validate tenant access
 */
export const validateTenantAccess = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // First authenticate the user
    await authenticateUser(req, res, async () => {
      // Get tenant ID from request
      const tenantId = req.params.tenantId || req.query.tenantId || req.body.tenantId;

      // If no tenant ID in request, continue (will use user's tenant)
      if (!tenantId) {
        return next();
      }

      // Check if user has access to the requested tenant
      if (req.user?.tenantId !== tenantId && req.user?.role !== 'admin') {
        return res.status(403).json({ error: 'Forbidden: No access to this tenant' });
      }

      next();
    });
  } catch (error) {
    console.error('Tenant access validation error:', error);
    return res.status(403).json({ error: 'Forbidden: Tenant access validation failed' });
  }
};

/**
 * Middleware to require tenant ID
 */
export const requireTenantId = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // First authenticate the user
    await authenticateUser(req, res, () => {
      // Check if user has a tenant ID
      if (!req.user?.tenantId) {
        return res.status(403).json({ error: 'Forbidden: Tenant ID required' });
      }

      next();
    });
  } catch (error) {
    console.error('Tenant ID requirement error:', error);
    return res.status(403).json({ error: 'Forbidden: Tenant ID required' });
  }
};

/**
 * Middleware to authenticate JWT token only (no user lookup)
 */
export const authenticateJWT = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized: No token provided' });
    }

    // Extract token
    const token = authHeader.split('Bearer ')[1];

    // Verify token
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return res.status(401).json({ error: 'Unauthorized: Invalid token' });
    }

    // Set minimal user info in request
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email || '',
      tenantId: '',
      role: ''
    };

    next();
  } catch (error) {
    console.error('JWT authentication error:', error);
    return res.status(401).json({ error: 'Unauthorized: Invalid token' });
  }
};
