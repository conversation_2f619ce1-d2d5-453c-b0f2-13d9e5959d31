import { Request, Response, NextFunction } from 'express';
import { FeatureFlagService } from '../services/feature-flag-service';

/**
 * Middleware to check if a feature flag is enabled
 * @param flagKey The key of the feature flag to check
 * @param options Options for the middleware
 * @returns Express middleware function
 */
export function requireFeatureFlag(
  flagKey: string,
  options: {
    redirectTo?: string;
    sendError?: boolean;
    errorStatus?: number;
    errorMessage?: string;
  } = {}
) {
  const {
    redirectTo,
    sendError = true,
    errorStatus = 403,
    errorMessage = 'This feature is not available',
  } = options;
  
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Get context from request
      const context = FeatureFlagService.getContextFromRequest(req);
      
      // Check if the feature flag is enabled
      const isEnabled = await FeatureFlagService.isEnabled(flagKey, context);
      
      if (isEnabled) {
        // Feature flag is enabled, continue
        return next();
      }
      
      // Feature flag is disabled
      if (redirectTo) {
        // Redirect to the specified URL
        return res.redirect(redirectTo);
      }
      
      if (sendError) {
        // Send error response
        return res.status(errorStatus).json({
          success: false,
          message: errorMessage,
        });
      }
      
      // Just continue with the request
      return next();
    } catch (error) {
      console.error(`Error in feature flag middleware for ${flagKey}:`, error);
      return next(error);
    }
  };
}

/**
 * Middleware to inject feature flags into the request object
 * @param flagKeys Array of feature flag keys to inject
 * @returns Express middleware function
 */
export function injectFeatureFlags(flagKeys: string[] = []) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Get context from request
      const context = FeatureFlagService.getContextFromRequest(req);
      
      // Initialize feature flags object on request
      req.featureFlags = {};
      
      // Check each feature flag
      for (const key of flagKeys) {
        req.featureFlags[key] = await FeatureFlagService.isEnabled(key, context);
      }
      
      // Continue with the request
      return next();
    } catch (error) {
      console.error('Error in inject feature flags middleware:', error);
      return next(error);
    }
  };
}

// Extend Express Request interface to include feature flags
declare global {
  namespace Express {
    interface Request {
      featureFlags?: Record<string, boolean>;
    }
  }
}
