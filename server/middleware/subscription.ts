import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to check if a user has access to a feature
 * @param featureKey The feature key to check
 */
export function checkFeatureEntitlement(featureKey: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if MongoDB is enabled
      const useMongoDb = process.env.MONGODB_ENABLED === 'true';
      
      if (useMongoDb) {
        // Import MongoDB-specific middleware
        const { requireFeature } = await import('./mongo-subscription-middleware');
        return requireFeature(featureKey)(req, res, next);
      } else {
        // Import PostgreSQL-specific middleware
        const { requireFeature } = await import('./subscription-middleware');
        return requireFeature(featureKey)(req, res, next);
      }
    } catch (error) {
      console.error(`Error checking feature entitlement for ${featureKey}:`, error);
      return res.status(500).json({ message: "Failed to check feature entitlement" });
    }
  };
}

/**
 * Middleware to check if a user is within resource limits
 * @param resourceType The resource type to check
 * @param getAmount Function to get the amount from the request
 */
export function checkResourceLimit(
  resourceType: string,
  getAmount: (req: Request) => number = () => 1
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if MongoDB is enabled
      const useMongoDb = process.env.MONGODB_ENABLED === 'true';
      
      if (useMongoDb) {
        // Import MongoDB-specific middleware
        const { checkResourceLimit } = await import('./mongo-subscription-middleware');
        return checkResourceLimit(resourceType, getAmount)(req, res, next);
      } else {
        // Import PostgreSQL-specific middleware
        const { checkResourceLimit } = await import('./subscription-middleware');
        return checkResourceLimit(resourceType, getAmount)(req, res, next);
      }
    } catch (error) {
      console.error(`Error checking resource limit for ${resourceType}:`, error);
      return res.status(500).json({ message: "Failed to check resource limit" });
    }
  };
}
