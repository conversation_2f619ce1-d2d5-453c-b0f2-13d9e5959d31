import { 
  SubscriptionPlan, 
  Tenant, 
  TenantSubscription, 
  Feature, 
  TenantUsage, 
  SubscriptionEvent, 
  UserTenant,
  ISubscriptionPlan,
  ITenant,
  ITenantSubscription,
  IFeature,
  ITenantUsage,
  ISubscriptionEvent,
  IUserTenant
} from '../models/mongoose/subscription-models';
import { 
  InsertSubscriptionPlan, 
  InsertTenantSubscription, 
  InsertFeature, 
  InsertTenantUsage, 
  InsertTenant, 
  InsertUserTenant 
} from '@shared/subscription-schema';
import mongoose from 'mongoose';

/**
 * MongoDB implementation of the subscription storage
 */
export class MongoSubscriptionStorage {
  /**
   * Get all subscription plans
   */
  async getSubscriptionPlans(): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.find().sort({ sortOrder: 1 });
  }

  /**
   * Get a specific subscription plan by ID
   */
  async getSubscriptionPlan(id: number | string): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findById(id);
  }

  /**
   * Create a new subscription plan
   */
  async createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<ISubscriptionPlan> {
    const newPlan = new SubscriptionPlan(plan);
    return await newPlan.save();
  }

  /**
   * Update an existing subscription plan
   */
  async updateSubscriptionPlan(id: number | string, plan: Partial<InsertSubscriptionPlan>): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findByIdAndUpdate(id, plan, { new: true });
  }

  /**
   * Delete a subscription plan
   */
  async deleteSubscriptionPlan(id: number | string): Promise<boolean> {
    const result = await SubscriptionPlan.deleteOne({ _id: id });
    return result.deletedCount === 1;
  }

  /**
   * Get all features
   */
  async getFeatures(): Promise<IFeature[]> {
    return await Feature.find();
  }

  /**
   * Get a specific feature by ID
   */
  async getFeature(id: number | string): Promise<IFeature | null> {
    return await Feature.findById(id);
  }

  /**
   * Get a specific feature by key
   */
  async getFeatureByKey(key: string): Promise<IFeature | null> {
    return await Feature.findOne({ key });
  }

  /**
   * Create a new feature
   */
  async createFeature(feature: InsertFeature): Promise<IFeature> {
    const newFeature = new Feature(feature);
    return await newFeature.save();
  }

  /**
   * Update an existing feature
   */
  async updateFeature(id: number | string, feature: Partial<InsertFeature>): Promise<IFeature | null> {
    return await Feature.findByIdAndUpdate(id, feature, { new: true });
  }

  /**
   * Delete a feature
   */
  async deleteFeature(id: number | string): Promise<boolean> {
    const result = await Feature.deleteOne({ _id: id });
    return result.deletedCount === 1;
  }

  /**
   * Get all tenant subscriptions
   */
  async getTenantSubscriptions(): Promise<ITenantSubscription[]> {
    return await TenantSubscription.find();
  }

  /**
   * Get a specific tenant subscription by ID
   */
  async getTenantSubscription(id: number | string): Promise<ITenantSubscription | null> {
    return await TenantSubscription.findById(id);
  }

  /**
   * Get a tenant subscription by tenant ID
   */
  async getTenantSubscriptionByTenantId(tenantId: string): Promise<ITenantSubscription | null> {
    return await TenantSubscription.findOne({ tenantId });
  }

  /**
   * Create a new tenant subscription
   */
  async createTenantSubscription(subscription: InsertTenantSubscription): Promise<ITenantSubscription> {
    const newSubscription = new TenantSubscription(subscription);
    return await newSubscription.save();
  }

  /**
   * Update an existing tenant subscription
   */
  async updateTenantSubscription(id: number | string, subscription: Partial<InsertTenantSubscription>): Promise<ITenantSubscription | null> {
    return await TenantSubscription.findByIdAndUpdate(id, subscription, { new: true });
  }

  /**
   * Get all tenant usage records
   */
  async getTenantUsages(): Promise<ITenantUsage[]> {
    return await TenantUsage.find();
  }

  /**
   * Get a specific tenant usage record by ID
   */
  async getTenantUsage(id: number | string): Promise<ITenantUsage | null> {
    return await TenantUsage.findById(id);
  }

  /**
   * Get tenant usage for a specific period
   */
  async getTenantUsageByPeriod(tenantId: string, period: string): Promise<ITenantUsage | null> {
    return await TenantUsage.findOne({ tenantId, period });
  }

  /**
   * Create a new tenant usage record
   */
  async createTenantUsage(usage: InsertTenantUsage): Promise<ITenantUsage> {
    const newUsage = new TenantUsage(usage);
    return await newUsage.save();
  }

  /**
   * Update an existing tenant usage record
   */
  async updateTenantUsage(id: number | string, usage: Partial<InsertTenantUsage>): Promise<ITenantUsage | null> {
    return await TenantUsage.findByIdAndUpdate(id, usage, { new: true });
  }

  /**
   * Get all tenants
   */
  async getTenants(): Promise<ITenant[]> {
    return await Tenant.find();
  }

  /**
   * Get a specific tenant by ID
   */
  async getTenant(id: number | string): Promise<ITenant | null> {
    return await Tenant.findById(id);
  }

  /**
   * Get a tenant by slug
   */
  async getTenantBySlug(slug: string): Promise<ITenant | null> {
    return await Tenant.findOne({ slug });
  }

  /**
   * Create a new tenant
   */
  async createTenant(tenant: InsertTenant): Promise<ITenant> {
    const newTenant = new Tenant(tenant);
    return await newTenant.save();
  }

  /**
   * Update an existing tenant
   */
  async updateTenant(id: number | string, tenant: Partial<InsertTenant>): Promise<ITenant | null> {
    return await Tenant.findByIdAndUpdate(id, tenant, { new: true });
  }

  /**
   * Get user-tenant relationships for a user
   */
  async getUserTenants(userId: number | string): Promise<IUserTenant[]> {
    return await UserTenant.find({ userId });
  }

  /**
   * Get user-tenant relationships for a tenant
   */
  async getTenantUsers(tenantId: number | string): Promise<IUserTenant[]> {
    return await UserTenant.find({ tenantId });
  }

  /**
   * Create a new user-tenant relationship
   */
  async createUserTenant(userTenant: InsertUserTenant): Promise<IUserTenant> {
    const newUserTenant = new UserTenant(userTenant);
    return await newUserTenant.save();
  }

  /**
   * Update an existing user-tenant relationship
   */
  async updateUserTenant(id: number | string, userTenant: Partial<InsertUserTenant>): Promise<IUserTenant | null> {
    return await UserTenant.findByIdAndUpdate(id, userTenant, { new: true });
  }

  /**
   * Create a subscription event
   */
  async createSubscriptionEvent(event: { type: string, payload: any }): Promise<ISubscriptionEvent> {
    const newEvent = new SubscriptionEvent({
      type: event.type,
      payload: event.payload,
      timestamp: new Date()
    });
    return await newEvent.save();
  }
}
