import { IStorage } from '../storage';
import {
  User,
  Contact,
  Company,
  Opportunity,
  Activity,
  Relationship,
  AiChat,
  Insight,
  IUser
} from '../models/mongoose';

import {
  InsertUser,
  InsertContact,
  InsertCompany,
  InsertOpportunity,
  InsertActivity,
  InsertRelationship,
  InsertAiChat,
  InsertInsight
} from '@shared/schema';

import {
  SubscriptionPlan as SubscriptionPlanType,
  TenantSubscription as TenantSubscriptionType,
  Feature as FeatureType,
  TenantUsage as TenantUsageType,
  Tenant as TenantType,
  UserTenant as UserTenantType,
  InsertSubscriptionPlan,
  InsertTenantSubscription,
  InsertFeature,
  InsertTenantUsage,
  InsertTenant,
  InsertUserTenant
} from '@shared/subscription-schema';

import { MongoSubscriptionStorage } from './mongo-subscription-storage';

/**
 * MongoDB implementation of the storage interface
 */
export class MongoStorage implements IStorage {
  private subscriptionStorage: MongoSubscriptionStorage;

  constructor() {
    this.subscriptionStorage = new MongoSubscriptionStorage();
  }

  // User methods
  async getUser(id: number): Promise<{ id: number; username: string; password: string; email: string; fullName: string | null; preferences: Record<string, any>; createdAt: Date; } | undefined> {
    try {
      const user = await User.findById(id);
      if (!user) return undefined;

      return {
        id: Number(user._id) || 0,
        username: user.username,
        password: user.password,
        email: user.email,
        fullName: user.fullName || null,
        preferences: user.preferences || {},
        createdAt: user.createdAt
      };
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<{ id: number; username: string; password: string; email: string; fullName: string | null; preferences: Record<string, any>; createdAt: Date; } | undefined> {
    try {
      const user = await User.findOne({ username });
      if (!user) return undefined;

      return {
        id: Number(user._id) || 0,
        username: user.username,
        password: user.password,
        email: user.email,
        fullName: user.fullName || null,
        preferences: user.preferences || {},
        createdAt: user.createdAt
      };
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<{ id: number; username: string; password: string; email: string; fullName: string | null; preferences: Record<string, any>; createdAt: Date; } | undefined> {
    try {
      const user = await User.findOne({ email });
      if (!user) return undefined;

      return {
        id: Number(user._id) || 0,
        username: user.username,
        password: user.password,
        email: user.email,
        fullName: user.fullName || null,
        preferences: user.preferences || {},
        createdAt: user.createdAt
      };
    } catch (error) {
      console.error('Error getting user by email:', error);
      return undefined;
    }
  }

  async createUser(user: InsertUser): Promise<{ id: number; username: string; password: string; email: string; fullName: string | null; preferences: Record<string, any>; createdAt: Date; }> {
    try {
      const newUser = new User(user);
      const savedUser = await newUser.save();

      return {
        id: Number(savedUser._id) || 0,
        username: savedUser.username,
        password: savedUser.password,
        email: savedUser.email,
        fullName: savedUser.fullName || null,
        preferences: savedUser.preferences || {},
        createdAt: savedUser.createdAt
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async updateUserPreferences(id: number, preferences: Record<string, any>): Promise<{ id: number; username: string; password: string; email: string; fullName: string | null; preferences: Record<string, any>; createdAt: Date; } | undefined> {
    try {
      const user = await User.findByIdAndUpdate(
        id,
        { $set: { preferences } },
        { new: true }
      );

      if (!user) return undefined;

      return {
        id: Number(user._id) || 0,
        username: user.username,
        password: user.password,
        email: user.email,
        fullName: user.fullName || null,
        preferences: user.preferences || {},
        createdAt: user.createdAt
      };
    } catch (error) {
      console.error('Error updating user preferences:', error);
      return undefined;
    }
  }

  // Contact methods
  async getContact(id: number): Promise<{ id: number; firstName: string; lastName: string; email: string | null; phone: string | null; title: string | null; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; companyId: number | null; notes: string | null; source: string | null; aiEnrichment: unknown; createdAt: Date; updatedAt: Date; createdBy: number | null; } | undefined> {
    try {
      const contact = await Contact.findById(id);
      if (!contact) return undefined;

      return {
        id: Number(contact._id) || 0,
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email || null,
        phone: contact.phone || null,
        title: contact.title || null,
        status: contact.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        companyId: contact.companyId ? Number(contact.companyId) : null,
        notes: contact.notes || null,
        source: contact.source || null,
        aiEnrichment: contact.aiEnrichment || null,
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
        createdBy: contact.createdBy ? Number(contact.createdBy) : null
      };
    } catch (error) {
      console.error('Error getting contact:', error);
      return undefined;
    }
  }

  async getContacts(limit?: number, offset?: number): Promise<{ id: number; firstName: string; lastName: string; email: string | null; phone: string | null; title: string | null; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; companyId: number | null; notes: string | null; source: string | null; aiEnrichment: unknown; createdAt: Date; updatedAt: Date; createdBy: number | null; }[]> {
    try {
      let query = Contact.find();

      if (offset) {
        query = query.skip(offset);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const contacts = await query.exec();

      return contacts.map(contact => ({
        id: Number(contact._id) || 0,
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email || null,
        phone: contact.phone || null,
        title: contact.title || null,
        status: contact.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        companyId: contact.companyId ? Number(contact.companyId) : null,
        notes: contact.notes || null,
        source: contact.source || null,
        aiEnrichment: contact.aiEnrichment || null,
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
        createdBy: contact.createdBy ? Number(contact.createdBy) : null
      }));
    } catch (error) {
      console.error('Error getting contacts:', error);
      return [];
    }
  }

  async getContactsByCompany(companyId: number): Promise<{ id: number; firstName: string; lastName: string; email: string | null; phone: string | null; title: string | null; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; companyId: number | null; notes: string | null; source: string | null; aiEnrichment: unknown; createdAt: Date; updatedAt: Date; createdBy: number | null; }[]> {
    try {
      const contacts = await Contact.find({ companyId }).exec();

      return contacts.map(contact => ({
        id: Number(contact._id) || 0,
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email || null,
        phone: contact.phone || null,
        title: contact.title || null,
        status: contact.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        companyId: contact.companyId ? Number(contact.companyId) : null,
        notes: contact.notes || null,
        source: contact.source || null,
        aiEnrichment: contact.aiEnrichment || null,
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
        createdBy: contact.createdBy ? Number(contact.createdBy) : null
      }));
    } catch (error) {
      console.error('Error getting contacts by company:', error);
      return [];
    }
  }

  async createContact(contact: InsertContact): Promise<{ id: number; firstName: string; lastName: string; email: string | null; phone: string | null; title: string | null; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; companyId: number | null; notes: string | null; source: string | null; aiEnrichment: unknown; createdAt: Date; updatedAt: Date; createdBy: number | null; }> {
    try {
      const newContact = new Contact(contact);
      const savedContact = await newContact.save();

      return {
        id: Number(savedContact._id) || 0,
        firstName: savedContact.firstName,
        lastName: savedContact.lastName,
        email: savedContact.email || null,
        phone: savedContact.phone || null,
        title: savedContact.title || null,
        status: savedContact.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        companyId: savedContact.companyId ? Number(savedContact.companyId) : null,
        notes: savedContact.notes || null,
        source: savedContact.source || null,
        aiEnrichment: savedContact.aiEnrichment || null,
        createdAt: savedContact.createdAt,
        updatedAt: savedContact.updatedAt,
        createdBy: savedContact.createdBy ? Number(savedContact.createdBy) : null
      };
    } catch (error) {
      console.error('Error creating contact:', error);
      throw error;
    }
  }

  async updateContact(id: number, contact: Partial<InsertContact>): Promise<{ id: number; firstName: string; lastName: string; email: string | null; phone: string | null; title: string | null; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; companyId: number | null; notes: string | null; source: string | null; aiEnrichment: unknown; createdAt: Date; updatedAt: Date; createdBy: number | null; } | undefined> {
    try {
      const updatedContact = await Contact.findByIdAndUpdate(
        id,
        { $set: contact },
        { new: true }
      );

      if (!updatedContact) return undefined;

      return {
        id: Number(updatedContact._id) || 0,
        firstName: updatedContact.firstName,
        lastName: updatedContact.lastName,
        email: updatedContact.email || null,
        phone: updatedContact.phone || null,
        title: updatedContact.title || null,
        status: updatedContact.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        companyId: updatedContact.companyId ? Number(updatedContact.companyId) : null,
        notes: updatedContact.notes || null,
        source: updatedContact.source || null,
        aiEnrichment: updatedContact.aiEnrichment || null,
        createdAt: updatedContact.createdAt,
        updatedAt: updatedContact.updatedAt,
        createdBy: updatedContact.createdBy ? Number(updatedContact.createdBy) : null
      };
    } catch (error) {
      console.error('Error updating contact:', error);
      return undefined;
    }
  }

  async deleteContact(id: number): Promise<boolean> {
    try {
      const result = await Contact.deleteOne({ _id: id });
      return result.deletedCount === 1;
    } catch (error) {
      console.error('Error deleting contact:', error);
      return false;
    }
  }

  // Company methods
  async getCompany(id: number): Promise<{ id: number; name: string; createdAt: Date; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; industry: string | null; website: string | null; employees: number | null; notes: string | null; aiEnrichment: unknown; updatedAt: Date; createdBy: number | null; } | undefined> {
    try {
      const company = await Company.findById(id);
      if (!company) return undefined;

      return {
        id: Number(company._id) || 0,
        name: company.name,
        industry: company.industry || null,
        website: company.website || null,
        employees: company.employees || null,
        status: company.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        notes: company.notes || null,
        aiEnrichment: company.aiEnrichment || null,
        createdAt: company.createdAt,
        updatedAt: company.updatedAt,
        createdBy: Number(company.createdBy) || null
      };
    } catch (error) {
      console.error('Error getting company:', error);
      return undefined;
    }
  }

  async getCompanies(limit?: number, offset?: number): Promise<{ id: number; name: string; createdAt: Date; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; industry: string | null; website: string | null; employees: number | null; notes: string | null; aiEnrichment: unknown; updatedAt: Date; createdBy: number | null; }[]> {
    try {
      let query = Company.find();

      if (offset) {
        query = query.skip(offset);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const companies = await query.exec();

      return companies.map(company => ({
        id: Number(company._id) || 0,
        name: company.name,
        industry: company.industry || null,
        website: company.website || null,
        employees: company.employees || null,
        status: company.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        notes: company.notes || null,
        aiEnrichment: company.aiEnrichment || null,
        createdAt: company.createdAt,
        updatedAt: company.updatedAt,
        createdBy: Number(company.createdBy) || null
      }));
    } catch (error) {
      console.error('Error getting companies:', error);
      return [];
    }
  }

  async createCompany(company: InsertCompany): Promise<{ id: number; name: string; createdAt: Date; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; industry: string | null; website: string | null; employees: number | null; notes: string | null; aiEnrichment: unknown; updatedAt: Date; createdBy: number | null; }> {
    try {
      const newCompany = new Company(company);
      const savedCompany = await newCompany.save();

      return {
        id: Number(savedCompany._id) || 0,
        name: savedCompany.name,
        industry: savedCompany.industry || null,
        website: savedCompany.website || null,
        employees: savedCompany.employees || null,
        status: savedCompany.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        notes: savedCompany.notes || null,
        aiEnrichment: savedCompany.aiEnrichment || null,
        createdAt: savedCompany.createdAt,
        updatedAt: savedCompany.updatedAt,
        createdBy: Number(savedCompany.createdBy) || null
      };
    } catch (error) {
      console.error('Error creating company:', error);
      throw error;
    }
  }

  async updateCompany(id: number, company: Partial<InsertCompany>): Promise<{ id: number; name: string; createdAt: Date; status: "active" | "inactive" | "lead" | "prospect" | "customer" | null; industry: string | null; website: string | null; employees: number | null; notes: string | null; aiEnrichment: unknown; updatedAt: Date; createdBy: number | null; } | undefined> {
    try {
      const updatedCompany = await Company.findByIdAndUpdate(
        id,
        { $set: company },
        { new: true }
      );

      if (!updatedCompany) return undefined;

      return {
        id: Number(updatedCompany._id) || 0,
        name: updatedCompany.name,
        industry: updatedCompany.industry || null,
        website: updatedCompany.website || null,
        employees: updatedCompany.employees || null,
        status: updatedCompany.status as "active" | "inactive" | "lead" | "prospect" | "customer" | null,
        notes: updatedCompany.notes || null,
        aiEnrichment: updatedCompany.aiEnrichment || null,
        createdAt: updatedCompany.createdAt,
        updatedAt: updatedCompany.updatedAt,
        createdBy: Number(updatedCompany.createdBy) || null
      };
    } catch (error) {
      console.error('Error updating company:', error);
      return undefined;
    }
  }

  async deleteCompany(id: number): Promise<boolean> {
    try {
      const result = await Company.deleteOne({ _id: id });
      return result.deletedCount === 1;
    } catch (error) {
      console.error('Error deleting company:', error);
      return false;
    }
  }

  // Opportunity methods
  async getOpportunity(id: number): Promise<{ id: number; name: string; createdAt: Date; value: number | null; notes: string | null; updatedAt: Date; createdBy: number | null; companyId: number | null; description: string | null; probability: number | null; stage: "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null; contactId: number | null; closeDate: Date | null; } | undefined> {
    try {
      const opportunity = await Opportunity.findById(id);
      if (!opportunity) return undefined;

      return {
        id: Number(opportunity._id) || 0,
        name: opportunity.name,
        value: opportunity.value || null,
        notes: opportunity.notes || null,
        stage: opportunity.stage as "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null,
        probability: opportunity.probability || null,
        companyId: opportunity.companyId ? Number(opportunity.companyId) : null,
        contactId: opportunity.contactId ? Number(opportunity.contactId) : null,
        description: opportunity.description || null,
        createdAt: opportunity.createdAt,
        updatedAt: opportunity.updatedAt,
        createdBy: opportunity.owner ? Number(opportunity.owner) : null,
        closeDate: opportunity.expectedCloseDate || null
      };
    } catch (error) {
      console.error('Error getting opportunity:', error);
      return undefined;
    }
  }

  async getOpportunities(limit?: number, offset?: number): Promise<{ id: number; name: string; createdAt: Date; value: number | null; notes: string | null; updatedAt: Date; createdBy: number | null; companyId: number | null; description: string | null; probability: number | null; stage: "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null; contactId: number | null; closeDate: Date | null; }[]> {
    try {
      let query = Opportunity.find();

      if (offset) {
        query = query.skip(offset);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const opportunities = await query.exec();

      return opportunities.map(opportunity => ({
        id: Number(opportunity._id) || 0,
        name: opportunity.name,
        value: opportunity.value || null,
        notes: opportunity.notes || null,
        stage: opportunity.stage as "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null,
        probability: opportunity.probability || null,
        companyId: opportunity.companyId ? Number(opportunity.companyId) : null,
        contactId: opportunity.contactId ? Number(opportunity.contactId) : null,
        description: opportunity.description || null,
        createdAt: opportunity.createdAt,
        updatedAt: opportunity.updatedAt,
        createdBy: opportunity.owner ? Number(opportunity.owner) : null,
        closeDate: opportunity.expectedCloseDate || null
      }));
    } catch (error) {
      console.error('Error getting opportunities:', error);
      return [];
    }
  }

  async getOpportunitiesByCompany(companyId: number): Promise<{ id: number; name: string; createdAt: Date; value: number | null; notes: string | null; updatedAt: Date; createdBy: number | null; companyId: number | null; description: string | null; probability: number | null; stage: "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null; contactId: number | null; closeDate: Date | null; }[]> {
    try {
      const opportunities = await Opportunity.find({ companyId }).exec();

      return opportunities.map(opportunity => ({
        id: Number(opportunity._id) || 0,
        name: opportunity.name,
        value: opportunity.value || null,
        notes: opportunity.notes || null,
        stage: opportunity.stage as "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null,
        probability: opportunity.probability || null,
        companyId: opportunity.companyId ? Number(opportunity.companyId) : null,
        contactId: opportunity.contactId ? Number(opportunity.contactId) : null,
        description: opportunity.description || null,
        createdAt: opportunity.createdAt,
        updatedAt: opportunity.updatedAt,
        createdBy: opportunity.owner ? Number(opportunity.owner) : null,
        closeDate: opportunity.expectedCloseDate || null
      }));
    } catch (error) {
      console.error('Error getting opportunities by company:', error);
      return [];
    }
  }

  async getOpportunitiesByContact(contactId: number): Promise<{ id: number; name: string; createdAt: Date; value: number | null; notes: string | null; updatedAt: Date; createdBy: number | null; companyId: number | null; description: string | null; probability: number | null; stage: "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null; contactId: number | null; closeDate: Date | null; }[]> {
    try {
      const opportunities = await Opportunity.find({ contactId }).exec();

      return opportunities.map(opportunity => ({
        id: Number(opportunity._id) || 0,
        name: opportunity.name,
        value: opportunity.value || null,
        notes: opportunity.notes || null,
        stage: opportunity.stage as "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null,
        probability: opportunity.probability || null,
        companyId: opportunity.companyId ? Number(opportunity.companyId) : null,
        contactId: opportunity.contactId ? Number(opportunity.contactId) : null,
        description: opportunity.description || null,
        createdAt: opportunity.createdAt,
        updatedAt: opportunity.updatedAt,
        createdBy: opportunity.owner ? Number(opportunity.owner) : null,
        closeDate: opportunity.expectedCloseDate || null
      }));
    } catch (error) {
      console.error('Error getting opportunities by contact:', error);
      return [];
    }
  }

  async createOpportunity(opportunity: InsertOpportunity): Promise<{ id: number; name: string; createdAt: Date; value: number | null; notes: string | null; updatedAt: Date; createdBy: number | null; companyId: number | null; description: string | null; probability: number | null; stage: "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null; contactId: number | null; closeDate: Date | null; }> {
    try {
      const newOpportunity = new Opportunity({
        ...opportunity,
        owner: opportunity.createdBy
      });
      const savedOpportunity = await newOpportunity.save();

      return {
        id: Number(savedOpportunity._id) || 0,
        name: savedOpportunity.name,
        value: savedOpportunity.value || null,
        notes: savedOpportunity.notes || null,
        stage: savedOpportunity.stage as "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null,
        probability: savedOpportunity.probability || null,
        companyId: savedOpportunity.companyId ? Number(savedOpportunity.companyId) : null,
        contactId: savedOpportunity.contactId ? Number(savedOpportunity.contactId) : null,
        description: savedOpportunity.description || null,
        createdAt: savedOpportunity.createdAt,
        updatedAt: savedOpportunity.updatedAt,
        createdBy: savedOpportunity.owner ? Number(savedOpportunity.owner) : null,
        closeDate: savedOpportunity.expectedCloseDate || null
      };
    } catch (error) {
      console.error('Error creating opportunity:', error);
      throw error;
    }
  }

  async updateOpportunity(id: number, opportunity: Partial<InsertOpportunity>): Promise<{ id: number; name: string; createdAt: Date; value: number | null; notes: string | null; updatedAt: Date; createdBy: number | null; companyId: number | null; description: string | null; probability: number | null; stage: "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null; contactId: number | null; closeDate: Date | null; } | undefined> {
    try {
      // Map createdBy to owner if it exists
      const updateData: Record<string, any> = { ...opportunity };
      if (updateData.createdBy) {
        updateData.owner = updateData.createdBy;
        delete updateData.createdBy;
      }

      const updatedOpportunity = await Opportunity.findByIdAndUpdate(
        id,
        { $set: updateData },
        { new: true }
      );

      if (!updatedOpportunity) return undefined;

      return {
        id: Number(updatedOpportunity._id) || 0,
        name: updatedOpportunity.name,
        value: updatedOpportunity.value || null,
        notes: updatedOpportunity.notes || null,
        stage: updatedOpportunity.stage as "discovery" | "qualified" | "proposal" | "negotiation" | "closed_won" | "closed_lost" | null,
        probability: updatedOpportunity.probability || null,
        companyId: updatedOpportunity.companyId ? Number(updatedOpportunity.companyId) : null,
        contactId: updatedOpportunity.contactId ? Number(updatedOpportunity.contactId) : null,
        description: updatedOpportunity.description || null,
        createdAt: updatedOpportunity.createdAt,
        updatedAt: updatedOpportunity.updatedAt,
        createdBy: updatedOpportunity.owner ? Number(updatedOpportunity.owner) : null,
        closeDate: updatedOpportunity.expectedCloseDate || null
      };
    } catch (error) {
      console.error('Error updating opportunity:', error);
      return undefined;
    }
  }

  async deleteOpportunity(id: number): Promise<boolean> {
    try {
      const result = await Opportunity.deleteOne({ _id: id });
      return result.deletedCount === 1;
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      return false;
    }
  }

  // Activity methods
  async getActivity(id: number): Promise<{ id: number; type: string; title: string; createdBy: number | null; companyId: number | null; description: string | null; contactId: number | null; opportunityId: number | null; timestamp: Date; } | undefined> {
    try {
      const activity = await Activity.findById(id);
      if (!activity) return undefined;

      return {
        id: Number(activity._id) || 0,
        type: activity.type,
        title: activity.title,
        description: activity.description || null,
        contactId: activity.contactId ? Number(activity.contactId) : null,
        companyId: activity.companyId ? Number(activity.companyId) : null,
        opportunityId: activity.opportunityId ? Number(activity.opportunityId) : null,
        createdBy: activity.owner ? Number(activity.owner) : null,
        timestamp: activity.createdAt
      };
    } catch (error) {
      console.error('Error getting activity:', error);
      return undefined;
    }
  }

  async getActivities(limit?: number, offset?: number): Promise<{ id: number; type: string; title: string; createdBy: number | null; companyId: number | null; description: string | null; contactId: number | null; opportunityId: number | null; timestamp: Date; }[]> {
    try {
      let query = Activity.find();

      if (offset) {
        query = query.skip(offset);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const activities = await query.exec();

      return activities.map(activity => ({
        id: Number(activity._id) || 0,
        type: activity.type,
        title: activity.title,
        description: activity.description || null,
        contactId: activity.contactId ? Number(activity.contactId) : null,
        companyId: activity.companyId ? Number(activity.companyId) : null,
        opportunityId: activity.opportunityId ? Number(activity.opportunityId) : null,
        createdBy: activity.owner ? Number(activity.owner) : null,
        timestamp: activity.createdAt
      }));
    } catch (error) {
      console.error('Error getting activities:', error);
      return [];
    }
  }

  async getActivitiesByContact(contactId: number): Promise<{ id: number; type: string; title: string; createdBy: number | null; companyId: number | null; description: string | null; contactId: number | null; opportunityId: number | null; timestamp: Date; }[]> {
    try {
      const activities = await Activity.find({ contactId }).exec();

      return activities.map(activity => ({
        id: Number(activity._id) || 0,
        type: activity.type,
        title: activity.title,
        description: activity.description || null,
        contactId: activity.contactId ? Number(activity.contactId) : null,
        companyId: activity.companyId ? Number(activity.companyId) : null,
        opportunityId: activity.opportunityId ? Number(activity.opportunityId) : null,
        createdBy: activity.owner ? Number(activity.owner) : null,
        timestamp: activity.createdAt
      }));
    } catch (error) {
      console.error('Error getting activities by contact:', error);
      return [];
    }
  }

  async getActivitiesByCompany(companyId: number): Promise<{ id: number; type: string; title: string; createdBy: number | null; companyId: number | null; description: string | null; contactId: number | null; opportunityId: number | null; timestamp: Date; }[]> {
    try {
      const activities = await Activity.find({ companyId }).exec();

      return activities.map(activity => ({
        id: Number(activity._id) || 0,
        type: activity.type,
        title: activity.title,
        description: activity.description || null,
        contactId: activity.contactId ? Number(activity.contactId) : null,
        companyId: activity.companyId ? Number(activity.companyId) : null,
        opportunityId: activity.opportunityId ? Number(activity.opportunityId) : null,
        createdBy: activity.owner ? Number(activity.owner) : null,
        timestamp: activity.createdAt
      }));
    } catch (error) {
      console.error('Error getting activities by company:', error);
      return [];
    }
  }

  async getActivitiesByOpportunity(opportunityId: number): Promise<{ id: number; type: string; title: string; createdBy: number | null; companyId: number | null; description: string | null; contactId: number | null; opportunityId: number | null; timestamp: Date; }[]> {
    try {
      const activities = await Activity.find({ opportunityId }).exec();

      return activities.map(activity => ({
        id: Number(activity._id) || 0,
        type: activity.type,
        title: activity.title,
        description: activity.description || null,
        contactId: activity.contactId ? Number(activity.contactId) : null,
        companyId: activity.companyId ? Number(activity.companyId) : null,
        opportunityId: activity.opportunityId ? Number(activity.opportunityId) : null,
        createdBy: activity.owner ? Number(activity.owner) : null,
        timestamp: activity.createdAt
      }));
    } catch (error) {
      console.error('Error getting activities by opportunity:', error);
      return [];
    }
  }

  async createActivity(activity: InsertActivity): Promise<{ id: number; type: string; title: string; createdBy: number | null; companyId: number | null; description: string | null; contactId: number | null; opportunityId: number | null; timestamp: Date; }> {
    try {
      // Convert the activity data to match the mongoose model
      const activityData = {
        type: activity.type,
        title: activity.title,
        description: activity.description || null,
        date: new Date(), // Use current date as the date
        completed: false,
        contactId: activity.contactId ? activity.contactId : null,
        companyId: activity.companyId ? activity.companyId : null,
        opportunityId: activity.opportunityId ? activity.opportunityId : null,
        owner: activity.createdBy || 1, // Default to user 1 if not provided
        customFields: {}
      };

      const newActivity = new Activity(activityData);
      const savedActivity = await newActivity.save();

      return {
        id: Number(savedActivity._id) || 0,
        type: savedActivity.type,
        title: savedActivity.title,
        description: savedActivity.description || null,
        contactId: savedActivity.contactId ? Number(savedActivity.contactId) : null,
        companyId: savedActivity.companyId ? Number(savedActivity.companyId) : null,
        opportunityId: savedActivity.opportunityId ? Number(savedActivity.opportunityId) : null,
        createdBy: activity.createdBy || null,
        timestamp: savedActivity.createdAt
      };
    } catch (error) {
      console.error('Error creating activity:', error);
      throw error;
    }
  }

  // Relationship methods
  async getRelationship(id: number): Promise<{ id: number; createdAt: Date; notes: string | null; createdBy: number | null; sourceType: string; sourceId: number; targetType: string; targetId: number; relationshipType: string | null; strength: number | null; } | undefined> {
    try {
      const relationship = await Relationship.findById(id);
      if (!relationship) return undefined;

      return {
        id: Number(relationship._id) || 0,
        createdAt: relationship.createdAt,
        notes: relationship.description || null,
        createdBy: Number(relationship.createdBy) || null,
        sourceType: relationship.sourceType,
        sourceId: Number(relationship.sourceId) || 0,
        targetType: relationship.targetType,
        targetId: Number(relationship.targetId) || 0,
        relationshipType: relationship.type || null,
        strength: relationship.strength || null
      };
    } catch (error) {
      console.error('Error getting relationship:', error);
      return undefined;
    }
  }

  async getRelationships(): Promise<{ id: number; createdAt: Date; notes: string | null; createdBy: number | null; sourceType: string; sourceId: number; targetType: string; targetId: number; relationshipType: string | null; strength: number | null; }[]> {
    try {
      const relationships = await Relationship.find().exec();

      return relationships.map(relationship => ({
        id: Number(relationship._id) || 0,
        createdAt: relationship.createdAt,
        notes: relationship.description || null,
        createdBy: Number(relationship.createdBy) || null,
        sourceType: relationship.sourceType,
        sourceId: Number(relationship.sourceId) || 0,
        targetType: relationship.targetType,
        targetId: Number(relationship.targetId) || 0,
        relationshipType: relationship.type || null,
        strength: relationship.strength || null
      }));
    } catch (error) {
      console.error('Error getting relationships:', error);
      return [];
    }
  }

  async getRelationshipsBySource(sourceType: string, sourceId: number): Promise<{ id: number; createdAt: Date; notes: string | null; createdBy: number | null; sourceType: string; sourceId: number; targetType: string; targetId: number; relationshipType: string | null; strength: number | null; }[]> {
    try {
      const relationships = await Relationship.find({ sourceType, sourceId }).exec();

      return relationships.map(relationship => ({
        id: Number(relationship._id) || 0,
        createdAt: relationship.createdAt,
        notes: relationship.description || null,
        createdBy: Number(relationship.createdBy) || null,
        sourceType: relationship.sourceType,
        sourceId: Number(relationship.sourceId) || 0,
        targetType: relationship.targetType,
        targetId: Number(relationship.targetId) || 0,
        relationshipType: relationship.type || null,
        strength: relationship.strength || null
      }));
    } catch (error) {
      console.error('Error getting relationships by source:', error);
      return [];
    }
  }

  async getRelationshipsByTarget(targetType: string, targetId: number): Promise<{ id: number; createdAt: Date; notes: string | null; createdBy: number | null; sourceType: string; sourceId: number; targetType: string; targetId: number; relationshipType: string | null; strength: number | null; }[]> {
    try {
      const relationships = await Relationship.find({ targetType, targetId }).exec();

      return relationships.map(relationship => ({
        id: Number(relationship._id) || 0,
        createdAt: relationship.createdAt,
        notes: relationship.description || null,
        createdBy: Number(relationship.createdBy) || null,
        sourceType: relationship.sourceType,
        sourceId: Number(relationship.sourceId) || 0,
        targetType: relationship.targetType,
        targetId: Number(relationship.targetId) || 0,
        relationshipType: relationship.type || null,
        strength: relationship.strength || null
      }));
    } catch (error) {
      console.error('Error getting relationships by target:', error);
      return [];
    }
  }

  async createRelationship(relationship: InsertRelationship): Promise<{ id: number; createdAt: Date; notes: string | null; createdBy: number | null; sourceType: string; sourceId: number; targetType: string; targetId: number; relationshipType: string | null; strength: number | null; }> {
    try {
      const newRelationship = new Relationship({
        ...relationship,
        // Map the fields to match the Mongoose model
        type: relationship.relationshipType,
        description: relationship.notes
      });
      const savedRelationship = await newRelationship.save();

      return {
        id: Number(savedRelationship._id) || 0,
        createdAt: savedRelationship.createdAt,
        notes: savedRelationship.description || null,
        createdBy: Number(savedRelationship.createdBy) || null,
        sourceType: savedRelationship.sourceType,
        sourceId: Number(savedRelationship.sourceId) || 0,
        targetType: savedRelationship.targetType,
        targetId: Number(savedRelationship.targetId) || 0,
        relationshipType: savedRelationship.type || null,
        strength: savedRelationship.strength || null
      };
    } catch (error) {
      console.error('Error creating relationship:', error);
      throw error;
    }
  }

  // AI Chat methods
  async getAiChatsByUser(userId: number, limit?: number): Promise<{ id: number; message: string; timestamp: Date; userId: number | null; response: string; metadata: unknown; }[]> {
    try {
      let query = AiChat.find({ userId }).sort({ updatedAt: -1 });

      if (limit) {
        query = query.limit(limit);
      }

      const chats = await query.exec();

      return chats.map(chat => {
        // Extract the first user message and assistant response from the messages array
        const userMessage = chat.messages?.find(m => m.role === 'user')?.content || '';
        const assistantResponse = chat.messages?.find(m => m.role === 'assistant')?.content || '';

        return {
          id: Number(chat._id) || 0,
          userId: Number(chat.userId) || null,
          message: userMessage,
          response: assistantResponse,
          timestamp: chat.createdAt,
          metadata: chat.context || {}
        };
      });
    } catch (error) {
      console.error('Error getting AI chats by user:', error);
      return [];
    }
  }

  async createAiChat(chat: InsertAiChat): Promise<{ id: number; message: string; timestamp: Date; userId: number | null; response: string; metadata: unknown; }> {
    try {
      // Create a new chat with the message and response in the messages array
      const chatData = {
        userId: chat.userId,
        messages: [
          { role: 'user', content: chat.message, timestamp: new Date() },
          { role: 'assistant', content: chat.response, timestamp: new Date() }
        ],
        context: chat.metadata || {}
      };

      const newChat = new AiChat(chatData);
      const savedChat = await newChat.save();

      return {
        id: Number(savedChat._id) || 0,
        userId: Number(savedChat.userId) || null,
        message: chat.message,
        response: chat.response,
        timestamp: savedChat.createdAt,
        metadata: savedChat.context || {}
      };
    } catch (error) {
      console.error('Error creating AI chat:', error);
      throw error;
    }
  }

  // Insight methods
  async getInsights(limit?: number, offset?: number): Promise<{ id: number; createdAt: Date; title: string; description: string; targetType: string | null; targetId: number | null; importance: number | null; category: string | null; read: boolean | null; }[]> {
    try {
      let query = Insight.find().sort({ importance: -1, createdAt: -1 });

      if (offset) {
        query = query.skip(offset);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const insights = await query.exec();

      return insights.map(insight => ({
        id: Number(insight._id) || 0,
        createdAt: insight.createdAt,
        title: insight.title,
        description: insight.description,
        targetType: insight.targetType,
        targetId: Number(insight.targetId) || 0,
        importance: insight.importance,
        category: insight.type || null,
        read: insight.isRead || null
      }));
    } catch (error) {
      console.error('Error getting insights:', error);
      return [];
    }
  }

  async getInsightsByTarget(targetType: string, targetId: number): Promise<{ id: number; createdAt: Date; title: string; description: string; targetType: string | null; targetId: number | null; importance: number | null; category: string | null; read: boolean | null; }[]> {
    try {
      const insights = await Insight.find({ targetType, targetId })
        .sort({ importance: -1, createdAt: -1 })
        .exec();

      return insights.map(insight => ({
        id: Number(insight._id) || 0,
        createdAt: insight.createdAt,
        title: insight.title,
        description: insight.description,
        targetType: insight.targetType,
        targetId: Number(insight.targetId) || 0,
        importance: insight.importance,
        category: insight.type || null,
        read: insight.isRead || null
      }));
    } catch (error) {
      console.error('Error getting insights by target:', error);
      return [];
    }
  }

  async createInsight(insight: InsertInsight): Promise<{ id: number; createdAt: Date; title: string; description: string; targetType: string | null; targetId: number | null; importance: number | null; category: string | null; read: boolean | null; }> {
    try {
      const newInsight = new Insight({
        ...insight,
        type: insight.category || null
      });
      const savedInsight = await newInsight.save();

      return {
        id: Number(savedInsight._id) || 0,
        createdAt: savedInsight.createdAt,
        title: savedInsight.title,
        description: savedInsight.description,
        targetType: savedInsight.targetType || null,
        targetId: Number(savedInsight.targetId) || 0,
        importance: savedInsight.importance || null,
        category: savedInsight.type || null,
        read: savedInsight.isRead || null
      };
    } catch (error) {
      console.error('Error creating insight:', error);
      throw error;
    }
  }

  async markInsightAsRead(id: number): Promise<{ id: number; createdAt: Date; title: string; description: string; targetType: string | null; targetId: number | null; importance: number | null; category: string | null; read: boolean | null; } | undefined> {
    try {
      const updatedInsight = await Insight.findByIdAndUpdate(
        id,
        { $set: { isRead: true } },
        { new: true }
      );

      if (!updatedInsight) return undefined;

      return {
        id: Number(updatedInsight._id) || 0,
        createdAt: updatedInsight.createdAt,
        title: updatedInsight.title,
        description: updatedInsight.description,
        targetType: updatedInsight.targetType || null,
        targetId: Number(updatedInsight.targetId) || 0,
        importance: updatedInsight.importance || null,
        category: updatedInsight.type || null,
        read: updatedInsight.isRead || null
      };
    } catch (error) {
      console.error('Error marking insight as read:', error);
      return undefined;
    }
  }

  // Dashboard metrics
  async getMetrics(): Promise<{
    totalContacts: number,
    totalCompanies: number,
    openOpportunities: number,
    pipelineValue: number,
    aiInsightsGenerated: number
  }> {
    try {
      const [
        totalContacts,
        totalCompanies,
        openOpportunities,
        aiInsightsGenerated
      ] = await Promise.all([
        Contact.countDocuments(),
        Company.countDocuments(),
        Opportunity.find({
          stage: {
            $nin: ['closed_won', 'closed_lost']
          }
        }),
        Insight.countDocuments({ generatedBy: 'ai' })
      ]);

      const pipelineValue = openOpportunities.reduce(
        (sum, opp) => sum + (opp.value || 0),
        0
      );

      return {
        totalContacts,
        totalCompanies,
        openOpportunities: openOpportunities.length,
        pipelineValue,
        aiInsightsGenerated
      };
    } catch (error) {
      console.error('Error getting metrics:', error);
      return {
        totalContacts: 0,
        totalCompanies: 0,
        openOpportunities: 0,
        pipelineValue: 0,
        aiInsightsGenerated: 0
      };
    }
  }

  // Subscription Plan methods
  async getSubscriptionPlans(): Promise<SubscriptionPlanType[]> {
    try {
      const plans = await this.subscriptionStorage.getSubscriptionPlans();
      return plans.map(plan => this.convertToSubscriptionPlanType(plan));
    } catch (error) {
      console.error('Error getting subscription plans:', error);
      return [];
    }
  }

  async getSubscriptionPlan(id: number): Promise<SubscriptionPlanType | undefined> {
    try {
      const plan = await this.subscriptionStorage.getSubscriptionPlan(id);
      return plan ? this.convertToSubscriptionPlanType(plan) : undefined;
    } catch (error) {
      console.error('Error getting subscription plan:', error);
      return undefined;
    }
  }

  async createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlanType> {
    try {
      const newPlan = await this.subscriptionStorage.createSubscriptionPlan(plan);
      return this.convertToSubscriptionPlanType(newPlan);
    } catch (error) {
      console.error('Error creating subscription plan:', error);
      throw error;
    }
  }

  async updateSubscriptionPlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlanType | undefined> {
    try {
      const updatedPlan = await this.subscriptionStorage.updateSubscriptionPlan(id, plan);
      return updatedPlan ? this.convertToSubscriptionPlanType(updatedPlan) : undefined;
    } catch (error) {
      console.error('Error updating subscription plan:', error);
      return undefined;
    }
  }

  async deleteSubscriptionPlan(id: number): Promise<boolean> {
    try {
      return await this.subscriptionStorage.deleteSubscriptionPlan(id);
    } catch (error) {
      console.error('Error deleting subscription plan:', error);
      return false;
    }
  }

  // Feature methods
  async getFeatures(): Promise<FeatureType[]> {
    try {
      const features = await this.subscriptionStorage.getFeatures();
      return features.map(feature => this.convertToFeatureType(feature));
    } catch (error) {
      console.error('Error getting features:', error);
      return [];
    }
  }

  async getFeature(id: number): Promise<FeatureType | undefined> {
    try {
      const feature = await this.subscriptionStorage.getFeature(id);
      return feature ? this.convertToFeatureType(feature) : undefined;
    } catch (error) {
      console.error('Error getting feature:', error);
      return undefined;
    }
  }

  async getFeatureByKey(key: string): Promise<FeatureType | undefined> {
    try {
      const feature = await this.subscriptionStorage.getFeatureByKey(key);
      return feature ? this.convertToFeatureType(feature) : undefined;
    } catch (error) {
      console.error('Error getting feature by key:', error);
      return undefined;
    }
  }

  async createFeature(feature: InsertFeature): Promise<FeatureType> {
    try {
      const newFeature = await this.subscriptionStorage.createFeature(feature);
      return this.convertToFeatureType(newFeature);
    } catch (error) {
      console.error('Error creating feature:', error);
      throw error;
    }
  }

  async updateFeature(id: number, feature: Partial<InsertFeature>): Promise<FeatureType | undefined> {
    try {
      const updatedFeature = await this.subscriptionStorage.updateFeature(id, feature);
      return updatedFeature ? this.convertToFeatureType(updatedFeature) : undefined;
    } catch (error) {
      console.error('Error updating feature:', error);
      return undefined;
    }
  }

  async deleteFeature(id: number): Promise<boolean> {
    try {
      return await this.subscriptionStorage.deleteFeature(id);
    } catch (error) {
      console.error('Error deleting feature:', error);
      return false;
    }
  }

  // Tenant Subscription methods
  async getTenantSubscriptions(): Promise<TenantSubscriptionType[]> {
    try {
      const subscriptions = await this.subscriptionStorage.getTenantSubscriptions();
      return subscriptions.map(subscription => this.convertToTenantSubscriptionType(subscription));
    } catch (error) {
      console.error('Error getting tenant subscriptions:', error);
      return [];
    }
  }

  async getTenantSubscription(id: number): Promise<TenantSubscriptionType | undefined> {
    try {
      const subscription = await this.subscriptionStorage.getTenantSubscription(id);
      return subscription ? this.convertToTenantSubscriptionType(subscription) : undefined;
    } catch (error) {
      console.error('Error getting tenant subscription:', error);
      return undefined;
    }
  }

  async getTenantSubscriptionByTenantId(tenantId: string): Promise<TenantSubscriptionType | undefined> {
    try {
      const subscription = await this.subscriptionStorage.getTenantSubscriptionByTenantId(tenantId);
      return subscription ? this.convertToTenantSubscriptionType(subscription) : undefined;
    } catch (error) {
      console.error('Error getting tenant subscription by tenant ID:', error);
      return undefined;
    }
  }

  async createTenantSubscription(subscription: InsertTenantSubscription): Promise<TenantSubscriptionType> {
    try {
      const newSubscription = await this.subscriptionStorage.createTenantSubscription(subscription);
      return this.convertToTenantSubscriptionType(newSubscription);
    } catch (error) {
      console.error('Error creating tenant subscription:', error);
      throw error;
    }
  }

  async updateTenantSubscription(id: number, subscription: Partial<InsertTenantSubscription>): Promise<TenantSubscriptionType | undefined> {
    try {
      const updatedSubscription = await this.subscriptionStorage.updateTenantSubscription(id, subscription);
      return updatedSubscription ? this.convertToTenantSubscriptionType(updatedSubscription) : undefined;
    } catch (error) {
      console.error('Error updating tenant subscription:', error);
      return undefined;
    }
  }

  // Tenant Usage methods
  async getTenantUsages(): Promise<TenantUsageType[]> {
    try {
      const usages = await this.subscriptionStorage.getTenantUsages();
      return usages.map(usage => this.convertToTenantUsageType(usage));
    } catch (error) {
      console.error('Error getting tenant usages:', error);
      return [];
    }
  }

  async getTenantUsage(id: number): Promise<TenantUsageType | undefined> {
    try {
      const usage = await this.subscriptionStorage.getTenantUsage(id);
      return usage ? this.convertToTenantUsageType(usage) : undefined;
    } catch (error) {
      console.error('Error getting tenant usage:', error);
      return undefined;
    }
  }

  async getTenantUsageByPeriod(tenantId: string, period: string): Promise<TenantUsageType | undefined> {
    try {
      const usage = await this.subscriptionStorage.getTenantUsageByPeriod(tenantId, period);
      return usage ? this.convertToTenantUsageType(usage) : undefined;
    } catch (error) {
      console.error('Error getting tenant usage by period:', error);
      return undefined;
    }
  }

  async createTenantUsage(usage: InsertTenantUsage): Promise<TenantUsageType> {
    try {
      const newUsage = await this.subscriptionStorage.createTenantUsage(usage);
      return this.convertToTenantUsageType(newUsage);
    } catch (error) {
      console.error('Error creating tenant usage:', error);
      throw error;
    }
  }

  async updateTenantUsage(id: number, usage: Partial<InsertTenantUsage>): Promise<TenantUsageType | undefined> {
    try {
      const updatedUsage = await this.subscriptionStorage.updateTenantUsage(id, usage);
      return updatedUsage ? this.convertToTenantUsageType(updatedUsage) : undefined;
    } catch (error) {
      console.error('Error updating tenant usage:', error);
      return undefined;
    }
  }

  // Tenant methods
  async getTenants(): Promise<TenantType[]> {
    try {
      const tenants = await this.subscriptionStorage.getTenants();
      return tenants.map(tenant => this.convertToTenantType(tenant));
    } catch (error) {
      console.error('Error getting tenants:', error);
      return [];
    }
  }

  async getTenant(id: number): Promise<TenantType | undefined> {
    try {
      const tenant = await this.subscriptionStorage.getTenant(id);
      return tenant ? this.convertToTenantType(tenant) : undefined;
    } catch (error) {
      console.error('Error getting tenant:', error);
      return undefined;
    }
  }

  async getTenantBySlug(slug: string): Promise<TenantType | undefined> {
    try {
      const tenant = await this.subscriptionStorage.getTenantBySlug(slug);
      return tenant ? this.convertToTenantType(tenant) : undefined;
    } catch (error) {
      console.error('Error getting tenant by slug:', error);
      return undefined;
    }
  }

  async createTenant(tenant: InsertTenant): Promise<TenantType> {
    try {
      const newTenant = await this.subscriptionStorage.createTenant(tenant);
      return this.convertToTenantType(newTenant);
    } catch (error) {
      console.error('Error creating tenant:', error);
      throw error;
    }
  }

  async updateTenant(id: number, tenant: Partial<InsertTenant>): Promise<TenantType | undefined> {
    try {
      const updatedTenant = await this.subscriptionStorage.updateTenant(id, tenant);
      return updatedTenant ? this.convertToTenantType(updatedTenant) : undefined;
    } catch (error) {
      console.error('Error updating tenant:', error);
      return undefined;
    }
  }

  // User Tenant methods
  async getUserTenants(userId: number): Promise<UserTenantType[]> {
    try {
      const userTenants = await this.subscriptionStorage.getUserTenants(userId);
      return userTenants.map(userTenant => this.convertToUserTenantType(userTenant));
    } catch (error) {
      console.error('Error getting user tenants:', error);
      return [];
    }
  }

  async getTenantUsers(tenantId: number): Promise<UserTenantType[]> {
    try {
      const tenantUsers = await this.subscriptionStorage.getTenantUsers(tenantId);
      return tenantUsers.map(tenantUser => this.convertToUserTenantType(tenantUser));
    } catch (error) {
      console.error('Error getting tenant users:', error);
      return [];
    }
  }

  async createUserTenant(userTenant: InsertUserTenant): Promise<UserTenantType> {
    try {
      const newUserTenant = await this.subscriptionStorage.createUserTenant(userTenant);
      return this.convertToUserTenantType(newUserTenant);
    } catch (error) {
      console.error('Error creating user tenant:', error);
      throw error;
    }
  }

  async updateUserTenant(id: number, userTenant: Partial<InsertUserTenant>): Promise<UserTenantType | undefined> {
    try {
      const updatedUserTenant = await this.subscriptionStorage.updateUserTenant(id, userTenant);
      return updatedUserTenant ? this.convertToUserTenantType(updatedUserTenant) : undefined;
    } catch (error) {
      console.error('Error updating user tenant:', error);
      return undefined;
    }
  }

  // Subscription Event methods
  async createSubscriptionEvent(event: { type: string, payload: any }): Promise<{ id: number, type: string, payload: any, timestamp: Date }> {
    try {
      const newEvent = await this.subscriptionStorage.createSubscriptionEvent(event);
      return {
        id: Number(newEvent._id) || 0,
        type: newEvent.type,
        payload: newEvent.payload,
        timestamp: newEvent.timestamp
      };
    } catch (error) {
      console.error('Error creating subscription event:', error);
      throw error;
    }
  }

  // Helper methods for type conversion
  private convertToSubscriptionPlanType(plan: any): SubscriptionPlanType {
    return {
      id: Number(plan._id) || 0,
      name: plan.name,
      description: plan.description || '',
      status: plan.status,
      isDefault: plan.isDefault,
      sortOrder: plan.sortOrder,
      price: plan.price.toString(),
      currency: plan.currency,
      billingPeriod: plan.billingPeriod,
      trialDays: plan.trialDays,
      limits: plan.limits,
      features: plan.features,
      moduleSettings: plan.moduleSettings,
      createdAt: plan.createdAt,
      updatedAt: plan.updatedAt
    };
  }

  private convertToFeatureType(feature: any): FeatureType {
    return {
      id: Number(feature._id) || 0,
      key: feature.key,
      name: feature.name,
      description: feature.description || '',
      category: feature.category,
      module: feature.module,
      defaultValue: feature.defaultValue,
      requiresRestart: feature.requiresRestart,
      uiComponent: feature.uiComponent,
      dependencies: feature.dependencies,
      createdAt: feature.createdAt,
      updatedAt: feature.updatedAt
    };
  }

  private convertToTenantSubscriptionType(subscription: any): TenantSubscriptionType {
    return {
      id: Number(subscription._id) || 0,
      tenantId: subscription.tenantId,
      planId: Number(subscription.planId) || 0,
      status: subscription.status,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      trialEndsAt: subscription.trialEndsAt,
      customLimits: subscription.customLimits,
      customFeatures: subscription.customFeatures,
      billingDetails: subscription.billingDetails,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt,
      canceledAt: subscription.canceledAt
    };
  }

  private convertToTenantUsageType(usage: any): TenantUsageType {
    return {
      id: Number(usage._id) || 0,
      tenantId: usage.tenantId,
      period: usage.period,
      usage: usage.usage,
      featureUsage: usage.featureUsage,
      updatedAt: usage.updatedAt
    };
  }

  private convertToTenantType(tenant: any): TenantType {
    return {
      id: Number(tenant._id) || 0,
      name: tenant.name,
      slug: tenant.slug,
      ownerId: Number(tenant.ownerId) || 0,
      settings: tenant.settings,
      status: tenant.status,
      createdAt: tenant.createdAt,
      updatedAt: tenant.updatedAt
    };
  }

  private convertToUserTenantType(userTenant: any): UserTenantType {
    return {
      id: Number(userTenant._id) || 0,
      userId: Number(userTenant.userId) || 0,
      tenantId: Number(userTenant.tenantId) || 0,
      role: userTenant.role,
      invitedBy: Number(userTenant.invitedBy) || null,
      status: userTenant.status,
      createdAt: userTenant.createdAt
    };
  }
}
