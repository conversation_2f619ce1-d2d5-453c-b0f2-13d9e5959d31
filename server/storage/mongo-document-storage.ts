import { DocumentModel } from '../models/mongoose';
import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const unlinkAsync = promisify(fs.unlink);
const mkdirAsync = promisify(fs.mkdir);

/**
 * MongoDB-specific document storage service
 */
export class MongoDocumentStorage {
  private uploadDir: string;

  constructor() {
    this.uploadDir = process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads');
    this.ensureUploadDirExists();
  }

  /**
   * Ensure the upload directory exists
   */
  private async ensureUploadDirExists(): Promise<void> {
    try {
      await mkdirAsync(this.uploadDir, { recursive: true });
    } catch (error) {
      console.error('Error creating upload directory:', error);
    }
  }

  /**
   * Get all documents
   */
  async getDocuments(limit: number = 100, offset: number = 0): Promise<any[]> {
    try {
      const documents = await DocumentModel.find()
        .sort({ createdAt: -1 })
        .skip(offset)
        .limit(limit)
        .exec();

      return documents;
    } catch (error) {
      console.error('Error getting documents:', error);
      throw new Error('Failed to get documents');
    }
  }

  /**
   * Get documents by entity
   */
  async getDocumentsByEntity(entityType: string, entityId: string): Promise<any[]> {
    try {
      const documents = await DocumentModel.find({
        entityType,
        entityId
      })
        .sort({ createdAt: -1 })
        .exec();

      return documents;
    } catch (error) {
      console.error('Error getting documents by entity:', error);
      throw new Error('Failed to get documents by entity');
    }
  }

  /**
   * Get document by ID
   */
  async getDocument(id: string): Promise<any> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new Error('Invalid document ID format');
      }

      const document = await DocumentModel.findById(id);

      if (!document) {
        throw new Error('Document not found');
      }

      return document;
    } catch (error) {
      console.error('Error getting document:', error);
      throw new Error('Failed to get document');
    }
  }

  /**
   * Create a new document
   */
  async createDocument(documentData: any, fileBuffer: Buffer): Promise<any> {
    try {
      // Generate a unique filename
      const filename = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${documentData.originalFilename}`;
      const filePath = path.join(this.uploadDir, filename);

      // Save the file to disk
      await writeFileAsync(filePath, fileBuffer);

      // Create document record in database
      const document = new DocumentModel({
        ...documentData,
        filename,
        filePath,
        fileSize: fileBuffer.length,
        uploadedBy: documentData.uploadedBy,
        createdAt: new Date()
      });

      await document.save();

      return document;
    } catch (error) {
      console.error('Error creating document:', error);
      throw new Error('Failed to create document');
    }
  }

  /**
   * Update document metadata
   */
  async updateDocument(id: string, documentData: any): Promise<any> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new Error('Invalid document ID format');
      }

      const document = await DocumentModel.findByIdAndUpdate(
        id,
        { $set: documentData },
        { new: true }
      );

      if (!document) {
        throw new Error('Document not found');
      }

      return document;
    } catch (error) {
      console.error('Error updating document:', error);
      throw new Error('Failed to update document');
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(id: string): Promise<boolean> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new Error('Invalid document ID format');
      }

      // Get document to get file path
      const document = await DocumentModel.findById(id);

      if (!document) {
        throw new Error('Document not found');
      }

      // Delete file from disk
      if (document.filePath) {
        try {
          await unlinkAsync(document.filePath);
        } catch (fileError) {
          console.error('Error deleting file:', fileError);
        }
      }

      // Delete document from database
      const result = await DocumentModel.deleteOne({ _id: id });

      return result.deletedCount === 1;
    } catch (error) {
      console.error('Error deleting document:', error);
      throw new Error('Failed to delete document');
    }
  }

  /**
   * Get document file content
   */
  async getDocumentContent(id: string): Promise<{ buffer: Buffer, contentType: string, filename: string }> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new Error('Invalid document ID format');
      }

      // Get document to get file path
      const document = await DocumentModel.findById(id);

      if (!document) {
        throw new Error('Document not found');
      }

      // Read file from disk
      // Access filePath from document as any to bypass type checking
      const buffer = await readFileAsync((document as any).filePath);

      return {
        buffer,
        contentType: (document as any).contentType,
        filename: (document as any).originalFilename
      };
    } catch (error) {
      console.error('Error getting document content:', error);
      throw new Error('Failed to get document content');
    }
  }

  /**
   * Search documents
   */
  async searchDocuments(query: string): Promise<any[]> {
    try {
      const documents = await DocumentModel.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { originalFilename: { $regex: query, $options: 'i' } },
          { tags: { $in: [new RegExp(query, 'i')] } }
        ]
      })
        .sort({ createdAt: -1 })
        .limit(100)
        .exec();

      return documents;
    } catch (error) {
      console.error('Error searching documents:', error);
      throw new Error('Failed to search documents');
    }
  }
}
