import { IStorage } from '../storage';
import { MemStorage } from '../storage'; // PostgreSQL implementation
import { MongoStorage } from './mongo-storage'; // MongoDB implementation
import clientPromise from '../mongodb';
import { initializeModels } from '../models/mongoose';

/**
 * Factory to create the appropriate storage implementation based on configuration
 */
export async function createStorage(): Promise<IStorage> {
  const useMongoDb = process.env.MONGODB_ENABLED === 'true';

  if (useMongoDb) {
    try {
      // Test MongoDB connection
      const client = await clientPromise;
      await client.db("admin").command({ ping: 1 });
      console.log("MongoDB connection successful! Using MongoDB storage.");

      // Initialize all Mongoose models
      await initializeModels();

      // Create and return MongoDB storage implementation
      return new MongoStorage();
    } catch (error) {
      console.error("MongoDB connection failed:", error);
      console.log("Falling back to PostgreSQL storage.");
      return new MemStorage();
    }
  } else {
    console.log("Using PostgreSQL storage.");
    return new MemStorage();
  }
}
