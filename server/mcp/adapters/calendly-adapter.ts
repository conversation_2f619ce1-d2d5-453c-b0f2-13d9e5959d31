/**
 * MCP Calendly Adapter
 * 
 * This adapter provides a standardized interface for <PERSON><PERSON>ly
 * to integrate with the Aizako CRM system.
 */

import axios from 'axios';
import { 
  BaseMCPSourceAdapter, 
  MCPSourceType, 
  MCPAuthType, 
  MCPSourceConfig,
  MCPRequestOptions,
  MCPResponse
} from '../index';

/**
 * Calendly Event Structure
 */
export interface CalendlyEvent {
  uri: string;
  name: string;
  status: string;
  start_time: string;
  end_time: string;
  event_type: string;
  location: {
    type: string;
    location?: string;
    join_url?: string;
  };
  invitees_counter: {
    active: number;
    limit: number;
    total: number;
  };
  created_at: string;
  updated_at: string;
  event_memberships?: Array<{
    user: string;
  }>;
  event_type_details?: {
    uri: string;
    name: string;
    duration: number;
    kind: string;
    slug: string;
    description?: string;
    color?: string;
  };
  cancellation?: {
    canceled_by: string;
    reason: string;
    canceler_type: string;
  };
  metadata?: Record<string, any>;
}

/**
 * Calendly Invitee Structure
 */
export interface CalendlyInvitee {
  uri: string;
  email: string;
  name: string;
  status: string;
  questions_and_answers: Array<{
    question: string;
    answer: string;
  }>;
  timezone: string;
  created_at: string;
  updated_at: string;
  tracking?: {
    utm_campaign?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_content?: string;
    utm_term?: string;
    salesforce_uuid?: string;
  };
  text_reminder_number?: string;
  rescheduled?: boolean;
  old_invitee?: string;
  new_invitee?: string;
  cancel_url: string;
  reschedule_url: string;
  payment?: {
    external_id: string;
    provider: string;
    amount: number;
    currency: string;
    terms: string;
    successful: boolean;
  };
  no_show?: {
    uri: string;
  };
}

/**
 * Calendly Event Type Structure
 */
export interface CalendlyEventType {
  uri: string;
  name: string;
  active: boolean;
  slug: string;
  scheduling_url: string;
  duration: number;
  kind: string;
  pooling_type: string;
  type: string;
  color: string;
  created_at: string;
  updated_at: string;
  internal_note?: string;
  description_plain?: string;
  description_html?: string;
  profile?: {
    type: string;
    name: string;
    owner: string;
  };
  secret?: boolean;
}

/**
 * Calendly Adapter Configuration
 */
export interface CalendlyAdapterConfig extends MCPSourceConfig {
  personalUrl?: string;
  organizationUri?: string;
  options?: {
    includeEventTypeDetails?: boolean;
    includeInvitees?: boolean;
    maxResults?: number;
  };
}

/**
 * MCP Calendly Adapter
 */
export class CalendlyAdapter extends BaseMCPSourceAdapter {
  private personalUrl?: string;
  private organizationUri?: string;
  private options: {
    includeEventTypeDetails: boolean;
    includeInvitees: boolean;
    maxResults: number;
  };

  constructor(config: CalendlyAdapterConfig) {
    super({
      ...config,
      type: MCPSourceType.CALENDAR,
      baseUrl: 'https://api.calendly.com'
    });
    
    this.personalUrl = config.personalUrl;
    this.organizationUri = config.organizationUri;
    this.options = {
      includeEventTypeDetails: config.options?.includeEventTypeDetails || false,
      includeInvitees: config.options?.includeInvitees || false,
      maxResults: config.options?.maxResults || 100
    };
  }

  /**
   * Connect to Calendly
   */
  async connect(): Promise<boolean> {
    try {
      // Create axios client with appropriate base URL and auth
      this.client = axios.create({
        baseURL: this.config.baseUrl,
        timeout: 10000
      });

      // Configure authentication based on auth type
      switch (this.config.auth.type) {
        case MCPAuthType.API_KEY:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.apiKey}`;
            return config;
          });
          break;
        
        case MCPAuthType.OAUTH2:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.accessToken}`;
            return config;
          });
          break;
        
        default:
          throw new Error(`Unsupported auth type for Calendly: ${this.config.auth.type}`);
      }

      // Test connection with a simple request to get user info
      const testResponse = await this.request('/users/me', 'GET');
      this.connected = testResponse.success;
      
      // If we don't have a personal URL yet, get it from the user info
      if (this.connected && !this.personalUrl && testResponse.data) {
        this.personalUrl = testResponse.data.resource.scheduling_url;
      }
      
      return this.connected;
    } catch (error) {
      console.error(`Failed to connect to Calendly:`, error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Get scheduled events
   */
  async getEvents(
    options: {
      startTime?: string;
      endTime?: string;
      status?: 'active' | 'canceled';
      limit?: number;
      includeEventTypeDetails?: boolean;
      includeInvitees?: boolean;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<CalendlyEvent[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error('Failed to connect to Calendly');
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        count: options.limit || this.options.maxResults
      };

      if (options.startTime) params.min_start_time = options.startTime;
      if (options.endTime) params.max_start_time = options.endTime;
      if (options.status) params.status = options.status;

      // Determine the endpoint based on whether we have an organization URI
      let endpoint = '/scheduled_events';
      if (this.organizationUri) {
        params.organization = this.organizationUri;
      } else {
        // Get user URI first
        const userResponse = await this.request('/users/me', 'GET');
        if (!userResponse.success || !userResponse.data) {
          throw new Error('Failed to get user information');
        }
        params.user = userResponse.data.resource.uri;
      }

      // Generate cache key if not provided
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get events
      const eventsResponse = await this.request<{ collection: CalendlyEvent[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!eventsResponse.success || !eventsResponse.data) {
        return {
          success: false,
          error: eventsResponse.error || 'Failed to get events',
          source: this.id,
          timestamp: Date.now()
        };
      }

      let events = eventsResponse.data.collection;

      // Get event type details if requested
      if ((options.includeEventTypeDetails !== undefined ? options.includeEventTypeDetails : this.options.includeEventTypeDetails) && events.length > 0) {
        for (let i = 0; i < events.length; i++) {
          const event = events[i];
          if (event.event_type) {
            const eventTypeResponse = await this.request<{ resource: CalendlyEventType }>(
              event.event_type,
              'GET'
            );
            if (eventTypeResponse.success && eventTypeResponse.data) {
              events[i] = {
                ...event,
                event_type_details: eventTypeResponse.data.resource
              };
            }
          }
        }
      }

      // Get invitees if requested
      if ((options.includeInvitees !== undefined ? options.includeInvitees : this.options.includeInvitees) && events.length > 0) {
        for (let i = 0; i < events.length; i++) {
          const event = events[i];
          const inviteesResponse = await this.request<{ collection: CalendlyInvitee[] }>(
            `${event.uri}/invitees`,
            'GET'
          );
          if (inviteesResponse.success && inviteesResponse.data) {
            events[i] = {
              ...event,
              invitees: inviteesResponse.data.collection
            };
          }
        }
      }

      return {
        success: true,
        data: events,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get events',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get event types
   */
  async getEventTypes(
    options: {
      active?: boolean;
      limit?: number;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<CalendlyEventType[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error('Failed to connect to Calendly');
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        count: options.limit || this.options.maxResults
      };

      if (options.active !== undefined) params.active = options.active;

      // Determine the endpoint based on whether we have an organization URI
      let endpoint = '/event_types';
      if (this.organizationUri) {
        params.organization = this.organizationUri;
      } else {
        // Get user URI first
        const userResponse = await this.request('/users/me', 'GET');
        if (!userResponse.success || !userResponse.data) {
          throw new Error('Failed to get user information');
        }
        params.user = userResponse.data.resource.uri;
      }

      // Generate cache key if not provided
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get event types
      const response = await this.request<{ collection: CalendlyEventType[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get event types',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.collection,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get event types',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get user information
   */
  async getUserInfo(
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<any>> {
    // Generate cache key if not provided
    const cacheKey = requestOptions?.cacheKey || 
      this.generateCacheKey('/users/me', 'GET');

    return this.request(
      '/users/me', 
      'GET', 
      undefined, 
      {
        ...requestOptions,
        cacheKey
      }
    );
  }
}
