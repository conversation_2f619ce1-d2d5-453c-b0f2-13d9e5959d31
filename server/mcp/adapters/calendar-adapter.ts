/**
 * MCP Calendar Adapter
 * 
 * This adapter provides a standardized interface for calendar providers
 * (Google Calendar, Office 365) to integrate with the Aizako CRM system.
 */

import axios from 'axios';
import { 
  BaseMCPSourceAdapter, 
  MCPSourceType, 
  MCPAuthType, 
  MCPSourceConfig,
  MCPRequestOptions,
  MCPResponse
} from '../index';

/**
 * Calendar Provider Types
 */
export enum CalendarProviderType {
  GOOGLE = 'google',
  OFFICE365 = 'office365',
  GENERIC = 'generic'
}

/**
 * Calendar Event Structure
 */
export interface CalendarEvent {
  id: string;
  calendarId: string;
  title: string;
  description?: string;
  location?: string;
  start: {
    dateTime: string;
    timeZone?: string;
  };
  end: {
    dateTime: string;
    timeZone?: string;
  };
  allDay: boolean;
  organizer: {
    email: string;
    name?: string;
  };
  attendees?: Array<{
    email: string;
    name?: string;
    status?: 'needsAction' | 'declined' | 'tentative' | 'accepted';
    optional?: boolean;
  }>;
  recurrence?: string[];
  status: 'confirmed' | 'tentative' | 'cancelled';
  visibility?: 'default' | 'public' | 'private' | 'confidential';
  conferenceData?: {
    type: string;
    url?: string;
    details?: Record<string, any>;
  };
  attachments?: Array<{
    id: string;
    name: string;
    contentType: string;
    url: string;
  }>;
  reminders?: Array<{
    method: 'email' | 'popup';
    minutes: number;
  }>;
  metadata?: Record<string, any>;
}

/**
 * Calendar Adapter Configuration
 */
export interface CalendarAdapterConfig extends MCPSourceConfig {
  provider: CalendarProviderType;
  email: string;
  options?: {
    maxResults?: number;
    defaultTimeZone?: string;
  };
}

/**
 * MCP Calendar Adapter
 */
export class CalendarAdapter extends BaseMCPSourceAdapter {
  private provider: CalendarProviderType;
  private email: string;
  private options: {
    maxResults: number;
    defaultTimeZone: string;
  };

  constructor(config: CalendarAdapterConfig) {
    super({
      ...config,
      type: MCPSourceType.CALENDAR
    });
    
    this.provider = config.provider;
    this.email = config.email;
    this.options = {
      maxResults: config.options?.maxResults || 100,
      defaultTimeZone: config.options?.defaultTimeZone || 'UTC'
    };
  }

  /**
   * Connect to the calendar provider
   */
  async connect(): Promise<boolean> {
    try {
      // Create axios client with appropriate base URL and auth
      this.client = axios.create({
        baseURL: this.config.baseUrl,
        timeout: 10000
      });

      // Configure authentication based on provider and auth type
      switch (this.config.auth.type) {
        case MCPAuthType.OAUTH2:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.accessToken}`;
            return config;
          });
          break;
        
        case MCPAuthType.API_KEY:
          this.client.interceptors.request.use(config => {
            config.headers['X-API-Key'] = this.config.auth.credentials.apiKey;
            return config;
          });
          break;
        
        case MCPAuthType.BASIC:
          this.client.interceptors.request.use(config => {
            const auth = Buffer.from(
              `${this.config.auth.credentials.username}:${this.config.auth.credentials.password}`
            ).toString('base64');
            config.headers.Authorization = `Basic ${auth}`;
            return config;
          });
          break;
      }

      // Test connection with a simple request
      const testResponse = await this.request('/test', 'GET');
      this.connected = testResponse.success;
      
      return this.connected;
    } catch (error) {
      console.error(`Failed to connect to calendar provider (${this.provider}):`, error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Get calendars for the user
   */
  async getCalendars(
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<Array<{ id: string; name: string; primary: boolean; }>>> {
    // Generate cache key if not provided
    const cacheKey = requestOptions?.cacheKey || 
      this.generateCacheKey('/calendars', 'GET');

    return this.request<Array<{ id: string; name: string; primary: boolean; }>>(
      '/calendars', 
      'GET', 
      undefined, 
      {
        ...requestOptions,
        cacheKey
      }
    );
  }

  /**
   * Get events from a calendar
   */
  async getEvents(
    options: {
      calendarId?: string;
      timeMin?: string;
      timeMax?: string;
      query?: string;
      maxResults?: number;
      singleEvents?: boolean;
      timeZone?: string;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<CalendarEvent[]>> {
    const params: Record<string, any> = {
      maxResults: options.maxResults || this.options.maxResults,
      singleEvents: options.singleEvents !== undefined ? options.singleEvents : true,
      timeZone: options.timeZone || this.options.defaultTimeZone
    };

    if (options.calendarId) params.calendarId = options.calendarId;
    if (options.timeMin) params.timeMin = options.timeMin;
    if (options.timeMax) params.timeMax = options.timeMax;
    if (options.query) params.q = options.query;

    // Generate cache key if not provided
    const cacheKey = requestOptions?.cacheKey || 
      this.generateCacheKey('/events', 'GET', params);

    return this.request<CalendarEvent[]>('/events', 'GET', params, {
      ...requestOptions,
      cacheKey
    });
  }

  /**
   * Get a specific event by ID
   */
  async getEvent(
    eventId: string,
    calendarId?: string,
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<CalendarEvent>> {
    const params: Record<string, any> = {};
    if (calendarId) params.calendarId = calendarId;

    // Generate cache key if not provided
    const cacheKey = requestOptions?.cacheKey || 
      this.generateCacheKey(`/events/${eventId}`, 'GET', params);

    return this.request<CalendarEvent>(`/events/${eventId}`, 'GET', params, {
      ...requestOptions,
      cacheKey
    });
  }

  /**
   * Create a new calendar event
   */
  async createEvent(
    event: {
      calendarId?: string;
      title: string;
      description?: string;
      location?: string;
      start: {
        dateTime: string;
        timeZone?: string;
      };
      end: {
        dateTime: string;
        timeZone?: string;
      };
      attendees?: Array<{
        email: string;
        name?: string;
        optional?: boolean;
      }>;
      recurrence?: string[];
      visibility?: 'default' | 'public' | 'private' | 'confidential';
      conferenceData?: {
        createRequest?: {
          requestId?: string;
          conferenceSolutionKey?: {
            type: string;
          };
        };
      };
      reminders?: Array<{
        method: 'email' | 'popup';
        minutes: number;
      }>;
    },
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<CalendarEvent>> {
    return this.request<CalendarEvent>('/events', 'POST', event, requestOptions);
  }

  /**
   * Update an existing calendar event
   */
  async updateEvent(
    eventId: string,
    event: {
      calendarId?: string;
      title?: string;
      description?: string;
      location?: string;
      start?: {
        dateTime: string;
        timeZone?: string;
      };
      end?: {
        dateTime: string;
        timeZone?: string;
      };
      attendees?: Array<{
        email: string;
        name?: string;
        optional?: boolean;
      }>;
      recurrence?: string[];
      visibility?: 'default' | 'public' | 'private' | 'confidential';
      conferenceData?: {
        createRequest?: {
          requestId?: string;
          conferenceSolutionKey?: {
            type: string;
          };
        };
      };
      reminders?: Array<{
        method: 'email' | 'popup';
        minutes: number;
      }>;
    },
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<CalendarEvent>> {
    return this.request<CalendarEvent>(`/events/${eventId}`, 'PATCH', event, requestOptions);
  }

  /**
   * Delete a calendar event
   */
  async deleteEvent(
    eventId: string,
    calendarId?: string,
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<void>> {
    const params: Record<string, any> = {};
    if (calendarId) params.calendarId = calendarId;

    return this.request<void>(`/events/${eventId}`, 'DELETE', params, requestOptions);
  }
}
