/**
 * MCP Social Media Adapter
 * 
 * This adapter provides a standardized interface for social media platforms
 * to integrate with the Aizako CRM system.
 */

import axios from 'axios';
import { 
  BaseMCPSourceAdapter, 
  MCPSourceType, 
  MCPAuthType, 
  MCPSourceConfig,
  MCPRequestOptions,
  MCPResponse
} from '../index';

/**
 * Social Media Provider Types
 */
export enum SocialMediaProviderType {
  LINKEDIN = 'linkedin',
  TWITTER = 'twitter',
  FACEBOOK = 'facebook',
  INSTAGRAM = 'instagram',
  CUSTOM = 'custom'
}

/**
 * Social Media Post Structure
 */
export interface SocialMediaPost {
  id: string;
  platform: SocialMediaProviderType;
  content: string;
  url?: string;
  mediaUrls?: string[];
  publishedAt: string;
  author: {
    id: string;
    name: string;
    username?: string;
    profileUrl?: string;
    profileImageUrl?: string;
  };
  engagement?: {
    likes: number;
    comments: number;
    shares: number;
    views?: number;
  };
  isReply?: boolean;
  replyToId?: string;
  hashtags?: string[];
  mentions?: string[];
  metadata?: Record<string, any>;
}

/**
 * Social Media Message Structure
 */
export interface SocialMediaMessage {
  id: string;
  platform: SocialMediaProviderType;
  content: string;
  sentAt: string;
  sender: {
    id: string;
    name: string;
    username?: string;
    profileUrl?: string;
    profileImageUrl?: string;
  };
  recipient: {
    id: string;
    name: string;
    username?: string;
    profileUrl?: string;
    profileImageUrl?: string;
  };
  isRead: boolean;
  mediaUrls?: string[];
  metadata?: Record<string, any>;
}

/**
 * Social Media Comment Structure
 */
export interface SocialMediaComment {
  id: string;
  platform: SocialMediaProviderType;
  content: string;
  postId: string;
  publishedAt: string;
  author: {
    id: string;
    name: string;
    username?: string;
    profileUrl?: string;
    profileImageUrl?: string;
  };
  engagement?: {
    likes: number;
    replies: number;
  };
  isReply?: boolean;
  replyToId?: string;
  metadata?: Record<string, any>;
}

/**
 * Social Media Profile Structure
 */
export interface SocialMediaProfile {
  id: string;
  platform: SocialMediaProviderType;
  name: string;
  username?: string;
  bio?: string;
  profileUrl: string;
  profileImageUrl?: string;
  followers: number;
  following: number;
  postsCount: number;
  isVerified?: boolean;
  location?: string;
  website?: string;
  joinedAt?: string;
  metadata?: Record<string, any>;
}

/**
 * Social Media Adapter Configuration
 */
export interface SocialMediaAdapterConfig extends MCPSourceConfig {
  provider: SocialMediaProviderType;
  username?: string;
  profileId?: string;
  options?: {
    includeReplies?: boolean;
    includeComments?: boolean;
    maxResults?: number;
  };
}

/**
 * MCP Social Media Adapter
 */
export class SocialMediaAdapter extends BaseMCPSourceAdapter {
  private provider: SocialMediaProviderType;
  private username?: string;
  private profileId?: string;
  private options: {
    includeReplies: boolean;
    includeComments: boolean;
    maxResults: number;
  };

  constructor(config: SocialMediaAdapterConfig) {
    super({
      ...config,
      type: MCPSourceType.SOCIAL_MEDIA
    });
    
    this.provider = config.provider;
    this.username = config.username;
    this.profileId = config.profileId;
    this.options = {
      includeReplies: config.options?.includeReplies || false,
      includeComments: config.options?.includeComments || false,
      maxResults: config.options?.maxResults || 100
    };
  }

  /**
   * Connect to the social media platform
   */
  async connect(): Promise<boolean> {
    try {
      // Create axios client with appropriate base URL and auth
      this.client = axios.create({
        baseURL: this.config.baseUrl,
        timeout: 10000
      });

      // Configure authentication based on auth type
      switch (this.config.auth.type) {
        case MCPAuthType.API_KEY:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.apiKey}`;
            return config;
          });
          break;
        
        case MCPAuthType.OAUTH2:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.accessToken}`;
            return config;
          });
          break;
        
        default:
          throw new Error(`Unsupported auth type for social media: ${this.config.auth.type}`);
      }

      // Test connection with a simple request
      const testEndpoint = this.getProviderEndpoint('/test');
      const testResponse = await this.request(testEndpoint, 'GET');
      this.connected = testResponse.success;
      
      return this.connected;
    } catch (error) {
      console.error(`Failed to connect to social media platform (${this.provider}):`, error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Get posts from the social media platform
   */
  async getPosts(
    options: {
      startDate?: string;
      endDate?: string;
      includeReplies?: boolean;
      hashtag?: string;
      keyword?: string;
      limit?: number;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<SocialMediaPost[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error(`Failed to connect to social media platform (${this.provider})`);
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        limit: options.limit || this.options.maxResults,
        include_replies: options.includeReplies !== undefined ? options.includeReplies : this.options.includeReplies
      };

      if (options.startDate) params.start_date = options.startDate;
      if (options.endDate) params.end_date = options.endDate;
      if (options.hashtag) params.hashtag = options.hashtag;
      if (options.keyword) params.keyword = options.keyword;
      if (this.username) params.username = this.username;
      if (this.profileId) params.profile_id = this.profileId;

      // Generate cache key if not provided
      const endpoint = this.getProviderEndpoint('/posts');
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get posts
      const response = await this.request<{ posts: SocialMediaPost[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get posts',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.posts,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get posts',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get messages from the social media platform
   */
  async getMessages(
    options: {
      startDate?: string;
      endDate?: string;
      recipientId?: string;
      unreadOnly?: boolean;
      limit?: number;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<SocialMediaMessage[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error(`Failed to connect to social media platform (${this.provider})`);
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        limit: options.limit || this.options.maxResults
      };

      if (options.startDate) params.start_date = options.startDate;
      if (options.endDate) params.end_date = options.endDate;
      if (options.recipientId) params.recipient_id = options.recipientId;
      if (options.unreadOnly !== undefined) params.unread_only = options.unreadOnly;
      if (this.profileId) params.profile_id = this.profileId;

      // Generate cache key if not provided
      const endpoint = this.getProviderEndpoint('/messages');
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get messages
      const response = await this.request<{ messages: SocialMediaMessage[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get messages',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.messages,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get messages',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get comments from the social media platform
   */
  async getComments(
    options: {
      postId?: string;
      startDate?: string;
      endDate?: string;
      includeReplies?: boolean;
      limit?: number;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<SocialMediaComment[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error(`Failed to connect to social media platform (${this.provider})`);
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        limit: options.limit || this.options.maxResults,
        include_replies: options.includeReplies !== undefined ? options.includeReplies : this.options.includeReplies
      };

      if (options.postId) params.post_id = options.postId;
      if (options.startDate) params.start_date = options.startDate;
      if (options.endDate) params.end_date = options.endDate;
      if (this.profileId) params.profile_id = this.profileId;

      // Generate cache key if not provided
      const endpoint = this.getProviderEndpoint('/comments');
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get comments
      const response = await this.request<{ comments: SocialMediaComment[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get comments',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.comments,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get comments',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get profile information from the social media platform
   */
  async getProfile(
    options: {
      username?: string;
      profileId?: string;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<SocialMediaProfile>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error(`Failed to connect to social media platform (${this.provider})`);
        }
      }

      // Build query parameters
      const params: Record<string, any> = {};

      const username = options.username || this.username;
      const profileId = options.profileId || this.profileId;

      if (username) params.username = username;
      if (profileId) params.profile_id = profileId;

      if (!username && !profileId) {
        throw new Error('Either username or profileId must be provided');
      }

      // Generate cache key if not provided
      const endpoint = this.getProviderEndpoint('/profile');
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get profile
      const response = await this.request<{ profile: SocialMediaProfile }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get profile',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.profile,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get profile',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get provider-specific endpoint
   */
  private getProviderEndpoint(path: string): string {
    switch (this.provider) {
      case SocialMediaProviderType.LINKEDIN:
        return `/v2${path}`;
      
      case SocialMediaProviderType.TWITTER:
        return `/2${path}`;
      
      case SocialMediaProviderType.FACEBOOK:
        return `/v16.0${path}`;
      
      case SocialMediaProviderType.INSTAGRAM:
        return `/v16.0${path}`;
      
      default:
        return path;
    }
  }
}
