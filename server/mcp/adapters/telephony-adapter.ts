/**
 * MCP Telephony Adapter
 * 
 * This adapter provides a standardized interface for telephony services
 * to integrate with the Aizako CRM system.
 */

import axios from 'axios';
import { 
  BaseMCPSourceAdapter, 
  MCPSourceType, 
  MCPAuthType, 
  MCPSourceConfig,
  MCPRequestOptions,
  MCPResponse
} from '../index';

/**
 * Telephony Provider Types
 */
export enum TelephonyProviderType {
  TWILIO = 'twilio',
  VONAGE = 'vonage',
  PLIVO = 'plivo',
  CUSTOM = 'custom'
}

/**
 * Call Direction
 */
export enum CallDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound'
}

/**
 * Call Status
 */
export enum CallStatus {
  QUEUED = 'queued',
  RINGING = 'ringing',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  BUSY = 'busy',
  FAILED = 'failed',
  NO_ANSWER = 'no-answer',
  CANCELED = 'canceled'
}

/**
 * Call Record Structure
 */
export interface CallRecord {
  id: string;
  from: string;
  to: string;
  direction: CallDirection;
  status: CallStatus;
  duration?: number;
  startTime?: string;
  endTime?: string;
  recordingUrl?: string;
  transcriptionUrl?: string;
  transcriptionText?: string;
  notes?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * SMS Message Structure
 */
export interface SMSMessage {
  id: string;
  from: string;
  to: string;
  body: string;
  direction: 'inbound' | 'outbound';
  status: 'queued' | 'sent' | 'delivered' | 'failed';
  sentAt?: string;
  deliveredAt?: string;
  metadata?: Record<string, any>;
}

/**
 * Voicemail Structure
 */
export interface Voicemail {
  id: string;
  from: string;
  to: string;
  duration?: number;
  recordingUrl?: string;
  transcriptionText?: string;
  receivedAt: string;
  isNew: boolean;
  metadata?: Record<string, any>;
}

/**
 * Telephony Adapter Configuration
 */
export interface TelephonyAdapterConfig extends MCPSourceConfig {
  provider: TelephonyProviderType;
  phoneNumber?: string;
  options?: {
    transcribeVoicemail?: boolean;
    transcribeCalls?: boolean;
    recordCalls?: boolean;
    maxResults?: number;
  };
}

/**
 * MCP Telephony Adapter
 */
export class TelephonyAdapter extends BaseMCPSourceAdapter {
  private provider: TelephonyProviderType;
  private phoneNumber?: string;
  private options: {
    transcribeVoicemail: boolean;
    transcribeCalls: boolean;
    recordCalls: boolean;
    maxResults: number;
  };

  constructor(config: TelephonyAdapterConfig) {
    super({
      ...config,
      type: MCPSourceType.TELEPHONY
    });
    
    this.provider = config.provider;
    this.phoneNumber = config.phoneNumber;
    this.options = {
      transcribeVoicemail: config.options?.transcribeVoicemail || false,
      transcribeCalls: config.options?.transcribeCalls || false,
      recordCalls: config.options?.recordCalls || false,
      maxResults: config.options?.maxResults || 100
    };
  }

  /**
   * Connect to the telephony service
   */
  async connect(): Promise<boolean> {
    try {
      // Create axios client with appropriate base URL and auth
      this.client = axios.create({
        baseURL: this.config.baseUrl,
        timeout: 10000
      });

      // Configure authentication based on auth type
      switch (this.config.auth.type) {
        case MCPAuthType.API_KEY:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.apiKey}`;
            return config;
          });
          break;
        
        case MCPAuthType.BASIC:
          this.client.interceptors.request.use(config => {
            const { username, password } = this.config.auth.credentials;
            const auth = Buffer.from(`${username}:${password}`).toString('base64');
            config.headers.Authorization = `Basic ${auth}`;
            return config;
          });
          break;
        
        case MCPAuthType.OAUTH2:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.accessToken}`;
            return config;
          });
          break;
        
        default:
          throw new Error(`Unsupported auth type for telephony: ${this.config.auth.type}`);
      }

      // Test connection with a simple request
      const testEndpoint = this.getProviderEndpoint('/test');
      const testResponse = await this.request(testEndpoint, 'GET');
      this.connected = testResponse.success;
      
      return this.connected;
    } catch (error) {
      console.error(`Failed to connect to telephony service (${this.provider}):`, error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Get call records
   */
  async getCallRecords(
    options: {
      startDate?: string;
      endDate?: string;
      direction?: CallDirection;
      status?: CallStatus;
      limit?: number;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<CallRecord[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error(`Failed to connect to telephony service (${this.provider})`);
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        limit: options.limit || this.options.maxResults
      };

      if (options.startDate) params.startDate = options.startDate;
      if (options.endDate) params.endDate = options.endDate;
      if (options.direction) params.direction = options.direction;
      if (options.status) params.status = options.status;
      if (this.phoneNumber) params.phoneNumber = this.phoneNumber;

      // Generate cache key if not provided
      const endpoint = this.getProviderEndpoint('/calls');
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get call records
      const response = await this.request<{ calls: CallRecord[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get call records',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.calls,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get call records',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get SMS messages
   */
  async getSMSMessages(
    options: {
      startDate?: string;
      endDate?: string;
      direction?: 'inbound' | 'outbound';
      limit?: number;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<SMSMessage[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error(`Failed to connect to telephony service (${this.provider})`);
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        limit: options.limit || this.options.maxResults
      };

      if (options.startDate) params.startDate = options.startDate;
      if (options.endDate) params.endDate = options.endDate;
      if (options.direction) params.direction = options.direction;
      if (this.phoneNumber) params.phoneNumber = this.phoneNumber;

      // Generate cache key if not provided
      const endpoint = this.getProviderEndpoint('/sms');
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get SMS messages
      const response = await this.request<{ messages: SMSMessage[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get SMS messages',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.messages,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get SMS messages',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get voicemails
   */
  async getVoicemails(
    options: {
      startDate?: string;
      endDate?: string;
      isNew?: boolean;
      limit?: number;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<Voicemail[]>> {
    try {
      // Ensure we're connected
      if (!this.isConnected()) {
        await this.connect();
        if (!this.isConnected()) {
          throw new Error(`Failed to connect to telephony service (${this.provider})`);
        }
      }

      // Build query parameters
      const params: Record<string, any> = {
        limit: options.limit || this.options.maxResults
      };

      if (options.startDate) params.startDate = options.startDate;
      if (options.endDate) params.endDate = options.endDate;
      if (options.isNew !== undefined) params.isNew = options.isNew;
      if (this.phoneNumber) params.phoneNumber = this.phoneNumber;

      // Generate cache key if not provided
      const endpoint = this.getProviderEndpoint('/voicemails');
      const cacheKey = requestOptions?.cacheKey || 
        this.generateCacheKey(endpoint, 'GET', params);

      // Get voicemails
      const response = await this.request<{ voicemails: Voicemail[] }>(
        endpoint, 
        'GET', 
        params, 
        {
          ...requestOptions,
          cacheKey
        }
      );

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || 'Failed to get voicemails',
          source: this.id,
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: response.data.voicemails,
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to get voicemails',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get provider-specific endpoint
   */
  private getProviderEndpoint(path: string): string {
    switch (this.provider) {
      case TelephonyProviderType.TWILIO:
        return `/2010-04-01/Accounts${path}`;
      
      case TelephonyProviderType.VONAGE:
        return `/v2${path}`;
      
      case TelephonyProviderType.PLIVO:
        return `/v1/Account${path}`;
      
      default:
        return path;
    }
  }
}
