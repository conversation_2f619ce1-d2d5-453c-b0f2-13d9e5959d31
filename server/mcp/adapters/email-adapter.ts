/**
 * MCP Email Adapter
 * 
 * This adapter provides a standardized interface for email providers
 * (Gmail, Outlook) to integrate with the Aizako CRM system.
 */

import axios from 'axios';
import { 
  BaseMCPSourceAdapter, 
  MCPSourceType, 
  MCPAuthType, 
  MCPSourceConfig,
  MCPRequestOptions,
  MCPResponse
} from '../index';

/**
 * Email Provider Types
 */
export enum EmailProviderType {
  GMAIL = 'gmail',
  OUTLOOK = 'outlook',
  GENERIC = 'generic'
}

/**
 * Email Message Structure
 */
export interface EmailMessage {
  id: string;
  threadId?: string;
  from: {
    email: string;
    name?: string;
  };
  to: Array<{
    email: string;
    name?: string;
  }>;
  cc?: Array<{
    email: string;
    name?: string;
  }>;
  bcc?: Array<{
    email: string;
    name?: string;
  }>;
  subject: string;
  body: {
    text?: string;
    html?: string;
  };
  attachments?: Array<{
    id: string;
    name: string;
    contentType: string;
    size: number;
    url?: string;
  }>;
  date: string;
  read: boolean;
  labels?: string[];
  importance?: 'low' | 'normal' | 'high';
  metadata?: Record<string, any>;
}

/**
 * Email Adapter Configuration
 */
export interface EmailAdapterConfig extends MCPSourceConfig {
  provider: EmailProviderType;
  email: string;
  options?: {
    maxResults?: number;
    includeAttachments?: boolean;
    includeBody?: boolean;
  };
}

/**
 * MCP Email Adapter
 */
export class EmailAdapter extends BaseMCPSourceAdapter {
  private provider: EmailProviderType;
  private email: string;
  private options: {
    maxResults: number;
    includeAttachments: boolean;
    includeBody: boolean;
  };

  constructor(config: EmailAdapterConfig) {
    super({
      ...config,
      type: MCPSourceType.EMAIL
    });
    
    this.provider = config.provider;
    this.email = config.email;
    this.options = {
      maxResults: config.options?.maxResults || 50,
      includeAttachments: config.options?.includeAttachments || false,
      includeBody: config.options?.includeBody || true
    };
  }

  /**
   * Connect to the email provider
   */
  async connect(): Promise<boolean> {
    try {
      // Create axios client with appropriate base URL and auth
      this.client = axios.create({
        baseURL: this.config.baseUrl,
        timeout: 10000
      });

      // Configure authentication based on provider and auth type
      switch (this.config.auth.type) {
        case MCPAuthType.OAUTH2:
          this.client.interceptors.request.use(config => {
            config.headers.Authorization = `Bearer ${this.config.auth.credentials.accessToken}`;
            return config;
          });
          break;
        
        case MCPAuthType.API_KEY:
          this.client.interceptors.request.use(config => {
            config.headers['X-API-Key'] = this.config.auth.credentials.apiKey;
            return config;
          });
          break;
        
        case MCPAuthType.BASIC:
          this.client.interceptors.request.use(config => {
            const auth = Buffer.from(
              `${this.config.auth.credentials.username}:${this.config.auth.credentials.password}`
            ).toString('base64');
            config.headers.Authorization = `Basic ${auth}`;
            return config;
          });
          break;
      }

      // Test connection with a simple request
      const testResponse = await this.request('/test', 'GET');
      this.connected = testResponse.success;
      
      return this.connected;
    } catch (error) {
      console.error(`Failed to connect to email provider (${this.provider}):`, error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Get messages from the email provider
   */
  async getMessages(
    options: {
      folder?: string;
      query?: string;
      from?: string;
      to?: string;
      after?: string;
      before?: string;
      limit?: number;
      includeBody?: boolean;
      includeAttachments?: boolean;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<EmailMessage[]>> {
    const params: Record<string, any> = {
      limit: options.limit || this.options.maxResults,
      includeBody: options.includeBody !== undefined ? options.includeBody : this.options.includeBody,
      includeAttachments: options.includeAttachments !== undefined ? options.includeAttachments : this.options.includeAttachments
    };

    if (options.folder) params.folder = options.folder;
    if (options.query) params.query = options.query;
    if (options.from) params.from = options.from;
    if (options.to) params.to = options.to;
    if (options.after) params.after = options.after;
    if (options.before) params.before = options.before;

    // Generate cache key if not provided
    const cacheKey = requestOptions?.cacheKey || 
      this.generateCacheKey('/messages', 'GET', params);

    return this.request<EmailMessage[]>('/messages', 'GET', params, {
      ...requestOptions,
      cacheKey
    });
  }

  /**
   * Get a specific message by ID
   */
  async getMessage(
    id: string,
    options: {
      includeBody?: boolean;
      includeAttachments?: boolean;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<EmailMessage>> {
    const params: Record<string, any> = {
      includeBody: options.includeBody !== undefined ? options.includeBody : this.options.includeBody,
      includeAttachments: options.includeAttachments !== undefined ? options.includeAttachments : this.options.includeAttachments
    };

    // Generate cache key if not provided
    const cacheKey = requestOptions?.cacheKey || 
      this.generateCacheKey(`/messages/${id}`, 'GET', params);

    return this.request<EmailMessage>(`/messages/${id}`, 'GET', params, {
      ...requestOptions,
      cacheKey
    });
  }

  /**
   * Send an email message
   */
  async sendMessage(
    message: {
      to: Array<{
        email: string;
        name?: string;
      }>;
      cc?: Array<{
        email: string;
        name?: string;
      }>;
      bcc?: Array<{
        email: string;
        name?: string;
      }>;
      subject: string;
      body: {
        text?: string;
        html?: string;
      };
      attachments?: Array<{
        name: string;
        contentType: string;
        content: string; // Base64 encoded content
      }>;
      importance?: 'low' | 'normal' | 'high';
    },
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<{ id: string }>> {
    return this.request<{ id: string }>('/messages', 'POST', message, requestOptions);
  }

  /**
   * Get email folders/labels
   */
  async getFolders(
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<Array<{ id: string; name: string; unreadCount: number }>>> {
    // Generate cache key if not provided
    const cacheKey = requestOptions?.cacheKey || 
      this.generateCacheKey('/folders', 'GET');

    return this.request<Array<{ id: string; name: string; unreadCount: number }>>(
      '/folders', 
      'GET', 
      undefined, 
      {
        ...requestOptions,
        cacheKey
      }
    );
  }

  /**
   * Search for email messages
   */
  async searchMessages(
    query: string,
    options: {
      folder?: string;
      limit?: number;
      includeBody?: boolean;
      includeAttachments?: boolean;
    } = {},
    requestOptions?: MCPRequestOptions
  ): Promise<MCPResponse<EmailMessage[]>> {
    return this.getMessages({
      query,
      folder: options.folder,
      limit: options.limit,
      includeBody: options.includeBody,
      includeAttachments: options.includeAttachments
    }, requestOptions);
  }
}
