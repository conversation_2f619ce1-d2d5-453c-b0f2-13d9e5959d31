/**
 * MCP Adapter Factory
 *
 * This module provides factory functions for creating MCP adapters
 * based on configuration.
 */

import { MCPSourceType, MCPSourceAdapter, MCPSourceConfig, MCPAuthType } from './index';
import { EmailAdapter, EmailAdapterConfig, EmailProviderType } from './adapters/email-adapter';
import { CalendarAdapter, CalendarAdapterConfig, CalendarProviderType } from './adapters/calendar-adapter';
import { CalendlyAdapter, CalendlyAdapterConfig } from './adapters/calendly-adapter';
import { TelephonyAdapter, TelephonyAdapterConfig, TelephonyProviderType } from './adapters/telephony-adapter';
import { SocialMediaAdapter, SocialMediaAdapterConfig, SocialMediaProviderType } from './adapters/social-media-adapter';

/**
 * Create an MCP adapter based on configuration
 */
export function createAdapter(config: MCPSourceConfig): MCPSourceAdapter | null {
  switch (config.type) {
    case MCPSourceType.EMAIL:
      return createEmailAdapter(config as EmailAdapterConfig);

    case MCPSourceType.CALENDAR:
      // Check if this is a Calendly adapter
      if ((config as any).calendly === true) {
        return createCalendlyAdapter(config as CalendlyAdapterConfig);
      }
      return createCalendarAdapter(config as CalendarAdapterConfig);

    case MCPSourceType.TELEPHONY:
      return createTelephonyAdapter(config as TelephonyAdapterConfig);

    case MCPSourceType.SOCIAL_MEDIA:
      return createSocialMediaAdapter(config as SocialMediaAdapterConfig);

    default:
      console.error(`Unsupported MCP source type: ${config.type}`);
      return null;
  }
}

/**
 * Create an email adapter
 */
export function createEmailAdapter(config: EmailAdapterConfig): EmailAdapter {
  // Set provider-specific defaults if not specified
  switch (config.provider) {
    case EmailProviderType.GMAIL:
      if (!config.baseUrl) {
        config.baseUrl = 'https://gmail.googleapis.com/gmail/v1/users/me';
      }
      break;

    case EmailProviderType.OUTLOOK:
      if (!config.baseUrl) {
        config.baseUrl = 'https://graph.microsoft.com/v1.0/me/messages';
      }
      break;
  }

  return new EmailAdapter(config);
}

/**
 * Create a calendar adapter
 */
export function createCalendarAdapter(config: CalendarAdapterConfig): CalendarAdapter {
  // Set provider-specific defaults if not specified
  switch (config.provider) {
    case CalendarProviderType.GOOGLE:
      if (!config.baseUrl) {
        config.baseUrl = 'https://www.googleapis.com/calendar/v3';
      }
      break;

    case CalendarProviderType.OFFICE365:
      if (!config.baseUrl) {
        config.baseUrl = 'https://graph.microsoft.com/v1.0/me/calendar';
      }
      break;
  }

  return new CalendarAdapter(config);
}

/**
 * Create a Gmail adapter with OAuth2 authentication
 */
export function createGmailAdapter(
  id: string,
  name: string,
  email: string,
  accessToken: string,
  refreshToken?: string,
  options?: {
    maxResults?: number;
    includeAttachments?: boolean;
    includeBody?: boolean;
  }
): EmailAdapter {
  return createEmailAdapter({
    id,
    name,
    provider: EmailProviderType.GMAIL,
    email,
    baseUrl: 'https://gmail.googleapis.com/gmail/v1/users/me',
    auth: {
      type: 'oauth2',
      credentials: {
        accessToken,
        refreshToken
      }
    },
    options
  });
}

/**
 * Create an Outlook adapter with OAuth2 authentication
 */
export function createOutlookAdapter(
  id: string,
  name: string,
  email: string,
  accessToken: string,
  refreshToken?: string,
  options?: {
    maxResults?: number;
    includeAttachments?: boolean;
    includeBody?: boolean;
  }
): EmailAdapter {
  return createEmailAdapter({
    id,
    name,
    provider: EmailProviderType.OUTLOOK,
    email,
    baseUrl: 'https://graph.microsoft.com/v1.0/me/messages',
    auth: {
      type: 'oauth2',
      credentials: {
        accessToken,
        refreshToken
      }
    },
    options
  });
}

/**
 * Create a Google Calendar adapter with OAuth2 authentication
 */
export function createGoogleCalendarAdapter(
  id: string,
  name: string,
  email: string,
  accessToken: string,
  refreshToken?: string,
  options?: {
    maxResults?: number;
    defaultTimeZone?: string;
  }
): CalendarAdapter {
  return createCalendarAdapter({
    id,
    name,
    provider: CalendarProviderType.GOOGLE,
    email,
    baseUrl: 'https://www.googleapis.com/calendar/v3',
    auth: {
      type: 'oauth2',
      credentials: {
        accessToken,
        refreshToken
      }
    },
    options
  });
}

/**
 * Create an Office 365 Calendar adapter with OAuth2 authentication
 */
export function createOffice365CalendarAdapter(
  id: string,
  name: string,
  email: string,
  accessToken: string,
  refreshToken?: string,
  options?: {
    maxResults?: number;
    defaultTimeZone?: string;
  }
): CalendarAdapter {
  return createCalendarAdapter({
    id,
    name,
    provider: CalendarProviderType.OFFICE365,
    email,
    baseUrl: 'https://graph.microsoft.com/v1.0/me/calendar',
    auth: {
      type: 'oauth2',
      credentials: {
        accessToken,
        refreshToken
      }
    },
    options
  });
}

/**
 * Create a Calendly adapter
 */
export function createCalendlyAdapter(config: CalendlyAdapterConfig): CalendlyAdapter {
  return new CalendlyAdapter(config);
}

/**
 * Create a Calendly adapter with API key authentication
 */
export function createCalendlyApiKeyAdapter(
  id: string,
  name: string,
  apiKey: string,
  personalUrl?: string,
  organizationUri?: string,
  options?: {
    includeEventTypeDetails?: boolean;
    includeInvitees?: boolean;
    maxResults?: number;
  }
): CalendlyAdapter {
  return createCalendlyAdapter({
    id,
    name,
    calendly: true,
    type: MCPSourceType.CALENDAR,
    personalUrl,
    organizationUri,
    auth: {
      type: MCPAuthType.API_KEY,
      credentials: {
        apiKey
      }
    },
    options
  });
}

/**
 * Create a Calendly adapter with OAuth2 authentication
 */
export function createCalendlyOAuthAdapter(
  id: string,
  name: string,
  accessToken: string,
  refreshToken?: string,
  personalUrl?: string,
  organizationUri?: string,
  options?: {
    includeEventTypeDetails?: boolean;
    includeInvitees?: boolean;
    maxResults?: number;
  }
): CalendlyAdapter {
  return createCalendlyAdapter({
    id,
    name,
    calendly: true,
    type: MCPSourceType.CALENDAR,
    personalUrl,
    organizationUri,
    auth: {
      type: MCPAuthType.OAUTH2,
      credentials: {
        accessToken,
        refreshToken
      }
    },
    options
  });
}

/**
 * Create a telephony adapter
 */
export function createTelephonyAdapter(config: TelephonyAdapterConfig): TelephonyAdapter {
  return new TelephonyAdapter(config);
}

/**
 * Create a telephony adapter with API key authentication
 */
export function createTelephonyApiKeyAdapter(
  id: string,
  name: string,
  provider: TelephonyProviderType,
  apiKey: string,
  phoneNumber?: string,
  baseUrl?: string,
  options?: {
    transcribeVoicemail?: boolean;
    transcribeCalls?: boolean;
    recordCalls?: boolean;
    maxResults?: number;
  }
): TelephonyAdapter {
  return createTelephonyAdapter({
    id,
    name,
    type: MCPSourceType.TELEPHONY,
    provider,
    phoneNumber,
    baseUrl: baseUrl || getDefaultTelephonyBaseUrl(provider),
    auth: {
      type: MCPAuthType.API_KEY,
      credentials: {
        apiKey
      }
    },
    options
  });
}

/**
 * Create a telephony adapter with basic authentication
 */
export function createTelephonyBasicAdapter(
  id: string,
  name: string,
  provider: TelephonyProviderType,
  username: string,
  password: string,
  phoneNumber?: string,
  baseUrl?: string,
  options?: {
    transcribeVoicemail?: boolean;
    transcribeCalls?: boolean;
    recordCalls?: boolean;
    maxResults?: number;
  }
): TelephonyAdapter {
  return createTelephonyAdapter({
    id,
    name,
    type: MCPSourceType.TELEPHONY,
    provider,
    phoneNumber,
    baseUrl: baseUrl || getDefaultTelephonyBaseUrl(provider),
    auth: {
      type: MCPAuthType.BASIC,
      credentials: {
        username,
        password
      }
    },
    options
  });
}

/**
 * Create a social media adapter
 */
export function createSocialMediaAdapter(config: SocialMediaAdapterConfig): SocialMediaAdapter {
  return new SocialMediaAdapter(config);
}

/**
 * Create a social media adapter with API key authentication
 */
export function createSocialMediaApiKeyAdapter(
  id: string,
  name: string,
  provider: SocialMediaProviderType,
  apiKey: string,
  username?: string,
  profileId?: string,
  baseUrl?: string,
  options?: {
    includeReplies?: boolean;
    includeComments?: boolean;
    maxResults?: number;
  }
): SocialMediaAdapter {
  return createSocialMediaAdapter({
    id,
    name,
    type: MCPSourceType.SOCIAL_MEDIA,
    provider,
    username,
    profileId,
    baseUrl: baseUrl || getDefaultSocialMediaBaseUrl(provider),
    auth: {
      type: MCPAuthType.API_KEY,
      credentials: {
        apiKey
      }
    },
    options
  });
}

/**
 * Create a social media adapter with OAuth2 authentication
 */
export function createSocialMediaOAuthAdapter(
  id: string,
  name: string,
  provider: SocialMediaProviderType,
  accessToken: string,
  refreshToken?: string,
  username?: string,
  profileId?: string,
  baseUrl?: string,
  options?: {
    includeReplies?: boolean;
    includeComments?: boolean;
    maxResults?: number;
  }
): SocialMediaAdapter {
  return createSocialMediaAdapter({
    id,
    name,
    type: MCPSourceType.SOCIAL_MEDIA,
    provider,
    username,
    profileId,
    baseUrl: baseUrl || getDefaultSocialMediaBaseUrl(provider),
    auth: {
      type: MCPAuthType.OAUTH2,
      credentials: {
        accessToken,
        refreshToken
      }
    },
    options
  });
}

/**
 * Get default base URL for telephony provider
 */
function getDefaultTelephonyBaseUrl(provider: TelephonyProviderType): string {
  switch (provider) {
    case TelephonyProviderType.TWILIO:
      return 'https://api.twilio.com';
    case TelephonyProviderType.VONAGE:
      return 'https://api.nexmo.com';
    case TelephonyProviderType.PLIVO:
      return 'https://api.plivo.com';
    default:
      return 'https://api.example.com';
  }
}

/**
 * Get default base URL for social media provider
 */
function getDefaultSocialMediaBaseUrl(provider: SocialMediaProviderType): string {
  switch (provider) {
    case SocialMediaProviderType.LINKEDIN:
      return 'https://api.linkedin.com';
    case SocialMediaProviderType.TWITTER:
      return 'https://api.twitter.com';
    case SocialMediaProviderType.FACEBOOK:
      return 'https://graph.facebook.com';
    case SocialMediaProviderType.INSTAGRAM:
      return 'https://graph.instagram.com';
    default:
      return 'https://api.example.com';
  }
}
