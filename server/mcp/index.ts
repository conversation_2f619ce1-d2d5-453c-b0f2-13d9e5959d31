/**
 * Model Context Protocol (MCP) Server
 *
 * This module provides a standardized protocol for connecting to diverse data sources
 * (email, calendar, telephony, social) to reduce integration complexity and maintenance.
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { EventEmitter } from 'events';
import { createHash } from 'crypto';
import { Redis } from 'ioredis';

// Configuration
const MCP_CONFIG = {
  CACHE_TTL: parseInt(process.env.MCP_CACHE_TTL || '3600'), // Default 1 hour
  RATE_LIMIT: parseInt(process.env.MCP_RATE_LIMIT || '100'), // Default 100 requests per minute
  TIMEOUT: parseInt(process.env.MCP_TIMEOUT || '10000'), // Default 10 seconds
  RETRY_ATTEMPTS: parseInt(process.env.MCP_RETRY_ATTEMPTS || '3'), // Default 3 retry attempts
  REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379'
};

// Redis client for caching and rate limiting
let redisClient: Redis | null = null;
try {
  // Create Redis client with retry strategy
  redisClient = new Redis(MCP_CONFIG.REDIS_URL, {
    retryStrategy: (times) => {
      // Retry with exponential backoff, but max 30 seconds
      const delay = Math.min(times * 1000, 30000);
      console.log(`Redis connection attempt ${times} failed. Retrying in ${delay}ms...`);
      return delay;
    },
    maxRetriesPerRequest: 3,
    enableOfflineQueue: false
  });

  // Handle successful connection
  redisClient.on('connect', () => {
    console.log('MCP Redis client connected successfully');
  });

  // Handle connection errors
  redisClient.on('error', (err) => {
    console.error('Redis connection error:', err);
    // Don't set redisClient to null, let the retry strategy handle reconnection
  });
} catch (error) {
  console.error('Failed to initialize Redis client:', error);
  redisClient = null;
}

/**
 * MCP Source Types
 */
export enum MCPSourceType {
  EMAIL = 'email',
  CALENDAR = 'calendar',
  TELEPHONY = 'telephony',
  SOCIAL = 'social',
  CRM = 'crm',
  DOCUMENT = 'document'
}

/**
 * MCP Authentication Types
 */
export enum MCPAuthType {
  OAUTH2 = 'oauth2',
  API_KEY = 'api_key',
  BASIC = 'basic',
  JWT = 'jwt',
  NONE = 'none'
}

/**
 * MCP Authentication Configuration
 */
export interface MCPAuthConfig {
  type: MCPAuthType;
  credentials: Record<string, any>;
}

/**
 * MCP Source Configuration
 */
export interface MCPSourceConfig {
  id: string;
  type: MCPSourceType;
  name: string;
  baseUrl: string;
  auth: MCPAuthConfig;
  tenantId?: string;
  options?: Record<string, any>;
}

/**
 * MCP Request Options
 */
export interface MCPRequestOptions {
  cacheKey?: string;
  cacheTTL?: number;
  bypassCache?: boolean;
  timeout?: number;
  retryAttempts?: number;
}

/**
 * MCP Response
 */
export interface MCPResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  source: string;
  cached?: boolean;
  timestamp: number;
}

/**
 * MCP Source Adapter Interface
 */
export interface MCPSourceAdapter {
  id: string;
  type: MCPSourceType;
  name: string;
  connect(): Promise<boolean>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
  request<T = any>(path: string, method: string, data?: any, options?: MCPRequestOptions): Promise<MCPResponse<T>>;
  subscribe(event: string, callback: (data: any) => void): void;
  unsubscribe(event: string, callback: (data: any) => void): void;
}

/**
 * Base MCP Source Adapter
 */
export abstract class BaseMCPSourceAdapter implements MCPSourceAdapter {
  id: string;
  type: MCPSourceType;
  name: string;
  protected config: MCPSourceConfig;
  protected client: AxiosInstance | null = null;
  protected connected: boolean = false;
  protected eventEmitter: EventEmitter = new EventEmitter();

  constructor(config: MCPSourceConfig) {
    this.id = config.id;
    this.type = config.type;
    this.name = config.name;
    this.config = config;
  }

  abstract connect(): Promise<boolean>;

  async disconnect(): Promise<void> {
    this.connected = false;
    this.client = null;
  }

  isConnected(): boolean {
    return this.connected;
  }

  async request<T = any>(
    path: string,
    method: string,
    data?: any,
    options?: MCPRequestOptions
  ): Promise<MCPResponse<T>> {
    if (!this.client || !this.connected) {
      await this.connect();
    }

    const requestOptions: MCPRequestOptions = {
      cacheKey: options?.cacheKey,
      cacheTTL: options?.cacheTTL || MCP_CONFIG.CACHE_TTL,
      bypassCache: options?.bypassCache || false,
      timeout: options?.timeout || MCP_CONFIG.TIMEOUT,
      retryAttempts: options?.retryAttempts || MCP_CONFIG.RETRY_ATTEMPTS
    };

    // Check cache if Redis is available and caching is not bypassed
    if (redisClient && requestOptions.cacheKey && !requestOptions.bypassCache) {
      try {
        const cachedResponse = await redisClient.get(`mcp:cache:${requestOptions.cacheKey}`);
        if (cachedResponse) {
          try {
            const parsed = JSON.parse(cachedResponse);
            return {
              ...parsed,
              cached: true,
              timestamp: Date.now()
            };
          } catch (error) {
            // Cache parsing error, continue with request
            console.warn('Failed to parse cached response:', error);
          }
        }
      } catch (error) {
        // Redis error, continue with request
        console.warn('Redis cache retrieval error:', error);
      }
    }

    try {
      const axiosConfig: AxiosRequestConfig = {
        method,
        url: path,
        data: method !== 'GET' ? data : undefined,
        params: method === 'GET' ? data : undefined,
        timeout: requestOptions.timeout
      };

      let attempts = 0;
      let lastError: any = null;

      while (attempts < (requestOptions.retryAttempts || 1)) {
        try {
          const response = await this.client!.request(axiosConfig);

          const result: MCPResponse<T> = {
            success: true,
            data: response.data,
            source: this.id,
            timestamp: Date.now()
          };

          // Cache successful response if Redis is available and cacheKey is provided
          if (redisClient && requestOptions.cacheKey) {
            try {
              await redisClient.set(
                `mcp:cache:${requestOptions.cacheKey}`,
                JSON.stringify(result),
                'EX',
                requestOptions.cacheTTL
              );
            } catch (error) {
              // Redis caching error, continue without caching
              console.warn('Redis caching error:', error);
            }
          }

          return result;
        } catch (error: any) {
          lastError = error;
          attempts++;

          // Only retry on network errors or 5xx responses
          if (
            !error.response ||
            (error.response && error.response.status >= 500)
          ) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
            continue;
          }

          // Don't retry on 4xx errors
          break;
        }
      }

      return {
        success: false,
        error: lastError?.message || 'Unknown error',
        source: this.id,
        timestamp: Date.now()
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Unknown error',
        source: this.id,
        timestamp: Date.now()
      };
    }
  }

  subscribe(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  unsubscribe(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  protected generateCacheKey(path: string, method: string, data?: any): string {
    const payload = JSON.stringify({ path, method, data });
    return createHash('md5').update(payload).digest('hex');
  }
}

/**
 * MCP Server - Main class for managing source adapters
 */
export class MCPServer {
  private static instance: MCPServer;
  private adapters: Map<string, MCPSourceAdapter> = new Map();
  private eventEmitter: EventEmitter = new EventEmitter();

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get the MCPServer instance (singleton)
   */
  public static getInstance(): MCPServer {
    if (!MCPServer.instance) {
      MCPServer.instance = new MCPServer();
    }
    return MCPServer.instance;
  }

  /**
   * Register a source adapter
   */
  public registerAdapter(adapter: MCPSourceAdapter): void {
    this.adapters.set(adapter.id, adapter);
    this.eventEmitter.emit('adapter:registered', adapter.id);
  }

  /**
   * Get a source adapter by ID
   */
  public getAdapter(id: string): MCPSourceAdapter | undefined {
    return this.adapters.get(id);
  }

  /**
   * Get all source adapters
   */
  public getAllAdapters(): MCPSourceAdapter[] {
    return Array.from(this.adapters.values());
  }

  /**
   * Get adapters by type
   */
  public getAdaptersByType(type: MCPSourceType): MCPSourceAdapter[] {
    return Array.from(this.adapters.values()).filter(adapter => adapter.type === type);
  }

  /**
   * Remove a source adapter
   */
  public removeAdapter(id: string): boolean {
    const adapter = this.adapters.get(id);
    if (adapter) {
      adapter.disconnect();
      this.adapters.delete(id);
      this.eventEmitter.emit('adapter:removed', id);
      return true;
    }
    return false;
  }

  /**
   * Subscribe to MCP server events
   */
  public subscribe(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * Unsubscribe from MCP server events
   */
  public unsubscribe(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }
}

// Export the singleton instance
export const mcpServer = MCPServer.getInstance();
