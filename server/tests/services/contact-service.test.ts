/**
 * Tests for the ContactService
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { ContactService } from '../../services/contact-service';
import { contactRepository } from '../../dal/contact-repository';
import { companyRepository } from '../../dal/company-repository';
import { DocumentNotFoundError } from '../../utils/mongodb-errors';

// Mock the repositories
jest.mock('../../dal/contact-repository', () => ({
  contactRepository: {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByIdOrThrow: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    search: jest.fn(),
    findByCompanyId: jest.fn(),
    findByStatus: jest.fn(),
    findByOwner: jest.fn(),
    findByTag: jest.fn()
  }
}));

jest.mock('../../dal/company-repository', () => ({
  companyRepository: {
    findById: jest.fn()
  }
}));

// Mock the logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('ContactService', () => {
  let service: ContactService;
  let mongoServer: MongoMemoryServer;
  const tenantId = 'tenant1';

  beforeAll(async () => {
    // Set up MongoDB Memory Server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    service = new ContactService();
  });

  describe('getAllContacts', () => {
    it('should call findAll with correct parameters', async () => {
      const mockContacts = [{ id: '1', firstName: 'John', lastName: 'Doe' }];
      (contactRepository.findAll as jest.Mock).mockResolvedValue(mockContacts);

      const options = { skip: 0, limit: 10, sort: { lastName: 1 } };
      const result = await service.getAllContacts(tenantId, options);

      expect(contactRepository.findAll).toHaveBeenCalledWith({}, tenantId, options);
      expect(result).toEqual(mockContacts);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.findAll as jest.Mock).mockRejectedValue(error);

      await expect(service.getAllContacts(tenantId)).rejects.toThrow(error);
    });
  });

  describe('getContactById', () => {
    it('should call findById with correct parameters', async () => {
      const mockContact = { id: '1', firstName: 'John', lastName: 'Doe' };
      (contactRepository.findById as jest.Mock).mockResolvedValue(mockContact);

      const result = await service.getContactById('1', tenantId);

      expect(contactRepository.findById).toHaveBeenCalledWith('1', tenantId);
      expect(result).toEqual(mockContact);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.findById as jest.Mock).mockRejectedValue(error);

      await expect(service.getContactById('1', tenantId)).rejects.toThrow(error);
    });
  });

  describe('getContactByIdOrThrow', () => {
    it('should call findByIdOrThrow with correct parameters', async () => {
      const mockContact = { id: '1', firstName: 'John', lastName: 'Doe' };
      (contactRepository.findByIdOrThrow as jest.Mock).mockResolvedValue(mockContact);

      const result = await service.getContactByIdOrThrow('1', tenantId);

      expect(contactRepository.findByIdOrThrow).toHaveBeenCalledWith('1', tenantId);
      expect(result).toEqual(mockContact);
    });

    it('should handle errors', async () => {
      const error = new DocumentNotFoundError('Contact', '1');
      (contactRepository.findByIdOrThrow as jest.Mock).mockRejectedValue(error);

      await expect(service.getContactByIdOrThrow('1', tenantId)).rejects.toThrow(error);
    });
  });

  describe('createContact', () => {
    it('should create a contact with default values', async () => {
      const mockContact = {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'new',
        tags: []
      };
      (contactRepository.create as jest.Mock).mockResolvedValue(mockContact);

      const data = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      };

      const result = await service.createContact(data, tenantId);

      expect(contactRepository.create).toHaveBeenCalledWith({
        ...data,
        status: 'new',
        tags: [],
        tenantId,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(result).toEqual(mockContact);
    });

    it('should validate company ID if provided', async () => {
      const mockCompany = { id: 'company1', name: 'Acme Inc.' };
      (companyRepository.findById as jest.Mock).mockResolvedValue(mockCompany);

      const mockContact = {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        companyId: 'company1'
      };
      (contactRepository.create as jest.Mock).mockResolvedValue(mockContact);

      const data = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        companyId: 'company1'
      };

      const result = await service.createContact(data, tenantId);

      expect(companyRepository.findById).toHaveBeenCalledWith('company1', tenantId);
      expect(contactRepository.create).toHaveBeenCalled();
      expect(result).toEqual(mockContact);
    });

    it('should throw an error if company is not found', async () => {
      (companyRepository.findById as jest.Mock).mockResolvedValue(null);

      const data = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        companyId: 'company1'
      };

      await expect(service.createContact(data, tenantId)).rejects.toThrow(DocumentNotFoundError);
      expect(contactRepository.create).not.toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.create as jest.Mock).mockRejectedValue(error);

      const data = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      };

      await expect(service.createContact(data, tenantId)).rejects.toThrow(error);
    });
  });

  describe('updateContact', () => {
    it('should update a contact', async () => {
      const mockContact = {
        id: '1',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>'
      };
      (contactRepository.update as jest.Mock).mockResolvedValue(mockContact);

      const data = {
        lastName: 'Smith'
      };

      const result = await service.updateContact('1', data, tenantId);

      expect(contactRepository.update).toHaveBeenCalledWith('1', {
        ...data,
        updatedAt: expect.any(Date)
      }, tenantId);
      expect(result).toEqual(mockContact);
    });

    it('should validate company ID if provided', async () => {
      const mockCompany = { id: 'company1', name: 'Acme Inc.' };
      (companyRepository.findById as jest.Mock).mockResolvedValue(mockCompany);

      const mockContact = {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        companyId: 'company1'
      };
      (contactRepository.update as jest.Mock).mockResolvedValue(mockContact);

      const data = {
        companyId: 'company1'
      };

      const result = await service.updateContact('1', data, tenantId);

      expect(companyRepository.findById).toHaveBeenCalledWith('company1', tenantId);
      expect(contactRepository.update).toHaveBeenCalled();
      expect(result).toEqual(mockContact);
    });

    it('should throw an error if company is not found', async () => {
      (companyRepository.findById as jest.Mock).mockResolvedValue(null);

      const data = {
        companyId: 'company1'
      };

      await expect(service.updateContact('1', data, tenantId)).rejects.toThrow(DocumentNotFoundError);
      expect(contactRepository.update).not.toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.update as jest.Mock).mockRejectedValue(error);

      const data = {
        lastName: 'Smith'
      };

      await expect(service.updateContact('1', data, tenantId)).rejects.toThrow(error);
    });
  });

  describe('deleteContact', () => {
    it('should call delete with correct parameters', async () => {
      (contactRepository.delete as jest.Mock).mockResolvedValue(true);

      const result = await service.deleteContact('1', tenantId);

      expect(contactRepository.delete).toHaveBeenCalledWith('1', tenantId);
      expect(result).toBe(true);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.delete as jest.Mock).mockRejectedValue(error);

      await expect(service.deleteContact('1', tenantId)).rejects.toThrow(error);
    });
  });

  describe('searchContacts', () => {
    it('should call search with correct parameters', async () => {
      const mockContacts = [
        { id: '1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
        { id: '2', firstName: 'Jane', lastName: 'Doe', email: '<EMAIL>' }
      ];
      (contactRepository.search as jest.Mock).mockResolvedValue(mockContacts);

      const result = await service.searchContacts('Doe', tenantId);

      expect(contactRepository.search).toHaveBeenCalledWith('Doe', tenantId);
      expect(result).toEqual(mockContacts);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.search as jest.Mock).mockRejectedValue(error);

      await expect(service.searchContacts('Doe', tenantId)).rejects.toThrow(error);
    });
  });

  describe('getContactsByCompanyId', () => {
    it('should call findByCompanyId with correct parameters', async () => {
      const mockContacts = [
        { id: '1', firstName: 'John', lastName: 'Doe', companyId: 'company1' },
        { id: '2', firstName: 'Jane', lastName: 'Doe', companyId: 'company1' }
      ];
      (contactRepository.findByCompanyId as jest.Mock).mockResolvedValue(mockContacts);

      const result = await service.getContactsByCompanyId('company1', tenantId);

      expect(contactRepository.findByCompanyId).toHaveBeenCalledWith('company1', tenantId);
      expect(result).toEqual(mockContacts);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.findByCompanyId as jest.Mock).mockRejectedValue(error);

      await expect(service.getContactsByCompanyId('company1', tenantId)).rejects.toThrow(error);
    });
  });

  describe('getContactsByStatus', () => {
    it('should call findByStatus with correct parameters', async () => {
      const mockContacts = [
        { id: '1', firstName: 'John', lastName: 'Doe', status: 'active' },
        { id: '2', firstName: 'Jane', lastName: 'Doe', status: 'active' }
      ];
      (contactRepository.findByStatus as jest.Mock).mockResolvedValue(mockContacts);

      const result = await service.getContactsByStatus('active', tenantId);

      expect(contactRepository.findByStatus).toHaveBeenCalledWith('active', tenantId);
      expect(result).toEqual(mockContacts);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.findByStatus as jest.Mock).mockRejectedValue(error);

      await expect(service.getContactsByStatus('active', tenantId)).rejects.toThrow(error);
    });
  });

  describe('getContactsByOwner', () => {
    it('should call findByOwner with correct parameters', async () => {
      const mockContacts = [
        { id: '1', firstName: 'John', lastName: 'Doe', owner: 'user1' },
        { id: '2', firstName: 'Jane', lastName: 'Doe', owner: 'user1' }
      ];
      (contactRepository.findByOwner as jest.Mock).mockResolvedValue(mockContacts);

      const result = await service.getContactsByOwner('user1', tenantId);

      expect(contactRepository.findByOwner).toHaveBeenCalledWith('user1', tenantId);
      expect(result).toEqual(mockContacts);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.findByOwner as jest.Mock).mockRejectedValue(error);

      await expect(service.getContactsByOwner('user1', tenantId)).rejects.toThrow(error);
    });
  });

  describe('getContactsByTag', () => {
    it('should call findByTag with correct parameters', async () => {
      const mockContacts = [
        { id: '1', firstName: 'John', lastName: 'Doe', tags: ['vip'] },
        { id: '2', firstName: 'Jane', lastName: 'Doe', tags: ['vip'] }
      ];
      (contactRepository.findByTag as jest.Mock).mockResolvedValue(mockContacts);

      const result = await service.getContactsByTag('vip', tenantId);

      expect(contactRepository.findByTag).toHaveBeenCalledWith('vip', tenantId);
      expect(result).toEqual(mockContacts);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (contactRepository.findByTag as jest.Mock).mockRejectedValue(error);

      await expect(service.getContactsByTag('vip', tenantId)).rejects.toThrow(error);
    });
  });
}
