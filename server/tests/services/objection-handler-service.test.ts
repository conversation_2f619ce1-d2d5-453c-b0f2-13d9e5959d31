/**
 * Tests for the ObjectionHandlerService
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { ObjectionHandlerService } from '../../services/objection-handler-service-new';
import { objectionRepository, objectionResponseRepository } from '../../dal';

// Mock the repositories
jest.mock('../../dal', () => ({
  objectionRepository: {
    findByTextSearch: jest.fn(),
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn()
  },
  objectionResponseRepository: {
    findByObjectionId: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    recordUsage: jest.fn()
  }
}));

// Mock the logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('ObjectionHandlerService', () => {
  let service: ObjectionHandlerService;
  let mongoServer: MongoMemoryServer;

  beforeAll(async () => {
    // Set up MongoDB Memory Server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    service = new ObjectionHandlerService();
  });

  describe('getObjectionCategories', () => {
    it('should return a list of objection categories', async () => {
      const categories = await service.getObjectionCategories();
      expect(categories).toEqual([
        'price',
        'product',
        'competition',
        'timing',
        'authority',
        'need',
        'trust',
        'other'
      ]);
    });
  });

  describe('getAllObjections', () => {
    it('should call findByTextSearch when search is provided', async () => {
      const mockObjections = [{ id: '1', name: 'Test Objection' }];
      (objectionRepository.findByTextSearch as jest.Mock).mockResolvedValue(mockObjections);

      const result = await service.getAllObjections({ search: 'test' });

      expect(objectionRepository.findByTextSearch).toHaveBeenCalledWith('test');
      expect(result).toEqual(mockObjections);
    });

    it('should call findAll with correct filters', async () => {
      const mockObjections = [{ id: '1', name: 'Test Objection' }];
      (objectionRepository.findAll as jest.Mock).mockResolvedValue(mockObjections);

      const result = await service.getAllObjections({
        category: 'price',
        isCommon: true,
        createdBy: '123'
      });

      expect(objectionRepository.findAll).toHaveBeenCalledWith(
        { category: 'price', isCommon: true, createdBy: '123' },
        undefined,
        { sort: { name: 1 } }
      );
      expect(result).toEqual(mockObjections);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionRepository.findAll as jest.Mock).mockRejectedValue(error);

      await expect(service.getAllObjections()).rejects.toThrow(error);
    });
  });

  describe('getObjectionById', () => {
    it('should call findById with correct ID', async () => {
      const mockObjection = { id: '1', name: 'Test Objection' };
      (objectionRepository.findById as jest.Mock).mockResolvedValue(mockObjection);

      const result = await service.getObjectionById('1');

      expect(objectionRepository.findById).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockObjection);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionRepository.findById as jest.Mock).mockRejectedValue(error);

      await expect(service.getObjectionById('1')).rejects.toThrow(error);
    });
  });

  describe('createObjection', () => {
    it('should call create with correct data', async () => {
      const mockObjection = { id: '1', name: 'Test Objection' };
      (objectionRepository.create as jest.Mock).mockResolvedValue(mockObjection);

      const data = {
        name: 'Test Objection',
        category: 'price',
        description: 'Test description'
      };

      const result = await service.createObjection(data, '123');

      expect(objectionRepository.create).toHaveBeenCalledWith({
        ...data,
        createdBy: '123',
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(result).toEqual(mockObjection);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionRepository.create as jest.Mock).mockRejectedValue(error);

      const data = {
        name: 'Test Objection',
        category: 'price',
        description: 'Test description'
      };

      await expect(service.createObjection(data, '123')).rejects.toThrow(error);
    });
  });

  describe('updateObjection', () => {
    it('should call update with correct data', async () => {
      const mockObjection = { id: '1', name: 'Updated Objection' };
      (objectionRepository.update as jest.Mock).mockResolvedValue(mockObjection);

      const data = {
        name: 'Updated Objection',
        description: 'Updated description'
      };

      const result = await service.updateObjection('1', data);

      expect(objectionRepository.update).toHaveBeenCalledWith('1', {
        ...data,
        updatedAt: expect.any(Date)
      });
      expect(result).toEqual(mockObjection);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionRepository.update as jest.Mock).mockRejectedValue(error);

      const data = {
        name: 'Updated Objection'
      };

      await expect(service.updateObjection('1', data)).rejects.toThrow(error);
    });
  });

  describe('deleteObjection', () => {
    it('should delete responses and then the objection', async () => {
      const mockResponses = [{ id: '101' }, { id: '102' }];
      (objectionResponseRepository.findByObjectionId as jest.Mock).mockResolvedValue(mockResponses);
      (objectionResponseRepository.delete as jest.Mock).mockResolvedValue(true);
      (objectionRepository.delete as jest.Mock).mockResolvedValue(true);

      const result = await service.deleteObjection('1');

      expect(objectionResponseRepository.findByObjectionId).toHaveBeenCalledWith('1');
      expect(objectionResponseRepository.delete).toHaveBeenCalledTimes(2);
      expect(objectionResponseRepository.delete).toHaveBeenCalledWith('101');
      expect(objectionResponseRepository.delete).toHaveBeenCalledWith('102');
      expect(objectionRepository.delete).toHaveBeenCalledWith('1');
      expect(result).toBe(true);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionResponseRepository.findByObjectionId as jest.Mock).mockRejectedValue(error);

      await expect(service.deleteObjection('1')).rejects.toThrow(error);
    });
  });

  describe('getResponsesForObjection', () => {
    it('should call findAll with correct filters', async () => {
      const mockResponses = [{ id: '1', response: 'Test Response' }];
      (objectionResponseRepository.findAll as jest.Mock).mockResolvedValue(mockResponses);

      const result = await service.getResponsesForObjection('1', {
        effectiveness: 4,
        isAIGenerated: true,
        createdBy: '123',
        opportunityId: '456',
        contactId: '789',
        companyId: '012'
      });

      expect(objectionResponseRepository.findAll).toHaveBeenCalledWith(
        {
          objectionId: '1',
          effectiveness: { $gte: 4 },
          isAIGenerated: true,
          createdBy: '123',
          opportunityId: '456',
          contactId: '789',
          companyId: '012'
        },
        undefined,
        { sort: { effectiveness: -1, usedCount: -1 } }
      );
      expect(result).toEqual(mockResponses);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionResponseRepository.findAll as jest.Mock).mockRejectedValue(error);

      await expect(service.getResponsesForObjection('1')).rejects.toThrow(error);
    });
  });

  describe('createResponse', () => {
    it('should call create with correct data', async () => {
      const mockResponse = { id: '1', response: 'Test Response' };
      (objectionResponseRepository.create as jest.Mock).mockResolvedValue(mockResponse);

      const data = {
        objectionId: '1',
        response: 'Test Response',
        context: 'Test Context',
        opportunityId: '456'
      };

      const result = await service.createResponse(data, '123');

      expect(objectionResponseRepository.create).toHaveBeenCalledWith({
        ...data,
        effectiveness: 3,
        usedCount: 0,
        successCount: 0,
        isAIGenerated: false,
        createdBy: '123',
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionResponseRepository.create as jest.Mock).mockRejectedValue(error);

      const data = {
        objectionId: '1',
        response: 'Test Response',
        context: 'Test Context'
      };

      await expect(service.createResponse(data, '123')).rejects.toThrow(error);
    });
  });

  describe('updateResponse', () => {
    it('should call update with correct data', async () => {
      const mockResponse = { id: '1', response: 'Updated Response' };
      (objectionResponseRepository.update as jest.Mock).mockResolvedValue(mockResponse);

      const data = {
        response: 'Updated Response',
        effectiveness: 5
      };

      const result = await service.updateResponse('1', data);

      expect(objectionResponseRepository.update).toHaveBeenCalledWith('1', {
        ...data,
        updatedAt: expect.any(Date)
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionResponseRepository.update as jest.Mock).mockRejectedValue(error);

      const data = {
        response: 'Updated Response'
      };

      await expect(service.updateResponse('1', data)).rejects.toThrow(error);
    });
  });

  describe('deleteResponse', () => {
    it('should call delete with correct ID', async () => {
      (objectionResponseRepository.delete as jest.Mock).mockResolvedValue(true);

      const result = await service.deleteResponse('1');

      expect(objectionResponseRepository.delete).toHaveBeenCalledWith('1');
      expect(result).toBe(true);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionResponseRepository.delete as jest.Mock).mockRejectedValue(error);

      await expect(service.deleteResponse('1')).rejects.toThrow(error);
    });
  });

  describe('recordResponseUsage', () => {
    it('should call recordUsage with correct parameters', async () => {
      const mockResponse = { id: '1', usedCount: 2, successCount: 1 };
      (objectionResponseRepository.recordUsage as jest.Mock).mockResolvedValue(mockResponse);

      const result = await service.recordResponseUsage('1', true);

      expect(objectionResponseRepository.recordUsage).toHaveBeenCalledWith('1', true);
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (objectionResponseRepository.recordUsage as jest.Mock).mockRejectedValue(error);

      await expect(service.recordResponseUsage('1')).rejects.toThrow(error);
    });
  });
}
