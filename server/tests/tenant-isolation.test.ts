import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { 
  EventsRaw, 
  CBICache,
  AnalyticsDataset,
  AttributionResults
} from '../models/mongoose';
import { AnalyticsDataService } from '../services/analytics-data-service';
import { CBIService } from '../services/cbi-service';
import { AttributionService } from '../services/attribution-service';
import { BigQueryService } from '../services/bigquery-service';

// Mock the BigQuery service
vi.mock('../services/bigquery-service', () => ({
  BigQueryService: {
    executeQuery: vi.fn(),
    insertRows: vi.fn(),
    createTable: vi.fn(),
    ensureTenantDataset: vi.fn(),
  }
}));

// Mock the subscription client
vi.mock('../services/subscription-client', () => ({
  subscriptionClient: {
    recordUsage: vi.fn().mockResolvedValue(true),
  }
}));

describe('Tenant Isolation Tests', () => {
  let mongoServer: MongoMemoryServer;
  
  // Test tenant IDs
  const tenant1Id = new mongoose.Types.ObjectId();
  const tenant2Id = new mongoose.Types.ObjectId();
  
  // Setup MongoDB memory server
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri);
  });
  
  // Cleanup after tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  // Clear collections before each test
  beforeEach(async () => {
    await EventsRaw.deleteMany({});
    await CBICache.deleteMany({});
    await AnalyticsDataset.deleteMany({});
    await AttributionResults.deleteMany({});
    
    // Reset mocks
    vi.clearAllMocks();
  });
  
  describe('AnalyticsDataService', () => {
    it('should enforce tenant isolation when tracking events', async () => {
      // Create events for two different tenants
      await AnalyticsDataService.trackEvent({
        tenant_id: tenant1Id.toString(),
        visitor_id: 'visitor1',
        channel: 'web',
        event_type: 'page_view',
        meta_json: {}
      });
      
      await AnalyticsDataService.trackEvent({
        tenant_id: tenant2Id.toString(),
        visitor_id: 'visitor2',
        channel: 'email',
        event_type: 'email_open',
        meta_json: {}
      });
      
      // Get events for tenant1
      const tenant1Events = await AnalyticsDataService.getEvents({
        tenant_id: tenant1Id.toString()
      });
      
      // Get events for tenant2
      const tenant2Events = await AnalyticsDataService.getEvents({
        tenant_id: tenant2Id.toString()
      });
      
      // Verify tenant isolation
      expect(tenant1Events.length).toBe(1);
      expect(tenant1Events[0].visitor_id).toBe('visitor1');
      expect(tenant1Events[0].channel).toBe('web');
      
      expect(tenant2Events.length).toBe(1);
      expect(tenant2Events[0].visitor_id).toBe('visitor2');
      expect(tenant2Events[0].channel).toBe('email');
    });
    
    it('should enforce tenant isolation when sending events to BigQuery', async () => {
      // Track an event
      await AnalyticsDataService.trackEvent({
        tenant_id: tenant1Id.toString(),
        visitor_id: 'visitor1',
        channel: 'web',
        event_type: 'page_view',
        meta_json: {}
      });
      
      // Verify BigQuery service was called with correct tenant ID
      expect(BigQueryService.insertRows).toHaveBeenCalledWith(
        tenant1Id.toString(),
        'events',
        expect.arrayContaining([
          expect.objectContaining({
            visitor_id: 'visitor1',
            channel: 'web',
            event_type: 'page_view'
          })
        ])
      );
    });
  });
  
  describe('CBIService', () => {
    beforeEach(async () => {
      // Create test datasets
      await AnalyticsDataset.create({
        tenant_id: tenant1Id,
        name: 'test_dataset',
        display_name: 'Test Dataset',
        description: 'Test dataset for tenant 1',
        source_type: 'bigquery',
        source_config: {},
        fields: [
          { name: 'field1', display_name: 'Field 1', data_type: 'string', description: 'Test field', is_dimension: true, is_metric: false }
        ],
        is_active: true,
        refresh_frequency: 'daily'
      });
      
      await AnalyticsDataset.create({
        tenant_id: tenant2Id,
        name: 'test_dataset',
        display_name: 'Test Dataset',
        description: 'Test dataset for tenant 2',
        source_type: 'bigquery',
        source_config: {},
        fields: [
          { name: 'field1', display_name: 'Field 1', data_type: 'string', description: 'Test field', is_dimension: true, is_metric: false }
        ],
        is_active: true,
        refresh_frequency: 'daily'
      });
      
      // Mock BigQuery response
      (BigQueryService.executeQuery as any).mockImplementation((tenantId, query) => {
        // Return different data based on tenant ID
        if (tenantId === tenant1Id.toString()) {
          return Promise.resolve([{ field1: 'tenant1_data' }]);
        } else {
          return Promise.resolve([{ field1: 'tenant2_data' }]);
        }
      });
    });
    
    it('should enforce tenant isolation when asking questions', async () => {
      // Create cached insights for two different tenants
      await CBICache.create({
        tenant_id: tenant1Id,
        question: 'Test question',
        question_hash: 'hash1',
        result_json: { data: 'tenant1_data' },
        chart_spec: { type: 'bar', data: [], options: {} },
        narrative: 'Tenant 1 narrative',
        dataset_ref: 'test_dataset',
        query_type: 'sql',
        execution_time_ms: 100,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      
      await CBICache.create({
        tenant_id: tenant2Id,
        question: 'Test question',
        question_hash: 'hash2',
        result_json: { data: 'tenant2_data' },
        chart_spec: { type: 'bar', data: [], options: {} },
        narrative: 'Tenant 2 narrative',
        dataset_ref: 'test_dataset',
        query_type: 'sql',
        execution_time_ms: 100,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      
      // Get recent insights for tenant1
      const tenant1Insights = await CBIService.getRecentInsights(tenant1Id.toString());
      
      // Get recent insights for tenant2
      const tenant2Insights = await CBIService.getRecentInsights(tenant2Id.toString());
      
      // Verify tenant isolation
      expect(tenant1Insights.length).toBe(1);
      expect(tenant1Insights[0].narrative).toBe('Tenant 1 narrative');
      expect(tenant1Insights[0].result_json.data).toBe('tenant1_data');
      
      expect(tenant2Insights.length).toBe(1);
      expect(tenant2Insights[0].narrative).toBe('Tenant 2 narrative');
      expect(tenant2Insights[0].result_json.data).toBe('tenant2_data');
    });
  });
  
  describe('AttributionService', () => {
    it('should enforce tenant isolation for attribution results', async () => {
      // Create attribution results for two different tenants
      await AttributionResults.create({
        tenant_id: tenant1Id,
        window: 'last_30_days',
        model_type: 'markov',
        channel: 'web',
        credit_pct: 50,
        cost: 1000,
        revenue: 2000,
        roi: 2,
        conversions: 10
      });
      
      await AttributionResults.create({
        tenant_id: tenant2Id,
        window: 'last_30_days',
        model_type: 'markov',
        channel: 'email',
        credit_pct: 30,
        cost: 500,
        revenue: 1500,
        roi: 3,
        conversions: 5
      });
      
      // Get attribution results for tenant1
      const tenant1Results = await AttributionService.getAttributionResults({
        tenant_id: tenant1Id.toString(),
        window: 'last_30_days',
        model_type: 'markov'
      });
      
      // Get attribution results for tenant2
      const tenant2Results = await AttributionService.getAttributionResults({
        tenant_id: tenant2Id.toString(),
        window: 'last_30_days',
        model_type: 'markov'
      });
      
      // Verify tenant isolation
      expect(tenant1Results.success).toBe(true);
      expect(tenant1Results.results.length).toBe(1);
      expect(tenant1Results.results[0].channel).toBe('web');
      expect(tenant1Results.results[0].credit_pct).toBe(50);
      
      expect(tenant2Results.success).toBe(true);
      expect(tenant2Results.results.length).toBe(1);
      expect(tenant2Results.results[0].channel).toBe('email');
      expect(tenant2Results.results[0].credit_pct).toBe(30);
    });
  });
  
  describe('BigQueryService', () => {
    it('should create tenant-specific dataset names', async () => {
      // Call the service
      await BigQueryService.ensureTenantDataset(tenant1Id.toString());
      
      // Verify tenant-specific dataset name
      expect(BigQueryService.ensureTenantDataset).toHaveBeenCalledWith(tenant1Id.toString());
    });
  });
});
