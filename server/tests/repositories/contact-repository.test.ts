/**
 * Contact Repository Unit Tests
 * 
 * These tests verify that the contact repository correctly implements
 * the repository pattern and provides type-safe access to contact data.
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { ContactRepository } from '../../repositories/contact-repository';
import { Contact, IContact } from '../../models/mongoose/contact-model';
import { DatabaseError } from '../../repositories/mongodb-repository';

// Mock the logger to prevent console output during tests
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('ContactRepository', () => {
  let mongoServer: MongoMemoryServer;
  let contactRepository: ContactRepository;
  let testTenantId: string;
  let testContactId: mongoose.Types.ObjectId;
  let testCompanyId: mongoose.Types.ObjectId;
  let testOwnerId: mongoose.Types.ObjectId;

  // Set up the in-memory database before all tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a new repository instance
    contactRepository = new ContactRepository();
    
    // Create test IDs
    testTenantId = 'test-tenant-id';
    testContactId = new mongoose.Types.ObjectId();
    testCompanyId = new mongoose.Types.ObjectId();
    testOwnerId = new mongoose.Types.ObjectId();
  });

  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up the database after each test
  afterEach(async () => {
    await Contact.deleteMany({});
  });

  // Helper function to create a test contact
  const createTestContact = async (overrides: Partial<IContact> = {}): Promise<IContact> => {
    const contact = new Contact({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '************',
      title: 'Test Title',
      companyId: testCompanyId,
      status: 'active',
      owner: testOwnerId,
      tags: ['test', 'example'],
      tenantId: testTenantId,
      ...overrides,
    });

    await contact.save();
    return contact.toObject();
  };

  describe('findByCompanyId', () => {
    it('should find contacts by company ID', async () => {
      // Create test contacts
      await createTestContact();
      await createTestContact({ email: '<EMAIL>' });
      await createTestContact({ companyId: new mongoose.Types.ObjectId(), email: '<EMAIL>' });

      // Find contacts by company ID
      const contacts = await contactRepository.findByCompanyId(
        testCompanyId.toString(),
        testTenantId
      );

      // Verify results
      expect(contacts).toHaveLength(2);
      expect(contacts[0].companyId.toString()).toBe(testCompanyId.toString());
      expect(contacts[1].companyId.toString()).toBe(testCompanyId.toString());
    });

    it('should throw an error for invalid company ID', async () => {
      await expect(
        contactRepository.findByCompanyId('invalid-id', testTenantId)
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('findByEmail', () => {
    it('should find a contact by email', async () => {
      // Create test contact
      const testContact = await createTestContact();

      // Find contact by email
      const contact = await contactRepository.findByEmail(
        '<EMAIL>',
        testTenantId
      );

      // Verify result
      expect(contact).not.toBeNull();
      expect(contact?.email).toBe('<EMAIL>');
    });

    it('should return null for non-existent email', async () => {
      // Find contact by non-existent email
      const contact = await contactRepository.findByEmail(
        '<EMAIL>',
        testTenantId
      );

      // Verify result
      expect(contact).toBeNull();
    });
  });

  describe('search', () => {
    it('should search contacts by name or email', async () => {
      // Create test contacts
      await createTestContact();
      await createTestContact({ firstName: 'Jane', lastName: 'Doe', email: '<EMAIL>' });
      await createTestContact({ firstName: 'Bob', lastName: 'Smith', email: '<EMAIL>' });

      // Search for contacts
      const result = await contactRepository.search('Jane', testTenantId);

      // Verify results
      expect(result.items).toHaveLength(1);
      expect(result.items[0].firstName).toBe('Jane');
      expect(result.total).toBe(1);
    });

    it('should return paginated results', async () => {
      // Create multiple test contacts
      for (let i = 0; i < 25; i++) {
        await createTestContact({ 
          firstName: `Test${i}`, 
          lastName: 'User', 
          email: `test${i}@example.com` 
        });
      }

      // Search with pagination
      const result = await contactRepository.search('Test', testTenantId, {
        page: 2,
        limit: 10,
      });

      // Verify pagination
      expect(result.items).toHaveLength(10);
      expect(result.page).toBe(2);
      expect(result.limit).toBe(10);
      expect(result.total).toBe(25);
      expect(result.totalPages).toBe(3);
      expect(result.hasPrevPage).toBe(true);
      expect(result.hasNextPage).toBe(true);
      expect(result.prevPage).toBe(1);
      expect(result.nextPage).toBe(3);
    });
  });

  // Add more tests for other methods as needed
});
