/**
 * Company Repository Unit Tests
 * 
 * These tests verify that the company repository correctly implements
 * the repository pattern and provides type-safe access to company data.
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { CompanyRepository } from '../../repositories/company-repository';
import { Company, ICompany } from '../../models/mongoose/company-model';
import { DatabaseError } from '../../repositories/mongodb-repository';

// Mock the logger to prevent console output during tests
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('CompanyRepository', () => {
  let mongoServer: MongoMemoryServer;
  let companyRepository: CompanyRepository;
  let testTenantId: string;
  let testCompanyId: mongoose.Types.ObjectId;
  let testOwnerId: mongoose.Types.ObjectId;

  // Set up the in-memory database before all tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a new repository instance
    companyRepository = new CompanyRepository();
    
    // Create test IDs
    testTenantId = 'test-tenant-id';
    testCompanyId = new mongoose.Types.ObjectId();
    testOwnerId = new mongoose.Types.ObjectId();
  });

  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up the database after each test
  afterEach(async () => {
    await Company.deleteMany({});
  });

  // Helper function to create a test company
  const createTestCompany = async (overrides: Partial<ICompany> = {}): Promise<ICompany> => {
    const company = new Company({
      name: 'Test Company',
      domain: 'testcompany.com',
      industry: 'Technology',
      size: '11-50',
      status: 'active',
      owner: testOwnerId,
      tags: ['test', 'technology'],
      tenantId: testTenantId,
      ...overrides,
    });

    await company.save();
    return company.toObject();
  };

  describe('findByDomain', () => {
    it('should find companies by domain', async () => {
      // Create test companies
      await createTestCompany();
      await createTestCompany({ domain: 'another-test.com', name: 'Another Test Company' });
      await createTestCompany({ domain: 'different.com', name: 'Different Company' });

      // Find companies by domain
      const companies = await companyRepository.findByDomain(
        'testcompany.com',
        testTenantId
      );

      // Verify results
      expect(companies).toHaveLength(1);
      expect(companies[0].domain).toBe('testcompany.com');
      expect(companies[0].name).toBe('Test Company');
    });

    it('should normalize domain when searching', async () => {
      // Create test company
      await createTestCompany();

      // Find company with different domain formats
      const companies1 = await companyRepository.findByDomain(
        'https://testcompany.com',
        testTenantId
      );
      const companies2 = await companyRepository.findByDomain(
        'www.testcompany.com',
        testTenantId
      );
      const companies3 = await companyRepository.findByDomain(
        'testcompany.com/',
        testTenantId
      );

      // Verify results
      expect(companies1).toHaveLength(1);
      expect(companies2).toHaveLength(1);
      expect(companies3).toHaveLength(1);
    });
  });

  describe('findByIndustry', () => {
    it('should find companies by industry', async () => {
      // Create test companies
      await createTestCompany();
      await createTestCompany({ industry: 'Healthcare', name: 'Healthcare Company' });
      await createTestCompany({ industry: 'Finance', name: 'Finance Company' });

      // Find companies by industry
      const companies = await companyRepository.findByIndustry(
        'Technology',
        testTenantId
      );

      // Verify results
      expect(companies).toHaveLength(1);
      expect(companies[0].industry).toBe('Technology');
      expect(companies[0].name).toBe('Test Company');
    });

    it('should perform case-insensitive search for industry', async () => {
      // Create test company
      await createTestCompany();

      // Find company with different case
      const companies = await companyRepository.findByIndustry(
        'technology',
        testTenantId
      );

      // Verify results
      expect(companies).toHaveLength(1);
      expect(companies[0].industry).toBe('Technology');
    });
  });

  describe('findByStatus', () => {
    it('should find companies by status', async () => {
      // Create test companies
      await createTestCompany();
      await createTestCompany({ status: 'inactive', name: 'Inactive Company' });
      await createTestCompany({ status: 'lead', name: 'Lead Company' });

      // Find companies by status
      const companies = await companyRepository.findByStatus(
        'active',
        testTenantId
      );

      // Verify results
      expect(companies).toHaveLength(1);
      expect(companies[0].status).toBe('active');
      expect(companies[0].name).toBe('Test Company');
    });
  });

  describe('findByTag', () => {
    it('should find companies by tag', async () => {
      // Create test companies
      await createTestCompany();
      await createTestCompany({ tags: ['enterprise', 'technology'], name: 'Enterprise Company' });
      await createTestCompany({ tags: ['startup', 'finance'], name: 'Startup Company' });

      // Find companies by tag
      const companies = await companyRepository.findByTag(
        'test',
        testTenantId
      );

      // Verify results
      expect(companies).toHaveLength(1);
      expect(companies[0].tags).toContain('test');
      expect(companies[0].name).toBe('Test Company');
    });
  });

  describe('search', () => {
    it('should search companies by name, industry, or description', async () => {
      // Create test companies
      await createTestCompany();
      await createTestCompany({ 
        name: 'Another Company', 
        industry: 'Test Industry',
        description: 'This is a test description'
      });
      await createTestCompany({ 
        name: 'Unrelated Company', 
        industry: 'Unrelated Industry',
        description: 'Unrelated description'
      });

      // Search for companies
      const result = await companyRepository.search(
        'test',
        testTenantId
      );

      // Verify results
      expect(result.items).toHaveLength(2);
      expect(result.items.map(c => c.name)).toContain('Test Company');
      expect(result.items.map(c => c.name)).toContain('Another Company');
      expect(result.total).toBe(2);
    });
  });

  // Add more tests for other methods as needed
});
