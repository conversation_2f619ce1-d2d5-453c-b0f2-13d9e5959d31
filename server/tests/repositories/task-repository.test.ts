/**
 * Task Repository Unit Tests
 * 
 * These tests verify that the task repository correctly implements
 * the repository pattern and provides type-safe access to task data.
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { TaskRepository } from '../../repositories/task-repository';
import { Task, ITask } from '../../models/mongoose/task-model';
import { DatabaseError } from '../../repositories/mongodb-repository';

// Mock the logger to prevent console output during tests
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('TaskRepository', () => {
  let mongoServer: MongoMemoryServer;
  let taskRepository: TaskRepository;
  let testTenantId: string;
  let testTaskId: mongoose.Types.ObjectId;
  let testAssigneeId: mongoose.Types.ObjectId;
  let testAssignerId: mongoose.Types.ObjectId;
  let testContactId: mongoose.Types.ObjectId;

  // Set up the in-memory database before all tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a new repository instance
    taskRepository = new TaskRepository();
    
    // Create test IDs
    testTenantId = 'test-tenant-id';
    testTaskId = new mongoose.Types.ObjectId();
    testAssigneeId = new mongoose.Types.ObjectId();
    testAssignerId = new mongoose.Types.ObjectId();
    testContactId = new mongoose.Types.ObjectId();
  });

  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up the database after each test
  afterEach(async () => {
    await Task.deleteMany({});
  });

  // Helper function to create a test task
  const createTestTask = async (overrides: Partial<ITask> = {}): Promise<ITask> => {
    const task = new Task({
      title: 'Test Task',
      description: 'This is a test task',
      status: 'todo',
      priority: 'medium',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      assignedTo: testAssigneeId,
      assignedBy: testAssignerId,
      tags: ['test', 'important'],
      tenantId: testTenantId,
      ...overrides,
    });

    await task.save();
    return task.toObject();
  };

  describe('findByAssignee', () => {
    it('should find tasks by assignee', async () => {
      // Create test tasks
      await createTestTask();
      await createTestTask({ title: 'Another Task' });
      await createTestTask({ 
        title: 'Different Assignee Task', 
        assignedTo: new mongoose.Types.ObjectId() 
      });

      // Find tasks by assignee
      const tasks = await taskRepository.findByAssignee(
        testAssigneeId.toString(),
        testTenantId
      );

      // Verify results
      expect(tasks).toHaveLength(2);
      expect(tasks[0].assignedTo.toString()).toBe(testAssigneeId.toString());
      expect(tasks[1].assignedTo.toString()).toBe(testAssigneeId.toString());
    });

    it('should throw an error for invalid assignee ID', async () => {
      await expect(
        taskRepository.findByAssignee('invalid-id', testTenantId)
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('findByStatus', () => {
    it('should find tasks by status', async () => {
      // Create test tasks
      await createTestTask();
      await createTestTask({ status: 'in_progress', title: 'In Progress Task' });
      await createTestTask({ status: 'completed', title: 'Completed Task' });

      // Find tasks by status
      const tasks = await taskRepository.findByStatus(
        'todo',
        testTenantId
      );

      // Verify results
      expect(tasks).toHaveLength(1);
      expect(tasks[0].status).toBe('todo');
      expect(tasks[0].title).toBe('Test Task');
    });
  });

  describe('findByPriority', () => {
    it('should find tasks by priority', async () => {
      // Create test tasks
      await createTestTask();
      await createTestTask({ priority: 'high', title: 'High Priority Task' });
      await createTestTask({ priority: 'low', title: 'Low Priority Task' });

      // Find tasks by priority
      const tasks = await taskRepository.findByPriority(
        'medium',
        testTenantId
      );

      // Verify results
      expect(tasks).toHaveLength(1);
      expect(tasks[0].priority).toBe('medium');
      expect(tasks[0].title).toBe('Test Task');
    });
  });

  describe('findByDueDateRange', () => {
    it('should find tasks by due date range', async () => {
      // Create test tasks
      const now = new Date();
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      
      await createTestTask({ dueDate: tomorrow, title: 'Tomorrow Task' });
      await createTestTask({ dueDate: nextWeek, title: 'Next Week Task' });
      await createTestTask({ dueDate: nextMonth, title: 'Next Month Task' });

      // Find tasks by due date range
      const startDate = new Date(now.getTime() - 1000); // Just before now
      const endDate = new Date(nextWeek.getTime() + 1000); // Just after next week
      
      const tasks = await taskRepository.findByDueDateRange(
        startDate,
        endDate,
        testTenantId
      );

      // Verify results
      expect(tasks).toHaveLength(2);
      expect(tasks.map(t => t.title)).toContain('Tomorrow Task');
      expect(tasks.map(t => t.title)).toContain('Next Week Task');
    });

    it('should throw an error for invalid date range', async () => {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      await expect(
        taskRepository.findByDueDateRange(now, yesterday, testTenantId)
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('findByRelatedEntity', () => {
    it('should find tasks by related entity', async () => {
      // Create test tasks
      await createTestTask({
        relatedTo: {
          type: 'contact',
          id: testContactId
        },
        title: 'Contact Task'
      });
      await createTestTask({
        relatedTo: {
          type: 'opportunity',
          id: new mongoose.Types.ObjectId()
        },
        title: 'Opportunity Task'
      });

      // Find tasks by related entity
      const tasks = await taskRepository.findByRelatedEntity(
        'contact',
        testContactId.toString(),
        testTenantId
      );

      // Verify results
      expect(tasks).toHaveLength(1);
      expect(tasks[0].title).toBe('Contact Task');
      expect(tasks[0].relatedTo?.type).toBe('contact');
      expect(tasks[0].relatedTo?.id.toString()).toBe(testContactId.toString());
    });
  });

  describe('findOverdueTasks', () => {
    it('should find overdue tasks', async () => {
      // Create test tasks
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      
      await createTestTask({ dueDate: yesterday, title: 'Overdue Task' });
      await createTestTask({ dueDate: tomorrow, title: 'Future Task' });
      await createTestTask({ 
        dueDate: yesterday, 
        status: 'completed', 
        title: 'Completed Overdue Task' 
      });

      // Find overdue tasks
      const tasks = await taskRepository.findOverdueTasks(testTenantId);

      // Verify results
      expect(tasks).toHaveLength(1);
      expect(tasks[0].title).toBe('Overdue Task');
      expect(tasks[0].status).toBe('todo');
    });
  });

  describe('completeTask', () => {
    it('should complete a task', async () => {
      // Create test task
      const task = await createTestTask();

      // Complete the task
      const completedTask = await taskRepository.completeTask(
        task._id.toString(),
        testTenantId
      );

      // Verify result
      expect(completedTask).not.toBeNull();
      expect(completedTask?.status).toBe('completed');
      expect(completedTask?.completedAt).toBeDefined();
    });

    it('should return null for non-existent task', async () => {
      // Complete a non-existent task
      const nonExistentId = new mongoose.Types.ObjectId();
      const completedTask = await taskRepository.completeTask(
        nonExistentId.toString(),
        testTenantId
      );

      // Verify result
      expect(completedTask).toBeNull();
    });
  });

  // Add more tests for other methods as needed
});
