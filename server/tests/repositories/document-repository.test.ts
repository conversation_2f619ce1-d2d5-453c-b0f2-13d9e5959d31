/**
 * Document Repository Unit Tests
 * 
 * These tests verify that the document repository correctly implements
 * the repository pattern and provides type-safe access to document data.
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { DocumentRepository } from '../../repositories/document-repository';
import { DocumentModel, IDocument } from '../../models/mongoose/document-model';
import { DatabaseError } from '../../repositories/mongodb-repository';

// Mock the logger to prevent console output during tests
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('DocumentRepository', () => {
  let mongoServer: MongoMemoryServer;
  let documentRepository: DocumentRepository;
  let testTenantId: string;
  let testDocumentId: mongoose.Types.ObjectId;
  let testOwnerId: mongoose.Types.ObjectId;
  let testContactId: mongoose.Types.ObjectId;

  // Set up the in-memory database before all tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a new repository instance
    documentRepository = new DocumentRepository();
    
    // Create test IDs
    testTenantId = 'test-tenant-id';
    testDocumentId = new mongoose.Types.ObjectId();
    testOwnerId = new mongoose.Types.ObjectId();
    testContactId = new mongoose.Types.ObjectId();
  });

  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up the database after each test
  afterEach(async () => {
    await DocumentModel.deleteMany({});
  });

  // Helper function to create a test document
  const createTestDocument = async (overrides: Partial<IDocument> = {}): Promise<IDocument> => {
    const document = new DocumentModel({
      name: 'Test Document',
      description: 'This is a test document',
      fileType: 'pdf',
      mimeType: 'application/pdf',
      size: 1024,
      url: 'https://example.com/test.pdf',
      path: '/documents/test.pdf',
      isPublic: false,
      owner: testOwnerId,
      tags: ['test', 'important'],
      metadata: { pages: 10 },
      tenantId: testTenantId,
      ...overrides,
    });

    await document.save();
    return document.toObject();
  };

  describe('findByOwner', () => {
    it('should find documents by owner', async () => {
      // Create test documents
      await createTestDocument();
      await createTestDocument({ name: 'Another Document' });
      await createTestDocument({ 
        name: 'Different Owner Document', 
        owner: new mongoose.Types.ObjectId() 
      });

      // Find documents by owner
      const documents = await documentRepository.findByOwner(
        testOwnerId.toString(),
        testTenantId
      );

      // Verify results
      expect(documents).toHaveLength(2);
      expect(documents[0].owner.toString()).toBe(testOwnerId.toString());
      expect(documents[1].owner.toString()).toBe(testOwnerId.toString());
    });

    it('should throw an error for invalid owner ID', async () => {
      await expect(
        documentRepository.findByOwner('invalid-id', testTenantId)
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('findByFileType', () => {
    it('should find documents by file type', async () => {
      // Create test documents
      await createTestDocument();
      await createTestDocument({ fileType: 'image', name: 'Image Document' });
      await createTestDocument({ fileType: 'document', name: 'Word Document' });

      // Find documents by file type
      const documents = await documentRepository.findByFileType(
        'pdf',
        testTenantId
      );

      // Verify results
      expect(documents).toHaveLength(1);
      expect(documents[0].fileType).toBe('pdf');
      expect(documents[0].name).toBe('Test Document');
    });
  });

  describe('findByRelatedEntity', () => {
    it('should find documents by related entity', async () => {
      // Create test documents
      await createTestDocument({
        relatedTo: {
          type: 'contact',
          id: testContactId
        },
        name: 'Contact Document'
      });
      await createTestDocument({
        relatedTo: {
          type: 'opportunity',
          id: new mongoose.Types.ObjectId()
        },
        name: 'Opportunity Document'
      });

      // Find documents by related entity
      const documents = await documentRepository.findByRelatedEntity(
        'contact',
        testContactId.toString(),
        testTenantId
      );

      // Verify results
      expect(documents).toHaveLength(1);
      expect(documents[0].name).toBe('Contact Document');
      expect(documents[0].relatedTo?.type).toBe('contact');
      expect(documents[0].relatedTo?.id.toString()).toBe(testContactId.toString());
    });
  });

  describe('findPublicDocuments', () => {
    it('should find public documents', async () => {
      // Create test documents
      await createTestDocument();
      await createTestDocument({ isPublic: true, name: 'Public Document 1' });
      await createTestDocument({ isPublic: true, name: 'Public Document 2' });

      // Find public documents
      const documents = await documentRepository.findPublicDocuments(testTenantId);

      // Verify results
      expect(documents).toHaveLength(2);
      expect(documents[0].isPublic).toBe(true);
      expect(documents[1].isPublic).toBe(true);
    });
  });

  describe('findByTag', () => {
    it('should find documents by tag', async () => {
      // Create test documents
      await createTestDocument();
      await createTestDocument({ tags: ['contract', 'important'], name: 'Contract Document' });
      await createTestDocument({ tags: ['invoice', 'finance'], name: 'Invoice Document' });

      // Find documents by tag
      const documents = await documentRepository.findByTag(
        'important',
        testTenantId
      );

      // Verify results
      expect(documents).toHaveLength(2);
      expect(documents.map(d => d.name)).toContain('Test Document');
      expect(documents.map(d => d.name)).toContain('Contract Document');
    });
  });

  describe('setDocumentPublic', () => {
    it('should make a document public', async () => {
      // Create test document
      const document = await createTestDocument();

      // Make the document public
      const updatedDocument = await documentRepository.setDocumentPublic(
        document._id.toString(),
        true,
        testTenantId
      );

      // Verify result
      expect(updatedDocument).not.toBeNull();
      expect(updatedDocument?.isPublic).toBe(true);
    });

    it('should make a document private', async () => {
      // Create test document
      const document = await createTestDocument({ isPublic: true });

      // Make the document private
      const updatedDocument = await documentRepository.setDocumentPublic(
        document._id.toString(),
        false,
        testTenantId
      );

      // Verify result
      expect(updatedDocument).not.toBeNull();
      expect(updatedDocument?.isPublic).toBe(false);
    });

    it('should return null for non-existent document', async () => {
      // Update a non-existent document
      const nonExistentId = new mongoose.Types.ObjectId();
      const updatedDocument = await documentRepository.setDocumentPublic(
        nonExistentId.toString(),
        true,
        testTenantId
      );

      // Verify result
      expect(updatedDocument).toBeNull();
    });
  });

  describe('search', () => {
    it('should search documents by name, description, or tags', async () => {
      // Create test documents
      await createTestDocument();
      await createTestDocument({ 
        name: 'Another Document', 
        description: 'This document contains test information'
      });
      await createTestDocument({ 
        name: 'Unrelated Document', 
        description: 'This document is unrelated',
        tags: ['unrelated', 'test-tag']
      });

      // Search for documents
      const result = await documentRepository.search(
        'test',
        testTenantId
      );

      // Verify results
      expect(result.items).toHaveLength(3);
      expect(result.total).toBe(3);
    });
  });

  // Add more tests for other methods as needed
});
