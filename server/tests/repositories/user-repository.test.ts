/**
 * User Repository Unit Tests
 * 
 * These tests verify that the user repository correctly implements
 * the repository pattern and provides type-safe access to user data.
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { UserRepository } from '../../repositories/user-repository';
import { User, IUser } from '../../models/mongoose/user-model';
import { DatabaseError } from '../../repositories/mongodb-repository';

// Mock the logger to prevent console output during tests
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('UserRepository', () => {
  let mongoServer: MongoMemoryServer;
  let userRepository: UserRepository;
  let testTenantId: string;
  let testUserId: mongoose.Types.ObjectId;

  // Set up the in-memory database before all tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a new repository instance
    userRepository = new UserRepository();
    
    // Create test IDs
    testTenantId = 'test-tenant-id';
    testUserId = new mongoose.Types.ObjectId();
  });

  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up the database after each test
  afterEach(async () => {
    await User.deleteMany({});
  });

  // Helper function to create a test user
  const createTestUser = async (overrides: Partial<IUser> = {}): Promise<IUser> => {
    const user = new User({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Test User',
      role: 'user',
      tenantId: testTenantId,
      ...overrides,
    });

    await user.save();
    return user.toObject();
  };

  describe('findByEmail', () => {
    it('should find a user by email', async () => {
      // Create test user
      await createTestUser();

      // Find user by email
      const user = await userRepository.findByEmail(
        '<EMAIL>',
        testTenantId
      );

      // Verify result
      expect(user).not.toBeNull();
      expect(user?.email).toBe('<EMAIL>');
    });

    it('should return null for non-existent email', async () => {
      // Find user by non-existent email
      const user = await userRepository.findByEmail(
        '<EMAIL>',
        testTenantId
      );

      // Verify result
      expect(user).toBeNull();
    });
  });

  describe('findByUsername', () => {
    it('should find a user by username', async () => {
      // Create test user
      await createTestUser();

      // Find user by username
      const user = await userRepository.findByUsername(
        'testuser',
        testTenantId
      );

      // Verify result
      expect(user).not.toBeNull();
      expect(user?.username).toBe('testuser');
    });

    it('should return null for non-existent username', async () => {
      // Find user by non-existent username
      const user = await userRepository.findByUsername(
        'nonexistent',
        testTenantId
      );

      // Verify result
      expect(user).toBeNull();
    });
  });

  describe('findByRole', () => {
    it('should find users by role', async () => {
      // Create test users
      await createTestUser();
      await createTestUser({ username: 'admin', email: '<EMAIL>', role: 'admin' });
      await createTestUser({ username: 'superadmin', email: '<EMAIL>', role: 'superadmin' });

      // Find users by role
      const users = await userRepository.findByRole(
        'admin',
        testTenantId
      );

      // Verify results
      expect(users).toHaveLength(1);
      expect(users[0].role).toBe('admin');
      expect(users[0].username).toBe('admin');
    });
  });

  describe('search', () => {
    it('should search users by username, email, or full name', async () => {
      // Create test users
      await createTestUser();
      await createTestUser({ 
        username: 'johndoe', 
        email: '<EMAIL>', 
        fullName: 'John Doe' 
      });
      await createTestUser({ 
        username: 'janedoe', 
        email: '<EMAIL>', 
        fullName: 'Jane Doe' 
      });

      // Search for users
      const result = await userRepository.search(
        'doe',
        testTenantId
      );

      // Verify results
      expect(result.items).toHaveLength(2);
      expect(result.items.map(u => u.username)).toContain('johndoe');
      expect(result.items.map(u => u.username)).toContain('janedoe');
      expect(result.total).toBe(2);
    });
  });

  describe('updatePreferences', () => {
    it('should update user preferences', async () => {
      // Create test user
      const testUser = await createTestUser();

      // Update preferences
      const preferences = {
        theme: 'dark',
        language: 'en',
        timezone: 'America/New_York',
      };

      const updatedUser = await userRepository.updatePreferences(
        testUser._id.toString(),
        preferences,
        testTenantId
      );

      // Verify result
      expect(updatedUser).not.toBeNull();
      expect(updatedUser?.preferences).toEqual(preferences);
    });
  });

  describe('updateLastLogin', () => {
    it('should update user last login', async () => {
      // Create test user
      const testUser = await createTestUser();

      // Update last login
      const updatedUser = await userRepository.updateLastLogin(
        testUser._id.toString(),
        testTenantId
      );

      // Verify result
      expect(updatedUser).not.toBeNull();
      expect(updatedUser?.lastLogin).toBeDefined();
    });
  });

  // Add more tests for other methods as needed
});
