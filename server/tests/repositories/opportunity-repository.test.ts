/**
 * Opportunity Repository Unit Tests
 * 
 * These tests verify that the opportunity repository correctly implements
 * the repository pattern and provides type-safe access to opportunity data.
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { OpportunityRepository } from '../../repositories/opportunity-repository';
import { Opportunity, IOpportunity } from '../../models/mongoose/opportunity-model';
import { DatabaseError } from '../../repositories/mongodb-repository';

// Mock the logger to prevent console output during tests
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('OpportunityRepository', () => {
  let mongoServer: MongoMemoryServer;
  let opportunityRepository: OpportunityRepository;
  let testTenantId: string;
  let testOpportunityId: mongoose.Types.ObjectId;
  let testCompanyId: mongoose.Types.ObjectId;
  let testContactId: mongoose.Types.ObjectId;
  let testOwnerId: mongoose.Types.ObjectId;

  // Set up the in-memory database before all tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a new repository instance
    opportunityRepository = new OpportunityRepository();
    
    // Create test IDs
    testTenantId = 'test-tenant-id';
    testOpportunityId = new mongoose.Types.ObjectId();
    testCompanyId = new mongoose.Types.ObjectId();
    testContactId = new mongoose.Types.ObjectId();
    testOwnerId = new mongoose.Types.ObjectId();
  });

  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up the database after each test
  afterEach(async () => {
    await Opportunity.deleteMany({});
  });

  // Helper function to create a test opportunity
  const createTestOpportunity = async (overrides: Partial<IOpportunity> = {}): Promise<IOpportunity> => {
    const opportunity = new Opportunity({
      name: 'Test Opportunity',
      value: 10000,
      currency: 'USD',
      stage: 'proposal',
      probability: 50,
      companyId: testCompanyId,
      contactId: testContactId,
      owner: testOwnerId,
      tenantId: testTenantId,
      ...overrides,
    });

    await opportunity.save();
    return opportunity.toObject();
  };

  describe('findByCompanyId', () => {
    it('should find opportunities by company ID', async () => {
      // Create test opportunities
      await createTestOpportunity();
      await createTestOpportunity({ name: 'Test Opportunity 2' });
      await createTestOpportunity({ companyId: new mongoose.Types.ObjectId(), name: 'Other Company Opportunity' });

      // Find opportunities by company ID
      const opportunities = await opportunityRepository.findByCompanyId(
        testCompanyId.toString(),
        testTenantId
      );

      // Verify results
      expect(opportunities).toHaveLength(2);
      expect(opportunities[0].companyId.toString()).toBe(testCompanyId.toString());
      expect(opportunities[1].companyId.toString()).toBe(testCompanyId.toString());
    });

    it('should throw an error for invalid company ID', async () => {
      await expect(
        opportunityRepository.findByCompanyId('invalid-id', testTenantId)
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('findByContactId', () => {
    it('should find opportunities by contact ID', async () => {
      // Create test opportunities
      await createTestOpportunity();
      await createTestOpportunity({ name: 'Test Opportunity 2' });
      await createTestOpportunity({ contactId: new mongoose.Types.ObjectId(), name: 'Other Contact Opportunity' });

      // Find opportunities by contact ID
      const opportunities = await opportunityRepository.findByContactId(
        testContactId.toString(),
        testTenantId
      );

      // Verify results
      expect(opportunities).toHaveLength(2);
      expect(opportunities[0].contactId.toString()).toBe(testContactId.toString());
      expect(opportunities[1].contactId.toString()).toBe(testContactId.toString());
    });

    it('should throw an error for invalid contact ID', async () => {
      await expect(
        opportunityRepository.findByContactId('invalid-id', testTenantId)
      ).rejects.toThrow(DatabaseError);
    });
  });

  describe('findByStage', () => {
    it('should find opportunities by stage', async () => {
      // Create test opportunities
      await createTestOpportunity();
      await createTestOpportunity({ name: 'Test Opportunity 2' });
      await createTestOpportunity({ stage: 'negotiation', name: 'Negotiation Opportunity' });

      // Find opportunities by stage
      const opportunities = await opportunityRepository.findByStage(
        'proposal',
        testTenantId
      );

      // Verify results
      expect(opportunities).toHaveLength(2);
      expect(opportunities[0].stage).toBe('proposal');
      expect(opportunities[1].stage).toBe('proposal');
    });

    it('should return empty array for non-existent stage', async () => {
      // Create test opportunity
      await createTestOpportunity();

      // Find opportunities by non-existent stage
      const opportunities = await opportunityRepository.findByStage(
        'nonexistent',
        testTenantId
      );

      // Verify results
      expect(opportunities).toHaveLength(0);
    });
  });

  describe('findByValueRange', () => {
    it('should find opportunities by value range', async () => {
      // Create test opportunities
      await createTestOpportunity({ value: 5000 });
      await createTestOpportunity({ value: 10000 });
      await createTestOpportunity({ value: 15000 });
      await createTestOpportunity({ value: 20000 });

      // Find opportunities by value range
      const opportunities = await opportunityRepository.findByValueRange(
        7500,
        17500,
        testTenantId
      );

      // Verify results
      expect(opportunities).toHaveLength(2);
      expect(opportunities[0].value).toBe(10000);
      expect(opportunities[1].value).toBe(15000);
    });

    it('should throw an error for invalid value range', async () => {
      await expect(
        opportunityRepository.findByValueRange(10000, 5000, testTenantId)
      ).rejects.toThrow(DatabaseError);
    });
  });

  // Add more tests for other methods as needed
});
