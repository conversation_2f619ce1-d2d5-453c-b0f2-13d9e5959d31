/**
 * Tests for the ObjectionRepository
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { ObjectionRepository, ObjectionEntity } from '../../dal/objection-repository';
import { Objection } from '../../models/mongoose';

// Mock the MongoDB models
jest.mock('../../models/mongoose', () => ({
  Objection: {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    countDocuments: jest.fn(),
    create: jest.fn(),
    deleteOne: jest.fn()
  }
}));

// Mock the logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('ObjectionRepository', () => {
  let repository: ObjectionRepository;
  let mongoServer: MongoMemoryServer;

  beforeAll(async () => {
    // Set up MongoDB Memory Server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    repository = new ObjectionRepository();
  });

  describe('toEntity', () => {
    it('should convert a document to an entity', () => {
      const doc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        tags: ['tag1', 'tag2'],
        isCommon: true,
        createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        customFields: { field1: 'value1' },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02')
      };

      const entity = repository['toEntity'](doc as any);

      expect(entity).toEqual({
        id: '5f7d7e8c8f8f8f8f8f8f8f8f',
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        tags: ['tag1', 'tag2'],
        isCommon: true,
        createdBy: '5f7d7e8c8f8f8f8f8f8f8f8e',
        customFields: { field1: 'value1' },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02')
      });
    });

    it('should return null if document is null', () => {
      const entity = repository['toEntity'](null);
      expect(entity).toBeNull();
    });
  });

  describe('toDocument', () => {
    it('should convert an entity to a document', () => {
      const entity: Partial<ObjectionEntity> = {
        id: '5f7d7e8c8f8f8f8f8f8f8f8f',
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        tags: ['tag1', 'tag2'],
        isCommon: true,
        createdBy: '5f7d7e8c8f8f8f8f8f8f8f8e',
        customFields: { field1: 'value1' }
      };

      const doc = repository['toDocument'](entity);

      expect(doc).toEqual({
        _id: expect.any(mongoose.Types.ObjectId),
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        tags: ['tag1', 'tag2'],
        isCommon: true,
        createdBy: expect.any(mongoose.Types.ObjectId),
        customFields: { field1: 'value1' }
      });
      expect(doc._id.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(doc.createdBy.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8e');
    });

    it('should handle entity without ID', () => {
      const entity: Partial<ObjectionEntity> = {
        name: 'Test Objection',
        category: 'price',
        description: 'Test description'
      };

      const doc = repository['toDocument'](entity);

      expect(doc).toEqual({
        name: 'Test Objection',
        category: 'price',
        description: 'Test description'
      });
    });
  });

  describe('findById', () => {
    it('should find an objection by ID', async () => {
      const mockDoc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (Objection.findOne as jest.Mock).mockResolvedValue(mockDoc);

      const result = await repository.findById('5f7d7e8c8f8f8f8f8f8f8f8f');

      expect(Objection.findOne).toHaveBeenCalledWith({
        _id: expect.any(mongoose.Types.ObjectId)
      });
      expect(result).not.toBeNull();
      expect(result?.id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(result?.name).toBe('Test Objection');
    });

    it('should add tenant filter when tenantId is provided', async () => {
      (Objection.findOne as jest.Mock).mockResolvedValue(null);

      await repository.findById('5f7d7e8c8f8f8f8f8f8f8f8f', 'tenant1');

      expect(Objection.findOne).toHaveBeenCalledWith({
        _id: expect.any(mongoose.Types.ObjectId),
        tenantId: 'tenant1'
      });
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (Objection.findOne as jest.Mock).mockRejectedValue(error);

      await expect(repository.findById('5f7d7e8c8f8f8f8f8f8f8f8f')).rejects.toThrow(error);
    });
  });

  describe('findAll', () => {
    it('should find all objections with filters', async () => {
      const mockDocs = [
        {
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
          name: 'Test Objection 1',
          category: 'price',
          description: 'Test description 1',
          createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          name: 'Test Objection 2',
          category: 'product',
          description: 'Test description 2',
          createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockQuery = {
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs)
      };

      (Objection.find as jest.Mock).mockReturnValue(mockQuery);

      const result = await repository.findAll(
        { category: 'price' },
        'tenant1',
        { skip: 0, limit: 10, sort: { name: 1 } }
      );

      expect(Objection.find).toHaveBeenCalledWith({ category: 'price', tenantId: 'tenant1' });
      expect(mockQuery.skip).toHaveBeenCalledWith(0);
      expect(mockQuery.limit).toHaveBeenCalledWith(10);
      expect(mockQuery.sort).toHaveBeenCalledWith({ name: 1 });
      expect(result.length).toBe(2);
      expect(result[0].id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(result[1].id).toBe('5f7d7e8c8f8f8f8f8f8f8f8e');
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      const mockQuery = {
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockRejectedValue(error)
      };

      (Objection.find as jest.Mock).mockReturnValue(mockQuery);

      await expect(repository.findAll()).rejects.toThrow(error);
    });
  });

  describe('count', () => {
    it('should count objections with filters', async () => {
      (Objection.countDocuments as jest.Mock).mockResolvedValue(5);

      const result = await repository.count({ category: 'price' }, 'tenant1');

      expect(Objection.countDocuments).toHaveBeenCalledWith({ category: 'price', tenantId: 'tenant1' });
      expect(result).toBe(5);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (Objection.countDocuments as jest.Mock).mockRejectedValue(error);

      await expect(repository.count()).rejects.toThrow(error);
    });
  });

  describe('create', () => {
    it('should create a new objection', async () => {
      const mockDoc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValue({
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
          name: 'Test Objection',
          category: 'price',
          description: 'Test description',
          createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          createdAt: new Date(),
          updatedAt: new Date()
        })
      };

      (Objection as any).mockImplementation(() => mockDoc);

      const data: Partial<ObjectionEntity> = {
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        createdBy: '5f7d7e8c8f8f8f8f8f8f8f8e'
      };

      const result = await repository.create(data);

      expect(Objection).toHaveBeenCalledWith({
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        createdBy: expect.any(mongoose.Types.ObjectId)
      });
      expect(mockDoc.save).toHaveBeenCalled();
      expect(result).not.toBeNull();
      expect(result.id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(result.name).toBe('Test Objection');
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      const mockDoc = {
        save: jest.fn().mockRejectedValue(error)
      };

      (Objection as any).mockImplementation(() => mockDoc);

      const data: Partial<ObjectionEntity> = {
        name: 'Test Objection',
        category: 'price',
        description: 'Test description'
      };

      await expect(repository.create(data)).rejects.toThrow(error);
    });
  });

  describe('update', () => {
    it('should update an objection', async () => {
      const mockDoc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        name: 'Updated Objection',
        category: 'price',
        description: 'Updated description',
        createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (Objection.findOneAndUpdate as jest.Mock).mockResolvedValue(mockDoc);

      const data: Partial<ObjectionEntity> = {
        name: 'Updated Objection',
        description: 'Updated description'
      };

      const result = await repository.update('5f7d7e8c8f8f8f8f8f8f8f8f', data, 'tenant1');

      expect(Objection.findOneAndUpdate).toHaveBeenCalledWith(
        {
          _id: expect.any(mongoose.Types.ObjectId),
          tenantId: 'tenant1'
        },
        {
          name: 'Updated Objection',
          description: 'Updated description'
        },
        { new: true }
      );
      expect(result).not.toBeNull();
      expect(result?.id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(result?.name).toBe('Updated Objection');
      expect(result?.description).toBe('Updated description');
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (Objection.findOneAndUpdate as jest.Mock).mockRejectedValue(error);

      const data: Partial<ObjectionEntity> = {
        name: 'Updated Objection'
      };

      await expect(repository.update('5f7d7e8c8f8f8f8f8f8f8f8f', data)).rejects.toThrow(error);
    });
  });

  describe('delete', () => {
    it('should delete an objection', async () => {
      (Objection.deleteOne as jest.Mock).mockResolvedValue({ deletedCount: 1 });

      const result = await repository.delete('5f7d7e8c8f8f8f8f8f8f8f8f', 'tenant1');

      expect(Objection.deleteOne).toHaveBeenCalledWith({
        _id: expect.any(mongoose.Types.ObjectId),
        tenantId: 'tenant1'
      });
      expect(result).toBe(true);
    });

    it('should return false if no objection was deleted', async () => {
      (Objection.deleteOne as jest.Mock).mockResolvedValue({ deletedCount: 0 });

      const result = await repository.delete('5f7d7e8c8f8f8f8f8f8f8f8f');

      expect(result).toBe(false);
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (Objection.deleteOne as jest.Mock).mockRejectedValue(error);

      await expect(repository.delete('5f7d7e8c8f8f8f8f8f8f8f8f')).rejects.toThrow(error);
    });
  });

  describe('findOne', () => {
    it('should find one objection with filters', async () => {
      const mockDoc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        name: 'Test Objection',
        category: 'price',
        description: 'Test description',
        createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (Objection.findOne as jest.Mock).mockResolvedValue(mockDoc);

      const result = await repository.findOne({ name: 'Test Objection' }, 'tenant1');

      expect(Objection.findOne).toHaveBeenCalledWith({ name: 'Test Objection', tenantId: 'tenant1' });
      expect(result).not.toBeNull();
      expect(result?.id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(result?.name).toBe('Test Objection');
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      (Objection.findOne as jest.Mock).mockRejectedValue(error);

      await expect(repository.findOne({ name: 'Test Objection' })).rejects.toThrow(error);
    });
  });

  describe('findByCategory', () => {
    it('should find objections by category', async () => {
      const mockDocs = [
        {
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
          name: 'Test Objection 1',
          category: 'price',
          description: 'Test description 1',
          createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockQuery = {
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs)
      };

      (Objection.find as jest.Mock).mockReturnValue(mockQuery);

      const result = await repository.findByCategory('price', 'tenant1');

      expect(Objection.find).toHaveBeenCalledWith({ category: 'price', tenantId: 'tenant1' });
      expect(result.length).toBe(1);
      expect(result[0].category).toBe('price');
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      const mockQuery = {
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockRejectedValue(error)
      };

      (Objection.find as jest.Mock).mockReturnValue(mockQuery);

      await expect(repository.findByCategory('price')).rejects.toThrow(error);
    });
  });
}
