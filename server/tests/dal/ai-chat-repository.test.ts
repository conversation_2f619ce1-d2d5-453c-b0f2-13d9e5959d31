/**
 * Tests for the AiChatRepository
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { AiChatRepository, AiChatEntity, MessageEntity } from '../../dal/ai-chat-repository';
import { IAiChat } from '../../models/mongoose/ai-chat-model';
import { AiChat } from '../../models/mongoose';

// Mock the MongoDB models
jest.mock('../../models/mongoose', () => ({
  AiChat: {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    countDocuments: jest.fn(),
    create: jest.fn(),
    deleteOne: jest.fn()
  }
}));

// Mock the logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('AiChatRepository', () => {
  let repository: AiChatRepository;
  let mongoServer: MongoMemoryServer;

  beforeAll(async () => {
    // Set up MongoDB Memory Server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    repository = new AiChatRepository();
  });

  describe('toEntity', () => {
    it('should convert a document to an entity', () => {
      const mockMessages = [
        {
          role: 'user' as const,
          content: 'Hello, AI!',
          timestamp: new Date('2023-01-01T10:00:00Z')
        },
        {
          role: 'assistant' as const,
          content: 'Hello! How can I help you today?',
          timestamp: new Date('2023-01-01T10:00:05Z')
        }
      ];

      const mockContext = { lastTopic: 'greeting' };

      const doc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        userId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        title: 'Test Chat',
        messages: mockMessages,
        context: mockContext,
        createdAt: new Date('2023-01-01T10:00:00Z'),
        updatedAt: new Date('2023-01-01T10:00:05Z')
      };

      const entity = repository['toEntity'](doc as any);

      expect(entity).not.toBeNull();
      expect(entity?.id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(entity?.userId).toBe('5f7d7e8c8f8f8f8f8f8f8f8e');
      expect(entity?.title).toBe('Test Chat');
      expect(entity?.messages).toHaveLength(2);
      expect(entity?.messages[0].role).toBe('user');
      expect(entity?.messages[0].content).toBe('Hello, AI!');
      expect(entity?.messages[1].role).toBe('assistant');
      expect(entity?.context).toEqual(mockContext);
      expect(entity?.createdAt).toEqual(new Date('2023-01-01T10:00:00Z'));
      expect(entity?.updatedAt).toEqual(new Date('2023-01-01T10:00:05Z'));
    });

    it('should return null for null document', () => {
      const entity = repository['toEntity'](null);
      expect(entity).toBeNull();
    });
  });

  describe('toDocument', () => {
    it('should convert an entity to a document', () => {
      const mockMessages = [
        {
          role: 'user' as const,
          content: 'Hello, AI!',
          timestamp: new Date('2023-01-01T10:00:00Z')
        },
        {
          role: 'assistant' as const,
          content: 'Hello! How can I help you today?',
          timestamp: new Date('2023-01-01T10:00:05Z')
        }
      ];

      const mockContext = { lastTopic: 'greeting' };

      const entity: Partial<AiChatEntity> = {
        id: '5f7d7e8c8f8f8f8f8f8f8f8f',
        userId: '5f7d7e8c8f8f8f8f8f8f8f8e',
        title: 'Test Chat',
        messages: mockMessages,
        context: mockContext
      };

      const doc = repository['toDocument'](entity);

      expect(doc).toEqual({
        _id: expect.any(mongoose.Types.ObjectId),
        userId: expect.any(mongoose.Types.ObjectId),
        title: 'Test Chat',
        messages: mockMessages,
        context: mockContext
      });
      expect(doc._id?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(doc.userId?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8e');
    });

    it('should handle entity without ID and userId', () => {
      const entity: Partial<AiChatEntity> = {
        title: 'Test Chat',
        messages: []
      };

      const doc = repository['toDocument'](entity);

      expect(doc).toEqual({
        title: 'Test Chat',
        messages: []
      });
    });
  });

  describe('findByUserId', () => {
    it('should find chats by user ID', async () => {
      const mockDocs = [
        {
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
          userId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          title: 'Chat 1',
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          userId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          title: 'Chat 2',
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockFind = {
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs)
      };

      (AiChat.find as jest.Mock).mockReturnValue(mockFind);

      const result = await repository.findByUserId('5f7d7e8c8f8f8f8f8f8f8f8e');

      expect(AiChat.find).toHaveBeenCalledWith({
        userId: expect.any(mongoose.Types.ObjectId)
      });
      expect(mockFind.sort).toHaveBeenCalledWith({ updatedAt: -1 });
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(result[1].id).toBe('5f7d7e8c8f8f8f8f8f8f8f8e');
    });
  });

  describe('addMessage', () => {
    it('should add a message to a chat', async () => {
      const mockMessage: MessageEntity = {
        role: 'user',
        content: 'New message',
        timestamp: new Date()
      };

      const mockDoc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        userId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        title: 'Test Chat',
        messages: [mockMessage],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (AiChat.findByIdAndUpdate as jest.Mock).mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockDoc)
      });

      const result = await repository.addMessage('5f7d7e8c8f8f8f8f8f8f8f8f', mockMessage);

      expect(AiChat.findByIdAndUpdate).toHaveBeenCalledWith(
        expect.any(mongoose.Types.ObjectId),
        {
          $push: { messages: mockMessage },
          $set: { updatedAt: expect.any(Date) }
        },
        { new: true }
      );
      expect(result).not.toBeNull();
      expect(result?.id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
    });
  });

  describe('search', () => {
    it('should search chats by title or content', async () => {
      const mockDocs = [
        {
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
          userId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
          title: 'Chat about AI',
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockFind = {
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs)
      };

      (AiChat.find as jest.Mock).mockReturnValue(mockFind);

      const result = await repository.search('5f7d7e8c8f8f8f8f8f8f8f8e', 'AI');

      expect(AiChat.find).toHaveBeenCalledWith({
        userId: expect.any(mongoose.Types.ObjectId),
        $text: { $search: 'AI' }
      });
      expect(mockFind.sort).toHaveBeenCalledWith({ score: { $meta: 'textScore' } });
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Chat about AI');
    });
  });

  describe('updateTitle', () => {
    it('should update chat title', async () => {
      const mockDoc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        userId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        title: 'Updated Title',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (AiChat.findByIdAndUpdate as jest.Mock).mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockDoc)
      });

      const result = await repository.updateTitle('5f7d7e8c8f8f8f8f8f8f8f8f', 'Updated Title');

      expect(AiChat.findByIdAndUpdate).toHaveBeenCalledWith(
        expect.any(mongoose.Types.ObjectId),
        { $set: { title: 'Updated Title', updatedAt: expect.any(Date) } },
        { new: true }
      );
      expect(result).not.toBeNull();
      expect(result?.title).toBe('Updated Title');
    });
  });
});
