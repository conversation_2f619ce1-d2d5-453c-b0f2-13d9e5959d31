/**
 * Tests for the ActivityRepository
 */
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { ActivityRepository, ActivityEntity } from '../../dal/activity-repository';
import { IActivity } from '../../models/mongoose/activity-model';
import { Activity } from '../../models/mongoose';

// Mock the MongoDB models
jest.mock('../../models/mongoose', () => ({
  Activity: {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    countDocuments: jest.fn(),
    create: jest.fn(),
    deleteOne: jest.fn()
  }
}));

// Mock the logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('ActivityRepository', () => {
  let repository: ActivityRepository;
  let mongoServer: MongoMemoryServer;

  beforeAll(async () => {
    // Set up MongoDB Memory Server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    repository = new ActivityRepository();
  });

  describe('toEntity', () => {
    it('should convert a document to an entity', () => {
      const doc = {
        _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
        title: 'Test Activity',
        type: 'call',
        description: 'Test description',
        timestamp: new Date('2023-01-01T10:00:00Z'),
        createdBy: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8e'),
        contactId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8d'),
        companyId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8c'),
        opportunityId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8b'),
        customFields: { field1: 'value1' },
        tenantId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8a'),
        createdAt: new Date('2023-01-01T10:00:00Z'),
        updatedAt: new Date('2023-01-01T10:00:05Z')
      };

      const entity = repository['toEntity'](doc as any);

      expect(entity).not.toBeNull();
      expect(entity?.id).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(entity?.title).toBe('Test Activity');
      expect(entity?.type).toBe('call');
      expect(entity?.description).toBe('Test description');
      expect(entity?.timestamp).toEqual(new Date('2023-01-01T10:00:00Z'));
      expect(entity?.createdBy).toBe('5f7d7e8c8f8f8f8f8f8f8f8e');
      expect(entity?.contactId).toBe('5f7d7e8c8f8f8f8f8f8f8f8d');
      expect(entity?.companyId).toBe('5f7d7e8c8f8f8f8f8f8f8f8c');
      expect(entity?.opportunityId).toBe('5f7d7e8c8f8f8f8f8f8f8f8b');
      expect(entity?.customFields).toEqual({ field1: 'value1' });
      expect(entity?.tenantId).toBe('5f7d7e8c8f8f8f8f8f8f8f8a');
      expect(entity?.createdAt).toEqual(new Date('2023-01-01T10:00:00Z'));
      expect(entity?.updatedAt).toEqual(new Date('2023-01-01T10:00:05Z'));
    });

    it('should return null for null document', () => {
      const entity = repository['toEntity'](null);
      expect(entity).toBeNull();
    });
  });

  describe('toDocument', () => {
    it('should convert an entity to a document', () => {
      const entity: Partial<ActivityEntity> = {
        id: '5f7d7e8c8f8f8f8f8f8f8f8f',
        title: 'Test Activity',
        type: 'call',
        description: 'Test description',
        timestamp: new Date('2023-01-01T10:00:00Z'),
        createdBy: '5f7d7e8c8f8f8f8f8f8f8f8e',
        contactId: '5f7d7e8c8f8f8f8f8f8f8f8d',
        companyId: '5f7d7e8c8f8f8f8f8f8f8f8c',
        opportunityId: '5f7d7e8c8f8f8f8f8f8f8f8b',
        customFields: { field1: 'value1' },
        tenantId: '5f7d7e8c8f8f8f8f8f8f8f8a'
      };

      const doc = repository['toDocument'](entity);

      expect(doc).toEqual({
        _id: expect.any(mongoose.Types.ObjectId),
        title: 'Test Activity',
        type: 'call',
        description: 'Test description',
        timestamp: new Date('2023-01-01T10:00:00Z'),
        createdBy: expect.any(mongoose.Types.ObjectId),
        contactId: expect.any(mongoose.Types.ObjectId),
        companyId: expect.any(mongoose.Types.ObjectId),
        opportunityId: expect.any(mongoose.Types.ObjectId),
        customFields: { field1: 'value1' },
        tenantId: expect.any(mongoose.Types.ObjectId)
      });
      expect(doc._id?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8f');
      expect(doc.createdBy?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8e');
      expect(doc.contactId?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8d');
      expect(doc.companyId?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8c');
      expect(doc.opportunityId?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8b');
      expect(doc.tenantId?.toString()).toBe('5f7d7e8c8f8f8f8f8f8f8f8a');
    });

    it('should handle entity without IDs', () => {
      const entity: Partial<ActivityEntity> = {
        title: 'Test Activity',
        type: 'call',
        timestamp: new Date('2023-01-01T10:00:00Z')
      };

      const doc = repository['toDocument'](entity);

      expect(doc).toEqual({
        title: 'Test Activity',
        type: 'call',
        timestamp: new Date('2023-01-01T10:00:00Z')
      });
    });
  });

  describe('findByContactId', () => {
    it('should find activities by contact ID', async () => {
      const mockDocs = [
        {
          _id: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8f'),
          title: 'Test Activity 1',
          type: 'call',
          timestamp: new Date(),
          contactId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8d'),
          tenantId: new mongoose.Types.ObjectId('5f7d7e8c8f8f8f8f8f8f8f8a'),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockFind = {
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs)
      };

      (Activity.find as jest.Mock).mockReturnValue(mockFind);

      const result = await repository.findByContactId('5f7d7e8c8f8f8f8f8f8f8f8d', '5f7d7e8c8f8f8f8f8f8f8f8a');

      expect(Activity.find).toHaveBeenCalledWith({
        contactId: expect.any(mongoose.Types.ObjectId),
        tenantId: expect.any(mongoose.Types.ObjectId)
      });
      expect(mockFind.sort).toHaveBeenCalledWith({ timestamp: -1 });
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Test Activity 1');
    });
  });

  // Add more tests for other methods as needed
});
