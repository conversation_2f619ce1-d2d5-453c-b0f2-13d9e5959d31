import { 
  SubscriptionPlan, 
  Feature, 
  TenantSubscription, 
  TenantUsage, 
  Tenant, 
  UserTenant, 
  SubscriptionEvent,
  ISubscriptionPlan,
  IFeature,
  ITenantSubscription,
  ITenantUsage,
  ITenant,
  IUserTenant
} from './models/mongoose';
import mongoose from 'mongoose';

/**
 * Service for managing subscriptions with MongoDB
 */
export class MongoSubscriptionService {
  /**
   * Get all subscription plans
   */
  async getPlans(): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.find().sort({ sortOrder: 1 });
  }

  /**
   * Get a specific subscription plan by ID
   */
  async getPlan(id: number | string): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findById(id);
  }

  /**
   * Create a new subscription plan
   */
  async createPlan(plan: any): Promise<ISubscriptionPlan> {
    const newPlan = new SubscriptionPlan(plan);
    const savedPlan = await newPlan.save();
    
    // Create subscription event
    await this.createEvent('subscription.plan.created', savedPlan);
    
    return savedPlan;
  }

  /**
   * Update an existing subscription plan
   */
  async updatePlan(id: number | string, plan: any): Promise<ISubscriptionPlan | null> {
    const updatedPlan = await SubscriptionPlan.findByIdAndUpdate(
      id,
      { $set: plan },
      { new: true }
    );
    
    if (updatedPlan) {
      // Create subscription event
      await this.createEvent('subscription.plan.updated', updatedPlan);
    }
    
    return updatedPlan;
  }

  /**
   * Delete a subscription plan
   */
  async deletePlan(id: number | string): Promise<boolean> {
    const result = await SubscriptionPlan.deleteOne({ _id: id });
    
    if (result.deletedCount === 1) {
      // Create subscription event
      await this.createEvent('subscription.plan.deleted', { id });
      return true;
    }
    
    return false;
  }

  /**
   * Get all features
   */
  async getFeatures(): Promise<IFeature[]> {
    return await Feature.find();
  }

  /**
   * Get a specific feature by ID
   */
  async getFeature(id: number | string): Promise<IFeature | null> {
    return await Feature.findById(id);
  }

  /**
   * Register a new feature
   */
  async registerFeature(feature: any): Promise<IFeature> {
    // Check if feature already exists
    const existingFeature = await Feature.findOne({ key: feature.key });
    
    if (existingFeature) {
      throw new Error(`Feature with key '${feature.key}' already exists`);
    }
    
    const newFeature = new Feature(feature);
    const savedFeature = await newFeature.save();
    
    // Create subscription event
    await this.createEvent('subscription.feature.registered', savedFeature);
    
    return savedFeature;
  }

  /**
   * Update an existing feature
   */
  async updateFeature(id: number | string, feature: any): Promise<IFeature | null> {
    const updatedFeature = await Feature.findByIdAndUpdate(
      id,
      { $set: feature },
      { new: true }
    );
    
    if (updatedFeature) {
      // Create subscription event
      await this.createEvent('subscription.feature.updated', updatedFeature);
    }
    
    return updatedFeature;
  }

  /**
   * Remove a feature
   */
  async removeFeature(id: number | string): Promise<boolean> {
    const result = await Feature.deleteOne({ _id: id });
    
    if (result.deletedCount === 1) {
      // Create subscription event
      await this.createEvent('subscription.feature.removed', { id });
      return true;
    }
    
    return false;
  }

  /**
   * Get all tenant subscriptions
   */
  async getTenantSubscriptions(): Promise<ITenantSubscription[]> {
    return await TenantSubscription.find();
  }

  /**
   * Get a specific tenant subscription by ID
   */
  async getTenantSubscription(id: number | string): Promise<ITenantSubscription | null> {
    return await TenantSubscription.findById(id);
  }

  /**
   * Get a tenant subscription by tenant ID
   */
  async getTenantSubscriptionByTenantId(tenantId: string): Promise<ITenantSubscription | null> {
    return await TenantSubscription.findOne({ tenantId });
  }

  /**
   * Get a tenant subscription with plan details
   */
  async getTenantSubscriptionWithPlan(tenantId: string): Promise<{ subscription: ITenantSubscription, plan: ISubscriptionPlan } | null> {
    const subscription = await TenantSubscription.findOne({ tenantId });
    
    if (!subscription) {
      return null;
    }
    
    const plan = await SubscriptionPlan.findById(subscription.planId);
    
    if (!plan) {
      return null;
    }
    
    return { subscription, plan };
  }

  /**
   * Create a new tenant subscription
   */
  async createTenantSubscription(subscription: any): Promise<ITenantSubscription> {
    const newSubscription = new TenantSubscription(subscription);
    const savedSubscription = await newSubscription.save();
    
    // Create subscription event
    await this.createEvent('subscription.tenant.created', savedSubscription);
    
    return savedSubscription;
  }

  /**
   * Update an existing tenant subscription
   */
  async updateTenantSubscription(id: number | string, subscription: any): Promise<ITenantSubscription | null> {
    const updatedSubscription = await TenantSubscription.findByIdAndUpdate(
      id,
      { $set: subscription },
      { new: true }
    );
    
    if (updatedSubscription) {
      // Create subscription event
      await this.createEvent('subscription.tenant.updated', updatedSubscription);
    }
    
    return updatedSubscription;
  }

  /**
   * Get tenant usage for a specific period
   */
  async getTenantUsage(tenantId: string, period?: string): Promise<ITenantUsage | null> {
    if (!period) {
      const currentDate = new Date();
      period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    }
    
    return await TenantUsage.findOne({ tenantId, period });
  }

  /**
   * Record resource usage
   */
  async recordUsage(tenantId: string, resourceType: string, amount: number = 1): Promise<void> {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    
    // Get current usage for this period
    let usage = await TenantUsage.findOne({ tenantId, period });
    
    if (!usage) {
      // Create new usage record if none exists
      usage = await TenantUsage.create({
        tenantId,
        period,
        usage: { [resourceType]: amount },
        featureUsage: {}
      });
    } else {
      // Update existing usage record
      const currentUsage = usage.usage || {};
      const updatedUsage = {
        ...currentUsage,
        [resourceType]: (currentUsage[resourceType] || 0) + amount
      };
      
      await TenantUsage.updateOne(
        { _id: usage._id },
        { $set: { usage: updatedUsage, updatedAt: new Date() } }
      );
    }
  }

  /**
   * Record feature usage
   */
  async recordFeatureUsage(tenantId: string, featureKey: string, amount: number = 1): Promise<void> {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    
    // Get current usage for this period
    let usage = await TenantUsage.findOne({ tenantId, period });
    
    if (!usage) {
      // Create new usage record if none exists
      usage = await TenantUsage.create({
        tenantId,
        period,
        usage: {},
        featureUsage: { [featureKey]: amount }
      });
    } else {
      // Update existing usage record
      const currentFeatureUsage = usage.featureUsage || {};
      const updatedFeatureUsage = {
        ...currentFeatureUsage,
        [featureKey]: (currentFeatureUsage[featureKey] || 0) + amount
      };
      
      await TenantUsage.updateOne(
        { _id: usage._id },
        { $set: { featureUsage: updatedFeatureUsage, updatedAt: new Date() } }
      );
    }
  }

  /**
   * Check if a tenant is entitled to use a feature
   */
  async checkEntitlement(tenantId: string, featureKey: string): Promise<boolean> {
    // Get tenant subscription
    const subscription = await TenantSubscription.findOne({ tenantId });
    
    if (!subscription) {
      return false;
    }
    
    // Check if subscription is active
    if (subscription.status !== 'active' && subscription.status !== 'trialing') {
      return false;
    }
    
    // Check if subscription is expired
    if (new Date(subscription.endDate) < new Date()) {
      return false;
    }
    
    // Check custom features first
    if (subscription.customFeatures && featureKey in subscription.customFeatures) {
      return subscription.customFeatures[featureKey];
    }
    
    // Get subscription plan
    const plan = await SubscriptionPlan.findById(subscription.planId);
    
    if (!plan) {
      return false;
    }
    
    // Check plan features
    return plan.features[featureKey] || false;
  }

  /**
   * Check if a tenant is within resource limits
   */
  async checkResourceLimit(tenantId: string, resourceType: string, additionalAmount: number = 1): Promise<boolean> {
    // Get tenant subscription
    const subscription = await TenantSubscription.findOne({ tenantId });
    
    if (!subscription) {
      return false;
    }
    
    // Check if subscription is active
    if (subscription.status !== 'active' && subscription.status !== 'trialing') {
      return false;
    }
    
    // Check if subscription is expired
    if (new Date(subscription.endDate) < new Date()) {
      return false;
    }
    
    // Get current usage
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    const usage = await TenantUsage.findOne({ tenantId, period });
    
    const currentUsage = usage?.usage?.[resourceType] || 0;
    const totalAmount = currentUsage + additionalAmount;
    
    // Check custom limits first
    if (subscription.customLimits && resourceType in subscription.customLimits) {
      return totalAmount <= subscription.customLimits[resourceType];
    }
    
    // Get subscription plan
    const plan = await SubscriptionPlan.findById(subscription.planId);
    
    if (!plan) {
      return false;
    }
    
    // Check plan limits
    return totalAmount <= (plan.limits[resourceType] || 0);
  }

  /**
   * Get all tenants
   */
  async getTenants(): Promise<ITenant[]> {
    return await Tenant.find();
  }

  /**
   * Get a specific tenant by ID
   */
  async getTenant(id: number | string): Promise<ITenant | null> {
    return await Tenant.findById(id);
  }

  /**
   * Create a new tenant
   */
  async createTenant(tenant: any): Promise<ITenant> {
    const newTenant = new Tenant(tenant);
    return await newTenant.save();
  }

  /**
   * Update an existing tenant
   */
  async updateTenant(id: number | string, tenant: any): Promise<ITenant | null> {
    return await Tenant.findByIdAndUpdate(
      id,
      { $set: tenant },
      { new: true }
    );
  }

  /**
   * Get user-tenant relationships for a user
   */
  async getUserTenants(userId: number | string): Promise<IUserTenant[]> {
    return await UserTenant.find({ userId });
  }

  /**
   * Get user-tenant relationships for a tenant
   */
  async getTenantUsers(tenantId: number | string): Promise<IUserTenant[]> {
    return await UserTenant.find({ tenantId });
  }

  /**
   * Create a new user-tenant relationship
   */
  async createUserTenant(userTenant: any): Promise<IUserTenant> {
    const newUserTenant = new UserTenant(userTenant);
    return await newUserTenant.save();
  }

  /**
   * Update an existing user-tenant relationship
   */
  async updateUserTenant(id: number | string, userTenant: any): Promise<IUserTenant | null> {
    return await UserTenant.findByIdAndUpdate(
      id,
      { $set: userTenant },
      { new: true }
    );
  }

  /**
   * Create a subscription event
   */
  async createEvent(type: string, payload: any): Promise<any> {
    const event = new SubscriptionEvent({
      type,
      payload,
      timestamp: new Date()
    });
    
    return await event.save();
  }

  /**
   * Get subscription cache for synchronization
   */
  async getSubscriptionCache(): Promise<any> {
    const [plans, features, tenantSubscriptions] = await Promise.all([
      SubscriptionPlan.find(),
      Feature.find(),
      TenantSubscription.find()
    ]);
    
    return {
      plans,
      features,
      tenantSubscriptions
    };
  }
}
