import { Request, Response } from 'express';
import { isOpenAIAvailable } from './ai';
import { spawn, exec, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import * as path from 'path';

// Configuration for Python AI service
const AI_SERVICE_CONFIG = {
  // Fallback to local if region-specific endpoints are not available
  ENDPOINTS: {
    DEFAULT: process.env.AI_SERVICE_URL || 'http://localhost:8000',
    JOHANNESBURG: process.env.AI_SERVICE_JNB_URL,
    LAGOS: process.env.AI_SERVICE_LOS_URL
  },
  // Timeout in milliseconds
  TIMEOUT: parseInt(process.env.AI_SERVICE_TIMEOUT || '15000'),
  // Whether to use CBOR instead of JSON for payload compression
  USE_CBOR: process.env.USE_CBOR_COMPRESSION === 'true'
};

// Track the AI service process to ensure we don't spawn multiple instances
let aiServiceProcess: ChildProcess | null = null;
let lastStartAttempt = 0;

/**
 * Determines the best AI service endpoint based on user location (if available)
 * or system load balancing
 */
function getBestAIEndpoint(userRegion?: string): string {
  if (userRegion === 'ZA' && AI_SERVICE_CONFIG.ENDPOINTS.JOHANNESBURG) {
    return AI_SERVICE_CONFIG.ENDPOINTS.JOHANNESBURG;
  }
  
  if (userRegion === 'NG' && AI_SERVICE_CONFIG.ENDPOINTS.LAGOS) {
    return AI_SERVICE_CONFIG.ENDPOINTS.LAGOS;
  }
  
  return AI_SERVICE_CONFIG.ENDPOINTS.DEFAULT;
}

/**
 * Function to determine if the AI Bridge is available and configured
 * Falls back to local OpenAI if bridge is not available
 */
export async function isAIBridgeAvailable(): Promise<boolean> {
  // Check if the service is responding
  try {
    const endpoint = getBestAIEndpoint();
    const response = await fetch(`${endpoint}/status`, {
      method: 'GET',
      signal: AbortSignal.timeout(2000) // 2 second timeout
    });
    
    if (response.ok) {
      return true;
    }
  } catch (error) {
    console.log("AI service not available, attempting to start it...");
  }
  
  // Service is not responding, try to start it if needed
  await ensureAIServiceRunning();
  
  // Return OpenAI availability as fallback
  return isOpenAIAvailable();
}

/**
 * Ensures the AI service is running, starting it if necessary
 */
async function ensureAIServiceRunning(): Promise<boolean> {
  // Don't attempt to start multiple times in quick succession
  const now = Date.now();
  if (now - lastStartAttempt < 30000) { // 30 second cooldown
    return false;
  }
  
  lastStartAttempt = now;
  
  try {
    // Run the service start script
    console.log("Starting AI service process...");
    
    // Check if the starter script exists, use it if it does
    const scriptPath = path.join(process.cwd(), 'start_ai_service.sh');
    const scriptExists = await fs.access(scriptPath).then(() => true).catch(() => false);
    
    if (scriptExists) {
      // Use the script if it exists
      console.log("Using start_ai_service.sh script");
      exec('bash start_ai_service.sh', (error, stdout, stderr) => {
        if (error) {
          console.error(`Error starting AI service: ${error.message}`);
          return false;
        }
        if (stderr) {
          console.error(`AI service stderr: ${stderr}`);
        }
        console.log(`AI service output: ${stdout}`);
      });
    } else {
      // Direct start as fallback
      console.log("No script found, starting service directly");
      const cwd = path.join(process.cwd(), 'ai_service');
      aiServiceProcess = spawn('python', ['-m', 'uvicorn', 'main:app', '--host', '0.0.0.0', '--port', '8000'], {
        cwd,
        detached: true,
        stdio: ['ignore', 'ignore', 'ignore']
      });
      
      aiServiceProcess.unref();
      
      // Wait a moment for startup
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    return true;
  } catch (error) {
    console.error("Failed to start AI service:", error);
    return false;
  }
}

/**
 * Proxies AI requests to Python backend for advanced processing
 * Falls back to direct OpenAI processing when Python services are unavailable
 */
export async function handleAIBridgeRequest(req: Request, res: Response) {
  try {
    const userRegion = req.get('X-User-Region') as string | undefined;
    const endpoint = getBestAIEndpoint(userRegion);
    
    // In a production environment, would implement actual CBOR encoding here
    // For now, using JSON for simplicity
    
    // Make request to Python AI service
    const response = await fetch(`${endpoint}/general`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': process.env.AI_SERVICE_KEY || ''
      },
      body: JSON.stringify({
        query: req.body.query,
        context: req.body.context,
        userId: (req.session as any)?.userId as number | undefined,
        requestType: req.body.requestType || 'general'
      })
    });
    
    if (!response.ok) {
      // Fallback to local OpenAI if available
      if (isOpenAIAvailable()) {
        // Log the failed AI bridge attempt
        console.warn(`AI Bridge request failed, falling back to local OpenAI processing`);
        
        // Route to local AI handler - this would be your existing AI handler
        // For now we'll just return a mock response
        return res.json({
          response: "This response is from the local OpenAI fallback",
          source: "openai_fallback"
        });
      }
      
      throw new Error(`AI Service returned status ${response.status}`);
    }
    
    const data = await response.json();
    return res.json({
      ...data,
      source: "python_ai_service"
    });
    
  } catch (error) {
    console.error('AI Bridge error:', error);
    
    if (isOpenAIAvailable()) {
      // Return fallback message
      return res.json({
        response: "The AI service is temporarily unavailable. Processing with simplified model.",
        source: "error_fallback"
      });
    }
    
    return res.status(500).json({
      error: 'AI processing failed',
      message: 'The AI service is currently unavailable. Please try again later.'
    });
  }
}

/**
 * Handles crew-specific AI requests that require multiple agent collaboration
 */
export async function handleCrewAIRequest(req: Request, res: Response) {
  try {
    const userRegion = req.get('X-User-Region') as string | undefined;
    const endpoint = getBestAIEndpoint(userRegion);
    
    const response = await fetch(`${endpoint}/crew`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': process.env.AI_SERVICE_KEY || ''
      },
      body: JSON.stringify({
        task: req.body.task || req.body.query,
        context: req.body.context || req.body.data || {},
        userId: (req.session as any)?.userId as number | undefined
      })
    });
    
    if (!response.ok) {
      throw new Error(`CrewAI Service returned status ${response.status}`);
    }
    
    const data = await response.json();
    return res.json({
      ...data,
      source: "crew_ai_service"
    });
    
  } catch (error) {
    console.error('CrewAI Bridge error:', error);
    
    return res.status(503).json({
      error: 'CrewAI processing failed',
      message: 'The advanced AI service is currently unavailable. Please try again later.'
    });
  }
}

/**
 * Routes for specific AI capabilities leveraging Python backend
 */
export function registerAIBridgeRoutes(app: any) {
  // General AI processing endpoint
  app.post('/api/ai-bridge/process', (req: Request, res: Response) => {
    return handleAIBridgeRequest(req, res);
  });
  
  // CrewAI specialized endpoints
  app.post('/api/ai-bridge/crew', (req: Request, res: Response) => {
    return handleCrewAIRequest(req, res);
  });
  
  // AI Bridge status endpoint
  app.get('/api/ai-bridge/status', async (_req: Request, res: Response) => {
    // Ensure service is running and check availability
    const isAvailable = await isAIBridgeAvailable();
    let pythonServiceStatus = {
      available: false,
      openai_available: false,
      version: null
    };
    
    // Try to get the actual status from the AI service
    try {
      const endpoint = getBestAIEndpoint();
      const response = await fetch(`${endpoint}/status`, {
        method: 'GET',
        headers: {
          'X-API-Key': process.env.AI_SERVICE_KEY || ''
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });
      
      if (response.ok) {
        const statusData = await response.json();
        pythonServiceStatus = {
          available: statusData.status === 'online',
          openai_available: statusData.openai_available,
          version: statusData.version
        };
      }
    } catch (error) {
      console.log("Could not connect to AI service for status check");
      // Continue with fallback status
    }
    
    return res.json({
      available: isAvailable,
      region: AI_SERVICE_CONFIG.ENDPOINTS.JOHANNESBURG ? 'multi-region' : 'default',
      compression: AI_SERVICE_CONFIG.USE_CBOR,
      python_service: pythonServiceStatus,
      autostart_enabled: true
    });
  });
}