import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import {
  insertUserSchema,
  insertContactSchema,
  insertCompanySchema,
  insertOpportunitySchema,
  insertActivitySchema,
  insertRelationshipSchema,
  insertAiChatSchema,
  insertInsightSchema
} from "@shared/schema";
import bcrypt from "bcrypt";
import session from "express-session";
import MemoryStore from "memorystore";
import OpenAI from "openai";
import { testConnection } from "./mongodb";
import { handleChatCompletion, generateInsights, enrichContactData, isOpenAIAvailable } from "./ai";
import { registerAIBridgeRoutes, isAIBridgeAvailable } from "./ai-bridge";
import { registerSubscriptionRoutes } from "./subscription-routes";
import { requireFeature, checkResourceLimit } from "./middleware/subscription-middleware";

// Configure OpenAI
const openai = new OpenAI({
  apiKey: process.env.CRM_OPENAI_API_KEY || process.env.OPENAI_API_KEY || "sk-your-key-here" // Using provided CRM_OPENAI_API_KEY
});

import { registerMongoRoutes } from './routes/mongo-routes';

export async function registerRoutes(app: Express): Promise<Server> {
  // Check if MongoDB is enabled
  const useMongoDb = process.env.MONGODB_ENABLED === 'true';

  if (useMongoDb) {
    console.log("Using MongoDB routes");
    registerMongoRoutes(app);
    const httpServer = createServer(app);
    return httpServer;
  }

  // Continue with PostgreSQL routes
  console.log("Using PostgreSQL routes");
  // Configure session
  const MemoryStoreSession = MemoryStore(session);
  app.use(
    session({
      cookie: { maxAge: 86400000 }, // 24 hours
      store: new MemoryStoreSession({
        checkPeriod: 86400000, // prune expired entries every 24h
      }),
      resave: false,
      saveUninitialized: false,
      secret: process.env.SESSION_SECRET || "rel-ai-crm-secret",
    })
  );

  // Session store for tracking Firebase auth tokens
  const firebaseTokens = new Map<string, { uid: string; expires: number }>();

  // Authentication middleware - checks for either session auth or Firebase token auth
  const authenticateUser = (req: Request, res: Response, next: Function) => {
    // First check traditional session authentication
    if (req.session && req.session.userId) {
      return next();
    }

    // Then check for Firebase token in Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const tokenInfo = firebaseTokens.get(token);

      if (tokenInfo && tokenInfo.expires > Date.now()) {
        // Valid token found, add user info to request
        (req as any).firebaseUid = tokenInfo.uid;
        return next();
      }
    }

    return res.status(401).json({ message: "Unauthorized" });
  };

  // Auth routes
  app.post("/api/auth/register", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);

      // Check if user already exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      const existingEmail = await storage.getUserByEmail(userData.email);
      if (existingEmail) {
        return res.status(400).json({ message: "Email already exists" });
      }

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // Create user with hashed password
      const user = await storage.createUser({
        ...userData,
        password: hashedPassword
      });

      // Create session
      req.session.userId = user.id;

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return res.status(201).json(userWithoutPassword);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      // Find user
      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Create session
      req.session.userId = user.id;

      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return res.status(200).json(userWithoutPassword);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Could not log out" });
      }
      res.clearCookie("connect.sid");
      return res.status(200).json({ message: "Logged out successfully" });
    });
  });

  app.get("/api/auth/me", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId as number;
      const user = await storage.getUser(userId);

      if (!user) {
        req.session.destroy(() => {});
        return res.status(401).json({ message: "User not found" });
      }

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return res.status(200).json(userWithoutPassword);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  // Firebase Auth sync endpoint
  app.post("/api/auth/sync", async (req, res) => {
    try {
      const { token, email, uid, displayName, photoURL } = req.body;

      if (!token || !uid || !email) {
        return res.status(400).json({ message: "Required fields missing" });
      }

      // Store token for future API requests
      // Token expiration set to 1 hour from now
      firebaseTokens.set(token, {
        uid,
        expires: Date.now() + 3600000
      });

      // Check if user exists by email
      let user = await storage.getUserByEmail(email);

      if (!user) {
        // Create a new user
        // Create user with basic info
        user = await storage.createUser({
          username: email.split('@')[0], // Use part of email as username
          email,
          password: Math.random().toString(36), // Generate a random password, will never be used
          fullName: displayName || email.split('@')[0]
        });
      }

      // Create session for user
      if (req.session) {
        req.session.userId = user.id;
      }

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return res.status(200).json(userWithoutPassword);
    } catch (error) {
      console.error("Firebase auth sync error:", error);
      return res.status(500).json({ message: "Error syncing Firebase auth", error: error.message });
    }
  });

  // Contact routes
  app.get("/api/contacts", authenticateUser, async (req, res) => {
    const limit = parseInt(req.query.limit as string || "100");
    const offset = parseInt(req.query.offset as string || "0");
    const contacts = await storage.getContacts(limit, offset);
    return res.status(200).json(contacts);
  });

  app.get("/api/contacts/:id", authenticateUser, async (req, res) => {
    const id = parseInt(req.params.id);
    const contact = await storage.getContact(id);

    if (!contact) {
      return res.status(404).json({ message: "Contact not found" });
    }

    return res.status(200).json(contact);
  });

  app.post("/api/contacts",
    authenticateUser,
    checkResourceLimit("contacts"), // Check if within contacts limit
    async (req, res) => {
    try {
      const contactData = insertContactSchema.parse(req.body);
      const userId = req.session.userId as number;

      const contact = await storage.createContact({
        ...contactData,
        createdBy: userId
      });

      // Create activity for the new contact
      await storage.createActivity({
        title: "Contact created",
        description: `Contact ${contact.firstName} ${contact.lastName} was created`,
        type: "contact_created",
        contactId: contact.id,
        createdBy: userId
      });

      return res.status(201).json(contact);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.patch("/api/contacts/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const contactData = insertContactSchema.partial().parse(req.body);

      const updatedContact = await storage.updateContact(id, contactData);

      if (!updatedContact) {
        return res.status(404).json({ message: "Contact not found" });
      }

      return res.status(200).json(updatedContact);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.delete("/api/contacts/:id", authenticateUser, async (req, res) => {
    const id = parseInt(req.params.id);
    const success = await storage.deleteContact(id);

    if (!success) {
      return res.status(404).json({ message: "Contact not found" });
    }

    return res.status(204).send();
  });

  // Company routes
  app.get("/api/companies", authenticateUser, async (req, res) => {
    const limit = parseInt(req.query.limit as string || "100");
    const offset = parseInt(req.query.offset as string || "0");
    const companies = await storage.getCompanies(limit, offset);
    return res.status(200).json(companies);
  });

  app.get("/api/companies/:id", authenticateUser, async (req, res) => {
    const id = parseInt(req.params.id);
    const company = await storage.getCompany(id);

    if (!company) {
      return res.status(404).json({ message: "Company not found" });
    }

    return res.status(200).json(company);
  });

  app.post("/api/companies",
    authenticateUser,
    checkResourceLimit("companies"), // Check if within companies limit
    async (req, res) => {
    try {
      const companyData = insertCompanySchema.parse(req.body);
      const userId = req.session.userId as number;

      const company = await storage.createCompany({
        ...companyData,
        createdBy: userId
      });

      // Create activity for the new company
      await storage.createActivity({
        title: "Company created",
        description: `Company ${company.name} was created`,
        type: "company_created",
        companyId: company.id,
        createdBy: userId
      });

      return res.status(201).json(company);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.patch("/api/companies/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const companyData = insertCompanySchema.partial().parse(req.body);

      const updatedCompany = await storage.updateCompany(id, companyData);

      if (!updatedCompany) {
        return res.status(404).json({ message: "Company not found" });
      }

      return res.status(200).json(updatedCompany);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.delete("/api/companies/:id", authenticateUser, async (req, res) => {
    const id = parseInt(req.params.id);
    const success = await storage.deleteCompany(id);

    if (!success) {
      return res.status(404).json({ message: "Company not found" });
    }

    return res.status(204).send();
  });

  // Opportunity routes
  app.get("/api/opportunities", authenticateUser, async (req, res) => {
    const limit = parseInt(req.query.limit as string || "100");
    const offset = parseInt(req.query.offset as string || "0");
    const opportunities = await storage.getOpportunities(limit, offset);
    return res.status(200).json(opportunities);
  });

  app.get("/api/opportunities/:id", authenticateUser, async (req, res) => {
    const id = parseInt(req.params.id);
    const opportunity = await storage.getOpportunity(id);

    if (!opportunity) {
      return res.status(404).json({ message: "Opportunity not found" });
    }

    return res.status(200).json(opportunity);
  });

  app.post("/api/opportunities",
    authenticateUser,
    checkResourceLimit("opportunities"), // Check if within opportunities limit
    async (req, res) => {
    try {
      const opportunityData = insertOpportunitySchema.parse(req.body);
      const userId = req.session.userId as number;

      const opportunity = await storage.createOpportunity({
        ...opportunityData,
        createdBy: userId
      });

      // Create activity for the new opportunity
      await storage.createActivity({
        title: "Opportunity created",
        description: `Opportunity ${opportunity.name} was created`,
        type: "opportunity_created",
        opportunityId: opportunity.id,
        createdBy: userId
      });

      return res.status(201).json(opportunity);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.patch("/api/opportunities/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const opportunityData = insertOpportunitySchema.partial().parse(req.body);

      const updatedOpportunity = await storage.updateOpportunity(id, opportunityData);

      if (!updatedOpportunity) {
        return res.status(404).json({ message: "Opportunity not found" });
      }

      return res.status(200).json(updatedOpportunity);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  app.delete("/api/opportunities/:id", authenticateUser, async (req, res) => {
    const id = parseInt(req.params.id);
    const success = await storage.deleteOpportunity(id);

    if (!success) {
      return res.status(404).json({ message: "Opportunity not found" });
    }

    return res.status(204).send();
  });

  // Activity routes
  app.get("/api/activities", authenticateUser, async (req, res) => {
    const limit = parseInt(req.query.limit as string || "100");
    const offset = parseInt(req.query.offset as string || "0");
    const activities = await storage.getActivities(limit, offset);
    return res.status(200).json(activities);
  });

  app.post("/api/activities", authenticateUser, async (req, res) => {
    try {
      const activityData = insertActivitySchema.parse(req.body);
      const userId = req.session.userId as number;

      const activity = await storage.createActivity({
        ...activityData,
        createdBy: userId
      });

      return res.status(201).json(activity);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  // Relationship routes
  app.get("/api/relationships", authenticateUser, async (req, res) => {
    const relationships = await storage.getRelationships();
    return res.status(200).json(relationships);
  });

  app.post("/api/relationships", authenticateUser, async (req, res) => {
    try {
      const relationshipData = insertRelationshipSchema.parse(req.body);
      const userId = req.session.userId as number;

      const relationship = await storage.createRelationship({
        ...relationshipData,
        createdBy: userId
      });

      return res.status(201).json(relationship);
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  });

  // AI Chat routes
  app.post("/api/ai/chat",
    authenticateUser,
    requireFeature("ai.assistant.basic"),
    checkResourceLimit("ai.tokens", () => 100), // Estimate token usage
    async (req, res) => {
    try {
      const { message } = req.body;
      const userId = req.session.userId as number;

      if (!message) {
        return res.status(400).json({ message: "Message is required" });
      }

      // Get some context data (most recent activities, contacts, and opportunities)
      const activities = await storage.getActivities(10);
      const contacts = await storage.getContacts(10);
      const opportunities = await storage.getOpportunities(10);

      // Use OpenAI to generate a response
      const completion = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: "system",
            content: `You are a helpful assistant for Aizako CRM that helps users manage their contacts, companies, and opportunities.
                     You have access to the following data:
                     - Recent activities: ${JSON.stringify(activities)}
                     - Recent contacts: ${JSON.stringify(contacts)}
                     - Recent opportunities: ${JSON.stringify(opportunities)}

                     Provide concise and helpful responses to user queries about their CRM data. If asked about specific contacts,
                     companies, or opportunities, refer to the data provided. If the user asks for information not available, politely
                     explain that you don't have that information and suggest how they might find it.`
          },
          { role: "user", content: message }
        ],
      });

      const aiResponse = completion.choices[0].message.content || "I'm sorry, I couldn't generate a response.";

      // Save the chat to the database
      const chat = await storage.createAiChat({
        userId,
        message,
        response: aiResponse,
        metadata: {}
      });

      return res.status(200).json(chat);
    } catch (error) {
      console.error("AI chat error:", error);
      return res.status(500).json({ message: "Error generating AI response" });
    }
  });

  app.get("/api/ai/history", authenticateUser, async (req, res) => {
    const userId = req.session.userId as number;
    const limit = parseInt(req.query.limit as string || "20");

    const chatHistory = await storage.getAiChatsByUser(userId, limit);
    return res.status(200).json(chatHistory);
  });

  // Contact enrichment with AI
  app.post("/api/ai/enrich-contact/:id",
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 200), // Enrichment uses tokens
    async (req, res) => {
    try {
      const contactId = parseInt(req.params.id);
      const contact = await storage.getContact(contactId);

      if (!contact) {
        return res.status(404).json({ message: "Contact not found" });
      }

      // Get related data
      const company = contact.companyId ? await storage.getCompany(contact.companyId) : null;
      const activities = await storage.getActivitiesByContact(contactId);
      const opportunities = await storage.getOpportunitiesByContact(contactId);

      // Use OpenAI to analyze the contact and generate enrichment data
      const completion = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: "system",
            content: `You are an AI assistant that enriches contact data with insights. Analyze the following contact and related data,
                     and generate JSON with enriched information including:
                     1. Potential interests based on their role and company
                     2. Recommended next steps for engagement
                     3. Potential pain points they might have
                     4. Suggested personalization approaches`
          },
          {
            role: "user",
            content: `Contact: ${JSON.stringify(contact)}
                     Company: ${JSON.stringify(company)}
                     Activities: ${JSON.stringify(activities)}
                     Opportunities: ${JSON.stringify(opportunities)}`
          }
        ],
        response_format: { type: "json_object" },
      });

      const enrichment = JSON.parse(completion.choices[0].message.content || "{}");

      // Update the contact with enrichment data
      const updatedContact = await storage.updateContact(contactId, {
        aiEnrichment: enrichment
      });

      // Create an insight
      await storage.createInsight({
        title: `New AI enrichment for ${contact.firstName} ${contact.lastName}`,
        description: "AI has identified new insights that might help you engage with this contact more effectively.",
        targetType: "contact",
        targetId: contactId,
        importance: 7,
        category: "enrichment",
        read: false
      });

      return res.status(200).json(updatedContact);
    } catch (error) {
      console.error("AI enrichment error:", error);
      return res.status(500).json({ message: "Error generating AI enrichment" });
    }
  });

  // Dashboard metrics
  app.get("/api/dashboard/metrics", authenticateUser, async (req, res) => {
    const metrics = await storage.getMetrics();
    return res.status(200).json(metrics);
  });

  // User preferences
  app.get("/api/user/preferences", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId as number;

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      return res.status(200).json(user.preferences || {});
    } catch (error) {
      console.error("Error fetching user preferences:", error);
      return res.status(500).json({ message: "Failed to fetch user preferences" });
    }
  });

  app.post("/api/user/preferences", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId as number;

      const { preferences } = req.body;
      if (!preferences || typeof preferences !== 'object') {
        return res.status(400).json({ message: "Invalid preferences data" });
      }

      const updatedUser = await storage.updateUserPreferences(userId, preferences);
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }

      return res.status(200).json(updatedUser.preferences);
    } catch (error) {
      console.error("Error updating user preferences:", error);
      return res.status(500).json({ message: "Failed to update user preferences" });
    }
  });

  // Insights
  app.get("/api/insights", authenticateUser, async (req, res) => {
    const limit = parseInt(req.query.limit as string || "20");
    const offset = parseInt(req.query.offset as string || "0");

    const insights = await storage.getInsights(limit, offset);
    return res.status(200).json(insights);
  });

  app.patch("/api/insights/:id/read", authenticateUser, async (req, res) => {
    const id = parseInt(req.params.id);
    const updatedInsight = await storage.markInsightAsRead(id);

    if (!updatedInsight) {
      return res.status(404).json({ message: "Insight not found" });
    }

    return res.status(200).json(updatedInsight);
  });

  // MongoDB test endpoint
  app.get("/api/mongodb/test", async (req, res) => {
    try {
      const { testMongoDBConnection } = await import('./mongodb-connection');
      const isConnected = await testMongoDBConnection();
      if (isConnected) {
        return res.status(200).json({
          message: "MongoDB connection successful!",
          status: "connected"
        });
      } else {
        return res.status(500).json({
          message: "Failed to connect to MongoDB",
          status: "disconnected"
        });
      }
    } catch (error) {
      console.error("MongoDB test error:", error);
      return res.status(500).json({
        message: "Error testing MongoDB connection",
        error: error instanceof Error ? error.message : String(error),
        status: "error"
      });
    }
  });

  // CopilotKit endpoints
  app.post('/api/copilot', authenticateUser, async (req, res) => {
    const { handleCopilotRuntime } = await import('./copilotkit');
    handleCopilotRuntime(req, res);
  });

  // CopilotKit runtime endpoint
  app.post('/api/copilotkit', async (req, res) => {
    // Handle CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-copilotkit-api-key');

    try {
      // Extract the request parameters from the CopilotKit request
      const { messages, action, context, tool_name, tool_parameters } = req.body || {};

      // Set a default user ID if not authenticated (development mode)
      if (!req.session.userId) {
        req.session.userId = 1;
      }

      // Forward to our enhanced CopilotKit handler
      const { handleCopilotRuntime } = await import('./copilotkit');
      return handleCopilotRuntime(req, res);
    } catch (error) {
      console.error('Error in CopilotKit endpoint:', error);
      res.status(500).json({
        id: new Date().getTime().toString(),
        object: "chat.completion",
        created: Math.floor(Date.now() / 1000),
        model: "gpt-4o",
        choices: [
          {
            index: 0,
            message: {
              role: "assistant",
              content: "I'm sorry, but I encountered an error processing your request. Please try again later."
            },
            finish_reason: "stop"
          }
        ],
        usage: { prompt_tokens: 10, completion_tokens: 20, total_tokens: 30 },
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Direct chat endpoint for local OpenAI API
  app.post('/api/chat', authenticateUser, async (req, res) => {
    try {
      const { message } = req.body;

      if (!message) {
        return res.status(400).json({ error: 'Message is required' });
      }

      // Get user ID from session
      const userId = req.session.userId as number;

      if (!isOpenAIAvailable()) {
        // Create a record of the failed chat attempt
        const chat = await storage.createAiChat({
          userId: userId,
          message: message,
          response: "AI service is not available at the moment. Please ensure an OpenAI API key is configured.",
          metadata: { error: "api_key_missing" }
        });

        return res.status(200).json({
          id: chat.id,
          userId: chat.userId,
          message: chat.message,
          response: chat.response,
          timestamp: chat.timestamp
        });
      }

      // Use handleChatCompletion from ai.ts to process the request
      const { handleChatCompletion } = await import('./ai');
      return handleChatCompletion(req, res);
    } catch (error) {
      console.error('Error in chat API:', error);
      return res.status(500).json({ error: 'An error occurred processing your request' });
    }
  });

  // Support OPTIONS requests for CORS
  app.options('/api/copilotkit', (req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-copilotkit-api-key, x-copilotkit-tool-name, x-copilotkit-tool-parameters');
    res.status(200).end();
  });

  app.post('/api/copilot/chat', authenticateUser, (req, res) => {
    handleChatCompletion(req, res);
  });

  app.get('/api/copilot/status', (req, res) => {
    const openaiApiKey = process.env.CRM_OPENAI_API_KEY || process.env.OPENAI_API_KEY;
    res.json({
      available: !!openaiApiKey,
      message: openaiApiKey ? undefined : "OpenAI API key is not configured. Some AI features will be limited."
    });
  });



  app.get('/api/copilot/insights',
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 500), // Insights generation uses many tokens
    async (req, res) => {
    try {
      const userId = req.session.userId as number;
      const insights = await generateInsights(userId);
      return res.status(200).json(insights);
    } catch (error) {
      console.error('Error generating insights:', error);
      return res.status(500).json({ error: 'Failed to generate insights' });
    }
  });

  app.get('/api/copilot/enrich/contact/:id', authenticateUser, async (req, res) => {
    try {
      const contactId = parseInt(req.params.id);
      const enrichedData = await enrichContactData(contactId);

      if (!enrichedData) {
        return res.status(404).json({ error: 'Contact not found or failed to enrich data' });
      }

      return res.status(200).json(enrichedData);
    } catch (error) {
      console.error('Error enriching contact data:', error);
      return res.status(500).json({ error: 'Failed to enrich contact data' });
    }
  });

  // Settings routes
  app.post('/api/settings/openai-key', authenticateUser, async (req, res) => {
    try {
      const { apiKey } = req.body;

      if (!apiKey || typeof apiKey !== 'string' || !apiKey.startsWith('sk-')) {
        return res.status(400).json({
          error: 'Invalid API key format. OpenAI API keys should start with "sk-".'
        });
      }

      // Test the API key with a small request
      try {
        const openai = new OpenAI({ apiKey });
        const response = await openai.chat.completions.create({
          model: "gpt-4o",
          messages: [{ role: "user", content: "Test" }],
          max_tokens: 5
        });

        if (response) {
          // Store the API key in environment
          process.env.CRM_OPENAI_API_KEY = apiKey;
          res.json({ success: true, message: "API key verified and saved successfully" });
        } else {
          res.status(400).json({ error: "API key test failed" });
        }
      } catch (error) {
        console.error('Error validating OpenAI API key:', error);
        return res.status(400).json({
          error: error instanceof Error ? error.message : 'Invalid OpenAI API key'
        });
      }
    } catch (error) {
      console.error("Error saving OpenAI API key:", error);
      res.status(500).json({
        error: error instanceof Error ? error.message : "Failed to save API key"
      });
    }
  });

  // Register AI Bridge routes
  registerAIBridgeRoutes(app);

  // Register Subscription routes
  registerSubscriptionRoutes(app, authenticateUser);

  // API status endpoint
  app.get('/api/status', async (req, res) => {
    try {
      let mongodbStatus = false;

      if (useMongoDb) {
        const { testMongoDBConnection } = await import('./mongodb-connection');
        mongodbStatus = await testMongoDBConnection();
      } else {
        mongodbStatus = await testConnection();
      }

      const status = {
        api: {
          openai: !!(process.env.CRM_OPENAI_API_KEY || process.env.OPENAI_API_KEY),
          mongodb: mongodbStatus,
          aiBridge: isAIBridgeAvailable()
        }
      };

      res.json(status);
    } catch (error) {
      console.error('Error checking API status:', error);
      res.status(500).json({ error: 'Failed to check API status' });
    }
  });

  // If we're using MongoDB, we've already returned the httpServer
  if (useMongoDb) {
    return createServer(app);
  }

  const httpServer = createServer(app);
  return httpServer;
}
