/**
 * Firebase Admin SDK initialization
 */
import * as admin from 'firebase-admin';
import { applicationDefault, cert, initializeApp } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';

// Initialize Firebase Admin SDK
let app: admin.app.App;

try {
  // Check if app is already initialized
  app = admin.getApp();
} catch (error) {
  // Initialize app with credentials
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    // Use service account credentials from environment variable
    app = initializeApp({
      credential: applicationDefault(),
    });
  } else {
    try {
      // Use service account credentials from JSON file
      const serviceAccount = require('../kairo-54d5e-firebase-adminsdk.json');
      app = initializeApp({
        credential: cert(serviceAccount),
      });
    } catch (e) {
      // If service account file is not found, initialize with a dummy app
      console.warn('Firebase service account file not found. Initializing with a dummy app.');
      app = initializeApp({
        projectId: 'dummy-project',
      });
    }
  }
}

// Export Firebase Admin SDK services
export const auth = getAuth(app);
export const firestore = getFirestore(app);
export const storage = getStorage(app);

export default app;
