import { PipelineNarratorService } from './pipeline-narrator-service';

/**
 * Scheduler for pipeline monitoring tasks
 */
export class PipelineScheduler {
  private static instance: PipelineScheduler;
  private dailyCheckInterval: NodeJS.Timeout | null = null;
  private weeklyCheckInterval: NodeJS.Timeout | null = null;
  
  private constructor() {
    // Private constructor for singleton pattern
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): PipelineScheduler {
    if (!PipelineScheduler.instance) {
      PipelineScheduler.instance = new PipelineScheduler();
    }
    return PipelineScheduler.instance;
  }
  
  /**
   * Start the scheduler
   */
  public startScheduler(): void {
    this.stopScheduler(); // Clear any existing intervals
    
    // Get the pipeline narrator service
    const pipelineService = PipelineNarratorService.getInstance();
    
    // Schedule daily checks (run every 24 hours)
    this.dailyCheckInterval = setInterval(async () => {
      try {
        console.log('Running daily pipeline checks...');
        await pipelineService.checkStalledOpportunities();
        await pipelineService.checkMeetingsWithoutProposals();
      } catch (error) {
        console.error('Error running daily pipeline checks:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours
    
    // Run immediately on startup
    setTimeout(async () => {
      try {
        console.log('Running initial pipeline checks...');
        await pipelineService.checkStalledOpportunities();
        await pipelineService.checkMeetingsWithoutProposals();
      } catch (error) {
        console.error('Error running initial pipeline checks:', error);
      }
    }, 5000); // 5 seconds after startup
    
    console.log('Pipeline scheduler started');
  }
  
  /**
   * Stop the scheduler
   */
  public stopScheduler(): void {
    if (this.dailyCheckInterval) {
      clearInterval(this.dailyCheckInterval);
      this.dailyCheckInterval = null;
    }
    
    if (this.weeklyCheckInterval) {
      clearInterval(this.weeklyCheckInterval);
      this.weeklyCheckInterval = null;
    }
    
    console.log('Pipeline scheduler stopped');
  }
}
