import mongoose from 'mongoose';
import { logger } from '../utils/logger';
import {
  EventType,
  AnalyticsEvent,
  WizardEvent,
  MeetingPrepEvent,
  ObjectionHandlerEvent,
  ProposalGeneratorEvent,
  BIEvent,
  AttributionEvent
} from '../../shared/types/analytics-events';
import {
  isAnalyticsEvent,
  isWizardEvent,
  isMeetingPrepEvent,
  isObjectionHandlerEvent,
  isProposalGeneratorEvent,
  isBIEvent,
  isAttributionEvent
} from '../../shared/types/guards/analytics-events';

/**
 * Analytics Event Bus Service
 *
 * This service provides a centralized event bus for analytics events
 * across the application. It allows components to publish events and
 * subscribe to events from other components.
 */
export class AnalyticsEventBusService {
  private subscribers: Map<EventType, Array<(event: AnalyticsEvent) => Promise<void>>> = new Map();

  /**
   * Initialize the Analytics Event Bus
   */
  async initialize(): Promise<void> {
    try {
      // Create the Event model if it doesn't exist
      if (!mongoose.models.AnalyticsEvent) {
        const AnalyticsEventSchema = new mongoose.Schema({
          type: { type: String, required: true, index: true },
          tenantId: { type: String, required: true, index: true },
          userId: { type: String, required: true, index: true },
          timestamp: { type: Date, default: Date.now, index: true },
          entityId: { type: String, index: true },
          entityType: { type: String, index: true },
          metadata: { type: mongoose.Schema.Types.Mixed }
        });

        mongoose.model('AnalyticsEvent', AnalyticsEventSchema);
      }

      logger.info('Analytics Event Bus initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Analytics Event Bus:', error);
      throw error;
    }
  }

  /**
   * Publish an event to the Analytics Event Bus
   */
  async publish(event: AnalyticsEvent): Promise<void> {
    try {
      // Set timestamp if not provided
      if (!event.timestamp) {
        event.timestamp = new Date();
      }

      // Store the event in the database
      const AnalyticsEvent = mongoose.model('AnalyticsEvent');
      const savedEvent = await AnalyticsEvent.create(event);

      // Notify subscribers
      const subscribers = this.subscribers.get(event.type) || [];

      // Execute subscribers in parallel
      await Promise.all(
        subscribers.map(subscriber => {
          try {
            return subscriber(savedEvent);
          } catch (error) {
            logger.error(`Error in subscriber for event type ${event.type}:`, error);
            return Promise.resolve();
          }
        })
      );

      logger.debug(`Event published: ${event.type}`, { eventId: savedEvent._id });
    } catch (error) {
      logger.error(`Error publishing event ${event.type}:`, error);
      throw error;
    }
  }

  /**
   * Subscribe to events of a specific type
   */
  subscribe(
    eventType: EventType,
    callback: (event: AnalyticsEvent) => Promise<void>
  ): void {
    const subscribers = this.subscribers.get(eventType) || [];
    subscribers.push(callback);
    this.subscribers.set(eventType, subscribers);

    logger.debug(`Subscribed to event type: ${eventType}`);
  }

  /**
   * Unsubscribe from events of a specific type
   */
  unsubscribe(
    eventType: EventType,
    callback: (event: AnalyticsEvent) => Promise<void>
  ): void {
    const subscribers = this.subscribers.get(eventType) || [];
    const index = subscribers.indexOf(callback);

    if (index !== -1) {
      subscribers.splice(index, 1);
      this.subscribers.set(eventType, subscribers);
      logger.debug(`Unsubscribed from event type: ${eventType}`);
    }
  }

  /**
   * Get events by type and filters
   */
  async getEvents(params: {
    type?: EventType;
    tenantId?: string;
    userId?: string;
    entityId?: string;
    entityType?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    skip?: number;
  }): Promise<AnalyticsEvent[]> {
    try {
      const {
        type,
        tenantId,
        userId,
        entityId,
        entityType,
        startDate,
        endDate,
        limit = 100,
        skip = 0
      } = params;

      // Build query
      const query: any = {};

      if (type) query.type = type;
      if (tenantId) query.tenantId = tenantId;
      if (userId) query.userId = userId;
      if (entityId) query.entityId = entityId;
      if (entityType) query.entityType = entityType;

      if (startDate || endDate) {
        query.timestamp = {};
        if (startDate) query.timestamp.$gte = startDate;
        if (endDate) query.timestamp.$lte = endDate;
      }

      // Execute query
      const AnalyticsEvent = mongoose.model('AnalyticsEvent');
      const events = await AnalyticsEvent.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);

      return events;
    } catch (error) {
      logger.error('Error getting events:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const analyticsEventBus = new AnalyticsEventBusService();
