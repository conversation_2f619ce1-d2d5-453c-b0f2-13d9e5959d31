import mongoose from 'mongoose';
import axios from 'axios';
import { 
  AttributionResults, 
  IAttributionResults,
  EventsRaw,
  IEventsRaw,
  MarketingCampaign,
  IMarketingCampaign
} from '../models/mongoose';
import { 
  AttributionResultsType,
  RunAttributionRequest,
  RunAttributionResponse,
  GetAttributionResultsRequest,
  GetAttributionResultsResponse,
  BudgetOptimizationRequest,
  BudgetOptimizationResponse
} from '@types/analytics-reporting';
import { logger } from '../utils/logger';
import { subscriptionClient } from './subscription-client';

// Environment variables
const AI_BRIDGE_URL = process.env.AI_BRIDGE_URL || 'http://localhost:5000';

/**
 * Service for Attribution AI
 */
export class AttributionService {
  /**
   * Run attribution model
   */
  static async runAttribution(request: RunAttributionRequest): Promise<RunAttributionResponse> {
    try {
      // Validate tenant_id
      if (!request.tenant_id) {
        throw new Error('Tenant ID is required');
      }
      
      // Check if attribution results already exist and force_refresh is not set
      if (!request.force_refresh) {
        const existingResults = await AttributionResults.findOne({
          tenant_id: new mongoose.Types.ObjectId(request.tenant_id),
          window: request.window,
          model_type: request.model_type
        });
        
        if (existingResults) {
          return {
            success: true,
            message: 'Attribution results already exist'
          };
        }
      }
      
      // Record usage
      await subscriptionClient.recordUsage(
        request.tenant_id,
        'analytics.attribution',
        1
      );
      
      // Try to use AI Bridge if available
      try {
        const response = await axios.post(`${AI_BRIDGE_URL}/api/attribution/run`, {
          window: request.window,
          model_type: request.model_type,
          tenant_id: request.tenant_id
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.data && response.data.success) {
          return {
            success: true,
            job_id: response.data.job_id,
            message: 'Attribution job started'
          };
        }
      } catch (error) {
        logger.warn('Error using AI Bridge for attribution, falling back to direct calculation:', error);
      }
      
      // Fall back to direct calculation
      // This is a simplified version - in a real implementation, this would be a background job
      await this.calculateAttribution(request.tenant_id, request.window, request.model_type);
      
      return {
        success: true,
        message: 'Attribution calculation completed'
      };
    } catch (error: any) {
      logger.error('Error running attribution:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }
  
  /**
   * Calculate attribution (simplified version)
   */
  private static async calculateAttribution(
    tenant_id: string,
    window: string,
    model_type: 'markov' | 'shapley' | 'first_touch' | 'last_touch' | 'linear'
  ): Promise<void> {
    try {
      // Get date range for window
      const { startDate, endDate } = this.getDateRangeForWindow(window);
      
      // Get events for the window
      const events = await EventsRaw.find({
        tenant_id: new mongoose.Types.ObjectId(tenant_id),
        timestamp: {
          $gte: startDate,
          $lte: endDate
        }
      }).sort({ timestamp: 1 });
      
      // Get marketing campaigns
      const campaigns = await MarketingCampaign.find({
        tenant_id: new mongoose.Types.ObjectId(tenant_id),
        start_date: { $lte: endDate },
        $or: [
          { end_date: { $gte: startDate } },
          { end_date: null }
        ]
      });
      
      // Group events by channel
      const channelEvents: Record<string, IEventsRaw[]> = {};
      const channelCosts: Record<string, number> = {};
      const channelConversions: Record<string, number> = {};
      const channelRevenue: Record<string, number> = {};
      
      // Initialize with campaigns
      campaigns.forEach(campaign => {
        if (!channelEvents[campaign.channel]) {
          channelEvents[campaign.channel] = [];
          channelCosts[campaign.channel] = 0;
          channelConversions[campaign.channel] = 0;
          channelRevenue[campaign.channel] = 0;
        }
        
        // Add campaign costs
        channelCosts[campaign.channel] += campaign.total_cost;
      });
      
      // Group events by channel
      events.forEach(event => {
        if (!channelEvents[event.channel]) {
          channelEvents[event.channel] = [];
          channelCosts[event.channel] = 0;
          channelConversions[event.channel] = 0;
          channelRevenue[event.channel] = 0;
        }
        
        channelEvents[event.channel].push(event);
        
        // Count conversions and revenue
        if (event.event_type === 'conversion') {
          channelConversions[event.channel]++;
          
          // Add revenue if available in meta_json
          if (event.meta_json && event.meta_json.revenue) {
            channelRevenue[event.channel] += parseFloat(event.meta_json.revenue);
          }
        }
      });
      
      // Calculate attribution based on model type
      const results: IAttributionResults[] = [];
      
      // Delete existing results
      await AttributionResults.deleteMany({
        tenant_id: new mongoose.Types.ObjectId(tenant_id),
        window,
        model_type
      });
      
      // Calculate attribution for each channel
      for (const channel of Object.keys(channelEvents)) {
        let creditPct = 0;
        
        if (model_type === 'first_touch') {
          // Simplified first-touch attribution
          creditPct = channelEvents[channel].some(e => e.event_type === 'first_visit') ? 100 : 0;
        } else if (model_type === 'last_touch') {
          // Simplified last-touch attribution
          creditPct = channelEvents[channel].some(e => e.event_type === 'conversion') ? 100 : 0;
        } else if (model_type === 'linear') {
          // Simplified linear attribution
          creditPct = Object.keys(channelEvents).length > 0 ? 100 / Object.keys(channelEvents).length : 0;
        } else {
          // For Markov and Shapley, we'd need more complex calculations
          // This is a placeholder - in a real implementation, these would use proper algorithms
          creditPct = channelConversions[channel] > 0 ? 
            (channelConversions[channel] / Object.values(channelConversions).reduce((sum, val) => sum + val, 0)) * 100 : 0;
        }
        
        // Create attribution result
        const result = new AttributionResults({
          tenant_id: new mongoose.Types.ObjectId(tenant_id),
          window,
          model_type,
          channel,
          credit_pct: creditPct,
          cost: channelCosts[channel] || 0,
          revenue: channelRevenue[channel] || 0,
          roi: channelCosts[channel] > 0 ? (channelRevenue[channel] / channelCosts[channel]) : 0,
          conversions: channelConversions[channel] || 0
        });
        
        results.push(result);
      }
      
      // Save results
      if (results.length > 0) {
        await AttributionResults.insertMany(results);
      }
    } catch (error) {
      logger.error('Error calculating attribution:', error);
      throw error;
    }
  }
  
  /**
   * Get date range for window
   */
  private static getDateRangeForWindow(window: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    let startDate = new Date();
    
    if (window === 'last_7_days') {
      startDate.setDate(startDate.getDate() - 7);
    } else if (window === 'last_30_days') {
      startDate.setDate(startDate.getDate() - 30);
    } else if (window === 'last_90_days') {
      startDate.setDate(startDate.getDate() - 90);
    } else if (window === 'current_month') {
      startDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
    } else if (window === 'current_quarter') {
      const quarter = Math.floor(startDate.getMonth() / 3);
      startDate = new Date(startDate.getFullYear(), quarter * 3, 1);
    } else if (window === 'current_year') {
      startDate = new Date(startDate.getFullYear(), 0, 1);
    } else {
      // Default to last 30 days
      startDate.setDate(startDate.getDate() - 30);
    }
    
    return { startDate, endDate };
  }
  
  /**
   * Get attribution results
   */
  static async getAttributionResults(request: GetAttributionResultsRequest): Promise<GetAttributionResultsResponse> {
    try {
      // Validate tenant_id
      if (!request.tenant_id) {
        throw new Error('Tenant ID is required');
      }
      
      // Build query
      const query: any = {
        tenant_id: new mongoose.Types.ObjectId(request.tenant_id),
        window: request.window,
        model_type: request.model_type
      };
      
      // Get results
      const results = await AttributionResults.find(query);
      
      if (results.length === 0) {
        return {
          success: true,
          results: [],
          summary: {
            total_cost: 0,
            total_revenue: 0,
            total_conversions: 0,
            average_roi: 0
          },
          message: 'No attribution results found'
        };
      }
      
      // Calculate summary
      const summary = {
        total_cost: results.reduce((sum, result) => sum + result.cost, 0),
        total_revenue: results.reduce((sum, result) => sum + result.revenue, 0),
        total_conversions: results.reduce((sum, result) => sum + result.conversions, 0),
        average_roi: 0
      };
      
      summary.average_roi = summary.total_cost > 0 ? summary.total_revenue / summary.total_cost : 0;
      
      // Map to type
      const mappedResults = results.map(result => ({
        id: result._id.toString(),
        tenant_id: result.tenant_id.toString(),
        window: result.window,
        model_type: result.model_type,
        channel: result.channel,
        campaign: result.campaign,
        medium: result.medium,
        source: result.source,
        creative: result.creative,
        keyword: result.keyword,
        credit_pct: result.credit_pct,
        cost: result.cost,
        revenue: result.revenue,
        roi: result.roi,
        conversions: result.conversions,
        created_at: result.created_at.toISOString(),
        updated_at: result.updated_at.toISOString()
      }));
      
      return {
        success: true,
        results: mappedResults,
        summary
      };
    } catch (error: any) {
      logger.error('Error getting attribution results:', error);
      return {
        success: false,
        results: [],
        summary: {
          total_cost: 0,
          total_revenue: 0,
          total_conversions: 0,
          average_roi: 0
        },
        message: error.message
      };
    }
  }
}
