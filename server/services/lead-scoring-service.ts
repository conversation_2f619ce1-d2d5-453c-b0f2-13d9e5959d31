/**
 * Lead Scoring Service
 * 
 * This service handles lead scoring operations using the Neo4j graph database.
 */

import { Contact, Interaction, Opportunity } from '../models/mongoose';
import { neo4jService } from './neo4j-service';
import { logger } from '../utils/logger';

interface LeadScore {
  current: number;
  previous?: number;
  change?: number;
  lastUpdated: Date;
  factors: {
    interactions: number;
    sentiment: number;
    opportunities: number;
    engagement: number;
    recency: number;
    similarContacts: number;
  };
  conversionProbability: number;
}

class LeadScoringService {
  /**
   * Initialize the lead scoring service
   */
  async initialize(): Promise<void> {
    try {
      await neo4jService.initialize();
      logger.info('Lead scoring service initialized');
    } catch (error) {
      logger.error('Failed to initialize lead scoring service:', error);
      throw error;
    }
  }

  /**
   * Sync a contact to the graph database
   */
  async syncContact(contactId: string, tenantId: string): Promise<void> {
    try {
      const contact = await Contact.findById(contactId);
      if (!contact) {
        throw new Error(`Contact not found: ${contactId}`);
      }

      await neo4jService.syncContact(contact, tenantId);
      logger.info(`Contact ${contactId} synced to graph database`);
    } catch (error) {
      logger.error(`Error syncing contact ${contactId} to graph database:`, error);
      throw error;
    }
  }

  /**
   * Sync an interaction to the graph database
   */
  async syncInteraction(interactionId: string, tenantId: string): Promise<void> {
    try {
      const interaction = await Interaction.findById(interactionId);
      if (!interaction) {
        throw new Error(`Interaction not found: ${interactionId}`);
      }

      await neo4jService.syncInteraction(interaction, tenantId);
      logger.info(`Interaction ${interactionId} synced to graph database`);
    } catch (error) {
      logger.error(`Error syncing interaction ${interactionId} to graph database:`, error);
      throw error;
    }
  }

  /**
   * Sync an opportunity to the graph database
   */
  async syncOpportunity(opportunityId: string, tenantId: string): Promise<void> {
    try {
      const opportunity = await Opportunity.findById(opportunityId);
      if (!opportunity) {
        throw new Error(`Opportunity not found: ${opportunityId}`);
      }

      await neo4jService.syncOpportunity(opportunity, tenantId);
      logger.info(`Opportunity ${opportunityId} synced to graph database`);
    } catch (error) {
      logger.error(`Error syncing opportunity ${opportunityId} to graph database:`, error);
      throw error;
    }
  }

  /**
   * Calculate lead score for a contact
   */
  async calculateLeadScore(contactId: string, tenantId: string): Promise<LeadScore> {
    try {
      // Get the contact
      const contact = await Contact.findById(contactId);
      if (!contact) {
        throw new Error(`Contact not found: ${contactId}`);
      }

      // Get the current score if it exists
      const previousScore = contact.score?.current;

      // Calculate the new score using Neo4j
      const score = await neo4jService.calculateLeadScore(contactId, tenantId);
      
      // Calculate conversion probability
      const conversionProbability = await neo4jService.getLeadConversionProbability(contactId, tenantId);

      // Get interactions for factor calculation
      const interactions = await Interaction.find({ contactId });
      
      // Calculate score factors
      const factors = this.calculateScoreFactors(interactions);
      
      // Create the lead score object
      const leadScore: LeadScore = {
        current: score,
        previous: previousScore,
        change: previousScore !== undefined ? score - previousScore : undefined,
        lastUpdated: new Date(),
        factors,
        conversionProbability: conversionProbability * 100, // Convert to percentage
      };

      // Update the contact with the new score
      await Contact.findByIdAndUpdate(contactId, { score: leadScore });

      logger.info(`Lead score calculated for contact ${contactId}: ${score}`);
      return leadScore;
    } catch (error) {
      logger.error(`Error calculating lead score for contact ${contactId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate score factors based on interactions
   */
  private calculateScoreFactors(interactions: any[]): LeadScore['factors'] {
    // Count interactions by type
    const interactionCount = interactions.length;
    
    // Calculate sentiment factor
    const sentimentCounts = {
      positive: 0,
      neutral: 0,
      negative: 0,
      mixed: 0,
    };
    
    interactions.forEach(interaction => {
      if (interaction.sentiment) {
        sentimentCounts[interaction.sentiment as keyof typeof sentimentCounts]++;
      }
    });
    
    const sentimentFactor = Math.min(
      100,
      (sentimentCounts.positive * 5 + 
       sentimentCounts.neutral * 2 - 
       sentimentCounts.negative * 3 + 
       sentimentCounts.mixed * 1) / 
      Math.max(1, interactionCount) * 20
    );
    
    // Calculate engagement factor
    const engagementFactor = Math.min(
      100,
      interactions.reduce((sum, interaction) => {
        let value = 0;
        switch (interaction.type) {
          case 'meeting':
            value = 10;
            break;
          case 'call':
            value = 8;
            break;
          case 'email':
            value = interaction.direction === 'inbound' ? 7 : 5;
            break;
          case 'social':
            value = 4;
            break;
          case 'sms':
            value = 3;
            break;
          default:
            value = 2;
        }
        return sum + value;
      }, 0) / Math.max(1, interactionCount) * 10
    );
    
    // Calculate recency factor
    const now = new Date();
    const recencyFactor = Math.min(
      100,
      interactions.reduce((sum, interaction) => {
        const interactionDate = new Date(interaction.timestamp);
        const daysDiff = Math.floor((now.getTime() - interactionDate.getTime()) / (1000 * 60 * 60 * 24));
        
        let value = 0;
        if (daysDiff < 7) {
          value = 10;
        } else if (daysDiff < 30) {
          value = 7;
        } else if (daysDiff < 90) {
          value = 4;
        } else if (daysDiff < 180) {
          value = 2;
        } else {
          value = 1;
        }
        
        return sum + value;
      }, 0) / Math.max(1, interactionCount) * 10
    );
    
    return {
      interactions: Math.min(100, interactionCount * 5),
      sentiment: sentimentFactor,
      opportunities: 0, // This will be calculated by Neo4j
      engagement: engagementFactor,
      recency: recencyFactor,
      similarContacts: 0, // This will be calculated by Neo4j
    };
  }

  /**
   * Get similar contacts for a contact
   */
  async getSimilarContacts(contactId: string, tenantId: string, limit: number = 5): Promise<any[]> {
    try {
      const similarContacts = await neo4jService.getSimilarContacts(contactId, tenantId, limit);
      return similarContacts;
    } catch (error) {
      logger.error(`Error getting similar contacts for ${contactId}:`, error);
      throw error;
    }
  }

  /**
   * Batch update lead scores for all contacts in a tenant
   */
  async batchUpdateLeadScores(tenantId: string): Promise<{ updated: number; errors: number }> {
    try {
      const contacts = await Contact.find({ tenantId });
      
      let updated = 0;
      let errors = 0;
      
      for (const contact of contacts) {
        try {
          await this.calculateLeadScore(contact._id.toString(), tenantId);
          updated++;
        } catch (error) {
          logger.error(`Error updating lead score for contact ${contact._id}:`, error);
          errors++;
        }
      }
      
      logger.info(`Batch update completed for tenant ${tenantId}: ${updated} updated, ${errors} errors`);
      return { updated, errors };
    } catch (error) {
      logger.error(`Error in batch update for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Sync all data for a tenant to the graph database
   */
  async syncAllData(tenantId: string): Promise<{ contacts: number; interactions: number; opportunities: number; errors: number }> {
    try {
      const contacts = await Contact.find({ tenantId });
      const interactions = await Interaction.find({ tenantId });
      const opportunities = await Opportunity.find({ tenantId });
      
      let syncedContacts = 0;
      let syncedInteractions = 0;
      let syncedOpportunities = 0;
      let errors = 0;
      
      // Sync contacts
      for (const contact of contacts) {
        try {
          await neo4jService.syncContact(contact, tenantId);
          syncedContacts++;
        } catch (error) {
          logger.error(`Error syncing contact ${contact._id}:`, error);
          errors++;
        }
      }
      
      // Sync interactions
      for (const interaction of interactions) {
        try {
          await neo4jService.syncInteraction(interaction, tenantId);
          syncedInteractions++;
        } catch (error) {
          logger.error(`Error syncing interaction ${interaction._id}:`, error);
          errors++;
        }
      }
      
      // Sync opportunities
      for (const opportunity of opportunities) {
        try {
          await neo4jService.syncOpportunity(opportunity, tenantId);
          syncedOpportunities++;
        } catch (error) {
          logger.error(`Error syncing opportunity ${opportunity._id}:`, error);
          errors++;
        }
      }
      
      logger.info(`Sync completed for tenant ${tenantId}: ${syncedContacts} contacts, ${syncedInteractions} interactions, ${syncedOpportunities} opportunities, ${errors} errors`);
      return { 
        contacts: syncedContacts, 
        interactions: syncedInteractions, 
        opportunities: syncedOpportunities, 
        errors 
      };
    } catch (error) {
      logger.error(`Error in sync all data for tenant ${tenantId}:`, error);
      throw error;
    }
  }
}

export const leadScoringService = new LeadScoringService();
