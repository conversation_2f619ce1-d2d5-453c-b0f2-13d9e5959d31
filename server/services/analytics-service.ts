import { AnalyticsEvent, IAnalyticsEvent, mapAnalyticsEventToType } from '../models/mongoose/analytics-event-model';
import mongoose from 'mongoose';
import { Request } from 'express';
import {
  AnalyticsEvent as AnalyticsEventType,
  AnalyticsContext,
  AnalyticsSession,
  AnalyticsCount,
  FunnelStep,
  TrackEventRequest
} from '@types/analytics';
import { isAnalyticsContext } from '@types/guards/analytics';

export interface AnalyticsEventData extends TrackEventRequest {
  userId?: string;
  tenantId?: string;
  sessionId?: string;
  timestamp?: Date;
}

export class AnalyticsService {
  /**
   * Track an analytics event
   */
  static async trackEvent(data: AnalyticsEventData): Promise<AnalyticsEventType> {
    const {
      eventType,
      eventName,
      properties = {},
      userId,
      tenantId,
      sessionId,
      timestamp = new Date(),
      context = {},
    } = data;

    // Validate context
    if (context && !isAnalyticsContext(context)) {
      throw new Error('Invalid analytics context');
    }

    const event = new AnalyticsEvent({
      eventType,
      eventName,
      properties,
      userId: userId ? new mongoose.Types.ObjectId(userId) : undefined,
      tenantId: tenantId ? new mongoose.Types.ObjectId(tenantId) : undefined,
      sessionId: sessionId || this.generateSessionId(),
      timestamp,
      context,
    });

    const savedEvent = await event.save();
    return mapAnalyticsEventToType(savedEvent);
  }

  /**
   * Track an analytics event from a request
   */
  static async trackEventFromRequest(
    req: Request,
    eventType: string,
    eventName: string,
    properties: Record<string, any> = {}
  ): Promise<IAnalyticsEvent> {
    const context = this.getContextFromRequest(req);

    return this.trackEvent({
      eventType,
      eventName,
      properties,
      userId: req.session?.userId,
      tenantId: req.session?.tenantId,
      sessionId: req.sessionID || req.headers['x-session-id'] as string || this.generateSessionId(),
      context,
    });
  }

  /**
   * Get analytics events
   */
  static async getEvents(options: {
    userId?: string;
    tenantId?: string;
    sessionId?: string;
    eventType?: string;
    eventName?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}): Promise<AnalyticsEventType[]> {
    const {
      userId,
      tenantId,
      sessionId,
      eventType,
      eventName,
      startDate,
      endDate,
      limit = 100,
      offset = 0,
    } = options;

    const query: any = {};

    if (userId) {
      query.userId = new mongoose.Types.ObjectId(userId);
    }

    if (tenantId) {
      query.tenantId = new mongoose.Types.ObjectId(tenantId);
    }

    if (sessionId) {
      query.sessionId = sessionId;
    }

    if (eventType) {
      query.eventType = eventType;
    }

    if (eventName) {
      query.eventName = eventName;
    }

    if (startDate || endDate) {
      query.timestamp = {};

      if (startDate) {
        query.timestamp.$gte = startDate;
      }

      if (endDate) {
        query.timestamp.$lte = endDate;
      }
    }

    const events = await AnalyticsEvent.find(query)
      .sort({ timestamp: -1 })
      .skip(offset)
      .limit(limit);

    return events.map(mapAnalyticsEventToType);
  }

  /**
   * Get event counts by type and name
   */
  static async getEventCounts(options: {
    userId?: string;
    tenantId?: string;
    eventType?: string;
    startDate?: Date;
    endDate?: Date;
    groupBy?: 'eventType' | 'eventName' | 'day' | 'week' | 'month';
  } = {}): Promise<{ _id: string; count: number }[]> {
    const {
      userId,
      tenantId,
      eventType,
      startDate,
      endDate,
      groupBy = 'eventName',
    } = options;

    const match: any = {};

    if (userId) {
      match.userId = new mongoose.Types.ObjectId(userId);
    }

    if (tenantId) {
      match.tenantId = new mongoose.Types.ObjectId(tenantId);
    }

    if (eventType) {
      match.eventType = eventType;
    }

    if (startDate || endDate) {
      match.timestamp = {};

      if (startDate) {
        match.timestamp.$gte = startDate;
      }

      if (endDate) {
        match.timestamp.$lte = endDate;
      }
    }

    const groupByField = this.getGroupByField(groupBy);

    const pipeline = [
      { $match: match },
      {
        $group: {
          _id: groupByField,
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
    ];

    return AnalyticsEvent.aggregate(pipeline);
  }

  /**
   * Get user sessions
   */
  static async getUserSessions(options: {
    userId?: string;
    tenantId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    sessionId: string;
    userId?: string;
    tenantId?: string;
    startTime: Date;
    endTime: Date;
    duration: number;
    eventCount: number;
    pages: string[];
  }[]> {
    const {
      userId,
      tenantId,
      startDate,
      endDate,
      limit = 100,
      offset = 0,
    } = options;

    const match: any = {};

    if (userId) {
      match.userId = new mongoose.Types.ObjectId(userId);
    }

    if (tenantId) {
      match.tenantId = new mongoose.Types.ObjectId(tenantId);
    }

    if (startDate || endDate) {
      match.timestamp = {};

      if (startDate) {
        match.timestamp.$gte = startDate;
      }

      if (endDate) {
        match.timestamp.$lte = endDate;
      }
    }

    const pipeline = [
      { $match: match },
      {
        $group: {
          _id: '$sessionId',
          userId: { $first: '$userId' },
          tenantId: { $first: '$tenantId' },
          startTime: { $min: '$timestamp' },
          endTime: { $max: '$timestamp' },
          eventCount: { $sum: 1 },
          pages: { $addToSet: '$context.page.path' },
        },
      },
      {
        $project: {
          sessionId: '$_id',
          userId: 1,
          tenantId: 1,
          startTime: 1,
          endTime: 1,
          duration: { $subtract: ['$endTime', '$startTime'] },
          eventCount: 1,
          pages: 1,
        },
      },
      { $sort: { startTime: -1 } },
      { $skip: offset },
      { $limit: limit },
    ];

    return AnalyticsEvent.aggregate(pipeline);
  }

  /**
   * Get funnel analysis
   */
  static async getFunnelAnalysis(options: {
    steps: { eventType: string; eventName: string }[];
    userId?: string;
    tenantId?: string;
    startDate?: Date;
    endDate?: Date;
    windowHours?: number;
  }): Promise<{
    step: number;
    eventType: string;
    eventName: string;
    count: number;
    dropoff: number;
    conversionRate: number;
  }[]> {
    const {
      steps,
      userId,
      tenantId,
      startDate,
      endDate,
      windowHours = 24,
    } = options;

    if (!steps || steps.length < 2) {
      throw new Error('Funnel analysis requires at least 2 steps');
    }

    // Get all events for the funnel
    const events = await this.getEvents({
      userId,
      tenantId,
      startDate,
      endDate,
      limit: 10000, // Large limit to get all events
    });

    // Group events by session
    const sessionEvents: Record<string, IAnalyticsEvent[]> = {};

    for (const event of events) {
      if (!sessionEvents[event.sessionId]) {
        sessionEvents[event.sessionId] = [];
      }

      sessionEvents[event.sessionId].push(event);
    }

    // Count users who completed each step
    const stepCounts: number[] = Array(steps.length).fill(0);

    for (const sessionId in sessionEvents) {
      const sessionEventsSorted = sessionEvents[sessionId].sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
      );

      let lastStepTime: Date | null = null;
      let currentStep = 0;

      for (const event of sessionEventsSorted) {
        // Check if this event matches the current step
        if (
          event.eventType === steps[currentStep].eventType &&
          event.eventName === steps[currentStep].eventName
        ) {
          // If this is the first step or within the time window
          if (
            currentStep === 0 ||
            (lastStepTime &&
              event.timestamp.getTime() - lastStepTime.getTime() <= windowHours * 60 * 60 * 1000)
          ) {
            stepCounts[currentStep]++;
            lastStepTime = event.timestamp;
            currentStep++;

            // If we've completed all steps, we're done with this session
            if (currentStep >= steps.length) {
              break;
            }
          }
        }
      }
    }

    // Calculate conversion rates and dropoffs
    const results = steps.map((step, index) => {
      const count = stepCounts[index];
      const previousCount = index > 0 ? stepCounts[index - 1] : count;
      const dropoff = index > 0 ? previousCount - count : 0;
      const conversionRate = previousCount > 0 ? (count / previousCount) * 100 : 100;

      return {
        step: index + 1,
        eventType: step.eventType,
        eventName: step.eventName,
        count,
        dropoff,
        conversionRate,
      };
    });

    return results;
  }

  /**
   * Get context from request
   */
  static getContextFromRequest(req: Request): AnalyticsEventData['context'] {
    return {
      page: {
        url: req.headers.referer,
        path: req.path,
        title: req.headers['x-page-title'] as string,
        referrer: req.headers.referer,
      },
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      location: {
        country: req.headers['cf-ipcountry'] as string,
        region: req.headers['cf-region'] as string,
        city: req.headers['cf-city'] as string,
      },
      device: {
        type: this.getDeviceType(req.headers['user-agent'] as string),
        browser: this.getBrowser(req.headers['user-agent'] as string),
        os: this.getOS(req.headers['user-agent'] as string),
      },
    };
  }

  /**
   * Get group by field for aggregation
   */
  private static getGroupByField(groupBy: string): any {
    switch (groupBy) {
      case 'eventType':
        return '$eventType';

      case 'eventName':
        return '$eventName';

      case 'day':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' },
        };

      case 'week':
        return {
          year: { $year: '$timestamp' },
          week: { $week: '$timestamp' },
        };

      case 'month':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
        };

      default:
        return '$eventName';
    }
  }

  /**
   * Generate a random session ID
   */
  private static generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Get device type from user agent
   */
  private static getDeviceType(userAgent?: string): string | undefined {
    if (!userAgent) return undefined;

    if (/mobile/i.test(userAgent)) {
      return 'mobile';
    } else if (/tablet/i.test(userAgent)) {
      return 'tablet';
    } else if (/ipad/i.test(userAgent)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /**
   * Get browser from user agent
   */
  private static getBrowser(userAgent?: string): string | undefined {
    if (!userAgent) return undefined;

    if (/chrome/i.test(userAgent)) {
      return 'chrome';
    } else if (/firefox/i.test(userAgent)) {
      return 'firefox';
    } else if (/safari/i.test(userAgent)) {
      return 'safari';
    } else if (/edge/i.test(userAgent)) {
      return 'edge';
    } else if (/opera/i.test(userAgent) || /opr/i.test(userAgent)) {
      return 'opera';
    } else if (/msie/i.test(userAgent) || /trident/i.test(userAgent)) {
      return 'ie';
    } else {
      return 'other';
    }
  }

  /**
   * Get OS from user agent
   */
  private static getOS(userAgent?: string): string | undefined {
    if (!userAgent) return undefined;

    if (/windows/i.test(userAgent)) {
      return 'windows';
    } else if (/macintosh|mac os x/i.test(userAgent)) {
      return 'mac';
    } else if (/linux/i.test(userAgent)) {
      return 'linux';
    } else if (/android/i.test(userAgent)) {
      return 'android';
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      return 'ios';
    } else {
      return 'other';
    }
  }
}
