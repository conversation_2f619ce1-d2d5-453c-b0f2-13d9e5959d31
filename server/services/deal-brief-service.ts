import { Opportunity, Contact, Company, Activity, IOpportunity, IContact, ICompany, IActivity } from '../models/mongoose';
import mongoose from 'mongoose';
import { VectorEmbeddingService } from './vector-embedding-service';
import axios from 'axios';

// Initialize Voyage AI client for text generation
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY;
const VOYAGE_API_URL = 'https://api.voyageai.com/v1/chat/completions';

// Define the deal brief structure
export interface DealBrief {
  summary: string;
  sentimentTrend: string;
  keyObjections: string[];
  nextBestAction: string;
  relevantActivities: Array<{
    id: string;
    title: string;
    type: string;
    date: Date;
    snippet: string;
  }>;
  relevantDocuments: Array<{
    id: string;
    name: string;
    type: string;
    snippet: string;
  }>;
}

/**
 * Service for generating deal briefs
 */
export class DealBriefService {
  private static instance: DealBriefService;
  private vectorService: VectorEmbeddingService;

  private constructor() {
    // Initialize vector embedding service
    this.vectorService = VectorEmbeddingService.getInstance();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): DealBriefService {
    if (!DealBriefService.instance) {
      DealBriefService.instance = new DealBriefService();
    }
    return DealBriefService.instance;
  }

  /**
   * Generate a deal brief for an opportunity
   */
  public async generateDealBrief(opportunityId: string): Promise<DealBrief> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Get opportunity details
      const opportunity = await Opportunity.findById(opportunityObjectId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      // Get related data
      const [contact, company, activities] = await Promise.all([
        opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
        opportunity.companyId ? Company.findById(opportunity.companyId) : null,
        Activity.find({ opportunityId: opportunityObjectId })
          .sort({ date: -1 })
          .limit(20)
      ]);

      // Find relevant content using vector search
      const relevantContent = await this.findRelevantContent(opportunityId);

      // Generate the deal brief using AI
      const dealBrief = await this.generateBriefWithAI(
        opportunity,
        contact,
        company,
        activities,
        relevantContent
      );

      return dealBrief;
    } catch (error) {
      console.error('Error generating deal brief:', error);
      throw error;
    }
  }

  /**
   * Find relevant content for the opportunity using vector search
   */
  private async findRelevantContent(opportunityId: string): Promise<any[]> {
    try {
      // Define search queries for different aspects of the deal
      const queries = [
        'deal status summary',
        'customer sentiment',
        'objections concerns issues',
        'next steps action items',
        'pricing discussion',
        'timeline deadlines',
        'decision makers stakeholders'
      ];

      // Run vector searches for each query
      const searchResults = await Promise.all(
        queries.map(async (query) => {
          return this.vectorService.findSimilarContent(opportunityId, query, 3);
        })
      );

      // Flatten and deduplicate results
      const allResults = searchResults.flat();
      const uniqueResults = Array.from(
        new Map(allResults.map(item => [item.id, item])).values()
      );

      return uniqueResults;
    } catch (error) {
      console.error('Error finding relevant content:', error);
      return [];
    }
  }

  /**
   * Generate the deal brief using Voyage AI
   */
  private async generateBriefWithAI(
    opportunity: IOpportunity,
    contact: IContact | null,
    company: ICompany | null,
    activities: IActivity[],
    relevantContent: any[]
  ): Promise<DealBrief> {
    try {
      // Prepare data for Voyage AI
      const data = {
        opportunity: {
          id: opportunity._id.toString(),
          name: opportunity.name,
          value: opportunity.value,
          currency: opportunity.currency,
          stage: opportunity.stage,
          probability: opportunity.probability,
          expectedCloseDate: opportunity.expectedCloseDate,
          description: opportunity.description,
          tags: opportunity.tags,
          createdAt: opportunity.createdAt,
          updatedAt: opportunity.updatedAt
        },
        contact: contact ? {
          id: contact._id.toString(),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title
        } : null,
        company: company ? {
          id: company._id.toString(),
          name: company.name,
          industry: company.industry,
          size: company.size
        } : null,
        activities: activities.map(activity => ({
          id: activity._id.toString(),
          type: activity.type,
          title: activity.title,
          date: activity.date,
          completed: activity.completed,
          notes: activity.notes
        })),
        relevantContent: relevantContent.map(item => ({
          id: item.id,
          content: item.content,
          similarity: item.similarity,
          metadata: item.metadata
        }))
      };

      // Create prompt for Voyage AI
      const prompt = `
        Generate a comprehensive deal brief for this opportunity.

        Analyze all the provided information including the opportunity details, contact, company, activities, and relevant content.

        The brief should include:
        1. A concise TL;DR summary of the deal status (2-3 sentences)
        2. An analysis of the sentiment trend (positive, negative, neutral, or mixed with explanation)
        3. A list of key objections or concerns raised by the prospect
        4. A clear next best action recommendation
        5. References to the most relevant activities and documents

        Format your response as a JSON object with the following structure:
        {
          "summary": "Concise deal summary...",
          "sentimentTrend": "Analysis of sentiment trend...",
          "keyObjections": ["Objection 1", "Objection 2", ...],
          "nextBestAction": "Specific recommendation for next step...",
          "relevantActivities": [
            {
              "id": "activity-id",
              "title": "Activity title",
              "type": "call/email/meeting",
              "date": "ISO date",
              "snippet": "Brief relevant excerpt"
            }
          ],
          "relevantDocuments": [
            {
              "id": "document-id",
              "name": "Document name",
              "type": "Document type",
              "snippet": "Brief relevant excerpt"
            }
          ]
        }
      `;

      // Check if Voyage API key is configured
      if (!VOYAGE_API_KEY) {
        throw new Error('VOYAGE_API_KEY is not configured');
      }

      // Call Voyage AI API
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales analyst for Aizako CRM.
              Your job is to analyze deal information and provide concise, actionable deal briefs.
              Focus on extracting key insights that help sales reps close more deals.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.7,
          max_tokens: 1500,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      // Parse the response
      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content in Voyage AI response');
      }

      const parsedResponse = JSON.parse(content);

      // Validate the response structure
      if (!parsedResponse.summary || !parsedResponse.sentimentTrend ||
          !Array.isArray(parsedResponse.keyObjections) || !parsedResponse.nextBestAction) {
        throw new Error('Invalid response structure from Voyage AI');
      }

      // Return the deal brief
      return {
        summary: parsedResponse.summary,
        sentimentTrend: parsedResponse.sentimentTrend,
        keyObjections: parsedResponse.keyObjections,
        nextBestAction: parsedResponse.nextBestAction,
        relevantActivities: parsedResponse.relevantActivities || [],
        relevantDocuments: parsedResponse.relevantDocuments || []
      };
    } catch (error) {
      console.error('Error generating brief with AI:', error);

      // Return a fallback brief if AI generation fails
      return {
        summary: 'Unable to generate summary due to an error.',
        sentimentTrend: 'Unable to analyze sentiment due to an error.',
        keyObjections: ['Unable to identify objections due to an error.'],
        nextBestAction: 'Review opportunity details manually and follow up with the prospect.',
        relevantActivities: [],
        relevantDocuments: []
      };
    }
  }
}

export default DealBriefService;
