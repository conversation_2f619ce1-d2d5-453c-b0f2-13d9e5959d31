/**
 * Tenant Domain Service
 * 
 * This service handles domain management for tenants including:
 * - Domain verification with Resend
 * - Tracking domain setup
 * - Webhook secret management
 */

import crypto from 'crypto';
import dns from 'dns';
import { promisify } from 'util';
import { 
  TenantDomain, 
  ITenantDomain, 
  DNSRecordType,
  DomainVerificationStatus,
  IDNSRecord
} from '../models/mongoose/tenant-domain-model';
import resendService from './resend-service';
import { logger } from '../utils/logger';

// Promisify DNS functions
const resolveTxt = promisify(dns.resolveTxt);
const resolveCname = promisify(dns.resolveCname);

class TenantDomainService {
  /**
   * Register a new domain for a tenant
   */
  async registerDomain(tenantId: string, domain: string, isDefault: boolean = false): Promise<ITenantDomain> {
    try {
      // Check if domain already exists for this tenant
      const existingDomain = await TenantDomain.findOne({
        tenantId,
        domain
      });

      if (existingDomain) {
        throw new Error(`Domain ${domain} is already registered for this tenant`);
      }

      // Register domain with Resend
      const resendDomain = await resendService.createDomain(domain);

      if (!resendDomain.success) {
        throw new Error(`Failed to register domain with Resend: ${resendDomain.error}`);
      }

      // If this is the default domain, unset any existing default
      if (isDefault) {
        await TenantDomain.updateMany(
          { tenantId, isDefault: true },
          { $set: { isDefault: false } }
        );
      }

      // Create verification records from Resend response
      const verificationRecords: IDNSRecord[] = [];

      // Add DKIM record
      if (resendDomain.data?.dkim) {
        verificationRecords.push({
          type: DNSRecordType.CNAME,
          host: resendDomain.data.dkim.host,
          value: resendDomain.data.dkim.value
        });
      }

      // Add SPF record
      if (resendDomain.data?.spf) {
        verificationRecords.push({
          type: DNSRecordType.TXT,
          host: resendDomain.data.spf.host,
          value: resendDomain.data.spf.value
        });
      }

      // Generate a webhook secret for this domain
      const webhookSecret = this.generateWebhookSecret();

      // Create the tenant domain record
      const tenantDomain = new TenantDomain({
        tenantId,
        domain,
        resendDomainId: resendDomain.data?.id,
        verificationStatus: DomainVerificationStatus.PENDING,
        verificationRecords,
        webhookSecret,
        isDefault,
        isActive: true
      });

      await tenantDomain.save();
      logger.info(`Domain ${domain} registered for tenant ${tenantId}`);

      return tenantDomain;
    } catch (error) {
      logger.error(`Error registering domain ${domain} for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Set up a tracking domain for a tenant
   */
  async setupTrackingDomain(tenantId: string, domain: string, trackingSubdomain: string = 'track'): Promise<ITenantDomain> {
    try {
      // Find the tenant domain
      const tenantDomain = await TenantDomain.findOne({
        tenantId,
        domain
      });

      if (!tenantDomain) {
        throw new Error(`Domain ${domain} not found for tenant ${tenantId}`);
      }

      // Construct the tracking domain
      const trackingDomain = `${trackingSubdomain}.${domain}`;

      // Check if the tracking domain is already set up
      if (tenantDomain.trackingDomain === trackingDomain && tenantDomain.trackingRecords?.length > 0) {
        return tenantDomain;
      }

      // Update tracking domain in Resend
      const result = await resendService.updateDomain(tenantDomain.resendDomainId!, {
        clickTrackingDomain: trackingDomain
      });

      if (!result.success) {
        throw new Error(`Failed to set up tracking domain with Resend: ${result.error}`);
      }

      // Create tracking domain record
      const trackingRecords: IDNSRecord[] = [
        {
          type: DNSRecordType.CNAME,
          host: trackingSubdomain,
          value: 'u.resend.net'
        }
      ];

      // Update the tenant domain record
      tenantDomain.trackingDomain = trackingDomain;
      tenantDomain.trackingRecords = trackingRecords;
      await tenantDomain.save();

      logger.info(`Tracking domain ${trackingDomain} set up for tenant ${tenantId}`);
      return tenantDomain;
    } catch (error) {
      logger.error(`Error setting up tracking domain for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Check domain verification status
   */
  async checkVerificationStatus(tenantDomainId: string): Promise<ITenantDomain> {
    try {
      // Find the tenant domain
      const tenantDomain = await TenantDomain.findById(tenantDomainId);

      if (!tenantDomain) {
        throw new Error(`Tenant domain ${tenantDomainId} not found`);
      }

      // Check status with Resend
      const result = await resendService.getDomain(tenantDomain.resendDomainId!);

      if (!result.success) {
        throw new Error(`Failed to check domain status with Resend: ${result.error}`);
      }

      // Update verification status
      tenantDomain.verificationStatus = result.data?.status === 'verified' 
        ? DomainVerificationStatus.VERIFIED 
        : DomainVerificationStatus.PENDING;
      
      tenantDomain.lastVerificationCheck = new Date();
      await tenantDomain.save();

      logger.info(`Domain verification status for ${tenantDomain.domain}: ${tenantDomain.verificationStatus}`);
      return tenantDomain;
    } catch (error) {
      logger.error(`Error checking verification status for domain ${tenantDomainId}:`, error);
      throw error;
    }
  }

  /**
   * Check DNS records manually
   */
  async checkDNSRecords(tenantDomainId: string): Promise<{
    verificationRecords: { record: IDNSRecord; verified: boolean }[];
    trackingRecords?: { record: IDNSRecord; verified: boolean }[];
    allVerified: boolean;
  }> {
    try {
      // Find the tenant domain
      const tenantDomain = await TenantDomain.findById(tenantDomainId);

      if (!tenantDomain) {
        throw new Error(`Tenant domain ${tenantDomainId} not found`);
      }

      // Check verification records
      const verificationResults = await Promise.all(
        tenantDomain.verificationRecords.map(async (record) => {
          const verified = await this.verifyDNSRecord(tenantDomain.domain, record);
          return { record, verified };
        })
      );

      // Check tracking records if they exist
      let trackingResults: { record: IDNSRecord; verified: boolean }[] = [];
      if (tenantDomain.trackingRecords && tenantDomain.trackingRecords.length > 0) {
        trackingResults = await Promise.all(
          tenantDomain.trackingRecords.map(async (record) => {
            const verified = await this.verifyDNSRecord(tenantDomain.domain, record);
            return { record, verified };
          })
        );
      }

      // Check if all records are verified
      const allVerified = [...verificationResults, ...trackingResults].every(result => result.verified);

      return {
        verificationRecords: verificationResults,
        trackingRecords: trackingResults.length > 0 ? trackingResults : undefined,
        allVerified
      };
    } catch (error) {
      logger.error(`Error checking DNS records for domain ${tenantDomainId}:`, error);
      throw error;
    }
  }

  /**
   * Verify a single DNS record
   */
  private async verifyDNSRecord(domain: string, record: IDNSRecord): Promise<boolean> {
    try {
      const host = record.host === '@' ? domain : `${record.host}.${domain}`;

      switch (record.type) {
        case DNSRecordType.TXT:
          const txtRecords = await resolveTxt(host);
          return txtRecords.some(txtRecord => 
            txtRecord.join('').includes(record.value)
          );

        case DNSRecordType.CNAME:
          const cnameRecords = await resolveCname(host);
          return cnameRecords.some(cnameRecord => 
            cnameRecord === record.value
          );

        default:
          logger.warn(`Unsupported DNS record type: ${record.type}`);
          return false;
      }
    } catch (error) {
      logger.error(`Error verifying DNS record for ${domain}:`, error);
      return false;
    }
  }

  /**
   * Generate a webhook secret for a domain
   */
  private generateWebhookSecret(): string {
    return `whsec_${crypto.randomBytes(24).toString('hex')}`;
  }

  /**
   * Rotate webhook secret for a domain
   */
  async rotateWebhookSecret(tenantDomainId: string): Promise<ITenantDomain> {
    try {
      // Find the tenant domain
      const tenantDomain = await TenantDomain.findById(tenantDomainId);

      if (!tenantDomain) {
        throw new Error(`Tenant domain ${tenantDomainId} not found`);
      }

      // Generate a new webhook secret
      tenantDomain.webhookSecret = this.generateWebhookSecret();
      await tenantDomain.save();

      logger.info(`Webhook secret rotated for domain ${tenantDomain.domain}`);
      return tenantDomain;
    } catch (error) {
      logger.error(`Error rotating webhook secret for domain ${tenantDomainId}:`, error);
      throw error;
    }
  }

  /**
   * Get tenant domain by Resend domain ID
   */
  async getByResendDomainId(resendDomainId: string): Promise<ITenantDomain | null> {
    try {
      return await TenantDomain.findOne({ resendDomainId });
    } catch (error) {
      logger.error(`Error getting tenant domain by Resend domain ID ${resendDomainId}:`, error);
      return null;
    }
  }

  /**
   * Get default domain for a tenant
   */
  async getDefaultDomain(tenantId: string): Promise<ITenantDomain | null> {
    try {
      return await TenantDomain.findOne({
        tenantId,
        isDefault: true,
        isActive: true
      });
    } catch (error) {
      logger.error(`Error getting default domain for tenant ${tenantId}:`, error);
      return null;
    }
  }
}

export default new TenantDomainService();
