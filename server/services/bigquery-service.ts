import { BigQuery } from '@google-cloud/bigquery';
import { logger } from '../utils/logger';
import { subscriptionClient } from './subscription-client';

// Initialize BigQuery client
const bigquery = new BigQuery({
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
  keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
});

/**
 * Service for BigQuery operations with tenant isolation
 */
export class BigQueryService {
  /**
   * Get the tenant-specific dataset name
   */
  private static getTenantDataset(tenantId: string): string {
    // Sanitize tenant ID to ensure it's valid for BigQuery naming
    const sanitizedTenantId = tenantId.replace(/[^a-zA-Z0-9_]/g, '_');
    return `aizako_analytics_${sanitizedTenantId}`;
  }

  /**
   * Ensure the tenant dataset exists
   */
  static async ensureTenantDataset(tenantId: string): Promise<void> {
    try {
      const datasetName = this.getTenantDataset(tenantId);
      const [exists] = await bigquery.dataset(datasetName).exists();

      if (!exists) {
        // Create the dataset if it doesn't exist
        await bigquery.createDataset(datasetName, {
          location: 'US', // Set your preferred location
        });

        logger.info(`Created BigQuery dataset for tenant: ${tenantId}`);
      }
    } catch (error) {
      logger.error(`Error ensuring tenant dataset: ${error}`);
      throw error;
    }
  }

  /**
   * Execute a query with tenant isolation
   */
  static async executeQuery(
    tenantId: string,
    query: string,
    params: Record<string, any> = {}
  ): Promise<any[]> {
    try {
      // Validate tenant ID
      if (!tenantId) {
        throw new Error('Tenant ID is required');
      }

      // Record usage
      await subscriptionClient.recordUsage(
        tenantId,
        'analytics.bigquery',
        1
      );

      // Ensure tenant dataset exists
      await this.ensureTenantDataset(tenantId);

      // Get dataset name
      const datasetName = this.getTenantDataset(tenantId);

      // Add tenant dataset to query parameters
      const queryParams = {
        ...params,
        tenant_dataset: datasetName,
        tenant_id: tenantId,
      };

      // Execute query
      const [rows] = await bigquery.query({
        query,
        params: queryParams,
        parameterMode: 'named',
      });

      return rows;
    } catch (error) {
      logger.error(`Error executing BigQuery query: ${error}`);
      throw error;
    }
  }

  /**
   * Insert rows into a BigQuery table with tenant isolation
   */
  static async insertRows(
    tenantId: string,
    tableName: string,
    rows: Record<string, any>[]
  ): Promise<void> {
    try {
      // Validate tenant ID
      if (!tenantId) {
        throw new Error('Tenant ID is required');
      }

      // Ensure tenant dataset exists
      await this.ensureTenantDataset(tenantId);

      // Get dataset name
      const datasetName = this.getTenantDataset(tenantId);

      // Add tenant_id to each row
      const rowsWithTenant = rows.map(row => ({
        ...row,
        tenant_id: tenantId,
        inserted_at: BigQuery.timestamp(new Date()),
      }));

      // Insert rows
      await bigquery
        .dataset(datasetName)
        .table(tableName)
        .insert(rowsWithTenant);

      logger.info(`Inserted ${rows.length} rows into ${datasetName}.${tableName}`);
    } catch (error) {
      logger.error(`Error inserting rows into BigQuery: ${error}`);
      throw error;
    }
  }

  /**
   * Create a table in the tenant's dataset
   */
  static async createTable(
    tenantId: string,
    tableName: string,
    schema: any[]
  ): Promise<void> {
    try {
      // Validate tenant ID
      if (!tenantId) {
        throw new Error('Tenant ID is required');
      }

      // Ensure tenant dataset exists
      await this.ensureTenantDataset(tenantId);

      // Get dataset name
      const datasetName = this.getTenantDataset(tenantId);

      // Add tenant_id field to schema if not present
      if (!schema.some(field => field.name === 'tenant_id')) {
        schema.push({ name: 'tenant_id', type: 'STRING', mode: 'REQUIRED' });
      }

      // Add inserted_at field to schema if not present
      if (!schema.some(field => field.name === 'inserted_at')) {
        schema.push({ name: 'inserted_at', type: 'TIMESTAMP', mode: 'REQUIRED' });
      }

      // Create table options
      const options = {
        schema,
        timePartitioning: {
          type: 'DAY',
          field: 'inserted_at',
        },
      };

      // Create table
      await bigquery
        .dataset(datasetName)
        .createTable(tableName, options);

      logger.info(`Created table ${datasetName}.${tableName}`);
    } catch (error) {
      logger.error(`Error creating BigQuery table: ${error}`);
      throw error;
    }
  }

  /**
   * Sync events from MongoDB to BigQuery
   */
  static async syncEventsToWarehouse(tenantId: string, startDate: Date, endDate: Date): Promise<number> {
    try {
      // This is a placeholder for the actual implementation
      // In a real implementation, this would:
      // 1. Query MongoDB for events in the date range for the tenant
      // 2. Transform the data as needed
      // 3. Insert into BigQuery using the insertRows method
      // 4. Return the number of synced events

      logger.info(`Syncing events for tenant ${tenantId} from ${startDate} to ${endDate}`);
      return 0;
    } catch (error) {
      logger.error(`Error syncing events to warehouse: ${error}`);
      throw error;
    }
  }
}
