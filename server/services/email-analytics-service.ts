/**
 * Email Analytics Service
 * 
 * This service handles email analytics including:
 * - Tracking email performance metrics
 * - Generating reports and insights
 * - A/B testing analysis
 */

import mongoose from 'mongoose';
import { 
  EmailTracking, 
  EmailEventType,
  Activity
} from '../models/mongoose';
import { logger } from '../utils/logger';

interface TimeRange {
  startDate: Date;
  endDate: Date;
}

interface EmailMetrics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  replied: number;
  bounced: number;
  complained: number;
  openRate: number;
  clickRate: number;
  clickToOpenRate: number;
  replyRate: number;
  bounceRate: number;
  complaintRate: number;
}

interface EmailAnalytics extends EmailMetrics {
  period: string;
  byDay?: { date: string; metrics: EmailMetrics }[];
  byHour?: { hour: number; metrics: EmailMetrics }[];
  byTemplate?: { templateId: string; templateName: string; metrics: EmailMetrics }[];
  byUser?: { userId: string; userName: string; metrics: EmailMetrics }[];
  byDevice?: { device: string; metrics: EmailMetrics }[];
  byLocation?: { country: string; metrics: EmailMetrics }[];
  abTestingResults?: {
    templateId: string;
    templateName: string;
    variants: {
      name: string;
      metrics: EmailMetrics;
    }[];
  }[];
}

class EmailAnalyticsService {
  /**
   * Get email analytics for a tenant
   */
  async getEmailAnalytics(
    tenantId: string,
    options: {
      timeRange: TimeRange;
      groupBy?: ('day' | 'hour' | 'template' | 'user' | 'device' | 'location')[];
      templateIds?: string[];
      userIds?: string[];
      includeABTesting?: boolean;
    }
  ): Promise<EmailAnalytics> {
    try {
      // Build base query
      const baseQuery: any = {
        tenantId: new mongoose.Types.ObjectId(tenantId),
        createdAt: {
          $gte: options.timeRange.startDate,
          $lte: options.timeRange.endDate
        }
      };

      // Add template filter if provided
      if (options.templateIds && options.templateIds.length > 0) {
        baseQuery['customFields.templateId'] = {
          $in: options.templateIds.map(id => new mongoose.Types.ObjectId(id))
        };
      }

      // Add user filter if provided
      if (options.userIds && options.userIds.length > 0) {
        baseQuery.userId = {
          $in: options.userIds.map(id => new mongoose.Types.ObjectId(id))
        };
      }

      // Get overall metrics
      const metrics = await this.calculateMetrics(baseQuery);

      // Initialize analytics object
      const analytics: EmailAnalytics = {
        period: `${options.timeRange.startDate.toISOString()} to ${options.timeRange.endDate.toISOString()}`,
        ...metrics
      };

      // Add groupings if requested
      if (options.groupBy) {
        if (options.groupBy.includes('day')) {
          analytics.byDay = await this.getMetricsByDay(baseQuery, options.timeRange);
        }

        if (options.groupBy.includes('hour')) {
          analytics.byHour = await this.getMetricsByHour(baseQuery);
        }

        if (options.groupBy.includes('template')) {
          analytics.byTemplate = await this.getMetricsByTemplate(baseQuery);
        }

        if (options.groupBy.includes('user')) {
          analytics.byUser = await this.getMetricsByUser(baseQuery);
        }

        if (options.groupBy.includes('device')) {
          analytics.byDevice = await this.getMetricsByDevice(baseQuery);
        }

        if (options.groupBy.includes('location')) {
          analytics.byLocation = await this.getMetricsByLocation(baseQuery);
        }
      }

      // Add A/B testing results if requested
      if (options.includeABTesting) {
        analytics.abTestingResults = await this.getABTestingResults(baseQuery);
      }

      return analytics;
    } catch (error) {
      logger.error(`Error getting email analytics for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate email metrics for a query
   */
  private async calculateMetrics(query: any): Promise<EmailMetrics> {
    try {
      // Count emails by status
      const statusCounts = await EmailTracking.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      // Initialize metrics
      const metrics: EmailMetrics = {
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        replied: 0,
        bounced: 0,
        complained: 0,
        openRate: 0,
        clickRate: 0,
        clickToOpenRate: 0,
        replyRate: 0,
        bounceRate: 0,
        complaintRate: 0
      };

      // Map status counts to metrics
      statusCounts.forEach((item) => {
        switch (item._id) {
          case EmailEventType.SENT:
            metrics.sent = item.count;
            break;
          case EmailEventType.DELIVERED:
            metrics.delivered = item.count;
            break;
          case EmailEventType.OPENED:
            metrics.opened = item.count;
            break;
          case EmailEventType.CLICKED:
            metrics.clicked = item.count;
            break;
          case EmailEventType.REPLIED:
            metrics.replied = item.count;
            break;
          case EmailEventType.BOUNCED:
            metrics.bounced = item.count;
            break;
          case EmailEventType.COMPLAINED:
            metrics.complained = item.count;
            break;
        }
      });

      // Calculate rates
      const deliveredCount = metrics.delivered > 0 ? metrics.delivered : metrics.sent;
      
      metrics.openRate = deliveredCount > 0 ? (metrics.opened / deliveredCount) * 100 : 0;
      metrics.clickRate = deliveredCount > 0 ? (metrics.clicked / deliveredCount) * 100 : 0;
      metrics.clickToOpenRate = metrics.opened > 0 ? (metrics.clicked / metrics.opened) * 100 : 0;
      metrics.replyRate = deliveredCount > 0 ? (metrics.replied / deliveredCount) * 100 : 0;
      metrics.bounceRate = metrics.sent > 0 ? (metrics.bounced / metrics.sent) * 100 : 0;
      metrics.complaintRate = deliveredCount > 0 ? (metrics.complained / deliveredCount) * 100 : 0;

      return metrics;
    } catch (error) {
      logger.error('Error calculating email metrics:', error);
      throw error;
    }
  }

  /**
   * Get metrics grouped by day
   */
  private async getMetricsByDay(baseQuery: any, timeRange: TimeRange): Promise<{ date: string; metrics: EmailMetrics }[]> {
    try {
      // Create a date range
      const days: { date: string; metrics: EmailMetrics }[] = [];
      const currentDate = new Date(timeRange.startDate);
      
      while (currentDate <= timeRange.endDate) {
        const dayStart = new Date(currentDate);
        const dayEnd = new Date(currentDate);
        dayEnd.setHours(23, 59, 59, 999);
        
        const dayQuery = {
          ...baseQuery,
          createdAt: {
            $gte: dayStart,
            $lte: dayEnd
          }
        };
        
        const metrics = await this.calculateMetrics(dayQuery);
        
        days.push({
          date: currentDate.toISOString().split('T')[0],
          metrics
        });
        
        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      return days;
    } catch (error) {
      logger.error('Error getting metrics by day:', error);
      throw error;
    }
  }

  /**
   * Get metrics grouped by hour
   */
  private async getMetricsByHour(baseQuery: any): Promise<{ hour: number; metrics: EmailMetrics }[]> {
    try {
      // Aggregate by hour
      const hourlyData = await EmailTracking.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: { 
              hour: { $hour: '$createdAt' },
              status: '$status'
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.hour': 1 } }
      ]);
      
      // Process results
      const hourlyMetrics: Record<number, Record<string, number>> = {};
      
      // Initialize hours
      for (let hour = 0; hour < 24; hour++) {
        hourlyMetrics[hour] = {
          sent: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
          replied: 0,
          bounced: 0,
          complained: 0
        };
      }
      
      // Fill in data
      hourlyData.forEach(item => {
        const hour = item._id.hour;
        const status = item._id.status;
        hourlyMetrics[hour][status.toLowerCase()] = item.count;
      });
      
      // Calculate metrics for each hour
      const result: { hour: number; metrics: EmailMetrics }[] = [];
      
      for (let hour = 0; hour < 24; hour++) {
        const data = hourlyMetrics[hour];
        
        const deliveredCount = data.delivered > 0 ? data.delivered : data.sent;
        
        const metrics: EmailMetrics = {
          sent: data.sent,
          delivered: data.delivered,
          opened: data.opened,
          clicked: data.clicked,
          replied: data.replied,
          bounced: data.bounced,
          complained: data.complained,
          openRate: deliveredCount > 0 ? (data.opened / deliveredCount) * 100 : 0,
          clickRate: deliveredCount > 0 ? (data.clicked / deliveredCount) * 100 : 0,
          clickToOpenRate: data.opened > 0 ? (data.clicked / data.opened) * 100 : 0,
          replyRate: deliveredCount > 0 ? (data.replied / deliveredCount) * 100 : 0,
          bounceRate: data.sent > 0 ? (data.bounced / data.sent) * 100 : 0,
          complaintRate: deliveredCount > 0 ? (data.complained / deliveredCount) * 100 : 0
        };
        
        result.push({ hour, metrics });
      }
      
      return result;
    } catch (error) {
      logger.error('Error getting metrics by hour:', error);
      throw error;
    }
  }

  /**
   * Get metrics grouped by template
   */
  private async getMetricsByTemplate(baseQuery: any): Promise<{ templateId: string; templateName: string; metrics: EmailMetrics }[]> {
    try {
      // Aggregate by template
      const templateData = await EmailTracking.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: { 
              templateId: '$customFields.templateId',
              status: '$status'
            },
            count: { $sum: 1 }
          }
        }
      ]);
      
      // Process results
      const templateMetrics: Record<string, Record<string, number>> = {};
      const templateNames: Record<string, string> = {};
      
      // Get template names
      const templateIds = [...new Set(templateData.map(item => item._id.templateId))].filter(Boolean);
      
      if (templateIds.length > 0) {
        const EmailTemplate = mongoose.model('EmailTemplate');
        const templates = await EmailTemplate.find({ _id: { $in: templateIds } }, 'name');
        
        templates.forEach(template => {
          templateNames[template._id.toString()] = template.name;
        });
      }
      
      // Initialize templates
      templateData.forEach(item => {
        const templateId = item._id.templateId || 'unknown';
        if (!templateMetrics[templateId]) {
          templateMetrics[templateId] = {
            sent: 0,
            delivered: 0,
            opened: 0,
            clicked: 0,
            replied: 0,
            bounced: 0,
            complained: 0
          };
        }
      });
      
      // Fill in data
      templateData.forEach(item => {
        const templateId = item._id.templateId || 'unknown';
        const status = item._id.status;
        templateMetrics[templateId][status.toLowerCase()] = item.count;
      });
      
      // Calculate metrics for each template
      const result: { templateId: string; templateName: string; metrics: EmailMetrics }[] = [];
      
      for (const [templateId, data] of Object.entries(templateMetrics)) {
        const deliveredCount = data.delivered > 0 ? data.delivered : data.sent;
        
        const metrics: EmailMetrics = {
          sent: data.sent,
          delivered: data.delivered,
          opened: data.opened,
          clicked: data.clicked,
          replied: data.replied,
          bounced: data.bounced,
          complained: data.complained,
          openRate: deliveredCount > 0 ? (data.opened / deliveredCount) * 100 : 0,
          clickRate: deliveredCount > 0 ? (data.clicked / deliveredCount) * 100 : 0,
          clickToOpenRate: data.opened > 0 ? (data.clicked / data.opened) * 100 : 0,
          replyRate: deliveredCount > 0 ? (data.replied / deliveredCount) * 100 : 0,
          bounceRate: data.sent > 0 ? (data.bounced / data.sent) * 100 : 0,
          complaintRate: deliveredCount > 0 ? (data.complained / deliveredCount) * 100 : 0
        };
        
        result.push({ 
          templateId, 
          templateName: templateNames[templateId] || 'Unknown Template', 
          metrics 
        });
      }
      
      return result;
    } catch (error) {
      logger.error('Error getting metrics by template:', error);
      throw error;
    }
  }

  /**
   * Get metrics grouped by user
   */
  private async getMetricsByUser(baseQuery: any): Promise<{ userId: string; userName: string; metrics: EmailMetrics }[]> {
    try {
      // Aggregate by user
      const userData = await EmailTracking.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: { 
              userId: '$userId',
              status: '$status'
            },
            count: { $sum: 1 }
          }
        }
      ]);
      
      // Process results
      const userMetrics: Record<string, Record<string, number>> = {};
      const userNames: Record<string, string> = {};
      
      // Get user names
      const userIds = [...new Set(userData.map(item => item._id.userId))].filter(Boolean);
      
      if (userIds.length > 0) {
        const User = mongoose.model('User');
        const users = await User.find({ _id: { $in: userIds } }, 'fullName');
        
        users.forEach(user => {
          userNames[user._id.toString()] = user.fullName;
        });
      }
      
      // Initialize users
      userData.forEach(item => {
        const userId = item._id.userId.toString();
        if (!userMetrics[userId]) {
          userMetrics[userId] = {
            sent: 0,
            delivered: 0,
            opened: 0,
            clicked: 0,
            replied: 0,
            bounced: 0,
            complained: 0
          };
        }
      });
      
      // Fill in data
      userData.forEach(item => {
        const userId = item._id.userId.toString();
        const status = item._id.status;
        userMetrics[userId][status.toLowerCase()] = item.count;
      });
      
      // Calculate metrics for each user
      const result: { userId: string; userName: string; metrics: EmailMetrics }[] = [];
      
      for (const [userId, data] of Object.entries(userMetrics)) {
        const deliveredCount = data.delivered > 0 ? data.delivered : data.sent;
        
        const metrics: EmailMetrics = {
          sent: data.sent,
          delivered: data.delivered,
          opened: data.opened,
          clicked: data.clicked,
          replied: data.replied,
          bounced: data.bounced,
          complained: data.complained,
          openRate: deliveredCount > 0 ? (data.opened / deliveredCount) * 100 : 0,
          clickRate: deliveredCount > 0 ? (data.clicked / deliveredCount) * 100 : 0,
          clickToOpenRate: data.opened > 0 ? (data.clicked / data.opened) * 100 : 0,
          replyRate: deliveredCount > 0 ? (data.replied / deliveredCount) * 100 : 0,
          bounceRate: data.sent > 0 ? (data.bounced / data.sent) * 100 : 0,
          complaintRate: deliveredCount > 0 ? (data.complained / deliveredCount) * 100 : 0
        };
        
        result.push({ 
          userId, 
          userName: userNames[userId] || 'Unknown User', 
          metrics 
        });
      }
      
      return result;
    } catch (error) {
      logger.error('Error getting metrics by user:', error);
      throw error;
    }
  }

  /**
   * Get metrics grouped by device
   */
  private async getMetricsByDevice(baseQuery: any): Promise<{ device: string; metrics: EmailMetrics }[]> {
    try {
      // Get all tracking records
      const trackingRecords = await EmailTracking.find(baseQuery, 'events');
      
      // Extract device information from events
      const deviceData: Record<string, Record<string, number>> = {
        desktop: { sent: 0, delivered: 0, opened: 0, clicked: 0, replied: 0, bounced: 0, complained: 0 },
        mobile: { sent: 0, delivered: 0, opened: 0, clicked: 0, replied: 0, bounced: 0, complained: 0 },
        tablet: { sent: 0, delivered: 0, opened: 0, clicked: 0, replied: 0, bounced: 0, complained: 0 },
        unknown: { sent: 0, delivered: 0, opened: 0, clicked: 0, replied: 0, bounced: 0, complained: 0 }
      };
      
      // Process events
      trackingRecords.forEach(record => {
        record.events.forEach(event => {
          const device = event.metadata?.device || 'unknown';
          const status = event.type.toLowerCase();
          
          if (deviceData[device]) {
            deviceData[device][status] = (deviceData[device][status] || 0) + 1;
          } else {
            deviceData.unknown[status] = (deviceData.unknown[status] || 0) + 1;
          }
        });
      });
      
      // Calculate metrics for each device
      const result: { device: string; metrics: EmailMetrics }[] = [];
      
      for (const [device, data] of Object.entries(deviceData)) {
        const deliveredCount = data.delivered > 0 ? data.delivered : data.sent;
        
        const metrics: EmailMetrics = {
          sent: data.sent,
          delivered: data.delivered,
          opened: data.opened,
          clicked: data.clicked,
          replied: data.replied,
          bounced: data.bounced,
          complained: data.complained,
          openRate: deliveredCount > 0 ? (data.opened / deliveredCount) * 100 : 0,
          clickRate: deliveredCount > 0 ? (data.clicked / deliveredCount) * 100 : 0,
          clickToOpenRate: data.opened > 0 ? (data.clicked / data.opened) * 100 : 0,
          replyRate: deliveredCount > 0 ? (data.replied / deliveredCount) * 100 : 0,
          bounceRate: data.sent > 0 ? (data.bounced / data.sent) * 100 : 0,
          complaintRate: deliveredCount > 0 ? (data.complained / deliveredCount) * 100 : 0
        };
        
        result.push({ device, metrics });
      }
      
      return result;
    } catch (error) {
      logger.error('Error getting metrics by device:', error);
      throw error;
    }
  }

  /**
   * Get metrics grouped by location
   */
  private async getMetricsByLocation(baseQuery: any): Promise<{ country: string; metrics: EmailMetrics }[]> {
    try {
      // Get all tracking records
      const trackingRecords = await EmailTracking.find(baseQuery, 'events');
      
      // Extract location information from events
      const locationData: Record<string, Record<string, number>> = {};
      
      // Process events
      trackingRecords.forEach(record => {
        record.events.forEach(event => {
          const country = event.metadata?.location?.country || 'unknown';
          const status = event.type.toLowerCase();
          
          if (!locationData[country]) {
            locationData[country] = {
              sent: 0,
              delivered: 0,
              opened: 0,
              clicked: 0,
              replied: 0,
              bounced: 0,
              complained: 0
            };
          }
          
          locationData[country][status] = (locationData[country][status] || 0) + 1;
        });
      });
      
      // Calculate metrics for each location
      const result: { country: string; metrics: EmailMetrics }[] = [];
      
      for (const [country, data] of Object.entries(locationData)) {
        const deliveredCount = data.delivered > 0 ? data.delivered : data.sent;
        
        const metrics: EmailMetrics = {
          sent: data.sent,
          delivered: data.delivered,
          opened: data.opened,
          clicked: data.clicked,
          replied: data.replied,
          bounced: data.bounced,
          complained: data.complained,
          openRate: deliveredCount > 0 ? (data.opened / deliveredCount) * 100 : 0,
          clickRate: deliveredCount > 0 ? (data.clicked / deliveredCount) * 100 : 0,
          clickToOpenRate: data.opened > 0 ? (data.clicked / data.opened) * 100 : 0,
          replyRate: deliveredCount > 0 ? (data.replied / deliveredCount) * 100 : 0,
          bounceRate: data.sent > 0 ? (data.bounced / data.sent) * 100 : 0,
          complaintRate: deliveredCount > 0 ? (data.complained / deliveredCount) * 100 : 0
        };
        
        result.push({ country, metrics });
      }
      
      return result;
    } catch (error) {
      logger.error('Error getting metrics by location:', error);
      throw error;
    }
  }

  /**
   * Get A/B testing results
   */
  private async getABTestingResults(baseQuery: any): Promise<{
    templateId: string;
    templateName: string;
    variants: {
      name: string;
      metrics: EmailMetrics;
    }[];
  }[]> {
    try {
      // Get all tracking records with A/B testing data
      const trackingRecords = await EmailTracking.find({
        ...baseQuery,
        'customFields.templateId': { $exists: true },
        'customFields.abTestingVariant': { $exists: true }
      });
      
      // Group by template and variant
      const templateVariantData: Record<string, Record<string, Record<string, number>>> = {};
      const templateNames: Record<string, string> = {};
      
      // Get template names
      const templateIds = [...new Set(trackingRecords.map(record => 
        record.customFields.templateId?.toString()
      ))].filter(Boolean);
      
      if (templateIds.length > 0) {
        const EmailTemplate = mongoose.model('EmailTemplate');
        const templates = await EmailTemplate.find({ _id: { $in: templateIds } }, 'name');
        
        templates.forEach(template => {
          templateNames[template._id.toString()] = template.name;
        });
      }
      
      // Process records
      trackingRecords.forEach(record => {
        const templateId = record.customFields.templateId?.toString() || 'unknown';
        const variant = record.customFields.abTestingVariant || 'original';
        const status = record.status.toLowerCase();
        
        if (!templateVariantData[templateId]) {
          templateVariantData[templateId] = {};
        }
        
        if (!templateVariantData[templateId][variant]) {
          templateVariantData[templateId][variant] = {
            sent: 0,
            delivered: 0,
            opened: 0,
            clicked: 0,
            replied: 0,
            bounced: 0,
            complained: 0
          };
        }
        
        templateVariantData[templateId][variant][status] = 
          (templateVariantData[templateId][variant][status] || 0) + 1;
      });
      
      // Calculate metrics for each template variant
      const result: {
        templateId: string;
        templateName: string;
        variants: {
          name: string;
          metrics: EmailMetrics;
        }[];
      }[] = [];
      
      for (const [templateId, variants] of Object.entries(templateVariantData)) {
        const templateVariants: {
          name: string;
          metrics: EmailMetrics;
        }[] = [];
        
        for (const [variant, data] of Object.entries(variants)) {
          const deliveredCount = data.delivered > 0 ? data.delivered : data.sent;
          
          const metrics: EmailMetrics = {
            sent: data.sent,
            delivered: data.delivered,
            opened: data.opened,
            clicked: data.clicked,
            replied: data.replied,
            bounced: data.bounced,
            complained: data.complained,
            openRate: deliveredCount > 0 ? (data.opened / deliveredCount) * 100 : 0,
            clickRate: deliveredCount > 0 ? (data.clicked / deliveredCount) * 100 : 0,
            clickToOpenRate: data.opened > 0 ? (data.clicked / data.opened) * 100 : 0,
            replyRate: deliveredCount > 0 ? (data.replied / deliveredCount) * 100 : 0,
            bounceRate: data.sent > 0 ? (data.bounced / data.sent) * 100 : 0,
            complaintRate: deliveredCount > 0 ? (data.complained / deliveredCount) * 100 : 0
          };
          
          templateVariants.push({ name: variant, metrics });
        }
        
        result.push({
          templateId,
          templateName: templateNames[templateId] || 'Unknown Template',
          variants: templateVariants
        });
      }
      
      return result;
    } catch (error) {
      logger.error('Error getting A/B testing results:', error);
      throw error;
    }
  }
}

export default new EmailAnalyticsService();
