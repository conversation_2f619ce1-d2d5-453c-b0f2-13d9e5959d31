/**
 * Resend Service
 *
 * This service handles interactions with the Resend API for email sending and tracking.
 */

import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import emailTrackingService from './email-tracking-service';

// Resend API configuration
const RESEND_API_URL = process.env.RESEND_API_URL || 'https://api.resend.com';
const RESEND_API_KEY = process.env.RESEND_API_KEY || '';

class ResendService {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string = RESEND_API_KEY, baseUrl: string = RESEND_API_URL) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  /**
   * Initialize the Resend service
   */
  async initialize(): Promise<boolean> {
    try {
      if (!this.apiKey) {
        logger.error('Resend API key not provided');
        return false;
      }

      // Test the API connection
      const response = await this.makeRequest('/domains', 'GET');

      if (response.success) {
        logger.info('Resend service initialized successfully');
        return true;
      } else {
        logger.error(`Failed to initialize Resend service: ${response.error}`);
        return false;
      }
    } catch (error) {
      logger.error('Error initializing Resend service:', error);
      return false;
    }
  }

  /**
   * Create a new domain in Resend
   */
  async createDomain(domain: string): Promise<{
    success: boolean;
    data?: {
      id: string;
      name: string;
      status: string;
      created_at: string;
      region: string;
      dkim?: { host: string; value: string; };
      spf?: { host: string; value: string; };
      return_path?: { host: string; value: string; };
    };
    error?: string;
  }> {
    try {
      const response = await this.makeRequest('/domains', 'POST', { name: domain });

      if (response.success) {
        logger.info(`Domain ${domain} created in Resend with ID ${response.data.id}`);
        return {
          success: true,
          data: response.data
        };
      } else {
        logger.error(`Failed to create domain ${domain} in Resend: ${response.error}`);
        return {
          success: false,
          error: response.error
        };
      }
    } catch (error) {
      logger.error(`Error creating domain ${domain} in Resend:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get domain details from Resend
   */
  async getDomain(domainId: string): Promise<{
    success: boolean;
    data?: {
      id: string;
      name: string;
      status: string;
      created_at: string;
      region: string;
      dkim_status?: string;
      spf_status?: string;
      return_path_status?: string;
    };
    error?: string;
  }> {
    try {
      const response = await this.makeRequest(`/domains/${domainId}`, 'GET');

      if (response.success) {
        logger.info(`Retrieved domain ${domainId} from Resend`);
        return {
          success: true,
          data: response.data
        };
      } else {
        logger.error(`Failed to get domain ${domainId} from Resend: ${response.error}`);
        return {
          success: false,
          error: response.error
        };
      }
    } catch (error) {
      logger.error(`Error getting domain ${domainId} from Resend:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Update domain settings in Resend
   */
  async updateDomain(domainId: string, options: {
    openTracking?: boolean;
    clickTracking?: boolean;
    clickTrackingDomain?: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const data: Record<string, any> = {};

      if (options.openTracking !== undefined) {
        data.open_tracking = options.openTracking;
      }

      if (options.clickTracking !== undefined) {
        data.click_tracking = options.clickTracking;
      }

      if (options.clickTrackingDomain) {
        data.custom_tracking_domain = options.clickTrackingDomain;
      }

      const response = await this.makeRequest(`/domains/${domainId}`, 'PATCH', data);

      if (response.success) {
        logger.info(`Updated domain ${domainId} in Resend`);
        return { success: true };
      } else {
        logger.error(`Failed to update domain ${domainId} in Resend: ${response.error}`);
        return { success: false, error: response.error };
      }
    } catch (error) {
      logger.error(`Error updating domain ${domainId} in Resend:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send an email using Resend
   */
  async sendEmail(data: {
    from: string;
    to: string | string[];
    subject: string;
    html: string;
    text?: string;
    cc?: string | string[];
    bcc?: string | string[];
    replyTo?: string;
    attachments?: Array<{
      filename: string;
      content: string | Buffer;
      contentType?: string;
    }>;
    tags?: Array<{
      name: string;
      value: string;
    }>;
    headers?: Record<string, string>;
    userId: string;
    contactId?: string;
    sequenceId?: string;
    sequenceStepId?: string;
    trackOpens?: boolean;
    trackLinks?: boolean;
    customFields?: Record<string, any>;
  }): Promise<{ success: boolean; messageId?: string; error?: string; trackingId?: string }> {
    try {
      // Generate a unique message ID for tracking
      const messageId = `${uuidv4()}@aizako.crm`;

      // Add custom headers for tracking
      const headers = {
        ...(data.headers || {}),
        'X-Aizako-Message-ID': messageId
      };

      // Prepare email data for Resend
      const emailData = {
        from: data.from,
        to: Array.isArray(data.to) ? data.to : [data.to],
        subject: data.subject,
        html: data.html,
        text: data.text,
        cc: data.cc ? (Array.isArray(data.cc) ? data.cc : [data.cc]) : undefined,
        bcc: data.bcc ? (Array.isArray(data.bcc) ? data.bcc : [data.bcc]) : undefined,
        reply_to: data.replyTo,
        attachments: data.attachments,
        tags: data.tags,
        headers,
        track_opens: data.trackOpens !== false, // Enable by default
        track_clicks: data.trackLinks !== false // Enable by default
      };

      // Send email via Resend API
      const response = await this.makeRequest('/emails', 'POST', emailData);

      if (response.success && response.data?.id) {
        logger.info(`Email sent via Resend: ${response.data.id}`);

        // Create tracking record
        const tracking = await emailTrackingService.createTracking({
          userId: data.userId,
          contactId: data.contactId,
          sequenceId: data.sequenceId,
          sequenceStepId: data.sequenceStepId,
          messageId,
          subject: data.subject,
          recipient: Array.isArray(data.to) ? data.to[0] : data.to,
          sender: data.from,
          trackingEnabled: data.trackOpens !== false,
          linkTrackingEnabled: data.trackLinks !== false,
          attachmentTrackingEnabled: true,
          customFields: {
            ...(data.customFields || {}),
            resendId: response.data.id
          }
        });

        return {
          success: true,
          messageId: response.data.id,
          trackingId: tracking._id.toString()
        };
      } else {
        logger.error(`Failed to send email via Resend: ${response.error}`);
        return {
          success: false,
          error: response.error || 'Unknown error'
        };
      }
    } catch (error) {
      logger.error('Error sending email via Resend:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process a webhook event from Resend
   */
  async processWebhookEvent(event: {
    type: string;
    data: Record<string, any>;
  }): Promise<boolean> {
    try {
      const tenantId = event.data.tenantId;
      logger.info(`Processing Resend webhook event: ${event.type}${tenantId ? ` for tenant ${tenantId}` : ''}`);

      // Extract message ID from headers or data
      const messageId = this.extractMessageId(event.data);

      if (!messageId) {
        logger.warn('No message ID found in webhook event');
        return false;
      }

      // Process different event types
      switch (event.type) {
        case 'email.delivered':
          // No specific action needed for delivery events
          return true;

        case 'email.opened':
          await this.processOpenEvent(messageId, event.data);

          // Create activity record for tenant if applicable
          if (tenantId) {
            await this.createActivityRecord(tenantId, 'EMAIL_OPEN', messageId, event.data);
          }
          return true;

        case 'email.clicked':
          await this.processClickEvent(messageId, event.data);

          // Create activity record for tenant if applicable
          if (tenantId) {
            await this.createActivityRecord(tenantId, 'EMAIL_CLICK', messageId, event.data);
          }
          return true;

        case 'email.complained':
        case 'email.bounced':
          // Handle negative events
          await this.processNegativeEvent(messageId, event.type, event.data);

          // Create activity record for tenant if applicable
          if (tenantId) {
            const activityType = event.type === 'email.complained' ? 'EMAIL_COMPLAINT' : 'EMAIL_BOUNCE';
            await this.createActivityRecord(tenantId, activityType, messageId, event.data);
          }
          return true;

        default:
          logger.info(`Unhandled Resend event type: ${event.type}`);
          return true;
      }
    } catch (error) {
      logger.error('Error processing Resend webhook event:', error);
      return false;
    }
  }

  /**
   * Create an activity record for a tenant
   */
  private async createActivityRecord(
    tenantId: string,
    activityType: string,
    messageId: string,
    data: Record<string, any>
  ): Promise<void> {
    try {
      // Find the tracking record to get contact information
      const tracking = await emailTrackingService.findByMessageId(messageId);

      if (!tracking) {
        logger.warn(`No tracking record found for message ID ${messageId}`);
        return;
      }

      // Get MongoDB models
      const mongoose = require('mongoose');
      const Activity = mongoose.model('Activity');

      // Create activity record
      const activity = new Activity({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        type: activityType,
        contactId: tracking.contactId,
        userId: tracking.userId,
        data: {
          messageId,
          subject: tracking.subject,
          recipient: tracking.recipient,
          timestamp: new Date(),
          metadata: {
            ip: data.ip,
            userAgent: data.user_agent,
            location: data.geo,
            url: data.url, // For click events
            device: this.detectDevice(data.user_agent || '')
          }
        },
        source: 'email_tracking'
      });

      await activity.save();
      logger.info(`Activity record created for tenant ${tenantId}: ${activityType}`);

      // Create edge between contact and activity if contact exists
      if (tracking.contactId) {
        const Edge = mongoose.model('Edge');

        const edge = new Edge({
          tenantId: new mongoose.Types.ObjectId(tenantId),
          fromType: 'Contact',
          fromId: tracking.contactId,
          toType: 'Activity',
          toId: activity._id,
          relationship: 'DID_ACTIVITY',
          metadata: {
            timestamp: new Date()
          }
        });

        await edge.save();
        logger.info(`Edge created between contact ${tracking.contactId} and activity ${activity._id}`);
      }
    } catch (error) {
      logger.error(`Error creating activity record for tenant ${tenantId}:`, error);
    }
  }

  /**
   * Process an email open event
   */
  private async processOpenEvent(messageId: string, data: Record<string, any>): Promise<void> {
    try {
      // Find tracking record by message ID
      const tracking = await emailTrackingService.findByMessageId(messageId);

      if (!tracking) {
        logger.warn(`No tracking record found for message ID ${messageId}`);
        return;
      }

      // Process open event
      await emailTrackingService.processOpenEvent(tracking.pixelId, {
        ip: data.ip || 'unknown',
        userAgent: data.user_agent || 'unknown',
        device: this.detectDevice(data.user_agent || ''),
        location: {
          city: data.geo?.city,
          country: data.geo?.country,
          region: data.geo?.region
        }
      });

      logger.info(`Processed open event for message ${messageId}`);
    } catch (error) {
      logger.error(`Error processing open event for message ${messageId}:`, error);
    }
  }

  /**
   * Process an email click event
   */
  private async processClickEvent(messageId: string, data: Record<string, any>): Promise<void> {
    try {
      // Find tracking record by message ID
      const tracking = await emailTrackingService.findByMessageId(messageId);

      if (!tracking) {
        logger.warn(`No tracking record found for message ID ${messageId}`);
        return;
      }

      // Generate a link ID if not available
      const linkId = data.link_id || `link-${uuidv4()}`;

      // Store link ID in tracking record if not already present
      if (!tracking.customFields.linkIds) {
        tracking.customFields.linkIds = [linkId];
        await tracking.save();
      } else if (!tracking.customFields.linkIds.includes(linkId)) {
        tracking.customFields.linkIds.push(linkId);
        await tracking.save();
      }

      // Process click event
      await emailTrackingService.processClickEvent(linkId, {
        ip: data.ip || 'unknown',
        userAgent: data.user_agent || 'unknown',
        device: this.detectDevice(data.user_agent || ''),
        location: {
          city: data.geo?.city,
          country: data.geo?.country,
          region: data.geo?.region
        },
        url: data.url
      });

      logger.info(`Processed click event for message ${messageId}`);
    } catch (error) {
      logger.error(`Error processing click event for message ${messageId}:`, error);
    }
  }

  /**
   * Process a negative email event (bounce, complaint)
   */
  private async processNegativeEvent(messageId: string, eventType: string, data: Record<string, any>): Promise<void> {
    try {
      // Find tracking record by message ID
      const tracking = await emailTrackingService.findByMessageId(messageId);

      if (!tracking) {
        logger.warn(`No tracking record found for message ID ${messageId}`);
        return;
      }

      // Map event type to EmailEventType
      const type = eventType === 'email.bounced' ? 'bounced' : 'spam';

      // Add event to tracking record
      tracking.events.push({
        type: type as any,
        timestamp: new Date(),
        metadata: {
          reason: data.reason || 'Unknown',
          description: data.description || '',
          ...data
        }
      });

      // Update status
      tracking.status = type as any;
      await tracking.save();

      logger.info(`Processed ${eventType} event for message ${messageId}`);
    } catch (error) {
      logger.error(`Error processing ${eventType} event for message ${messageId}:`, error);
    }
  }

  /**
   * Extract message ID from webhook event data
   */
  private extractMessageId(data: Record<string, any>): string | null {
    // Try to get from headers
    if (data.headers && data.headers['X-Aizako-Message-ID']) {
      return data.headers['X-Aizako-Message-ID'];
    }

    // Try to get from custom ID
    if (data.message_id) {
      return data.message_id;
    }

    return null;
  }

  /**
   * Detect device type from user agent
   */
  private detectDevice(userAgent: string): string {
    const ua = userAgent.toLowerCase();

    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone') || ua.includes('ipad')) {
      return 'mobile';
    } else if (ua.includes('tablet')) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /**
   * Make a request to the Resend API
   */
  private async makeRequest(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    data?: any
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await axios({
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        data: data ? JSON.stringify(data) : undefined
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Enable or disable tracking for a domain
   */
  async updateDomainTracking(domainId: string, options: {
    openTracking?: boolean;
    clickTracking?: boolean;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const data: Record<string, boolean> = {};

      if (options.openTracking !== undefined) {
        data.open_tracking = options.openTracking;
      }

      if (options.clickTracking !== undefined) {
        data.click_tracking = options.clickTracking;
      }

      const response = await this.makeRequest(`/domains/${domainId}`, 'PATCH', data);

      if (response.success) {
        logger.info(`Updated tracking settings for domain ${domainId}`);
        return { success: true };
      } else {
        logger.error(`Failed to update tracking settings for domain ${domainId}: ${response.error}`);
        return { success: false, error: response.error };
      }
    } catch (error) {
      logger.error(`Error updating tracking settings for domain ${domainId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

export default new ResendService();
