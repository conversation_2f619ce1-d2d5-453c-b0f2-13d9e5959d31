import { DocumentModel, Activity, Opportunity } from '../models/mongoose';
import mongoose from 'mongoose';
import axios from 'axios';

// Initialize Voyage AI client
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY;
const VOYAGE_API_URL = 'https://api.voyageai.com/v1/embeddings';

/**
 * Service for generating and managing vector embeddings with MongoDB Vector Search
 */
export class VectorEmbeddingService {
  private static instance: VectorEmbeddingService;
  private mongoClient: mongoose.Mongoose;
  private db: mongoose.Connection;

  private constructor() {
    // Private constructor for singleton pattern
    this.mongoClient = mongoose;
    this.db = mongoose.connection;

    // Ensure vector indexes are created
    this.ensureVectorIndexes();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): VectorEmbeddingService {
    if (!VectorEmbeddingService.instance) {
      VectorEmbeddingService.instance = new VectorEmbeddingService();
    }
    return VectorEmbeddingService.instance;
  }

  /**
   * Ensure vector indexes are created in MongoDB
   */
  private async ensureVectorIndexes(): Promise<void> {
    try {
      // Check if we have an active connection
      if (this.db.readyState !== 1) {
        console.log('MongoDB connection not ready, skipping vector index creation');
        return;
      }

      console.log('Ensuring vector indexes are created in MongoDB...');

      // Create vector index for documents if it doesn't exist
      const documentCollection = this.db.collection('documents');
      const documentIndexes = await documentCollection.listIndexes().toArray();

      if (!documentIndexes.some(index => index.name === 'vector_index')) {
        await documentCollection.createIndex(
          { vector_embedding: "vector" },
          {
            name: "vector_index",
            vectorSize: 1024,  // Voyage 3 embedding size
            vectorSearchOptions: { similarity: "cosine" }
          }
        );
        console.log('Created vector index for documents collection');
      }

      // Create vector index for activities if it doesn't exist
      const activityCollection = this.db.collection('activities');
      const activityIndexes = await activityCollection.listIndexes().toArray();

      if (!activityIndexes.some(index => index.name === 'vector_index')) {
        await activityCollection.createIndex(
          { vector_embedding: "vector" },
          {
            name: "vector_index",
            vectorSize: 1024,  // Voyage 3 embedding size
            vectorSearchOptions: { similarity: "cosine" }
          }
        );
        console.log('Created vector index for activities collection');
      }

      console.log('Vector indexes verified');
    } catch (error) {
      console.error('Error ensuring vector indexes:', error);
    }
  }

  /**
   * Generate embeddings for a text using Voyage AI
   */
  public async generateEmbedding(text: string): Promise<number[]> {
    try {
      if (!VOYAGE_API_KEY) {
        throw new Error('VOYAGE_API_KEY is not configured');
      }

      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-3',
          input: text,
          encoding_format: 'float'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      return response.data.data[0].embedding;
    } catch (error) {
      console.error('Error generating embedding with Voyage AI:', error);
      throw error;
    }
  }

  /**
   * Store embeddings for a document
   */
  public async storeDocumentEmbedding(documentId: string): Promise<boolean> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(documentId)) {
        throw new Error('Invalid document ID format');
      }

      const documentObjectId = new mongoose.Types.ObjectId(documentId);

      // Get the document
      const document = await DocumentModel.findById(documentObjectId);
      if (!document) {
        throw new Error('Document not found');
      }

      // Generate content from document
      const content = `${document.name} ${document.description || ''}`;

      // Generate embedding
      const embedding = await this.generateEmbedding(content);

      // Store embedding in MongoDB
      await this.db.collection('documents').updateOne(
        { _id: documentObjectId },
        { $set: {
            vector_embedding: embedding,
            vector_content: content,
            vector_updated_at: new Date()
          }
        }
      );

      return true;
    } catch (error) {
      console.error('Error storing document embedding:', error);
      return false;
    }
  }

  /**
   * Store embeddings for an activity
   */
  public async storeActivityEmbedding(activityId: string): Promise<boolean> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(activityId)) {
        throw new Error('Invalid activity ID format');
      }

      const activityObjectId = new mongoose.Types.ObjectId(activityId);

      // Get the activity
      const activity = await Activity.findById(activityObjectId);
      if (!activity) {
        throw new Error('Activity not found');
      }

      // Generate content from activity
      const content = `${activity.title} ${activity.description || ''} ${activity.notes || ''}`;

      // Generate embedding
      const embedding = await this.generateEmbedding(content);

      // Store embedding in MongoDB
      await this.db.collection('activities').updateOne(
        { _id: activityObjectId },
        { $set: {
            vector_embedding: embedding,
            vector_content: content,
            vector_updated_at: new Date()
          }
        }
      );

      return true;
    } catch (error) {
      console.error('Error storing activity embedding:', error);
      return false;
    }
  }

  /**
   * Process embeddings for all documents and activities related to an opportunity
   */
  public async processOpportunityContent(opportunityId: string): Promise<boolean> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Get documents related to this opportunity
      const documents = await DocumentModel.find({
        'relatedTo.type': 'opportunity',
        'relatedTo.id': opportunityObjectId
      });

      // Get activities related to this opportunity
      const activities = await Activity.find({
        opportunityId: opportunityObjectId,
        type: { $in: ['call', 'email', 'meeting', 'note'] }
      });

      // Process document embeddings
      for (const doc of documents) {
        await this.storeDocumentEmbedding(doc._id.toString());
      }

      // Process activity embeddings
      for (const activity of activities) {
        await this.storeActivityEmbedding(activity._id.toString());
      }

      return true;
    } catch (error) {
      console.error('Error processing opportunity content:', error);
      return false;
    }
  }

  /**
   * Get embeddings for opportunity-related documents and activities
   * This method retrieves pre-computed embeddings from MongoDB
   */
  public async getOpportunityContentEmbeddings(opportunityId: string): Promise<{
    documents: Array<{ id: string; content: string; embedding: number[]; metadata: any }>;
    activities: Array<{ id: string; content: string; embedding: number[]; metadata: any }>;
  }> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Ensure all content has embeddings
      await this.processOpportunityContent(opportunityId);

      // Get documents with embeddings
      const documents = await this.db.collection('documents').find({
        'relatedTo.type': 'opportunity',
        'relatedTo.id': opportunityObjectId,
        'vector_embedding': { $exists: true }
      }).toArray();

      // Get activities with embeddings
      const activities = await this.db.collection('activities').find({
        opportunityId: opportunityObjectId,
        type: { $in: ['call', 'email', 'meeting', 'note'] },
        'vector_embedding': { $exists: true }
      }).toArray();

      // Format document results
      const documentEmbeddings = documents.map(doc => ({
        id: doc._id.toString(),
        content: doc.vector_content || `${doc.name} ${doc.description || ''}`,
        embedding: doc.vector_embedding,
        metadata: {
          type: 'document',
          name: doc.name,
          fileType: doc.fileType,
          createdAt: doc.createdAt
        }
      }));

      // Format activity results
      const activityEmbeddings = activities.map(activity => ({
        id: activity._id.toString(),
        content: activity.vector_content || `${activity.title} ${activity.description || ''} ${activity.notes || ''}`,
        embedding: activity.vector_embedding,
        metadata: {
          type: 'activity',
          activityType: activity.type,
          date: activity.date,
          completed: activity.completed
        }
      }));

      return {
        documents: documentEmbeddings,
        activities: activityEmbeddings
      };
    } catch (error) {
      console.error('Error getting opportunity content embeddings:', error);
      throw error;
    }
  }

  /**
   * Find similar content based on a query using MongoDB vector search
   */
  public async findSimilarContent(
    opportunityId: string,
    query: string,
    limit: number = 5
  ): Promise<Array<{ id: string; content: string; similarity: number; metadata: any }>> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Ensure all content has embeddings
      await this.processOpportunityContent(opportunityId);

      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);

      // Search for similar documents using MongoDB vector search
      const documentResults = await this.db.collection('documents').aggregate([
        {
          $search: {
            vectorSearch: {
              queryVector: queryEmbedding,
              path: "vector_embedding",
              numCandidates: 100,
              limit: limit
            }
          }
        },
        {
          $match: {
            'relatedTo.type': 'opportunity',
            'relatedTo.id': opportunityObjectId
          }
        },
        {
          $project: {
            _id: 1,
            name: 1,
            description: 1,
            fileType: 1,
            createdAt: 1,
            vector_content: 1,
            score: { $meta: "searchScore" }
          }
        },
        {
          $limit: limit
        }
      ]).toArray();

      // Search for similar activities using MongoDB vector search
      const activityResults = await this.db.collection('activities').aggregate([
        {
          $search: {
            vectorSearch: {
              queryVector: queryEmbedding,
              path: "vector_embedding",
              numCandidates: 100,
              limit: limit
            }
          }
        },
        {
          $match: {
            opportunityId: opportunityObjectId,
            type: { $in: ['call', 'email', 'meeting', 'note'] }
          }
        },
        {
          $project: {
            _id: 1,
            title: 1,
            description: 1,
            notes: 1,
            type: 1,
            date: 1,
            completed: 1,
            vector_content: 1,
            score: { $meta: "searchScore" }
          }
        },
        {
          $limit: limit
        }
      ]).toArray();

      // Format document results
      const documentItems = documentResults.map(doc => ({
        id: doc._id.toString(),
        content: doc.vector_content || `${doc.name} ${doc.description || ''}`,
        similarity: doc.score,
        metadata: {
          type: 'document',
          name: doc.name,
          fileType: doc.fileType,
          createdAt: doc.createdAt
        }
      }));

      // Format activity results
      const activityItems = activityResults.map(activity => ({
        id: activity._id.toString(),
        content: activity.vector_content || `${activity.title} ${activity.description || ''} ${activity.notes || ''}`,
        similarity: activity.score,
        metadata: {
          type: 'activity',
          activityType: activity.type,
          date: activity.date,
          completed: activity.completed
        }
      }));

      // Combine and sort results
      const combinedResults = [...documentItems, ...activityItems]
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

      return combinedResults;
    } catch (error) {
      console.error('Error finding similar content with vector search:', error);

      // Fallback to traditional search if vector search fails
      console.log('Falling back to traditional search...');
      return this.findSimilarContentFallback(opportunityId, query, limit);
    }
  }

  /**
   * Fallback method for finding similar content when vector search is not available
   */
  private async findSimilarContentFallback(
    opportunityId: string,
    query: string,
    limit: number = 5
  ): Promise<Array<{ id: string; content: string; similarity: number; metadata: any }>> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Get documents related to this opportunity
      const documents = await DocumentModel.find({
        'relatedTo.type': 'opportunity',
        'relatedTo.id': opportunityObjectId
      }).limit(20);

      // Get activities related to this opportunity
      const activities = await Activity.find({
        opportunityId: opportunityObjectId,
        type: { $in: ['call', 'email', 'meeting', 'note'] }
      }).sort({ date: -1 }).limit(20);

      // Simple text matching function
      const calculateTextSimilarity = (text: string, queryText: string): number => {
        const textLower = text.toLowerCase();
        const queryLower = queryText.toLowerCase();

        // Count occurrences of query terms in the text
        const queryTerms = queryLower.split(/\s+/).filter(term => term.length > 2);
        let matchCount = 0;

        for (const term of queryTerms) {
          if (textLower.includes(term)) {
            matchCount++;
          }
        }

        return queryTerms.length > 0 ? matchCount / queryTerms.length : 0;
      };

      // Process documents
      const documentItems = documents.map(doc => {
        const content = `${doc.name} ${doc.description || ''}`;
        return {
          id: doc._id.toString(),
          content,
          similarity: calculateTextSimilarity(content, query),
          metadata: {
            type: 'document',
            name: doc.name,
            fileType: doc.fileType,
            createdAt: doc.createdAt
          }
        };
      });

      // Process activities
      const activityItems = activities.map(activity => {
        const content = `${activity.title} ${activity.description || ''} ${activity.notes || ''}`;
        return {
          id: activity._id.toString(),
          content,
          similarity: calculateTextSimilarity(content, query),
          metadata: {
            type: 'activity',
            activityType: activity.type,
            date: activity.date,
            completed: activity.completed
          }
        };
      });

      // Combine, sort by similarity, and return top results
      return [...documentItems, ...activityItems]
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      console.error('Error in fallback content search:', error);
      return [];
    }
  }
}

export default VectorEmbeddingService;
