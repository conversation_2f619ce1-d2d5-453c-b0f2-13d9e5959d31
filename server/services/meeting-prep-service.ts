import mongoose from 'mongoose';
import axios from 'axios';
import {
  Contact,
  Company,
  Opportunity,
  Activity,
  IContact,
  ICompany,
  IOpportunity,
  IActivity
} from '../models/mongoose';
import { DocumentModel } from '../models/mongoose';
import { graphRAGService } from './graph-rag-service';
import { logger } from '../utils/logger';

// Define the Meeting Prep Document structure
export interface MeetingPrep {
  id: string;
  title: string;
  createdAt: Date;
  contactId?: string;
  companyId?: string;
  opportunityId?: string;
  summary: string;
  accountHistory: {
    keyEvents: Array<{
      date: Date;
      description: string;
      type: string;
    }>;
    timeline: string;
  };
  relationshipHistory: Array<{
    timestamp: string;
    type: string;
    nodeType: string;
    nodeId: string;
    nodeName: string;
    properties: any;
  }>;
  stakeholders: Array<{
    name: string;
    role: string;
    influence: string;
    notes: string;
  }>;
  companyNews: Array<{
    date: Date;
    title: string;
    source: string;
    summary: string;
    url?: string;
  }>;
  talkingPoints: Array<{
    topic: string;
    points: string[];
    priority: number;
  }>;
  buyingSignals: Array<{
    id: string;
    content: string;
    type: string;
    date: Date;
    source: string;
  }>;
  predictedQuestions: Array<{
    question: string;
    category: string;
    importance: string;
  }>;
  suggestedCollateral: Array<{
    id: string;
    name: string;
    type: string;
    url: string;
  }>;
  nextSteps: string[];
  documentUrl: string;
  addToAgendaUrl?: string;
}

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

/**
 * Service for generating meeting preparation documents
 */
export class MeetingPrepService {
  /**
   * Generate a meeting prep document for a contact, company, or opportunity
   */
  async generateMeetingPrep(
    params: {
      contactId?: string;
      companyId?: string;
      opportunityId?: string;
      userId: string;
    }
  ): Promise<MeetingPrep> {
    try {
      const { contactId, companyId, opportunityId, userId } = params;

      if (!contactId && !companyId && !opportunityId) {
        throw new Error('At least one of contactId, companyId, or opportunityId must be provided');
      }

      // Get the tenant ID from the user
      const user = await mongoose.model('User').findById(userId);
      if (!user) {
        throw new Error('User not found');
      }
      const tenantId = user.tenantId?.toString() || 'default';

      // Convert string IDs to ObjectIds
      const contactObjectId = contactId ? new mongoose.Types.ObjectId(contactId) : undefined;
      const companyObjectId = companyId ? new mongoose.Types.ObjectId(companyId) : undefined;
      const opportunityObjectId = opportunityId ? new mongoose.Types.ObjectId(opportunityId) : undefined;

      // Fetch data based on provided IDs
      const [contact, company, opportunity, activities] = await Promise.all([
        contactId ? Contact.findById(contactObjectId) : null,
        companyId ? Company.findById(companyObjectId) : null,
        opportunityId ? Opportunity.findById(opportunityObjectId) : null,
        this.getRelatedActivities(contactObjectId, companyObjectId, opportunityObjectId)
      ]);

      // If we have a contact but no company, try to get the company from the contact
      let enhancedCompany = company;
      if (contact && contact.companyId && !company) {
        enhancedCompany = await Company.findById(contact.companyId);
      }

      // If we have an opportunity but no contact or company, try to get them
      let updatedContact = contact;
      if (opportunity) {
        if (!updatedContact && opportunity.contactId) {
          const opportunityContact = await Contact.findById(opportunity.contactId);
          if (opportunityContact) {
            // Only use this if we don't already have a contact
            updatedContact = opportunityContact;
          }
        }

        if (!enhancedCompany && opportunity.companyId) {
          const opportunityCompany = await Company.findById(opportunity.companyId);
          if (opportunityCompany) {
            enhancedCompany = opportunityCompany;
          }
        }
      }

      // Get stakeholders if we have a company
      const stakeholders = enhancedCompany
        ? await this.getCompanyStakeholders(enhancedCompany._id)
        : [];

      // Get company news if we have a company
      const companyNews = enhancedCompany
        ? await this.getCompanyNews(enhancedCompany.name)
        : [];

      // Get GraphRAG context
      logger.info('Getting GraphRAG context for meeting prep');
      const graphRAGContext = await graphRAGService.getMeetingPrepContext({
        contactId,
        companyId,
        opportunityId,
        tenantId
      });

      // Generate the meeting prep using AI with GraphRAG context
      const meetingPrep = await this.generateMeetingPrepWithAI(
        updatedContact,
        enhancedCompany,
        opportunity,
        activities,
        stakeholders,
        companyNews,
        graphRAGContext
      );

      // Create a PDF document and store it
      const documentUrl = await this.createPdfDocument(meetingPrep, userId);

      // Add the document URL to the meeting prep
      meetingPrep.documentUrl = documentUrl;

      // Record wizard usage for analytics
      await graphRAGService.recordWizardUsage({
        wizardType: 'meeting_prep',
        userId,
        tenantId,
        entityId: opportunityId || companyId || contactId,
        entityType: opportunityId ? 'opportunity' : companyId ? 'company' : 'contact',
        result: 'success',
        metadata: {
          documentUrl
        }
      });

      return meetingPrep;
    } catch (error) {
      logger.error('Error generating meeting prep:', error);

      // Record wizard usage failure
      if (params.userId) {
        try {
          const user = await mongoose.model('User').findById(params.userId);
          const tenantId = user?.tenantId?.toString() || 'default';

          await graphRAGService.recordWizardUsage({
            wizardType: 'meeting_prep',
            userId: params.userId,
            tenantId,
            entityId: params.opportunityId || params.companyId || params.contactId,
            entityType: params.opportunityId ? 'opportunity' : params.companyId ? 'company' : 'contact',
            result: 'failure',
            metadata: {
              error: error.message
            }
          });
        } catch (recordError) {
          logger.error('Error recording wizard usage failure:', recordError);
        }
      }

      throw error;
    }
  }

  /**
   * Get activities related to a contact, company, or opportunity
   */
  private async getRelatedActivities(
    contactId?: mongoose.Types.ObjectId,
    companyId?: mongoose.Types.ObjectId,
    opportunityId?: mongoose.Types.ObjectId
  ): Promise<IActivity[]> {
    const query: any = {};

    if (contactId) {
      query.contactId = contactId;
    }

    if (companyId) {
      query.companyId = companyId;
    }

    if (opportunityId) {
      query.opportunityId = opportunityId;
    }

    return Activity.find(query)
      .sort({ date: -1 })
      .limit(20);
  }

  /**
   * Get stakeholders for a company
   */
  private async getCompanyStakeholders(
    companyId: mongoose.Types.ObjectId
  ): Promise<IContact[]> {
    return Contact.find({ companyId })
      .sort({ updatedAt: -1 });
  }

  /**
   * Get recent news for a company using web search
   */
  private async getCompanyNews(
    companyName: string
  ): Promise<Array<{
    date: Date;
    title: string;
    source: string;
    summary: string;
    url?: string;
  }>> {
    try {
      // Try to use the AI service for web search
      const response = await axios.post(`${AI_SERVICE_URL}/crew`, {
        task: `Find recent news about ${companyName}. Focus on business news, financial results, product launches, and major announcements. Return the results as a JSON array with date, title, source, summary, and url fields.`,
        userId: 1, // System user
        context: {
          companyName
        }
      });

      if (response.data && response.data.response) {
        try {
          // Try to parse the response as JSON
          const newsData = JSON.parse(response.data.response);
          if (Array.isArray(newsData)) {
            return newsData.map(item => ({
              ...item,
              date: new Date(item.date)
            }));
          }
        } catch (e) {
          console.error('Error parsing news data:', e);
        }
      }

      // Fallback to empty array if we couldn't get news
      return [];
    } catch (error) {
      console.error('Error fetching company news:', error);
      return [];
    }
  }

  /**
   * Generate meeting prep document using Voyage AI with GraphRAG context
   */
  private async generateMeetingPrepWithAI(
    contact: IContact | null,
    company: ICompany | null,
    opportunity: IOpportunity | null,
    activities: IActivity[],
    stakeholders: IContact[],
    companyNews: Array<{
      date: Date;
      title: string;
      source: string;
      summary: string;
      url?: string;
    }>,
    graphRAGContext?: {
      relationshipHistory: any[];
      stakeholders: any[];
      buyingSignals: any[];
      predictedQuestions: any[];
      suggestedCollateral: any[];
      companyNews?: any[];
    }
  ): Promise<MeetingPrep> {
    try {
      // Prepare data for Voyage AI
      const data = {
        contact: contact ? {
          id: contact._id.toString(),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title,
          status: contact.status,
          notes: contact.notes,
          tags: contact.tags,
          createdAt: contact.createdAt,
          updatedAt: contact.updatedAt
        } : null,
        company: company ? {
          id: company._id.toString(),
          name: company.name,
          domain: company.domain,
          industry: company.industry,
          size: company.size,
          location: company.location,
          description: company.description,
          website: company.website,
          status: company.status,
          tags: company.tags,
          notes: company.notes,
          createdAt: company.createdAt,
          updatedAt: company.updatedAt
        } : null,
        opportunity: opportunity ? {
          id: opportunity._id.toString(),
          name: opportunity.name,
          value: opportunity.value,
          currency: opportunity.currency,
          stage: opportunity.stage,
          probability: opportunity.probability,
          expectedCloseDate: opportunity.expectedCloseDate,
          description: opportunity.description,
          tags: opportunity.tags,
          createdAt: opportunity.createdAt,
          updatedAt: opportunity.updatedAt
        } : null,
        activities: activities.map(activity => ({
          id: activity._id.toString(),
          type: activity.type,
          title: activity.title,
          description: activity.description,
          date: activity.date,
          completed: activity.completed,
          outcome: activity.outcome,
          createdAt: activity.createdAt,
          updatedAt: activity.updatedAt
        })),
        stakeholders: stakeholders.map(stakeholder => ({
          id: stakeholder._id.toString(),
          name: `${stakeholder.firstName} ${stakeholder.lastName}`,
          email: stakeholder.email,
          phone: stakeholder.phone,
          title: stakeholder.title,
          status: stakeholder.status,
          notes: stakeholder.notes,
          tags: stakeholder.tags
        })),
        companyNews,
        // Include GraphRAG context if available
        graphRAG: graphRAGContext ? {
          relationshipHistory: graphRAGContext.relationshipHistory || [],
          enhancedStakeholders: graphRAGContext.stakeholders || [],
          buyingSignals: graphRAGContext.buyingSignals || [],
          predictedQuestions: graphRAGContext.predictedQuestions || [],
          suggestedCollateral: graphRAGContext.suggestedCollateral || [],
          enhancedCompanyNews: graphRAGContext.companyNews || []
        } : null
      };

      // Create prompt for Voyage AI
      const prompt = `
        Generate a comprehensive meeting preparation document.

        Analyze all the provided information including the contact details, company information, opportunity details, activities, stakeholders, company news, and the GraphRAG context.

        Pay special attention to the GraphRAG context which contains:
        - Relationship history from the graph database
        - Enhanced stakeholder information
        - Buying signals detected in previous interactions
        - Predicted questions the prospect might ask
        - Suggested collateral to share during the meeting
        - Enhanced company news

        The meeting prep document should include:
        1. A concise summary of the meeting purpose and context
        2. Account history with key events and timeline
        3. Relationship history showing connections between entities
        4. Stakeholder analysis with roles, influence, and notes
        5. Recent company news with summaries
        6. Buying signals detected in previous interactions
        7. Talking points organized by priority
        8. Predicted questions the prospect might ask
        9. Suggested collateral to share during the meeting
        10. Suggested next steps after the meeting

        Format the response as a JSON object with the following structure:
        {
          "title": "Meeting Prep: [Company/Contact Name]",
          "summary": "Brief overview of the meeting purpose",
          "accountHistory": {
            "keyEvents": [
              { "date": "YYYY-MM-DD", "description": "Event description", "type": "call/email/meeting" }
            ],
            "timeline": "Narrative summary of the account history"
          },
          "relationshipHistory": [
            { "timestamp": "YYYY-MM-DD", "type": "RELATIONSHIP_TYPE", "nodeType": "Entity Type", "nodeName": "Entity Name" }
          ],
          "stakeholders": [
            { "name": "Full Name", "role": "Job Title", "influence": "Decision Maker/Influencer/User", "notes": "Relevant details" }
          ],
          "companyNews": [
            { "date": "YYYY-MM-DD", "title": "News headline", "source": "Source name", "summary": "Brief summary" }
          ],
          "buyingSignals": [
            { "id": "signal-id", "content": "Signal content", "type": "Signal type", "date": "YYYY-MM-DD", "source": "Source" }
          ],
          "talkingPoints": [
            { "topic": "Topic name", "points": ["Point 1", "Point 2"], "priority": 1-5 }
          ],
          "predictedQuestions": [
            { "question": "Question text", "category": "Category", "importance": "high/medium/low" }
          ],
          "suggestedCollateral": [
            { "id": "collateral-id", "name": "Collateral name", "type": "pdf/spreadsheet/image", "url": "URL" }
          ],
          "nextSteps": ["Step 1", "Step 2"]
        }
      `;

      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/crew`, {
          task: prompt,
          userId: 1, // System user
          context: data
        }, {
          timeout: 30000 // 30 second timeout
        });

        if (aiResponse.data && aiResponse.data.response) {
          try {
            // Try to parse the response as JSON
            const meetingPrepData = JSON.parse(aiResponse.data.response);

            // Create a unique ID for the meeting prep
            const id = new mongoose.Types.ObjectId().toString();

            return {
              id,
              createdAt: new Date(),
              contactId: contact?._id.toString(),
              companyId: company?._id.toString(),
              opportunityId: opportunity?._id.toString(),
              documentUrl: '', // Will be filled in later
              ...meetingPrepData
            };
          } catch (e) {
            console.error('Error parsing AI service response:', e);
          }
        }
      } catch (aiError) {
        console.error('Error using AI service, falling back to Voyage AI:', aiError);
      }

      // Fallback to Voyage AI if the AI service fails
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales analyst for Aizako CRM.
              Your job is to analyze meeting information and provide comprehensive meeting preparation documents.
              Focus on extracting key insights that help sales reps prepare for successful meetings.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.7,
          max_tokens: 2000,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      if (response.data && response.data.choices && response.data.choices[0].message.content) {
        const meetingPrepData = JSON.parse(response.data.choices[0].message.content);

        // Create a unique ID for the meeting prep
        const id = new mongoose.Types.ObjectId().toString();

        return {
          id,
          createdAt: new Date(),
          contactId: contact?._id.toString(),
          companyId: company?._id.toString(),
          opportunityId: opportunity?._id.toString(),
          documentUrl: '', // Will be filled in later
          ...meetingPrepData
        };
      }

      throw new Error('Failed to generate meeting prep document');
    } catch (error) {
      console.error('Error generating meeting prep with AI:', error);
      throw error;
    }
  }

  /**
   * Create a PDF document from the meeting prep data and store it
   *
   * TODO: Future improvement - Integrate with MongoDB Atlas for document storage
   * See docs/mongodb-atlas-storage.md for detailed implementation plan
   * Options include:
   * 1. Using GridFS to store files directly in MongoDB
   * 2. Using MongoDB Atlas with AWS S3 integration
   */
  private async createPdfDocument(
    meetingPrep: MeetingPrep,
    userId: string
  ): Promise<string> {
    try {
      // Convert the meeting prep to HTML
      const htmlContent = this.convertMeetingPrepToHtml(meetingPrep);

      // Try to use the AI service to convert HTML to PDF
      try {
        const pdfResponse = await axios.post(
          `${AI_SERVICE_URL}/documents/html-to-pdf`,
          {
            html: htmlContent,
            filename: `meeting_prep_${meetingPrep.id}.pdf`
          },
          {
            responseType: 'arraybuffer',
            timeout: 30000 // 30 second timeout
          }
        );

        // Create a buffer from the response
        const pdfBuffer = Buffer.from(pdfResponse.data);

        // Save the PDF to the document storage
        const document = new DocumentModel({
          name: meetingPrep.title,
          description: `Meeting preparation document for ${meetingPrep.title.replace('Meeting Prep: ', '')}`,
          fileType: 'pdf',
          mimeType: 'application/pdf',
          size: pdfBuffer.length,
          url: `/api/documents/${meetingPrep.id}/content`,
          path: `/documents/meeting_prep_${meetingPrep.id}.pdf`,
          isPublic: false,
          owner: new mongoose.Types.ObjectId(userId),
          relatedTo: meetingPrep.opportunityId ? {
            type: 'opportunity',
            id: new mongoose.Types.ObjectId(meetingPrep.opportunityId)
          } : meetingPrep.companyId ? {
            type: 'company',
            id: new mongoose.Types.ObjectId(meetingPrep.companyId)
          } : meetingPrep.contactId ? {
            type: 'contact',
            id: new mongoose.Types.ObjectId(meetingPrep.contactId)
          } : undefined,
          tags: ['meeting-prep', 'ai-generated'],
          metadata: {
            meetingPrepId: meetingPrep.id,
            generatedAt: new Date().toISOString()
          }
        });

        await document.save();

        // Save the PDF content to a file
        // Note: This is a temporary solution. In production, we'll use MongoDB Atlas
        // for document storage instead of the local file system.
        const fs = require('fs');
        const path = require('path');
        const uploadsDir = path.join(process.cwd(), 'uploads');

        // Ensure the uploads directory exists
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }

        const filePath = path.join(uploadsDir, `meeting_prep_${meetingPrep.id}.pdf`);
        fs.writeFileSync(filePath, pdfBuffer);

        return `/api/documents/${document._id}/content`;
      } catch (pdfError) {
        console.error('Error generating PDF with AI service:', pdfError);

        // Fallback to storing HTML as a document
        const document = new DocumentModel({
          name: meetingPrep.title,
          description: `Meeting preparation document for ${meetingPrep.title.replace('Meeting Prep: ', '')}`,
          fileType: 'html',
          mimeType: 'text/html',
          size: htmlContent.length,
          url: `/api/documents/${meetingPrep.id}/content`,
          path: `/documents/meeting_prep_${meetingPrep.id}.html`,
          isPublic: false,
          owner: new mongoose.Types.ObjectId(userId),
          relatedTo: meetingPrep.opportunityId ? {
            type: 'opportunity',
            id: new mongoose.Types.ObjectId(meetingPrep.opportunityId)
          } : meetingPrep.companyId ? {
            type: 'company',
            id: new mongoose.Types.ObjectId(meetingPrep.companyId)
          } : meetingPrep.contactId ? {
            type: 'contact',
            id: new mongoose.Types.ObjectId(meetingPrep.contactId)
          } : undefined,
          tags: ['meeting-prep', 'ai-generated'],
          metadata: {
            meetingPrepId: meetingPrep.id,
            generatedAt: new Date().toISOString(),
            format: 'html' // Indicate this is HTML, not PDF
          }
        });

        await document.save();

        // Save the HTML content to a file
        const fs = require('fs');
        const path = require('path');
        const uploadsDir = path.join(process.cwd(), 'uploads');

        // Ensure the uploads directory exists
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }

        const filePath = path.join(uploadsDir, `meeting_prep_${meetingPrep.id}.html`);
        fs.writeFileSync(filePath, htmlContent);

        return `/api/documents/${document._id}/content`;
      }
    } catch (error) {
      console.error('Error creating PDF document:', error);
      throw error;
    }
  }

  /**
   * Convert meeting prep data to HTML
   */
  private convertMeetingPrepToHtml(meetingPrep: MeetingPrep): string {
    // Format date for display
    const formatDate = (dateString: string | Date) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    };

    // Create HTML content
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${meetingPrep.title}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          h1 {
            color: #2563eb;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
          }
          h2 {
            color: #4b5563;
            margin-top: 25px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
          }
          .summary {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
          }
          .event {
            margin-bottom: 10px;
          }
          .event-date {
            font-weight: bold;
            color: #6b7280;
          }
          .event-type {
            color: #2563eb;
            font-size: 0.9em;
            text-transform: uppercase;
          }
          .stakeholder {
            margin-bottom: 15px;
          }
          .stakeholder-name {
            font-weight: bold;
          }
          .stakeholder-role {
            color: #6b7280;
            font-style: italic;
          }
          .news-item {
            margin-bottom: 15px;
          }
          .news-date {
            color: #6b7280;
            font-size: 0.9em;
          }
          .news-title {
            font-weight: bold;
          }
          .news-source {
            color: #6b7280;
            font-style: italic;
          }
          .talking-point {
            margin-bottom: 20px;
          }
          .talking-point-topic {
            font-weight: bold;
            color: #2563eb;
          }
          .priority {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            margin-left: 10px;
          }
          .priority-1 {
            background-color: #fee2e2;
            color: #b91c1c;
          }
          .priority-2 {
            background-color: #ffedd5;
            color: #c2410c;
          }
          .priority-3 {
            background-color: #fef3c7;
            color: #92400e;
          }
          .priority-4 {
            background-color: #ecfccb;
            color: #4d7c0f;
          }
          .priority-5 {
            background-color: #d1fae5;
            color: #065f46;
          }
          ul {
            padding-left: 20px;
          }
          .next-steps {
            background-color: #dbeafe;
            padding: 15px;
            border-radius: 5px;
          }
          .relationship-item {
            margin-bottom: 10px;
            padding: 5px;
            border-left: 3px solid #2563eb;
            padding-left: 10px;
          }
          .buying-signal {
            background-color: #fef3c7;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
          }
          .predicted-question {
            margin-bottom: 10px;
          }
          .question-importance-high {
            color: #b91c1c;
            font-weight: bold;
          }
          .question-importance-medium {
            color: #92400e;
          }
          .question-importance-low {
            color: #4d7c0f;
          }
          .collateral-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 5px;
            background-color: #f3f4f6;
            border-radius: 5px;
          }
          .collateral-icon {
            margin-right: 10px;
            color: #4b5563;
          }
          .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 0.8em;
            color: #9ca3af;
          }
        </style>
      </head>
      <body>
        <h1>${meetingPrep.title}</h1>

        <div class="summary">
          <p>${meetingPrep.summary}</p>
        </div>

        <h2>Account History</h2>
        <div>
          ${meetingPrep.accountHistory.keyEvents.map(event => `
            <div class="event">
              <span class="event-date">${formatDate(event.date)}</span> -
              <span class="event-type">${event.type}</span>:
              ${event.description}
            </div>
          `).join('')}

          <p>${meetingPrep.accountHistory.timeline}</p>
        </div>

        <h2>Stakeholders</h2>
        <div>
          ${meetingPrep.stakeholders.map(stakeholder => `
            <div class="stakeholder">
              <div class="stakeholder-name">${stakeholder.name}</div>
              <div class="stakeholder-role">${stakeholder.role} - ${stakeholder.influence}</div>
              <p>${stakeholder.notes}</p>
            </div>
          `).join('')}
        </div>

        <h2>Recent Company News</h2>
        <div>
          ${meetingPrep.companyNews.map(news => `
            <div class="news-item">
              <div class="news-date">${formatDate(news.date)}</div>
              <div class="news-title">${news.title}</div>
              <div class="news-source">${news.source}</div>
              <p>${news.summary}</p>
            </div>
          `).join('')}
        </div>

        <h2>Talking Points</h2>
        <div>
          ${meetingPrep.talkingPoints.map(point => `
            <div class="talking-point">
              <div>
                <span class="talking-point-topic">${point.topic}</span>
                <span class="priority priority-${point.priority}">Priority ${point.priority}</span>
              </div>
              <ul>
                ${point.points.map(p => `<li>${p}</li>`).join('')}
              </ul>
            </div>
          `).join('')}
        </div>

        ${meetingPrep.relationshipHistory && meetingPrep.relationshipHistory.length > 0 ? `
        <h2>Relationship History</h2>
        <div>
          ${meetingPrep.relationshipHistory.map(item => `
            <div class="relationship-item">
              <span class="event-date">${formatDate(item.timestamp)}</span> -
              <span class="event-type">${item.type}</span>:
              ${item.nodeType} ${item.nodeName}
            </div>
          `).join('')}
        </div>
        ` : ''}

        ${meetingPrep.buyingSignals && meetingPrep.buyingSignals.length > 0 ? `
        <h2>Buying Signals</h2>
        <div>
          ${meetingPrep.buyingSignals.map(signal => `
            <div class="buying-signal">
              <div class="event-date">${formatDate(signal.date)}</div>
              <div class="event-type">${signal.type}</div>
              <p>${signal.content}</p>
              <div class="news-source">Source: ${signal.source}</div>
            </div>
          `).join('')}
        </div>
        ` : ''}

        ${meetingPrep.predictedQuestions && meetingPrep.predictedQuestions.length > 0 ? `
        <h2>Predicted Questions</h2>
        <div>
          ${meetingPrep.predictedQuestions.map(question => `
            <div class="predicted-question">
              <span class="question-importance-${question.importance.toLowerCase()}">${question.question}</span>
              <span> (${question.category})</span>
            </div>
          `).join('')}
        </div>
        ` : ''}

        ${meetingPrep.suggestedCollateral && meetingPrep.suggestedCollateral.length > 0 ? `
        <h2>Suggested Collateral</h2>
        <div>
          ${meetingPrep.suggestedCollateral.map(item => `
            <div class="collateral-item">
              <span class="collateral-icon">📄</span>
              <a href="${item.url}" target="_blank">${item.name}</a> (${item.type})
            </div>
          `).join('')}
        </div>
        ` : ''}

        <h2>Next Steps</h2>
        <div class="next-steps">
          <ul>
            ${meetingPrep.nextSteps.map(step => `<li>${step}</li>`).join('')}
          </ul>
        </div>

        ${meetingPrep.addToAgendaUrl ? `
        <div style="margin-top: 20px; text-align: center;">
          <a href="${meetingPrep.addToAgendaUrl}" target="_blank" style="display: inline-block; padding: 10px 20px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 5px;">Add to Calendar Agenda</a>
        </div>
        ` : ''}

        <div class="footer">
          <p>Generated by Aizako CRM on ${formatDate(new Date())}</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate a URL to add meeting prep to a calendar agenda
   */
  async generateAddToAgendaUrl(
    meetingPrepId: string,
    calendarType: 'google' | 'outlook' = 'google'
  ): Promise<string> {
    try {
      // Get the meeting prep
      const meetingPrep = await this.getMeetingPrepById(meetingPrepId);

      if (!meetingPrep) {
        throw new Error('Meeting prep not found');
      }

      // Get the contact, company, and opportunity
      const [contact, company, opportunity] = await Promise.all([
        meetingPrep.contactId ? Contact.findById(meetingPrep.contactId) : null,
        meetingPrep.companyId ? Company.findById(meetingPrep.companyId) : null,
        meetingPrep.opportunityId ? Opportunity.findById(meetingPrep.opportunityId) : null
      ]);

      // Create a title for the calendar event
      const title = `Meeting with ${contact ? `${contact.firstName} ${contact.lastName}` : company ? company.name : 'Client'}`;

      // Create a description for the calendar event
      const description = `
Meeting Prep: ${meetingPrep.title}

${meetingPrep.summary}

Key Talking Points:
${meetingPrep.talkingPoints.map(point => `- ${point.topic}`).join('\n')}

View full meeting prep: ${process.env.APP_URL || 'https://app.aizako.com'}/documents/${meetingPrep.id}
      `.trim();

      // Create a date for the calendar event (default to tomorrow at 10 AM)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(10, 0, 0, 0);

      // Format the date for the calendar URL
      const startDate = tomorrow.toISOString().replace(/[-:]/g, '').replace(/\.\d{3}/g, '');

      // Set the duration to 1 hour
      const endDate = new Date(tomorrow.getTime() + 60 * 60 * 1000).toISOString().replace(/[-:]/g, '').replace(/\.\d{3}/g, '');

      // Generate the calendar URL
      if (calendarType === 'google') {
        return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&details=${encodeURIComponent(description)}&dates=${startDate}/${endDate}`;
      } else {
        return `https://outlook.office.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(description)}&startdt=${startDate}&enddt=${endDate}`;
      }
    } catch (error) {
      logger.error('Error generating add to agenda URL:', error);
      throw error;
    }
  }

  /**
   * Get a meeting prep by ID
   */
  async getMeetingPrepById(id: string): Promise<MeetingPrep | null> {
    try {
      // For now, we don't store meeting preps in the database
      // In a future implementation, we would retrieve it from MongoDB
      return null;
    } catch (error) {
      logger.error('Error getting meeting prep by ID:', error);
      return null;
    }
  }
}

// Export a singleton instance
export const meetingPrepService = new MeetingPrepService();
