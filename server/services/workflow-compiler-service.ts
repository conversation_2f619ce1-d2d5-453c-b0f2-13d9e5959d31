import { v4 as uuidv4 } from 'uuid';
import * as yaml from 'js-yaml';
import { logger } from '../utils/logger';
import {
  IWorkflowNode,
  IWorkflowEdge,
  WorkflowTriggerType,
  WorkflowActionType,
  WorkflowConditionType
} from '../models/mongoose';

/**
 * Interface for Workflow DSL
 */
interface WorkflowDSL {
  name: string;
  description?: string;
  trigger: {
    type: string;
    config: Record<string, any>;
  };
  nodes: {
    id: string;
    type: string;
    data: {
      type: string;
      config: Record<string, any>;
    };
  }[];
  edges: {
    source: string;
    target: string;
    condition?: Record<string, any>;
  }[];
}

/**
 * Interface for Compiled Workflow
 */
interface CompiledWorkflow {
  dsl_yaml: string;
  nodes: IWorkflowNode[];
  edges: IWorkflowEdge[];
  errors: string[];
  warnings: string[];
}

/**
 * Workflow Compiler Service
 * Compiles DSL YAML into executable workflow graphs
 */
export class WorkflowCompilerService {
  /**
   * Compile DSL YAML into a workflow graph
   */
  compileWorkflow(dslYaml: string): CompiledWorkflow {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Parse YAML
      let dsl: WorkflowDSL;
      try {
        dsl = yaml.load(dslYaml) as WorkflowDSL;
      } catch (yamlError) {
        errors.push(`Invalid YAML: ${yamlError}`);
        return {
          dsl_yaml: dslYaml,
          nodes: [],
          edges: [],
          errors,
          warnings
        };
      }

      // Validate DSL structure
      if (!this.validateDSL(dsl, errors)) {
        return {
          dsl_yaml: dslYaml,
          nodes: [],
          edges: [],
          errors,
          warnings
        };
      }

      // Generate nodes and edges
      const { nodes, edges } = this.generateGraph(dsl, warnings);

      return {
        dsl_yaml: dslYaml,
        nodes,
        edges,
        errors,
        warnings
      };
    } catch (error) {
      logger.error(`Error compiling workflow: ${error}`);
      return {
        dsl_yaml: dslYaml,
        nodes: [],
        edges: [],
        errors: [`Compilation error: ${error}`],
        warnings: []
      };
    }
  }

  /**
   * Validate DSL structure
   */
  private validateDSL(dsl: any, errors: string[]): boolean {
    // Check if dsl is an object
    if (!dsl || typeof dsl !== 'object') {
      errors.push('DSL must be an object');
      return false;
    }

    // Check required fields
    if (!dsl.name) {
      errors.push('Workflow name is required');
    }

    if (!dsl.trigger) {
      errors.push('Workflow trigger is required');
    } else if (!dsl.trigger.type) {
      errors.push('Trigger type is required');
    } else {
      // Validate trigger type
      this.validateTriggerType(dsl.trigger.type, errors);
    }

    if (!dsl.nodes || !Array.isArray(dsl.nodes) || dsl.nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    } else {
      // Validate each node
      dsl.nodes.forEach((node: any, index: number) => {
        if (!node.id) {
          errors.push(`Node at index ${index} is missing an id`);
        }
        if (!node.type) {
          errors.push(`Node at index ${index} is missing a type`);
        } else if (!['action', 'condition'].includes(node.type)) {
          errors.push(`Node at index ${index} has invalid type: ${node.type}`);
        }

        if (!node.data) {
          errors.push(`Node at index ${index} is missing data`);
        } else if (!node.data.type) {
          errors.push(`Node at index ${index} is missing data.type`);
        } else {
          // Validate node data type based on node type
          if (node.type === 'action') {
            this.validateActionType(node.data.type, errors);
          } else if (node.type === 'condition') {
            this.validateConditionType(node.data.type, errors);
          }
        }
      });
    }

    if (!dsl.edges || !Array.isArray(dsl.edges)) {
      errors.push('Workflow must have edges array (can be empty)');
    } else {
      // Validate each edge
      dsl.edges.forEach((edge: any, index: number) => {
        if (!edge.source) {
          errors.push(`Edge at index ${index} is missing a source`);
        }
        if (!edge.target) {
          errors.push(`Edge at index ${index} is missing a target`);
        }
      });
    }

    return errors.length === 0;
  }

  /**
   * Validate trigger type
   */
  private validateTriggerType(type: string, errors: string[]): boolean {
    const validTriggerTypes = Object.values(WorkflowTriggerType);

    if (!validTriggerTypes.includes(type as WorkflowTriggerType)) {
      errors.push(`Invalid trigger type: ${type}. Valid types are: ${validTriggerTypes.join(', ')}`);
      return false;
    }

    return true;
  }

  /**
   * Validate action type
   */
  private validateActionType(type: string, errors: string[]): boolean {
    const validActionTypes = Object.values(WorkflowActionType);

    if (!validActionTypes.includes(type as WorkflowActionType)) {
      errors.push(`Invalid action type: ${type}. Valid types are: ${validActionTypes.join(', ')}`);
      return false;
    }

    return true;
  }

  /**
   * Validate condition type
   */
  private validateConditionType(type: string, errors: string[]): boolean {
    const validConditionTypes = Object.values(WorkflowConditionType);

    if (!validConditionTypes.includes(type as WorkflowConditionType)) {
      errors.push(`Invalid condition type: ${type}. Valid types are: ${validConditionTypes.join(', ')}`);
      return false;
    }

    return true;
  }

  /**
   * Generate workflow graph from DSL
   */
  private generateGraph(dsl: WorkflowDSL, warnings: string[]): { nodes: IWorkflowNode[], edges: IWorkflowEdge[] } {
    const nodes: IWorkflowNode[] = [];
    const edges: IWorkflowEdge[] = [];

    // Create trigger node
    const triggerId = 'trigger';
    nodes.push({
      id: triggerId,
      type: 'trigger',
      position: { x: 100, y: 100 },
      data: {
        type: dsl.trigger.type,
        config: dsl.trigger.config,
        description: `Trigger: ${dsl.trigger.type}`
      }
    });

    // Create nodes from DSL
    const nodeMap = new Map<string, IWorkflowNode>();
    nodeMap.set(triggerId, nodes[0]);

    // Add nodes from DSL
    let yPosition = 200;
    dsl.nodes.forEach((node, index) => {
      const nodeId = node.id || `node_${index}`;
      const nodeType = node.type as 'action' | 'condition';

      const workflowNode: IWorkflowNode = {
        id: nodeId,
        type: nodeType,
        position: { x: 100, y: yPosition },
        data: {
          type: node.data.type,
          config: node.data.config,
          description: `${nodeType}: ${node.data.type}`
        }
      };

      nodes.push(workflowNode);
      nodeMap.set(nodeId, workflowNode);
      yPosition += 100;
    });

    // Create edges
    dsl.edges.forEach((edge, index) => {
      const edgeId = `edge_${index}`;

      // Validate source and target nodes exist
      if (!nodeMap.has(edge.source)) {
        warnings.push(`Edge source '${edge.source}' does not exist`);
        return;
      }

      if (!nodeMap.has(edge.target)) {
        warnings.push(`Edge target '${edge.target}' does not exist`);
        return;
      }

      const workflowEdge: IWorkflowEdge = {
        id: edgeId,
        source: edge.source,
        target: edge.target,
        label: edge.condition ? 'if' : undefined,
        condition: edge.condition
      };

      edges.push(workflowEdge);
    });

    // If no edges from trigger, add default edge to first node
    if (!edges.some(e => e.source === triggerId) && nodes.length > 1) {
      const firstNodeId = nodes[1].id;
      edges.push({
        id: `edge_trigger_default`,
        source: triggerId,
        target: firstNodeId
      });
    }

    return { nodes, edges };
  }

  /**
   * Update graph positions for better visualization
   */
  updateGraphPositions(nodes: IWorkflowNode[], edges: IWorkflowEdge[]): IWorkflowNode[] {
    // Create a map of node dependencies
    const dependencyMap = new Map<string, string[]>();

    // Initialize with empty arrays
    nodes.forEach(node => {
      dependencyMap.set(node.id, []);
    });

    // Fill in dependencies based on edges
    edges.forEach(edge => {
      const dependencies = dependencyMap.get(edge.target) || [];
      dependencies.push(edge.source);
      dependencyMap.set(edge.target, dependencies);
    });

    // Assign levels based on dependencies
    const nodeLevels = new Map<string, number>();
    const assignLevel = (nodeId: string, level: number) => {
      const currentLevel = nodeLevels.get(nodeId) || 0;
      nodeLevels.set(nodeId, Math.max(currentLevel, level));

      // Find all nodes that depend on this node
      edges.filter(e => e.source === nodeId).forEach(edge => {
        assignLevel(edge.target, level + 1);
      });
    };

    // Start with trigger node at level 0
    const triggerNode = nodes.find(n => n.type === 'trigger');
    if (triggerNode) {
      assignLevel(triggerNode.id, 0);
    }

    // Update node positions based on levels
    const updatedNodes = nodes.map(node => {
      const level = nodeLevels.get(node.id) || 0;
      return {
        ...node,
        position: {
          x: 100 + (level * 200),
          y: node.position.y
        }
      };
    });

    return updatedNodes;
  }
}

// Export a singleton instance
export const workflowCompilerService = new WorkflowCompilerService();
