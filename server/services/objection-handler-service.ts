import mongoose from 'mongoose';
import axios from 'axios';
import {
  Objection,
  ObjectionResponse,
  Contact,
  Company,
  Opportunity,
  Activity
} from '../models/mongoose';
import { graphRAGService } from './graph-rag-service';
import { logger } from '../utils/logger';
import { toObjectId } from '../utils/mongodb-utils';
import {
  isError,
  isMongoDocument,
  isObjectionDocument,
  isContactDocument,
  isCompanyDocument,
  isOpportunityDocument,
  isActivityDocument,
  getDocumentId
} from '../utils/type-guards';
import {
  ObjectionHandlerContext
} from '../../shared/types/graph-rag';
import {
  ObjectionDocument,
  ObjectionResponseDocument,
  ContactDocument,
  CompanyDocument,
  OpportunityDocument,
  ActivityDocument
} from '../@types/mongoose-types';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

/**
 * Service for handling sales objections
 */
export class ObjectionHandlerService {
  /**
   * Get all objection categories
   */
  async getObjectionCategories(): Promise<string[]> {
    return [
      'price',
      'product',
      'competition',
      'timing',
      'authority',
      'need',
      'trust',
      'other'
    ];
  }

  /**
   * Get all objections
   */
  async getAllObjections(
    filter: {
      category?: string;
      isCommon?: boolean;
      createdBy?: string;
      search?: string;
    } = {}
  ): Promise<ObjectionDocument[]> {
    try {
      const query: any = {};

      if (filter.category) {
        query.category = filter.category;
      }

      if (filter.isCommon !== undefined) {
        query.isCommon = filter.isCommon;
      }

      if (filter.createdBy) {
        query.createdBy = toObjectId(filter.createdBy);
      }

      if (filter.search) {
        query.$text = { $search: filter.search };
      }

      const objections = await Objection.find(query).sort({ name: 1 });
      return objections as ObjectionDocument[];
    } catch (error) {
      console.error('Error getting objections:', error);
      throw error;
    }
  }

  /**
   * Get objection by ID
   */
  async getObjectionById(id: string): Promise<ObjectionDocument | null> {
    try {
      const objection = await Objection.findById(id);
      return objection as ObjectionDocument | null;
    } catch (error) {
      console.error(`Error getting objection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new objection
   */
  async createObjection(
    data: {
      name: string;
      category: string;
      description: string;
      tags?: string[];
      isCommon?: boolean;
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<ObjectionDocument> {
    try {
      const objection = new Objection({
        ...data,
        createdBy: toObjectId(userId)
      });

      const savedObjection = await objection.save();
      return savedObjection as ObjectionDocument;
    } catch (error) {
      console.error('Error creating objection:', error);
      throw error;
    }
  }

  /**
   * Update an objection
   */
  async updateObjection(
    id: string,
    data: {
      name?: string;
      category?: string;
      description?: string;
      tags?: string[];
      isCommon?: boolean;
      customFields?: Record<string, any>;
    }
  ): Promise<ObjectionDocument | null> {
    try {
      const updatedObjection = await Objection.findByIdAndUpdate(id, data, { new: true });
      return updatedObjection as ObjectionDocument | null;
    } catch (error) {
      console.error(`Error updating objection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an objection
   */
  async deleteObjection(id: string): Promise<boolean> {
    try {
      const result = await Objection.findByIdAndDelete(id);

      if (result) {
        // Also delete all responses for this objection
        await ObjectionResponse.deleteMany({ objectionId: id });
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error deleting objection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get responses for an objection
   */
  async getResponsesForObjection(
    objectionId: string,
    filter: {
      effectiveness?: number;
      isAIGenerated?: boolean;
      createdBy?: string;
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
    } = {}
  ): Promise<ObjectionResponseDocument[]> {
    try {
      const query: any = { objectionId: toObjectId(objectionId) };

      if (filter.effectiveness) {
        query.effectiveness = { $gte: filter.effectiveness };
      }

      if (filter.isAIGenerated !== undefined) {
        query.isAIGenerated = filter.isAIGenerated;
      }

      if (filter.createdBy) {
        query.createdBy = toObjectId(filter.createdBy);
      }

      if (filter.opportunityId) {
        query.opportunityId = toObjectId(filter.opportunityId);
      }

      if (filter.contactId) {
        query.contactId = toObjectId(filter.contactId);
      }

      if (filter.companyId) {
        query.companyId = toObjectId(filter.companyId);
      }

      const responses = await ObjectionResponse.find(query)
        .sort({ effectiveness: -1, usedCount: -1 });

      return responses as ObjectionResponseDocument[];
    } catch (error) {
      console.error(`Error getting responses for objection with ID ${objectionId}:`, error);
      throw error;
    }
  }

  /**
   * Create a response for an objection
   */
  async createResponse(
    data: {
      objectionId: string;
      response: string;
      context: string;
      effectiveness?: number;
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      isAIGenerated?: boolean;
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<ObjectionResponseDocument> {
    try {
      const response = new ObjectionResponse({
        ...data,
        objectionId: toObjectId(data.objectionId),
        opportunityId: data.opportunityId ? toObjectId(data.opportunityId) : undefined,
        contactId: data.contactId ? toObjectId(data.contactId) : undefined,
        companyId: data.companyId ? toObjectId(data.companyId) : undefined,
        createdBy: toObjectId(userId)
      });

      const savedResponse = await response.save();
      return savedResponse as ObjectionResponseDocument;
    } catch (error) {
      console.error('Error creating objection response:', error);
      throw error;
    }
  }

  /**
   * Update a response
   */
  async updateResponse(
    id: string,
    data: {
      response?: string;
      context?: string;
      effectiveness?: number;
      usedCount?: number;
      successCount?: number;
      customFields?: Record<string, any>;
    }
  ): Promise<ObjectionResponseDocument | null> {
    try {
      const updatedResponse = await ObjectionResponse.findByIdAndUpdate(id, data, { new: true });
      return updatedResponse as ObjectionResponseDocument | null;
    } catch (error) {
      console.error(`Error updating objection response with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a response
   */
  async deleteResponse(id: string): Promise<boolean> {
    try {
      const result = await ObjectionResponse.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      console.error(`Error deleting objection response with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Record response usage
   */
  async recordResponseUsage(
    id: string,
    wasSuccessful: boolean = false
  ): Promise<ObjectionResponseDocument | null> {
    try {
      const update: any = { $inc: { usedCount: 1 } };

      if (wasSuccessful) {
        update.$inc.successCount = 1;
      }

      const updatedResponse = await ObjectionResponse.findByIdAndUpdate(id, update, { new: true });
      return updatedResponse as ObjectionResponseDocument | null;
    } catch (error) {
      console.error(`Error recording usage for objection response with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Generate AI response for an objection
   */
  async generateAIResponse(
    params: {
      objectionId: string;
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      objectionText?: string; // Optional direct objection text for real-time handling
    },
    userId: string
  ): Promise<ObjectionResponseDocument> {
    try {
      const { objectionId, opportunityId, contactId, companyId, objectionText } = params;

      // Get the tenant ID from the user
      const user = await mongoose.model('User').findById(userId);
      if (!user) {
        throw new Error('User not found');
      }
      const tenantId = user.tenantId?.toString() || 'default';

      // Get the objection
      const objection = await this.getObjectionById(objectionId);

      if (!objection) {
        throw new Error(`Objection with ID ${objectionId} not found`);
      }

      // Get related data
      const [contactResult, companyResult, opportunityResult] = await Promise.all([
        contactId ? Contact.findById(contactId) : null,
        companyId ? Company.findById(companyId) : null,
        opportunityId ? Opportunity.findById(opportunityId) : null
      ]);

      // Cast to proper document types
      const contact = contactResult as ContactDocument | null;
      const company = companyResult as CompanyDocument | null;
      const opportunity = opportunityResult as OpportunityDocument | null;

      // Get activities if we have an opportunity
      const activityResults = opportunity && isOpportunityDocument(opportunity)
        ? await Activity.find({ opportunityId: opportunity._id })
            .sort({ date: -1 })
            .limit(10)
        : [];

      // Cast activities to proper document type
      const activities = activityResults as ActivityDocument[];

      // Get GraphRAG context for objection handling
      logger.info('Getting GraphRAG context for objection handling');
      const graphRAGContext = await graphRAGService.getObjectionHandlerContext({
        objectionText: objectionText || objection.description,
        opportunityId,
        contactId,
        companyId,
        tenantId
      });

      // Generate the response using AI with GraphRAG context
      const aiResponse = await this.generateResponseWithAI(
        objection,
        contact,
        company,
        opportunity,
        activities,
        graphRAGContext
      );

      // Create and save the response
      const response = await this.createResponse(
        {
          objectionId: objectionId,
          response: aiResponse.response,
          context: aiResponse.context,
          effectiveness: 3, // Default middle rating
          opportunityId: opportunityId,
          contactId: contactId,
          companyId: companyId,
          isAIGenerated: true
        },
        userId
      );

      // Record wizard usage for analytics
      const entityId = opportunityId || companyId || contactId;
      if (entityId) {
        await graphRAGService.recordWizardUsage({
          wizardType: 'objection_handler',
          userId,
          tenantId,
          entityId,
          entityType: opportunityId ? 'opportunity' : companyId ? 'company' : 'contact',
          result: 'success',
          metadata: {
            objectionId,
            responseId: isMongoDocument(response) ? getDocumentId(response) : ''
          }
        });
      }

      return response;
    } catch (error) {
      logger.error('Error generating AI response:', error);

      // Record wizard usage failure
      try {
        const entityId = params.opportunityId || params.companyId || params.contactId;
        if (entityId) {
          const user = await mongoose.model('User').findById(userId);
          const tenantId = user?.tenantId?.toString() || 'default';

          await graphRAGService.recordWizardUsage({
            wizardType: 'objection_handler',
            userId,
            tenantId,
            entityId,
            entityType: params.opportunityId ? 'opportunity' : params.companyId ? 'company' : 'contact',
            result: 'failure',
            metadata: {
              objectionId: params.objectionId,
              error: isError(error) ? error.message : 'Unknown error'
            }
          });
        }
      } catch (recordError) {
        logger.error('Error recording wizard usage failure:', recordError);
      }

      throw error;
    }
  }

  /**
   * Generate a response using AI with GraphRAG context
   */
  private async generateResponseWithAI(
    objection: ObjectionDocument,
    contact: ContactDocument | null,
    company: CompanyDocument | null,
    opportunity: OpportunityDocument | null,
    activities: ActivityDocument[],
    graphRAGContext?: ObjectionHandlerContext
  ): Promise<{ response: string; context: string }> {
    try {
      // Prepare data for AI
      const data = {
        objection: {
          name: objection.name,
          category: objection.category,
          description: objection.description,
          tags: objection.tags
        },
        contact: contact && isContactDocument(contact) ? {
          id: getDocumentId(contact),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title,
          status: contact.status,
          notes: contact.notes,
          tags: contact.tags
        } : null,
        company: company && isCompanyDocument(company) ? {
          id: getDocumentId(company),
          name: company.name,
          domain: company.domain,
          industry: company.industry,
          size: company.size,
          location: company.location,
          description: company.description,
          website: company.website,
          status: company.status,
          tags: company.tags,
          notes: company.notes
        } : null,
        opportunity: opportunity && isOpportunityDocument(opportunity) ? {
          id: getDocumentId(opportunity),
          name: opportunity.name,
          value: opportunity.value,
          currency: opportunity.currency,
          stage: opportunity.stage,
          probability: opportunity.probability,
          expectedCloseDate: opportunity.expectedCloseDate,
          description: opportunity.description,
          tags: opportunity.tags,
          notes: opportunity.notes
        } : null,
        activities: activities.map(activity => {
          if (isActivityDocument(activity)) {
            return {
              id: getDocumentId(activity),
              type: activity.type,
              title: activity.title,
              description: activity.description,
              date: activity.date,
              completed: activity.completed,
              outcome: activity.outcome
            };
          }
          return {
            id: 'unknown',
            type: 'unknown',
            title: 'Unknown Activity',
            description: '',
            date: new Date(),
            completed: false,
            outcome: ''
          };
        }),
        // Include GraphRAG context if available
        graphRAG: graphRAGContext ? {
          objectionClass: graphRAGContext.objectionClass,
          similarObjections: graphRAGContext.similarObjections || [],
          effectiveRebuttals: graphRAGContext.effectiveRebuttals || [],
          supportingEvidence: graphRAGContext.supportingEvidence || []
        } : null
      };

      // Create prompt for AI
      const prompt = `
        Generate a persuasive and effective response to the following sales objection.

        Objection: ${objection.name}
        Category: ${objection.category}
        Description: ${objection.description}

        ${contact ? `Contact: ${contact.firstName} ${contact.lastName}, ${contact.title || 'No title'}` : ''}
        ${company ? `Company: ${company.name}, ${company.industry || 'Unknown industry'}` : ''}
        ${opportunity ? `Opportunity: ${opportunity.name}, Stage: ${opportunity.stage}, Value: ${opportunity.currency}${opportunity.value}` : ''}

        ${graphRAGContext ? `
        Pay special attention to the GraphRAG context which contains:
        - Objection classification: ${graphRAGContext.objectionClass}
        - Similar objections that have been successfully handled
        - Effective rebuttals ranked by win rate
        - Supporting evidence (case studies, ROI calculations)

        Use the effective rebuttals and supporting evidence from the GraphRAG context to craft a highly persuasive response.
        ` : ''}

        Provide a response that:
        1. Acknowledges the objection without repeating it negatively
        2. Empathizes with the concern
        3. Provides a clear, value-focused response
        4. Includes specific details from the context when relevant
        5. Cites supporting evidence when available
        6. Ends with a question or next step

        Format your response as a JSON object with these fields:
        {
          "response": "The full response to the objection",
          "context": "A brief explanation of the strategy used in this response",
          "supportingEvidence": ["List of supporting evidence used in the response"]
        }
      `;

      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/crew`, {
          task: prompt,
          userId: 1, // System user
          context: data
        }, {
          timeout: 30000 // 30 second timeout
        });

        if (aiResponse.data && aiResponse.data.response) {
          try {
            // Try to parse the response as JSON
            const responseData = JSON.parse(aiResponse.data.response);
            return {
              response: responseData.response,
              context: responseData.context
            };
          } catch (e) {
            console.error('Error parsing AI service response:', e);
          }
        }
      } catch (aiError) {
        console.error('Error using AI service, falling back to Voyage AI:', aiError);
      }

      // Fallback to Voyage AI if the AI service fails
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales coach for Aizako CRM.
              Your job is to help sales representatives handle objections effectively.
              Focus on providing persuasive, value-focused responses that address the specific objection.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.7,
          max_tokens: 1000,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      if (response.data && response.data.choices && response.data.choices[0].message.content) {
        const responseData = JSON.parse(response.data.choices[0].message.content);
        return {
          response: responseData.response,
          context: responseData.context
        };
      }

      throw new Error('Failed to generate response with AI');
    } catch (error) {
      console.error('Error generating response with AI:', error);
      throw error;
    }
  }

  /**
   * Classify an objection using AI and GraphRAG
   */
  async classifyObjection(
    objectionText: string,
    params?: {
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      userId?: string;
    }
  ): Promise<{
    category: string;
    confidence: number;
    suggestedObjections: Array<{
      id: string;
      name: string;
      similarity: number;
    }>;
    graphRAGContext?: any;
  }> {
    try {
      // Get GraphRAG context if we have user and entity IDs
      let graphRAGContext;
      if (params && params.userId) {
        try {
          // Get the tenant ID from the user
          const user = await mongoose.model('User').findById(params.userId);
          if (user) {
            const tenantId = user.tenantId?.toString() || 'default';

            // Get GraphRAG context
            logger.info('Getting GraphRAG context for objection classification');
            graphRAGContext = await graphRAGService.getObjectionHandlerContext({
              objectionText,
              opportunityId: params.opportunityId,
              contactId: params.contactId,
              companyId: params.companyId,
              tenantId
            });

            // If we have GraphRAG context with objection class, use it
            if (graphRAGContext && graphRAGContext.objectionClass) {
              return {
                category: graphRAGContext.objectionClass,
                confidence: 0.9, // High confidence since it's from GraphRAG
                suggestedObjections: graphRAGContext.similarObjections.map((obj: any) => ({
                  id: obj.id,
                  name: obj.text,
                  similarity: obj.successRate || 0.8
                })),
                graphRAGContext
              };
            }
          }
        } catch (graphError) {
          logger.error('Error getting GraphRAG context:', graphError);
          // Continue with AI classification if GraphRAG fails
        }
      }

      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/crew`, {
          task: `Classify the following sales objection into one of these categories: price, product, competition, timing, authority, need, trust, other.
                Also find similar objections from our database.

                Objection: ${objectionText}

                Return the result as a JSON object with these fields:
                {
                  "category": "The most appropriate category",
                  "confidence": A number between 0 and 1 representing confidence in the classification,
                  "suggestedObjections": []
                }`,
          userId: 1, // System user
          context: {}
        }, {
          timeout: 15000 // 15 second timeout
        });

        if (aiResponse.data && aiResponse.data.response) {
          try {
            // Try to parse the response as JSON
            const classificationData = JSON.parse(aiResponse.data.response);

            // Get suggested objections from the database
            const suggestedObjections = await this.getSimilarObjections(objectionText);

            return {
              category: classificationData.category,
              confidence: classificationData.confidence,
              suggestedObjections,
              graphRAGContext
            };
          } catch (e) {
            logger.error('Error parsing AI service response:', e);
          }
        }
      } catch (aiError) {
        logger.error('Error using AI service, falling back to Voyage AI:', aiError);
      }

      // Fallback to Voyage AI if the AI service fails
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales coach for Aizako CRM.
              Your job is to classify sales objections into appropriate categories.
              Categories: price, product, competition, timing, authority, need, trust, other.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `Classify the following sales objection:

              Objection: ${objectionText}

              Return the result as a JSON object with these fields:
              {
                "category": "The most appropriate category",
                "confidence": A number between 0 and 1 representing confidence in the classification
              }`
            }
          ],
          temperature: 0.3,
          max_tokens: 500,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      if (response.data && response.data.choices && response.data.choices[0].message.content) {
        const classificationData = JSON.parse(response.data.choices[0].message.content);

        // Get suggested objections from the database
        const suggestedObjections = await this.getSimilarObjections(objectionText);

        return {
          category: classificationData.category,
          confidence: classificationData.confidence,
          suggestedObjections,
          graphRAGContext
        };
      }

      // Default fallback
      return {
        category: 'other',
        confidence: 0.5,
        suggestedObjections: await this.getSimilarObjections(objectionText),
        graphRAGContext: graphRAGContext || {
          objectionClass: 'other',
          similarObjections: [],
          effectiveRebuttals: [],
          supportingEvidence: []
        }
      };
    } catch (error) {
      logger.error('Error classifying objection:', error);

      // Default fallback
      return {
        category: 'other',
        confidence: 0.5,
        suggestedObjections: [],
        graphRAGContext: {
          objectionClass: 'other',
          similarObjections: [],
          effectiveRebuttals: [],
          supportingEvidence: []
        }
      };
    }
  }

  /**
   * Get similar objections from the database
   */
  private async getSimilarObjections(
    objectionText: string
  ): Promise<Array<{
    id: string;
    name: string;
    similarity: number;
  }>> {
    try {
      // Use MongoDB text search to find similar objections
      const similarObjections = await Objection.find(
        { $text: { $search: objectionText } },
        { score: { $meta: "textScore" } }
      )
        .sort({ score: { $meta: "textScore" } })
        .limit(5);

      return similarObjections.map(objection => {
        if (isObjectionDocument(objection)) {
          return {
            id: getDocumentId(objection),
            name: objection.name,
            similarity: 0.8 // This is a placeholder; in a real implementation, we would calculate actual similarity
          };
        }
        return {
          id: 'unknown',
          name: 'Unknown Objection',
          similarity: 0.5
        };
      });
    } catch (error) {
      console.error('Error finding similar objections:', error);
      return [];
    }
  }

  /**
   * Handle a real-time objection
   */
  async handleRealTimeObjection(
    params: {
      objectionText: string;
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
    },
    userId: string
  ): Promise<{
    objectionClass: string;
    response: string;
    rebuttals: Array<{
      id: string;
      text: string;
      winRate: number;
      evidence: string[];
    }>;
    supportingEvidence: Array<{
      id: string;
      title: string;
      type: string;
      description: string;
      url: string;
    }>;
  }> {
    try {
      // Get the tenant ID from the user
      const user = await mongoose.model('User').findById(userId);
      if (!user) {
        throw new Error('User not found');
      }
      const tenantId = user.tenantId?.toString() || 'default';

      // Get GraphRAG context for objection handling
      logger.info('Getting GraphRAG context for real-time objection handling');
      const graphRAGContext = await graphRAGService.getObjectionHandlerContext({
        objectionText: params.objectionText,
        opportunityId: params.opportunityId,
        contactId: params.contactId,
        companyId: params.companyId,
        tenantId
      });

      // Classify the objection
      const classification = await this.classifyObjection(
        params.objectionText,
        {
          opportunityId: params.opportunityId,
          contactId: params.contactId,
          companyId: params.companyId,
          userId
        }
      );

      // Find or create an objection in the database
      let objection = await Objection.findOne({
        category: classification.category,
        $text: { $search: params.objectionText }
      });

      // Create a new objection if not found
      if (!objection) {
        const objectionData = {
          name: params.objectionText.length > 100
            ? params.objectionText.substring(0, 97) + '...'
            : params.objectionText,
          category: classification.category,
          description: params.objectionText,
          isCommon: false
        };

        objection = await this.createObjection(objectionData, userId);

        // Verify that the objection was created successfully
        if (!objection) {
          throw new Error('Failed to create objection');
        }
      }

      // Ensure objection is a valid MongoDB document
      if (!isObjectionDocument(objection)) {
        throw new Error('Invalid objection document');
      }

      // Generate a response
      const response = await this.generateAIResponse(
        {
          objectionId: getDocumentId(objection),
          opportunityId: params.opportunityId,
          contactId: params.contactId,
          companyId: params.companyId,
          objectionText: params.objectionText
        },
        userId
      );

      // Record wizard usage for analytics
      const entityId = params.opportunityId || params.companyId || params.contactId;
      if (entityId) {
        await graphRAGService.recordWizardUsage({
          wizardType: 'objection_handler',
          userId,
          tenantId,
          entityId,
          entityType: params.opportunityId ? 'opportunity' : params.companyId ? 'company' : 'contact',
          result: 'success',
          metadata: {
            objectionId: getDocumentId(objection),
            responseId: isMongoDocument(response) ? getDocumentId(response) : '',
            objectionText: params.objectionText,
            objectionClass: classification.category
          }
        });
      }

      // Map supporting evidence to ensure URL is always a string
      const supportingEvidenceWithUrl = (graphRAGContext?.supportingEvidence || []).map(evidence => ({
        id: evidence.id,
        title: evidence.title,
        type: evidence.type,
        description: evidence.description,
        url: evidence.url || ''
      }));

      return {
        objectionClass: classification.category,
        response: response.response,
        rebuttals: graphRAGContext?.effectiveRebuttals || [],
        supportingEvidence: supportingEvidenceWithUrl
      };
    } catch (error) {
      logger.error('Error handling real-time objection:', error);

      // Record wizard usage failure
      try {
        const entityId = params.opportunityId || params.companyId || params.contactId;
        if (entityId) {
          // Get the tenant ID from the user
          const user = await mongoose.model('User').findById(userId);
          const failureTenantId = user?.tenantId?.toString() || 'default';

          await graphRAGService.recordWizardUsage({
            wizardType: 'objection_handler',
            userId,
            tenantId: failureTenantId,
            entityId,
            entityType: params.opportunityId ? 'opportunity' : params.companyId ? 'company' : 'contact',
            result: 'failure',
            metadata: {
              objectionText: params.objectionText,
              error: isError(error) ? error.message : 'Unknown error'
            }
          });
        }
      } catch (recordError) {
        logger.error('Error recording wizard usage failure:', recordError);
      }

      throw error;
    }
  }
}

// Export a singleton instance
export const objectionHandlerService = new ObjectionHandlerService();
