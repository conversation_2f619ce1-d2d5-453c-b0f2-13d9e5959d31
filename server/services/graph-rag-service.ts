import mongoose from 'mongoose';
import { neo4jService } from './neo4j-service';
import { VectorEmbeddingService } from './vector-embedding-service';
import { analyticsEventBus } from './analytics-event-bus';
import { EventType } from '../../shared/types/analytics-events';
import {
  GraphRAGContextParams,
  MeetingPrepContextParams,
  ObjectionHandlerContextParams,
  ProposalGeneratorContextParams,
  MeetingPrepContext,
  ObjectionHandlerContext,
  ProposalGeneratorContext,
  WizardUsageParams
} from '../../shared/types/graph-rag';
import {
  isGraphRAGContextParams,
  isMeetingPrepContextParams,
  isObjectionHandlerContextParams,
  isProposalGeneratorContextParams,
  isWizardUsageParams
} from '../../shared/types/guards/graph-rag';
import {
  Contact,
  Company,
  Opportunity,
  Activity,
  Document as DocumentModel,
  IContact,
  ICompany,
  IOpportunity,
  IActivity,
  IDocument
} from '../models/mongoose';
import { logger } from '../utils/logger';

/**
 * GraphRAG Insight Engine Service
 *
 * This service combines Neo4j graph data with MongoDB vector search
 * to provide rich context for AI-powered sales wizards.
 */
export class GraphRAGService {
  private vectorService: VectorEmbeddingService;

  constructor() {
    this.vectorService = new VectorEmbeddingService();
  }

  /**
   * Initialize the GraphRAG service
   */
  async initialize(): Promise<void> {
    try {
      // Initialize Neo4j service
      await neo4jService.initialize();

      // Initialize vector service
      await this.vectorService.initialize();

      logger.info('GraphRAG service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize GraphRAG service:', error);
      throw error;
    }
  }

  /**
   * Get context for a meeting preparation
   */
  async getMeetingPrepContext(params: MeetingPrepContextParams): Promise<MeetingPrepContext> {
    try {
      const { contactId, companyId, opportunityId, tenantId } = params;

      // Get relationship history from Neo4j
      const relationshipHistory = await this.getRelationshipHistory({
        contactId,
        companyId,
        opportunityId,
        tenantId
      });

      // Get stakeholders from Neo4j
      const stakeholders = companyId
        ? await this.getCompanyStakeholders(companyId, tenantId)
        : [];

      // Get buying signals from vector search
      const buyingSignals = await this.getBuyingSignals({
        contactId,
        companyId,
        opportunityId
      });

      // Get predicted questions using vector similarity
      const predictedQuestions = await this.getPredictedQuestions({
        contactId,
        companyId,
        opportunityId
      });

      // Get suggested collateral using vector similarity
      const suggestedCollateral = await this.getSuggestedCollateral({
        contactId,
        companyId,
        opportunityId
      });

      // Get company news if we have a company
      const companyNews = companyId
        ? await this.getCompanyNews(companyId)
        : undefined;

      return {
        relationshipHistory,
        stakeholders,
        buyingSignals,
        predictedQuestions,
        suggestedCollateral,
        companyNews
      };
    } catch (error) {
      logger.error('Error getting meeting prep context:', error);
      throw error;
    }
  }

  /**
   * Get context for objection handling
   */
  async getObjectionHandlerContext(params: ObjectionHandlerContextParams): Promise<ObjectionHandlerContext> {
    try {
      const { objectionText, opportunityId, contactId, companyId, tenantId } = params;

      // Classify the objection using vector similarity
      const objectionClass = await this.classifyObjection(objectionText);

      // Find similar objections that have been successfully handled
      const similarObjections = await this.findSimilarObjections(objectionText);

      // Get effective rebuttals ranked by win rate
      const effectiveRebuttals = await this.getEffectiveRebuttals({
        objectionClass,
        objectionText,
        opportunityId,
        contactId,
        companyId,
        tenantId
      });

      // Get supporting evidence (case studies, ROI calculations)
      const supportingEvidence = await this.getSupportingEvidence({
        objectionClass,
        opportunityId,
        contactId,
        companyId
      });

      return {
        objectionClass,
        similarObjections,
        effectiveRebuttals,
        supportingEvidence
      };
    } catch (error) {
      logger.error('Error getting objection handler context:', error);
      throw error;
    }
  }

  /**
   * Get context for proposal generation
   */
  async getProposalGeneratorContext(params: ProposalGeneratorContextParams): Promise<ProposalGeneratorContext> {
    try {
      const { opportunityId, contactId, companyId, tenantId } = params;

      // Get pricing tiers and discount policy
      const pricingTiers = await this.getPricingTiers(tenantId);
      const discountPolicy = await this.getDiscountPolicy(tenantId);

      // Get legal boilerplate
      const legalBoilerplate = await this.getLegalBoilerplate(tenantId);

      // Get suggested upsells based on similar closed-won deals
      const suggestedUpsells = await this.getSuggestedUpsells({
        opportunityId,
        contactId,
        companyId,
        tenantId
      });

      // Get industry-specific clauses
      const industrySpecificClauses = companyId
        ? await this.getIndustrySpecificClauses(companyId, tenantId)
        : [];

      // Get similar deals that were closed-won
      const similarDeals = await this.getSimilarDeals({
        opportunityId,
        contactId,
        companyId,
        tenantId
      });

      return {
        pricingTiers,
        discountPolicy,
        legalBoilerplate,
        suggestedUpsells,
        industrySpecificClauses,
        similarDeals
      };
    } catch (error) {
      logger.error('Error getting proposal generator context:', error);
      throw error;
    }
  }

  /**
   * Record wizard usage event for analytics
   */
  async recordWizardUsage(params: WizardUsageParams): Promise<void> {
    try {
      // Map wizard type to event type
      const eventTypeMap = {
        meeting_prep: EventType.MEETING_PREP_GENERATED,
        objection_handler: EventType.OBJECTION_RESPONSE_GENERATED,
        proposal_generator: EventType.PROPOSAL_GENERATED,
        follow_up_coach: EventType.WIZARD_USED,
        win_loss_analyzer: EventType.WIZARD_USED
      };

      const eventType = eventTypeMap[params.wizardType] || EventType.WIZARD_USED;

      // Publish event to analytics event bus
      await analyticsEventBus.publish({
        type: eventType,
        tenantId: params.tenantId,
        userId: params.userId,
        entityId: params.entityId,
        entityType: params.entityType,
        metadata: {
          ...params.metadata,
          wizardType: params.wizardType,
          result: params.result
        }
      });

      logger.info('Wizard usage recorded:', params);
    } catch (error) {
      logger.error('Error recording wizard usage:', error);
    }
  }

  /**
   * Get relationship history from Neo4j
   */
  private async getRelationshipHistory(params: {
    contactId?: string;
    companyId?: string;
    opportunityId?: string;
    tenantId: string;
  }): Promise<any[]> {
    try {
      const { contactId, companyId, opportunityId, tenantId } = params;

      // Build the Cypher query based on available IDs
      let query = '';
      const queryParams: any = { tenantId };

      if (contactId) {
        query = `
          MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
          MATCH (c)-[r]-(n)
          WHERE n.tenantId = $tenantId
          RETURN c, r, n
          ORDER BY r.timestamp DESC
          LIMIT 50
        `;
        queryParams.contactId = contactId;
      } else if (companyId) {
        query = `
          MATCH (c:Company {id: $companyId, tenantId: $tenantId})
          MATCH (c)-[r]-(n)
          WHERE n.tenantId = $tenantId
          RETURN c, r, n
          ORDER BY r.timestamp DESC
          LIMIT 50
        `;
        queryParams.companyId = companyId;
      } else if (opportunityId) {
        query = `
          MATCH (o:Opportunity {id: $opportunityId, tenantId: $tenantId})
          MATCH (o)-[r]-(n)
          WHERE n.tenantId = $tenantId
          RETURN o, r, n
          ORDER BY r.timestamp DESC
          LIMIT 50
        `;
        queryParams.opportunityId = opportunityId;
      } else {
        return [];
      }

      // Execute the query
      const result = await neo4jService.runQuery(query, queryParams);

      // Transform the result into a timeline format
      return result.map((record: any) => {
        const relationship = record.get('r');
        const node = record.get('n');

        return {
          timestamp: relationship.properties.timestamp || new Date().toISOString(),
          type: relationship.type,
          nodeType: node.labels[0],
          nodeId: node.properties.id,
          nodeName: node.properties.name || `${node.properties.firstName || ''} ${node.properties.lastName || ''}`.trim(),
          properties: relationship.properties
        };
      });
    } catch (error) {
      logger.error('Error getting relationship history:', error);
      return [];
    }
  }

  /**
   * Get company stakeholders from Neo4j
   */
  private async getCompanyStakeholders(companyId: string, tenantId: string): Promise<any[]> {
    try {
      const query = `
        MATCH (c:Company {id: $companyId, tenantId: $tenantId})-[r:HAS_CONTACT]-(contact:Contact)
        WHERE contact.tenantId = $tenantId
        RETURN contact
        ORDER BY contact.importance DESC
      `;

      const result = await neo4jService.runQuery(query, { companyId, tenantId });

      // Transform the result into a stakeholder list
      return result.map((record: any) => {
        const contact = record.get('contact');

        return {
          id: contact.properties.id,
          name: `${contact.properties.firstName || ''} ${contact.properties.lastName || ''}`.trim(),
          title: contact.properties.title,
          email: contact.properties.email,
          phone: contact.properties.phone,
          importance: contact.properties.importance || 'medium'
        };
      });
    } catch (error) {
      logger.error('Error getting company stakeholders:', error);
      return [];
    }
  }

  /**
   * Get buying signals using vector search
   */
  private async getBuyingSignals(params: {
    contactId?: string;
    companyId?: string;
    opportunityId?: string;
  }): Promise<any[]> {
    try {
      const { contactId, companyId, opportunityId } = params;

      // Define buying signal keywords
      const buyingSignalQuery = "interested pricing demo budget approval decision timeline purchase";

      // Use opportunityId if available, otherwise use contactId or companyId
      const id = opportunityId || contactId || companyId;

      if (!id) {
        return [];
      }

      // Use vector search to find similar content
      const similarContent = await this.vectorService.findSimilarContent(
        id,
        buyingSignalQuery,
        10
      );

      // Filter and transform the results
      return similarContent
        .filter(item => item.similarity > 0.6)
        .map(item => ({
          id: item.id,
          content: item.content,
          type: item.metadata.type,
          date: item.metadata.type === 'activity' ? item.metadata.date : item.metadata.createdAt,
          source: item.metadata.type === 'activity' ? item.metadata.activityType : item.metadata.name
        }));
    } catch (error) {
      logger.error('Error getting buying signals:', error);
      return [];
    }
  }

  /**
   * Get predicted questions using vector similarity
   */
  private async getPredictedQuestions(params: {
    contactId?: string;
    companyId?: string;
    opportunityId?: string;
  }): Promise<any[]> {
    try {
      // For now, return a static list of questions
      // In a future implementation, this would use vector similarity to find common questions
      return [
        {
          question: "What makes your solution different from competitors?",
          category: "differentiation",
          importance: "high"
        },
        {
          question: "Can you provide case studies from similar companies?",
          category: "social_proof",
          importance: "medium"
        },
        {
          question: "What is your implementation timeline?",
          category: "logistics",
          importance: "high"
        },
        {
          question: "How does your pricing model work?",
          category: "pricing",
          importance: "high"
        },
        {
          question: "What kind of support do you offer?",
          category: "support",
          importance: "medium"
        }
      ];
    } catch (error) {
      logger.error('Error getting predicted questions:', error);
      return [];
    }
  }

  /**
   * Get suggested collateral using vector similarity
   */
  private async getSuggestedCollateral(params: {
    contactId?: string;
    companyId?: string;
    opportunityId?: string;
  }): Promise<any[]> {
    try {
      // For now, return a static list of collateral
      // In a future implementation, this would use vector similarity to find relevant documents
      return [
        {
          id: "1",
          name: "Product Overview",
          type: "pdf",
          url: "/documents/product-overview.pdf"
        },
        {
          id: "2",
          name: "Case Study: Similar Industry",
          type: "pdf",
          url: "/documents/case-study.pdf"
        },
        {
          id: "3",
          name: "ROI Calculator",
          type: "spreadsheet",
          url: "/documents/roi-calculator.xlsx"
        },
        {
          id: "4",
          name: "Implementation Timeline",
          type: "image",
          url: "/documents/implementation-timeline.png"
        }
      ];
    } catch (error) {
      logger.error('Error getting suggested collateral:', error);
      return [];
    }
  }

  /**
   * Get company news
   */
  private async getCompanyNews(companyId: string): Promise<any[]> {
    try {
      // Get the company name
      const company = await Company.findById(companyId);

      if (!company) {
        return [];
      }

      // For now, return a static list of news
      // In a future implementation, this would use a news API or web search
      return [
        {
          date: new Date(),
          title: `${company.name} Announces Q2 Results`,
          source: "Business Wire",
          summary: "The company reported strong growth in Q2, exceeding analyst expectations.",
          url: "https://example.com/news/1"
        },
        {
          date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
          title: `${company.name} Launches New Product Line`,
          source: "TechCrunch",
          summary: "The company has expanded its product offerings with a new line of services.",
          url: "https://example.com/news/2"
        }
      ];
    } catch (error) {
      logger.error('Error getting company news:', error);
      return [];
    }
  }

  /**
   * Classify an objection using vector similarity
   */
  private async classifyObjection(objectionText: string): Promise<string> {
    try {
      // Define objection classes
      const objectionClasses = [
        "price_too_high",
        "no_budget",
        "need_approval",
        "competitor_better",
        "no_need",
        "timing_not_right",
        "technical_concerns",
        "support_concerns",
        "implementation_concerns",
        "other"
      ];

      // For now, use a simple keyword matching approach
      // In a future implementation, this would use vector similarity

      const objectionLower = objectionText.toLowerCase();

      if (objectionLower.includes("price") || objectionLower.includes("expensive") || objectionLower.includes("cost")) {
        return "price_too_high";
      } else if (objectionLower.includes("budget")) {
        return "no_budget";
      } else if (objectionLower.includes("approval") || objectionLower.includes("decision maker")) {
        return "need_approval";
      } else if (objectionLower.includes("competitor") || objectionLower.includes("alternative")) {
        return "competitor_better";
      } else if (objectionLower.includes("need") || objectionLower.includes("requirement")) {
        return "no_need";
      } else if (objectionLower.includes("timing") || objectionLower.includes("later") || objectionLower.includes("not now")) {
        return "timing_not_right";
      } else if (objectionLower.includes("technical") || objectionLower.includes("feature")) {
        return "technical_concerns";
      } else if (objectionLower.includes("support") || objectionLower.includes("service")) {
        return "support_concerns";
      } else if (objectionLower.includes("implementation") || objectionLower.includes("integration")) {
        return "implementation_concerns";
      } else {
        return "other";
      }
    } catch (error) {
      logger.error('Error classifying objection:', error);
      return "other";
    }
  }

  /**
   * Find similar objections that have been successfully handled
   */
  private async findSimilarObjections(objectionText: string): Promise<any[]> {
    try {
      // For now, return a static list of objections
      // In a future implementation, this would use vector similarity
      return [
        {
          id: "1",
          text: "Your product is too expensive compared to alternatives.",
          class: "price_too_high",
          successRate: 0.75
        },
        {
          id: "2",
          text: "We don't have budget allocated for this right now.",
          class: "no_budget",
          successRate: 0.65
        },
        {
          id: "3",
          text: "I need to get approval from my manager before proceeding.",
          class: "need_approval",
          successRate: 0.85
        }
      ];
    } catch (error) {
      logger.error('Error finding similar objections:', error);
      return [];
    }
  }

  /**
   * Get effective rebuttals ranked by win rate
   */
  private async getEffectiveRebuttals(params: {
    objectionClass: string;
    objectionText: string;
    opportunityId?: string;
    contactId?: string;
    companyId?: string;
    tenantId: string;
  }): Promise<any[]> {
    try {
      // For now, return a static list of rebuttals
      // In a future implementation, this would use vector similarity and win rate data

      const { objectionClass } = params;

      switch (objectionClass) {
        case "price_too_high":
          return [
            {
              id: "1",
              text: "Our solution provides a higher ROI than competitors due to our advanced automation features. On average, customers see a 30% reduction in operational costs within the first 6 months.",
              winRate: 0.82,
              evidence: ["ROI Calculator", "Case Study: Cost Savings"]
            },
            {
              id: "2",
              text: "We offer flexible payment terms that can be spread across multiple quarters to accommodate your budget constraints.",
              winRate: 0.75,
              evidence: ["Payment Plans", "Financing Options"]
            }
          ];
        case "no_budget":
          return [
            {
              id: "3",
              text: "Many of our customers reallocate budget from other initiatives once they see our ROI projections. Would you like me to show you how this could be a net positive for your budget?",
              winRate: 0.68,
              evidence: ["Budget Reallocation Guide", "ROI Calculator"]
            },
            {
              id: "4",
              text: "We can start with a smaller implementation that fits your current budget and expand as you see results.",
              winRate: 0.72,
              evidence: ["Phased Implementation Plan"]
            }
          ];
        default:
          return [
            {
              id: "5",
              text: "I understand your concern. Let me address that specifically...",
              winRate: 0.65,
              evidence: []
            }
          ];
      }
    } catch (error) {
      logger.error('Error getting effective rebuttals:', error);
      return [];
    }
  }

  /**
   * Get supporting evidence (case studies, ROI calculations)
   */
  private async getSupportingEvidence(params: {
    objectionClass: string;
    opportunityId?: string;
    contactId?: string;
    companyId?: string;
  }): Promise<any[]> {
    try {
      // For now, return a static list of evidence
      // In a future implementation, this would use vector similarity

      const { objectionClass } = params;

      switch (objectionClass) {
        case "price_too_high":
          return [
            {
              id: "1",
              title: "ROI Calculator",
              type: "tool",
              description: "Interactive calculator showing cost savings over time",
              url: "/tools/roi-calculator"
            },
            {
              id: "2",
              title: "Case Study: Company X Achieved 40% Cost Reduction",
              type: "pdf",
              description: "Detailed case study with financial metrics",
              url: "/documents/case-study-company-x.pdf"
            }
          ];
        case "no_budget":
          return [
            {
              id: "3",
              title: "Budget Reallocation Guide",
              type: "pdf",
              description: "Guide to finding budget for high-ROI initiatives",
              url: "/documents/budget-guide.pdf"
            },
            {
              id: "4",
              title: "Phased Implementation Plan",
              type: "pdf",
              description: "Step-by-step implementation with budget considerations",
              url: "/documents/phased-implementation.pdf"
            }
          ];
        default:
          return [];
      }
    } catch (error) {
      logger.error('Error getting supporting evidence:', error);
      return [];
    }
  }

  /**
   * Get pricing tiers
   */
  private async getPricingTiers(tenantId: string): Promise<any[]> {
    try {
      // For now, return a static list of pricing tiers
      // In a future implementation, this would come from a pricing database
      return [
        {
          id: "1",
          name: "Starter",
          price: 100,
          billingPeriod: "monthly",
          features: ["Feature 1", "Feature 2", "Feature 3"]
        },
        {
          id: "2",
          name: "Professional",
          price: 200,
          billingPeriod: "monthly",
          features: ["Feature 1", "Feature 2", "Feature 3", "Feature 4", "Feature 5"]
        },
        {
          id: "3",
          name: "Enterprise",
          price: 500,
          billingPeriod: "monthly",
          features: ["Feature 1", "Feature 2", "Feature 3", "Feature 4", "Feature 5", "Feature 6", "Feature 7"]
        }
      ];
    } catch (error) {
      logger.error('Error getting pricing tiers:', error);
      return [];
    }
  }

  /**
   * Get discount policy
   */
  private async getDiscountPolicy(tenantId: string): Promise<any> {
    try {
      // For now, return a static discount policy
      // In a future implementation, this would come from a policy database
      return {
        maxDiscount: 20,
        approvalThresholds: [
          { threshold: 10, approver: "Sales Manager" },
          { threshold: 15, approver: "Sales Director" },
          { threshold: 20, approver: "VP of Sales" }
        ],
        volumeDiscounts: [
          { threshold: 5, discount: 5 },
          { threshold: 10, discount: 10 },
          { threshold: 20, discount: 15 }
        ]
      };
    } catch (error) {
      logger.error('Error getting discount policy:', error);
      return {};
    }
  }

  /**
   * Get legal boilerplate
   */
  private async getLegalBoilerplate(tenantId: string): Promise<any> {
    try {
      // For now, return a static legal boilerplate
      // In a future implementation, this would come from a legal database
      return {
        termsAndConditions: "Standard terms and conditions apply...",
        privacyPolicy: "Privacy policy details...",
        paymentTerms: "Payment is due within 30 days of invoice date...",
        cancellationPolicy: "Cancellation requires 30 days written notice..."
      };
    } catch (error) {
      logger.error('Error getting legal boilerplate:', error);
      return {};
    }
  }

  /**
   * Get suggested upsells based on similar closed-won deals
   */
  private async getSuggestedUpsells(params: {
    opportunityId: string;
    contactId?: string;
    companyId?: string;
    tenantId: string;
  }): Promise<any[]> {
    try {
      // For now, return a static list of upsells
      // In a future implementation, this would use Neo4j to find similar deals
      return [
        {
          id: "1",
          name: "Premium Support",
          description: "24/7 priority support with dedicated account manager",
          price: 100,
          billingPeriod: "monthly",
          conversionRate: 0.75
        },
        {
          id: "2",
          name: "Advanced Analytics",
          description: "In-depth analytics and reporting capabilities",
          price: 50,
          billingPeriod: "monthly",
          conversionRate: 0.65
        },
        {
          id: "3",
          name: "Training Package",
          description: "Comprehensive training for your team",
          price: 500,
          billingPeriod: "one-time",
          conversionRate: 0.85
        }
      ];
    } catch (error) {
      logger.error('Error getting suggested upsells:', error);
      return [];
    }
  }

  /**
   * Get industry-specific clauses
   */
  private async getIndustrySpecificClauses(companyId: string, tenantId: string): Promise<any[]> {
    try {
      // Get the company
      const company = await Company.findById(companyId);

      if (!company || !company.industry) {
        return [];
      }

      // For now, return industry-specific clauses based on the company's industry
      // In a future implementation, this would come from a legal database

      switch (company.industry.toLowerCase()) {
        case "healthcare":
          return [
            {
              id: "1",
              name: "HIPAA Compliance",
              text: "The parties agree to comply with the Health Insurance Portability and Accountability Act...",
              required: true
            },
            {
              id: "2",
              name: "PHI Handling",
              text: "All Protected Health Information (PHI) will be handled in accordance with...",
              required: true
            }
          ];
        case "finance":
          return [
            {
              id: "3",
              name: "Financial Data Security",
              text: "All financial data will be secured using industry-standard encryption...",
              required: true
            },
            {
              id: "4",
              name: "Regulatory Compliance",
              text: "The parties agree to comply with all applicable financial regulations...",
              required: true
            }
          ];
        default:
          return [];
      }
    } catch (error) {
      logger.error('Error getting industry-specific clauses:', error);
      return [];
    }
  }

  /**
   * Get similar deals that were closed-won
   */
  private async getSimilarDeals(params: {
    opportunityId: string;
    contactId?: string;
    companyId?: string;
    tenantId: string;
  }): Promise<any[]> {
    try {
      // For now, return a static list of similar deals
      // In a future implementation, this would use Neo4j to find similar deals
      return [
        {
          id: "1",
          name: "Company A Deal",
          value: 10000,
          products: ["Product 1", "Product 2"],
          closedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
        },
        {
          id: "2",
          name: "Company B Deal",
          value: 15000,
          products: ["Product 1", "Product 3"],
          closedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) // 60 days ago
        }
      ];
    } catch (error) {
      logger.error('Error getting similar deals:', error);
      return [];
    }
  }
}

// Create and export a singleton instance
export const graphRAGService = new GraphRAGService();
