/**
 * Neo4j Service
 * 
 * This service provides integration with Neo4j for graph-based operations,
 * particularly for predictive lead scoring.
 */

import neo4j, { Driver, Session, Record as Neo4jRecord } from 'neo4j-driver';
import { Contact } from '../models/mongoose';
import { logger } from '../utils/logger';

// Neo4j configuration
const NEO4J_URI = process.env.NEO4J_URI || 'bolt://localhost:7687';
const NEO4J_USER = process.env.NEO4J_USER || 'neo4j';
const NEO4J_PASSWORD = process.env.NEO4J_PASSWORD || 'password';

class Neo4jService {
  private driver: Driver | null = null;
  private initialized: boolean = false;

  /**
   * Initialize the Neo4j connection
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      this.driver = neo4j.driver(
        NEO4J_URI,
        neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD),
        {
          maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
          maxConnectionPoolSize: 50,
          connectionAcquisitionTimeout: 2 * 60 * 1000, // 2 minutes
        }
      );

      // Test the connection
      const session = this.driver.session();
      try {
        await session.run('RETURN 1');
        this.initialized = true;
        logger.info('Neo4j connection established successfully');
      } finally {
        await session.close();
      }
    } catch (error) {
      logger.error('Failed to initialize Neo4j connection:', error);
      throw error;
    }
  }

  /**
   * Get a Neo4j session
   */
  private getSession(): Session {
    if (!this.driver || !this.initialized) {
      throw new Error('Neo4j service not initialized');
    }
    return this.driver.session();
  }

  /**
   * Close the Neo4j connection
   */
  async close(): Promise<void> {
    if (this.driver) {
      await this.driver.close();
      this.driver = null;
      this.initialized = false;
      logger.info('Neo4j connection closed');
    }
  }

  /**
   * Sync a contact to Neo4j
   */
  async syncContact(contact: any, tenantId: string): Promise<void> {
    const session = this.getSession();
    try {
      // Create or update the contact node
      await session.run(
        `
        MERGE (c:Contact {id: $id, tenantId: $tenantId})
        ON CREATE SET 
          c.firstName = $firstName,
          c.lastName = $lastName,
          c.email = $email,
          c.phone = $phone,
          c.title = $title,
          c.status = $status,
          c.source = $source,
          c.createdAt = $createdAt,
          c.updatedAt = $updatedAt
        ON MATCH SET 
          c.firstName = $firstName,
          c.lastName = $lastName,
          c.email = $email,
          c.phone = $phone,
          c.title = $title,
          c.status = $status,
          c.source = $source,
          c.updatedAt = $updatedAt
        RETURN c
        `,
        {
          id: contact._id.toString(),
          tenantId,
          firstName: contact.firstName || '',
          lastName: contact.lastName || '',
          email: contact.email || '',
          phone: contact.phone || '',
          title: contact.title || '',
          status: contact.status || 'lead',
          source: contact.source || '',
          createdAt: contact.createdAt?.toISOString() || new Date().toISOString(),
          updatedAt: contact.updatedAt?.toISOString() || new Date().toISOString(),
        }
      );

      // If the contact has a company, create the relationship
      if (contact.companyId) {
        await session.run(
          `
          MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
          MERGE (comp:Company {id: $companyId, tenantId: $tenantId})
          MERGE (c)-[r:WORKS_AT]->(comp)
          RETURN c, comp, r
          `,
          {
            contactId: contact._id.toString(),
            companyId: contact.companyId.toString(),
            tenantId,
          }
        );
      }

      logger.info(`Contact ${contact._id} synced to Neo4j`);
    } catch (error) {
      logger.error(`Error syncing contact ${contact._id} to Neo4j:`, error);
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Sync an interaction to Neo4j
   */
  async syncInteraction(interaction: any, tenantId: string): Promise<void> {
    const session = this.getSession();
    try {
      // Create or update the interaction node
      await session.run(
        `
        MERGE (i:Interaction {id: $id, tenantId: $tenantId})
        ON CREATE SET 
          i.type = $type,
          i.direction = $direction,
          i.channel = $channel,
          i.timestamp = $timestamp,
          i.subject = $subject,
          i.summary = $summary,
          i.sentiment = $sentiment,
          i.createdAt = $createdAt,
          i.updatedAt = $updatedAt
        ON MATCH SET 
          i.type = $type,
          i.direction = $direction,
          i.channel = $channel,
          i.timestamp = $timestamp,
          i.subject = $subject,
          i.summary = $summary,
          i.sentiment = $sentiment,
          i.updatedAt = $updatedAt
        RETURN i
        `,
        {
          id: interaction._id.toString(),
          tenantId,
          type: interaction.type || '',
          direction: interaction.direction || '',
          channel: interaction.channel || '',
          timestamp: interaction.timestamp || new Date().toISOString(),
          subject: interaction.subject || '',
          summary: interaction.summary || '',
          sentiment: interaction.sentiment || 'neutral',
          createdAt: interaction.createdAt?.toISOString() || new Date().toISOString(),
          updatedAt: interaction.updatedAt?.toISOString() || new Date().toISOString(),
        }
      );

      // Create the relationship between contact and interaction
      await session.run(
        `
        MATCH (i:Interaction {id: $interactionId, tenantId: $tenantId})
        MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
        MERGE (c)-[r:HAS_INTERACTION]->(i)
        RETURN c, i, r
        `,
        {
          interactionId: interaction._id.toString(),
          contactId: interaction.contactId.toString(),
          tenantId,
        }
      );

      logger.info(`Interaction ${interaction._id} synced to Neo4j`);
    } catch (error) {
      logger.error(`Error syncing interaction ${interaction._id} to Neo4j:`, error);
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Sync an opportunity to Neo4j
   */
  async syncOpportunity(opportunity: any, tenantId: string): Promise<void> {
    const session = this.getSession();
    try {
      // Create or update the opportunity node
      await session.run(
        `
        MERGE (o:Opportunity {id: $id, tenantId: $tenantId})
        ON CREATE SET 
          o.name = $name,
          o.stage = $stage,
          o.value = $value,
          o.currency = $currency,
          o.closeDate = $closeDate,
          o.probability = $probability,
          o.createdAt = $createdAt,
          o.updatedAt = $updatedAt
        ON MATCH SET 
          o.name = $name,
          o.stage = $stage,
          o.value = $value,
          o.currency = $currency,
          o.closeDate = $closeDate,
          o.probability = $probability,
          o.updatedAt = $updatedAt
        RETURN o
        `,
        {
          id: opportunity._id.toString(),
          tenantId,
          name: opportunity.name || '',
          stage: opportunity.stage || '',
          value: opportunity.value || 0,
          currency: opportunity.currency || '$',
          closeDate: opportunity.closeDate || null,
          probability: opportunity.probability || 0,
          createdAt: opportunity.createdAt?.toISOString() || new Date().toISOString(),
          updatedAt: opportunity.updatedAt?.toISOString() || new Date().toISOString(),
        }
      );

      // Create relationships
      if (opportunity.contactId) {
        await session.run(
          `
          MATCH (o:Opportunity {id: $opportunityId, tenantId: $tenantId})
          MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
          MERGE (c)-[r:HAS_OPPORTUNITY]->(o)
          RETURN c, o, r
          `,
          {
            opportunityId: opportunity._id.toString(),
            contactId: opportunity.contactId.toString(),
            tenantId,
          }
        );
      }

      if (opportunity.companyId) {
        await session.run(
          `
          MATCH (o:Opportunity {id: $opportunityId, tenantId: $tenantId})
          MATCH (comp:Company {id: $companyId, tenantId: $tenantId})
          MERGE (comp)-[r:HAS_OPPORTUNITY]->(o)
          RETURN comp, o, r
          `,
          {
            opportunityId: opportunity._id.toString(),
            companyId: opportunity.companyId.toString(),
            tenantId,
          }
        );
      }

      logger.info(`Opportunity ${opportunity._id} synced to Neo4j`);
    } catch (error) {
      logger.error(`Error syncing opportunity ${opportunity._id} to Neo4j:`, error);
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Calculate lead score for a contact
   */
  async calculateLeadScore(contactId: string, tenantId: string): Promise<number> {
    const session = this.getSession();
    try {
      // Complex scoring algorithm using graph relationships and properties
      const result = await session.run(
        `
        MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
        
        // Base score
        WITH c, 50 as score
        
        // Interaction score
        OPTIONAL MATCH (c)-[:HAS_INTERACTION]->(i:Interaction)
        WITH c, score, count(i) as interactionCount,
             sum(CASE i.sentiment
                  WHEN 'positive' THEN 5
                  WHEN 'neutral' THEN 2
                  WHEN 'negative' THEN -2
                  WHEN 'mixed' THEN 0
                  ELSE 0
                END) as sentimentScore,
             sum(CASE i.type
                  WHEN 'meeting' THEN 8
                  WHEN 'call' THEN 5
                  WHEN 'email' THEN 3
                  WHEN 'social' THEN 2
                  ELSE 1
                END) as typeScore
        
        // Opportunity score
        OPTIONAL MATCH (c)-[:HAS_OPPORTUNITY]->(o:Opportunity)
        WITH c, score, interactionCount, sentimentScore, typeScore,
             count(o) as opportunityCount,
             sum(o.value) as totalValue,
             max(o.probability) as maxProbability
        
        // Company relationship score
        OPTIONAL MATCH (c)-[:WORKS_AT]->(comp:Company)
        OPTIONAL MATCH (comp)-[:HAS_OPPORTUNITY]->(compOpp:Opportunity)
        WITH c, score, interactionCount, sentimentScore, typeScore,
             opportunityCount, totalValue, maxProbability,
             count(compOpp) as companyOppCount
        
        // Calculate final score
        WITH c, score +
             // Interaction factors
             CASE WHEN interactionCount > 10 THEN 15
                  WHEN interactionCount > 5 THEN 10
                  WHEN interactionCount > 0 THEN 5
                  ELSE 0
             END +
             // Sentiment factor
             CASE WHEN sentimentScore > 20 THEN 15
                  WHEN sentimentScore > 10 THEN 10
                  WHEN sentimentScore > 0 THEN 5
                  WHEN sentimentScore < 0 THEN -5
                  ELSE 0
             END +
             // Interaction type factor
             CASE WHEN typeScore > 30 THEN 10
                  WHEN typeScore > 15 THEN 5
                  ELSE 0
             END +
             // Opportunity factors
             CASE WHEN opportunityCount > 0 THEN 15 ELSE 0 END +
             CASE WHEN totalValue > 100000 THEN 15
                  WHEN totalValue > 10000 THEN 10
                  WHEN totalValue > 0 THEN 5
                  ELSE 0
             END +
             CASE WHEN maxProbability > 80 THEN 10
                  WHEN maxProbability > 50 THEN 5
                  WHEN maxProbability > 0 THEN 2
                  ELSE 0
             END +
             // Company opportunity factor
             CASE WHEN companyOppCount > 2 THEN 10
                  WHEN companyOppCount > 0 THEN 5
                  ELSE 0
             END as finalScore
        
        // Ensure score is between 0 and 100
        RETURN 
          CASE 
            WHEN finalScore > 100 THEN 100
            WHEN finalScore < 0 THEN 0
            ELSE finalScore
          END as leadScore
        `,
        {
          contactId,
          tenantId,
        }
      );

      if (result.records.length === 0) {
        return 50; // Default score if no data available
      }

      const leadScore = result.records[0].get('leadScore').toNumber();
      logger.info(`Lead score calculated for contact ${contactId}: ${leadScore}`);
      return leadScore;
    } catch (error) {
      logger.error(`Error calculating lead score for contact ${contactId}:`, error);
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Get similar contacts based on graph relationships
   */
  async getSimilarContacts(contactId: string, tenantId: string, limit: number = 5): Promise<any[]> {
    const session = this.getSession();
    try {
      const result = await session.run(
        `
        MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
        
        // Find contacts with similar interactions
        OPTIONAL MATCH (c)-[:HAS_INTERACTION]->(i:Interaction)<-[:HAS_INTERACTION]-(similar:Contact)
        WHERE similar.tenantId = $tenantId AND similar.id <> $contactId
        
        // Find contacts at the same company
        OPTIONAL MATCH (c)-[:WORKS_AT]->(comp:Company)<-[:WORKS_AT]-(colleague:Contact)
        WHERE colleague.tenantId = $tenantId AND colleague.id <> $contactId
        
        // Find contacts with similar opportunities
        OPTIONAL MATCH (c)-[:HAS_OPPORTUNITY]->(o:Opportunity)
        OPTIONAL MATCH (otherContact:Contact)-[:HAS_OPPORTUNITY]->(similarOpp:Opportunity)
        WHERE otherContact.tenantId = $tenantId AND otherContact.id <> $contactId
          AND similarOpp.stage = o.stage
        
        // Combine all similar contacts with a similarity score
        WITH c, 
             similar, count(i) as interactionSimilarity,
             colleague, count(comp) as companySimilarity,
             otherContact, count(similarOpp) as opportunitySimilarity
        
        WITH c, 
             CASE WHEN similar IS NOT NULL THEN similar ELSE null END as similar,
             CASE WHEN colleague IS NOT NULL THEN colleague ELSE null END as colleague,
             CASE WHEN otherContact IS NOT NULL THEN otherContact ELSE null END as otherContact,
             interactionSimilarity, companySimilarity, opportunitySimilarity
        
        // Combine all contacts and calculate total similarity
        WITH c, 
             COLLECT(DISTINCT similar) + COLLECT(DISTINCT colleague) + COLLECT(DISTINCT otherContact) as allContacts,
             interactionSimilarity, companySimilarity, opportunitySimilarity
        
        UNWIND allContacts as contact
        WHERE contact IS NOT NULL
        
        WITH DISTINCT contact,
             SUM(interactionSimilarity) * 2 + 
             SUM(companySimilarity) * 3 + 
             SUM(opportunitySimilarity) * 1 as similarityScore
        
        ORDER BY similarityScore DESC
        LIMIT $limit
        
        RETURN contact.id as id, contact.firstName as firstName, contact.lastName as lastName, 
               contact.email as email, contact.title as title, similarityScore
        `,
        {
          contactId,
          tenantId,
          limit: neo4j.int(limit),
        }
      );

      return result.records.map((record: Neo4jRecord) => ({
        id: record.get('id'),
        firstName: record.get('firstName'),
        lastName: record.get('lastName'),
        email: record.get('email'),
        title: record.get('title'),
        similarityScore: record.get('similarityScore').toNumber(),
      }));
    } catch (error) {
      logger.error(`Error getting similar contacts for ${contactId}:`, error);
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Get lead conversion probability
   */
  async getLeadConversionProbability(contactId: string, tenantId: string): Promise<number> {
    const session = this.getSession();
    try {
      const result = await session.run(
        `
        MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
        
        // Get contact's interactions
        OPTIONAL MATCH (c)-[:HAS_INTERACTION]->(i:Interaction)
        WITH c, count(i) as interactionCount,
             count(CASE WHEN i.type = 'meeting' THEN 1 END) as meetingCount,
             count(CASE WHEN i.type = 'call' THEN 1 END) as callCount,
             count(CASE WHEN i.type = 'email' THEN 1 END) as emailCount,
             count(CASE WHEN i.sentiment = 'positive' THEN 1 END) as positiveCount
        
        // Find similar contacts that converted
        MATCH (similar:Contact {tenantId: $tenantId})-[:HAS_OPPORTUNITY]->(o:Opportunity)
        WHERE o.stage = 'closed_won'
        
        // Compare interaction patterns
        OPTIONAL MATCH (similar)-[:HAS_INTERACTION]->(si:Interaction)
        WITH c, interactionCount, meetingCount, callCount, emailCount, positiveCount,
             similar, count(si) as similarInteractionCount,
             count(CASE WHEN si.type = 'meeting' THEN 1 END) as similarMeetingCount,
             count(CASE WHEN si.type = 'call' THEN 1 END) as similarCallCount,
             count(CASE WHEN si.type = 'email' THEN 1 END) as similarEmailCount,
             count(CASE WHEN si.sentiment = 'positive' THEN 1 END) as similarPositiveCount
        
        // Calculate similarity score
        WITH c, similar,
             CASE 
               WHEN interactionCount = 0 THEN 0
               ELSE (
                 ABS(1.0 * meetingCount / interactionCount - 1.0 * similarMeetingCount / similarInteractionCount) +
                 ABS(1.0 * callCount / interactionCount - 1.0 * similarCallCount / similarInteractionCount) +
                 ABS(1.0 * emailCount / interactionCount - 1.0 * similarEmailCount / similarInteractionCount) +
                 ABS(1.0 * positiveCount / interactionCount - 1.0 * similarPositiveCount / similarInteractionCount)
               )
             END as patternDifference
        
        // Get the most similar converted contacts
        ORDER BY patternDifference ASC
        LIMIT 10
        
        // Calculate conversion probability based on lead score
        WITH c, count(similar) as similarConvertedCount
        
        MATCH (c:Contact {id: $contactId, tenantId: $tenantId})
        
        // Get the lead score (simplified version of the calculation)
        OPTIONAL MATCH (c)-[:HAS_INTERACTION]->(i:Interaction)
        WITH c, similarConvertedCount,
             count(i) as interactionCount,
             sum(CASE i.sentiment
                  WHEN 'positive' THEN 5
                  WHEN 'neutral' THEN 2
                  WHEN 'negative' THEN -2
                  WHEN 'mixed' THEN 0
                  ELSE 0
                END) as sentimentScore
        
        // Calculate conversion probability
        WITH c, 
             CASE 
               WHEN similarConvertedCount = 0 THEN 0.3 // Base probability
               ELSE 0.3 + (similarConvertedCount * 0.05) // Increase based on similar converted contacts
             END +
             CASE 
               WHEN interactionCount > 10 THEN 0.2
               WHEN interactionCount > 5 THEN 0.1
               WHEN interactionCount > 0 THEN 0.05
               ELSE 0
             END +
             CASE 
               WHEN sentimentScore > 20 THEN 0.2
               WHEN sentimentScore > 10 THEN 0.1
               WHEN sentimentScore > 0 THEN 0.05
               WHEN sentimentScore < 0 THEN -0.1
               ELSE 0
             END as conversionProbability
        
        // Ensure probability is between 0 and 1
        RETURN 
          CASE 
            WHEN conversionProbability > 1 THEN 1
            WHEN conversionProbability < 0 THEN 0
            ELSE conversionProbability
          END as probability
        `,
        {
          contactId,
          tenantId,
        }
      );

      if (result.records.length === 0) {
        return 0.3; // Default probability if no data available
      }

      const probability = result.records[0].get('probability').toNumber();
      return probability;
    } catch (error) {
      logger.error(`Error calculating conversion probability for contact ${contactId}:`, error);
      throw error;
    } finally {
      await session.close();
    }
  }
}

export const neo4jService = new Neo4jService();
