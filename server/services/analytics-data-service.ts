import mongoose from 'mongoose';
import { Request } from 'express';
import {
  EventsRaw,
  IEventsRaw,
  AnalyticsDataset,
  IAnalyticsDataset,
  MarketingCampaign,
  IMarketingCampaign
} from '../models/mongoose';
import {
  EventsRawType,
  AnalyticsDatasetType,
  MarketingCampaignType,
  TrackEventRequest
} from '@types/analytics-reporting';
import { logger } from '../utils/logger';
import { subscriptionClient } from './subscription-client';
import { BigQueryService } from './bigquery-service';

/**
 * Service for managing analytics data
 */
export class AnalyticsDataService {
  /**
   * Track an event
   */
  static async trackEvent(data: TrackEventRequest): Promise<string> {
    try {
      // Validate tenant_id
      if (!data.tenant_id) {
        throw new Error('Tenant ID is required');
      }

      // Validate that either visitor_id or contact_id is provided
      if (!data.visitor_id && !data.contact_id) {
        throw new Error('Either visitor_id or contact_id is required');
      }

      // Record usage
      await subscriptionClient.recordUsage(
        data.tenant_id,
        'analytics.event_tracking',
        1
      );

      // Create event
      const timestamp = data.timestamp ? new Date(data.timestamp) : new Date();
      const event = new EventsRaw({
        tenant_id: new mongoose.Types.ObjectId(data.tenant_id),
        visitor_id: data.visitor_id,
        contact_id: data.contact_id ? new mongoose.Types.ObjectId(data.contact_id) : undefined,
        timestamp,
        channel: data.channel,
        campaign: data.campaign,
        medium: data.medium,
        source: data.source,
        event_type: data.event_type,
        meta_json: data.meta_json || {},
      });

      // Save event to MongoDB
      const savedEvent = await event.save();

      // Also send to BigQuery for data warehousing (non-blocking)
      this.sendEventToBigQuery(data.tenant_id, {
        event_id: savedEvent._id.toString(),
        visitor_id: data.visitor_id,
        contact_id: data.contact_id,
        timestamp: timestamp.toISOString(),
        channel: data.channel,
        campaign: data.campaign,
        medium: data.medium,
        source: data.source,
        event_type: data.event_type,
        meta_json: JSON.stringify(data.meta_json || {}),
      }).catch(error => {
        // Log error but don't fail the request
        logger.error('Error sending event to BigQuery:', error);
      });

      return savedEvent._id.toString();
    } catch (error) {
      logger.error('Error tracking event:', error);
      throw error;
    }
  }

  /**
   * Send event to BigQuery
   * This is a private method to handle BigQuery integration
   */
  private static async sendEventToBigQuery(tenantId: string, eventData: Record<string, any>): Promise<void> {
    try {
      // Ensure events table exists
      await this.ensureEventsTable(tenantId);

      // Insert event into BigQuery
      await BigQueryService.insertRows(tenantId, 'events', [eventData]);
    } catch (error) {
      logger.error('Error sending event to BigQuery:', error);
      throw error;
    }
  }

  /**
   * Ensure events table exists in BigQuery
   */
  private static async ensureEventsTable(tenantId: string): Promise<void> {
    try {
      // Define schema for events table
      const schema = [
        { name: 'event_id', type: 'STRING', mode: 'REQUIRED' },
        { name: 'visitor_id', type: 'STRING' },
        { name: 'contact_id', type: 'STRING' },
        { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
        { name: 'channel', type: 'STRING', mode: 'REQUIRED' },
        { name: 'campaign', type: 'STRING' },
        { name: 'medium', type: 'STRING' },
        { name: 'source', type: 'STRING' },
        { name: 'event_type', type: 'STRING', mode: 'REQUIRED' },
        { name: 'meta_json', type: 'STRING' },
      ];

      // Create table if it doesn't exist
      await BigQueryService.createTable(tenantId, 'events', schema);
    } catch (error) {
      logger.error('Error ensuring events table:', error);
      throw error;
    }
  }

  /**
   * Get events
   */
  static async getEvents(options: {
    tenant_id: string;
    visitor_id?: string;
    contact_id?: string;
    channel?: string;
    event_type?: string;
    start_date?: Date;
    end_date?: Date;
    limit?: number;
    offset?: number;
  }): Promise<EventsRawType[]> {
    try {
      const {
        tenant_id,
        visitor_id,
        contact_id,
        channel,
        event_type,
        start_date,
        end_date,
        limit = 100,
        offset = 0,
      } = options;

      // Build query
      const query: any = {
        tenant_id: new mongoose.Types.ObjectId(tenant_id),
      };

      if (visitor_id) {
        query.visitor_id = visitor_id;
      }

      if (contact_id) {
        query.contact_id = new mongoose.Types.ObjectId(contact_id);
      }

      if (channel) {
        query.channel = channel;
      }

      if (event_type) {
        query.event_type = event_type;
      }

      if (start_date || end_date) {
        query.timestamp = {};

        if (start_date) {
          query.timestamp.$gte = start_date;
        }

        if (end_date) {
          query.timestamp.$lte = end_date;
        }
      }

      // Execute query
      const events = await EventsRaw.find(query)
        .sort({ timestamp: -1 })
        .skip(offset)
        .limit(limit);

      // Map to type
      return events.map(event => ({
        id: event._id.toString(),
        tenant_id: event.tenant_id.toString(),
        visitor_id: event.visitor_id,
        contact_id: event.contact_id?.toString(),
        timestamp: event.timestamp.toISOString(),
        channel: event.channel,
        campaign: event.campaign,
        medium: event.medium,
        source: event.source,
        event_type: event.event_type,
        meta_json: event.meta_json,
        created_at: event.created_at.toISOString(),
        updated_at: event.updated_at.toISOString(),
      }));
    } catch (error) {
      logger.error('Error getting events:', error);
      throw error;
    }
  }

  /**
   * Get event counts
   */
  static async getEventCounts(options: {
    tenant_id: string;
    group_by: 'channel' | 'event_type' | 'campaign' | 'source' | 'medium' | 'day' | 'week' | 'month';
    start_date?: Date;
    end_date?: Date;
  }): Promise<{ _id: string; count: number }[]> {
    try {
      const {
        tenant_id,
        group_by,
        start_date,
        end_date,
      } = options;

      // Build match stage
      const match: any = {
        tenant_id: new mongoose.Types.ObjectId(tenant_id),
      };

      if (start_date || end_date) {
        match.timestamp = {};

        if (start_date) {
          match.timestamp.$gte = start_date;
        }

        if (end_date) {
          match.timestamp.$lte = end_date;
        }
      }

      // Build group stage
      let groupId: any;

      if (group_by === 'day') {
        groupId = {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' },
        };
      } else if (group_by === 'week') {
        groupId = {
          year: { $year: '$timestamp' },
          week: { $week: '$timestamp' },
        };
      } else if (group_by === 'month') {
        groupId = {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
        };
      } else {
        groupId = `$${group_by}`;
      }

      // Execute aggregation
      const counts = await EventsRaw.aggregate([
        { $match: match },
        {
          $group: {
            _id: groupId,
            count: { $sum: 1 },
          },
        },
        { $sort: { count: -1 } },
      ]);

      return counts;
    } catch (error) {
      logger.error('Error getting event counts:', error);
      throw error;
    }
  }

  /**
   * Create or update a dataset
   */
  static async createOrUpdateDataset(dataset: Partial<AnalyticsDatasetType> & { tenant_id: string }): Promise<string> {
    try {
      // Validate tenant_id
      if (!dataset.tenant_id) {
        throw new Error('Tenant ID is required');
      }

      // Check if dataset exists
      let existingDataset: IAnalyticsDataset | null = null;

      if (dataset.id) {
        existingDataset = await AnalyticsDataset.findById(dataset.id);
      } else if (dataset.name) {
        existingDataset = await AnalyticsDataset.findOne({
          tenant_id: new mongoose.Types.ObjectId(dataset.tenant_id),
          name: dataset.name,
        });
      }

      if (existingDataset) {
        // Update existing dataset
        Object.assign(existingDataset, {
          display_name: dataset.display_name || existingDataset.display_name,
          description: dataset.description || existingDataset.description,
          source_type: dataset.source_type || existingDataset.source_type,
          source_config: dataset.source_config || existingDataset.source_config,
          fields: dataset.fields || existingDataset.fields,
          is_active: dataset.is_active !== undefined ? dataset.is_active : existingDataset.is_active,
          refresh_frequency: dataset.refresh_frequency || existingDataset.refresh_frequency,
        });

        await existingDataset.save();
        return existingDataset._id.toString();
      } else {
        // Create new dataset
        const newDataset = new AnalyticsDataset({
          tenant_id: new mongoose.Types.ObjectId(dataset.tenant_id),
          name: dataset.name,
          display_name: dataset.display_name,
          description: dataset.description,
          source_type: dataset.source_type,
          source_config: dataset.source_config,
          fields: dataset.fields || [],
          is_active: dataset.is_active !== undefined ? dataset.is_active : true,
          refresh_frequency: dataset.refresh_frequency || 'daily',
        });

        await newDataset.save();
        return newDataset._id.toString();
      }
    } catch (error) {
      logger.error('Error creating or updating dataset:', error);
      throw error;
    }
  }

  /**
   * Get datasets
   */
  static async getDatasets(options: {
    tenant_id: string;
    is_active?: boolean;
  }): Promise<AnalyticsDatasetType[]> {
    try {
      const { tenant_id, is_active } = options;

      // Build query
      const query: any = {
        tenant_id: new mongoose.Types.ObjectId(tenant_id),
      };

      if (is_active !== undefined) {
        query.is_active = is_active;
      }

      // Execute query
      const datasets = await AnalyticsDataset.find(query).sort({ name: 1 });

      // Map to type
      return datasets.map(dataset => ({
        id: dataset._id.toString(),
        tenant_id: dataset.tenant_id.toString(),
        name: dataset.name,
        display_name: dataset.display_name,
        description: dataset.description,
        source_type: dataset.source_type,
        source_config: dataset.source_config,
        fields: dataset.fields,
        is_active: dataset.is_active,
        refresh_frequency: dataset.refresh_frequency,
        last_refreshed_at: dataset.last_refreshed_at?.toISOString(),
        created_at: dataset.created_at.toISOString(),
        updated_at: dataset.updated_at.toISOString(),
      }));
    } catch (error) {
      logger.error('Error getting datasets:', error);
      throw error;
    }
  }
}
