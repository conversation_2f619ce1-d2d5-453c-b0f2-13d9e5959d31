/**
 * Email Template Service
 * 
 * This service handles email template management including:
 * - Creating and managing templates
 * - A/B testing variants
 * - Template rendering with variables
 * - Template performance tracking
 */

import mongoose from 'mongoose';
import { 
  EmailTemplate, 
  IEmailTemplate, 
  EmailTemplateCategory 
} from '../models/mongoose/email-template-model';
import { EmailTracking } from '../models/mongoose';
import { logger } from '../utils/logger';

class EmailTemplateService {
  /**
   * Create a new email template
   */
  async createTemplate(data: {
    userId: string;
    tenantId?: string;
    name: string;
    description?: string;
    subject: string;
    body: string;
    plainText?: string;
    category?: EmailTemplateCategory;
    tags?: string[];
    isActive?: boolean;
    isDefault?: boolean;
    variables?: {
      name: string;
      description?: string;
      defaultValue?: string;
      required?: boolean;
    }[];
    metadata?: {
      previewText?: string;
      fromName?: string;
      fromEmail?: string;
      replyTo?: string;
    };
    trackingSettings?: {
      trackOpens?: boolean;
      trackLinks?: boolean;
    };
    customFields?: Record<string, any>;
  }): Promise<IEmailTemplate> {
    try {
      // Check if template with same name already exists
      const existingTemplate = await EmailTemplate.findOne({
        tenantId: data.tenantId ? new mongoose.Types.ObjectId(data.tenantId) : undefined,
        name: data.name
      });

      if (existingTemplate) {
        throw new Error(`Template with name "${data.name}" already exists`);
      }

      // Extract variables from template body
      const extractedVariables = this.extractVariablesFromTemplate(data.body);
      
      // Merge extracted variables with provided variables
      const variables = data.variables || [];
      extractedVariables.forEach(varName => {
        if (!variables.some(v => v.name === varName)) {
          variables.push({
            name: varName,
            required: false
          });
        }
      });

      // Create the template
      const template = new EmailTemplate({
        userId: new mongoose.Types.ObjectId(data.userId),
        tenantId: data.tenantId ? new mongoose.Types.ObjectId(data.tenantId) : undefined,
        name: data.name,
        description: data.description,
        subject: data.subject,
        body: data.body,
        plainText: data.plainText || this.htmlToPlainText(data.body),
        category: data.category || EmailTemplateCategory.OTHER,
        tags: data.tags || [],
        isActive: data.isActive !== false,
        isDefault: data.isDefault || false,
        variables,
        metadata: data.metadata || {},
        trackingSettings: {
          trackOpens: data.trackingSettings?.trackOpens !== false,
          trackLinks: data.trackingSettings?.trackLinks !== false
        },
        customFields: data.customFields || {}
      });

      await template.save();
      logger.info(`Email template created: ${template.name}`);
      return template;
    } catch (error) {
      logger.error('Error creating email template:', error);
      throw error;
    }
  }

  /**
   * Update an email template
   */
  async updateTemplate(
    templateId: string,
    data: Partial<{
      name: string;
      description: string;
      subject: string;
      body: string;
      plainText: string;
      category: EmailTemplateCategory;
      tags: string[];
      isActive: boolean;
      isDefault: boolean;
      variables: {
        name: string;
        description?: string;
        defaultValue?: string;
        required?: boolean;
      }[];
      metadata: {
        previewText?: string;
        fromName?: string;
        fromEmail?: string;
        replyTo?: string;
      };
      trackingSettings: {
        trackOpens: boolean;
        trackLinks: boolean;
      };
      customFields: Record<string, any>;
    }>
  ): Promise<IEmailTemplate | null> {
    try {
      // Find the template
      const template = await EmailTemplate.findById(templateId);

      if (!template) {
        logger.warn(`Template ${templateId} not found`);
        return null;
      }

      // Update fields
      if (data.name !== undefined) template.name = data.name;
      if (data.description !== undefined) template.description = data.description;
      if (data.subject !== undefined) template.subject = data.subject;
      
      if (data.body !== undefined) {
        template.body = data.body;
        
        // Update plainText if body changed and plainText not provided
        if (data.plainText === undefined) {
          template.plainText = this.htmlToPlainText(data.body);
        }
        
        // Extract variables from updated body
        if (data.variables === undefined) {
          const extractedVariables = this.extractVariablesFromTemplate(data.body);
          
          // Add new variables
          extractedVariables.forEach(varName => {
            if (!template.variables.some(v => v.name === varName)) {
              template.variables.push({
                name: varName,
                required: false
              });
            }
          });
        }
      }
      
      if (data.plainText !== undefined) template.plainText = data.plainText;
      if (data.category !== undefined) template.category = data.category;
      if (data.tags !== undefined) template.tags = data.tags;
      if (data.isActive !== undefined) template.isActive = data.isActive;
      if (data.isDefault !== undefined) template.isDefault = data.isDefault;
      if (data.variables !== undefined) template.variables = data.variables;
      
      if (data.metadata !== undefined) {
        template.metadata = {
          ...template.metadata,
          ...data.metadata
        };
      }
      
      if (data.trackingSettings !== undefined) {
        template.trackingSettings = {
          ...template.trackingSettings,
          ...data.trackingSettings
        };
      }
      
      if (data.customFields !== undefined) {
        template.customFields = {
          ...template.customFields,
          ...data.customFields
        };
      }

      await template.save();
      logger.info(`Email template updated: ${template.name}`);
      return template;
    } catch (error) {
      logger.error(`Error updating email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Get an email template by ID
   */
  async getTemplateById(templateId: string): Promise<IEmailTemplate | null> {
    try {
      return await EmailTemplate.findById(templateId);
    } catch (error) {
      logger.error(`Error getting email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Get templates by tenant
   */
  async getTemplatesByTenant(
    tenantId: string,
    options: {
      category?: EmailTemplateCategory;
      tags?: string[];
      isActive?: boolean;
      search?: string;
      page?: number;
      limit?: number;
      sort?: string;
      sortDirection?: 'asc' | 'desc';
    } = {}
  ): Promise<{ templates: IEmailTemplate[]; total: number }> {
    try {
      // Build query
      const query: any = { tenantId: new mongoose.Types.ObjectId(tenantId) };

      if (options.category) {
        query.category = options.category;
      }

      if (options.tags && options.tags.length > 0) {
        query.tags = { $in: options.tags };
      }

      if (options.isActive !== undefined) {
        query.isActive = options.isActive;
      }

      if (options.search) {
        query.$text = { $search: options.search };
      }

      // Build sort
      const sort: any = {};
      if (options.sort) {
        sort[options.sort] = options.sortDirection === 'desc' ? -1 : 1;
      } else {
        sort.createdAt = -1; // Default sort by creation date, newest first
      }

      // Pagination
      const page = options.page || 1;
      const limit = options.limit || 20;
      const skip = (page - 1) * limit;

      // Execute query
      const templates = await EmailTemplate.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);

      // Get total count
      const total = await EmailTemplate.countDocuments(query);

      return { templates, total };
    } catch (error) {
      logger.error(`Error getting email templates for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an email template
   */
  async deleteTemplate(templateId: string): Promise<boolean> {
    try {
      const result = await EmailTemplate.deleteOne({ _id: new mongoose.Types.ObjectId(templateId) });
      
      if (result.deletedCount === 0) {
        logger.warn(`Template ${templateId} not found for deletion`);
        return false;
      }
      
      logger.info(`Email template ${templateId} deleted`);
      return true;
    } catch (error) {
      logger.error(`Error deleting email template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Enable A/B testing for a template
   */
  async enableABTesting(
    templateId: string,
    variants: {
      name: string;
      subject?: string;
      body?: string;
      metadata?: Record<string, any>;
      weight: number;
    }[]
  ): Promise<IEmailTemplate | null> {
    try {
      // Find the template
      const template = await EmailTemplate.findById(templateId);

      if (!template) {
        logger.warn(`Template ${templateId} not found`);
        return null;
      }

      // Validate variants
      if (!variants || variants.length < 2) {
        throw new Error('At least two variants are required for A/B testing');
      }

      // Enable A/B testing and set variants
      template.abTestingEnabled = true;
      template.abTestingVariants = variants;

      await template.save();
      logger.info(`A/B testing enabled for template ${template.name}`);
      return template;
    } catch (error) {
      logger.error(`Error enabling A/B testing for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Disable A/B testing for a template
   */
  async disableABTesting(templateId: string): Promise<IEmailTemplate | null> {
    try {
      // Find the template
      const template = await EmailTemplate.findById(templateId);

      if (!template) {
        logger.warn(`Template ${templateId} not found`);
        return null;
      }

      // Disable A/B testing
      template.abTestingEnabled = false;

      await template.save();
      logger.info(`A/B testing disabled for template ${template.name}`);
      return template;
    } catch (error) {
      logger.error(`Error disabling A/B testing for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Select a variant for A/B testing
   */
  selectABTestingVariant(template: IEmailTemplate): {
    variant: string;
    subject?: string;
    body?: string;
    metadata?: Record<string, any>;
  } {
    if (!template.abTestingEnabled || !template.abTestingVariants || template.abTestingVariants.length < 2) {
      return {
        variant: 'original',
        subject: template.subject,
        body: template.body,
        metadata: template.metadata
      };
    }

    // Calculate total weight
    const totalWeight = template.abTestingVariants.reduce((sum, variant) => sum + variant.weight, 0);
    
    // Generate random number between 0 and totalWeight
    const random = Math.random() * totalWeight;
    
    // Select variant based on weight
    let cumulativeWeight = 0;
    for (const variant of template.abTestingVariants) {
      cumulativeWeight += variant.weight;
      if (random <= cumulativeWeight) {
        return {
          variant: variant.name,
          subject: variant.subject || template.subject,
          body: variant.body || template.body,
          metadata: variant.metadata || template.metadata
        };
      }
    }
    
    // Fallback to original
    return {
      variant: 'original',
      subject: template.subject,
      body: template.body,
      metadata: template.metadata
    };
  }

  /**
   * Render a template with variables
   */
  renderTemplate(
    template: IEmailTemplate,
    variables: Record<string, any> = {}
  ): { subject: string; body: string; plainText: string } {
    let subject = template.subject;
    let body = template.body;
    let plainText = template.plainText || this.htmlToPlainText(body);

    // Replace variables in subject, body, and plainText
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      subject = subject.replace(regex, String(value));
      body = body.replace(regex, String(value));
      plainText = plainText.replace(regex, String(value));
    }

    return { subject, body, plainText };
  }

  /**
   * Update template statistics
   */
  async updateTemplateStats(
    templateId: string,
    eventType: 'sent' | 'opened' | 'clicked' | 'replied' | 'bounced' | 'complained'
  ): Promise<void> {
    try {
      // Update stats using atomic operation
      const updateField = `stats.${eventType}`;
      await EmailTemplate.updateOne(
        { _id: new mongoose.Types.ObjectId(templateId) },
        { $inc: { [updateField]: 1 } }
      );
      
      logger.info(`Updated ${eventType} stats for template ${templateId}`);
    } catch (error) {
      logger.error(`Error updating stats for template ${templateId}:`, error);
    }
  }

  /**
   * Get template performance metrics
   */
  async getTemplatePerformance(templateId: string): Promise<{
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
    bounced: number;
    complained: number;
    openRate: number;
    clickRate: number;
    clickToOpenRate: number;
    replyRate: number;
    bounceRate: number;
    complaintRate: number;
  }> {
    try {
      // Get template
      const template = await EmailTemplate.findById(templateId);

      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }

      // Get stats
      const { sent, opened, clicked, replied, bounced, complained } = template.stats || {
        sent: 0,
        opened: 0,
        clicked: 0,
        replied: 0,
        bounced: 0,
        complained: 0
      };

      // Calculate rates
      const openRate = sent > 0 ? (opened / sent) * 100 : 0;
      const clickRate = sent > 0 ? (clicked / sent) * 100 : 0;
      const clickToOpenRate = opened > 0 ? (clicked / opened) * 100 : 0;
      const replyRate = sent > 0 ? (replied / sent) * 100 : 0;
      const bounceRate = sent > 0 ? (bounced / sent) * 100 : 0;
      const complaintRate = sent > 0 ? (complained / sent) * 100 : 0;

      return {
        sent,
        opened,
        clicked,
        replied,
        bounced,
        complained,
        openRate,
        clickRate,
        clickToOpenRate,
        replyRate,
        bounceRate,
        complaintRate
      };
    } catch (error) {
      logger.error(`Error getting performance metrics for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Extract variables from template
   */
  private extractVariablesFromTemplate(template: string): string[] {
    const variableRegex = /{{(.*?)}}/g;
    const variables: string[] = [];
    let match;

    while ((match = variableRegex.exec(template)) !== null) {
      const variableName = match[1].trim();
      if (!variables.includes(variableName)) {
        variables.push(variableName);
      }
    }

    return variables;
  }

  /**
   * Convert HTML to plain text
   */
  private htmlToPlainText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
      .trim();
  }
}

export default new EmailTemplateService();
