/**
 * MCP Initializer
 *
 * This module initializes the MCP Server and registers adapters
 * based on configuration.
 */

import { mcpServer, MCPSourceType, MCPAuthType } from '../mcp';
import {
  createGmailAdapter,
  createOutlookAdapter,
  createGoogleCalendarAdapter,
  createOffice365CalendarAdapter,
  createCalendlyApiKeyAdapter,
  createCalendlyOAuthAdapter,
  createTelephonyApiKeyAdapter,
  createTelephonyBasicAdapter,
  createSocialMediaApiKeyAdapter,
  createSocialMediaOAuthAdapter,
  TelephonyProviderType,
  SocialMediaProviderType
} from '../mcp/adapter-factory';
import { User } from '../models/mongoose';

/**
 * Initialize MCP Server
 */
export async function initializeMCPServer(): Promise<void> {
  try {
    console.log('Initializing MCP Server...');

    // Register adapters for users with OAuth credentials
    await registerUserAdapters();

    console.log(`MCP Server initialized with ${mcpServer.getAllAdapters().length} adapters`);
  } catch (error) {
    console.error('Error initializing MCP Server:', error);
  }
}

/**
 * Register adapters for users with OAuth credentials
 */
async function registerUserAdapters(): Promise<void> {
  try {
    // Find users with OAuth credentials
    const users = await User.find({
      'integrations.oauth': { $exists: true }
    });

    console.log(`Found ${users.length} users with OAuth credentials`);

    for (const user of users) {
      const userId = user._id.toString();

      // Register Gmail adapter if available
      if (user.integrations?.oauth?.google?.accessToken) {
        const email = user.integrations.oauth.google.email || user.email;

        try {
          const gmailAdapter = createGmailAdapter(
            `gmail-${userId}`,
            `Gmail (${email})`,
            email,
            user.integrations.oauth.google.accessToken,
            user.integrations.oauth.google.refreshToken
          );

          mcpServer.registerAdapter(gmailAdapter);
          console.log(`Registered Gmail adapter for user ${userId}`);

          // Register Google Calendar adapter if available
          const calendarAdapter = createGoogleCalendarAdapter(
            `google-calendar-${userId}`,
            `Google Calendar (${email})`,
            email,
            user.integrations.oauth.google.accessToken,
            user.integrations.oauth.google.refreshToken
          );

          mcpServer.registerAdapter(calendarAdapter);
          console.log(`Registered Google Calendar adapter for user ${userId}`);
        } catch (error) {
          console.error(`Error registering Google adapters for user ${userId}:`, error);
        }
      }

      // Register Outlook adapter if available
      if (user.integrations?.oauth?.microsoft?.accessToken) {
        const email = user.integrations.oauth.microsoft.email || user.email;

        try {
          const outlookAdapter = createOutlookAdapter(
            `outlook-${userId}`,
            `Outlook (${email})`,
            email,
            user.integrations.oauth.microsoft.accessToken,
            user.integrations.oauth.microsoft.refreshToken
          );

          mcpServer.registerAdapter(outlookAdapter);
          console.log(`Registered Outlook adapter for user ${userId}`);

          // Register Office 365 Calendar adapter if available
          const calendarAdapter = createOffice365CalendarAdapter(
            `office365-calendar-${userId}`,
            `Office 365 Calendar (${email})`,
            email,
            user.integrations.oauth.microsoft.accessToken,
            user.integrations.oauth.microsoft.refreshToken
          );

          mcpServer.registerAdapter(calendarAdapter);
          console.log(`Registered Office 365 Calendar adapter for user ${userId}`);
        } catch (error) {
          console.error(`Error registering Microsoft adapters for user ${userId}:`, error);
        }
      }

      // Register Calendly adapter if available
      if (user.integrations?.calendly?.apiKey) {
        try {
          const calendlyAdapter = createCalendlyApiKeyAdapter(
            `calendly-${userId}`,
            `Calendly (${user.email})`,
            user.integrations.calendly.apiKey,
            user.integrations.calendly.personalUrl,
            user.integrations.calendly.organizationUri
          );

          mcpServer.registerAdapter(calendlyAdapter);
          console.log(`Registered Calendly adapter for user ${userId}`);
        } catch (error) {
          console.error(`Error registering Calendly adapter for user ${userId}:`, error);
        }
      } else if (user.integrations?.oauth?.calendly?.accessToken) {
        try {
          const calendlyAdapter = createCalendlyOAuthAdapter(
            `calendly-${userId}`,
            `Calendly (${user.email})`,
            user.integrations.oauth.calendly.accessToken,
            user.integrations.oauth.calendly.refreshToken,
            user.integrations.oauth.calendly.personalUrl,
            user.integrations.oauth.calendly.organizationUri
          );

          mcpServer.registerAdapter(calendlyAdapter);
          console.log(`Registered Calendly OAuth adapter for user ${userId}`);
        } catch (error) {
          console.error(`Error registering Calendly OAuth adapter for user ${userId}:`, error);
        }
      }

      // Register Telephony adapters if available
      if (user.integrations?.telephony) {
        // Twilio adapter
        if (user.integrations.telephony.twilio?.accountSid && user.integrations.telephony.twilio?.authToken) {
          try {
            const twilioAdapter = createTelephonyBasicAdapter(
              `twilio-${userId}`,
              `Twilio (${user.email})`,
              TelephonyProviderType.TWILIO,
              user.integrations.telephony.twilio.accountSid,
              user.integrations.telephony.twilio.authToken,
              user.integrations.telephony.twilio.phoneNumber,
              undefined,
              {
                transcribeVoicemail: true,
                transcribeCalls: true,
                recordCalls: true
              }
            );

            mcpServer.registerAdapter(twilioAdapter);
            console.log(`Registered Twilio adapter for user ${userId}`);
          } catch (error) {
            console.error(`Error registering Twilio adapter for user ${userId}:`, error);
          }
        }

        // Vonage adapter
        if (user.integrations.telephony.vonage?.apiKey && user.integrations.telephony.vonage?.apiSecret) {
          try {
            const vonageAdapter = createTelephonyApiKeyAdapter(
              `vonage-${userId}`,
              `Vonage (${user.email})`,
              TelephonyProviderType.VONAGE,
              user.integrations.telephony.vonage.apiKey,
              user.integrations.telephony.vonage.phoneNumber
            );

            mcpServer.registerAdapter(vonageAdapter);
            console.log(`Registered Vonage adapter for user ${userId}`);
          } catch (error) {
            console.error(`Error registering Vonage adapter for user ${userId}:`, error);
          }
        }
      }

      // Register Social Media adapters if available
      if (user.integrations?.socialMedia) {
        // LinkedIn adapter
        if (user.integrations.oauth?.linkedin?.accessToken) {
          try {
            const linkedinAdapter = createSocialMediaOAuthAdapter(
              `linkedin-${userId}`,
              `LinkedIn (${user.email})`,
              SocialMediaProviderType.LINKEDIN,
              user.integrations.oauth.linkedin.accessToken,
              user.integrations.oauth.linkedin.refreshToken,
              user.integrations.socialMedia.linkedin?.username,
              user.integrations.socialMedia.linkedin?.profileId
            );

            mcpServer.registerAdapter(linkedinAdapter);
            console.log(`Registered LinkedIn adapter for user ${userId}`);
          } catch (error) {
            console.error(`Error registering LinkedIn adapter for user ${userId}:`, error);
          }
        }

        // Twitter adapter
        if (user.integrations.oauth?.twitter?.accessToken) {
          try {
            const twitterAdapter = createSocialMediaOAuthAdapter(
              `twitter-${userId}`,
              `Twitter (${user.email})`,
              SocialMediaProviderType.TWITTER,
              user.integrations.oauth.twitter.accessToken,
              user.integrations.oauth.twitter.refreshToken,
              user.integrations.socialMedia.twitter?.username
            );

            mcpServer.registerAdapter(twitterAdapter);
            console.log(`Registered Twitter adapter for user ${userId}`);
          } catch (error) {
            console.error(`Error registering Twitter adapter for user ${userId}:`, error);
          }
        }

        // Facebook adapter
        if (user.integrations.oauth?.facebook?.accessToken) {
          try {
            const facebookAdapter = createSocialMediaOAuthAdapter(
              `facebook-${userId}`,
              `Facebook (${user.email})`,
              SocialMediaProviderType.FACEBOOK,
              user.integrations.oauth.facebook.accessToken,
              user.integrations.oauth.facebook.refreshToken,
              undefined,
              user.integrations.socialMedia.facebook?.pageId
            );

            mcpServer.registerAdapter(facebookAdapter);
            console.log(`Registered Facebook adapter for user ${userId}`);
          } catch (error) {
            console.error(`Error registering Facebook adapter for user ${userId}:`, error);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error registering user adapters:', error);
  }
}

/**
 * Shutdown MCP Server
 */
export async function shutdownMCPServer(): Promise<void> {
  try {
    console.log('Shutting down MCP Server...');

    // Disconnect all adapters
    const adapters = mcpServer.getAllAdapters();

    for (const adapter of adapters) {
      try {
        await adapter.disconnect();
        mcpServer.removeAdapter(adapter.id);
      } catch (error) {
        console.error(`Error disconnecting adapter ${adapter.id}:`, error);
      }
    }

    console.log('MCP Server shut down successfully');
  } catch (error) {
    console.error('Error shutting down MCP Server:', error);
  }
}
