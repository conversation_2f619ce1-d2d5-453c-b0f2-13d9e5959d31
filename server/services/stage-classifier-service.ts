import { Opportunity, Activity, Contact, Company, IOpportunity } from '../models/mongoose';
import mongoose from 'mongoose';
import { VectorEmbeddingService } from './vector-embedding-service';
import axios from 'axios';

// Initialize Voyage AI client for text generation
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY;
const VOYAGE_API_URL = 'https://api.voyageai.com/v1/chat/completions';

// Define the stage classification result
export interface StageClassificationResult {
  currentStage: string;
  recommendedStage: string;
  confidence: number; // 0-1 scale
  explanation: string;
  keyIndicators: string[];
  requiresReview: boolean;
}

// Define the stage transition
export interface StageTransition {
  opportunityId: string;
  previousStage: string;
  newStage: string;
  confidence: number;
  explanation: string;
  timestamp: Date;
  automatic: boolean;
}

/**
 * Service for classifying opportunity stages based on communications and activities
 */
export class StageClassifierService {
  private static instance: StageClassifierService;
  private vectorService: VectorEmbeddingService;

  // Define the pipeline stages in order
  private pipelineStages: string[] = [
    'prospecting',
    'qualification',
    'needs_analysis',
    'value_proposition',
    'decision_makers',
    'proposal',
    'negotiation',
    'closed_won',
    'closed_lost'
  ];

  private constructor() {
    // Initialize vector embedding service
    this.vectorService = VectorEmbeddingService.getInstance();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): StageClassifierService {
    if (!StageClassifierService.instance) {
      StageClassifierService.instance = new StageClassifierService();
    }
    return StageClassifierService.instance;
  }

  /**
   * Classify the stage of an opportunity
   */
  public async classifyOpportunityStage(opportunityId: string): Promise<StageClassificationResult> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Get opportunity details
      const opportunity = await Opportunity.findById(opportunityObjectId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      // Get related data
      const [contact, company, activities] = await Promise.all([
        opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
        opportunity.companyId ? Company.findById(opportunity.companyId) : null,
        Activity.find({ opportunityId: opportunityObjectId })
          .sort({ date: -1 })
          .limit(30)
      ]);

      // Analyze the opportunity and determine the appropriate stage
      const stageClassification = await this.analyzeOpportunityStage(
        opportunity,
        contact,
        company,
        activities
      );

      return stageClassification;
    } catch (error) {
      console.error('Error classifying opportunity stage:', error);
      throw error;
    }
  }

  /**
   * Update the stage of an opportunity if needed
   */
  public async updateOpportunityStageIfNeeded(opportunityId: string, autoUpdate: boolean = false): Promise<StageTransition | null> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Get opportunity details
      const opportunity = await Opportunity.findById(opportunityObjectId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      // Classify the opportunity stage
      const classification = await this.classifyOpportunityStage(opportunityId);

      // If the recommended stage is different from the current stage
      if (classification.recommendedStage !== opportunity.stage) {
        // Create a stage transition record
        const transition: StageTransition = {
          opportunityId,
          previousStage: opportunity.stage,
          newStage: classification.recommendedStage,
          confidence: classification.confidence,
          explanation: classification.explanation,
          timestamp: new Date(),
          automatic: autoUpdate
        };

        // If auto-update is enabled and confidence is high enough, update the stage
        if (autoUpdate && classification.confidence >= 0.8 && !classification.requiresReview) {
          // Update the opportunity stage
          opportunity.stage = classification.recommendedStage;
          await opportunity.save();
        }

        return transition;
      }

      return null;
    } catch (error) {
      console.error('Error updating opportunity stage:', error);
      throw error;
    }
  }

  /**
   * Analyze an opportunity and determine the appropriate stage
   */
  private async analyzeOpportunityStage(
    opportunity: IOpportunity,
    contact: any,
    company: any,
    activities: any[]
  ): Promise<StageClassificationResult> {
    try {
      // Prepare data for OpenAI
      const data = {
        opportunity: {
          id: opportunity._id.toString(),
          name: opportunity.name,
          value: opportunity.value,
          currency: opportunity.currency,
          stage: opportunity.stage,
          probability: opportunity.probability,
          expectedCloseDate: opportunity.expectedCloseDate,
          description: opportunity.description,
          tags: opportunity.tags,
          createdAt: opportunity.createdAt,
          updatedAt: opportunity.updatedAt
        },
        contact: contact ? {
          id: contact._id.toString(),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title
        } : null,
        company: company ? {
          id: company._id.toString(),
          name: company.name,
          industry: company.industry,
          size: company.size
        } : null,
        activities: activities.map(activity => ({
          id: activity._id.toString(),
          type: activity.type,
          title: activity.title,
          date: activity.date,
          completed: activity.completed,
          notes: activity.notes,
          description: activity.description
        })),
        pipelineStages: this.pipelineStages
      };

      // Create prompt for OpenAI
      const prompt = `
        Analyze this opportunity and determine the most appropriate sales pipeline stage.

        The current stage is "${opportunity.stage}".

        Based on the opportunity details, contact, company, and especially the recent activities,
        determine if the opportunity should remain in the current stage, move forward, or move backward.

        Consider the following stage definitions:

        - prospecting: Initial contact with potential customer, identifying if they are a good fit
        - qualification: Determining if the prospect has a need, budget, and authority to purchase
        - needs_analysis: Detailed discovery of customer requirements and pain points
        - value_proposition: Presenting how your solution addresses the customer's specific needs
        - decision_makers: Identifying and engaging with all stakeholders involved in the purchase decision
        - proposal: Formal proposal or quote has been presented to the customer
        - negotiation: Discussing terms, pricing, and addressing objections
        - closed_won: Deal has been successfully closed with a signed contract
        - closed_lost: Deal has been lost to a competitor or the customer decided not to proceed

        Look for key indicators in the activities such as:
        - Meetings with decision makers
        - Discussions about budget
        - Requests for proposals or pricing
        - Objections or concerns raised
        - Expressions of interest or commitment
        - Mentions of competitors
        - Scheduling of demos or presentations
        - Requests for references
        - Contract discussions

        Format your response as a JSON object with the following structure:
        {
          "currentStage": "current_stage_name",
          "recommendedStage": "recommended_stage_name",
          "confidence": 0.85, // 0-1 scale indicating confidence in the recommendation
          "explanation": "Clear explanation of why this stage is recommended",
          "keyIndicators": ["indicator 1", "indicator 2", ...], // Key pieces of evidence supporting the recommendation
          "requiresReview": false // true if human review is recommended before changing the stage
        }
      `;

      // Check if Voyage API key is configured
      if (!VOYAGE_API_KEY) {
        throw new Error('VOYAGE_API_KEY is not configured');
      }

      // Call Voyage AI API
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales process analyst for Aizako CRM.
              Your job is to analyze opportunity data and determine the appropriate sales pipeline stage.
              Be objective and data-driven in your analysis.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.3, // Lower temperature for more consistent results
          max_tokens: 1000,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      // Parse the response
      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content in Voyage AI response');
      }

      const parsedResponse = JSON.parse(content);

      // Validate the response structure
      if (!parsedResponse.currentStage || !parsedResponse.recommendedStage ||
          typeof parsedResponse.confidence !== 'number' || !parsedResponse.explanation) {
        throw new Error('Invalid response structure from Voyage AI');
      }

      // Ensure confidence is within range
      const confidence = Math.min(Math.max(parsedResponse.confidence, 0), 1);

      // Return the stage classification
      return {
        currentStage: parsedResponse.currentStage,
        recommendedStage: parsedResponse.recommendedStage,
        confidence,
        explanation: parsedResponse.explanation,
        keyIndicators: parsedResponse.keyIndicators || [],
        requiresReview: parsedResponse.requiresReview || false
      };
    } catch (error) {
      console.error('Error analyzing opportunity stage:', error);

      // Return a fallback classification if analysis fails
      return {
        currentStage: opportunity.stage,
        recommendedStage: opportunity.stage,
        confidence: 0,
        explanation: 'Unable to analyze stage due to an error.',
        keyIndicators: [],
        requiresReview: true
      };
    }
  }

  /**
   * Check all opportunities for potential stage updates
   */
  public async checkAllOpportunities(autoUpdate: boolean = false): Promise<StageTransition[]> {
    try {
      // Get all active opportunities (not closed)
      const opportunities = await Opportunity.find({
        stage: { $nin: ['closed_won', 'closed_lost'] }
      });

      console.log(`Checking ${opportunities.length} active opportunities for stage updates`);

      const transitions: StageTransition[] = [];

      // Check each opportunity
      for (const opportunity of opportunities) {
        try {
          const transition = await this.updateOpportunityStageIfNeeded(
            opportunity._id.toString(),
            autoUpdate
          );

          if (transition) {
            transitions.push(transition);
          }
        } catch (error) {
          console.error(`Error checking opportunity ${opportunity._id}:`, error);
        }
      }

      return transitions;
    } catch (error) {
      console.error('Error checking all opportunities:', error);
      return [];
    }
  }
}

export default StageClassifierService;
