/**
 * Smart Interaction Timeline Service
 * 
 * This service manages the Smart Interaction Timeline for contacts,
 * aggregating interactions from various sources and providing AI-powered
 * summaries and next action suggestions.
 */

import mongoose from 'mongoose';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { 
  Contact, 
  IContact, 
  IInteraction 
} from '../models/mongoose';
import { mcpServer, MCPSourceType } from '../mcp';
import { EmailAdapter } from '../mcp/adapters/email-adapter';
import { CalendarAdapter } from '../mcp/adapters/calendar-adapter';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

/**
 * Smart Interaction Timeline Service
 */
export class InteractionTimelineService {
  /**
   * Get interactions for a contact
   */
  async getInteractions(
    contactId: string,
    options: {
      limit?: number;
      skip?: number;
      startDate?: Date;
      endDate?: Date;
      types?: string[];
      sources?: string[];
      sentiment?: 'positive' | 'neutral' | 'negative';
      search?: string;
    } = {}
  ): Promise<IInteraction[]> {
    try {
      const contact = await Contact.findById(contactId);
      
      if (!contact || !contact.interactions) {
        return [];
      }
      
      let interactions = [...contact.interactions];
      
      // Apply filters
      if (options.startDate) {
        interactions = interactions.filter(i => 
          i.timestamp >= options.startDate!
        );
      }
      
      if (options.endDate) {
        interactions = interactions.filter(i => 
          i.timestamp <= options.endDate!
        );
      }
      
      if (options.types && options.types.length > 0) {
        interactions = interactions.filter(i => 
          options.types!.includes(i.type)
        );
      }
      
      if (options.sources && options.sources.length > 0) {
        interactions = interactions.filter(i => 
          options.sources!.includes(i.source)
        );
      }
      
      if (options.sentiment) {
        interactions = interactions.filter(i => 
          i.sentiment === options.sentiment
        );
      }
      
      if (options.search) {
        const searchLower = options.search.toLowerCase();
        interactions = interactions.filter(i => 
          i.summary.toLowerCase().includes(searchLower) ||
          i.content?.text?.toLowerCase().includes(searchLower) ||
          i.content?.html?.toLowerCase().includes(searchLower)
        );
      }
      
      // Sort by timestamp (newest first)
      interactions.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
      
      // Apply pagination
      if (options.skip) {
        interactions = interactions.slice(options.skip);
      }
      
      if (options.limit) {
        interactions = interactions.slice(0, options.limit);
      }
      
      return interactions;
    } catch (error) {
      console.error('Error getting interactions:', error);
      throw error;
    }
  }

  /**
   * Add an interaction to a contact's timeline
   */
  async addInteraction(
    contactId: string,
    interaction: Omit<IInteraction, 'id'>
  ): Promise<IInteraction> {
    try {
      const contact = await Contact.findById(contactId);
      
      if (!contact) {
        throw new Error(`Contact not found: ${contactId}`);
      }
      
      // Generate a unique ID for the interaction
      const newInteraction: IInteraction = {
        ...interaction,
        id: uuidv4()
      };
      
      // Initialize interactions array if it doesn't exist
      if (!contact.interactions) {
        contact.interactions = [];
      }
      
      // Add the interaction
      contact.interactions.push(newInteraction);
      
      // Update lastContactedAt if this is a newer interaction
      if (!contact.lastContactedAt || 
          new Date(interaction.timestamp) > new Date(contact.lastContactedAt)) {
        contact.lastContactedAt = new Date(interaction.timestamp);
      }
      
      // Save the contact
      await contact.save();
      
      return newInteraction;
    } catch (error) {
      console.error('Error adding interaction:', error);
      throw error;
    }
  }

  /**
   * Update an interaction in a contact's timeline
   */
  async updateInteraction(
    contactId: string,
    interactionId: string,
    updates: Partial<IInteraction>
  ): Promise<IInteraction | null> {
    try {
      const contact = await Contact.findById(contactId);
      
      if (!contact || !contact.interactions) {
        return null;
      }
      
      // Find the interaction
      const interactionIndex = contact.interactions.findIndex(i => i.id === interactionId);
      
      if (interactionIndex === -1) {
        return null;
      }
      
      // Update the interaction
      contact.interactions[interactionIndex] = {
        ...contact.interactions[interactionIndex],
        ...updates
      };
      
      // Save the contact
      await contact.save();
      
      return contact.interactions[interactionIndex];
    } catch (error) {
      console.error('Error updating interaction:', error);
      throw error;
    }
  }

  /**
   * Delete an interaction from a contact's timeline
   */
  async deleteInteraction(
    contactId: string,
    interactionId: string
  ): Promise<boolean> {
    try {
      const contact = await Contact.findById(contactId);
      
      if (!contact || !contact.interactions) {
        return false;
      }
      
      // Find the interaction
      const interactionIndex = contact.interactions.findIndex(i => i.id === interactionId);
      
      if (interactionIndex === -1) {
        return false;
      }
      
      // Remove the interaction
      contact.interactions.splice(interactionIndex, 1);
      
      // Save the contact
      await contact.save();
      
      return true;
    } catch (error) {
      console.error('Error deleting interaction:', error);
      throw error;
    }
  }

  /**
   * Sync interactions from MCP sources
   */
  async syncInteractions(
    contactId: string,
    options: {
      sources?: MCPSourceType[];
      startDate?: Date;
      endDate?: Date;
      limit?: number;
    } = {}
  ): Promise<{
    added: number;
    updated: number;
    errors: number;
  }> {
    try {
      const contact = await Contact.findById(contactId);
      
      if (!contact) {
        throw new Error(`Contact not found: ${contactId}`);
      }
      
      // Initialize interactions array if it doesn't exist
      if (!contact.interactions) {
        contact.interactions = [];
      }
      
      const result = {
        added: 0,
        updated: 0,
        errors: 0
      };
      
      // Get email adapters
      const emailAdapters = options.sources?.includes(MCPSourceType.EMAIL) !== false
        ? mcpServer.getAdaptersByType(MCPSourceType.EMAIL) as EmailAdapter[]
        : [];
      
      // Get calendar adapters
      const calendarAdapters = options.sources?.includes(MCPSourceType.CALENDAR) !== false
        ? mcpServer.getAdaptersByType(MCPSourceType.CALENDAR) as CalendarAdapter[]
        : [];
      
      // Sync email interactions
      for (const adapter of emailAdapters) {
        try {
          // Skip if adapter is not connected
          if (!adapter.isConnected()) {
            await adapter.connect();
            if (!adapter.isConnected()) {
              continue;
            }
          }
          
          // Get messages related to this contact
          const response = await adapter.getMessages({
            query: contact.email,
            after: options.startDate?.toISOString(),
            before: options.endDate?.toISOString(),
            limit: options.limit
          });
          
          if (!response.success || !response.data) {
            result.errors++;
            continue;
          }
          
          // Process each message
          for (const message of response.data) {
            // Check if this message is already in the timeline
            const existingIndex = contact.interactions.findIndex(i => 
              i.source === adapter.id && i.sourceId === message.id
            );
            
            // Generate AI summary for the message
            const summary = await this.generateSummary(
              message.subject,
              message.body.text || message.body.html || ''
            );
            
            // Determine sentiment
            const sentiment = await this.analyzeSentiment(
              message.subject,
              message.body.text || message.body.html || ''
            );
            
            // Determine direction
            const direction = message.from.email === contact.email
              ? 'outbound'
              : 'inbound';
            
            // Create interaction object
            const interaction: Omit<IInteraction, 'id'> = {
              type: 'email',
              source: adapter.id,
              sourceId: message.id,
              timestamp: new Date(message.date),
              summary: summary || `Email: ${message.subject}`,
              sentiment,
              direction,
              participants: [
                {
                  email: message.from.email,
                  name: message.from.name
                },
                ...(message.to.map(to => ({
                  email: to.email,
                  name: to.name
                })))
              ],
              content: {
                text: message.body.text,
                html: message.body.html,
                attachments: message.attachments?.map(a => ({
                  name: a.name,
                  type: a.contentType,
                  url: a.url
                }))
              },
              aiGenerated: !!summary,
              aiConfidence: 0.8
            };
            
            if (existingIndex === -1) {
              // Add new interaction
              contact.interactions.push({
                ...interaction,
                id: uuidv4()
              });
              result.added++;
            } else {
              // Update existing interaction
              contact.interactions[existingIndex] = {
                ...contact.interactions[existingIndex],
                ...interaction,
                id: contact.interactions[existingIndex].id
              };
              result.updated++;
            }
          }
        } catch (error) {
          console.error(`Error syncing email interactions from ${adapter.id}:`, error);
          result.errors++;
        }
      }
      
      // Save the contact
      await contact.save();
      
      return result;
    } catch (error) {
      console.error('Error syncing interactions:', error);
      throw error;
    }
  }

  /**
   * Generate a summary for an interaction
   */
  private async generateSummary(
    subject: string,
    content: string
  ): Promise<string | null> {
    try {
      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/general`, {
          query: `Summarize this email in one sentence: Subject: ${subject}\n\nContent: ${content}`,
          userId: 1 // System user
        }, {
          timeout: 5000 // 5 second timeout
        });

        if (aiResponse.data && aiResponse.data.response) {
          return aiResponse.data.response;
        }
      } catch (aiError) {
        console.error('Error using AI service for summary, falling back to Voyage AI:', aiError);
      }
      
      // Fallback to Voyage AI
      if (VOYAGE_API_KEY) {
        const response = await axios.post(
          VOYAGE_API_URL,
          {
            model: 'voyage-2',
            messages: [
              {
                role: 'system',
                content: 'You are an AI assistant that summarizes emails in one concise sentence.'
              },
              {
                role: 'user',
                content: `Summarize this email in one sentence:\n\nSubject: ${subject}\n\nContent: ${content}`
              }
            ],
            temperature: 0.3,
            max_tokens: 100
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${VOYAGE_API_KEY}`
            }
          }
        );
        
        if (response.data && response.data.choices && response.data.choices[0]) {
          return response.data.choices[0].message.content.trim();
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error generating summary:', error);
      return null;
    }
  }

  /**
   * Analyze sentiment for an interaction
   */
  private async analyzeSentiment(
    subject: string,
    content: string
  ): Promise<'positive' | 'neutral' | 'negative' | undefined> {
    try {
      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/general`, {
          query: `Analyze the sentiment of this email and respond with only one word: positive, neutral, or negative.\n\nSubject: ${subject}\n\nContent: ${content}`,
          userId: 1 // System user
        }, {
          timeout: 5000 // 5 second timeout
        });

        if (aiResponse.data && aiResponse.data.response) {
          const sentiment = aiResponse.data.response.trim().toLowerCase();
          if (['positive', 'neutral', 'negative'].includes(sentiment)) {
            return sentiment as 'positive' | 'neutral' | 'negative';
          }
        }
      } catch (aiError) {
        console.error('Error using AI service for sentiment, falling back to Voyage AI:', aiError);
      }
      
      // Fallback to Voyage AI
      if (VOYAGE_API_KEY) {
        const response = await axios.post(
          VOYAGE_API_URL,
          {
            model: 'voyage-2',
            messages: [
              {
                role: 'system',
                content: 'You are an AI assistant that analyzes sentiment. Respond with only one word: positive, neutral, or negative.'
              },
              {
                role: 'user',
                content: `Analyze the sentiment of this email:\n\nSubject: ${subject}\n\nContent: ${content}`
              }
            ],
            temperature: 0.3,
            max_tokens: 10
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${VOYAGE_API_KEY}`
            }
          }
        );
        
        if (response.data && response.data.choices && response.data.choices[0]) {
          const sentiment = response.data.choices[0].message.content.trim().toLowerCase();
          if (['positive', 'neutral', 'negative'].includes(sentiment)) {
            return sentiment as 'positive' | 'neutral' | 'negative';
          }
        }
      }
      
      return undefined;
    } catch (error) {
      console.error('Error analyzing sentiment:', error);
      return undefined;
    }
  }
}

// Export a singleton instance
export const interactionTimelineService = new InteractionTimelineService();
