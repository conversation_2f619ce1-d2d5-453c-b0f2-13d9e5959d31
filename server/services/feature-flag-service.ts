import { FeatureFlag, IFeatureFlag, mapFeatureFlagToType } from '../models/mongoose/feature-flag-model';
import mongoose from 'mongoose';
import { Request } from 'express';
import {
  FeatureFlagContext,
  FeatureFlag as FeatureFlagType,
  CreateFeatureFlagRequest,
  UpdateFeatureFlagRequest
} from '@types/feature-flags';
import { isFeatureFlagRule } from '@types/guards/feature-flags';
import { toObjectId, toObjectIds } from '../utils/mongodb-utils';

export class FeatureFlagService {
  /**
   * Check if a feature flag is enabled for a given context
   */
  static async isEnabled(key: string, context: FeatureFlagContext = {}): Promise<boolean> {
    try {
      // Get the feature flag
      const featureFlag = await FeatureFlag.findOne({ key });

      // If the feature flag doesn't exist, it's disabled
      if (!featureFlag) {
        return false;
      }

      // If the feature flag is globally enabled, it's enabled for everyone
      if (featureFlag.enabled) {
        return true;
      }

      // Check if the feature flag is enabled for the user
      if (context.userId && featureFlag.enabledForUsers.some(id =>
        id.toString() === context.userId
      )) {
        return true;
      }

      // Check if the feature flag is enabled for the tenant
      if (context.tenantId && featureFlag.enabledForTenants.some(id =>
        id.toString() === context.tenantId
      )) {
        return true;
      }

      // Check if the feature flag is enabled based on percentage rollout
      if (featureFlag.enabledForPercentage > 0) {
        const userId = context.userId || '';
        const hash = this.hashString(`${key}:${userId}`);
        const percentage = hash % 100;

        if (percentage < featureFlag.enabledForPercentage) {
          return true;
        }
      }

      // Check if the feature flag is enabled based on rules
      if (featureFlag.rules && featureFlag.rules.length > 0) {
        for (const rule of featureFlag.rules) {
          if (this.evaluateRule(rule, context)) {
            return true;
          }
        }
      }

      // If none of the above conditions are met, the feature flag is disabled
      return false;
    } catch (error) {
      console.error(`Error checking feature flag ${key}:`, error);
      return false;
    }
  }

  /**
   * Get all feature flags
   */
  static async getAllFlags(options: {
    enabled?: boolean;
    tags?: string[];
    limit?: number;
    offset?: number;
  } = {}): Promise<FeatureFlagType[]> {
    const { enabled, tags, limit = 100, offset = 0 } = options;

    const query: any = {};

    if (enabled !== undefined) {
      query.enabled = enabled;
    }

    if (tags && tags.length > 0) {
      query.tags = { $in: tags };
    }

    const flags = await FeatureFlag.find(query)
      .sort({ key: 1 })
      .skip(offset)
      .limit(limit);

    return flags.map(mapFeatureFlagToType);
  }

  /**
   * Create a new feature flag
   */
  static async createFlag(data: CreateFeatureFlagRequest & { createdBy: string }): Promise<FeatureFlagType> {
    const {
      key,
      name,
      description,
      enabled = false,
      enabledForUsers = [],
      enabledForTenants = [],
      enabledForPercentage = 0,
      rules = [],
      tags = [],
      createdBy,
    } = data;

    // Validate rules
    if (rules.length > 0 && !rules.every(isFeatureFlagRule)) {
      throw new Error('Invalid feature flag rules');
    }

    // Convert string IDs to ObjectIds
    const userIds = enabledForUsers.map(id => toObjectId(id));
    const tenantIds = enabledForTenants.map(id => toObjectId(id));

    const featureFlag = new FeatureFlag({
      key,
      name,
      description,
      enabled,
      enabledForUsers: userIds,
      enabledForTenants: tenantIds,
      enabledForPercentage,
      rules,
      tags,
      createdBy: toObjectId(createdBy),
    });

    const savedFlag = await featureFlag.save();
    return mapFeatureFlagToType(savedFlag);
  }

  /**
   * Update a feature flag
   */
  static async updateFlag(key: string, data: UpdateFeatureFlagRequest): Promise<FeatureFlagType | null> {
    const updateData: any = { ...data };

    // Validate rules if provided
    if (data.rules && data.rules.length > 0 && !data.rules.every(isFeatureFlagRule)) {
      throw new Error('Invalid feature flag rules');
    }

    // Convert string IDs to ObjectIds if provided
    if (data.enabledForUsers) {
      updateData.enabledForUsers = data.enabledForUsers.map(id => toObjectId(id));
    }

    if (data.enabledForTenants) {
      updateData.enabledForTenants = data.enabledForTenants.map(id => toObjectId(id));
    }

    const updatedFlag = await FeatureFlag.findOneAndUpdate(
      { key },
      { $set: updateData },
      { new: true }
    );

    return updatedFlag ? mapFeatureFlagToType(updatedFlag) : null;
  }

  /**
   * Delete a feature flag
   */
  static async deleteFlag(key: string): Promise<boolean> {
    const result = await FeatureFlag.deleteOne({ key });
    return result.deletedCount === 1;
  }

  /**
   * Get feature flag context from request
   */
  static getContextFromRequest(req: Request): FeatureFlagContext {
    return {
      userId: req.session?.userId,
      tenantId: req.session?.tenantId,
      location: {
        country: req.headers['cf-ipcountry'] as string,
        region: req.headers['cf-region'] as string,
      },
      custom: {
        userAgent: req.headers['user-agent'],
        ip: req.ip,
      },
    };
  }

  /**
   * Evaluate a rule against a context
   */
  private static evaluateRule(rule: { type: string; value: any }, context: FeatureFlagContext): boolean {
    switch (rule.type) {
      case 'user':
        return context.userId === rule.value;

      case 'tenant':
        return context.tenantId === rule.value;

      case 'date':
        const now = new Date();
        const startDate = new Date(rule.value.startDate);
        const endDate = new Date(rule.value.endDate);
        return now >= startDate && now <= endDate;

      case 'time':
        const currentHour = new Date().getHours();
        return currentHour >= rule.value.startHour && currentHour <= rule.value.endHour;

      case 'location':
        if (rule.value.country && context.location?.country) {
          return rule.value.country === context.location.country;
        }
        if (rule.value.region && context.location?.region) {
          return rule.value.region === context.location.region;
        }
        return false;

      case 'custom':
        if (!context.custom || !rule.value.key) {
          return false;
        }

        const customValue = context.custom[rule.value.key];
        return customValue === rule.value.value;

      default:
        return false;
    }
  }

  /**
   * Simple hash function for consistent percentage rollouts
   */
  private static hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }
}
