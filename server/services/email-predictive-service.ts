/**
 * Email Predictive Service
 * 
 * This service provides predictive analytics and AI-powered recommendations
 * for email campaigns based on historical performance data.
 */

import mongoose from 'mongoose';
import { 
  EmailTracking, 
  EmailTemplate,
  Activity,
  Contact,
  Sequence,
  SequenceStep
} from '../models/mongoose';
import { logger } from '../utils/logger';

interface PredictionResult {
  score: number;
  confidence: number;
  factors: {
    factor: string;
    impact: number;
    description: string;
  }[];
}

interface RecommendationItem {
  type: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  action?: string;
}

interface ContactPrediction {
  contactId: string;
  contactName: string;
  contactEmail: string;
  openProbability: number;
  clickProbability: number;
  replyProbability: number;
  bestTimeToSend: {
    dayOfWeek: number;
    hourOfDay: number;
    confidence: number;
  };
  bestTemplateId?: string;
  bestTemplateName?: string;
}

class EmailPredictiveService {
  /**
   * Predict email open probability for a specific contact
   */
  async predictOpenProbability(
    tenantId: string,
    contactId: string,
    templateId?: string
  ): Promise<PredictionResult> {
    try {
      // Get contact's email history
      const emailHistory = await EmailTracking.find({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        contactId: new mongoose.Types.ObjectId(contactId)
      }).sort({ createdAt: -1 }).limit(20);

      if (emailHistory.length === 0) {
        return this.getDefaultPrediction('No email history available');
      }

      // Calculate open rate from history
      const openCount = emailHistory.filter(email => 
        email.status === 'OPENED' || email.status === 'CLICKED' || email.status === 'REPLIED'
      ).length;
      
      const openRate = openCount / emailHistory.length;
      
      // Get template data if provided
      let templateFactor = 0;
      let templateDescription = '';
      
      if (templateId) {
        const template = await EmailTemplate.findById(templateId);
        if (template && template.stats) {
          const templateOpenRate = template.stats.opened / template.stats.sent;
          templateFactor = templateOpenRate - 0.25; // Compare to baseline
          templateDescription = templateFactor > 0 
            ? `Template "${template.name}" performs above average`
            : `Template "${template.name}" performs below average`;
        }
      }
      
      // Get contact engagement level
      const activities = await Activity.find({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        contactId: new mongoose.Types.ObjectId(contactId)
      }).sort({ createdAt: -1 }).limit(50);
      
      const engagementLevel = activities.length / 50; // Normalize to 0-1
      
      // Calculate time-based factors
      const now = new Date();
      const dayOfWeek = now.getDay();
      const hourOfDay = now.getHours();
      
      // Check if this is a good time to send based on historical data
      const timeBasedOpenRate = await this.getTimeBasedOpenRate(
        tenantId, 
        dayOfWeek, 
        hourOfDay
      );
      
      // Calculate final prediction
      const baseScore = openRate * 0.5 + engagementLevel * 0.3 + timeBasedOpenRate * 0.2;
      let finalScore = baseScore + templateFactor * 0.2;
      
      // Ensure score is between 0 and 1
      finalScore = Math.max(0, Math.min(1, finalScore));
      
      // Calculate confidence based on amount of data
      const confidence = Math.min(0.9, 0.3 + (emailHistory.length / 30) * 0.6);
      
      // Prepare factors
      const factors = [
        {
          factor: 'Historical Open Rate',
          impact: openRate * 0.5,
          description: `Contact has opened ${openCount} of ${emailHistory.length} emails`
        },
        {
          factor: 'Engagement Level',
          impact: engagementLevel * 0.3,
          description: `Contact has ${activities.length} recent activities`
        },
        {
          factor: 'Time of Day',
          impact: timeBasedOpenRate * 0.2,
          description: `${this.getDayName(dayOfWeek)} at ${hourOfDay}:00 has ${
            timeBasedOpenRate > 0.5 ? 'good' : 'average'
          } open rates`
        }
      ];
      
      if (templateFactor !== 0) {
        factors.push({
          factor: 'Template Performance',
          impact: templateFactor * 0.2,
          description: templateDescription
        });
      }
      
      return {
        score: finalScore,
        confidence,
        factors: factors.sort((a, b) => Math.abs(b.impact) - Math.abs(a.impact))
      };
    } catch (error) {
      logger.error(`Error predicting open probability for contact ${contactId}:`, error);
      return this.getDefaultPrediction('Error calculating prediction');
    }
  }

  /**
   * Get time-based open rate
   */
  private async getTimeBasedOpenRate(
    tenantId: string,
    dayOfWeek: number,
    hourOfDay: number
  ): Promise<number> {
    try {
      // Get emails sent at similar time
      const timeWindowEmails = await EmailTracking.find({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        'events.type': 'SENT',
        'events.timestamp': {
          $exists: true
        }
      }).limit(1000);
      
      if (timeWindowEmails.length === 0) {
        return 0.5; // Default if no data
      }
      
      // Filter emails sent on similar day and hour
      const relevantEmails = timeWindowEmails.filter(email => {
        const sentEvent = email.events.find(event => event.type === 'SENT');
        if (!sentEvent || !sentEvent.timestamp) return false;
        
        const sentDate = new Date(sentEvent.timestamp);
        return sentDate.getDay() === dayOfWeek && 
               Math.abs(sentDate.getHours() - hourOfDay) <= 1;
      });
      
      if (relevantEmails.length < 10) {
        return 0.5; // Not enough data for reliable prediction
      }
      
      // Calculate open rate for this time window
      const openCount = relevantEmails.filter(email => 
        email.status === 'OPENED' || email.status === 'CLICKED' || email.status === 'REPLIED'
      ).length;
      
      return openCount / relevantEmails.length;
    } catch (error) {
      logger.error('Error calculating time-based open rate:', error);
      return 0.5; // Default on error
    }
  }

  /**
   * Get default prediction when data is insufficient
   */
  private getDefaultPrediction(reason: string): PredictionResult {
    return {
      score: 0.5, // Neutral prediction
      confidence: 0.3, // Low confidence
      factors: [
        {
          factor: 'Insufficient Data',
          impact: 0.5,
          description: reason
        }
      ]
    };
  }

  /**
   * Get day name from day of week number
   */
  private getDayName(dayOfWeek: number): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek];
  }

  /**
   * Get best time to send emails based on historical data
   */
  async getBestTimeToSend(tenantId: string): Promise<{
    dayOfWeek: number;
    hourOfDay: number;
    openRate: number;
    confidence: number;
  }> {
    try {
      // Get all emails with sent and open events
      const emails = await EmailTracking.find({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        'events.type': 'SENT',
        status: { $in: ['OPENED', 'CLICKED', 'REPLIED'] }
      }).limit(5000);
      
      if (emails.length < 100) {
        // Not enough data for reliable prediction
        return {
          dayOfWeek: 2, // Tuesday
          hourOfDay: 10, // 10 AM
          openRate: 0.5,
          confidence: 0.3
        };
      }
      
      // Initialize time slots
      const timeSlots: Record<string, { sent: number; opened: number }> = {};
      
      for (let day = 0; day < 7; day++) {
        for (let hour = 0; hour < 24; hour++) {
          timeSlots[`${day}-${hour}`] = { sent: 0, opened: 0 };
        }
      }
      
      // Analyze emails
      emails.forEach(email => {
        const sentEvent = email.events.find(event => event.type === 'SENT');
        if (!sentEvent || !sentEvent.timestamp) return;
        
        const sentDate = new Date(sentEvent.timestamp);
        const day = sentDate.getDay();
        const hour = sentDate.getHours();
        const key = `${day}-${hour}`;
        
        timeSlots[key].sent++;
        
        if (email.status === 'OPENED' || email.status === 'CLICKED' || email.status === 'REPLIED') {
          timeSlots[key].opened++;
        }
      });
      
      // Find best time slot
      let bestKey = '2-10'; // Default: Tuesday 10 AM
      let bestOpenRate = 0;
      
      Object.entries(timeSlots).forEach(([key, data]) => {
        if (data.sent < 10) return; // Ignore slots with too few emails
        
        const openRate = data.opened / data.sent;
        if (openRate > bestOpenRate) {
          bestOpenRate = openRate;
          bestKey = key;
        }
      });
      
      const [day, hour] = bestKey.split('-').map(Number);
      
      // Calculate confidence based on amount of data
      const confidence = Math.min(0.9, 0.3 + (timeSlots[bestKey].sent / 100) * 0.6);
      
      return {
        dayOfWeek: day,
        hourOfDay: hour,
        openRate: bestOpenRate,
        confidence
      };
    } catch (error) {
      logger.error('Error calculating best time to send:', error);
      return {
        dayOfWeek: 2, // Tuesday
        hourOfDay: 10, // 10 AM
        openRate: 0.5,
        confidence: 0.3
      };
    }
  }

  /**
   * Get best performing templates for a specific contact
   */
  async getBestTemplatesForContact(
    tenantId: string,
    contactId: string
  ): Promise<{
    templateId: string;
    templateName: string;
    category: string;
    score: number;
    confidence: number;
  }[]> {
    try {
      // Get contact's email history
      const emailHistory = await EmailTracking.find({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        contactId: new mongoose.Types.ObjectId(contactId),
        'customFields.templateId': { $exists: true }
      }).populate('customFields.templateId');
      
      if (emailHistory.length === 0) {
        return await this.getGlobalBestTemplates(tenantId);
      }
      
      // Group by template and calculate performance
      const templatePerformance: Record<string, {
        templateId: string;
        templateName: string;
        category: string;
        sent: number;
        opened: number;
        clicked: number;
        replied: number;
      }> = {};
      
      emailHistory.forEach(email => {
        const templateId = email.customFields?.templateId?.toString();
        if (!templateId) return;
        
        const template = email.customFields.templateId as any;
        const templateName = template.name || 'Unknown Template';
        const category = template.category || 'other';
        
        if (!templatePerformance[templateId]) {
          templatePerformance[templateId] = {
            templateId,
            templateName,
            category,
            sent: 0,
            opened: 0,
            clicked: 0,
            replied: 0
          };
        }
        
        templatePerformance[templateId].sent++;
        
        if (email.status === 'OPENED') templatePerformance[templateId].opened++;
        if (email.status === 'CLICKED') templatePerformance[templateId].clicked++;
        if (email.status === 'REPLIED') templatePerformance[templateId].replied++;
      });
      
      // Calculate scores
      const templateScores = Object.values(templatePerformance).map(template => {
        const openRate = template.sent > 0 ? template.opened / template.sent : 0;
        const clickRate = template.sent > 0 ? template.clicked / template.sent : 0;
        const replyRate = template.sent > 0 ? template.replied / template.sent : 0;
        
        // Weighted score with emphasis on replies
        const score = openRate * 0.3 + clickRate * 0.3 + replyRate * 0.4;
        
        // Confidence based on number of emails sent
        const confidence = Math.min(0.9, 0.3 + (template.sent / 10) * 0.6);
        
        return {
          templateId: template.templateId,
          templateName: template.templateName,
          category: template.category,
          score,
          confidence
        };
      });
      
      // Sort by score
      return templateScores.sort((a, b) => b.score - a.score);
    } catch (error) {
      logger.error(`Error getting best templates for contact ${contactId}:`, error);
      return await this.getGlobalBestTemplates(tenantId);
    }
  }

  /**
   * Get globally best performing templates
   */
  private async getGlobalBestTemplates(tenantId: string): Promise<{
    templateId: string;
    templateName: string;
    category: string;
    score: number;
    confidence: number;
  }[]> {
    try {
      // Get templates with stats
      const templates = await EmailTemplate.find({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        'stats.sent': { $gt: 10 } // Only templates with sufficient data
      });
      
      if (templates.length === 0) {
        return [];
      }
      
      // Calculate scores
      const templateScores = templates.map(template => {
        const stats = template.stats || { sent: 0, opened: 0, clicked: 0, replied: 0 };
        const openRate = stats.sent > 0 ? stats.opened / stats.sent : 0;
        const clickRate = stats.sent > 0 ? stats.clicked / stats.sent : 0;
        const replyRate = stats.sent > 0 ? stats.replied / stats.sent : 0;
        
        // Weighted score with emphasis on replies
        const score = openRate * 0.3 + clickRate * 0.3 + replyRate * 0.4;
        
        // Confidence based on number of emails sent
        const confidence = Math.min(0.9, 0.3 + (stats.sent / 100) * 0.6);
        
        return {
          templateId: template._id.toString(),
          templateName: template.name,
          category: template.category,
          score,
          confidence
        };
      });
      
      // Sort by score
      return templateScores.sort((a, b) => b.score - a.score);
    } catch (error) {
      logger.error('Error getting global best templates:', error);
      return [];
    }
  }

  /**
   * Generate AI-powered recommendations for improving email performance
   */
  async generateRecommendations(
    tenantId: string
  ): Promise<RecommendationItem[]> {
    try {
      const recommendations: RecommendationItem[] = [];
      
      // Analyze template performance
      const templates = await EmailTemplate.find({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        'stats.sent': { $gt: 10 } // Only templates with sufficient data
      });
      
      if (templates.length > 0) {
        // Find templates with low open rates
        const lowOpenRateTemplates = templates.filter(template => {
          const stats = template.stats || { sent: 0, opened: 0 };
          return stats.sent > 20 && (stats.opened / stats.sent) < 0.15;
        });
        
        if (lowOpenRateTemplates.length > 0) {
          recommendations.push({
            type: 'template_subject',
            description: `${lowOpenRateTemplates.length} templates have open rates below 15%. Consider improving subject lines.`,
            impact: 'high',
            action: 'Review and A/B test subject lines'
          });
        }
        
        // Find templates with good open rates but low click rates
        const lowClickRateTemplates = templates.filter(template => {
          const stats = template.stats || { sent: 0, opened: 0, clicked: 0 };
          return stats.opened > 20 && 
                 (stats.opened / stats.sent) > 0.2 && 
                 (stats.clicked / stats.opened) < 0.1;
        });
        
        if (lowClickRateTemplates.length > 0) {
          recommendations.push({
            type: 'template_content',
            description: `${lowClickRateTemplates.length} templates have good open rates but low click rates. Improve call-to-action.`,
            impact: 'medium',
            action: 'Enhance call-to-action buttons and links'
          });
        }
      }
      
      // Analyze sending patterns
      const bestTime = await this.getBestTimeToSend(tenantId);
      if (bestTime.confidence > 0.5) {
        recommendations.push({
          type: 'sending_time',
          description: `Emails sent on ${this.getDayName(bestTime.dayOfWeek)} at ${bestTime.hourOfDay}:00 have ${(bestTime.openRate * 100).toFixed(1)}% open rate.`,
          impact: 'medium',
          action: 'Schedule important emails for this time'
        });
      }
      
      // Analyze A/B testing usage
      const abTestingCount = await EmailTemplate.countDocuments({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        abTestingEnabled: true
      });
      
      if (abTestingCount < 3 && templates.length > 5) {
        recommendations.push({
          type: 'ab_testing',
          description: 'Only a few templates are using A/B testing. Increase usage to optimize performance.',
          impact: 'high',
          action: 'Enable A/B testing for high-volume templates'
        });
      }
      
      // Analyze personalization
      const templatesWithoutVariables = await EmailTemplate.countDocuments({
        tenantId: new mongoose.Types.ObjectId(tenantId),
        body: { $not: /{{.*}}/ } // Templates without variables
      });
      
      if (templatesWithoutVariables > 0) {
        recommendations.push({
          type: 'personalization',
          description: `${templatesWithoutVariables} templates don't use personalization variables.`,
          impact: 'medium',
          action: 'Add personalization to improve engagement'
        });
      }
      
      return recommendations;
    } catch (error) {
      logger.error('Error generating recommendations:', error);
      return [
        {
          type: 'error',
          description: 'Unable to generate recommendations due to an error.',
          impact: 'low'
        }
      ];
    }
  }

  /**
   * Generate contact-level predictions for a list of contacts
   */
  async generateContactPredictions(
    tenantId: string,
    contactIds: string[]
  ): Promise<ContactPrediction[]> {
    try {
      const predictions: ContactPrediction[] = [];
      
      // Get best time to send overall
      const bestTime = await this.getBestTimeToSend(tenantId);
      
      // Get contacts
      const contacts = await Contact.find({
        _id: { $in: contactIds.map(id => new mongoose.Types.ObjectId(id)) }
      });
      
      // Process each contact
      for (const contact of contacts) {
        // Get open probability
        const openPrediction = await this.predictOpenProbability(
          tenantId,
          contact._id.toString()
        );
        
        // Get best templates
        const bestTemplates = await this.getBestTemplatesForContact(
          tenantId,
          contact._id.toString()
        );
        
        // Create prediction
        predictions.push({
          contactId: contact._id.toString(),
          contactName: `${contact.firstName} ${contact.lastName}`,
          contactEmail: contact.email,
          openProbability: openPrediction.score,
          clickProbability: openPrediction.score * 0.7, // Estimate
          replyProbability: openPrediction.score * 0.4, // Estimate
          bestTimeToSend: {
            dayOfWeek: bestTime.dayOfWeek,
            hourOfDay: bestTime.hourOfDay,
            confidence: bestTime.confidence
          },
          bestTemplateId: bestTemplates.length > 0 ? bestTemplates[0].templateId : undefined,
          bestTemplateName: bestTemplates.length > 0 ? bestTemplates[0].templateName : undefined
        });
      }
      
      return predictions;
    } catch (error) {
      logger.error('Error generating contact predictions:', error);
      return [];
    }
  }
}

export default new EmailPredictiveService();
