import nodemailer from 'nodemailer';
import { EmailConfig, IEmailConfig, User } from '../models/mongoose';
import mongoose from 'mongoose';
import resendService from './resend-service';
import { logger } from '../utils/logger';

interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
    path?: string;
  }>;
  cc?: string | string[];
  bcc?: string | string[];
  replyTo?: string;
  contactId?: string;
  sequenceId?: string;
  sequenceStepId?: string;
  trackOpens?: boolean;
  trackLinks?: boolean;
  customFields?: Record<string, any>;
}

interface TemplateData {
  [key: string]: string | number | boolean | Date | undefined;
}

export class EmailService {
  /**
   * Get email configuration for a user
   */
  async getEmailConfig(userId: string): Promise<IEmailConfig | null> {
    try {
      return await EmailConfig.findOne({ userId: new mongoose.Types.ObjectId(userId), isEnabled: true });
    } catch (error) {
      console.error('Error getting email configuration:', error);
      return null;
    }
  }

  /**
   * Create or update email configuration
   */
  async saveEmailConfig(userId: string, config: Partial<IEmailConfig>): Promise<IEmailConfig | null> {
    try {
      // Check if config already exists
      const existingConfig = await EmailConfig.findOne({ userId: new mongoose.Types.ObjectId(userId) });

      if (existingConfig) {
        // Update existing config
        return await EmailConfig.findByIdAndUpdate(
          existingConfig._id,
          { $set: config },
          { new: true }
        );
      } else {
        // Create new config
        const newConfig = new EmailConfig({
          ...config,
          userId: new mongoose.Types.ObjectId(userId)
        });
        return await newConfig.save();
      }
    } catch (error) {
      console.error('Error saving email configuration:', error);
      return null;
    }
  }

  /**
   * Send an email using the user's configuration
   */
  async sendEmail(userId: string, options: EmailOptions): Promise<{ success: boolean; messageId?: string; error?: string; trackingId?: string }> {
    try {
      // Get email configuration
      const config = await this.getEmailConfig(userId);

      if (!config || !config.isEnabled) {
        throw new Error('Email configuration not found or disabled');
      }

      // Check if using Resend
      if (config.provider === 'resend') {
        return this.sendViaResend(userId, config, options);
      }

      // Create transporter based on provider
      const transporter = await this.createTransporter(config);

      if (!transporter) {
        throw new Error('Failed to create email transporter');
      }

      // Send email
      const result = await transporter.sendMail({
        from: `"${config.fromName}" <${config.fromEmail}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text || this.htmlToText(options.html),
        attachments: options.attachments,
        cc: options.cc,
        bcc: options.bcc,
        replyTo: options.replyTo || config.replyToEmail || config.fromEmail
      });

      logger.info(`Email sent via ${config.provider}:`, result.messageId);
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      logger.error('Error sending email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send an email via Resend
   */
  private async sendViaResend(
    userId: string,
    config: IEmailConfig,
    options: EmailOptions
  ): Promise<{ success: boolean; messageId?: string; error?: string; trackingId?: string }> {
    try {
      // Prepare from address
      const from = `${config.fromName} <${config.fromEmail}>`;

      // Determine reply-to address
      const replyTo = options.replyTo || config.replyToEmail || config.fromEmail;

      // Send via Resend service
      const result = await resendService.sendEmail({
        from,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text || this.htmlToText(options.html),
        cc: options.cc,
        bcc: options.bcc,
        replyTo,
        attachments: options.attachments,
        userId,
        contactId: options.contactId,
        sequenceId: options.sequenceId,
        sequenceStepId: options.sequenceStepId,
        trackOpens: options.trackOpens,
        trackLinks: options.trackLinks,
        customFields: options.customFields
      });

      return result;
    } catch (error) {
      logger.error('Error sending email via Resend:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send an email using a template
   */
  async sendTemplateEmail(
    userId: string,
    templateKey: 'proposalShare' | 'proposalAccepted' | 'proposalRejected',
    to: string | string[],
    data: TemplateData,
    options?: Partial<EmailOptions>
  ): Promise<{ success: boolean; messageId?: string; error?: string; trackingId?: string }> {
    try {
      // Get email configuration
      const config = await this.getEmailConfig(userId);

      if (!config || !config.isEnabled) {
        throw new Error('Email configuration not found or disabled');
      }

      // Get template
      const template = config.templates[templateKey];

      if (!template) {
        throw new Error(`Email template '${templateKey}' not found`);
      }

      // Replace template variables
      const subject = this.replaceTemplateVariables(template.subject, data);
      const html = this.replaceTemplateVariables(template.body, data);

      // Send email
      return await this.sendEmail(userId, {
        to,
        subject,
        html,
        ...options
      });
    } catch (error) {
      logger.error('Error sending template email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create a nodemailer transporter based on the email configuration
   */
  private async createTransporter(config: IEmailConfig): Promise<nodemailer.Transporter | null> {
    try {
      switch (config.provider) {
        case 'smtp':
          return nodemailer.createTransport({
            host: config.smtpHost,
            port: config.smtpPort,
            secure: config.smtpSecure,
            auth: {
              user: config.smtpUsername,
              pass: config.smtpPassword
            }
          });

        case 'gmail':
          return nodemailer.createTransport({
            service: 'gmail',
            auth: {
              user: config.smtpUsername,
              pass: config.smtpPassword
            }
          });

        case 'outlook':
          return nodemailer.createTransport({
            service: 'outlook',
            auth: {
              user: config.smtpUsername,
              pass: config.smtpPassword
            }
          });

        case 'sendgrid':
          return nodemailer.createTransport({
            service: 'SendGrid',
            auth: {
              user: 'apikey',
              pass: config.apiKey
            }
          });

        case 'mailgun':
          // For Mailgun, we'd typically use their API directly
          // This is a simplified version using SMTP
          return nodemailer.createTransport({
            host: 'smtp.mailgun.org',
            port: 587,
            secure: false,
            auth: {
              user: config.smtpUsername,
              pass: config.smtpPassword
            }
          });

        case 'ses':
          // For AWS SES, we'd typically use the AWS SDK
          // This is a simplified version using SMTP
          return nodemailer.createTransport({
            host: `email-smtp.${config.region || 'us-east-1'}.amazonaws.com`,
            port: 587,
            secure: false,
            auth: {
              user: config.smtpUsername,
              pass: config.smtpPassword
            }
          });

        case 'resend':
          // Resend is handled separately in sendViaResend method
          throw new Error('Resend provider should be handled by sendViaResend method');

        default:
          throw new Error(`Unsupported email provider: ${config.provider}`);
      }
    } catch (error) {
      logger.error('Error creating email transporter:', error);
      return null;
    }
  }

  /**
   * Replace template variables with actual values
   */
  private replaceTemplateVariables(template: string, data: TemplateData): string {
    let result = template;

    // Replace all {{variable}} occurrences with their values
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value?.toString() || '');
    }

    return result;
  }

  /**
   * Convert HTML to plain text (simple version)
   */
  private htmlToText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
      .trim();
  }
}

export default new EmailService();
