import axios from 'axios';
import { createHash } from 'crypto';
import { 
  InsightsCache, 
  IInsightsCache,
  IChartSpec,
  IRecommendedPlay
} from '../models/mongoose';
import { logger } from '../utils/logger';
import { SubscriptionClient } from '../utils/subscription-client';

// AI Service configuration
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

// Subscription client for feature entitlement and usage tracking
const subscriptionClient = new SubscriptionClient();

/**
 * Interface for Insight Request
 */
interface InsightRequest {
  question: string;
  tenant_id: string;
  user_id: string;
  refresh?: boolean;
  filters?: Record<string, any>;
}

/**
 * Interface for Insight Response
 */
interface InsightResponse {
  success: boolean;
  insight?: IInsightsCache;
  error?: string;
  source: 'cache' | 'ai_service' | 'voyage_fallback' | 'error';
}

/**
 * Interface for AI Service Insight Response
 */
interface AIServiceInsightResponse {
  success: boolean;
  narrative: string;
  chart_spec: IChartSpec;
  why_it_matters: string[];
  recommended_plays: IRecommendedPlay[];
  sql_query?: string;
  graph_query?: string;
  feature_weights?: Record<string, number>;
  dataset_ref: string;
  error?: string;
}

/**
 * Insights Service
 * Generates insights from natural language questions
 */
export class InsightsService {
  /**
   * Generate insight from a natural language question
   */
  async generateInsight(request: InsightRequest): Promise<InsightResponse> {
    try {
      // Check feature entitlement
      const hasAccess = await subscriptionClient.checkFeatureEntitlement(
        request.tenant_id,
        'insights.generate'
      );
      
      if (!hasAccess) {
        return {
          success: false,
          error: 'Feature not available in your subscription plan',
          source: 'error'
        };
      }
      
      // Generate hash for question
      const questionHash = this.generateQuestionHash(request.question, request.filters);
      
      // Check cache if refresh is not requested
      if (!request.refresh) {
        const cachedInsight = await InsightsCache.findOne({
          tenant_id: request.tenant_id,
          question_hash: questionHash,
          expires_at: { $gt: new Date() }
        });
        
        if (cachedInsight) {
          return {
            success: true,
            insight: cachedInsight,
            source: 'cache'
          };
        }
      }
      
      // Record usage
      await subscriptionClient.recordUsage(
        request.tenant_id,
        'insights.generate',
        1
      );
      
      // Try using AI service first
      try {
        const aiServiceResponse = await this.generateWithAIService(request);
        
        // Save to cache
        const insight = await this.saveToCache(
          request.tenant_id,
          request.question,
          questionHash,
          aiServiceResponse
        );
        
        return {
          success: true,
          insight,
          source: 'ai_service'
        };
      } catch (aiError) {
        logger.warn(`AI service error, falling back to Voyage: ${aiError}`);
        
        // Fallback to Voyage AI
        try {
          const voyageResponse = await this.generateWithVoyage(request);
          
          // Save to cache
          const insight = await this.saveToCache(
            request.tenant_id,
            request.question,
            questionHash,
            voyageResponse
          );
          
          return {
            success: true,
            insight,
            source: 'voyage_fallback'
          };
        } catch (voyageError) {
          logger.error(`Voyage fallback error: ${voyageError}`);
          return {
            success: false,
            error: `Failed to generate insight: ${voyageError}`,
            source: 'error'
          };
        }
      }
    } catch (error) {
      logger.error(`Error generating insight: ${error}`);
      return {
        success: false,
        error: `Failed to generate insight: ${error}`,
        source: 'error'
      };
    }
  }
  
  /**
   * Generate insight using AI service
   */
  private async generateWithAIService(request: InsightRequest): Promise<AIServiceInsightResponse> {
    try {
      const response = await axios.post(`${AI_SERVICE_URL}/insights/generate`, {
        question: request.question,
        tenant_id: request.tenant_id,
        user_id: request.user_id,
        filters: request.filters
      });
      
      if (response.data && response.data.success) {
        return response.data;
      } else {
        throw new Error(response.data.error || 'Unknown error from AI service');
      }
    } catch (error) {
      logger.error(`Error calling AI service for insight generation: ${error}`);
      throw error;
    }
  }
  
  /**
   * Generate insight using Voyage AI as fallback
   */
  private async generateWithVoyage(request: InsightRequest): Promise<AIServiceInsightResponse> {
    try {
      // System prompt for Voyage AI
      const systemPrompt = `You are an expert business analyst that generates insights from CRM data. 
      
Your task is to analyze the user's question about their CRM data and generate a comprehensive insight that includes:
1. A narrative explanation (200 words max)
2. A chart specification
3. "Why it matters" bullet points
4. Recommended actions

Respond with a JSON object in this format:
{
  "narrative": "string",
  "chart_spec": {
    "type": "bar|line|pie|funnel|sankey|scatter|heatmap|table",
    "data": {...},
    "options": {...},
    "title": "string",
    "subtitle": "string"
  },
  "why_it_matters": ["string", "string", ...],
  "recommended_plays": [
    {
      "title": "string",
      "description": "string",
      "impact": "high|medium|low",
      "workflow_template": "string"
    }
  ],
  "dataset_ref": "string"
}`;

      // Call Voyage API
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-3',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: request.question }
          ],
          temperature: 0.2,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );
      
      if (response.data && response.data.choices && response.data.choices[0]) {
        const content = response.data.choices[0].message.content;
        const parsedContent = JSON.parse(content);
        
        return {
          success: true,
          ...parsedContent,
          dataset_ref: parsedContent.dataset_ref || 'voyage_generated'
        };
      } else {
        throw new Error('Invalid response from Voyage API');
      }
    } catch (error) {
      logger.error(`Error calling Voyage API for insight generation: ${error}`);
      throw error;
    }
  }
  
  /**
   * Save insight to cache
   */
  private async saveToCache(
    tenant_id: string,
    question: string,
    questionHash: string,
    data: AIServiceInsightResponse
  ): Promise<IInsightsCache> {
    try {
      // Set expiration date (24 hours from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
      
      // Create or update cache entry
      const insight = await InsightsCache.findOneAndUpdate(
        {
          tenant_id,
          question_hash: questionHash
        },
        {
          tenant_id,
          question,
          question_hash: questionHash,
          dataset_ref: data.dataset_ref,
          narrative: data.narrative,
          chart_spec: data.chart_spec,
          why_it_matters: data.why_it_matters,
          recommended_plays: data.recommended_plays,
          sql_query: data.sql_query,
          graph_query: data.graph_query,
          feature_weights: data.feature_weights,
          expires_at: expiresAt,
          refresh_count: 0,
          last_refresh_at: new Date()
        },
        {
          new: true,
          upsert: true
        }
      );
      
      return insight;
    } catch (error) {
      logger.error(`Error saving insight to cache: ${error}`);
      throw error;
    }
  }
  
  /**
   * Generate hash for question
   */
  private generateQuestionHash(question: string, filters?: Record<string, any>): string {
    const hashInput = filters 
      ? `${question}|${JSON.stringify(filters)}`
      : question;
    
    return createHash('md5').update(hashInput).digest('hex');
  }
  
  /**
   * Get cached insights
   */
  async getCachedInsights(
    tenant_id: string,
    options: {
      limit?: number;
      skip?: number;
    } = {}
  ): Promise<{ insights: IInsightsCache[]; total: number }> {
    try {
      const { limit = 20, skip = 0 } = options;
      
      // Execute query
      const insights = await InsightsCache.find({ tenant_id })
        .sort({ updated_at: -1 })
        .skip(skip)
        .limit(limit);
      
      // Get total count
      const total = await InsightsCache.countDocuments({ tenant_id });
      
      return { insights, total };
    } catch (error) {
      logger.error(`Error getting cached insights: ${error}`);
      throw error;
    }
  }
  
  /**
   * Submit feedback for an insight
   */
  async submitFeedback(
    insight_id: string,
    tenant_id: string,
    feedback: {
      helpful: boolean;
      comment?: string;
      submitted_by: string;
    }
  ): Promise<IInsightsCache> {
    try {
      const insight = await InsightsCache.findOne({
        _id: insight_id,
        tenant_id
      });
      
      if (!insight) {
        throw new Error(`Insight not found: ${insight_id}`);
      }
      
      // Add feedback
      insight.feedback = insight.feedback || [];
      insight.feedback.push({
        helpful: feedback.helpful,
        comment: feedback.comment,
        submitted_by: feedback.submitted_by,
        submitted_at: new Date()
      });
      
      // Save insight
      return await insight.save();
    } catch (error) {
      logger.error(`Error submitting feedback: ${error}`);
      throw error;
    }
  }
}

// Export a singleton instance
export const insightsService = new InsightsService();
