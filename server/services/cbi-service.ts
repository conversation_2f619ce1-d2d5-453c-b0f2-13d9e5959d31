import mongoose from 'mongoose';
import crypto from 'crypto';
import axios from 'axios';
import {
  CBICache,
  ICBICache,
  AnalyticsDataset,
  IAnalyticsDataset
} from '../models/mongoose';
import {
  CBICacheType,
  AskBIRequest,
  AskBIResponse,
  ChartSpecType,
  SuggestedExperimentType
} from '@types/analytics-reporting';
import { logger } from '../utils/logger';
import { subscriptionClient } from './subscription-client';
import { BigQueryService } from './bigquery-service';
import { AnalyticsDataService } from './analytics-data-service';

// Environment variables
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';
const AI_BRIDGE_URL = process.env.AI_BRIDGE_URL || 'http://localhost:5000';

/**
 * Service for Conversational BI
 */
export class CBIService {
  /**
   * Ask a question and get insights
   */
  static async askQuestion(request: AskBIRequest): Promise<AskBIResponse> {
    try {
      // Validate tenant_id
      if (!request.tenant_id) {
        throw new Error('Tenant ID is required');
      }

      // Generate hash for question
      const questionHash = this.generateQuestionHash(request.question, request.filters);

      // Check cache if refresh is not requested
      if (!request.refresh) {
        const cachedInsight = await CBICache.findOne({
          tenant_id: new mongoose.Types.ObjectId(request.tenant_id),
          question_hash: questionHash,
          expires_at: { $gt: new Date() }
        });

        if (cachedInsight) {
          return {
            success: true,
            insight: this.mapCBICacheToType(cachedInsight),
            source: 'cache'
          };
        }
      }

      // Record usage
      await subscriptionClient.recordUsage(
        request.tenant_id,
        'analytics.conversational_bi',
        1
      );

      // Get dataset
      let dataset: IAnalyticsDataset | null = null;

      if (request.dataset_ref) {
        dataset = await AnalyticsDataset.findOne({
          tenant_id: new mongoose.Types.ObjectId(request.tenant_id),
          name: request.dataset_ref,
          is_active: true
        });
      } else {
        // Get first active dataset
        dataset = await AnalyticsDataset.findOne({
          tenant_id: new mongoose.Types.ObjectId(request.tenant_id),
          is_active: true
        });
      }

      if (!dataset) {
        throw new Error('No active dataset found');
      }

      // Generate insight
      const startTime = Date.now();
      const insight = await this.generateInsight(request.question, request.filters, dataset, request.tenant_id);
      const endTime = Date.now();

      // Cache insight
      const cbiCache = new CBICache({
        tenant_id: new mongoose.Types.ObjectId(request.tenant_id),
        question: request.question,
        question_hash: questionHash,
        result_json: insight.result_json,
        chart_spec: insight.chart_spec,
        narrative: insight.narrative,
        root_cause_analysis: insight.root_cause_analysis,
        suggested_experiments: insight.suggested_experiments,
        dataset_ref: dataset.name,
        query_type: insight.query_type,
        raw_query: insight.raw_query,
        execution_time_ms: endTime - startTime,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      });

      await cbiCache.save();

      return {
        success: true,
        insight: this.mapCBICacheToType(cbiCache),
        source: 'generated'
      };
    } catch (error: any) {
      logger.error('Error asking question:', error);
      return {
        success: false,
        insight: {} as CBICacheType,
        message: error.message
      };
    }
  }

  /**
   * Generate a hash for a question and filters
   */
  private static generateQuestionHash(question: string, filters?: Record<string, any>): string {
    const data = JSON.stringify({ question, filters });
    return crypto.createHash('md5').update(data).digest('hex');
  }

  /**
   * Map CBICache document to type
   */
  private static mapCBICacheToType(doc: ICBICache): CBICacheType {
    return {
      id: doc._id.toString(),
      tenant_id: doc.tenant_id.toString(),
      question: doc.question,
      question_hash: doc.question_hash,
      result_json: doc.result_json,
      chart_spec: doc.chart_spec as ChartSpecType,
      narrative: doc.narrative,
      root_cause_analysis: doc.root_cause_analysis,
      suggested_experiments: doc.suggested_experiments as SuggestedExperimentType[],
      dataset_ref: doc.dataset_ref,
      query_type: doc.query_type as 'sql' | 'cypher' | 'vector',
      raw_query: doc.raw_query,
      execution_time_ms: doc.execution_time_ms,
      created_at: doc.created_at.toISOString(),
      expires_at: doc.expires_at.toISOString()
    };
  }

  /**
   * Generate insight from question
   */
  private static async generateInsight(
    question: string,
    filters: Record<string, any> | undefined,
    dataset: IAnalyticsDataset,
    tenant_id: string
  ): Promise<{
    result_json: Record<string, any>;
    chart_spec: ChartSpecType;
    narrative: string;
    root_cause_analysis?: string;
    suggested_experiments?: SuggestedExperimentType[];
    query_type: 'sql' | 'cypher' | 'vector';
    raw_query?: string;
  }> {
    try {
      // Try to use AI Bridge if available
      try {
        const response = await axios.post(`${AI_BRIDGE_URL}/api/cbi/generate`, {
          question,
          filters,
          dataset: {
            name: dataset.name,
            source_type: dataset.source_type,
            source_config: dataset.source_config,
            fields: dataset.fields
          },
          tenant_id
        }, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 30000 // 30 seconds
        });

        if (response.data && response.data.success) {
          return response.data.insight;
        }
      } catch (error) {
        logger.warn('Error using AI Bridge for CBI, falling back to direct generation:', error);
      }

      // For BigQuery datasets, try to generate and execute SQL directly
      if (dataset.source_type === 'bigquery') {
        try {
          return await this.generateBigQueryInsight(question, filters, dataset, tenant_id);
        } catch (error) {
          logger.warn('Error generating BigQuery insight, falling back to LLM generation:', error);
        }
      }

      // Fall back to direct generation using Voyage AI
      const systemPrompt = `You are an expert data analyst for Aizako CRM. Your task is to analyze data and provide insights based on user questions.

Dataset: ${dataset.name}
Description: ${dataset.description}
Fields: ${JSON.stringify(dataset.fields)}

Generate a response in the following JSON format:
{
  "result_json": {}, // The data for the chart
  "chart_spec": {
    "type": "bar|line|pie|funnel|sankey|cohort|forecast|anomaly_detection|what_if_analysis|trend_prediction",
    "data": {}, // Chart data in the format expected by the chart library
    "options": {} // Chart options
  },
  "narrative": "", // A concise explanation of the data in plain language
  "root_cause_analysis": "", // Optional analysis of why the data shows this pattern
  "suggested_experiments": [ // Optional list of experiments to try
    {
      "title": "",
      "description": "",
      "estimated_impact": "",
      "confidence": 0-100
    }
  ],
  "query_type": "sql|cypher|vector",
  "raw_query": "" // The query that would be used to get this data
}`;

      // Call Voyage API
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-3',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `Question: ${question}${filters ? `\nFilters: ${JSON.stringify(filters)}` : ''}` }
          ],
          temperature: 0.2,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      if (response.data && response.data.choices && response.data.choices[0]) {
        const content = response.data.choices[0].message.content;
        return JSON.parse(content);
      } else {
        throw new Error('Invalid response from Voyage API');
      }
    } catch (error) {
      logger.error('Error generating insight:', error);
      throw error;
    }
  }

  /**
   * Generate insight using BigQuery
   */
  private static async generateBigQueryInsight(
    question: string,
    filters: Record<string, any> | undefined,
    dataset: IAnalyticsDataset,
    tenant_id: string
  ): Promise<{
    result_json: Record<string, any>;
    chart_spec: ChartSpecType;
    narrative: string;
    root_cause_analysis?: string;
    suggested_experiments?: SuggestedExperimentType[];
    query_type: 'sql';
    raw_query: string;
  }> {
    // First, generate SQL query from natural language question
    const sqlGenerationPrompt = `
You are an expert SQL query generator for a BigQuery analytics system.
Your task is to convert a natural language question into a SQL query.

Dataset: ${dataset.name}
Description: ${dataset.description}
Fields: ${JSON.stringify(dataset.fields)}

IMPORTANT: The query MUST include 'tenant_id = @tenant_id' in the WHERE clause for security.
Use @tenant_dataset as a parameter for the dataset name.

Generate a response in the following JSON format:
{
  "sql_query": "", // The SQL query to execute
  "chart_type": "bar|line|pie|funnel|area", // Recommended chart type
  "x_axis": "", // Field to use for x-axis
  "y_axis": "", // Field to use for y-axis
  "group_by": "" // Optional field to group by
}`;

    // Call Voyage API to generate SQL
    const sqlGenResponse = await axios.post(
      VOYAGE_API_URL,
      {
        model: 'voyage-3',
        messages: [
          { role: 'system', content: sqlGenerationPrompt },
          { role: 'user', content: `Question: ${question}${filters ? `\nFilters: ${JSON.stringify(filters)}` : ''}` }
        ],
        temperature: 0.1,
        response_format: { type: 'json_object' }
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${VOYAGE_API_KEY}`
        }
      }
    );

    if (!sqlGenResponse.data?.choices?.[0]?.message?.content) {
      throw new Error('Failed to generate SQL query');
    }

    const sqlGenContent = JSON.parse(sqlGenResponse.data.choices[0].message.content);
    const sqlQuery = sqlGenContent.sql_query;

    // Validate that the query includes tenant_id filter
    if (!sqlQuery.includes('tenant_id = @tenant_id')) {
      throw new Error('Generated SQL query does not include tenant_id filter');
    }

    // Execute the query
    const queryResults = await BigQueryService.executeQuery(tenant_id, sqlQuery);

    // Generate chart spec based on results
    const chartType = sqlGenContent.chart_type || 'bar';
    const chartData = this.formatBigQueryResults(queryResults, sqlGenContent);

    // Generate narrative using Voyage
    const narrativePrompt = `
You are an expert data analyst for Aizako CRM.
Your task is to analyze data and provide insights based on query results.

Question: ${question}
SQL Query: ${sqlQuery}
Results: ${JSON.stringify(queryResults.slice(0, 10))}${queryResults.length > 10 ? ' (truncated)' : ''}

Generate a response in the following JSON format:
{
  "narrative": "", // A concise explanation of the data in plain language (2-3 paragraphs)
  "root_cause_analysis": "", // Analysis of why the data shows this pattern (1-2 paragraphs)
  "suggested_experiments": [ // List of 2-3 experiments to try
    {
      "title": "",
      "description": "",
      "estimated_impact": "",
      "confidence": 0-100
    }
  ]
}`;

    const narrativeResponse = await axios.post(
      VOYAGE_API_URL,
      {
        model: 'voyage-3',
        messages: [
          { role: 'system', content: narrativePrompt },
          { role: 'user', content: `Generate narrative for the query results.` }
        ],
        temperature: 0.3,
        response_format: { type: 'json_object' }
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${VOYAGE_API_KEY}`
        }
      }
    );

    if (!narrativeResponse.data?.choices?.[0]?.message?.content) {
      throw new Error('Failed to generate narrative');
    }

    const narrativeContent = JSON.parse(narrativeResponse.data.choices[0].message.content);

    // Construct the final insight
    return {
      result_json: queryResults,
      chart_spec: {
        type: chartType,
        data: chartData,
        options: this.getChartOptions(chartType, sqlGenContent)
      },
      narrative: narrativeContent.narrative,
      root_cause_analysis: narrativeContent.root_cause_analysis,
      suggested_experiments: narrativeContent.suggested_experiments,
      query_type: 'sql',
      raw_query: sqlQuery
    };
  }

  /**
   * Format BigQuery results for charting
   */
  private static formatBigQueryResults(
    results: any[],
    sqlGenContent: { x_axis: string; y_axis: string; group_by?: string }
  ): any {
    if (!results || results.length === 0) {
      return [];
    }

    const { x_axis, y_axis, group_by } = sqlGenContent;

    // If there's a group_by field, format for grouped charts
    if (group_by && results[0][group_by] !== undefined) {
      // Group results by x_axis value
      const groupedByX = results.reduce((acc, row) => {
        const xValue = String(row[x_axis] || 'Unknown');
        if (!acc[xValue]) {
          acc[xValue] = {};
        }
        const groupValue = String(row[group_by] || 'Unknown');
        acc[xValue][groupValue] = row[y_axis];
        return acc;
      }, {});

      // Convert to chart data format
      return Object.entries(groupedByX).map(([xValue, groups]) => ({
        name: xValue,
        ...groups
      }));
    }

    // Simple format for non-grouped charts
    return results.map(row => ({
      name: String(row[x_axis] || 'Unknown'),
      value: row[y_axis]
    }));
  }

  /**
   * Get chart options based on chart type
   */
  private static getChartOptions(
    chartType: string,
    sqlGenContent: { x_axis: string; y_axis: string; group_by?: string }
  ): Record<string, any> {
    const { x_axis, y_axis, group_by } = sqlGenContent;

    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false
    };

    switch (chartType) {
      case 'bar':
        return {
          ...baseOptions,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: y_axis
              }
            },
            x: {
              title: {
                display: true,
                text: x_axis
              }
            }
          },
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          ...(group_by ? {
            dataKey: x_axis,
            series: [{ dataKey: y_axis, name: y_axis }]
          } : {
            dataKey: 'name',
            series: [{ dataKey: 'value', name: y_axis }]
          })
        };

      case 'line':
        return {
          ...baseOptions,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: y_axis
              }
            },
            x: {
              title: {
                display: true,
                text: x_axis
              }
            }
          },
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          elements: {
            line: {
              tension: 0.4
            }
          }
        };

      case 'pie':
        return {
          ...baseOptions,
          plugins: {
            legend: {
              position: 'right'
            },
            tooltip: {
              mode: 'point',
              intersect: true
            }
          },
          dataKey: 'value',
          nameKey: 'name'
        };

      default:
        return baseOptions;
    }
  }

  /**
   * Get recent insights
   */
  static async getRecentInsights(tenant_id: string, limit: number = 10): Promise<CBICacheType[]> {
    try {
      const insights = await CBICache.find({
        tenant_id: new mongoose.Types.ObjectId(tenant_id)
      })
      .sort({ created_at: -1 })
      .limit(limit);

      return insights.map(insight => this.mapCBICacheToType(insight));
    } catch (error) {
      logger.error('Error getting recent insights:', error);
      throw error;
    }
  }

  /**
   * Delete insight
   */
  static async deleteInsight(id: string, tenant_id: string): Promise<boolean> {
    try {
      const result = await CBICache.deleteOne({
        _id: new mongoose.Types.ObjectId(id),
        tenant_id: new mongoose.Types.ObjectId(tenant_id)
      });

      return result.deletedCount > 0;
    } catch (error) {
      logger.error('Error deleting insight:', error);
      throw error;
    }
  }
}
