/**
 * Email Tracking Service
 *
 * This service handles email tracking functionality including:
 * - Generating tracking pixels
 * - Processing tracking events (opens, clicks, etc.)
 * - Managing email tracking data
 * - Generating AI-powered reply drafts
 */

import mongoose from 'mongoose';
import axios from 'axios';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import {
  EmailTracking,
  IEmailTracking,
  EmailEventType,
  Contact,
  IContact,
  Sequence,
  SequenceEnrollment
} from '../models/mongoose';
import emailService from './email-service';
import { notificationService } from './notification-service';
import { mcpServer, MCPSourceType } from '../mcp';
import { EmailAdapter } from '../mcp/adapters/email-adapter';
import { logger } from '../utils/logger';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

// Base URL for tracking pixel
const TRACKING_BASE_URL = process.env.TRACKING_BASE_URL || 'https://track.aizako.com';
const APP_BASE_URL = process.env.APP_BASE_URL || 'https://app.aizako.com';

export class EmailTrackingService {
  /**
   * Create a new email tracking record
   */
  async createTracking(data: {
    userId: string;
    tenantId?: string;
    contactId?: string;
    sequenceId?: string;
    sequenceStepId?: string;
    messageId: string;
    subject: string;
    recipient: string;
    sender: string;
    trackingEnabled?: boolean;
    linkTrackingEnabled?: boolean;
    attachmentTrackingEnabled?: boolean;
    customFields?: Record<string, any>;
  }): Promise<IEmailTracking> {
    try {
      // Generate a unique pixel ID
      const pixelId = this.generatePixelId();

      // Create the tracking record
      const tracking = new EmailTracking({
        userId: new mongoose.Types.ObjectId(data.userId),
        tenantId: data.tenantId ? new mongoose.Types.ObjectId(data.tenantId) : undefined,
        contactId: data.contactId ? new mongoose.Types.ObjectId(data.contactId) : undefined,
        sequenceId: data.sequenceId ? new mongoose.Types.ObjectId(data.sequenceId) : undefined,
        sequenceStepId: data.sequenceStepId ? new mongoose.Types.ObjectId(data.sequenceStepId) : undefined,
        messageId: data.messageId,
        subject: data.subject,
        recipient: data.recipient,
        sender: data.sender,
        status: EmailEventType.SENT,
        pixelId,
        trackingEnabled: data.trackingEnabled !== false,
        linkTrackingEnabled: data.linkTrackingEnabled !== false,
        attachmentTrackingEnabled: data.attachmentTrackingEnabled !== false,
        events: [{
          type: EmailEventType.SENT,
          timestamp: new Date(),
          metadata: {}
        }],
        customFields: data.customFields || {}
      });

      await tracking.save();
      logger.info(`Email tracking created for message ${data.messageId} to ${data.recipient}`);
      return tracking;
    } catch (error) {
      logger.error('Error creating email tracking:', error);
      throw error;
    }
  }

  /**
   * Generate a tracking pixel HTML
   */
  generateTrackingPixel(pixelId: string): string {
    const trackingUrl = `${TRACKING_BASE_URL}/pixel/${pixelId}`;
    return `<img src="${trackingUrl}" width="1" height="1" alt="" style="display:none;">`;
  }

  /**
   * Generate a tracking pixel ID
   */
  private generatePixelId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Process a pixel open event
   */
  async processOpenEvent(pixelId: string, metadata: {
    ip?: string;
    userAgent?: string;
    device?: string;
    location?: {
      city?: string;
      country?: string;
      region?: string;
    };
  }): Promise<IEmailTracking | null> {
    try {
      // Find the tracking record
      const tracking = await EmailTracking.findOne({ pixelId });

      if (!tracking) {
        logger.warn(`No tracking record found for pixel ID ${pixelId}`);
        return null;
      }

      // Check if tracking is enabled
      if (!tracking.trackingEnabled) {
        logger.info(`Tracking is disabled for message ${tracking.messageId}`);
        return tracking;
      }

      // Add the open event
      tracking.events.push({
        type: EmailEventType.OPENED,
        timestamp: new Date(),
        metadata
      });

      // Update the status if this is the first open
      if (tracking.status !== EmailEventType.OPENED &&
          tracking.status !== EmailEventType.CLICKED &&
          tracking.status !== EmailEventType.REPLIED) {
        tracking.status = EmailEventType.OPENED;
      }

      await tracking.save();
      logger.info(`Email open event processed for message ${tracking.messageId}`);

      // Send notification to the user
      await this.sendOpenNotification(tracking);

      // Generate AI reply draft if not already generated
      if (!tracking.replyDraftGenerated) {
        this.generateReplyDraft(tracking._id.toString()).catch(error => {
          logger.error(`Error generating reply draft for message ${tracking.messageId}:`, error);
        });
      }

      // Process sequence step if applicable
      if (tracking.sequenceId && tracking.sequenceStepId) {
        this.processSequenceEvent(tracking, EmailEventType.OPENED).catch(error => {
          logger.error(`Error processing sequence event for message ${tracking.messageId}:`, error);
        });
      }

      return tracking;
    } catch (error) {
      logger.error(`Error processing open event for pixel ID ${pixelId}:`, error);
      throw error;
    }
  }

  /**
   * Process a link click event
   */
  async processClickEvent(linkId: string, metadata: {
    ip?: string;
    userAgent?: string;
    device?: string;
    location?: {
      city?: string;
      country?: string;
      region?: string;
    };
    url?: string;
  }): Promise<IEmailTracking | null> {
    try {
      // Find the tracking record
      const tracking = await EmailTracking.findOne({ 'customFields.linkIds': linkId });

      if (!tracking) {
        logger.warn(`No tracking record found for link ID ${linkId}`);
        return null;
      }

      // Check if link tracking is enabled
      if (!tracking.linkTrackingEnabled) {
        logger.info(`Link tracking is disabled for message ${tracking.messageId}`);
        return tracking;
      }

      // Add the click event
      tracking.events.push({
        type: EmailEventType.CLICKED,
        timestamp: new Date(),
        metadata: {
          ...metadata,
          linkId
        }
      });

      // Update the status if this is the first click
      if (tracking.status !== EmailEventType.CLICKED &&
          tracking.status !== EmailEventType.REPLIED) {
        tracking.status = EmailEventType.CLICKED;
      }

      await tracking.save();
      logger.info(`Email click event processed for message ${tracking.messageId}`);

      // Send notification to the user
      await this.sendClickNotification(tracking, metadata.url);

      // Generate AI reply draft if not already generated
      if (!tracking.replyDraftGenerated) {
        this.generateReplyDraft(tracking._id.toString()).catch(error => {
          logger.error(`Error generating reply draft for message ${tracking.messageId}:`, error);
        });
      }

      // Process sequence step if applicable
      if (tracking.sequenceId && tracking.sequenceStepId) {
        this.processSequenceEvent(tracking, EmailEventType.CLICKED).catch(error => {
          logger.error(`Error processing sequence event for message ${tracking.messageId}:`, error);
        });
      }

      return tracking;
    } catch (error) {
      logger.error(`Error processing click event for link ID ${linkId}:`, error);
      throw error;
    }
  }

  /**
   * Process a reply event
   */
  async processReplyEvent(messageId: string, metadata: {
    subject?: string;
    snippet?: string;
    timestamp?: Date;
  }): Promise<IEmailTracking | null> {
    try {
      // Find the tracking record
      const tracking = await EmailTracking.findOne({ messageId });

      if (!tracking) {
        logger.warn(`No tracking record found for message ID ${messageId}`);
        return null;
      }

      // Add the reply event
      tracking.events.push({
        type: EmailEventType.REPLIED,
        timestamp: new Date(),
        metadata
      });

      // Update the status
      tracking.status = EmailEventType.REPLIED;

      await tracking.save();
      logger.info(`Email reply event processed for message ${tracking.messageId}`);

      // Send notification to the user
      await this.sendReplyNotification(tracking, metadata.snippet);

      // Process sequence step if applicable
      if (tracking.sequenceId && tracking.sequenceStepId) {
        this.processSequenceEvent(tracking, EmailEventType.REPLIED).catch(error => {
          logger.error(`Error processing sequence event for message ${tracking.messageId}:`, error);
        });
      }

      return tracking;
    } catch (error) {
      logger.error(`Error processing reply event for message ID ${messageId}:`, error);
      throw error;
    }
  }

  /**
   * Generate an AI-powered reply draft
   */
  async generateReplyDraft(trackingId: string): Promise<string | null> {
    try {
      // Find the tracking record
      const tracking = await EmailTracking.findById(trackingId);

      if (!tracking) {
        logger.warn(`No tracking record found for ID ${trackingId}`);
        return null;
      }

      // Get contact information if available
      let contact: IContact | null = null;
      if (tracking.contactId) {
        contact = await Contact.findById(tracking.contactId);
      }

      // Prepare context for AI
      const context = {
        recipient: tracking.recipient,
        subject: tracking.subject,
        contactInfo: contact ? {
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          company: contact.companyId ? 'Associated company' : undefined,
          status: contact.status,
          notes: contact.notes,
          persona: contact.persona
        } : undefined,
        events: tracking.events.map(event => ({
          type: event.type,
          timestamp: event.timestamp
        })),
        messageId: tracking.messageId
      };

      // Call AI service to generate reply draft
      const response = await axios.post(`${AI_SERVICE_URL}/email/reply-draft`, {
        trackingId: tracking._id.toString(),
        userId: tracking.userId.toString(),
        tenantId: tracking.tenantId?.toString(),
        context
      });

      if (response.status === 200 && response.data.success) {
        // Update tracking record
        tracking.replyDraftGenerated = true;
        tracking.replyDraftId = response.data.draftId;
        await tracking.save();

        logger.info(`Reply draft generated for message ${tracking.messageId}`);
        return response.data.draftId;
      } else {
        logger.error(`Failed to generate reply draft: ${response.data.error || 'Unknown error'}`);
        return null;
      }
    } catch (error) {
      logger.error(`Error generating reply draft for tracking ID ${trackingId}:`, error);
      throw error;
    }
  }

  /**
   * Send an open notification to the user
   */
  private async sendOpenNotification(tracking: IEmailTracking): Promise<void> {
    try {
      const contactName = tracking.contactId ?
        await this.getContactName(tracking.contactId.toString()) :
        tracking.recipient;

      await notificationService.createNotification({
        userId: tracking.userId.toString(),
        type: 'email_opened',
        title: 'Email Opened',
        message: `${contactName} opened your email "${tracking.subject}"`,
        data: {
          trackingId: tracking._id.toString(),
          messageId: tracking.messageId,
          recipient: tracking.recipient,
          subject: tracking.subject,
          contactId: tracking.contactId?.toString()
        },
        isRead: false
      });
    } catch (error) {
      logger.error(`Error sending open notification for message ${tracking.messageId}:`, error);
    }
  }

  /**
   * Send a click notification to the user
   */
  private async sendClickNotification(tracking: IEmailTracking, url?: string): Promise<void> {
    try {
      const contactName = tracking.contactId ?
        await this.getContactName(tracking.contactId.toString()) :
        tracking.recipient;

      await notificationService.createNotification({
        userId: tracking.userId.toString(),
        type: 'email_clicked',
        title: 'Email Link Clicked',
        message: `${contactName} clicked a link in your email "${tracking.subject}"${url ? `: ${url}` : ''}`,
        data: {
          trackingId: tracking._id.toString(),
          messageId: tracking.messageId,
          recipient: tracking.recipient,
          subject: tracking.subject,
          contactId: tracking.contactId?.toString(),
          url
        },
        isRead: false
      });
    } catch (error) {
      logger.error(`Error sending click notification for message ${tracking.messageId}:`, error);
    }
  }

  /**
   * Send a reply notification to the user
   */
  private async sendReplyNotification(tracking: IEmailTracking, snippet?: string): Promise<void> {
    try {
      const contactName = tracking.contactId ?
        await this.getContactName(tracking.contactId.toString()) :
        tracking.recipient;

      await notificationService.createNotification({
        userId: tracking.userId.toString(),
        type: 'email_replied',
        title: 'Email Reply Received',
        message: `${contactName} replied to your email "${tracking.subject}"${snippet ? `: "${snippet.substring(0, 50)}${snippet.length > 50 ? '...' : ''}"` : ''}`,
        data: {
          trackingId: tracking._id.toString(),
          messageId: tracking.messageId,
          recipient: tracking.recipient,
          subject: tracking.subject,
          contactId: tracking.contactId?.toString(),
          snippet
        },
        isRead: false
      });
    } catch (error) {
      logger.error(`Error sending reply notification for message ${tracking.messageId}:`, error);
    }
  }

  /**
   * Get a contact's full name
   */
  private async getContactName(contactId: string): Promise<string> {
    try {
      const contact = await Contact.findById(contactId);
      return contact ? `${contact.firstName} ${contact.lastName}` : 'Unknown Contact';
    } catch (error) {
      logger.error(`Error getting contact name for ID ${contactId}:`, error);
      return 'Unknown Contact';
    }
  }

  /**
   * Find a tracking record by message ID
   */
  async findByMessageId(messageId: string): Promise<IEmailTracking | null> {
    try {
      const tracking = await EmailTracking.findOne({ messageId });
      return tracking;
    } catch (error) {
      logger.error(`Error finding tracking record for message ID ${messageId}:`, error);
      return null;
    }
  }

  /**
   * Process a sequence event
   */
  private async processSequenceEvent(tracking: IEmailTracking, eventType: EmailEventType): Promise<void> {
    try {
      if (!tracking.sequenceId || !tracking.sequenceStepId) {
        return;
      }

      // Find the sequence enrollment
      const enrollment = await SequenceEnrollment.findOne({
        sequenceId: tracking.sequenceId,
        contactId: tracking.contactId
      });

      if (!enrollment) {
        logger.warn(`No sequence enrollment found for sequence ${tracking.sequenceId} and contact ${tracking.contactId}`);
        return;
      }

      // Update the step history
      const stepIndex = enrollment.stepHistory.findIndex(step =>
        step.trackingId && step.trackingId.equals(tracking._id));

      if (stepIndex >= 0) {
        // Step already exists in history, update it
        enrollment.stepHistory[stepIndex].status = 'success';
        enrollment.stepHistory[stepIndex].result = eventType;
      }

      await enrollment.save();
      logger.info(`Sequence event processed for message ${tracking.messageId}`);

      // If this is a reply, we might want to pause or complete the sequence
      if (eventType === EmailEventType.REPLIED) {
        // Get the sequence
        const sequence = await Sequence.findById(tracking.sequenceId);

        if (sequence && sequence.goal.toLowerCase().includes('meeting') ||
            sequence.goal.toLowerCase().includes('demo')) {
          // If the goal is to book a meeting/demo, we might want to pause the sequence
          // until we determine if the reply indicates a meeting was booked
          enrollment.status = 'paused';
          enrollment.pauseReason = 'Received reply, checking if meeting was booked';
          await enrollment.save();
          logger.info(`Sequence paused for enrollment ${enrollment._id} due to reply`);
        }
      }
    } catch (error) {
      logger.error(`Error processing sequence event for message ${tracking.messageId}:`, error);
    }
  }
}

export default new EmailTrackingService();
