/**
 * Notification Service
 *
 * This service handles notification functionality including:
 * - Creating and managing notifications
 * - Sending real-time notifications
 * - Managing notification preferences
 */

import mongoose from 'mongoose';
import { Notification, INotification } from '../models/mongoose';
import { logger } from '../utils/logger';

class NotificationService {
  /**
   * Create a new notification
   */
  async createNotification(data: {
    userId: string;
    type: string;
    title: string;
    message: string;
    data?: Record<string, any>;
    isRead?: boolean;
  }): Promise<INotification> {
    try {
      // Create the notification
      const notification = new Notification({
        userId: new mongoose.Types.ObjectId(data.userId),
        type: data.type,
        title: data.title,
        message: data.message,
        data: data.data || {},
        isRead: data.isRead !== undefined ? data.isRead : false
      });

      await notification.save();
      logger.info(`Notification created: ${notification.title}`);

      // Emit real-time notification event
      // In a production environment, you would use a WebSocket or similar
      // For now, we'll just log it
      logger.info(`Real-time notification emitted to user ${data.userId}: ${notification.title}`);

      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   */
  async getNotifications(
    userId: string,
    options: {
      type?: string;
      isRead?: boolean;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{ notifications: INotification[]; total: number }> {
    try {
      // Build query
      const query: any = { userId: new mongoose.Types.ObjectId(userId) };

      if (options.type) {
        query.type = options.type;
      }

      if (options.isRead !== undefined) {
        query.isRead = options.isRead;
      }

      // Get total count
      const total = await Notification.countDocuments(query);

      // Get notifications
      const notifications = await Notification.find(query)
        .sort({ createdAt: -1 })
        .skip(options.offset || 0)
        .limit(options.limit || 20);

      return { notifications, total };
    } catch (error) {
      logger.error('Error getting notifications:', error);
      throw error;
    }
  }

  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<INotification | null> {
    try {
      // Find and update the notification
      const notification = await Notification.findOneAndUpdate(
        {
          _id: new mongoose.Types.ObjectId(notificationId),
          userId: new mongoose.Types.ObjectId(userId)
        },
        { isRead: true },
        { new: true }
      );

      if (!notification) {
        logger.warn(`Notification ${notificationId} not found or does not belong to user ${userId}`);
        return null;
      }

      logger.info(`Notification ${notificationId} marked as read`);
      return notification;
    } catch (error) {
      logger.error(`Error marking notification ${notificationId} as read:`, error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string, type?: string): Promise<number> {
    try {
      // Build query
      const query: any = { userId: new mongoose.Types.ObjectId(userId), isRead: false };

      if (type) {
        query.type = type;
      }

      // Update notifications
      const result = await Notification.updateMany(query, { isRead: true });

      logger.info(`${result.modifiedCount} notifications marked as read for user ${userId}`);
      return result.modifiedCount;
    } catch (error) {
      logger.error(`Error marking all notifications as read for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: string, userId: string): Promise<boolean> {
    try {
      // Delete the notification
      const result = await Notification.deleteOne({
        _id: new mongoose.Types.ObjectId(notificationId),
        userId: new mongoose.Types.ObjectId(userId)
      });

      if (result.deletedCount === 0) {
        logger.warn(`Notification ${notificationId} not found or does not belong to user ${userId}`);
        return false;
      }

      logger.info(`Notification ${notificationId} deleted`);
      return true;
    } catch (error) {
      logger.error(`Error deleting notification ${notificationId}:`, error);
      throw error;
    }
  }

  /**
   * Delete all notifications for a user
   */
  async deleteAllNotifications(userId: string, type?: string): Promise<number> {
    try {
      // Build query
      const query: any = { userId: new mongoose.Types.ObjectId(userId) };

      if (type) {
        query.type = type;
      }

      // Delete notifications
      const result = await Notification.deleteMany(query);

      logger.info(`${result.deletedCount} notifications deleted for user ${userId}`);
      return result.deletedCount;
    } catch (error) {
      logger.error(`Error deleting all notifications for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get unread notification count for a user
   */
  async getUnreadCount(userId: string, type?: string): Promise<number> {
    try {
      // Build query
      const query: any = { userId: new mongoose.Types.ObjectId(userId), isRead: false };

      if (type) {
        query.type = type;
      }

      // Get count
      const count = await Notification.countDocuments(query);

      return count;
    } catch (error) {
      logger.error(`Error getting unread notification count for user ${userId}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;
