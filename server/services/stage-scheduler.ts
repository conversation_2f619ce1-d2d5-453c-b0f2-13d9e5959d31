import { StageTransitionService } from './stage-transition-service';

/**
 * Scheduler for stage transition checks
 */
export class StageScheduler {
  private static instance: StageScheduler;
  private dailyCheckInterval: NodeJS.Timeout | null = null;
  
  private constructor() {
    // Private constructor for singleton pattern
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): StageScheduler {
    if (!StageScheduler.instance) {
      StageScheduler.instance = new StageScheduler();
    }
    return StageScheduler.instance;
  }
  
  /**
   * Start the scheduler
   */
  public startScheduler(autoApply: boolean = false): void {
    this.stopScheduler(); // Clear any existing intervals
    
    // Get the stage transition service
    const stageService = StageTransitionService.getInstance();
    
    // Schedule daily checks (run every 24 hours)
    this.dailyCheckInterval = setInterval(async () => {
      try {
        console.log('Running daily stage transition checks...');
        await stageService.checkAllOpportunities(autoApply);
      } catch (error) {
        console.error('Error running daily stage transition checks:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours
    
    // Run immediately on startup
    setTimeout(async () => {
      try {
        console.log('Running initial stage transition checks...');
        await stageService.checkAllOpportunities(autoApply);
      } catch (error) {
        console.error('Error running initial stage transition checks:', error);
      }
    }, 30000); // 30 seconds after startup to allow other services to initialize
    
    console.log('Stage scheduler started');
  }
  
  /**
   * Stop the scheduler
   */
  public stopScheduler(): void {
    if (this.dailyCheckInterval) {
      clearInterval(this.dailyCheckInterval);
      this.dailyCheckInterval = null;
    }
    
    console.log('Stage scheduler stopped');
  }
}
