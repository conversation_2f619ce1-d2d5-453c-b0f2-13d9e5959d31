import mongoose from 'mongoose';
import axios from 'axios';
import { 
  FollowUp, 
  FollowUpTemplate, 
  Contact, 
  Company, 
  Opportunity, 
  Activity,
  IFollowUp,
  IFollowUpTemplate,
  IContact,
  ICompany,
  IOpportunity,
  IActivity
} from '../models/mongoose';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

/**
 * Follow-up Coach Service
 * Provides functionality for generating and managing follow-up recommendations
 */
export class FollowUpCoachService {
  /**
   * Get all follow-ups for a user
   */
  async getFollowUps(
    filter: {
      userId: string;
      status?: 'pending' | 'completed' | 'cancelled';
      type?: 'email' | 'call' | 'meeting' | 'task' | 'other';
      priority?: 'low' | 'medium' | 'high';
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      startDate?: Date;
      endDate?: Date;
      search?: string;
    }
  ): Promise<IFollowUp[]> {
    try {
      const query: any = {
        createdBy: new mongoose.Types.ObjectId(filter.userId)
      };
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.priority) {
        query.priority = filter.priority;
      }
      
      if (filter.opportunityId) {
        query.opportunityId = new mongoose.Types.ObjectId(filter.opportunityId);
      }
      
      if (filter.contactId) {
        query.contactId = new mongoose.Types.ObjectId(filter.contactId);
      }
      
      if (filter.companyId) {
        query.companyId = new mongoose.Types.ObjectId(filter.companyId);
      }
      
      if (filter.startDate || filter.endDate) {
        query.scheduledDate = {};
        
        if (filter.startDate) {
          query.scheduledDate.$gte = filter.startDate;
        }
        
        if (filter.endDate) {
          query.scheduledDate.$lte = filter.endDate;
        }
      }
      
      if (filter.search) {
        query.$text = { $search: filter.search };
      }
      
      return await FollowUp.find(query)
        .sort({ scheduledDate: 1 })
        .populate('contactId', 'firstName lastName email')
        .populate('companyId', 'name')
        .populate('opportunityId', 'name value');
    } catch (error) {
      console.error('Error getting follow-ups:', error);
      throw error;
    }
  }

  /**
   * Get a follow-up by ID
   */
  async getFollowUpById(id: string): Promise<IFollowUp | null> {
    try {
      return await FollowUp.findById(id)
        .populate('contactId', 'firstName lastName email')
        .populate('companyId', 'name')
        .populate('opportunityId', 'name value')
        .populate('activityId', 'title type date');
    } catch (error) {
      console.error(`Error getting follow-up with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new follow-up
   */
  async createFollowUp(
    data: {
      title: string;
      description?: string;
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      activityId?: string;
      scheduledDate: Date;
      completedDate?: Date;
      status?: 'pending' | 'completed' | 'cancelled';
      type?: 'email' | 'call' | 'meeting' | 'task' | 'other';
      priority?: 'low' | 'medium' | 'high';
      template?: string;
      content?: string;
      reminderDate?: Date;
      isAIGenerated?: boolean;
      tags?: string[];
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<IFollowUp> {
    try {
      const followUp = new FollowUp({
        ...data,
        opportunityId: data.opportunityId ? new mongoose.Types.ObjectId(data.opportunityId) : undefined,
        contactId: data.contactId ? new mongoose.Types.ObjectId(data.contactId) : undefined,
        companyId: data.companyId ? new mongoose.Types.ObjectId(data.companyId) : undefined,
        activityId: data.activityId ? new mongoose.Types.ObjectId(data.activityId) : undefined,
        isAIGenerated: data.isAIGenerated || false,
        createdBy: new mongoose.Types.ObjectId(userId)
      });
      
      return await followUp.save();
    } catch (error) {
      console.error('Error creating follow-up:', error);
      throw error;
    }
  }

  /**
   * Update a follow-up
   */
  async updateFollowUp(
    id: string,
    data: {
      title?: string;
      description?: string;
      scheduledDate?: Date;
      completedDate?: Date;
      status?: 'pending' | 'completed' | 'cancelled';
      type?: 'email' | 'call' | 'meeting' | 'task' | 'other';
      priority?: 'low' | 'medium' | 'high';
      template?: string;
      content?: string;
      reminderDate?: Date;
      tags?: string[];
      customFields?: Record<string, any>;
    }
  ): Promise<IFollowUp | null> {
    try {
      return await FollowUp.findByIdAndUpdate(
        id,
        { $set: data },
        { new: true }
      );
    } catch (error) {
      console.error(`Error updating follow-up with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a follow-up
   */
  async deleteFollowUp(id: string): Promise<boolean> {
    try {
      const result = await FollowUp.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      console.error(`Error deleting follow-up with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Generate follow-up recommendations using AI
   */
  async generateFollowUpRecommendations(
    params: {
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      activityId?: string;
      count?: number;
    },
    userId: string
  ): Promise<IFollowUp[]> {
    try {
      const { opportunityId, contactId, companyId, activityId, count = 3 } = params;
      
      // Get related data
      const [contact, company, opportunity, activity] = await Promise.all([
        contactId ? Contact.findById(contactId) : null,
        companyId ? Company.findById(companyId) : null,
        opportunityId ? Opportunity.findById(opportunityId) : null,
        activityId ? Activity.findById(activityId) : null
      ]);
      
      // Get recent activities
      const activities = await Activity.find({
        $or: [
          { contactId: contactId ? new mongoose.Types.ObjectId(contactId) : null },
          { companyId: companyId ? new mongoose.Types.ObjectId(companyId) : null },
          { opportunityId: opportunityId ? new mongoose.Types.ObjectId(opportunityId) : null }
        ].filter(Boolean)
      })
        .sort({ date: -1 })
        .limit(10);
      
      // Generate follow-up recommendations using AI
      const recommendations = await this.generateFollowUpsWithAI(
        contact,
        company,
        opportunity,
        activity,
        activities,
        count
      );
      
      // Create follow-up records
      const followUps = await Promise.all(
        recommendations.map(async (rec) => {
          return await this.createFollowUp(
            {
              title: rec.title,
              description: rec.description,
              opportunityId,
              contactId,
              companyId,
              activityId,
              scheduledDate: new Date(rec.scheduledDate),
              type: rec.type as any,
              priority: rec.priority as any,
              content: rec.content,
              reminderDate: rec.reminderDate ? new Date(rec.reminderDate) : undefined,
              isAIGenerated: true,
              tags: rec.tags
            },
            userId
          );
        })
      );
      
      return followUps;
    } catch (error) {
      console.error('Error generating follow-up recommendations:', error);
      throw error;
    }
  }

  /**
   * Generate follow-ups using AI
   */
  private async generateFollowUpsWithAI(
    contact: IContact | null,
    company: ICompany | null,
    opportunity: IOpportunity | null,
    activity: IActivity | null,
    activities: IActivity[],
    count: number
  ): Promise<Array<{
    title: string;
    description: string;
    scheduledDate: string;
    type: string;
    priority: string;
    content: string;
    reminderDate?: string;
    tags: string[];
  }>> {
    try {
      // Prepare data for AI
      const data = {
        contact: contact ? {
          id: contact._id.toString(),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title,
          status: contact.status,
          notes: contact.notes,
          tags: contact.tags,
          createdAt: contact.createdAt,
          updatedAt: contact.updatedAt
        } : null,
        company: company ? {
          id: company._id.toString(),
          name: company.name,
          industry: company.industry,
          website: company.website,
          size: company.size,
          status: company.status,
          notes: company.notes,
          tags: company.tags,
          createdAt: company.createdAt,
          updatedAt: company.updatedAt
        } : null,
        opportunity: opportunity ? {
          id: opportunity._id.toString(),
          name: opportunity.name,
          value: opportunity.value,
          currency: opportunity.currency,
          stage: opportunity.stage,
          probability: opportunity.probability,
          expectedCloseDate: opportunity.expectedCloseDate,
          description: opportunity.description,
          tags: opportunity.tags,
          createdAt: opportunity.createdAt,
          updatedAt: opportunity.updatedAt
        } : null,
        activity: activity ? {
          id: activity._id.toString(),
          title: activity.title,
          type: activity.type,
          description: activity.description,
          date: activity.date,
          duration: activity.duration,
          completed: activity.completed,
          createdAt: activity.createdAt,
          updatedAt: activity.updatedAt
        } : null,
        recentActivities: activities.map(act => ({
          id: act._id.toString(),
          title: act.title,
          type: act.type,
          description: act.description,
          date: act.date,
          duration: act.duration,
          completed: act.completed,
          createdAt: act.createdAt,
          updatedAt: act.updatedAt
        })),
        count
      };
      
      // Create prompt for AI
      const prompt = `
        Based on the provided data, generate ${count} follow-up recommendations.
        
        For each recommendation, provide:
        1. A clear title
        2. A brief description
        3. A suggested scheduled date (in ISO format)
        4. Type (email, call, meeting, task, or other)
        5. Priority (low, medium, high)
        6. Content (suggested message or talking points)
        7. Optional reminder date (in ISO format)
        8. Relevant tags
        
        Consider the context of the contact, company, opportunity, and recent activities.
        Ensure the follow-ups are timely, relevant, and likely to move the relationship or opportunity forward.
        Format your response as a JSON array of follow-up objects.
      `;
      
      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/crew`, {
          task: prompt,
          userId: 1, // System user
          context: data
        }, {
          timeout: 30000 // 30 second timeout
        });
        
        if (aiResponse.data && aiResponse.data.response) {
          try {
            // Try to parse the response as JSON
            const followUps = JSON.parse(aiResponse.data.response);
            return followUps;
          } catch (e) {
            console.error('Error parsing AI service response:', e);
          }
        }
      } catch (aiError) {
        console.error('Error using AI service, falling back to Voyage AI:', aiError);
      }
      
      // Fallback to Voyage AI if the AI service fails
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales follow-up coach for Aizako CRM.
              Your job is to help sales representatives with timely and effective follow-ups.
              Focus on providing actionable, personalized follow-up recommendations.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.7,
          max_tokens: 1500,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );
      
      if (response.data && response.data.choices && response.data.choices.length > 0) {
        try {
          const content = response.data.choices[0].message.content;
          const followUps = JSON.parse(content);
          return followUps.followUps || followUps;
        } catch (e) {
          console.error('Error parsing Voyage AI response:', e);
          throw new Error('Failed to parse AI response');
        }
      }
      
      throw new Error('Failed to generate follow-up recommendations');
    } catch (error) {
      console.error('Error generating follow-ups with AI:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const followUpCoachService = new FollowUpCoachService();
