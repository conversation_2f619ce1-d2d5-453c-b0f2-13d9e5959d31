import { IOpportunity, IContact, ICompany, IActivity } from '../models/mongoose';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || process.env.CRM_OPENAI_API_KEY
});

// Define the insight structure
export interface PipelineInsight {
  title: string;
  description: string;
  type: 'opportunity' | 'risk' | 'trend' | 'suggestion' | 'reminder';
  importance: number; // 1-5 scale
}

// Define the input for generating insights
export interface PipelineInsightInput {
  opportunity: IOpportunity;
  contact: IContact | null;
  company: ICompany | null;
  activities: IActivity[];
  eventType: 'stage_change' | 'new_opportunity' | 'stalled_opportunity' | 'meetings_no_proposal';
  oldStage?: string;
  newStage?: string;
  daysSinceUpdate?: number;
  meetingCount?: number;
}

/**
 * Generate a pipeline insight using OpenAI
 */
export async function generatePipelineInsight(
  input: PipelineInsightInput
): Promise<PipelineInsight | null> {
  try {
    // Prepare the data for OpenAI
    const data = {
      opportunity: {
        id: input.opportunity._id.toString(),
        name: input.opportunity.name,
        value: input.opportunity.value,
        currency: input.opportunity.currency,
        stage: input.opportunity.stage,
        probability: input.opportunity.probability,
        expectedCloseDate: input.opportunity.expectedCloseDate,
        description: input.opportunity.description,
        tags: input.opportunity.tags,
        createdAt: input.opportunity.createdAt,
        updatedAt: input.opportunity.updatedAt
      },
      contact: input.contact ? {
        id: input.contact._id.toString(),
        name: `${input.contact.firstName} ${input.contact.lastName}`,
        email: input.contact.email,
        phone: input.contact.phone,
        title: input.contact.title
      } : null,
      company: input.company ? {
        id: input.company._id.toString(),
        name: input.company.name,
        industry: input.company.industry,
        size: input.company.size
      } : null,
      activities: input.activities.map(activity => ({
        type: activity.type,
        title: activity.title,
        date: activity.date,
        completed: activity.completed,
        notes: activity.notes
      })),
      event: {
        type: input.eventType,
        oldStage: input.oldStage,
        newStage: input.newStage,
        daysSinceUpdate: input.daysSinceUpdate,
        meetingCount: input.meetingCount
      }
    };

    // Create a prompt based on the event type
    let prompt = '';
    
    switch (input.eventType) {
      case 'stage_change':
        prompt = `
          Analyze this opportunity stage change from "${input.oldStage}" to "${input.newStage}".
          Generate a concise, insightful narrative about what this means for the deal.
          Include any risks, opportunities, or recommended next steps based on the data.
          If moving forward in the pipeline, be encouraging but realistic.
          If moving backward, identify potential issues and suggest recovery actions.
        `;
        break;
        
      case 'new_opportunity':
        prompt = `
          A new opportunity "${input.opportunity.name}" has been created.
          Generate a concise, insightful narrative about this new opportunity.
          Include any initial recommendations for successfully advancing this deal.
          Consider the value, company industry, and any other relevant factors.
        `;
        break;
        
      case 'stalled_opportunity':
        prompt = `
          This opportunity has been in the "${input.opportunity.stage}" stage for ${input.daysSinceUpdate} days without updates.
          Generate a concise, insightful narrative about why this deal might be stalled.
          Include specific recommendations to re-engage and move the opportunity forward.
          Consider the current stage, value, and any past activities.
        `;
        break;
        
      case 'meetings_no_proposal':
        prompt = `
          This opportunity has had ${input.meetingCount} meetings but is still in the "${input.opportunity.stage}" stage without advancing to proposal.
          Generate a concise, insightful narrative about why this deal might not be progressing.
          Include specific recommendations to move toward a proposal or qualify out.
          Consider the meeting history, current stage, and value of the opportunity.
        `;
        break;
    }

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o', // Using the newest model for best results
      messages: [
        {
          role: 'system',
          content: `You are an expert sales pipeline analyst for Aizako CRM. 
          Your job is to analyze pipeline events and provide insightful narratives in plain English.
          Focus on actionable insights that help sales reps close more deals.
          Format your response as a JSON object with title, description, type (opportunity, risk, trend, suggestion, reminder), 
          and importance (1-5 scale, where 5 is highest priority).
          Current date: ${new Date().toISOString().split('T')[0]}`
        },
        {
          role: 'user',
          content: `${prompt}\n\nData: ${JSON.stringify(data)}`
        }
      ],
      temperature: 0.7,
      max_tokens: 800,
      response_format: { type: 'json_object' }
    });

    // Parse the response
    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content in OpenAI response');
    }

    const parsedResponse = JSON.parse(content);
    
    // Validate the response structure
    if (!parsedResponse.title || !parsedResponse.description || !parsedResponse.type || !parsedResponse.importance) {
      throw new Error('Invalid response structure from OpenAI');
    }

    // Ensure importance is within range
    const importance = Math.min(Math.max(parseInt(parsedResponse.importance), 1), 5);

    // Return the insight
    return {
      title: parsedResponse.title,
      description: parsedResponse.description,
      type: parsedResponse.type as 'opportunity' | 'risk' | 'trend' | 'suggestion' | 'reminder',
      importance
    };
  } catch (error) {
    console.error('Error generating pipeline insight:', error);
    return null;
  }
}
