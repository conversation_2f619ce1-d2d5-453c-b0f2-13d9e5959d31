import mongoose from 'mongoose';
import axios from 'axios';
import {
  Proposal,
  ProposalTemplate,
  Contact,
  Company,
  Opportunity,
  Activity,
  DocumentModel,
  IProposal,
  IProposalTemplate,
  IContact,
  ICompany,
  IOpportunity,
  IActivity,
  IDocument
} from '../models/mongoose';
import { EmailService } from './email-service';
import { ProposalAnalyticsService } from './proposal-analytics-service';
import { graphRAGService } from './graph-rag-service';
import { logger } from '../utils/logger';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

// Initialize services
const emailService = new EmailService();
const analyticsService = new ProposalAnalyticsService();

/**
 * Service for generating and managing proposals
 */
export class ProposalGeneratorService {
  /**
   * Get all proposal templates
   */
  async getAllTemplates(
    filter: {
      category?: string;
      isDefault?: boolean;
      createdBy?: string;
      search?: string;
    } = {}
  ): Promise<IProposalTemplate[]> {
    try {
      const query: any = {};

      if (filter.category) {
        query.category = filter.category;
      }

      if (filter.isDefault !== undefined) {
        query.isDefault = filter.isDefault;
      }

      if (filter.createdBy) {
        query.createdBy = new mongoose.Types.ObjectId(filter.createdBy);
      }

      if (filter.search) {
        query.$text = { $search: filter.search };
      }

      return await ProposalTemplate.find(query).sort({ name: 1 });
    } catch (error) {
      console.error('Error getting proposal templates:', error);
      throw error;
    }
  }

  /**
   * Get template by ID
   */
  async getTemplateById(id: string): Promise<IProposalTemplate | null> {
    try {
      return await ProposalTemplate.findById(id);
    } catch (error) {
      console.error(`Error getting template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new proposal template
   */
  async createTemplate(
    data: {
      name: string;
      description?: string;
      category: string;
      content: string;
      sections: Array<{
        id: string;
        name: string;
        type: string;
        content: string;
        isRequired: boolean;
        order: number;
      }>;
      variables?: string[];
      tags?: string[];
      isDefault?: boolean;
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<IProposalTemplate> {
    try {
      // Extract variables from content if not provided
      const variables = data.variables || this.extractVariables(data.content);

      const template = new ProposalTemplate({
        ...data,
        variables,
        createdBy: new mongoose.Types.ObjectId(userId)
      });

      return await template.save();
    } catch (error) {
      console.error('Error creating proposal template:', error);
      throw error;
    }
  }

  /**
   * Update a proposal template
   */
  async updateTemplate(
    id: string,
    data: {
      name?: string;
      description?: string;
      category?: string;
      content?: string;
      sections?: Array<{
        id: string;
        name: string;
        type: string;
        content: string;
        isRequired: boolean;
        order: number;
      }>;
      variables?: string[];
      tags?: string[];
      isDefault?: boolean;
      customFields?: Record<string, any>;
    }
  ): Promise<IProposalTemplate | null> {
    try {
      // Extract variables from content if content is provided but variables are not
      if (data.content && !data.variables) {
        data.variables = this.extractVariables(data.content);
      }

      return await ProposalTemplate.findByIdAndUpdate(id, data, { new: true });
    } catch (error) {
      console.error(`Error updating template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a proposal template
   */
  async deleteTemplate(id: string): Promise<boolean> {
    try {
      const result = await ProposalTemplate.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      console.error(`Error deleting template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Record template usage
   */
  async recordTemplateUsage(
    id: string,
    wasSuccessful: boolean = false
  ): Promise<IProposalTemplate | null> {
    try {
      const update: any = { $inc: { usageCount: 1 } };

      if (wasSuccessful) {
        update.$inc.successCount = 1;
      }

      return await ProposalTemplate.findByIdAndUpdate(id, update, { new: true });
    } catch (error) {
      console.error(`Error recording usage for template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all proposals
   */
  async getAllProposals(
    filter: {
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      status?: string;
      createdBy?: string;
      search?: string;
    } = {}
  ): Promise<IProposal[]> {
    try {
      const query: any = {};

      if (filter.opportunityId) {
        query.opportunityId = new mongoose.Types.ObjectId(filter.opportunityId);
      }

      if (filter.contactId) {
        query.contactId = new mongoose.Types.ObjectId(filter.contactId);
      }

      if (filter.companyId) {
        query.companyId = new mongoose.Types.ObjectId(filter.companyId);
      }

      if (filter.status) {
        query.status = filter.status;
      }

      if (filter.createdBy) {
        query.createdBy = new mongoose.Types.ObjectId(filter.createdBy);
      }

      if (filter.search) {
        query.$text = { $search: filter.search };
      }

      return await Proposal.find(query).sort({ createdAt: -1 });
    } catch (error) {
      console.error('Error getting proposals:', error);
      throw error;
    }
  }

  /**
   * Get proposal by ID
   */
  async getProposalById(id: string): Promise<IProposal | null> {
    try {
      return await Proposal.findById(id);
    } catch (error) {
      console.error(`Error getting proposal with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new proposal
   */
  async createProposal(
    data: {
      name: string;
      description?: string;
      opportunityId: string;
      contactId?: string;
      companyId?: string;
      templateId?: string;
      content: string;
      sections: Array<{
        id: string;
        name: string;
        type: string;
        content: string;
        order: number;
      }>;
      value: number;
      currency?: string;
      validUntil?: Date;
      tags?: string[];
      notes?: string;
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<IProposal> {
    try {
      const proposal = new Proposal({
        ...data,
        status: 'draft',
        opportunityId: new mongoose.Types.ObjectId(data.opportunityId),
        contactId: data.contactId ? new mongoose.Types.ObjectId(data.contactId) : undefined,
        companyId: data.companyId ? new mongoose.Types.ObjectId(data.companyId) : undefined,
        templateId: data.templateId ? new mongoose.Types.ObjectId(data.templateId) : undefined,
        createdBy: new mongoose.Types.ObjectId(userId)
      });

      return await proposal.save();
    } catch (error) {
      console.error('Error creating proposal:', error);
      throw error;
    }
  }

  /**
   * Update a proposal
   */
  async updateProposal(
    id: string,
    data: {
      name?: string;
      description?: string;
      content?: string;
      sections?: Array<{
        id: string;
        name: string;
        type: string;
        content: string;
        order: number;
      }>;
      status?: string;
      value?: number;
      currency?: string;
      validUntil?: Date;
      sentAt?: Date;
      viewedAt?: Date;
      acceptedAt?: Date;
      rejectedAt?: Date;
      documentUrl?: string;
      documentId?: string;
      tags?: string[];
      notes?: string;
      customFields?: Record<string, any>;
    }
  ): Promise<IProposal | null> {
    try {
      // Convert documentId to ObjectId if provided
      if (data.documentId) {
        data.documentId = new mongoose.Types.ObjectId(data.documentId) as any;
      }

      return await Proposal.findByIdAndUpdate(id, data, { new: true });
    } catch (error) {
      console.error(`Error updating proposal with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a proposal
   */
  async deleteProposal(id: string): Promise<boolean> {
    try {
      const result = await Proposal.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      console.error(`Error deleting proposal with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Generate a proposal using AI with GraphRAG context
   */
  async generateProposal(
    params: {
      opportunityId: string;
      templateId?: string;
      contactId?: string;
      companyId?: string;
    },
    userId: string
  ): Promise<IProposal> {
    try {
      const { opportunityId, templateId, contactId, companyId } = params;

      // Get the tenant ID from the user
      const user = await mongoose.model('User').findById(userId);
      if (!user) {
        throw new Error('User not found');
      }
      const tenantId = user.tenantId?.toString() || 'default';

      // Get the opportunity
      const opportunity = await Opportunity.findById(opportunityId);

      if (!opportunity) {
        throw new Error(`Opportunity with ID ${opportunityId} not found`);
      }

      // Get related data
      const [contact, company, activities, template] = await Promise.all([
        contactId ? Contact.findById(contactId) :
          opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
        companyId ? Company.findById(companyId) :
          opportunity.companyId ? Company.findById(opportunity.companyId) : null,
        Activity.find({ opportunityId: new mongoose.Types.ObjectId(opportunityId) })
          .sort({ date: -1 })
          .limit(10),
        templateId ? ProposalTemplate.findById(templateId) :
          ProposalTemplate.findOne({ isDefault: true, category: 'sales' })
      ]);

      if (!template) {
        throw new Error('No suitable template found for proposal generation');
      }

      // Get GraphRAG context for proposal generation
      logger.info('Getting GraphRAG context for proposal generation');
      const graphRAGContext = await graphRAGService.getProposalGeneratorContext({
        opportunityId,
        contactId: contactId || (opportunity.contactId ? opportunity.contactId.toString() : undefined),
        companyId: companyId || (opportunity.companyId ? opportunity.companyId.toString() : undefined),
        tenantId
      });

      // Generate the proposal using AI with GraphRAG context
      const proposalData = await this.generateProposalWithAI(
        opportunity,
        template,
        contact,
        company,
        activities,
        graphRAGContext
      );

      // Create and save the proposal
      const proposal = await this.createProposal(
        {
          name: proposalData.name,
          description: proposalData.description,
          opportunityId: opportunityId,
          contactId: contactId || (opportunity.contactId ? opportunity.contactId.toString() : undefined),
          companyId: companyId || (opportunity.companyId ? opportunity.companyId.toString() : undefined),
          templateId: template._id.toString(),
          content: proposalData.content,
          sections: proposalData.sections,
          value: proposalData.value,
          currency: proposalData.currency,
          validUntil: proposalData.validUntil ? new Date(proposalData.validUntil) : undefined,
          tags: proposalData.tags,
          notes: proposalData.notes
        },
        userId
      );

      // Record template usage
      await this.recordTemplateUsage(template._id.toString());

      // Record wizard usage for analytics
      await graphRAGService.recordWizardUsage({
        wizardType: 'proposal_generator',
        userId,
        tenantId,
        entityId: opportunityId,
        entityType: 'opportunity',
        result: 'success',
        metadata: {
          proposalId: proposal._id.toString(),
          templateId: template._id.toString()
        }
      });

      return proposal;
    } catch (error) {
      logger.error('Error generating proposal:', error);

      // Record wizard usage failure
      if (params.userId) {
        try {
          const user = await mongoose.model('User').findById(params.userId);
          const tenantId = user?.tenantId?.toString() || 'default';

          await graphRAGService.recordWizardUsage({
            wizardType: 'proposal_generator',
            userId: params.userId,
            tenantId,
            entityId: params.opportunityId,
            entityType: 'opportunity',
            result: 'failure',
            metadata: {
              error: error.message
            }
          });
        } catch (recordError) {
          logger.error('Error recording wizard usage failure:', recordError);
        }
      }

      throw error;
    }
  }

  /**
   * Generate a document from a proposal in the specified format
   */
  async generateProposalDocument(
    proposalId: string,
    userId: string,
    format: 'html' | 'pdf' | 'docx' | 'markdown' | 'claude-html' = 'pdf'
  ): Promise<{
    documentUrl: string;
    format: string;
    isDownloadable: boolean;
  }> {
    try {
      // Get the proposal
      const proposal = await this.getProposalById(proposalId);

      if (!proposal) {
        throw new Error(`Proposal with ID ${proposalId} not found`);
      }

      // Define which formats are downloadable
      const downloadableFormats = ['pdf', 'docx', 'markdown'];
      const isDownloadable = downloadableFormats.includes(format);

      // Convert the proposal to HTML as the base format
      const htmlContent = this.convertProposalToHtml(proposal);

      // Handle different formats
      switch (format) {
        case 'pdf':
          return await this.generatePdfDocument(proposal, htmlContent, userId);
        case 'docx':
          return await this.generateDocxDocument(proposal, htmlContent, userId);
        case 'markdown':
          return await this.generateMarkdownDocument(proposal, htmlContent, userId);
        case 'claude-html':
          return await this.generateClaudeHtmlDocument(proposal, userId);
        case 'html':
        default:
          return await this.generateHtmlDocument(proposal, htmlContent, userId);
      }
    } catch (error) {
      console.error('Error generating proposal document:', error);
      throw error;
    }
  }

  /**
   * Generate a PDF document from a proposal
   */
  private async generatePdfDocument(
    proposal: IProposal,
    htmlContent: string,
    userId: string
  ): Promise<{
    documentUrl: string;
    format: string;
    isDownloadable: boolean;
  }> {
    try {
      // Try to use the AI service to convert HTML to PDF
      const pdfResponse = await axios.post(
        `${AI_SERVICE_URL}/documents/html-to-pdf`,
        {
          html: htmlContent,
          filename: `proposal_${proposal._id}.pdf`
        },
        {
          responseType: 'arraybuffer',
          timeout: 30000 // 30 second timeout
        }
      );

      // Create a buffer from the response
      const pdfBuffer = Buffer.from(pdfResponse.data);

      // Save the PDF to the document storage
      const document = new DocumentModel({
        name: proposal.name,
        description: `Proposal for ${proposal.name}`,
        fileType: 'pdf',
        mimeType: 'application/pdf',
        size: pdfBuffer.length,
        url: `/api/documents/${proposal._id}/content`,
        path: `/documents/proposal_${proposal._id}.pdf`,
        isPublic: false,
        owner: new mongoose.Types.ObjectId(userId),
        relatedTo: {
          type: 'opportunity',
          id: proposal.opportunityId
        },
        tags: ['proposal', 'ai-generated'],
        metadata: {
          proposalId: proposal._id.toString(),
          generatedAt: new Date().toISOString(),
          format: 'pdf'
        }
      });

      await document.save();

      // Save the PDF content to a file
      const fs = require('fs');
      const path = require('path');
      const uploadsDir = path.join(process.cwd(), 'uploads');

      // Ensure the uploads directory exists
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const filePath = path.join(uploadsDir, `proposal_${proposal._id}.pdf`);
      fs.writeFileSync(filePath, pdfBuffer);

      // Update the proposal with the document information
      const availableFormats = proposal.availableFormats || [];
      if (!availableFormats.includes('pdf')) {
        availableFormats.push('pdf');
      }

      await this.updateProposal(proposal._id.toString(), {
        documentUrl: `/api/documents/${document._id}/content`,
        documentId: document._id.toString(),
        format: 'pdf',
        availableFormats
      });

      return {
        documentUrl: `/api/documents/${document._id}/content`,
        format: 'pdf',
        isDownloadable: true
      };
    } catch (error) {
      console.error('Error generating PDF document:', error);
      // Fall back to HTML if PDF generation fails
      return await this.generateHtmlDocument(proposal, htmlContent, userId);
    }
  }

  /**
   * Generate an HTML document from a proposal
   */
  private async generateHtmlDocument(
    proposal: IProposal,
    htmlContent: string,
    userId: string
  ): Promise<{
    documentUrl: string;
    format: string;
    isDownloadable: boolean;
  }> {
    // Save HTML as a document
    const document = new DocumentModel({
      name: proposal.name,
      description: `Proposal for ${proposal.name}`,
      fileType: 'html',
      mimeType: 'text/html',
      size: htmlContent.length,
      url: `/api/documents/${proposal._id}/content`,
      path: `/documents/proposal_${proposal._id}.html`,
      isPublic: false,
      owner: new mongoose.Types.ObjectId(userId),
      relatedTo: {
        type: 'opportunity',
        id: proposal.opportunityId
      },
      tags: ['proposal', 'ai-generated'],
      metadata: {
        proposalId: proposal._id.toString(),
        generatedAt: new Date().toISOString(),
        format: 'html'
      }
    });

    await document.save();

    // Save the HTML content to a file
    const fs = require('fs');
    const path = require('path');
    const uploadsDir = path.join(process.cwd(), 'uploads');

    // Ensure the uploads directory exists
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const filePath = path.join(uploadsDir, `proposal_${proposal._id}.html`);
    fs.writeFileSync(filePath, htmlContent);

    // Update the proposal with the document information
    const availableFormats = proposal.availableFormats || [];
    if (!availableFormats.includes('html')) {
      availableFormats.push('html');
    }

    await this.updateProposal(proposal._id.toString(), {
      documentUrl: `/api/documents/${document._id}/content`,
      documentId: document._id.toString(),
      format: 'html',
      availableFormats
    });

    return {
      documentUrl: `/api/documents/${document._id}/content`,
      format: 'html',
      isDownloadable: false // HTML is viewable but not downloadable
    };
  }

  /**
   * Generate a DOCX document from a proposal
   */
  private async generateDocxDocument(
    proposal: IProposal,
    htmlContent: string,
    userId: string
  ): Promise<{
    documentUrl: string;
    format: string;
    isDownloadable: boolean;
  }> {
    try {
      // Use html-docx-js to convert HTML to DOCX
      const htmlDocx = require('html-docx-js');

      // Enhance the HTML with better styling for DOCX
      const enhancedHtml = this.enhanceHtmlForDocx(htmlContent, proposal);

      // Convert HTML to DOCX
      const docxBuffer = htmlDocx.asBlob(enhancedHtml);

      // Save the DOCX to the document storage
      const document = new DocumentModel({
        name: proposal.name,
        description: `Proposal for ${proposal.name}`,
        fileType: 'docx',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        size: docxBuffer.length,
        url: `/api/documents/${proposal._id}/content`,
        path: `/documents/proposal_${proposal._id}.docx`,
        isPublic: false,
        owner: new mongoose.Types.ObjectId(userId),
        relatedTo: {
          type: 'opportunity',
          id: proposal.opportunityId
        },
        tags: ['proposal', 'ai-generated'],
        metadata: {
          proposalId: proposal._id.toString(),
          generatedAt: new Date().toISOString(),
          format: 'docx'
        }
      });

      await document.save();

      // Save the DOCX content to a file
      const fs = require('fs');
      const path = require('path');
      const uploadsDir = path.join(process.cwd(), 'uploads');

      // Ensure the uploads directory exists
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const filePath = path.join(uploadsDir, `proposal_${proposal._id}.docx`);
      fs.writeFileSync(filePath, docxBuffer);

      // Update the proposal with the document information
      const availableFormats = proposal.availableFormats || [];
      if (!availableFormats.includes('docx')) {
        availableFormats.push('docx');
      }

      await this.updateProposal(proposal._id.toString(), {
        documentUrl: `/api/documents/${document._id}/content`,
        documentId: document._id.toString(),
        format: 'docx',
        availableFormats
      });

      return {
        documentUrl: `/api/documents/${document._id}/content`,
        format: 'docx',
        isDownloadable: true
      };
    } catch (error) {
      console.error('Error generating DOCX document:', error);
      // Fall back to HTML if DOCX generation fails
      return await this.generateHtmlDocument(proposal, htmlContent, userId);
    }
  }

  /**
   * Enhance HTML for DOCX conversion
   */
  private enhanceHtmlForDocx(htmlContent: string, proposal: IProposal): string {
    // Add DOCX-specific styling
    const docxStyles = `
      <style>
        body {
          font-family: 'Calibri', sans-serif;
          color: #333333;
          line-height: 1.5;
        }
        h1 {
          color: #2c5282;
          font-size: 24pt;
          margin-bottom: 16pt;
          border-bottom: 1pt solid #2c5282;
          padding-bottom: 8pt;
        }
        h2 {
          color: #3182ce;
          font-size: 18pt;
          margin-top: 16pt;
          margin-bottom: 12pt;
        }
        h3 {
          color: #4299e1;
          font-size: 14pt;
          margin-top: 12pt;
          margin-bottom: 8pt;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 16pt 0;
        }
        th {
          background-color: #ebf8ff;
          color: #2c5282;
          font-weight: bold;
          text-align: left;
          padding: 8pt;
          border: 1pt solid #bee3f8;
        }
        td {
          padding: 8pt;
          border: 1pt solid #bee3f8;
        }
        .proposal-header {
          text-align: center;
          margin-bottom: 24pt;
        }
        .proposal-footer {
          text-align: center;
          margin-top: 24pt;
          font-size: 9pt;
          color: #718096;
        }
        .section {
          margin-bottom: 16pt;
        }
        .highlight {
          background-color: #ebf8ff;
          padding: 8pt;
          border-left: 4pt solid #3182ce;
        }
        .price {
          font-weight: bold;
          color: #2c5282;
        }
      </style>
    `;

    // Add a header with company branding
    const header = `
      <div class="proposal-header">
        <h1>${proposal.name}</h1>
        <p>Prepared for: ${proposal.companyId ? 'Company' : 'Client'}</p>
        <p>Date: ${new Date().toLocaleDateString()}</p>
        <p>Valid until: ${proposal.validUntil ? new Date(proposal.validUntil).toLocaleDateString() : 'N/A'}</p>
      </div>
    `;

    // Add a footer with contact information
    const footer = `
      <div class="proposal-footer">
        <p>Generated by Aizako CRM | Contact: <EMAIL> | www.aizako.com</p>
        <p>Proposal ID: ${proposal._id}</p>
      </div>
    `;

    // Insert our styles and header/footer into the HTML content
    let enhancedHtml = htmlContent;

    // Add styles to the head
    if (enhancedHtml.includes('<head>')) {
      enhancedHtml = enhancedHtml.replace('<head>', `<head>${docxStyles}`);
    } else {
      enhancedHtml = `<html><head>${docxStyles}</head><body>${enhancedHtml}</body></html>`;
    }

    // Add header at the beginning of the body
    if (enhancedHtml.includes('<body>')) {
      enhancedHtml = enhancedHtml.replace('<body>', `<body>${header}`);
    } else if (enhancedHtml.includes('</head>')) {
      enhancedHtml = enhancedHtml.replace('</head>', `</head><body>${header}`);
    }

    // Add footer at the end of the body
    if (enhancedHtml.includes('</body>')) {
      enhancedHtml = enhancedHtml.replace('</body>', `${footer}</body>`);
    } else {
      enhancedHtml = `${enhancedHtml}${footer}`;
    }

    return enhancedHtml;
  }

  /**
   * Generate a Markdown document from a proposal
   */
  private async generateMarkdownDocument(
    proposal: IProposal,
    htmlContent: string,
    userId: string
  ): Promise<{
    documentUrl: string;
    format: string;
    isDownloadable: boolean;
  }> {
    try {
      // Use turndown to convert HTML to Markdown
      const TurndownService = require('turndown');
      const turndownService = new TurndownService({
        headingStyle: 'atx',
        codeBlockStyle: 'fenced',
        emDelimiter: '*'
      });

      // Add rules for better Markdown conversion
      turndownService.addRule('tables', {
        filter: ['table'],
        replacement: function(content: string, node: any) {
          // This is a simplified table conversion
          // For complex tables, more sophisticated handling would be needed
          const rows = node.querySelectorAll('tr');
          let markdownTable = '';

          // Process each row
          for (let i = 0; i < rows.length; i++) {
            const cells = rows[i].querySelectorAll('th, td');
            const rowContent = [];

            // Process each cell in the row
            for (let j = 0; j < cells.length; j++) {
              rowContent.push(cells[j].textContent.trim());
            }

            markdownTable += '| ' + rowContent.join(' | ') + ' |\n';

            // Add header separator after the first row
            if (i === 0) {
              const separators = [];
              for (let j = 0; j < cells.length; j++) {
                separators.push('---');
              }
              markdownTable += '| ' + separators.join(' | ') + ' |\n';
            }
          }

          return '\n\n' + markdownTable + '\n\n';
        }
      });

      // Enhance the HTML before conversion
      const enhancedHtml = this.enhanceHtmlForMarkdown(htmlContent, proposal);

      // Convert HTML to Markdown
      let markdownContent = turndownService.turndown(enhancedHtml);

      // Add a nice header
      const header = `# ${proposal.name}\n\n` +
        `*Proposal for ${proposal.companyId ? 'Company' : 'Client'}*\n\n` +
        `**Date:** ${new Date().toLocaleDateString()}\n\n` +
        `**Valid until:** ${proposal.validUntil ? new Date(proposal.validUntil).toLocaleDateString() : 'N/A'}\n\n` +
        `---\n\n`;

      // Add a footer
      const footer = `\n\n---\n\n` +
        `*Generated by Aizako CRM | Contact: <EMAIL> | www.aizako.com*\n\n` +
        `Proposal ID: ${proposal._id}\n`;

      // Combine everything
      markdownContent = header + markdownContent + footer;

      // Save the Markdown to the document storage
      const document = new DocumentModel({
        name: proposal.name,
        description: `Proposal for ${proposal.name}`,
        fileType: 'md',
        mimeType: 'text/markdown',
        size: markdownContent.length,
        url: `/api/documents/${proposal._id}/content`,
        path: `/documents/proposal_${proposal._id}.md`,
        isPublic: false,
        owner: new mongoose.Types.ObjectId(userId),
        relatedTo: {
          type: 'opportunity',
          id: proposal.opportunityId
        },
        tags: ['proposal', 'ai-generated'],
        metadata: {
          proposalId: proposal._id.toString(),
          generatedAt: new Date().toISOString(),
          format: 'markdown'
        }
      });

      await document.save();

      // Save the Markdown content to a file
      const fs = require('fs');
      const path = require('path');
      const uploadsDir = path.join(process.cwd(), 'uploads');

      // Ensure the uploads directory exists
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const filePath = path.join(uploadsDir, `proposal_${proposal._id}.md`);
      fs.writeFileSync(filePath, markdownContent);

      // Update the proposal with the document information
      const availableFormats = proposal.availableFormats || [];
      if (!availableFormats.includes('markdown')) {
        availableFormats.push('markdown');
      }

      await this.updateProposal(proposal._id.toString(), {
        documentUrl: `/api/documents/${document._id}/content`,
        documentId: document._id.toString(),
        format: 'markdown',
        availableFormats
      });

      return {
        documentUrl: `/api/documents/${document._id}/content`,
        format: 'markdown',
        isDownloadable: true
      };
    } catch (error) {
      console.error('Error generating Markdown document:', error);
      // Fall back to HTML if Markdown generation fails
      return await this.generateHtmlDocument(proposal, htmlContent, userId);
    }
  }

  /**
   * Enhance HTML for Markdown conversion
   */
  private enhanceHtmlForMarkdown(htmlContent: string, proposal: IProposal): string {
    // Simplify the HTML to make it more Markdown-friendly
    let enhancedHtml = htmlContent;

    // Remove complex styling that doesn't translate well to Markdown
    enhancedHtml = enhancedHtml.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');

    // Replace div tags with more semantic elements
    enhancedHtml = enhancedHtml.replace(/<div[^>]*class="section"[^>]*>/gi, '<section>');
    enhancedHtml = enhancedHtml.replace(/<\/div>/gi, '</section>');

    // Ensure headings have proper hierarchy
    enhancedHtml = enhancedHtml.replace(/<h1[^>]*>([\s\S]*?)<\/h1>/gi, '<h1>$1</h1>');

    // Add emphasis to important points
    enhancedHtml = enhancedHtml.replace(/<strong[^>]*>([\s\S]*?)<\/strong>/gi, '<strong>$1</strong>');
    enhancedHtml = enhancedHtml.replace(/<em[^>]*>([\s\S]*?)<\/em>/gi, '<em>$1</em>');

    // Simplify lists
    enhancedHtml = enhancedHtml.replace(/<ul[^>]*>/gi, '<ul>');
    enhancedHtml = enhancedHtml.replace(/<ol[^>]*>/gi, '<ol>');

    return enhancedHtml;
  }

  /**
   * Generate a Claude-enhanced HTML document with beautiful design
   */
  private async generateClaudeHtmlDocument(
    proposal: IProposal,
    userId: string
  ): Promise<{
    documentUrl: string;
    format: string;
    isDownloadable: boolean;
  }> {
    try {
      // Get related data for context
      const [contact, company, opportunity] = await Promise.all([
        proposal.contactId ? Contact.findById(proposal.contactId) : null,
        proposal.companyId ? Company.findById(proposal.companyId) : null,
        Opportunity.findById(proposal.opportunityId)
      ]);

      // Generate beautiful HTML with Claude
      const claudeHtml = await this.generateBeautifulHtmlWithClaude(proposal, contact, company, opportunity);

      // Save the Claude-generated HTML to the document storage
      const document = new DocumentModel({
        name: proposal.name,
        description: `Proposal for ${proposal.name} (Claude-enhanced design)`,
        fileType: 'html',
        mimeType: 'text/html',
        size: claudeHtml.length,
        url: `/api/documents/${proposal._id}/claude-content`,
        path: `/documents/proposal_${proposal._id}_claude.html`,
        isPublic: false,
        owner: new mongoose.Types.ObjectId(userId),
        relatedTo: {
          type: 'opportunity',
          id: proposal.opportunityId
        },
        tags: ['proposal', 'ai-generated', 'claude-enhanced'],
        metadata: {
          proposalId: proposal._id.toString(),
          generatedAt: new Date().toISOString(),
          format: 'claude-html'
        }
      });

      await document.save();

      // Save the HTML content to a file
      const fs = require('fs');
      const path = require('path');
      const uploadsDir = path.join(process.cwd(), 'uploads');

      // Ensure the uploads directory exists
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const filePath = path.join(uploadsDir, `proposal_${proposal._id}_claude.html`);
      fs.writeFileSync(filePath, claudeHtml);

      // Update the proposal with the document information
      const availableFormats = proposal.availableFormats || [];
      if (!availableFormats.includes('claude-html')) {
        availableFormats.push('claude-html');
      }

      await this.updateProposal(proposal._id.toString(), {
        documentUrl: `/api/documents/${document._id}/content`,
        documentId: document._id.toString(),
        format: 'claude-html',
        availableFormats
      });

      return {
        documentUrl: `/api/documents/${document._id}/content`,
        format: 'claude-html',
        isDownloadable: false // Claude HTML is viewable but not downloadable
      };
    } catch (error) {
      console.error('Error generating Claude HTML document:', error);
      // Fall back to regular HTML if Claude generation fails
      const htmlContent = this.convertProposalToHtml(proposal);
      return await this.generateHtmlDocument(proposal, htmlContent, userId);
    }
  }

  /**
   * Generate beautiful HTML with Claude
   */
  private async generateBeautifulHtmlWithClaude(
    proposal: IProposal,
    contact: IContact | null,
    company: ICompany | null,
    opportunity: IOpportunity | null
  ): Promise<string> {
    try {
      // Prepare data for Claude
      const data = {
        proposal: {
          name: proposal.name,
          description: proposal.description,
          sections: proposal.sections,
          value: proposal.value,
          currency: proposal.currency,
          validUntil: proposal.validUntil
        },
        contact: contact ? {
          name: `${contact.firstName} ${contact.lastName}`,
          title: contact.title,
          email: contact.email
        } : null,
        company: company ? {
          name: company.name,
          industry: company.industry,
          size: company.size
        } : null,
        opportunity: opportunity ? {
          name: opportunity.name,
          stage: opportunity.stage,
          value: opportunity.value,
          currency: opportunity.currency
        } : null
      };

      // Create prompt for Claude with enhanced design requirements
      const prompt = `
        Create a beautiful, professional HTML proposal based on the provided data.

        The proposal should have:
        1. A modern, visually appealing design with vibrant colors and gradients
        2. Professional typography with a mix of serif and sans-serif fonts
        3. Color accents that create visual interest and highlight important information
        4. Responsive layout with proper spacing and alignment
        5. Clear sections with strong visual hierarchy
        6. Subtle animations or transitions for interactive elements
        7. Visual elements like icons, dividers, and callout boxes
        8. A professional header with logo placeholder and company information
        9. A well-designed pricing section that clearly communicates value
        10. A visually appealing footer with contact information

        Design specifications:
        - Use a color palette with primary color #3182CE (blue) and complementary colors
        - Include gradient backgrounds for section headers (#EBF8FF to #BEE3F8)
        - Use box shadows and rounded corners for cards and containers
        - Implement a clean, readable typography system with headings in #2C5282 (dark blue)
        - Add subtle hover effects for interactive elements
        - Include appropriate icons from Font Awesome or similar (embedded as SVG)

        The proposal should be for: ${proposal.name}
        Value: ${proposal.currency}${proposal.value}
        ${proposal.validUntil ? `Valid until: ${new Date(proposal.validUntil).toLocaleDateString()}` : ''}

        Include these sections:
        ${proposal.sections.map(s => `- ${s.name} (${s.type}): ${s.content.substring(0, 100)}...`).join('\n')}

        Design requirements:
        1. Use a modern, professional design with clean typography
        2. Include a responsive layout that works on all devices
        3. Use appropriate colors for headings, backgrounds, and accents
        4. Include styled tables for pricing information
        5. Add appropriate spacing and visual hierarchy
        6. Include a header with logo placeholder and company information
        7. Add a footer with contact details and validity information

        The HTML should be complete with all CSS included in a <style> tag.
        Do not use external resources or libraries.

        Return ONLY the HTML code, nothing else.
      `;

      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/crew`, {
          task: prompt,
          userId: 1, // System user
          context: data
        }, {
          timeout: 30000 // 30 second timeout
        });

        if (aiResponse.data && aiResponse.data.response) {
          return aiResponse.data.response;
        }
      } catch (aiError) {
        console.error('Error using AI service, falling back to Voyage AI:', aiError);
      }

      // Fallback to Voyage AI if the AI service fails
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert web designer specializing in creating beautiful HTML proposals.
              Focus on creating a modern, visually appealing design with vibrant colors, gradients, and professional typography.
              Use a color palette with primary color #3182CE (blue) and complementary colors.
              Include gradient backgrounds, box shadows, and rounded corners for a polished look.
              Add visual elements like icons, dividers, and callout boxes to enhance the design.
              Ensure the design is responsive and works well on all devices.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.7,
          max_tokens: 4000
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      if (response.data && response.data.choices && response.data.choices[0].message.content) {
        return response.data.choices[0].message.content;
      }

      throw new Error('Failed to generate beautiful HTML with Claude');
    } catch (error) {
      console.error('Error generating beautiful HTML with Claude:', error);
      throw error;
    }
  }

  /**
   * Send a proposal to the client
   */
  async sendProposal(
    proposalId: string
  ): Promise<IProposal | null> {
    try {
      // Get the proposal
      const proposal = await this.getProposalById(proposalId);

      if (!proposal) {
        throw new Error(`Proposal with ID ${proposalId} not found`);
      }

      // Check if the proposal has a document
      if (!proposal.documentUrl) {
        throw new Error('Proposal does not have a document. Generate a document first.');
      }

      // Update the proposal status and sent date
      return await this.updateProposal(proposalId, {
        status: 'sent',
        sentAt: new Date()
      });

      // TODO: Implement email sending functionality
    } catch (error) {
      console.error(`Error sending proposal with ID ${proposalId}:`, error);
      throw error;
    }
  }

  /**
   * Mark a proposal as viewed
   */
  async markProposalAsViewed(
    proposalId: string
  ): Promise<IProposal | null> {
    try {
      // Get the proposal
      const proposal = await this.getProposalById(proposalId);

      if (!proposal) {
        throw new Error(`Proposal with ID ${proposalId} not found`);
      }

      // Update the proposal status and viewed date
      return await this.updateProposal(proposalId, {
        status: 'viewed',
        viewedAt: new Date()
      });
    } catch (error) {
      console.error(`Error marking proposal with ID ${proposalId} as viewed:`, error);
      throw error;
    }
  }

  /**
   * Mark a proposal as accepted
   */
  async markProposalAsAccepted(
    proposalId: string
  ): Promise<IProposal | null> {
    try {
      // Get the proposal
      const proposal = await this.getProposalById(proposalId);

      if (!proposal) {
        throw new Error(`Proposal with ID ${proposalId} not found`);
      }

      // Update the proposal status and accepted date
      return await this.updateProposal(proposalId, {
        status: 'accepted',
        acceptedAt: new Date()
      });

      // TODO: Update the opportunity status if needed
    } catch (error) {
      console.error(`Error marking proposal with ID ${proposalId} as accepted:`, error);
      throw error;
    }
  }

  /**
   * Mark a proposal as rejected
   */
  async markProposalAsRejected(
    proposalId: string,
    reason?: string
  ): Promise<IProposal | null> {
    try {
      // Get the proposal
      const proposal = await this.getProposalById(proposalId);

      if (!proposal) {
        throw new Error(`Proposal with ID ${proposalId} not found`);
      }

      // Update the proposal status and rejected date
      return await this.updateProposal(proposalId, {
        status: 'rejected',
        rejectedAt: new Date(),
        notes: reason ? (proposal.notes ? `${proposal.notes}\n\nRejection reason: ${reason}` : `Rejection reason: ${reason}`) : proposal.notes
      });
    } catch (error) {
      console.error(`Error marking proposal with ID ${proposalId} as rejected:`, error);
      throw error;
    }
  }

  /**
   * Create a shareable link for a proposal
   */
  async createShareableLink(
    id: string,
    userId: string,
    options: {
      expirationDays: number;
      format: 'html' | 'pdf' | 'docx' | 'markdown' | 'claude-html';
    }
  ): Promise<{
    shareableUrl: string;
    expiresAt: Date;
    format: string;
  } | null> {
    try {
      // Get the proposal
      const proposal = await Proposal.findById(id);

      if (!proposal) {
        return null;
      }

      // Generate a unique token
      const crypto = require('crypto');
      const token = crypto.randomBytes(32).toString('hex');

      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + options.expirationDays);

      // Generate the document in the requested format if not already available
      if (!proposal.availableFormats?.includes(options.format)) {
        await this.generateProposalDocument(id, userId, options.format);
      }

      // Create a shareable link record
      const shareableLink = {
        proposalId: proposal._id,
        token,
        format: options.format,
        expiresAt,
        createdBy: new mongoose.Types.ObjectId(userId),
        createdAt: new Date()
      };

      // Store the shareable link in the proposal
      await Proposal.findByIdAndUpdate(
        id,
        {
          $push: {
            'customFields.shareableLinks': shareableLink
          }
        }
      );

      // Track the share in analytics
      await analyticsService.trackShare(
        id,
        userId,
        'link',
        token,
        expiresAt
      );

      // Generate the shareable URL
      const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const shareableUrl = `${baseUrl}/shared/proposals/${token}`;

      return {
        shareableUrl,
        expiresAt,
        format: options.format
      };
    } catch (error) {
      console.error(`Error creating shareable link for proposal with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Export proposal to email
   */
  async exportProposalToEmail(
    id: string,
    userId: string,
    options: {
      email: string;
      format: 'pdf' | 'docx' | 'markdown';
      message: string;
    }
  ): Promise<boolean> {
    try {
      // Get the proposal
      const proposal = await Proposal.findById(id);

      if (!proposal) {
        return false;
      }

      // Generate the document in the requested format if not already available
      if (!proposal.availableFormats?.includes(options.format)) {
        await this.generateProposalDocument(id, userId, options.format);
      }

      // Get the document
      const document = await DocumentModel.findById(proposal.documentId);

      if (!document) {
        return false;
      }

      // Get the user who is sending the email
      const user = await mongoose.model('User').findById(userId);

      if (!user) {
        throw new Error('User not found');
      }

      // Get the opportunity, contact, and company
      const [opportunity, contact, company] = await Promise.all([
        proposal.opportunityId ? Opportunity.findById(proposal.opportunityId) : null,
        proposal.contactId ? Contact.findById(proposal.contactId) : null,
        proposal.companyId ? Company.findById(proposal.companyId) : null
      ]);

      // Get the file content
      const fs = require('fs');
      const path = require('path');
      const filePath = path.join(process.cwd(), 'uploads', `proposal_${proposal._id}.${options.format === 'markdown' ? 'md' : options.format}`);

      let fileContent: Buffer | null = null;
      if (fs.existsSync(filePath)) {
        fileContent = fs.readFileSync(filePath);
      }

      // Prepare template data
      const templateData = {
        senderName: user.fullName,
        recipientName: contact ? `${contact.firstName} ${contact.lastName}` : 'Client',
        proposalName: proposal.name,
        proposalUrl: `${process.env.BASE_URL || 'http://localhost:3000'}/proposals/${proposal._id}`,
        expirationDate: proposal.validUntil ? new Date(proposal.validUntil).toLocaleDateString() : 'N/A',
        message: options.message || '',
        companyName: company ? company.name : '',
        opportunityName: opportunity ? opportunity.name : '',
        value: `${proposal.currency}${proposal.value.toLocaleString()}`
      };

      // Create a share token for tracking
      const crypto = require('crypto');
      const shareToken = crypto.randomBytes(16).toString('hex');
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 30); // 30 days expiration

      // Track the share
      await analyticsService.trackShare(
        id,
        userId,
        'email',
        shareToken,
        expiresAt,
        undefined,
        options.email
      );

      // Send the email
      const attachments = fileContent ? [
        {
          filename: `${proposal.name}.${options.format === 'markdown' ? 'md' : options.format}`,
          content: fileContent,
          contentType: options.format === 'pdf' ? 'application/pdf' :
                      options.format === 'docx' ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' :
                      'text/markdown'
        }
      ] : undefined;

      const emailSent = await emailService.sendTemplateEmail(
        userId,
        'proposalShare',
        options.email,
        templateData,
        attachments
      );

      if (!emailSent) {
        throw new Error('Failed to send email');
      }

      // Record the export in the proposal
      await Proposal.findByIdAndUpdate(
        id,
        {
          $push: {
            'customFields.exports': {
              type: 'email',
              destination: options.email,
              format: options.format,
              exportedAt: new Date(),
              exportedBy: new mongoose.Types.ObjectId(userId),
              shareToken
            }
          }
        }
      );

      return true;
    } catch (error) {
      console.error(`Error exporting proposal with ID ${id} to email:`, error);
      throw error;
    }
  }

  /**
   * Export proposal to social media
   */
  async exportProposalToSocial(
    id: string,
    userId: string,
    options: {
      platform: 'linkedin' | 'twitter' | 'facebook';
      message: string;
    }
  ): Promise<boolean> {
    try {
      // Get the proposal
      const proposal = await Proposal.findById(id);

      if (!proposal) {
        return false;
      }

      // In a real implementation, this would post to the social media platform
      // For now, we'll just log the action and return success
      console.log(`Exporting proposal ${id} to ${options.platform}`);
      console.log(`Message: ${options.message}`);

      // Record the export in the proposal
      await Proposal.findByIdAndUpdate(
        id,
        {
          $push: {
            'customFields.exports': {
              type: 'social',
              destination: options.platform,
              message: options.message,
              exportedAt: new Date(),
              exportedBy: new mongoose.Types.ObjectId(userId)
            }
          }
        }
      );

      return true;
    } catch (error) {
      console.error(`Error exporting proposal with ID ${id} to social media:`, error);
      throw error;
    }
  }

  /**
   * Generate a proposal using AI with GraphRAG context
   */
  private async generateProposalWithAI(
    opportunity: IOpportunity,
    template: IProposalTemplate,
    contact: IContact | null,
    company: ICompany | null,
    activities: IActivity[],
    graphRAGContext?: {
      pricingTiers: any[];
      discountPolicy: any;
      legalBoilerplate: any;
      suggestedUpsells: any[];
      industrySpecificClauses: any[];
      similarDeals: any[];
    }
  ): Promise<{
    name: string;
    description: string;
    content: string;
    sections: Array<{
      id: string;
      name: string;
      type: string;
      content: string;
      order: number;
    }>;
    value: number;
    currency: string;
    validUntil?: string;
    tags: string[];
    notes?: string;
  }> {
    try {
      // Prepare data for AI
      const data = {
        opportunity: {
          id: opportunity._id.toString(),
          name: opportunity.name,
          value: opportunity.value,
          currency: opportunity.currency,
          stage: opportunity.stage,
          probability: opportunity.probability,
          expectedCloseDate: opportunity.expectedCloseDate,
          description: opportunity.description,
          tags: opportunity.tags,
          notes: opportunity.notes
        },
        template: {
          id: template._id.toString(),
          name: template.name,
          category: template.category,
          content: template.content,
          sections: template.sections,
          variables: template.variables
        },
        contact: contact ? {
          id: contact._id.toString(),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title,
          status: contact.status,
          notes: contact.notes,
          tags: contact.tags
        } : null,
        company: company ? {
          id: company._id.toString(),
          name: company.name,
          domain: company.domain,
          industry: company.industry,
          size: company.size,
          location: company.location,
          description: company.description,
          website: company.website,
          status: company.status,
          tags: company.tags,
          notes: company.notes
        } : null,
        activities: activities.map(activity => ({
          id: activity._id.toString(),
          type: activity.type,
          title: activity.title,
          description: activity.description,
          date: activity.date,
          completed: activity.completed,
          outcome: activity.outcome
        })),
        // Include GraphRAG context if available
        graphRAG: graphRAGContext ? {
          pricingTiers: graphRAGContext.pricingTiers || [],
          discountPolicy: graphRAGContext.discountPolicy || {},
          legalBoilerplate: graphRAGContext.legalBoilerplate || {},
          suggestedUpsells: graphRAGContext.suggestedUpsells || [],
          industrySpecificClauses: graphRAGContext.industrySpecificClauses || [],
          similarDeals: graphRAGContext.similarDeals || []
        } : null
      };

      // Create prompt for AI
      const prompt = `
        Generate a comprehensive sales proposal based on the provided opportunity, template, contact, company, activity information, and GraphRAG context.

        ${graphRAGContext ? `
        Pay special attention to the GraphRAG context which contains:
        - Pricing tiers and discount policy
        - Legal boilerplate text
        - Suggested upsells based on similar closed-won deals
        - Industry-specific clauses
        - Similar deals that were closed-won

        Use the pricing tiers, discount policy, and suggested upsells from the GraphRAG context to create a compelling pricing section.
        Include relevant legal boilerplate and industry-specific clauses in the terms section.
        Reference similar successful deals to strengthen your proposal.
        ` : ''}

        The proposal should include:
        1. A compelling title and description
        2. Customized content based on the template, filling in all variables with appropriate values
        3. Sections that address the client's needs and pain points
        4. Pricing information based on the opportunity value and pricing tiers
        5. Suggested upsells or cross-sells when appropriate
        6. Legal terms and conditions with industry-specific clauses
        7. Timeline and next steps

        Format the response as a JSON object with the following structure:
        {
          "name": "Proposal title",
          "description": "Brief description of the proposal",
          "content": "The full HTML content of the proposal",
          "sections": [
            {
              "id": "unique-id",
              "name": "Section name",
              "type": "text/pricing/timeline/team/testimonials/terms/custom",
              "content": "Section content in HTML",
              "order": 1
            }
          ],
          "value": 10000,
          "currency": "USD",
          "validUntil": "YYYY-MM-DD",
          "tags": ["tag1", "tag2"],
          "notes": "Internal notes about the proposal"
        }
      `;

      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/crew`, {
          task: prompt,
          userId: 1, // System user
          context: data
        }, {
          timeout: 30000 // 30 second timeout
        });

        if (aiResponse.data && aiResponse.data.response) {
          try {
            // Try to parse the response as JSON
            const proposalData = JSON.parse(aiResponse.data.response);
            return proposalData;
          } catch (e) {
            logger.error('Error parsing AI service response:', e);
          }
        }
      } catch (aiError) {
        logger.error('Error using AI service, falling back to Voyage AI:', aiError);
      }

      // Fallback to Voyage AI if the AI service fails
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales proposal writer for Aizako CRM.
              Your job is to create compelling, customized sales proposals based on opportunity data, templates, and GraphRAG context.
              Focus on addressing client needs and providing clear value propositions.
              Use pricing tiers, discount policies, and suggested upsells from the GraphRAG context when available.
              Include relevant legal boilerplate and industry-specific clauses in the terms section.
              Reference similar successful deals to strengthen your proposal.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.7,
          max_tokens: 2000,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );

      if (response.data && response.data.choices && response.data.choices[0].message.content) {
        const proposalData = JSON.parse(response.data.choices[0].message.content);
        return proposalData;
      }

      throw new Error('Failed to generate proposal with AI');
    } catch (error) {
      logger.error('Error generating proposal with AI:', error);
      throw error;
    }
  }

  /**
   * Convert a proposal to HTML
   */
  private convertProposalToHtml(proposal: IProposal): string {
    // Create HTML content
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${proposal.name}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          h1 {
            color: #2563eb;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
          }
          h2 {
            color: #4b5563;
            margin-top: 25px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
          }
          .proposal-header {
            text-align: center;
            margin-bottom: 40px;
          }
          .proposal-meta {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
          }
          .section {
            margin-bottom: 30px;
          }
          .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .pricing-table th, .pricing-table td {
            border: 1px solid #e5e7eb;
            padding: 10px;
            text-align: left;
          }
          .pricing-table th {
            background-color: #f3f4f6;
          }
          .pricing-total {
            font-weight: bold;
            text-align: right;
          }
          .timeline {
            margin-left: 20px;
          }
          .timeline-item {
            margin-bottom: 15px;
          }
          .timeline-date {
            font-weight: bold;
            color: #2563eb;
          }
          .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 0.8em;
            color: #9ca3af;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
          }
        </style>
      </head>
      <body>
        ${proposal.content}

        <div class="footer">
          <p>Generated by Aizako CRM on ${new Date().toLocaleDateString()}</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Extract variables from template content
   */
  private extractVariables(content: string): string[] {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;

    while ((match = variableRegex.exec(content)) !== null) {
      const variable = match[1].trim();
      if (!variables.includes(variable)) {
        variables.push(variable);
      }
    }

    return variables;
  }
}

// Export a singleton instance
export const proposalGeneratorService = new ProposalGeneratorService();
