/**
 * Forecast Service
 *
 * This service handles deal forecasting functionality including:
 * - Generating deal forecasts with confidence scores
 * - Identifying risk factors and slip reasons
 * - Creating team forecasts and dashboards
 * - Integrating with Neo4j for graph-based analysis
 */

import mongoose from 'mongoose';
import axios from 'axios';
import {
  DealForecast,
  TeamForecast,
  IDealForecast,
  ITeamForecast,
  ForecastFactorType,
  IForecastFactor,
  Opportunity,
  IOpportunity,
  Contact,
  Activity
} from '../models/mongoose';
import { neo4jService } from './neo4j-service';
import { notificationService } from './notification-service';
import { logger } from '../utils/logger';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

// Notification service instance is imported from notification-service.ts

class ForecastService {
  /**
   * Generate a forecast for a deal
   */
  async generateDealForecast(opportunityId: string, userId: string, tenantId?: string): Promise<IDealForecast> {
    try {
      // Get the opportunity
      const opportunity = await Opportunity.findById(opportunityId);

      if (!opportunity) {
        throw new Error(`Opportunity ${opportunityId} not found`);
      }

      // Get previous forecast if it exists
      const previousForecast = await DealForecast.findOne({ opportunityId: new mongoose.Types.ObjectId(opportunityId) })
        .sort({ forecastDate: -1 });

      // Prepare data for forecast generation
      const forecastData = {
        opportunityId: new mongoose.Types.ObjectId(opportunityId),
        userId: new mongoose.Types.ObjectId(userId),
        tenantId: tenantId ? new mongoose.Types.ObjectId(tenantId) : undefined,
        forecastDate: new Date(),
        predictedCloseDate: opportunity.closeDate,
        originalCloseDate: opportunity.closeDate,
        predictedAmount: opportunity.value,
        originalAmount: opportunity.value,
        currency: opportunity.currency || 'USD',
        confidence: opportunity.probability || 50,
        probability: opportunity.probability || 50,
        factors: [] as IForecastFactor[],
        riskLevel: 'medium' as 'low' | 'medium' | 'high',
        previousForecasts: previousForecast ? [{
          forecastDate: previousForecast.forecastDate,
          predictedCloseDate: previousForecast.predictedCloseDate,
          predictedAmount: previousForecast.predictedAmount,
          confidence: previousForecast.confidence,
          probability: previousForecast.probability
        }] : []
      };

      // Get forecast factors from Neo4j
      const factors = await this.getForecastFactors(opportunityId, tenantId);
      forecastData.factors = factors;

      // Calculate risk level based on factors
      forecastData.riskLevel = this.calculateRiskLevel(factors);

      // Calculate predicted close date and amount based on factors
      const { predictedCloseDate, predictedAmount } = this.calculatePredictions(
        opportunity, factors
      );
      forecastData.predictedCloseDate = predictedCloseDate;
      forecastData.predictedAmount = predictedAmount;

      // Generate suggested actions
      const suggestedActions = await this.generateSuggestedActions(opportunityId, factors, tenantId);
      forecastData.suggestedActions = suggestedActions;

      // Create the forecast
      const forecast = new DealForecast(forecastData);
      await forecast.save();

      // If the predicted close date is different from the original, add slip reason
      if (forecastData.predictedCloseDate.getTime() !== forecastData.originalCloseDate.getTime()) {
        const slipReason = await this.generateSlipReason(opportunityId, factors, tenantId);
        forecast.slipReason = slipReason;
        await forecast.save();
      }

      logger.info(`Forecast generated for opportunity ${opportunityId}`);

      // Send notification if risk level is high
      if (forecast.riskLevel === 'high') {
        await this.sendRiskNotification(forecast);
      }

      return forecast;
    } catch (error) {
      logger.error(`Error generating forecast for opportunity ${opportunityId}:`, error);
      throw error;
    }
  }

  /**
   * Generate a team forecast
   */
  async generateTeamForecast(data: {
    userId: string;
    tenantId?: string;
    period: string;
    startDate: Date;
    endDate: Date;
    targetAmount: number;
    currency?: string;
  }): Promise<ITeamForecast> {
    try {
      // Get all opportunities in the date range
      const opportunities = await Opportunity.find({
        closeDate: {
          $gte: data.startDate,
          $lte: data.endDate
        },
        ...(data.tenantId ? { tenantId: new mongoose.Types.ObjectId(data.tenantId) } : {})
      });

      if (opportunities.length === 0) {
        throw new Error('No opportunities found in the specified date range');
      }

      // Calculate forecast metrics
      const dealCount = opportunities.length;
      const predictedAmount = opportunities.reduce((sum, opp) => sum + (opp.value || 0), 0);

      // Get at-risk deals
      const atRiskDeals = opportunities.filter(opp => {
        // Consider deals with probability < 50% as at risk
        return (opp.probability || 0) < 50;
      });

      const atRiskAmount = atRiskDeals.reduce((sum, opp) => sum + (opp.value || 0), 0);
      const atRiskDealCount = atRiskDeals.length;

      // Calculate upside (potential additional revenue)
      const upside = opportunities.reduce((sum, opp) => {
        // Only count upside for deals with probability > 50%
        if ((opp.probability || 0) > 50) {
          // Upside is the additional revenue if the deal closes at a higher value
          return sum + ((opp.value || 0) * 0.1); // Assume 10% upside
        }
        return sum;
      }, 0);

      // Calculate best and worst case scenarios
      const bestCaseAmount = predictedAmount + upside;
      const worstCaseAmount = predictedAmount - atRiskAmount;

      // Group by stage
      const byStage: { stage: string; amount: number; dealCount: number; probability: number }[] = [];
      const stageMap = new Map<string, { amount: number; dealCount: number; totalProbability: number }>();

      opportunities.forEach(opp => {
        const stage = opp.stage || 'Unknown';
        const amount = opp.value || 0;
        const probability = opp.probability || 0;

        if (!stageMap.has(stage)) {
          stageMap.set(stage, { amount: 0, dealCount: 0, totalProbability: 0 });
        }

        const stageData = stageMap.get(stage)!;
        stageData.amount += amount;
        stageData.dealCount += 1;
        stageData.totalProbability += probability;
      });

      stageMap.forEach((data, stage) => {
        byStage.push({
          stage,
          amount: data.amount,
          dealCount: data.dealCount,
          probability: data.totalProbability / data.dealCount
        });
      });

      // Group by rep
      const byRep: {
        userId: mongoose.Types.ObjectId;
        amount: number;
        dealCount: number;
        confidence: number;
        atRiskAmount: number;
        atRiskDealCount: number;
      }[] = [];

      const repMap = new Map<string, {
        amount: number;
        dealCount: number;
        totalConfidence: number;
        atRiskAmount: number;
        atRiskDealCount: number;
      }>();

      opportunities.forEach(opp => {
        const repId = opp.owner?.toString() || 'unknown';
        const amount = opp.value || 0;
        const probability = opp.probability || 0;
        const isAtRisk = probability < 50;

        if (!repMap.has(repId)) {
          repMap.set(repId, {
            amount: 0,
            dealCount: 0,
            totalConfidence: 0,
            atRiskAmount: 0,
            atRiskDealCount: 0
          });
        }

        const repData = repMap.get(repId)!;
        repData.amount += amount;
        repData.dealCount += 1;
        repData.totalConfidence += probability;

        if (isAtRisk) {
          repData.atRiskAmount += amount;
          repData.atRiskDealCount += 1;
        }
      });

      repMap.forEach((data, repId) => {
        if (repId !== 'unknown') {
          byRep.push({
            userId: new mongoose.Types.ObjectId(repId),
            amount: data.amount,
            dealCount: data.dealCount,
            confidence: data.totalConfidence / data.dealCount,
            atRiskAmount: data.atRiskAmount,
            atRiskDealCount: data.atRiskDealCount
          });
        }
      });

      // Calculate overall confidence
      const confidence = opportunities.reduce((sum, opp) => sum + (opp.probability || 0), 0) / dealCount;

      // Create the team forecast
      const forecast = new TeamForecast({
        userId: new mongoose.Types.ObjectId(data.userId),
        tenantId: data.tenantId ? new mongoose.Types.ObjectId(data.tenantId) : undefined,
        period: data.period,
        startDate: data.startDate,
        endDate: data.endDate,
        forecastDate: new Date(),
        predictedAmount,
        targetAmount: data.targetAmount,
        currency: data.currency || 'USD',
        confidence,
        dealCount,
        atRiskAmount,
        atRiskDealCount,
        upside,
        bestCaseAmount,
        worstCaseAmount,
        byStage,
        byRep
      });

      await forecast.save();
      logger.info(`Team forecast generated for period ${data.period}`);
      return forecast;
    } catch (error) {
      logger.error(`Error generating team forecast:`, error);
      throw error;
    }
  }

  /**
   * Get forecast factors from Neo4j
   */
  private async getForecastFactors(opportunityId: string, tenantId?: string): Promise<IForecastFactor[]> {
    try {
      // Initialize Neo4j service if needed
      if (!neo4jService.isInitialized()) {
        await neo4jService.initialize();
      }

      // Get factors from Neo4j
      const factors = await neo4jService.getDealForecastFactors(opportunityId, tenantId);
      return factors;
    } catch (error) {
      logger.error(`Error getting forecast factors from Neo4j:`, error);

      // Return default factors if Neo4j fails
      return [
        {
          type: ForecastFactorType.STAGE_DURATION,
          name: 'Stage Duration',
          impact: -20,
          description: 'Deal has been in current stage longer than average'
        },
        {
          type: ForecastFactorType.ACTIVITY,
          name: 'Recent Activity',
          impact: 15,
          description: 'Recent positive interactions with the prospect'
        }
      ];
    }
  }

  /**
   * Calculate risk level based on factors
   */
  private calculateRiskLevel(factors: IForecastFactor[]): 'low' | 'medium' | 'high' {
    // Calculate total negative impact
    const negativeImpact = factors
      .filter(factor => factor.impact < 0)
      .reduce((sum, factor) => sum + Math.abs(factor.impact), 0);

    // Calculate total positive impact
    const positiveImpact = factors
      .filter(factor => factor.impact > 0)
      .reduce((sum, factor) => sum + factor.impact, 0);

    // Calculate net impact
    const netImpact = positiveImpact - negativeImpact;

    // Determine risk level
    if (netImpact < -30) {
      return 'high';
    } else if (netImpact > 30) {
      return 'low';
    } else {
      return 'medium';
    }
  }

  /**
   * Calculate predicted close date and amount based on factors
   */
  private calculatePredictions(
    opportunity: IOpportunity,
    factors: IForecastFactor[]
  ): { predictedCloseDate: Date; predictedAmount: number } {
    // Start with original values
    const originalCloseDate = new Date(opportunity.closeDate);
    const originalAmount = opportunity.value || 0;

    // Calculate date adjustment (in days)
    let dateAdjustment = 0;
    factors.forEach(factor => {
      if (factor.type === ForecastFactorType.STAGE_DURATION ||
          factor.type === ForecastFactorType.STAKEHOLDER ||
          factor.type === ForecastFactorType.LEGAL_REVIEW ||
          factor.type === ForecastFactorType.TECHNICAL_REVIEW) {
        // These factors tend to affect timeline
        dateAdjustment += Math.round(factor.impact / 10); // Convert impact to days
      }
    });

    // Calculate amount adjustment (percentage)
    let amountAdjustment = 0;
    factors.forEach(factor => {
      if (factor.type === ForecastFactorType.BUDGET ||
          factor.type === ForecastFactorType.COMPETITOR ||
          factor.type === ForecastFactorType.SENTIMENT) {
        // These factors tend to affect amount
        amountAdjustment += factor.impact / 100; // Convert impact to percentage
      }
    });

    // Apply adjustments
    const predictedCloseDate = new Date(originalCloseDate);
    predictedCloseDate.setDate(originalCloseDate.getDate() + dateAdjustment);

    const predictedAmount = originalAmount * (1 + amountAdjustment);

    return { predictedCloseDate, predictedAmount };
  }

  /**
   * Generate suggested actions based on factors
   */
  private async generateSuggestedActions(
    opportunityId: string,
    factors: IForecastFactor[],
    tenantId?: string
  ): Promise<string[]> {
    try {
      // Call AI service to generate suggested actions
      const response = await axios.post(`${AI_SERVICE_URL}/forecast/suggested-actions`, {
        opportunityId,
        tenantId,
        factors
      });

      if (response.status === 200 && response.data.success) {
        return response.data.suggestedActions;
      } else {
        logger.error(`Failed to generate suggested actions: ${response.data.error || 'Unknown error'}`);

        // Return default actions based on factors
        return this.getDefaultSuggestedActions(factors);
      }
    } catch (error) {
      logger.error(`Error generating suggested actions:`, error);
      return this.getDefaultSuggestedActions(factors);
    }
  }

  /**
   * Get default suggested actions based on factors
   */
  private getDefaultSuggestedActions(factors: IForecastFactor[]): string[] {
    const actions: string[] = [];

    // Add actions based on factor types
    factors.forEach(factor => {
      if (factor.impact < 0) {
        switch (factor.type) {
          case ForecastFactorType.STAKEHOLDER:
            actions.push('Engage with additional stakeholders to build broader support');
            break;
          case ForecastFactorType.SENTIMENT:
            actions.push('Schedule a call to address concerns and improve sentiment');
            break;
          case ForecastFactorType.ACTIVITY:
            actions.push('Increase engagement with regular check-ins and value-add content');
            break;
          case ForecastFactorType.STAGE_DURATION:
            actions.push('Create an action plan to move the deal to the next stage');
            break;
          case ForecastFactorType.COMPETITOR:
            actions.push('Prepare a competitive analysis highlighting your unique value proposition');
            break;
          case ForecastFactorType.BUDGET:
            actions.push('Explore alternative pricing models or payment terms');
            break;
          case ForecastFactorType.DECISION_MAKER:
            actions.push('Identify and engage with the primary decision maker');
            break;
          case ForecastFactorType.LEGAL_REVIEW:
            actions.push('Proactively address potential legal concerns');
            break;
          case ForecastFactorType.TECHNICAL_REVIEW:
            actions.push('Schedule a technical deep dive to address implementation concerns');
            break;
        }
      }
    });

    // Add general actions if no specific ones were added
    if (actions.length === 0) {
      actions.push('Review the opportunity details and update the forecast');
      actions.push('Schedule a follow-up call to assess current status');
    }

    return [...new Set(actions)]; // Remove duplicates
  }

  /**
   * Generate slip reason based on factors
   */
  private async generateSlipReason(
    opportunityId: string,
    factors: IForecastFactor[],
    tenantId?: string
  ): Promise<string> {
    try {
      // Call AI service to generate slip reason
      const response = await axios.post(`${AI_SERVICE_URL}/forecast/slip-reason`, {
        opportunityId,
        tenantId,
        factors
      });

      if (response.status === 200 && response.data.success) {
        return response.data.slipReason;
      } else {
        logger.error(`Failed to generate slip reason: ${response.data.error || 'Unknown error'}`);

        // Return default reason based on factors
        return this.getDefaultSlipReason(factors);
      }
    } catch (error) {
      logger.error(`Error generating slip reason:`, error);
      return this.getDefaultSlipReason(factors);
    }
  }

  /**
   * Get default slip reason based on factors
   */
  private getDefaultSlipReason(factors: IForecastFactor[]): string {
    // Find the factor with the most negative impact
    const worstFactor = factors
      .filter(factor => factor.impact < 0)
      .sort((a, b) => a.impact - b.impact)[0];

    if (worstFactor) {
      return `Deal timeline extended due to ${worstFactor.name.toLowerCase()}: ${worstFactor.description}`;
    } else {
      return 'Deal timeline extended due to changing customer requirements';
    }
  }

  /**
   * Send a notification for high-risk deals
   */
  private async sendRiskNotification(forecast: IDealForecast): Promise<void> {
    try {
      // Get the opportunity
      const opportunity = await Opportunity.findById(forecast.opportunityId);

      if (!opportunity) {
        logger.warn(`Opportunity ${forecast.opportunityId} not found`);
        return;
      }

      // Send notification
      await notificationService.createNotification({
        userId: forecast.userId.toString(),
        type: 'forecast_risk',
        title: 'High-Risk Deal Alert',
        message: `Deal "${opportunity.name}" has been flagged as high-risk. Predicted close date: ${forecast.predictedCloseDate.toLocaleDateString()}`,
        data: {
          opportunityId: forecast.opportunityId.toString(),
          forecastId: forecast._id.toString(),
          riskLevel: forecast.riskLevel,
          slipReason: forecast.slipReason,
          suggestedActions: forecast.suggestedActions
        },
        isRead: false
      });

      logger.info(`Risk notification sent for opportunity ${forecast.opportunityId}`);
    } catch (error) {
      logger.error(`Error sending risk notification:`, error);
    }
  }
}

export default new ForecastService();
