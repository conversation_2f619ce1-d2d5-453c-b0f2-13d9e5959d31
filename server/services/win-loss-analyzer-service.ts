import mongoose from 'mongoose';
import axios from 'axios';
import { 
  WinLossAnalysis, 
  WinLossFactor, 
  Opportunity, 
  Contact, 
  Company, 
  Activity,
  IWinLossAnalysis,
  IWinLossFactor,
  IOpportunity,
  IContact,
  ICompany,
  IActivity
} from '../models/mongoose';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

/**
 * Win/Loss Analyzer Service
 * Provides functionality for analyzing won and lost opportunities
 */
export class WinLossAnalyzerService {
  private static instance: WinLossAnalyzerService;

  /**
   * Get the singleton instance
   */
  public static getInstance(): WinLossAnalyzerService {
    if (!WinLossAnalyzerService.instance) {
      WinLossAnalyzerService.instance = new WinLossAnalyzerService();
    }
    return WinLossAnalyzerService.instance;
  }

  /**
   * Get all win/loss analyses
   */
  async getAnalyses(
    filter: {
      outcome?: 'won' | 'lost';
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      startDate?: Date;
      endDate?: Date;
      minValue?: number;
      maxValue?: number;
      search?: string;
    } = {}
  ): Promise<IWinLossAnalysis[]> {
    try {
      const query: any = {};
      
      if (filter.outcome) {
        query.outcome = filter.outcome;
      }
      
      if (filter.opportunityId) {
        query.opportunityId = new mongoose.Types.ObjectId(filter.opportunityId);
      }
      
      if (filter.contactId) {
        query.contactId = new mongoose.Types.ObjectId(filter.contactId);
      }
      
      if (filter.companyId) {
        query.companyId = new mongoose.Types.ObjectId(filter.companyId);
      }
      
      if (filter.startDate || filter.endDate) {
        query.closedDate = {};
        
        if (filter.startDate) {
          query.closedDate.$gte = filter.startDate;
        }
        
        if (filter.endDate) {
          query.closedDate.$lte = filter.endDate;
        }
      }
      
      if (filter.minValue !== undefined || filter.maxValue !== undefined) {
        query.value = {};
        
        if (filter.minValue !== undefined) {
          query.value.$gte = filter.minValue;
        }
        
        if (filter.maxValue !== undefined) {
          query.value.$lte = filter.maxValue;
        }
      }
      
      if (filter.search) {
        query.$text = { $search: filter.search };
      }
      
      return await WinLossAnalysis.find(query)
        .sort({ closedDate: -1 })
        .populate('opportunityId', 'name value currency stage')
        .populate('contactId', 'firstName lastName email')
        .populate('companyId', 'name industry');
    } catch (error) {
      console.error('Error getting win/loss analyses:', error);
      throw error;
    }
  }

  /**
   * Get a win/loss analysis by ID
   */
  async getAnalysisById(id: string): Promise<IWinLossAnalysis | null> {
    try {
      return await WinLossAnalysis.findById(id)
        .populate('opportunityId', 'name value currency stage')
        .populate('contactId', 'firstName lastName email')
        .populate('companyId', 'name industry');
    } catch (error) {
      console.error(`Error getting win/loss analysis with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new win/loss analysis
   */
  async createAnalysis(
    data: {
      title: string;
      description?: string;
      opportunityId: string;
      contactId?: string;
      companyId?: string;
      outcome: 'won' | 'lost';
      value: number;
      currency: string;
      closedDate: Date;
      stage: string;
      salesCycle: number;
      keyFactors: Array<{
        factor: string;
        impact: 'positive' | 'negative';
        weight: number;
        description?: string;
      }>;
      competitorInfo?: {
        name?: string;
        strengths?: string[];
        weaknesses?: string[];
        pricingDifference?: number;
      };
      feedback?: string;
      learnings: string[];
      recommendations: string[];
      isAIGenerated?: boolean;
      tags?: string[];
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<IWinLossAnalysis> {
    try {
      const analysis = new WinLossAnalysis({
        ...data,
        opportunityId: new mongoose.Types.ObjectId(data.opportunityId),
        contactId: data.contactId ? new mongoose.Types.ObjectId(data.contactId) : undefined,
        companyId: data.companyId ? new mongoose.Types.ObjectId(data.companyId) : undefined,
        isAIGenerated: data.isAIGenerated || false,
        createdBy: new mongoose.Types.ObjectId(userId)
      });
      
      // Update factor statistics
      await this.updateFactorStatistics(data.keyFactors, data.outcome);
      
      return await analysis.save();
    } catch (error) {
      console.error('Error creating win/loss analysis:', error);
      throw error;
    }
  }

  /**
   * Update a win/loss analysis
   */
  async updateAnalysis(
    id: string,
    data: {
      title?: string;
      description?: string;
      outcome?: 'won' | 'lost';
      value?: number;
      currency?: string;
      closedDate?: Date;
      stage?: string;
      salesCycle?: number;
      keyFactors?: Array<{
        factor: string;
        impact: 'positive' | 'negative';
        weight: number;
        description?: string;
      }>;
      competitorInfo?: {
        name?: string;
        strengths?: string[];
        weaknesses?: string[];
        pricingDifference?: number;
      };
      feedback?: string;
      learnings?: string[];
      recommendations?: string[];
      tags?: string[];
      customFields?: Record<string, any>;
    }
  ): Promise<IWinLossAnalysis | null> {
    try {
      // Get the original analysis to compare factors
      const originalAnalysis = await WinLossAnalysis.findById(id);
      
      if (!originalAnalysis) {
        return null;
      }
      
      // If key factors are being updated, update factor statistics
      if (data.keyFactors && data.keyFactors.length > 0) {
        // Remove old factors from statistics
        await this.updateFactorStatistics(
          originalAnalysis.keyFactors.map(f => ({
            factor: f.factor,
            impact: f.impact,
            weight: -f.weight // Negative to remove the weight
          })),
          originalAnalysis.outcome
        );
        
        // Add new factors to statistics
        await this.updateFactorStatistics(
          data.keyFactors,
          data.outcome || originalAnalysis.outcome
        );
      }
      
      return await WinLossAnalysis.findByIdAndUpdate(
        id,
        { $set: data },
        { new: true }
      );
    } catch (error) {
      console.error(`Error updating win/loss analysis with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a win/loss analysis
   */
  async deleteAnalysis(id: string): Promise<boolean> {
    try {
      // Get the analysis to remove its factors from statistics
      const analysis = await WinLossAnalysis.findById(id);
      
      if (!analysis) {
        return false;
      }
      
      // Remove factors from statistics
      await this.updateFactorStatistics(
        analysis.keyFactors.map(f => ({
          factor: f.factor,
          impact: f.impact,
          weight: -f.weight // Negative to remove the weight
        })),
        analysis.outcome
      );
      
      const result = await WinLossAnalysis.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      console.error(`Error deleting win/loss analysis with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Generate a win/loss analysis for an opportunity
   */
  async generateAnalysis(
    opportunityId: string,
    userId: string
  ): Promise<IWinLossAnalysis> {
    try {
      // Get the opportunity
      const opportunity = await Opportunity.findById(opportunityId);
      
      if (!opportunity) {
        throw new Error(`Opportunity with ID ${opportunityId} not found`);
      }
      
      // Check if the opportunity is closed
      if (!opportunity.stage.startsWith('closed_')) {
        throw new Error('Cannot generate analysis for an opportunity that is not closed');
      }
      
      // Check if an analysis already exists
      const existingAnalysis = await WinLossAnalysis.findOne({ opportunityId: new mongoose.Types.ObjectId(opportunityId) });
      
      if (existingAnalysis) {
        throw new Error('An analysis already exists for this opportunity');
      }
      
      // Get related data
      const [contact, company] = await Promise.all([
        opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
        opportunity.companyId ? Company.findById(opportunity.companyId) : null
      ]);
      
      // Get activities
      const activities = await Activity.find({ opportunityId: new mongoose.Types.ObjectId(opportunityId) })
        .sort({ date: -1 })
        .limit(20);
      
      // Calculate sales cycle in days
      const salesCycle = opportunity.createdAt && opportunity.updatedAt
        ? Math.round((opportunity.updatedAt.getTime() - opportunity.createdAt.getTime()) / (1000 * 60 * 60 * 24))
        : 0;
      
      // Generate analysis using AI
      const analysisData = await this.generateAnalysisWithAI(
        opportunity,
        contact,
        company,
        activities,
        salesCycle
      );
      
      // Create the analysis
      return await this.createAnalysis(
        {
          ...analysisData,
          opportunityId,
          contactId: contact?._id.toString(),
          companyId: company?._id.toString(),
          value: opportunity.value || 0,
          currency: opportunity.currency || 'USD',
          closedDate: opportunity.updatedAt || new Date(),
          stage: opportunity.stage,
          salesCycle,
          isAIGenerated: true
        },
        userId
      );
    } catch (error) {
      console.error(`Error generating analysis for opportunity ${opportunityId}:`, error);
      throw error;
    }
  }

  /**
   * Get win/loss factors
   */
  async getFactors(
    filter: {
      category?: string;
      impact?: 'positive' | 'negative' | 'neutral';
      isActive?: boolean;
      search?: string;
    } = {}
  ): Promise<IWinLossFactor[]> {
    try {
      const query: any = {};
      
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.impact) {
        query.impact = filter.impact;
      }
      
      if (filter.isActive !== undefined) {
        query.isActive = filter.isActive;
      }
      
      if (filter.search) {
        query.$text = { $search: filter.search };
      }
      
      return await WinLossFactor.find(query).sort({ frequency: -1 });
    } catch (error) {
      console.error('Error getting win/loss factors:', error);
      throw error;
    }
  }

  /**
   * Get win/loss statistics
   */
  async getStatistics(
    filter: {
      startDate?: Date;
      endDate?: Date;
      companyId?: string;
    } = {}
  ): Promise<{
    totalDeals: number;
    wonDeals: number;
    lostDeals: number;
    winRate: number;
    totalValue: number;
    avgDealSize: number;
    avgSalesCycle: number;
    topWinFactors: Array<{ factor: string; frequency: number; impact: number }>;
    topLossFactors: Array<{ factor: string; frequency: number; impact: number }>;
  }> {
    try {
      const query: any = {};
      
      if (filter.startDate || filter.endDate) {
        query.closedDate = {};
        
        if (filter.startDate) {
          query.closedDate.$gte = filter.startDate;
        }
        
        if (filter.endDate) {
          query.closedDate.$lte = filter.endDate;
        }
      }
      
      if (filter.companyId) {
        query.companyId = new mongoose.Types.ObjectId(filter.companyId);
      }
      
      // Get all analyses matching the filter
      const analyses = await WinLossAnalysis.find(query);
      
      // Calculate statistics
      const totalDeals = analyses.length;
      const wonDeals = analyses.filter(a => a.outcome === 'won').length;
      const lostDeals = analyses.filter(a => a.outcome === 'lost').length;
      const winRate = totalDeals > 0 ? (wonDeals / totalDeals) * 100 : 0;
      
      const totalValue = analyses.reduce((sum, a) => sum + a.value, 0);
      const avgDealSize = totalDeals > 0 ? totalValue / totalDeals : 0;
      
      const avgSalesCycle = totalDeals > 0
        ? analyses.reduce((sum, a) => sum + a.salesCycle, 0) / totalDeals
        : 0;
      
      // Get top factors
      const factors = await WinLossFactor.find({ isActive: true })
        .sort({ frequency: -1 })
        .limit(20);
      
      const topWinFactors = factors
        .filter(f => f.impact === 'positive')
        .map(f => ({
          factor: f.name,
          frequency: f.frequency,
          impact: f.outcomeCorrelation * f.averageWeight
        }))
        .sort((a, b) => b.impact - a.impact)
        .slice(0, 5);
      
      const topLossFactors = factors
        .filter(f => f.impact === 'negative')
        .map(f => ({
          factor: f.name,
          frequency: f.frequency,
          impact: Math.abs(f.outcomeCorrelation * f.averageWeight)
        }))
        .sort((a, b) => b.impact - a.impact)
        .slice(0, 5);
      
      return {
        totalDeals,
        wonDeals,
        lostDeals,
        winRate,
        totalValue,
        avgDealSize,
        avgSalesCycle,
        topWinFactors,
        topLossFactors
      };
    } catch (error) {
      console.error('Error getting win/loss statistics:', error);
      throw error;
    }
  }

  /**
   * Update factor statistics
   */
  private async updateFactorStatistics(
    factors: Array<{
      factor: string;
      impact: 'positive' | 'negative';
      weight: number;
      description?: string;
    }>,
    outcome: 'won' | 'lost'
  ): Promise<void> {
    try {
      for (const factor of factors) {
        // Find or create the factor
        let factorDoc = await WinLossFactor.findOne({ name: factor.factor });
        
        if (!factorDoc) {
          // Create a new factor
          factorDoc = new WinLossFactor({
            name: factor.factor,
            category: this.getCategoryFromFactor(factor.factor),
            impact: factor.impact,
            frequency: 0,
            averageWeight: 0,
            outcomeCorrelation: 0,
            isActive: true,
            createdBy: new mongoose.Types.ObjectId('000000000000000000000000') // System user
          });
        }
        
        // Update statistics
        const oldFrequency = factorDoc.frequency;
        const oldWeight = factorDoc.averageWeight;
        const oldCorrelation = factorDoc.outcomeCorrelation;
        
        // Update frequency
        const frequencyDelta = factor.weight > 0 ? 1 : -1;
        factorDoc.frequency = Math.max(0, factorDoc.frequency + frequencyDelta);
        
        // Update average weight
        if (factorDoc.frequency > 0) {
          if (factor.weight > 0) {
            // Adding a new factor
            factorDoc.averageWeight = (oldFrequency * oldWeight + Math.abs(factor.weight)) / factorDoc.frequency;
          } else if (oldFrequency > 1) {
            // Removing a factor but not the last one
            factorDoc.averageWeight = (oldFrequency * oldWeight - Math.abs(factor.weight)) / (oldFrequency - 1);
          } else {
            // Removing the last factor
            factorDoc.averageWeight = 5; // Default
          }
        }
        
        // Update outcome correlation
        const correlationImpact = factor.weight > 0 ? 0.1 : -0.1;
        const correlationDirection = outcome === 'won' ? 1 : -1;
        const impactDirection = factor.impact === 'positive' ? 1 : -1;
        
        factorDoc.outcomeCorrelation = Math.max(-1, Math.min(1, 
          oldCorrelation + (correlationImpact * correlationDirection * impactDirection)
        ));
        
        // Update impact based on correlation
        if (factorDoc.outcomeCorrelation > 0.3) {
          factorDoc.impact = 'positive';
        } else if (factorDoc.outcomeCorrelation < -0.3) {
          factorDoc.impact = 'negative';
        } else {
          factorDoc.impact = 'neutral';
        }
        
        await factorDoc.save();
      }
    } catch (error) {
      console.error('Error updating factor statistics:', error);
      throw error;
    }
  }

  /**
   * Get category from factor name
   */
  private getCategoryFromFactor(factor: string): string {
    const lowerFactor = factor.toLowerCase();
    
    if (lowerFactor.includes('price') || lowerFactor.includes('cost') || lowerFactor.includes('budget')) {
      return 'pricing';
    } else if (lowerFactor.includes('feature') || lowerFactor.includes('product') || lowerFactor.includes('quality')) {
      return 'product';
    } else if (lowerFactor.includes('relationship') || lowerFactor.includes('trust') || lowerFactor.includes('rapport')) {
      return 'relationship';
    } else if (lowerFactor.includes('competitor') || lowerFactor.includes('alternative')) {
      return 'competition';
    } else if (lowerFactor.includes('timing') || lowerFactor.includes('urgency') || lowerFactor.includes('deadline')) {
      return 'timing';
    } else if (lowerFactor.includes('decision') || lowerFactor.includes('authority') || lowerFactor.includes('stakeholder')) {
      return 'decision_making';
    } else {
      return 'other';
    }
  }

  /**
   * Generate analysis using AI
   */
  private async generateAnalysisWithAI(
    opportunity: IOpportunity,
    contact: IContact | null,
    company: ICompany | null,
    activities: IActivity[],
    salesCycle: number
  ): Promise<{
    title: string;
    description: string;
    outcome: 'won' | 'lost';
    keyFactors: Array<{
      factor: string;
      impact: 'positive' | 'negative';
      weight: number;
      description?: string;
    }>;
    competitorInfo?: {
      name?: string;
      strengths?: string[];
      weaknesses?: string[];
      pricingDifference?: number;
    };
    feedback?: string;
    learnings: string[];
    recommendations: string[];
  }> {
    try {
      // Prepare data for AI
      const data = {
        opportunity: {
          id: opportunity._id.toString(),
          name: opportunity.name,
          value: opportunity.value,
          currency: opportunity.currency,
          stage: opportunity.stage,
          probability: opportunity.probability,
          expectedCloseDate: opportunity.expectedCloseDate,
          description: opportunity.description,
          tags: opportunity.tags,
          createdAt: opportunity.createdAt,
          updatedAt: opportunity.updatedAt
        },
        contact: contact ? {
          id: contact._id.toString(),
          name: `${contact.firstName} ${contact.lastName}`,
          email: contact.email,
          phone: contact.phone,
          title: contact.title,
          status: contact.status,
          notes: contact.notes,
          tags: contact.tags
        } : null,
        company: company ? {
          id: company._id.toString(),
          name: company.name,
          industry: company.industry,
          website: company.website,
          size: company.size,
          status: company.status,
          notes: company.notes,
          tags: company.tags
        } : null,
        activities: activities.map(activity => ({
          id: activity._id.toString(),
          type: activity.type,
          title: activity.title,
          description: activity.description,
          date: activity.date,
          completed: activity.completed
        })),
        salesCycle,
        outcome: opportunity.stage === 'closed_won' ? 'won' : 'lost'
      };
      
      // Create prompt for AI
      const prompt = `
        Analyze this ${data.outcome} opportunity and generate a comprehensive win/loss analysis.
        
        Please provide:
        1. A clear title for the analysis
        2. A brief description summarizing the key points
        3. Key factors that contributed to the ${data.outcome} outcome, with:
           - Factor name
           - Impact (positive or negative)
           - Weight (1-10 scale)
           - Brief description
        4. Competitor information if available
        5. Key learnings from this opportunity
        6. Actionable recommendations for future opportunities
        
        Format your response as a JSON object with the following structure:
        {
          "title": "string",
          "description": "string",
          "outcome": "${data.outcome}",
          "keyFactors": [
            {
              "factor": "string",
              "impact": "positive|negative",
              "weight": number,
              "description": "string"
            }
          ],
          "competitorInfo": {
            "name": "string",
            "strengths": ["string"],
            "weaknesses": ["string"],
            "pricingDifference": number
          },
          "feedback": "string",
          "learnings": ["string"],
          "recommendations": ["string"]
        }
      `;
      
      // Try to use the AI service first
      try {
        const aiResponse = await axios.post(`${AI_SERVICE_URL}/crew`, {
          task: prompt,
          userId: 1, // System user
          context: data
        }, {
          timeout: 30000 // 30 second timeout
        });
        
        if (aiResponse.data && aiResponse.data.response) {
          try {
            // Try to parse the response as JSON
            const analysisData = JSON.parse(aiResponse.data.response);
            return analysisData;
          } catch (e) {
            console.error('Error parsing AI service response:', e);
          }
        }
      } catch (aiError) {
        console.error('Error using AI service, falling back to Voyage AI:', aiError);
      }
      
      // Fallback to Voyage AI if the AI service fails
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-2',
          messages: [
            {
              role: 'system',
              content: `You are an expert sales analyst for Aizako CRM.
              Your job is to analyze won and lost opportunities to identify patterns and provide insights.
              Focus on extracting key factors that contributed to the outcome and providing actionable recommendations.
              Current date: ${new Date().toISOString().split('T')[0]}`
            },
            {
              role: 'user',
              content: `${prompt}\n\nData: ${JSON.stringify(data)}`
            }
          ],
          temperature: 0.7,
          max_tokens: 2000,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );
      
      if (response.data && response.data.choices && response.data.choices.length > 0) {
        try {
          const content = response.data.choices[0].message.content;
          return JSON.parse(content);
        } catch (e) {
          console.error('Error parsing Voyage AI response:', e);
          throw new Error('Failed to parse AI response');
        }
      }
      
      throw new Error('Failed to generate win/loss analysis');
    } catch (error) {
      console.error('Error generating analysis with AI:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const winLossAnalyzerService = WinLossAnalyzerService.getInstance();
