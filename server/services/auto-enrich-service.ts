/**
 * Auto-Enrich Service
 * 
 * This service provides automatic enrichment of contact data from various sources.
 */

import axios from 'axios';
import { Contact } from '../models/mongoose';
import { logger } from '../utils/logger';
import { OpenAI } from 'openai';
import { mongoApiClient } from '../api/mongo-api-client';

// OpenAI configuration
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

// Enrichment API configuration
const CLEARBIT_API_KEY = process.env.CLEARBIT_API_KEY;
const HUNTER_API_KEY = process.env.HUNTER_API_KEY;
const FULLCONTACT_API_KEY = process.env.FULLCONTACT_API_KEY;

interface EnrichmentResult {
  source: string;
  data: Record<string, any>;
  confidence: number;
}

interface PersonaGenerationResult {
  summary: string;
  communicationPreferences: {
    preferredChannel?: 'email' | 'phone' | 'in-person' | 'video';
    bestTimeToContact?: string;
    responseTime?: 'fast' | 'medium' | 'slow';
  };
  interests: string[];
  painPoints: string[];
  decisionFactors: string[];
  aiConfidence: number;
}

class AutoEnrichService {
  /**
   * Enrich a contact with data from various sources
   */
  async enrichContact(contactId: string, tenantId: string): Promise<{
    updated: boolean;
    enrichedFields: string[];
    sources: string[];
  }> {
    try {
      // Get the contact
      const contact = await Contact.findById(contactId);
      if (!contact) {
        throw new Error(`Contact not found: ${contactId}`);
      }

      // Track which fields were enriched
      const enrichedFields: string[] = [];
      const sources: string[] = [];
      let updated = false;

      // Only enrich if we have an email
      if (contact.email) {
        // Get enrichment data from multiple sources
        const enrichmentResults = await Promise.allSettled([
          this.enrichFromClearbit(contact.email),
          this.enrichFromHunter(contact.email),
          this.enrichFromFullContact(contact.email, contact.firstName, contact.lastName),
        ]);

        // Process successful results
        const successfulResults: EnrichmentResult[] = [];
        
        enrichmentResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            successfulResults.push(result.value);
            sources.push(result.value.source);
          } else {
            logger.warn(`Enrichment source ${index} failed:`, result.reason);
          }
        });

        // Apply enrichment data to contact
        if (successfulResults.length > 0) {
          const updates = this.mergeEnrichmentData(contact, successfulResults);
          
          if (Object.keys(updates).length > 0) {
            // Apply updates to contact
            Object.entries(updates).forEach(([key, value]) => {
              if (key !== '_id' && key !== 'createdAt' && key !== 'updatedAt') {
                // @ts-ignore
                contact[key] = value;
                enrichedFields.push(key);
              }
            });
            
            // Save the contact
            await contact.save();
            updated = true;
          }
        }
      }

      // Generate AI persona if we have enough data
      if (contact.interactions && contact.interactions.length >= 3) {
        const persona = await this.generatePersona(contactId, tenantId);
        
        if (persona) {
          contact.persona = persona;
          await contact.save();
          enrichedFields.push('persona');
          sources.push('ai');
          updated = true;
        }
      }

      logger.info(`Contact ${contactId} enrichment completed. Updated: ${updated}, Fields: ${enrichedFields.join(', ')}`);
      return { updated, enrichedFields, sources };
    } catch (error) {
      logger.error(`Error enriching contact ${contactId}:`, error);
      throw error;
    }
  }

  /**
   * Enrich contact data from Clearbit
   */
  private async enrichFromClearbit(email: string): Promise<EnrichmentResult> {
    try {
      if (!CLEARBIT_API_KEY) {
        return { source: 'clearbit', data: {}, confidence: 0 };
      }

      const response = await axios.get(`https://person.clearbit.com/v2/people/find`, {
        params: { email },
        headers: { Authorization: `Bearer ${CLEARBIT_API_KEY}` },
      });

      return {
        source: 'clearbit',
        data: response.data,
        confidence: 0.9,
      };
    } catch (error) {
      logger.error('Clearbit enrichment error:', error);
      return { source: 'clearbit', data: {}, confidence: 0 };
    }
  }

  /**
   * Enrich contact data from Hunter
   */
  private async enrichFromHunter(email: string): Promise<EnrichmentResult> {
    try {
      if (!HUNTER_API_KEY) {
        return { source: 'hunter', data: {}, confidence: 0 };
      }

      const response = await axios.get(`https://api.hunter.io/v2/email-verifier`, {
        params: { email, api_key: HUNTER_API_KEY },
      });

      return {
        source: 'hunter',
        data: response.data.data,
        confidence: response.data.data.score,
      };
    } catch (error) {
      logger.error('Hunter enrichment error:', error);
      return { source: 'hunter', data: {}, confidence: 0 };
    }
  }

  /**
   * Enrich contact data from FullContact
   */
  private async enrichFromFullContact(
    email: string,
    firstName?: string,
    lastName?: string
  ): Promise<EnrichmentResult> {
    try {
      if (!FULLCONTACT_API_KEY) {
        return { source: 'fullcontact', data: {}, confidence: 0 };
      }

      const payload: Record<string, any> = { email };
      
      if (firstName && lastName) {
        payload.name = { givenName: firstName, familyName: lastName };
      }

      const response = await axios.post(
        'https://api.fullcontact.com/v3/person.enrich',
        payload,
        {
          headers: {
            Authorization: `Bearer ${FULLCONTACT_API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return {
        source: 'fullcontact',
        data: response.data,
        confidence: 0.8,
      };
    } catch (error) {
      logger.error('FullContact enrichment error:', error);
      return { source: 'fullcontact', data: {}, confidence: 0 };
    }
  }

  /**
   * Merge enrichment data from multiple sources
   */
  private mergeEnrichmentData(
    contact: any,
    results: EnrichmentResult[]
  ): Record<string, any> {
    const updates: Record<string, any> = {};
    
    // Sort results by confidence
    results.sort((a, b) => b.confidence - a.confidence);
    
    // Process Clearbit data
    const clearbit = results.find(r => r.source === 'clearbit');
    if (clearbit && clearbit.data) {
      if (!contact.title && clearbit.data.employment?.title) {
        updates.title = clearbit.data.employment.title;
      }
      
      if (!contact.companyName && clearbit.data.employment?.name) {
        updates.companyName = clearbit.data.employment.name;
      }
      
      if (!contact.phone && clearbit.data.phone) {
        updates.phone = clearbit.data.phone;
      }
      
      if (!contact.location && clearbit.data.geo) {
        updates.location = `${clearbit.data.geo.city || ''}, ${clearbit.data.geo.country || ''}`.trim();
      }
      
      if (!contact.bio && clearbit.data.bio) {
        updates.bio = clearbit.data.bio;
      }
      
      if (!contact.avatar && clearbit.data.avatar) {
        updates.avatar = clearbit.data.avatar;
      }
      
      // Social profiles
      if (!contact.socialProfiles && clearbit.data.linkedin?.handle) {
        updates.socialProfiles = updates.socialProfiles || {};
        updates.socialProfiles.linkedin = `https://linkedin.com/in/${clearbit.data.linkedin.handle}`;
      }
      
      if (!contact.socialProfiles && clearbit.data.twitter?.handle) {
        updates.socialProfiles = updates.socialProfiles || {};
        updates.socialProfiles.twitter = `https://twitter.com/${clearbit.data.twitter.handle}`;
      }
    }
    
    // Process Hunter data
    const hunter = results.find(r => r.source === 'hunter');
    if (hunter && hunter.data) {
      if (!contact.emailVerified) {
        updates.emailVerified = hunter.data.status === 'valid';
      }
      
      if (!contact.companyName && hunter.data.company) {
        updates.companyName = hunter.data.company;
      }
      
      if (!contact.companyDomain && hunter.data.domain) {
        updates.companyDomain = hunter.data.domain;
      }
    }
    
    // Process FullContact data
    const fullcontact = results.find(r => r.source === 'fullcontact');
    if (fullcontact && fullcontact.data) {
      if (!contact.title && fullcontact.data.employment?.[0]?.title) {
        updates.title = fullcontact.data.employment[0].title;
      }
      
      if (!contact.companyName && fullcontact.data.employment?.[0]?.name) {
        updates.companyName = fullcontact.data.employment[0].name;
      }
      
      if (!contact.location && fullcontact.data.demographics?.locationGeneral) {
        updates.location = fullcontact.data.demographics.locationGeneral;
      }
      
      if (!contact.socialProfiles && fullcontact.data.socialProfiles) {
        updates.socialProfiles = updates.socialProfiles || {};
        
        fullcontact.data.socialProfiles.forEach((profile: any) => {
          if (profile.type === 'linkedin') {
            updates.socialProfiles.linkedin = profile.url;
          } else if (profile.type === 'twitter') {
            updates.socialProfiles.twitter = profile.url;
          } else if (profile.type === 'facebook') {
            updates.socialProfiles.facebook = profile.url;
          }
        });
      }
    }
    
    return updates;
  }

  /**
   * Generate AI persona for a contact
   */
  async generatePersona(contactId: string, tenantId: string): Promise<PersonaGenerationResult | null> {
    try {
      // Get contact with interactions
      const contact = await Contact.findById(contactId);
      if (!contact || !contact.interactions || contact.interactions.length === 0) {
        return null;
      }
      
      // Get interactions
      const interactions = await mongoApiClient.interactions.getByContactId(contactId);
      
      if (!interactions || interactions.length === 0) {
        return null;
      }
      
      // Prepare interaction data for the prompt
      const interactionSummaries = interactions.map(interaction => {
        return `- Type: ${interaction.type}, Date: ${new Date(interaction.timestamp).toISOString().split('T')[0]}, Summary: ${interaction.summary}, Sentiment: ${interaction.sentiment || 'neutral'}`;
      }).join('\n');
      
      // Create the prompt
      const prompt = `
      You are an AI assistant that analyzes customer interactions and generates detailed customer personas.
      
      Contact Information:
      - Name: ${contact.firstName} ${contact.lastName}
      - Email: ${contact.email || 'Unknown'}
      - Title: ${contact.title || 'Unknown'}
      - Company: ${contact.companyName || 'Unknown'}
      
      Recent Interactions:
      ${interactionSummaries}
      
      Based on the above information, generate a detailed customer persona that includes:
      1. A summary of the contact's communication style, preferences, and business needs
      2. Communication preferences (preferred channel, best time to contact, response time)
      3. Key interests related to our products/services
      4. Pain points they're trying to solve
      5. Decision factors that would influence their purchasing decisions
      
      Format your response as a JSON object with the following structure:
      {
        "summary": "Concise summary of the contact's profile",
        "communicationPreferences": {
          "preferredChannel": "email|phone|in-person|video",
          "bestTimeToContact": "Morning|Afternoon|Evening",
          "responseTime": "fast|medium|slow"
        },
        "interests": ["interest1", "interest2", ...],
        "painPoints": ["pain point 1", "pain point 2", ...],
        "decisionFactors": ["factor1", "factor2", ...],
        "aiConfidence": 0.85 // Your confidence in this analysis from 0 to 1
      }
      `;
      
      // Call OpenAI API
      const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: 'You are an expert CRM analyst that creates detailed customer personas based on interaction history.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });
      
      // Parse the response
      const content = response.choices[0].message.content;
      if (!content) {
        throw new Error('No content in OpenAI response');
      }
      
      const persona = JSON.parse(content) as PersonaGenerationResult;
      
      logger.info(`Generated persona for contact ${contactId}`);
      return persona;
    } catch (error) {
      logger.error(`Error generating persona for contact ${contactId}:`, error);
      return null;
    }
  }

  /**
   * Batch enrich contacts for a tenant
   */
  async batchEnrichContacts(tenantId: string, limit: number = 50): Promise<{
    processed: number;
    enriched: number;
    errors: number;
  }> {
    try {
      // Get contacts that haven't been enriched recently
      // Sort by those with most interactions but no persona
      const contacts = await Contact.find({
        tenantId,
        $or: [
          { 'persona': { $exists: false } },
          { 'persona.lastUpdated': { $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } } // 30 days
        ]
      })
      .sort({ 'interactions.0': -1 }) // Prioritize contacts with interactions
      .limit(limit);
      
      let processed = 0;
      let enriched = 0;
      let errors = 0;
      
      for (const contact of contacts) {
        try {
          processed++;
          const result = await this.enrichContact(contact._id.toString(), tenantId);
          if (result.updated) {
            enriched++;
          }
        } catch (error) {
          logger.error(`Error enriching contact ${contact._id}:`, error);
          errors++;
        }
      }
      
      logger.info(`Batch enrichment completed for tenant ${tenantId}: ${processed} processed, ${enriched} enriched, ${errors} errors`);
      return { processed, enriched, errors };
    } catch (error) {
      logger.error(`Error in batch enrichment for tenant ${tenantId}:`, error);
      throw error;
    }
  }
}

export const autoEnrichService = new AutoEnrichService();
