/**
 * Adaptive Sequence Service
 * 
 * This service provides functionality for adaptive sequences that
 * automatically adjust based on recipient interactions and performance data.
 */

import mongoose from 'mongoose';
import { 
  Sequence, 
  SequenceStep, 
  SequenceEnrollment,
  EmailTemplate,
  EmailTracking,
  Contact
} from '../models/mongoose';
import { SequenceStepChannelType, SequenceStepTimingType } from '../models/mongoose/sequence-model';
import emailPredictiveService from './email-predictive-service';
import emailTemplateService from './email-template-service';
import { logger } from '../utils/logger';

interface AdaptiveRuleCondition {
  type: 'open' | 'click' | 'reply' | 'no_open' | 'no_click' | 'no_reply';
  timeframe?: number; // hours
}

interface AdaptiveRuleAction {
  type: 'skip_step' | 'add_step' | 'change_template' | 'change_timing' | 'change_channel';
  stepIndex?: number;
  templateId?: string;
  timing?: {
    type: SequenceStepTimingType;
    value: number;
  };
  channel?: SequenceStepChannelType;
}

interface AdaptiveRule {
  name: string;
  description?: string;
  condition: AdaptiveRuleCondition;
  action: AdaptiveRuleAction;
  isActive: boolean;
}

interface AdaptiveSequenceConfig {
  isAdaptive: boolean;
  optimizeFor: 'opens' | 'clicks' | 'replies';
  adaptationLevel: 'conservative' | 'moderate' | 'aggressive';
  rules: AdaptiveRule[];
  allowTemplateSelection: boolean;
  allowTimingAdjustment: boolean;
  allowChannelSwitching: boolean;
}

class AdaptiveSequenceService {
  /**
   * Update sequence configuration with adaptive settings
   */
  async updateSequenceAdaptiveConfig(
    sequenceId: string,
    config: AdaptiveSequenceConfig
  ): Promise<boolean> {
    try {
      // Find the sequence
      const sequence = await Sequence.findById(sequenceId);
      
      if (!sequence) {
        logger.warn(`Sequence ${sequenceId} not found`);
        return false;
      }
      
      // Update the sequence with adaptive configuration
      sequence.customFields = {
        ...sequence.customFields,
        adaptiveConfig: config
      };
      
      await sequence.save();
      logger.info(`Updated adaptive configuration for sequence ${sequenceId}`);
      return true;
    } catch (error) {
      logger.error(`Error updating adaptive configuration for sequence ${sequenceId}:`, error);
      return false;
    }
  }

  /**
   * Get sequence adaptive configuration
   */
  async getSequenceAdaptiveConfig(
    sequenceId: string
  ): Promise<AdaptiveSequenceConfig | null> {
    try {
      // Find the sequence
      const sequence = await Sequence.findById(sequenceId);
      
      if (!sequence) {
        logger.warn(`Sequence ${sequenceId} not found`);
        return null;
      }
      
      // Get adaptive configuration
      const adaptiveConfig = sequence.customFields?.adaptiveConfig as AdaptiveSequenceConfig;
      
      if (!adaptiveConfig) {
        // Return default configuration if none exists
        return {
          isAdaptive: false,
          optimizeFor: 'replies',
          adaptationLevel: 'moderate',
          rules: [],
          allowTemplateSelection: true,
          allowTimingAdjustment: true,
          allowChannelSwitching: false
        };
      }
      
      return adaptiveConfig;
    } catch (error) {
      logger.error(`Error getting adaptive configuration for sequence ${sequenceId}:`, error);
      return null;
    }
  }

  /**
   * Process adaptive rules for a sequence enrollment
   */
  async processAdaptiveRules(
    enrollmentId: string
  ): Promise<{
    adapted: boolean;
    changes: {
      type: string;
      description: string;
    }[];
  }> {
    try {
      // Find the enrollment
      const enrollment = await SequenceEnrollment.findById(enrollmentId)
        .populate('sequenceId')
        .populate('contactId')
        .populate({
          path: 'steps',
          populate: {
            path: 'sequenceStepId'
          }
        });
      
      if (!enrollment) {
        logger.warn(`Enrollment ${enrollmentId} not found`);
        return { adapted: false, changes: [] };
      }
      
      // Get sequence
      const sequence = enrollment.sequenceId as any;
      if (!sequence) {
        logger.warn(`Sequence not found for enrollment ${enrollmentId}`);
        return { adapted: false, changes: [] };
      }
      
      // Get adaptive configuration
      const adaptiveConfig = sequence.customFields?.adaptiveConfig as AdaptiveSequenceConfig;
      
      if (!adaptiveConfig || !adaptiveConfig.isAdaptive) {
        // Sequence is not adaptive
        return { adapted: false, changes: [] };
      }
      
      // Get contact
      const contact = enrollment.contactId as any;
      if (!contact) {
        logger.warn(`Contact not found for enrollment ${enrollmentId}`);
        return { adapted: false, changes: [] };
      }
      
      const changes: { type: string; description: string }[] = [];
      
      // Process rules
      if (adaptiveConfig.rules && adaptiveConfig.rules.length > 0) {
        for (const rule of adaptiveConfig.rules) {
          if (!rule.isActive) continue;
          
          const ruleApplied = await this.applyRule(
            enrollment,
            rule,
            sequence,
            contact
          );
          
          if (ruleApplied) {
            changes.push({
              type: 'rule',
              description: `Applied rule: ${rule.name}`
            });
          }
        }
      }
      
      // Apply AI-based adaptations if allowed
      if (adaptiveConfig.allowTemplateSelection || 
          adaptiveConfig.allowTimingAdjustment || 
          adaptiveConfig.allowChannelSwitching) {
        
        const aiChanges = await this.applyAIAdaptations(
          enrollment,
          adaptiveConfig,
          sequence,
          contact
        );
        
        changes.push(...aiChanges);
      }
      
      return {
        adapted: changes.length > 0,
        changes
      };
    } catch (error) {
      logger.error(`Error processing adaptive rules for enrollment ${enrollmentId}:`, error);
      return { adapted: false, changes: [] };
    }
  }

  /**
   * Apply a specific rule to an enrollment
   */
  private async applyRule(
    enrollment: any,
    rule: AdaptiveRule,
    sequence: any,
    contact: any
  ): Promise<boolean> {
    try {
      // Check if condition is met
      const conditionMet = await this.checkRuleCondition(
        rule.condition,
        enrollment,
        contact
      );
      
      if (!conditionMet) {
        return false;
      }
      
      // Apply action
      switch (rule.action.type) {
        case 'skip_step':
          return await this.applySkipStepAction(enrollment, rule.action);
          
        case 'add_step':
          return await this.applyAddStepAction(enrollment, rule.action, sequence);
          
        case 'change_template':
          return await this.applyChangeTemplateAction(enrollment, rule.action);
          
        case 'change_timing':
          return await this.applyChangeTimingAction(enrollment, rule.action);
          
        case 'change_channel':
          return await this.applyChangeChannelAction(enrollment, rule.action);
          
        default:
          return false;
      }
    } catch (error) {
      logger.error('Error applying rule:', error);
      return false;
    }
  }

  /**
   * Check if a rule condition is met
   */
  private async checkRuleCondition(
    condition: AdaptiveRuleCondition,
    enrollment: any,
    contact: any
  ): Promise<boolean> {
    try {
      // Get recent email tracking records
      const timeframe = condition.timeframe || 72; // Default 72 hours
      const since = new Date();
      since.setHours(since.getHours() - timeframe);
      
      const trackingRecords = await EmailTracking.find({
        contactId: contact._id,
        'customFields.sequenceId': enrollment.sequenceId,
        'customFields.sequenceEnrollmentId': enrollment._id,
        createdAt: { $gte: since }
      }).sort({ createdAt: -1 });
      
      switch (condition.type) {
        case 'open':
          return trackingRecords.some(record => 
            record.status === 'OPENED' || record.status === 'CLICKED' || record.status === 'REPLIED'
          );
          
        case 'click':
          return trackingRecords.some(record => 
            record.status === 'CLICKED' || record.status === 'REPLIED'
          );
          
        case 'reply':
          return trackingRecords.some(record => 
            record.status === 'REPLIED'
          );
          
        case 'no_open':
          return trackingRecords.length > 0 && trackingRecords.every(record => 
            record.status !== 'OPENED' && record.status !== 'CLICKED' && record.status !== 'REPLIED'
          );
          
        case 'no_click':
          return trackingRecords.some(record => 
            record.status === 'OPENED'
          ) && trackingRecords.every(record => 
            record.status !== 'CLICKED' && record.status !== 'REPLIED'
          );
          
        case 'no_reply':
          return trackingRecords.some(record => 
            record.status === 'OPENED' || record.status === 'CLICKED'
          ) && trackingRecords.every(record => 
            record.status !== 'REPLIED'
          );
          
        default:
          return false;
      }
    } catch (error) {
      logger.error('Error checking rule condition:', error);
      return false;
    }
  }

  /**
   * Apply skip step action
   */
  private async applySkipStepAction(
    enrollment: any,
    action: AdaptiveRuleAction
  ): Promise<boolean> {
    try {
      // Find the step to skip
      const stepIndex = action.stepIndex || 0;
      
      if (stepIndex >= enrollment.steps.length) {
        return false;
      }
      
      // Mark step as skipped
      enrollment.steps[stepIndex].status = 'SKIPPED';
      enrollment.steps[stepIndex].customFields = {
        ...enrollment.steps[stepIndex].customFields,
        skippedByAdaptiveRule: true
      };
      
      await enrollment.save();
      logger.info(`Skipped step ${stepIndex} for enrollment ${enrollment._id}`);
      return true;
    } catch (error) {
      logger.error('Error applying skip step action:', error);
      return false;
    }
  }

  /**
   * Apply add step action
   */
  private async applyAddStepAction(
    enrollment: any,
    action: AdaptiveRuleAction,
    sequence: any
  ): Promise<boolean> {
    try {
      // Get sequence steps
      const sequenceSteps = await SequenceStep.find({
        sequenceId: sequence._id
      }).sort({ order: 1 });
      
      if (sequenceSteps.length === 0) {
        return false;
      }
      
      // Find a step to add (use the first step as a template)
      const templateStep = sequenceSteps[0];
      
      // Create a new step for the enrollment
      const newStep = {
        sequenceStepId: templateStep._id,
        status: 'PENDING',
        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Schedule for tomorrow
        customFields: {
          addedByAdaptiveRule: true
        }
      };
      
      // Add the step to the enrollment
      enrollment.steps.push(newStep);
      await enrollment.save();
      
      logger.info(`Added step for enrollment ${enrollment._id}`);
      return true;
    } catch (error) {
      logger.error('Error applying add step action:', error);
      return false;
    }
  }

  /**
   * Apply change template action
   */
  private async applyChangeTemplateAction(
    enrollment: any,
    action: AdaptiveRuleAction
  ): Promise<boolean> {
    try {
      if (!action.templateId) {
        return false;
      }
      
      // Find the next pending email step
      const pendingEmailStep = enrollment.steps.find((step: any) => 
        step.status === 'PENDING' && 
        step.sequenceStepId.channel === 'email'
      );
      
      if (!pendingEmailStep) {
        return false;
      }
      
      // Update the step with the new template
      pendingEmailStep.customFields = {
        ...pendingEmailStep.customFields,
        originalTemplateId: pendingEmailStep.customFields?.templateId,
        templateId: action.templateId,
        changedByAdaptiveRule: true
      };
      
      await enrollment.save();
      logger.info(`Changed template for step in enrollment ${enrollment._id}`);
      return true;
    } catch (error) {
      logger.error('Error applying change template action:', error);
      return false;
    }
  }

  /**
   * Apply change timing action
   */
  private async applyChangeTimingAction(
    enrollment: any,
    action: AdaptiveRuleAction
  ): Promise<boolean> {
    try {
      if (!action.timing) {
        return false;
      }
      
      // Find the next pending step
      const pendingStep = enrollment.steps.find((step: any) => 
        step.status === 'PENDING'
      );
      
      if (!pendingStep) {
        return false;
      }
      
      // Calculate new scheduled time
      let newScheduledAt = new Date();
      
      switch (action.timing.type) {
        case 'days':
          newScheduledAt.setDate(newScheduledAt.getDate() + action.timing.value);
          break;
          
        case 'hours':
          newScheduledAt.setHours(newScheduledAt.getHours() + action.timing.value);
          break;
          
        case 'minutes':
          newScheduledAt.setMinutes(newScheduledAt.getMinutes() + action.timing.value);
          break;
          
        default:
          return false;
      }
      
      // Update the step with the new timing
      pendingStep.scheduledAt = newScheduledAt;
      pendingStep.customFields = {
        ...pendingStep.customFields,
        originalScheduledAt: pendingStep.scheduledAt,
        changedByAdaptiveRule: true
      };
      
      await enrollment.save();
      logger.info(`Changed timing for step in enrollment ${enrollment._id}`);
      return true;
    } catch (error) {
      logger.error('Error applying change timing action:', error);
      return false;
    }
  }

  /**
   * Apply change channel action
   */
  private async applyChangeChannelAction(
    enrollment: any,
    action: AdaptiveRuleAction
  ): Promise<boolean> {
    try {
      if (!action.channel) {
        return false;
      }
      
      // Find the next pending step
      const pendingStep = enrollment.steps.find((step: any) => 
        step.status === 'PENDING'
      );
      
      if (!pendingStep) {
        return false;
      }
      
      // Get the sequence step
      const sequenceStep = await SequenceStep.findById(pendingStep.sequenceStepId);
      
      if (!sequenceStep) {
        return false;
      }
      
      // Find or create an alternative step with the desired channel
      const alternativeStep = await SequenceStep.findOne({
        sequenceId: sequenceStep.sequenceId,
        channel: action.channel
      });
      
      if (!alternativeStep) {
        return false;
      }
      
      // Update the step with the new channel
      pendingStep.sequenceStepId = alternativeStep._id;
      pendingStep.customFields = {
        ...pendingStep.customFields,
        originalStepId: sequenceStep._id.toString(),
        changedByAdaptiveRule: true
      };
      
      await enrollment.save();
      logger.info(`Changed channel for step in enrollment ${enrollment._id}`);
      return true;
    } catch (error) {
      logger.error('Error applying change channel action:', error);
      return false;
    }
  }

  /**
   * Apply AI-based adaptations
   */
  private async applyAIAdaptations(
    enrollment: any,
    adaptiveConfig: AdaptiveSequenceConfig,
    sequence: any,
    contact: any
  ): Promise<{ type: string; description: string }[]> {
    try {
      const changes: { type: string; description: string }[] = [];
      const tenantId = sequence.tenantId.toString();
      
      // Find the next pending email step
      const pendingEmailStep = enrollment.steps.find((step: any) => 
        step.status === 'PENDING' && 
        step.sequenceStepId.channel === 'email'
      );
      
      if (!pendingEmailStep) {
        return changes;
      }
      
      // Get contact predictions
      const predictions = await emailPredictiveService.generateContactPredictions(
        tenantId,
        [contact._id.toString()]
      );
      
      if (predictions.length === 0) {
        return changes;
      }
      
      const prediction = predictions[0];
      
      // Apply template selection if allowed
      if (adaptiveConfig.allowTemplateSelection && prediction.bestTemplateId) {
        pendingEmailStep.customFields = {
          ...pendingEmailStep.customFields,
          originalTemplateId: pendingEmailStep.customFields?.templateId,
          templateId: prediction.bestTemplateId,
          changedByAI: true
        };
        
        changes.push({
          type: 'template',
          description: `Changed template to "${prediction.bestTemplateName}" based on contact's history`
        });
      }
      
      // Apply timing adjustment if allowed
      if (adaptiveConfig.allowTimingAdjustment && prediction.bestTimeToSend.confidence > 0.5) {
        // Get the best day and hour
        const bestDay = prediction.bestTimeToSend.dayOfWeek;
        const bestHour = prediction.bestTimeToSend.hourOfDay;
        
        // Calculate the next occurrence of this day and hour
        const now = new Date();
        const daysToAdd = (bestDay - now.getDay() + 7) % 7;
        const nextBestDay = new Date(now);
        nextBestDay.setDate(now.getDate() + (daysToAdd === 0 ? 7 : daysToAdd));
        nextBestDay.setHours(bestHour, 0, 0, 0);
        
        // Only change if it's within a reasonable timeframe (7 days)
        const maxScheduleDate = new Date(now);
        maxScheduleDate.setDate(now.getDate() + 7);
        
        if (nextBestDay <= maxScheduleDate) {
          pendingEmailStep.scheduledAt = nextBestDay;
          pendingEmailStep.customFields = {
            ...pendingEmailStep.customFields,
            originalScheduledAt: pendingEmailStep.scheduledAt,
            changedByAI: true
          };
          
          changes.push({
            type: 'timing',
            description: `Scheduled for ${this.getDayName(bestDay)} at ${bestHour}:00 based on optimal engagement time`
          });
        }
      }
      
      // Apply channel switching if allowed
      if (adaptiveConfig.allowChannelSwitching) {
        // Check if we should switch to another channel based on open probability
        if (prediction.openProbability < 0.2) {
          // Find an alternative step with a different channel
          const sequenceStep = await SequenceStep.findById(pendingEmailStep.sequenceStepId);
          
          if (sequenceStep) {
            const alternativeStep = await SequenceStep.findOne({
              sequenceId: sequenceStep.sequenceId,
              channel: { $ne: 'email' }
            });
            
            if (alternativeStep) {
              pendingEmailStep.sequenceStepId = alternativeStep._id;
              pendingEmailStep.customFields = {
                ...pendingEmailStep.customFields,
                originalStepId: sequenceStep._id.toString(),
                changedByAI: true
              };
              
              changes.push({
                type: 'channel',
                description: `Switched to ${alternativeStep.channel} channel due to low email engagement probability`
              });
            }
          }
        }
      }
      
      // Save changes if any were made
      if (changes.length > 0) {
        await enrollment.save();
        logger.info(`Applied AI adaptations for enrollment ${enrollment._id}`);
      }
      
      return changes;
    } catch (error) {
      logger.error('Error applying AI adaptations:', error);
      return [];
    }
  }

  /**
   * Get day name from day of week number
   */
  private getDayName(dayOfWeek: number): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek];
  }

  /**
   * Create default adaptive rules for a sequence
   */
  async createDefaultAdaptiveRules(sequenceId: string): Promise<boolean> {
    try {
      // Get sequence
      const sequence = await Sequence.findById(sequenceId);
      
      if (!sequence) {
        logger.warn(`Sequence ${sequenceId} not found`);
        return false;
      }
      
      // Create default rules
      const defaultRules: AdaptiveRule[] = [
        {
          name: 'Skip next step on reply',
          description: 'Skip the next step if the contact replies to an email',
          condition: {
            type: 'reply',
            timeframe: 48
          },
          action: {
            type: 'skip_step',
            stepIndex: 1
          },
          isActive: true
        },
        {
          name: 'Change template on no open',
          description: 'Try a different template if the contact doesn\'t open an email',
          condition: {
            type: 'no_open',
            timeframe: 48
          },
          action: {
            type: 'change_template',
            // Template ID will be set dynamically
          },
          isActive: true
        },
        {
          name: 'Delay next step on click but no reply',
          description: 'Give the contact more time if they clicked but didn\'t reply',
          condition: {
            type: 'click',
            timeframe: 48
          },
          action: {
            type: 'change_timing',
            timing: {
              type: 'days',
              value: 2
            }
          },
          isActive: true
        }
      ];
      
      // Find a template for the no-open rule
      const templates = await EmailTemplate.find({
        tenantId: sequence.tenantId,
        category: 'follow_up',
        isActive: true
      }).limit(1);
      
      if (templates.length > 0) {
        defaultRules[1].action.templateId = templates[0]._id.toString();
      }
      
      // Update sequence with default rules
      const adaptiveConfig: AdaptiveSequenceConfig = {
        isAdaptive: true,
        optimizeFor: 'replies',
        adaptationLevel: 'moderate',
        rules: defaultRules,
        allowTemplateSelection: true,
        allowTimingAdjustment: true,
        allowChannelSwitching: false
      };
      
      sequence.customFields = {
        ...sequence.customFields,
        adaptiveConfig
      };
      
      await sequence.save();
      logger.info(`Created default adaptive rules for sequence ${sequenceId}`);
      return true;
    } catch (error) {
      logger.error(`Error creating default adaptive rules for sequence ${sequenceId}:`, error);
      return false;
    }
  }
}

export default new AdaptiveSequenceService();
