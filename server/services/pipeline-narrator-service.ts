import mongoose from 'mongoose';
import { Opportunity, IOpportunity, Insight, Activity, Contact, Company } from '../models/mongoose';
import OpenAI from 'openai';
import { generatePipelineInsight } from './pipeline-insight-generator';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || process.env.CRM_OPENAI_API_KEY
});

/**
 * Service to monitor pipeline events and generate insights
 */
export class PipelineNarratorService {
  private static instance: PipelineNarratorService;
  private isMonitoring: boolean = false;
  private changeStream: mongoose.mongo.ChangeStream | null = null;

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): PipelineNarratorService {
    if (!PipelineNarratorService.instance) {
      PipelineNarratorService.instance = new PipelineNarratorService();
    }
    return PipelineNarratorService.instance;
  }

  /**
   * Start monitoring pipeline events
   */
  public async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.log('Pipeline monitoring already active');
      return;
    }

    try {
      // Ensure Opportunity model is initialized
      if (!mongoose.models.Opportunity) {
        throw new Error('Opportunity model not initialized');
      }

      // Create a change stream to watch for opportunity updates
      this.changeStream = Opportunity.watch(
        [{ $match: { 'operationType': { $in: ['update', 'insert'] } } }],
        { fullDocument: 'updateLookup' }
      );

      // Handle change events
      this.changeStream.on('change', async (change) => {
        try {
          await this.handleOpportunityChange(change);
        } catch (error) {
          console.error('Error handling opportunity change:', error);
        }
      });

      this.isMonitoring = true;
      console.log('Pipeline monitoring started');
    } catch (error) {
      console.error('Failed to start pipeline monitoring:', error);
      throw error;
    }
  }

  /**
   * Stop monitoring pipeline events
   */
  public async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring || !this.changeStream) {
      return;
    }

    try {
      await this.changeStream.close();
      this.changeStream = null;
      this.isMonitoring = false;
      console.log('Pipeline monitoring stopped');
    } catch (error) {
      console.error('Error stopping pipeline monitoring:', error);
      throw error;
    }
  }

  /**
   * Handle opportunity change event
   */
  private async handleOpportunityChange(change: any): Promise<void> {
    // Only process updates with stage changes
    if (change.operationType === 'update' && 
        change.updateDescription?.updatedFields?.stage) {
      
      const opportunity = change.fullDocument as IOpportunity;
      const oldStage = change.updateDescription.updatedFields.stage;
      const newStage = opportunity.stage;

      // Skip if stages are the same (shouldn't happen, but just in case)
      if (oldStage === newStage) {
        return;
      }

      await this.generateInsightForStageChange(opportunity, oldStage, newStage);
    }
    // Handle new opportunities
    else if (change.operationType === 'insert') {
      const opportunity = change.fullDocument as IOpportunity;
      await this.generateInsightForNewOpportunity(opportunity);
    }
  }

  /**
   * Generate insight for stage change
   */
  private async generateInsightForStageChange(
    opportunity: IOpportunity,
    oldStage: string,
    newStage: string
  ): Promise<void> {
    try {
      // Get related data for context
      const [contact, company, activities] = await Promise.all([
        opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
        opportunity.companyId ? Company.findById(opportunity.companyId) : null,
        Activity.find({ opportunityId: opportunity._id }).sort({ date: -1 }).limit(5)
      ]);

      // Generate insight using AI
      const insight = await generatePipelineInsight({
        opportunity,
        contact,
        company,
        activities,
        eventType: 'stage_change',
        oldStage,
        newStage
      });

      // Save the insight
      if (insight) {
        const newInsight = new Insight({
          title: insight.title,
          description: insight.description,
          type: insight.type,
          importance: insight.importance,
          targetType: 'opportunity',
          targetId: opportunity._id,
          generatedBy: 'ai',
          isRead: false,
          actionTaken: false
        });
        
        await newInsight.save();
        console.log(`Pipeline insight generated for opportunity ${opportunity.name}`);
      }
    } catch (error) {
      console.error(`Error generating insight for stage change in opportunity ${opportunity._id}:`, error);
    }
  }

  /**
   * Generate insight for new opportunity
   */
  private async generateInsightForNewOpportunity(opportunity: IOpportunity): Promise<void> {
    try {
      // Get related data for context
      const [contact, company] = await Promise.all([
        opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
        opportunity.companyId ? Company.findById(opportunity.companyId) : null
      ]);

      // Generate insight using AI
      const insight = await generatePipelineInsight({
        opportunity,
        contact,
        company,
        activities: [],
        eventType: 'new_opportunity'
      });

      // Save the insight
      if (insight) {
        const newInsight = new Insight({
          title: insight.title,
          description: insight.description,
          type: insight.type,
          importance: insight.importance,
          targetType: 'opportunity',
          targetId: opportunity._id,
          generatedBy: 'ai',
          isRead: false,
          actionTaken: false
        });
        
        await newInsight.save();
        console.log(`Pipeline insight generated for new opportunity ${opportunity.name}`);
      }
    } catch (error) {
      console.error(`Error generating insight for new opportunity ${opportunity._id}:`, error);
    }
  }

  /**
   * Check for stalled opportunities and generate insights
   * This can be run on a schedule (e.g., daily)
   */
  public async checkStalledOpportunities(): Promise<void> {
    try {
      // Find opportunities that haven't been updated in 10+ days and aren't in closed stages
      const tenDaysAgo = new Date();
      tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);
      
      const stalledOpportunities = await Opportunity.find({
        updatedAt: { $lt: tenDaysAgo },
        stage: { $nin: ['closed_won', 'closed_lost'] }
      });
      
      console.log(`Found ${stalledOpportunities.length} stalled opportunities`);
      
      // Generate insights for each stalled opportunity
      for (const opportunity of stalledOpportunities) {
        try {
          // Get related data for context
          const [contact, company, activities] = await Promise.all([
            opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
            opportunity.companyId ? Company.findById(opportunity.companyId) : null,
            Activity.find({ opportunityId: opportunity._id }).sort({ date: -1 }).limit(5)
          ]);
          
          // Generate insight using AI
          const insight = await generatePipelineInsight({
            opportunity,
            contact,
            company,
            activities,
            eventType: 'stalled_opportunity',
            daysSinceUpdate: Math.floor((Date.now() - opportunity.updatedAt.getTime()) / (1000 * 60 * 60 * 24))
          });
          
          // Save the insight
          if (insight) {
            const newInsight = new Insight({
              title: insight.title,
              description: insight.description,
              type: insight.type,
              importance: insight.importance,
              targetType: 'opportunity',
              targetId: opportunity._id,
              generatedBy: 'ai',
              isRead: false,
              actionTaken: false
            });
            
            await newInsight.save();
          }
        } catch (error) {
          console.error(`Error processing stalled opportunity ${opportunity._id}:`, error);
        }
      }
    } catch (error) {
      console.error('Error checking for stalled opportunities:', error);
    }
  }

  /**
   * Check for opportunities with meetings but no proposals
   * This can be run on a schedule (e.g., daily)
   */
  public async checkMeetingsWithoutProposals(): Promise<void> {
    try {
      // Find opportunities in early stages
      const earlyStageOpportunities = await Opportunity.find({
        stage: { $in: ['prospecting', 'qualification', 'needs_analysis'] }
      });
      
      for (const opportunity of earlyStageOpportunities) {
        try {
          // Count meetings for this opportunity
          const meetingCount = await Activity.countDocuments({
            opportunityId: opportunity._id,
            type: 'meeting',
            completed: true
          });
          
          // If 3+ meetings but still in early stage, generate insight
          if (meetingCount >= 3) {
            // Get related data for context
            const [contact, company, activities] = await Promise.all([
              opportunity.contactId ? Contact.findById(opportunity.contactId) : null,
              opportunity.companyId ? Company.findById(opportunity.companyId) : null,
              Activity.find({ opportunityId: opportunity._id }).sort({ date: -1 }).limit(5)
            ]);
            
            // Generate insight using AI
            const insight = await generatePipelineInsight({
              opportunity,
              contact,
              company,
              activities,
              eventType: 'meetings_no_proposal',
              meetingCount
            });
            
            // Save the insight
            if (insight) {
              const newInsight = new Insight({
                title: insight.title,
                description: insight.description,
                type: insight.type,
                importance: insight.importance,
                targetType: 'opportunity',
                targetId: opportunity._id,
                generatedBy: 'ai',
                isRead: false,
                actionTaken: false
              });
              
              await newInsight.save();
            }
          }
        } catch (error) {
          console.error(`Error processing opportunity with meetings ${opportunity._id}:`, error);
        }
      }
    } catch (error) {
      console.error('Error checking for meetings without proposals:', error);
    }
  }
}

export default PipelineNarratorService;
