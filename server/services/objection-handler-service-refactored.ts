/**
 * Service for handling sales objections
 * 
 * This service provides functionality for managing and responding to sales objections.
 * It uses the repository pattern for data access and integrates with AI services for
 * generating responses.
 */
import mongoose from 'mongoose';
import axios from 'axios';
import {
  Contact,
  Company,
  Opportunity,
  Activity
} from '../models/mongoose';
import { graphRAGService } from './graph-rag-service';
import { logger } from '../utils/logger';
import { toObjectId } from '../utils/mongodb-utils';
import {
  isError,
  isContactDocument,
  isCompanyDocument,
  isOpportunityDocument,
  isActivityDocument,
  getDocumentId
} from '../utils/type-guards';
import { ObjectionHandlerContext } from '../../shared/types/graph-rag';
import {
  ContactDocument,
  CompanyDocument,
  OpportunityDocument,
  ActivityDocument
} from '../@types/mongoose-types';
import {
  objectionRepository,
  ObjectionEntity,
  objectionResponseRepository,
  ObjectionResponseEntity
} from '../dal';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

/**
 * Service for handling sales objections
 */
export class ObjectionHandlerService {
  /**
   * Get all objection categories
   */
  async getObjectionCategories(): Promise<string[]> {
    return [
      'price',
      'product',
      'competition',
      'timing',
      'authority',
      'need',
      'trust',
      'other'
    ];
  }

  /**
   * Get all objections
   */
  async getAllObjections(
    filter: {
      category?: string;
      isCommon?: boolean;
      createdBy?: string;
      search?: string;
    } = {}
  ): Promise<ObjectionEntity[]> {
    try {
      if (filter.search) {
        return await objectionRepository.findByTextSearch(filter.search);
      }

      const repoFilter: Record<string, any> = {};
      
      if (filter.category) {
        repoFilter.category = filter.category;
      }
      
      if (filter.isCommon !== undefined) {
        repoFilter.isCommon = filter.isCommon;
      }
      
      if (filter.createdBy) {
        repoFilter.createdBy = filter.createdBy;
      }
      
      return await objectionRepository.findAll(repoFilter, undefined, {
        sort: { name: 1 }
      });
    } catch (error) {
      logger.error('Error getting objections:', error);
      throw error;
    }
  }

  /**
   * Get objection by ID
   */
  async getObjectionById(id: string): Promise<ObjectionEntity | null> {
    try {
      return await objectionRepository.findById(id);
    } catch (error) {
      logger.error(`Error getting objection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new objection
   */
  async createObjection(
    data: {
      name: string;
      category: string;
      description: string;
      tags?: string[];
      isCommon?: boolean;
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<ObjectionEntity> {
    try {
      return await objectionRepository.create({
        ...data,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      logger.error('Error creating objection:', error);
      throw error;
    }
  }

  /**
   * Update an objection
   */
  async updateObjection(
    id: string,
    data: {
      name?: string;
      category?: string;
      description?: string;
      tags?: string[];
      isCommon?: boolean;
      customFields?: Record<string, any>;
    }
  ): Promise<ObjectionEntity | null> {
    try {
      return await objectionRepository.update(id, {
        ...data,
        updatedAt: new Date()
      });
    } catch (error) {
      logger.error(`Error updating objection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an objection
   */
  async deleteObjection(id: string): Promise<boolean> {
    try {
      // First delete all responses for this objection
      const responses = await objectionResponseRepository.findByObjectionId(id);
      
      for (const response of responses) {
        await objectionResponseRepository.delete(response.id);
      }
      
      // Then delete the objection
      return await objectionRepository.delete(id);
    } catch (error) {
      logger.error(`Error deleting objection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get responses for an objection
   */
  async getResponsesForObjection(
    objectionId: string,
    filter: {
      effectiveness?: number;
      isAIGenerated?: boolean;
      createdBy?: string;
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
    } = {}
  ): Promise<ObjectionResponseEntity[]> {
    try {
      const repoFilter: Record<string, any> = { objectionId };
      
      if (filter.effectiveness) {
        repoFilter.effectiveness = { $gte: filter.effectiveness };
      }
      
      if (filter.isAIGenerated !== undefined) {
        repoFilter.isAIGenerated = filter.isAIGenerated;
      }
      
      if (filter.createdBy) {
        repoFilter.createdBy = filter.createdBy;
      }
      
      if (filter.opportunityId) {
        repoFilter.opportunityId = filter.opportunityId;
      }
      
      if (filter.contactId) {
        repoFilter.contactId = filter.contactId;
      }
      
      if (filter.companyId) {
        repoFilter.companyId = filter.companyId;
      }
      
      return await objectionResponseRepository.findAll(repoFilter, undefined, {
        sort: { effectiveness: -1, usedCount: -1 }
      });
    } catch (error) {
      logger.error(`Error getting responses for objection with ID ${objectionId}:`, error);
      throw error;
    }
  }

  /**
   * Create a response for an objection
   */
  async createResponse(
    data: {
      objectionId: string;
      response: string;
      context: string;
      effectiveness?: number;
      opportunityId?: string;
      contactId?: string;
      companyId?: string;
      isAIGenerated?: boolean;
      customFields?: Record<string, any>;
    },
    userId: string
  ): Promise<ObjectionResponseEntity> {
    try {
      return await objectionResponseRepository.create({
        ...data,
        effectiveness: data.effectiveness || 3, // Default to middle rating
        usedCount: 0,
        successCount: 0,
        isAIGenerated: data.isAIGenerated || false,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      logger.error('Error creating objection response:', error);
      throw error;
    }
  }

  /**
   * Update a response
   */
  async updateResponse(
    id: string,
    data: {
      response?: string;
      context?: string;
      effectiveness?: number;
      usedCount?: number;
      successCount?: number;
      customFields?: Record<string, any>;
    }
  ): Promise<ObjectionResponseEntity | null> {
    try {
      return await objectionResponseRepository.update(id, {
        ...data,
        updatedAt: new Date()
      });
    } catch (error) {
      logger.error(`Error updating objection response with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a response
   */
  async deleteResponse(id: string): Promise<boolean> {
    try {
      return await objectionResponseRepository.delete(id);
    } catch (error) {
      logger.error(`Error deleting objection response with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Record response usage
   */
  async recordResponseUsage(
    id: string,
    wasSuccessful: boolean = false
  ): Promise<ObjectionResponseEntity | null> {
    try {
      return await objectionResponseRepository.recordUsage(id, wasSuccessful);
    } catch (error) {
      logger.error(`Error recording usage for objection response with ID ${id}:`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const objectionHandlerService = new ObjectionHandlerService();
