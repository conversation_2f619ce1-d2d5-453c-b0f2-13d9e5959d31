import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { 
  Workflow, 
  WorkflowRun, 
  WorkflowVersion,
  IWorkflow,
  IWorkflowRun,
  IWorkflowVersion,
  IWorkflowNode,
  IWorkflowEdge
} from '../models/mongoose';
import { workflowCompilerService } from './workflow-compiler-service';
import { nlParserService } from './nl-parser-service';
import { logger } from '../utils/logger';
import { SubscriptionClient } from '../utils/subscription-client';

// Subscription client for feature entitlement and usage tracking
const subscriptionClient = new SubscriptionClient();

/**
 * Interface for Create Workflow Request
 */
interface CreateWorkflowRequest {
  name: string;
  description?: string;
  dsl_yaml?: string;
  original_prompt?: string;
  tenant_id: string;
  created_by: string;
  tags?: string[];
}

/**
 * Interface for Update Workflow Request
 */
interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  dsl_yaml?: string;
  nodes?: IWorkflowNode[];
  edges?: IWorkflowEdge[];
  status?: 'draft' | 'active' | 'paused' | 'archived';
  tags?: string[];
  updated_by: string;
}

/**
 * Interface for Workflow Simulation Request
 */
interface SimulateWorkflowRequest {
  workflow_id: string;
  trigger_event: {
    type: string;
    data: Record<string, any>;
  };
  tenant_id: string;
  user_id: string;
}

/**
 * Interface for Execute Workflow Request
 */
interface ExecuteWorkflowRequest {
  workflow_id: string;
  trigger_event: {
    type: string;
    data: Record<string, any>;
  };
  tenant_id: string;
  user_id?: string;
}

/**
 * Workflow Service
 * Manages workflow creation, execution, and management
 */
export class WorkflowService {
  /**
   * Create a new workflow
   */
  async createWorkflow(request: CreateWorkflowRequest): Promise<IWorkflow> {
    try {
      // Check feature entitlement
      const hasAccess = await subscriptionClient.checkFeatureEntitlement(
        request.tenant_id,
        'workflow.create'
      );
      
      if (!hasAccess) {
        throw new Error('Feature not available in your subscription plan');
      }
      
      // If original prompt is provided but no DSL, generate DSL from prompt
      let dslYaml = request.dsl_yaml;
      let nodes: IWorkflowNode[] = [];
      let edges: IWorkflowEdge[] = [];
      
      if (request.original_prompt && !dslYaml) {
        const parseResult = await nlParserService.parsePrompt({
          prompt: request.original_prompt,
          tenant_id: request.tenant_id,
          user_id: request.created_by
        });
        
        if (!parseResult.success) {
          throw new Error(`Failed to parse prompt: ${parseResult.error}`);
        }
        
        dslYaml = parseResult.dsl_yaml;
        nodes = parseResult.nodes || [];
        edges = parseResult.edges || [];
      }
      
      // If DSL is provided but no nodes/edges, compile DSL
      if (dslYaml && (!nodes.length || !edges.length)) {
        const compileResult = workflowCompilerService.compileWorkflow(dslYaml);
        
        if (compileResult.errors.length > 0) {
          throw new Error(`Compilation errors: ${compileResult.errors.join(', ')}`);
        }
        
        nodes = compileResult.nodes;
        edges = compileResult.edges;
      }
      
      // Create workflow
      const workflow = new Workflow({
        tenant_id: request.tenant_id,
        name: request.name,
        description: request.description,
        dsl_yaml: dslYaml,
        nodes,
        edges,
        status: 'draft',
        created_by: request.created_by,
        updated_by: request.created_by,
        original_prompt: request.original_prompt,
        tags: request.tags
      });
      
      // Save workflow
      const savedWorkflow = await workflow.save();
      
      // Record usage
      await subscriptionClient.recordUsage(
        request.tenant_id,
        'workflow.create',
        1
      );
      
      return savedWorkflow;
    } catch (error) {
      logger.error(`Error creating workflow: ${error}`);
      throw error;
    }
  }
  
  /**
   * Get workflow by ID
   */
  async getWorkflow(id: string, tenant_id: string): Promise<IWorkflow> {
    try {
      const workflow = await Workflow.findOne({
        _id: id,
        tenant_id
      });
      
      if (!workflow) {
        throw new Error(`Workflow not found: ${id}`);
      }
      
      return workflow;
    } catch (error) {
      logger.error(`Error getting workflow: ${error}`);
      throw error;
    }
  }
  
  /**
   * List workflows for a tenant
   */
  async listWorkflows(
    tenant_id: string,
    options: {
      status?: 'draft' | 'active' | 'paused' | 'archived';
      tags?: string[];
      limit?: number;
      skip?: number;
    } = {}
  ): Promise<{ workflows: IWorkflow[]; total: number }> {
    try {
      const { status, tags, limit = 20, skip = 0 } = options;
      
      // Build query
      const query: any = { tenant_id };
      
      if (status) {
        query.status = status;
      }
      
      if (tags && tags.length > 0) {
        query.tags = { $in: tags };
      }
      
      // Execute query
      const workflows = await Workflow.find(query)
        .sort({ updated_at: -1 })
        .skip(skip)
        .limit(limit);
      
      // Get total count
      const total = await Workflow.countDocuments(query);
      
      return { workflows, total };
    } catch (error) {
      logger.error(`Error listing workflows: ${error}`);
      throw error;
    }
  }
  
  /**
   * Update workflow
   */
  async updateWorkflow(id: string, tenant_id: string, request: UpdateWorkflowRequest): Promise<IWorkflow> {
    try {
      // Find workflow
      const workflow = await this.getWorkflow(id, tenant_id);
      
      // Check if workflow is being published
      const isPublishing = request.status === 'active' && workflow.status !== 'active';
      
      if (isPublishing) {
        // Check feature entitlement for publishing
        const hasPublishAccess = await subscriptionClient.checkFeatureEntitlement(
          tenant_id,
          'workflow.publish'
        );
        
        if (!hasPublishAccess) {
          throw new Error('Publishing workflows is not available in your subscription plan');
        }
      }
      
      // Update fields
      if (request.name) workflow.name = request.name;
      if (request.description !== undefined) workflow.description = request.description;
      if (request.status) workflow.status = request.status;
      if (request.tags) workflow.tags = request.tags;
      workflow.updated_by = request.updated_by;
      
      // If DSL is updated, recompile
      if (request.dsl_yaml) {
        workflow.dsl_yaml = request.dsl_yaml;
        
        const compileResult = workflowCompilerService.compileWorkflow(request.dsl_yaml);
        
        if (compileResult.errors.length > 0) {
          throw new Error(`Compilation errors: ${compileResult.errors.join(', ')}`);
        }
        
        workflow.nodes = compileResult.nodes;
        workflow.edges = compileResult.edges;
      } else if (request.nodes && request.edges) {
        // If nodes and edges are updated directly
        workflow.nodes = request.nodes;
        workflow.edges = request.edges;
      }
      
      // If publishing, create a version
      if (isPublishing) {
        await this.createWorkflowVersion(workflow);
        
        // Record usage
        await subscriptionClient.recordUsage(
          tenant_id,
          'workflow.publish',
          1
        );
      }
      
      // Save workflow
      const savedWorkflow = await workflow.save();
      
      return savedWorkflow;
    } catch (error) {
      logger.error(`Error updating workflow: ${error}`);
      throw error;
    }
  }
  
  /**
   * Delete workflow
   */
  async deleteWorkflow(id: string, tenant_id: string): Promise<boolean> {
    try {
      const result = await Workflow.deleteOne({
        _id: id,
        tenant_id
      });
      
      if (result.deletedCount === 0) {
        throw new Error(`Workflow not found: ${id}`);
      }
      
      // Delete associated versions
      await WorkflowVersion.deleteMany({
        workflow_id: id,
        tenant_id
      });
      
      return true;
    } catch (error) {
      logger.error(`Error deleting workflow: ${error}`);
      throw error;
    }
  }
  
  /**
   * Create workflow version
   */
  private async createWorkflowVersion(workflow: IWorkflow): Promise<IWorkflowVersion> {
    try {
      const version = new WorkflowVersion({
        workflow_id: workflow._id,
        tenant_id: workflow.tenant_id,
        version: workflow.version,
        dsl_yaml: workflow.dsl_yaml,
        nodes: workflow.nodes,
        edges: workflow.edges,
        created_by: workflow.updated_by
      });
      
      return await version.save();
    } catch (error) {
      logger.error(`Error creating workflow version: ${error}`);
      throw error;
    }
  }
  
  /**
   * Get workflow versions
   */
  async getWorkflowVersions(
    workflow_id: string,
    tenant_id: string
  ): Promise<IWorkflowVersion[]> {
    try {
      const versions = await WorkflowVersion.find({
        workflow_id,
        tenant_id
      }).sort({ version: -1 });
      
      return versions;
    } catch (error) {
      logger.error(`Error getting workflow versions: ${error}`);
      throw error;
    }
  }
  
  /**
   * Simulate workflow execution
   */
  async simulateWorkflow(request: SimulateWorkflowRequest): Promise<IWorkflowRun> {
    try {
      // Check feature entitlement
      const hasAccess = await subscriptionClient.checkFeatureEntitlement(
        request.tenant_id,
        'workflow.simulate'
      );
      
      if (!hasAccess) {
        throw new Error('Workflow simulation is not available in your subscription plan');
      }
      
      // Get workflow
      const workflow = await this.getWorkflow(request.workflow_id, request.tenant_id);
      
      // Create simulation run
      const run = new WorkflowRun({
        workflow_id: workflow._id,
        tenant_id: request.tenant_id,
        trigger_event: request.trigger_event,
        status: 'completed', // Simulations are marked as completed immediately
        created_by: request.user_id,
        is_simulation: true,
        start_ts: new Date(),
        end_ts: new Date(),
        step_executions: workflow.nodes.map(node => ({
          node_id: node.id,
          started_at: new Date(),
          completed_at: new Date(),
          status: 'completed',
          result: { simulated: true }
        }))
      });
      
      // Save run
      const savedRun = await run.save();
      
      // Record usage
      await subscriptionClient.recordUsage(
        request.tenant_id,
        'workflow.simulate',
        1
      );
      
      return savedRun;
    } catch (error) {
      logger.error(`Error simulating workflow: ${error}`);
      throw error;
    }
  }
  
  /**
   * Execute workflow
   * Note: This is a placeholder. In a real implementation, this would
   * queue the workflow for execution by a workflow engine.
   */
  async executeWorkflow(request: ExecuteWorkflowRequest): Promise<IWorkflowRun> {
    try {
      // Check feature entitlement
      const hasAccess = await subscriptionClient.checkFeatureEntitlement(
        request.tenant_id,
        'workflow.execute'
      );
      
      if (!hasAccess) {
        throw new Error('Workflow execution is not available in your subscription plan');
      }
      
      // Get workflow
      const workflow = await this.getWorkflow(request.workflow_id, request.tenant_id);
      
      // Check if workflow is active
      if (workflow.status !== 'active') {
        throw new Error(`Cannot execute workflow with status: ${workflow.status}`);
      }
      
      // Create run
      const run = new WorkflowRun({
        workflow_id: workflow._id,
        tenant_id: request.tenant_id,
        trigger_event: request.trigger_event,
        status: 'pending',
        created_by: request.user_id,
        is_simulation: false,
        start_ts: new Date()
      });
      
      // Save run
      const savedRun = await run.save();
      
      // Record usage
      await subscriptionClient.recordUsage(
        request.tenant_id,
        'workflow.execute',
        1
      );
      
      // In a real implementation, this would queue the workflow for execution
      // For now, we'll just mark it as completed
      savedRun.status = 'completed';
      savedRun.end_ts = new Date();
      await savedRun.save();
      
      return savedRun;
    } catch (error) {
      logger.error(`Error executing workflow: ${error}`);
      throw error;
    }
  }
  
  /**
   * Get workflow runs
   */
  async getWorkflowRuns(
    workflow_id: string,
    tenant_id: string,
    options: {
      status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
      is_simulation?: boolean;
      limit?: number;
      skip?: number;
    } = {}
  ): Promise<{ runs: IWorkflowRun[]; total: number }> {
    try {
      const { status, is_simulation, limit = 20, skip = 0 } = options;
      
      // Build query
      const query: any = { workflow_id, tenant_id };
      
      if (status) {
        query.status = status;
      }
      
      if (is_simulation !== undefined) {
        query.is_simulation = is_simulation;
      }
      
      // Execute query
      const runs = await WorkflowRun.find(query)
        .sort({ start_ts: -1 })
        .skip(skip)
        .limit(limit);
      
      // Get total count
      const total = await WorkflowRun.countDocuments(query);
      
      return { runs, total };
    } catch (error) {
      logger.error(`Error getting workflow runs: ${error}`);
      throw error;
    }
  }
}

// Export a singleton instance
export const workflowService = new WorkflowService();
