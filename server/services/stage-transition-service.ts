import { StageTransition, Opportunity, Activity, Insight } from '../models/mongoose';
import { StageClassifierService, StageTransition as StageTransitionType } from './stage-classifier-service';
import mongoose from 'mongoose';

/**
 * Service for managing opportunity stage transitions
 */
export class StageTransitionService {
  private static instance: StageTransitionService;
  private classifierService: StageClassifierService;

  private constructor() {
    // Initialize stage classifier service
    this.classifierService = StageClassifierService.getInstance();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): StageTransitionService {
    if (!StageTransitionService.instance) {
      StageTransitionService.instance = new StageTransitionService();
    }
    return StageTransitionService.instance;
  }

  /**
   * Create a new stage transition
   */
  public async createStageTransition(transitionData: StageTransitionType): Promise<any> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(transitionData.opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(transitionData.opportunityId);

      // Create the stage transition
      const stageTransition = new StageTransition({
        opportunityId: opportunityObjectId,
        previousStage: transitionData.previousStage,
        newStage: transitionData.newStage,
        confidence: transitionData.confidence,
        explanation: transitionData.explanation,
        keyIndicators: transitionData.keyIndicators || [],
        appliedBy: transitionData.automatic ? 'ai' : null,
        appliedAt: transitionData.automatic ? new Date() : null
      });

      await stageTransition.save();

      // If the transition was automatic, create an activity and insight
      if (transitionData.automatic) {
        // Get the opportunity
        const opportunity = await Opportunity.findById(opportunityObjectId);
        if (!opportunity) {
          throw new Error('Opportunity not found');
        }

        // Create an activity for the stage change
        const activity = new Activity({
          type: 'stage_change',
          title: `Stage changed from ${transitionData.previousStage} to ${transitionData.newStage}`,
          description: `AI automatically changed the stage based on recent activities and communications.`,
          date: new Date(),
          completed: true,
          opportunityId: opportunityObjectId,
          owner: opportunity.owner,
          notes: transitionData.explanation
        });

        await activity.save();

        // Create an insight for the stage change
        const insight = new Insight({
          title: `Stage automatically updated to ${transitionData.newStage}`,
          description: transitionData.explanation,
          type: 'suggestion',
          importance: 4,
          targetType: 'opportunity',
          targetId: opportunityObjectId,
          generatedBy: 'ai',
          isRead: false,
          actionTaken: true
        });

        await insight.save();
      }

      return stageTransition;
    } catch (error) {
      console.error('Error creating stage transition:', error);
      throw error;
    }
  }

  /**
   * Get stage transitions for an opportunity
   */
  public async getStageTransitions(opportunityId: string): Promise<any[]> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      const opportunityObjectId = new mongoose.Types.ObjectId(opportunityId);

      // Get stage transitions for this opportunity
      const transitions = await StageTransition.find({
        opportunityId: opportunityObjectId
      })
      .sort({ createdAt: -1 })
      .exec();

      return transitions;
    } catch (error) {
      console.error('Error getting stage transitions:', error);
      throw error;
    }
  }

  /**
   * Apply a stage transition
   */
  public async applyStageTransition(transitionId: string, userId: string, approved: boolean): Promise<any> {
    try {
      // Check if IDs are valid ObjectIds
      if (!mongoose.Types.ObjectId.isValid(transitionId) || !mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('Invalid ID format');
      }

      const transitionObjectId = new mongoose.Types.ObjectId(transitionId);
      const userObjectId = new mongoose.Types.ObjectId(userId);

      // Get the stage transition
      const transition = await StageTransition.findById(transitionObjectId);
      if (!transition) {
        throw new Error('Stage transition not found');
      }

      // Update the transition
      transition.reviewedBy = userObjectId;
      transition.reviewedAt = new Date();
      transition.approved = approved;
      
      // If approved, apply the stage change
      if (approved) {
        transition.appliedBy = 'user';
        transition.appliedAt = new Date();

        // Update the opportunity stage
        const opportunity = await Opportunity.findById(transition.opportunityId);
        if (!opportunity) {
          throw new Error('Opportunity not found');
        }

        opportunity.stage = transition.newStage;
        await opportunity.save();

        // Create an activity for the stage change
        const activity = new Activity({
          type: 'stage_change',
          title: `Stage changed from ${transition.previousStage} to ${transition.newStage}`,
          description: `User approved the stage change.`,
          date: new Date(),
          completed: true,
          opportunityId: transition.opportunityId,
          owner: userObjectId,
          notes: transition.explanation
        });

        await activity.save();
      }

      await transition.save();

      return transition;
    } catch (error) {
      console.error('Error applying stage transition:', error);
      throw error;
    }
  }

  /**
   * Check for stage transitions for all opportunities
   */
  public async checkAllOpportunities(autoApply: boolean = false): Promise<any[]> {
    try {
      // Use the classifier service to check all opportunities
      const transitions = await this.classifierService.checkAllOpportunities(autoApply);

      // Create stage transition records for each transition
      const createdTransitions = await Promise.all(
        transitions.map(async (transition) => {
          try {
            return await this.createStageTransition(transition);
          } catch (error) {
            console.error(`Error creating stage transition for opportunity ${transition.opportunityId}:`, error);
            return null;
          }
        })
      );

      return createdTransitions.filter(Boolean);
    } catch (error) {
      console.error('Error checking all opportunities for stage transitions:', error);
      return [];
    }
  }

  /**
   * Analyze an opportunity for potential stage change
   */
  public async analyzeOpportunity(opportunityId: string): Promise<any> {
    try {
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        throw new Error('Invalid opportunity ID format');
      }

      // Use the classifier service to analyze the opportunity
      const classification = await this.classifierService.classifyOpportunityStage(opportunityId);

      return classification;
    } catch (error) {
      console.error('Error analyzing opportunity for stage change:', error);
      throw error;
    }
  }
}

export default StageTransitionService;
