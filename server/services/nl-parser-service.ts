import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { SubscriptionClient } from '../utils/subscription-client';

// AI Service configuration
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

// Subscription client for feature entitlement and usage tracking
const subscriptionClient = new SubscriptionClient();

/**
 * Interface for NL Parser Request
 */
interface NLParserRequest {
  prompt: string;
  tenant_id: string;
  user_id: string;
}

/**
 * Interface for NL Parser Response
 */
interface NLParserResponse {
  success: boolean;
  dsl_yaml?: string;
  nodes?: any[];
  edges?: any[];
  error?: string;
  source: 'ai_service' | 'voyage_fallback' | 'error';
}

/**
 * Natural Language Parser Service
 * Converts natural language descriptions into workflow DSL
 */
export class NLParserService {
  /**
   * Parse a natural language prompt into workflow DSL
   */
  async parsePrompt(request: NLParserRequest): Promise<NLParserResponse> {
    try {
      // Check feature entitlement
      const hasAccess = await subscriptionClient.checkFeatureEntitlement(
        request.tenant_id,
        'workflow.nl_parser'
      );
      
      if (!hasAccess) {
        return {
          success: false,
          error: 'Feature not available in your subscription plan',
          source: 'error'
        };
      }
      
      // Record usage
      await subscriptionClient.recordUsage(
        request.tenant_id,
        'workflow.nl_parser',
        1
      );
      
      // Try using AI service first
      try {
        const aiServiceResponse = await this.parseWithAIService(request);
        return {
          ...aiServiceResponse,
          source: 'ai_service'
        };
      } catch (aiError) {
        logger.warn(`AI service error, falling back to Voyage: ${aiError}`);
        
        // Fallback to Voyage AI
        const voyageResponse = await this.parseWithVoyage(request);
        return {
          ...voyageResponse,
          source: 'voyage_fallback'
        };
      }
    } catch (error) {
      logger.error(`Error parsing NL prompt: ${error}`);
      return {
        success: false,
        error: `Failed to parse prompt: ${error}`,
        source: 'error'
      };
    }
  }
  
  /**
   * Parse prompt using AI service
   */
  private async parseWithAIService(request: NLParserRequest): Promise<Omit<NLParserResponse, 'source'>> {
    try {
      const response = await axios.post(`${AI_SERVICE_URL}/workflow/parse`, {
        prompt: request.prompt,
        tenant_id: request.tenant_id,
        user_id: request.user_id
      });
      
      if (response.data && response.data.success) {
        return {
          success: true,
          dsl_yaml: response.data.dsl_yaml,
          nodes: response.data.nodes || [],
          edges: response.data.edges || []
        };
      } else {
        throw new Error(response.data.error || 'Unknown error from AI service');
      }
    } catch (error) {
      logger.error(`Error calling AI service for NL parsing: ${error}`);
      throw error;
    }
  }
  
  /**
   * Parse prompt using Voyage AI as fallback
   */
  private async parseWithVoyage(request: NLParserRequest): Promise<Omit<NLParserResponse, 'source'>> {
    try {
      // System prompt for Voyage AI
      const systemPrompt = `You are an expert workflow compiler that converts natural language descriptions into YAML workflow definitions. 
      
Your task is to parse the user's natural language description of a workflow and convert it into a structured YAML format that follows this schema:

workflow:
  name: string
  description: string
  trigger:
    type: string (one of: lead_score_change, email_event, form_submission, record_update, webhook)
    config: object
  nodes:
    - id: string
      type: string (one of: action, condition)
      data:
        type: string
        config: object
  edges:
    - source: string (node id)
      target: string (node id)
      condition: object (optional)

For actions, use these types: send_email, update_record, create_task, notify_slack, call_webhook
For conditions, use these types: if_condition, wait_until, for_each

Respond ONLY with valid YAML, no explanations or markdown formatting.`;

      // Call Voyage API
      const response = await axios.post(
        VOYAGE_API_URL,
        {
          model: 'voyage-3',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: request.prompt }
          ],
          temperature: 0.2,
          response_format: { type: 'text' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOYAGE_API_KEY}`
          }
        }
      );
      
      if (response.data && response.data.choices && response.data.choices[0]) {
        const dslYaml = response.data.choices[0].message.content.trim();
        
        // Generate basic nodes and edges from YAML
        // In a real implementation, this would be more sophisticated
        const nodes = [
          { id: 'trigger', type: 'trigger', position: { x: 100, y: 100 }, data: { type: 'trigger' } }
        ];
        
        const edges = [];
        
        return {
          success: true,
          dsl_yaml: dslYaml,
          nodes,
          edges
        };
      } else {
        throw new Error('Invalid response from Voyage API');
      }
    } catch (error) {
      logger.error(`Error calling Voyage API for NL parsing: ${error}`);
      return {
        success: false,
        error: `Failed to parse with Voyage: ${error}`
      };
    }
  }
}

// Export a singleton instance
export const nlParserService = new NLParserService();
