/**
 * Service for managing contacts
 * 
 * This service provides functionality for managing contacts in the CRM system.
 * It uses the repository pattern for data access.
 */
import { contactRepository, ContactEntity } from '../dal/contact-repository';
import { companyRepository } from '../dal/company-repository';
import { logger } from '../utils/logger';
import { DocumentNotFoundError } from '../utils/mongodb-errors';

/**
 * Contact creation data
 */
export interface ContactCreateData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  company?: string;
  companyId?: string;
  status?: string;
  source?: string;
  owner?: string;
  tags?: string[];
  notes?: string;
  customFields?: Record<string, any>;
}

/**
 * Contact update data
 */
export interface ContactUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  company?: string;
  companyId?: string;
  status?: string;
  source?: string;
  owner?: string;
  tags?: string[];
  notes?: string;
  customFields?: Record<string, any>;
}

/**
 * Service for managing contacts
 */
export class ContactService {
  /**
   * Get all contacts for a tenant
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   */
  async getAllContacts(
    tenantId: string,
    options: {
      skip?: number;
      limit?: number;
      sort?: Record<string, 1 | -1>;
    } = {}
  ): Promise<ContactEntity[]> {
    try {
      return await contactRepository.findAll({}, tenantId, options);
    } catch (error) {
      logger.error('Error getting all contacts:', error);
      throw error;
    }
  }

  /**
   * Get contact by ID
   * @param id Contact ID
   * @param tenantId Tenant ID
   * @returns Contact or null if not found
   */
  async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
    try {
      return await contactRepository.findById(id, tenantId);
    } catch (error) {
      logger.error(`Error getting contact with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get contact by ID or throw an error if not found
   * @param id Contact ID
   * @param tenantId Tenant ID
   * @returns Contact
   * @throws DocumentNotFoundError if the contact is not found
   */
  async getContactByIdOrThrow(id: string, tenantId: string): Promise<ContactEntity> {
    try {
      return await contactRepository.findByIdOrThrow(id, tenantId);
    } catch (error) {
      logger.error(`Error getting contact with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new contact
   * @param data Contact data
   * @param tenantId Tenant ID
   * @returns Created contact
   */
  async createContact(data: ContactCreateData, tenantId: string): Promise<ContactEntity> {
    try {
      // Validate company ID if provided
      if (data.companyId) {
        const company = await companyRepository.findById(data.companyId, tenantId);
        if (!company) {
          throw new DocumentNotFoundError('Company', data.companyId);
        }
      }

      // Create the contact
      return await contactRepository.create({
        ...data,
        status: data.status || 'new',
        tags: data.tags || [],
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      logger.error('Error creating contact:', error);
      throw error;
    }
  }

  /**
   * Update a contact
   * @param id Contact ID
   * @param data Contact data
   * @param tenantId Tenant ID
   * @returns Updated contact or null if not found
   */
  async updateContact(
    id: string,
    data: ContactUpdateData,
    tenantId: string
  ): Promise<ContactEntity | null> {
    try {
      // Validate company ID if provided
      if (data.companyId) {
        const company = await companyRepository.findById(data.companyId, tenantId);
        if (!company) {
          throw new DocumentNotFoundError('Company', data.companyId);
        }
      }

      // Update the contact
      return await contactRepository.update(id, {
        ...data,
        updatedAt: new Date()
      }, tenantId);
    } catch (error) {
      logger.error(`Error updating contact with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a contact
   * @param id Contact ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   */
  async deleteContact(id: string, tenantId: string): Promise<boolean> {
    try {
      return await contactRepository.delete(id, tenantId);
    } catch (error) {
      logger.error(`Error deleting contact with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Search contacts
   * @param query Search query
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async searchContacts(query: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return await contactRepository.search(query, tenantId);
    } catch (error) {
      logger.error(`Error searching contacts with query "${query}":`, error);
      throw error;
    }
  }

  /**
   * Get contacts by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async getContactsByCompanyId(companyId: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return await contactRepository.findByCompanyId(companyId, tenantId);
    } catch (error) {
      logger.error(`Error getting contacts by company ID ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Get contacts by status
   * @param status Status
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async getContactsByStatus(status: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return await contactRepository.findByStatus(status, tenantId);
    } catch (error) {
      logger.error(`Error getting contacts by status ${status}:`, error);
      throw error;
    }
  }

  /**
   * Get contacts by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async getContactsByOwner(ownerId: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return await contactRepository.findByOwner(ownerId, tenantId);
    } catch (error) {
      logger.error(`Error getting contacts by owner ${ownerId}:`, error);
      throw error;
    }
  }

  /**
   * Get contacts by tag
   * @param tag Tag
   * @param tenantId Tenant ID
   * @returns Array of contacts
   */
  async getContactsByTag(tag: string, tenantId: string): Promise<ContactEntity[]> {
    try {
      return await contactRepository.findByTag(tag, tenantId);
    } catch (error) {
      logger.error(`Error getting contacts by tag ${tag}:`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const contactService = new ContactService();
