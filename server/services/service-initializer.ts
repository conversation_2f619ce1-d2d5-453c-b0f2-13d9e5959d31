import { PipelineNarratorService } from './pipeline-narrator-service';
import { PipelineScheduler } from './pipeline-scheduler';
import { StageScheduler } from './stage-scheduler';
import { initializeMCPServer, shutdownMCPServer } from './mcp-initializer';
import { analyticsEventBus } from './analytics-event-bus';
import { graphRAGService } from './graph-rag-service';
import { logger } from '../utils/logger';

/**
 * Initialize all services
 */
export async function initializeServices(): Promise<void> {
  try {
    console.log('Initializing services...');

    // Only initialize if MongoDB is enabled
    if (process.env.MONGODB_ENABLED === 'true') {
      // Initialize Analytics Event Bus
      await initializeAnalyticsEventBus();

      // Initialize GraphRAG service
      await initializeGraphRAGService();

      // Initialize pipeline monitoring
      await initializePipelineMonitoring();

      // Initialize stage monitoring
      await initializeStageMonitoring();

      // Initialize MCP Server
      await initializeMCPServer();
    } else {
      console.log('MongoDB is not enabled, skipping service initialization');
    }

    console.log('Services initialized successfully');
  } catch (error) {
    console.error('Error initializing services:', error);
    throw error;
  }
}

/**
 * Initialize pipeline monitoring
 */
async function initializePipelineMonitoring(): Promise<void> {
  try {
    console.log('Initializing pipeline monitoring...');

    // Start pipeline monitoring
    const pipelineService = PipelineNarratorService.getInstance();
    await pipelineService.startMonitoring();

    // Start pipeline scheduler
    const pipelineScheduler = PipelineScheduler.getInstance();
    pipelineScheduler.startScheduler();

    console.log('Pipeline monitoring initialized successfully');
  } catch (error) {
    console.error('Error initializing pipeline monitoring:', error);
    throw error;
  }
}

/**
 * Shutdown all services
 */
export async function shutdownServices(): Promise<void> {
  try {
    console.log('Shutting down services...');

    // Only shutdown if MongoDB is enabled
    if (process.env.MONGODB_ENABLED === 'true') {
      // Shutdown pipeline monitoring
      await shutdownPipelineMonitoring();

      // Shutdown stage monitoring
      await shutdownStageMonitoring();

      // Shutdown MCP Server
      await shutdownMCPServer();
    }

    console.log('Services shut down successfully');
  } catch (error) {
    console.error('Error shutting down services:', error);
    throw error;
  }
}

/**
 * Shutdown pipeline monitoring
 */
async function shutdownPipelineMonitoring(): Promise<void> {
  try {
    console.log('Shutting down pipeline monitoring...');

    // Stop pipeline monitoring
    const pipelineService = PipelineNarratorService.getInstance();
    await pipelineService.stopMonitoring();

    // Stop pipeline scheduler
    const pipelineScheduler = PipelineScheduler.getInstance();
    pipelineScheduler.stopScheduler();

    console.log('Pipeline monitoring shut down successfully');
  } catch (error) {
    console.error('Error shutting down pipeline monitoring:', error);
    throw error;
  }
}

/**
 * Initialize stage monitoring
 */
async function initializeStageMonitoring(): Promise<void> {
  try {
    console.log('Initializing stage monitoring...');

    // Start stage scheduler
    const stageScheduler = StageScheduler.getInstance();

    // Auto-apply stage changes if enabled in environment
    const autoApply = process.env.AUTO_APPLY_STAGE_CHANGES === 'true';
    stageScheduler.startScheduler(autoApply);

    console.log('Stage monitoring initialized successfully');
  } catch (error) {
    console.error('Error initializing stage monitoring:', error);
    throw error;
  }
}

/**
 * Shutdown stage monitoring
 */
async function shutdownStageMonitoring(): Promise<void> {
  try {
    console.log('Shutting down stage monitoring...');

    // Stop stage scheduler
    const stageScheduler = StageScheduler.getInstance();
    stageScheduler.stopScheduler();

    console.log('Stage monitoring shut down successfully');
  } catch (error) {
    console.error('Error shutting down stage monitoring:', error);
    throw error;
  }
}

/**
 * Initialize Analytics Event Bus
 */
async function initializeAnalyticsEventBus(): Promise<void> {
  try {
    logger.info('Initializing Analytics Event Bus...');

    // Initialize the Analytics Event Bus
    await analyticsEventBus.initialize();

    logger.info('Analytics Event Bus initialized successfully');
  } catch (error) {
    logger.error('Error initializing Analytics Event Bus:', error);
    throw error;
  }
}

/**
 * Initialize GraphRAG Service
 */
async function initializeGraphRAGService(): Promise<void> {
  try {
    logger.info('Initializing GraphRAG Service...');

    // Initialize the GraphRAG Service
    await graphRAGService.initialize();

    logger.info('GraphRAG Service initialized successfully');
  } catch (error) {
    logger.error('Error initializing GraphRAG Service:', error);
    throw error;
  }
}
