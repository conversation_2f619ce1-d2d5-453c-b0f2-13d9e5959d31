import {
  Experiment,
  ExperimentResult,
  IExperiment,
  IExperimentResult,
  mapExperimentToType,
  mapExperimentResultToType
} from '../models/mongoose/experiment-model';
import mongoose from 'mongoose';
import { Request } from 'express';
import {
  ExperimentContext,
  Experiment as ExperimentType,
  ExperimentResult as ExperimentResultType,
  CreateExperimentRequest,
  UpdateExperimentRequest
} from '@types/experiments';
import {
  isExperimentFilter,
  isExperimentStatus
} from '@types/guards/experiments';

export class ExperimentService {
  /**
   * Get an experiment by key
   */
  static async getExperiment(key: string): Promise<ExperimentType | null> {
    const experiment = await Experiment.findOne({ key });
    return experiment ? mapExperimentToType(experiment) : null;
  }

  /**
   * Get all experiments
   */
  static async getAllExperiments(options: {
    status?: 'draft' | 'running' | 'paused' | 'completed' | 'archived';
    limit?: number;
    offset?: number;
  } = {}): Promise<ExperimentType[]> {
    const { status, limit = 100, offset = 0 } = options;

    const query: any = {};

    if (status && isExperimentStatus(status)) {
      query.status = status;
    }

    const experiments = await Experiment.find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);

    return experiments.map(mapExperimentToType);
  }

  /**
   * Create a new experiment
   */
  static async createExperiment(data: CreateExperimentRequest & { createdBy: string }): Promise<ExperimentType> {
    const {
      key,
      name,
      description,
      variants,
      audience = {},
      goals,
      createdBy,
    } = data;

    // Validate filters if provided
    if (audience.filters && audience.filters.length > 0) {
      if (!audience.filters.every(isExperimentFilter)) {
        throw new Error('Invalid experiment filters');
      }
    }

    // Convert string IDs to ObjectIds
    const userIds = audience.userIds?.map(id => new mongoose.Types.ObjectId(id)) || [];
    const tenantIds = audience.tenantIds?.map(id => new mongoose.Types.ObjectId(id)) || [];

    // Ensure variant weights sum to 100
    const totalWeight = variants.reduce((sum, variant) => sum + variant.weight, 0);
    const normalizedVariants = variants.map(variant => ({
      ...variant,
      weight: Math.round((variant.weight / totalWeight) * 100),
    }));

    // Ensure at least one primary goal
    const goalsWithPrimary = goals.map((goal, index) => ({
      ...goal,
      primary: goal.primary || index === 0,
    }));

    const experiment = new Experiment({
      key,
      name,
      description,
      status: 'draft',
      variants: normalizedVariants,
      audience: {
        percentage: audience.percentage || 100,
        userIds,
        tenantIds,
        filters: audience.filters || [],
      },
      goals: goalsWithPrimary,
      createdBy: new mongoose.Types.ObjectId(createdBy),
    });

    const savedExperiment = await experiment.save();
    return mapExperimentToType(savedExperiment);
  }

  /**
   * Update an experiment
   */
  static async updateExperiment(key: string, data: UpdateExperimentRequest): Promise<ExperimentType | null> {
    const updateData: any = { ...data };

    // Validate status if provided
    if (data.status && !isExperimentStatus(data.status)) {
      throw new Error('Invalid experiment status');
    }

    // Validate filters if provided
    if (data.audience?.filters && data.audience.filters.length > 0) {
      if (!data.audience.filters.every(isExperimentFilter)) {
        throw new Error('Invalid experiment filters');
      }
    }

    // Convert string IDs to ObjectIds if provided
    if (data.audience?.userIds) {
      updateData.audience.userIds = data.audience.userIds.map(id => new mongoose.Types.ObjectId(id));
    }

    if (data.audience?.tenantIds) {
      updateData.audience.tenantIds = data.audience.tenantIds.map(id => new mongoose.Types.ObjectId(id));
    }

    // Normalize variant weights if provided
    if (data.variants) {
      const totalWeight = data.variants.reduce((sum, variant) => sum + variant.weight, 0);
      updateData.variants = data.variants.map(variant => ({
        ...variant,
        weight: Math.round((variant.weight / totalWeight) * 100),
      }));
    }

    // Ensure at least one primary goal if goals are provided
    if (data.goals) {
      const hasPrimary = data.goals.some(goal => goal.primary);
      updateData.goals = data.goals.map((goal, index) => ({
        ...goal,
        primary: goal.primary || (!hasPrimary && index === 0),
      }));
    }

    const updatedExperiment = await Experiment.findOneAndUpdate(
      { key },
      { $set: updateData },
      { new: true }
    );

    return updatedExperiment ? mapExperimentToType(updatedExperiment) : null;
  }

  /**
   * Delete an experiment
   */
  static async deleteExperiment(key: string): Promise<boolean> {
    const result = await Experiment.deleteOne({ key });
    return result.deletedCount === 1;
  }

  /**
   * Get experiment variant for a user
   */
  static async getVariant(experimentKey: string, context: ExperimentContext): Promise<string | null> {
    try {
      // Get the experiment
      const experiment = await Experiment.findOne({ key: experimentKey });

      // If the experiment doesn't exist or is not running, return null
      if (!experiment || experiment.status !== 'running') {
        return null;
      }

      // Check if the experiment has started and not ended
      const now = new Date();
      if (experiment.startDate && now < experiment.startDate) {
        return null;
      }
      if (experiment.endDate && now > experiment.endDate) {
        return null;
      }

      // Check if the user is in the audience
      if (!this.isInAudience(experiment, context)) {
        return null;
      }

      // Check if the user already has a variant assigned
      const existingResult = await ExperimentResult.findOne({
        experimentId: experiment._id,
        userId: new mongoose.Types.ObjectId(context.userId),
      });

      if (existingResult) {
        // Record exposure
        await ExperimentResult.updateOne(
          { _id: existingResult._id },
          {
            $push: {
              exposures: {
                timestamp: new Date(),
                context: {
                  sessionId: context.sessionId,
                  ...context.custom,
                },
              },
            },
          }
        );

        return existingResult.variant;
      }

      // Assign a variant based on weights
      const variant = this.assignVariant(experiment.variants);

      // Record the assignment
      const experimentResult = new ExperimentResult({
        experimentId: experiment._id,
        userId: new mongoose.Types.ObjectId(context.userId),
        tenantId: context.tenantId ? new mongoose.Types.ObjectId(context.tenantId) : undefined,
        sessionId: context.sessionId,
        variant: variant.key,
        exposures: [
          {
            timestamp: new Date(),
            context: {
              sessionId: context.sessionId,
              ...context.custom,
            },
          },
        ],
        conversions: [],
      });

      await experimentResult.save();

      return variant.key;
    } catch (error) {
      console.error(`Error getting variant for experiment ${experimentKey}:`, error);
      return null;
    }
  }

  /**
   * Track a conversion for an experiment
   */
  static async trackConversion(
    experimentKey: string,
    goalKey: string,
    context: ExperimentContext,
    value?: number,
    metadata?: Record<string, any>
  ): Promise<boolean> {
    try {
      // Get the experiment
      const experiment = await Experiment.findOne({ key: experimentKey });

      // If the experiment doesn't exist, return false
      if (!experiment) {
        return false;
      }

      // Check if the goal exists
      const goal = experiment.goals.find(g => g.key === goalKey);
      if (!goal) {
        return false;
      }

      // Find the user's experiment result
      const experimentResult = await ExperimentResult.findOne({
        experimentId: experiment._id,
        userId: new mongoose.Types.ObjectId(context.userId),
      });

      // If the user is not in the experiment, return false
      if (!experimentResult) {
        return false;
      }

      // Record the conversion
      await ExperimentResult.updateOne(
        { _id: experimentResult._id },
        {
          $push: {
            conversions: {
              goalKey,
              timestamp: new Date(),
              value,
              metadata: {
                sessionId: context.sessionId,
                ...metadata,
              },
            },
          },
        }
      );

      return true;
    } catch (error) {
      console.error(`Error tracking conversion for experiment ${experimentKey}:`, error);
      return false;
    }
  }

  /**
   * Get experiment results
   */
  static async getResults(experimentKey: string): Promise<{
    experiment: IExperiment;
    variants: {
      key: string;
      name: string;
      users: number;
      conversions: {
        goalKey: string;
        count: number;
        conversionRate: number;
        value?: number;
      }[];
    }[];
  }> {
    // Get the experiment
    const experiment = await Experiment.findOne({ key: experimentKey });

    if (!experiment) {
      throw new Error(`Experiment ${experimentKey} not found`);
    }

    // Get all results for the experiment
    const results = await ExperimentResult.find({
      experimentId: experiment._id,
    });

    // Calculate metrics for each variant
    const variantResults = experiment.variants.map(variant => {
      // Filter results for this variant
      const variantResults = results.filter(result => result.variant === variant.key);

      // Calculate conversions for each goal
      const conversions = experiment.goals.map(goal => {
        // Count users who converted for this goal
        const convertedUsers = variantResults.filter(result =>
          result.conversions.some(conversion => conversion.goalKey === goal.key)
        ).length;

        // Calculate conversion rate
        const conversionRate = variantResults.length > 0
          ? (convertedUsers / variantResults.length) * 100
          : 0;

        // Calculate total value if applicable
        const totalValue = variantResults.reduce((sum, result) => {
          const goalConversions = result.conversions.filter(c => c.goalKey === goal.key);
          return sum + goalConversions.reduce((goalSum, c) => goalSum + (c.value || 0), 0);
        }, 0);

        return {
          goalKey: goal.key,
          count: convertedUsers,
          conversionRate,
          value: totalValue > 0 ? totalValue : undefined,
        };
      });

      return {
        key: variant.key,
        name: variant.name,
        users: variantResults.length,
        conversions,
      };
    });

    return {
      experiment,
      variants: variantResults,
    };
  }

  /**
   * Get experiment context from request
   */
  static getContextFromRequest(req: Request): ExperimentContext {
    return {
      userId: req.session?.userId,
      tenantId: req.session?.tenantId,
      sessionId: req.sessionID || req.headers['x-session-id'] as string || this.generateSessionId(),
      location: {
        country: req.headers['cf-ipcountry'] as string,
        region: req.headers['cf-region'] as string,
      },
      device: {
        type: this.getDeviceType(req.headers['user-agent'] as string),
        browser: this.getBrowser(req.headers['user-agent'] as string),
        os: this.getOS(req.headers['user-agent'] as string),
      },
      custom: {
        ip: req.ip,
        referrer: req.headers.referer,
        path: req.path,
      },
    };
  }

  /**
   * Check if a user is in the audience for an experiment
   */
  private static isInAudience(experiment: IExperiment, context: ExperimentContext): boolean {
    const { audience } = experiment;

    // Check if the user is explicitly included
    if (audience.userIds && audience.userIds.length > 0) {
      const userIdStr = context.userId.toString();
      if (audience.userIds.some(id => id.toString() === userIdStr)) {
        return true;
      }
      // If there are explicit user IDs and the user is not in the list, exclude them
      return false;
    }

    // Check if the tenant is explicitly included
    if (context.tenantId && audience.tenantIds && audience.tenantIds.length > 0) {
      const tenantIdStr = context.tenantId.toString();
      if (audience.tenantIds.some(id => id.toString() === tenantIdStr)) {
        return true;
      }
      // If there are explicit tenant IDs and the tenant is not in the list, exclude them
      return false;
    }

    // Check audience percentage
    if (audience.percentage < 100) {
      const hash = this.hashString(`${experiment.key}:${context.userId}`);
      const percentage = hash % 100;

      if (percentage >= audience.percentage) {
        return false;
      }
    }

    // Check filters
    if (audience.filters && audience.filters.length > 0) {
      for (const filter of audience.filters) {
        if (!this.evaluateFilter(filter, context)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Evaluate a filter against a context
   */
  private static evaluateFilter(filter: { type: string; value: any }, context: ExperimentContext): boolean {
    switch (filter.type) {
      case 'location':
        if (filter.value.country && context.location?.country) {
          return filter.value.country === context.location.country;
        }
        if (filter.value.region && context.location?.region) {
          return filter.value.region === context.location.region;
        }
        return false;

      case 'device':
        if (filter.value.type && context.device?.type) {
          return filter.value.type === context.device.type;
        }
        if (filter.value.browser && context.device?.browser) {
          return filter.value.browser === context.device.browser;
        }
        if (filter.value.os && context.device?.os) {
          return filter.value.os === context.device.os;
        }
        return false;

      case 'custom':
        if (!context.custom || !filter.value.key) {
          return false;
        }

        const customValue = context.custom[filter.value.key];
        return customValue === filter.value.value;

      default:
        return false;
    }
  }

  /**
   * Assign a variant based on weights
   */
  private static assignVariant(variants: IExperiment['variants']): IExperiment['variants'][0] {
    const totalWeight = variants.reduce((sum, variant) => sum + variant.weight, 0);
    const random = Math.random() * totalWeight;

    let cumulativeWeight = 0;
    for (const variant of variants) {
      cumulativeWeight += variant.weight;
      if (random <= cumulativeWeight) {
        return variant;
      }
    }

    // Fallback to the first variant
    return variants[0];
  }

  /**
   * Simple hash function for consistent percentage assignments
   */
  private static hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Generate a random session ID
   */
  private static generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Get device type from user agent
   */
  private static getDeviceType(userAgent?: string): string | undefined {
    if (!userAgent) return undefined;

    if (/mobile/i.test(userAgent)) {
      return 'mobile';
    } else if (/tablet/i.test(userAgent)) {
      return 'tablet';
    } else if (/ipad/i.test(userAgent)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /**
   * Get browser from user agent
   */
  private static getBrowser(userAgent?: string): string | undefined {
    if (!userAgent) return undefined;

    if (/chrome/i.test(userAgent)) {
      return 'chrome';
    } else if (/firefox/i.test(userAgent)) {
      return 'firefox';
    } else if (/safari/i.test(userAgent)) {
      return 'safari';
    } else if (/edge/i.test(userAgent)) {
      return 'edge';
    } else if (/opera/i.test(userAgent) || /opr/i.test(userAgent)) {
      return 'opera';
    } else if (/msie/i.test(userAgent) || /trident/i.test(userAgent)) {
      return 'ie';
    } else {
      return 'other';
    }
  }

  /**
   * Get OS from user agent
   */
  private static getOS(userAgent?: string): string | undefined {
    if (!userAgent) return undefined;

    if (/windows/i.test(userAgent)) {
      return 'windows';
    } else if (/macintosh|mac os x/i.test(userAgent)) {
      return 'mac';
    } else if (/linux/i.test(userAgent)) {
      return 'linux';
    } else if (/android/i.test(userAgent)) {
      return 'android';
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      return 'ios';
    } else {
      return 'other';
    }
  }
}
