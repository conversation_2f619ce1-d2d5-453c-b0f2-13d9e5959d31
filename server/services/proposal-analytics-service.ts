import { ProposalAnalytics, IProposalAnalytics, Proposal } from '../models/mongoose';
import mongoose from 'mongoose';
import { Request } from 'express';

export class ProposalAnalyticsService {
  /**
   * Get or create analytics for a proposal
   */
  async getOrCreateAnalytics(proposalId: string): Promise<IProposalAnalytics> {
    try {
      // Check if analytics already exist
      let analytics = await ProposalAnalytics.findOne({ proposalId: new mongoose.Types.ObjectId(proposalId) });

      if (!analytics) {
        // Create new analytics
        analytics = new ProposalAnalytics({
          proposalId: new mongoose.Types.ObjectId(proposalId),
          views: [],
          downloads: [],
          shares: [],
          interactions: [],
          totalViews: 0,
          uniqueViews: 0,
          totalDownloads: 0,
          totalShares: 0,
          averageViewDuration: 0
        });
        await analytics.save();
      }

      return analytics;
    } catch (error) {
      console.error(`Error getting analytics for proposal ${proposalId}:`, error);
      throw error;
    }
  }

  /**
   * Track a proposal view
   */
  async trackView(
    proposalId: string,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    try {
      const analytics = await this.getOrCreateAnalytics(proposalId);
      
      // Get client info
      const ip = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';
      const referrer = req.headers.referer || 'unknown';
      
      // Check if this is a unique view (based on IP and user agent)
      const isUnique = !analytics.views.some(view => 
        view.ip === ip && 
        view.userAgent === userAgent && 
        // Consider views more than 1 hour apart as unique
        (new Date().getTime() - new Date(view.timestamp).getTime() > 3600000)
      );
      
      // Add view
      analytics.views.push({
        timestamp: new Date(),
        ip,
        userAgent,
        referrer,
        isUnique,
        shareToken
      });
      
      // Update counters
      analytics.totalViews += 1;
      if (isUnique) {
        analytics.uniqueViews += 1;
      }
      
      analytics.lastViewedAt = new Date();
      
      // Update proposal status if it's not already viewed or accepted
      const proposal = await Proposal.findById(proposalId);
      if (proposal && proposal.status === 'sent') {
        proposal.status = 'viewed';
        proposal.viewedAt = new Date();
        await proposal.save();
      }
      
      await analytics.save();
    } catch (error) {
      console.error(`Error tracking view for proposal ${proposalId}:`, error);
      // Don't throw error to prevent affecting the user experience
    }
  }

  /**
   * Track a proposal download
   */
  async trackDownload(
    proposalId: string,
    format: string,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    try {
      const analytics = await this.getOrCreateAnalytics(proposalId);
      
      // Get client info
      const ip = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';
      
      // Check if this is a unique download (based on IP and user agent)
      const isUnique = !analytics.downloads.some(download => 
        download.ip === ip && 
        download.userAgent === userAgent && 
        download.format === format &&
        // Consider downloads more than 1 hour apart as unique
        (new Date().getTime() - new Date(download.timestamp).getTime() > 3600000)
      );
      
      // Add download
      analytics.downloads.push({
        timestamp: new Date(),
        format,
        ip,
        userAgent,
        isUnique,
        shareToken
      });
      
      // Update counter
      analytics.totalDownloads += 1;
      
      await analytics.save();
    } catch (error) {
      console.error(`Error tracking download for proposal ${proposalId}:`, error);
      // Don't throw error to prevent affecting the user experience
    }
  }

  /**
   * Track a proposal share
   */
  async trackShare(
    proposalId: string,
    userId: string,
    method: 'link' | 'email' | 'social',
    shareToken: string,
    expiresAt: Date,
    platform?: string,
    recipientEmail?: string
  ): Promise<void> {
    try {
      const analytics = await this.getOrCreateAnalytics(proposalId);
      
      // Add share
      analytics.shares.push({
        timestamp: new Date(),
        method,
        platform,
        recipientEmail,
        shareToken,
        expiresAt,
        sharedBy: new mongoose.Types.ObjectId(userId)
      });
      
      // Update counter
      analytics.totalShares += 1;
      
      await analytics.save();
    } catch (error) {
      console.error(`Error tracking share for proposal ${proposalId}:`, error);
      // Don't throw error to prevent affecting the user experience
    }
  }

  /**
   * Track a custom interaction
   */
  async trackInteraction(
    proposalId: string,
    type: string,
    data: Record<string, any>,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    try {
      const analytics = await this.getOrCreateAnalytics(proposalId);
      
      // Get client info
      const ip = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';
      
      // Add interaction
      analytics.interactions.push({
        timestamp: new Date(),
        type,
        data,
        ip,
        userAgent,
        shareToken
      });
      
      await analytics.save();
    } catch (error) {
      console.error(`Error tracking interaction for proposal ${proposalId}:`, error);
      // Don't throw error to prevent affecting the user experience
    }
  }

  /**
   * Update view duration
   */
  async updateViewDuration(
    proposalId: string,
    duration: number,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    try {
      const analytics = await this.getOrCreateAnalytics(proposalId);
      
      // Get client info
      const ip = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';
      
      // Find the most recent view from this client
      const viewIndex = analytics.views.findIndex(view => 
        view.ip === ip && 
        view.userAgent === userAgent &&
        (!shareToken || view.shareToken === shareToken) &&
        // Only consider views from the last hour
        (new Date().getTime() - new Date(view.timestamp).getTime() <= 3600000)
      );
      
      if (viewIndex !== -1) {
        // Update view duration
        analytics.views[viewIndex].viewDuration = duration;
        
        // Recalculate average view duration
        const totalDuration = analytics.views.reduce((sum, view) => sum + (view.viewDuration || 0), 0);
        const viewsWithDuration = analytics.views.filter(view => view.viewDuration !== undefined).length;
        
        if (viewsWithDuration > 0) {
          analytics.averageViewDuration = totalDuration / viewsWithDuration;
        }
        
        await analytics.save();
      }
    } catch (error) {
      console.error(`Error updating view duration for proposal ${proposalId}:`, error);
      // Don't throw error to prevent affecting the user experience
    }
  }

  /**
   * Get analytics for a proposal
   */
  async getAnalytics(proposalId: string): Promise<IProposalAnalytics | null> {
    try {
      return await ProposalAnalytics.findOne({ proposalId: new mongoose.Types.ObjectId(proposalId) });
    } catch (error) {
      console.error(`Error getting analytics for proposal ${proposalId}:`, error);
      throw error;
    }
  }
}
