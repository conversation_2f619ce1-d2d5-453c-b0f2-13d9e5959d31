/**
 * Sequence Service
 *
 * This service handles sales sequence functionality including:
 * - Creating and managing sequences
 * - Enrolling contacts in sequences
 * - Executing sequence steps
 * - Adaptive sequence optimization
 */

import mongoose from 'mongoose';
import axios from 'axios';
import {
  Sequence,
  SequenceStep,
  SequenceEnrollment,
  ISequence,
  ISequenceStep,
  ISequenceEnrollment,
  SequenceStepChannelType,
  SequenceStepTimingType,
  Contact,
  EmailTracking
} from '../models/mongoose';
import { EmailService } from './email-service';
import { EmailTrackingService } from './email-tracking-service';
import { notificationService } from './notification-service';
import { mcpServer, MCPSourceType } from '../mcp';
import { EmailAdapter } from '../mcp/adapters/email-adapter';
import { logger } from '../utils/logger';

// Configuration for AI service
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8000';
const VOYAGE_API_URL = process.env.VOYAGE_API_URL || 'https://api.voyageai.com/v1/chat/completions';
const VOYAGE_API_KEY = process.env.VOYAGE_API_KEY || '';

// Email service instance
const emailService = new EmailService();
const emailTrackingService = new EmailTrackingService();

class SequenceService {
  /**
   * Create a new sequence
   */
  async createSequence(data: {
    userId: string;
    tenantId?: string;
    name: string;
    description?: string;
    goal: string;
    targetConversionDays?: number;
    isTemplate?: boolean;
    tags?: string[];
    customFields?: Record<string, any>;
  }): Promise<ISequence> {
    try {
      // Create the sequence
      const sequence = new Sequence({
        userId: new mongoose.Types.ObjectId(data.userId),
        tenantId: data.tenantId ? new mongoose.Types.ObjectId(data.tenantId) : undefined,
        name: data.name,
        description: data.description,
        goal: data.goal,
        targetConversionDays: data.targetConversionDays || 14,
        isActive: true,
        isTemplate: data.isTemplate || false,
        tags: data.tags || [],
        customFields: data.customFields || {}
      });

      await sequence.save();
      logger.info(`Sequence created: ${sequence.name}`);
      return sequence;
    } catch (error) {
      logger.error('Error creating sequence:', error);
      throw error;
    }
  }

  /**
   * Add a step to a sequence
   */
  async addSequenceStep(data: {
    sequenceId: string;
    stepNumber: number;
    channel: SequenceStepChannelType;
    templateId?: string;
    content?: string;
    subject?: string;
    timing: {
      type: SequenceStepTimingType;
      value: number | string | Date;
      timeOfDay?: string;
    };
    aiDynamic?: boolean;
    conditions?: {
      type: string;
      value: any;
    }[];
    metadata?: Record<string, any>;
  }): Promise<ISequenceStep> {
    try {
      // Create the sequence step
      const step = new SequenceStep({
        sequenceId: new mongoose.Types.ObjectId(data.sequenceId),
        stepNumber: data.stepNumber,
        channel: data.channel,
        templateId: data.templateId ? new mongoose.Types.ObjectId(data.templateId) : undefined,
        content: data.content,
        subject: data.subject,
        timing: data.timing,
        aiDynamic: data.aiDynamic || false,
        conditions: data.conditions || [],
        metadata: data.metadata || {}
      });

      await step.save();
      logger.info(`Sequence step added: ${step.stepNumber} to sequence ${data.sequenceId}`);
      return step;
    } catch (error) {
      logger.error('Error adding sequence step:', error);
      throw error;
    }
  }

  /**
   * Enroll a contact in a sequence
   */
  async enrollContact(data: {
    sequenceId: string;
    contactId: string;
    userId: string;
    tenantId?: string;
    startDate?: Date;
    customFields?: Record<string, any>;
  }): Promise<ISequenceEnrollment> {
    try {
      // Check if the contact is already enrolled in this sequence
      const existingEnrollment = await SequenceEnrollment.findOne({
        sequenceId: new mongoose.Types.ObjectId(data.sequenceId),
        contactId: new mongoose.Types.ObjectId(data.contactId)
      });

      if (existingEnrollment) {
        throw new Error(`Contact ${data.contactId} is already enrolled in sequence ${data.sequenceId}`);
      }

      // Create the enrollment
      const enrollment = new SequenceEnrollment({
        sequenceId: new mongoose.Types.ObjectId(data.sequenceId),
        contactId: new mongoose.Types.ObjectId(data.contactId),
        userId: new mongoose.Types.ObjectId(data.userId),
        tenantId: data.tenantId ? new mongoose.Types.ObjectId(data.tenantId) : undefined,
        status: 'active',
        currentStepNumber: 1,
        startDate: data.startDate || new Date(),
        stepHistory: [],
        customFields: data.customFields || {}
      });

      await enrollment.save();
      logger.info(`Contact ${data.contactId} enrolled in sequence ${data.sequenceId}`);

      // Update sequence stats
      await Sequence.findByIdAndUpdate(data.sequenceId, {
        $inc: {
          'stats.totalEnrolled': 1,
          'stats.activeEnrolled': 1
        }
      });

      // Schedule the first step
      await this.scheduleNextStep(enrollment._id.toString());

      return enrollment;
    } catch (error) {
      logger.error('Error enrolling contact in sequence:', error);
      throw error;
    }
  }

  /**
   * Schedule the next step in a sequence
   */
  async scheduleNextStep(enrollmentId: string): Promise<void> {
    try {
      // Find the enrollment
      const enrollment = await SequenceEnrollment.findById(enrollmentId);

      if (!enrollment) {
        logger.warn(`No enrollment found for ID ${enrollmentId}`);
        return;
      }

      // Check if the enrollment is active
      if (enrollment.status !== 'active') {
        logger.info(`Enrollment ${enrollmentId} is not active (status: ${enrollment.status})`);
        return;
      }

      // Find the current step
      const step = await SequenceStep.findOne({
        sequenceId: enrollment.sequenceId,
        stepNumber: enrollment.currentStepNumber
      });

      if (!step) {
        logger.warn(`No step found for sequence ${enrollment.sequenceId} and step number ${enrollment.currentStepNumber}`);

        // Check if we've reached the end of the sequence
        const maxStep = await SequenceStep.findOne({
          sequenceId: enrollment.sequenceId
        }).sort({ stepNumber: -1 });

        if (!maxStep || maxStep.stepNumber < enrollment.currentStepNumber) {
          // We've reached the end of the sequence
          enrollment.status = 'completed';
          enrollment.completionDate = new Date();
          await enrollment.save();

          // Update sequence stats
          await Sequence.findByIdAndUpdate(enrollment.sequenceId, {
            $inc: {
              'stats.activeEnrolled': -1,
              'stats.completedEnrolled': 1
            }
          });

          logger.info(`Enrollment ${enrollmentId} completed (reached end of sequence)`);
        }

        return;
      }

      // Calculate the execution time based on timing settings
      const executionTime = await this.calculateExecutionTime(step, enrollment);

      // Schedule the step execution
      // In a production environment, you would use a job scheduler like Bull or Agenda
      // For simplicity, we'll use setTimeout here
      const now = new Date();
      const delay = executionTime.getTime() - now.getTime();

      if (delay <= 0) {
        // Execute immediately
        await this.executeStep(enrollment._id.toString(), step._id.toString());
      } else {
        // Schedule for later
        setTimeout(async () => {
          await this.executeStep(enrollment._id.toString(), step._id.toString());
        }, delay);

        logger.info(`Step ${step.stepNumber} for enrollment ${enrollmentId} scheduled for ${executionTime}`);
      }
    } catch (error) {
      logger.error(`Error scheduling next step for enrollment ${enrollmentId}:`, error);
    }
  }

  /**
   * Calculate the execution time for a step
   */
  private async calculateExecutionTime(step: ISequenceStep, enrollment: ISequenceEnrollment): Promise<Date> {
    const now = new Date();
    let executionTime = new Date();

    switch (step.timing.type) {
      case SequenceStepTimingType.IMMEDIATELY:
        executionTime = now;
        break;

      case SequenceStepTimingType.DAYS_AFTER_PREVIOUS:
        // Get the previous step execution time
        if (enrollment.stepHistory.length > 0 && enrollment.currentStepNumber > 1) {
          const previousStepExecution = enrollment.stepHistory.find(
            history => history.stepNumber === enrollment.currentStepNumber - 1
          );

          if (previousStepExecution) {
            executionTime = new Date(previousStepExecution.executedAt);
            executionTime.setDate(executionTime.getDate() + Number(step.timing.value));
          } else {
            // Fallback to days after start
            executionTime = new Date(enrollment.startDate);
            executionTime.setDate(executionTime.getDate() + Number(step.timing.value));
          }
        } else {
          // First step, use start date
          executionTime = new Date(enrollment.startDate);
          executionTime.setDate(executionTime.getDate() + Number(step.timing.value));
        }
        break;

      case SequenceStepTimingType.DAYS_AFTER_START:
        executionTime = new Date(enrollment.startDate);
        executionTime.setDate(executionTime.getDate() + Number(step.timing.value));
        break;

      case SequenceStepTimingType.SPECIFIC_DATE:
        executionTime = new Date(step.timing.value);
        break;

      case SequenceStepTimingType.WEEKDAY:
        // Find the next occurrence of the specified weekday
        const targetDay = Number(step.timing.value); // 0 = Sunday, 1 = Monday, etc.
        const daysToAdd = (targetDay - now.getDay() + 7) % 7;
        executionTime = new Date(now);
        executionTime.setDate(now.getDate() + daysToAdd);
        break;

      // Other timing types would require more complex logic
      // and would typically be handled by a job scheduler
      default:
        executionTime = now;
    }

    // Apply time of day if specified
    if (step.timing.timeOfDay) {
      const [hours, minutes] = step.timing.timeOfDay.split(':').map(Number);
      executionTime.setHours(hours, minutes, 0, 0);
    }

    // Ensure the execution time is in the future
    if (executionTime <= now) {
      executionTime = now;
    }

    return executionTime;
  }

  /**
   * Execute a sequence step
   */
  async executeStep(enrollmentId: string, stepId: string): Promise<void> {
    try {
      // Find the enrollment and step
      const enrollment = await SequenceEnrollment.findById(enrollmentId);
      const step = await SequenceStep.findById(stepId);

      if (!enrollment || !step) {
        logger.warn(`Enrollment ${enrollmentId} or step ${stepId} not found`);
        return;
      }

      // Check if the enrollment is active
      if (enrollment.status !== 'active') {
        logger.info(`Enrollment ${enrollmentId} is not active (status: ${enrollment.status})`);
        return;
      }

      // Get the contact
      const contact = await Contact.findById(enrollment.contactId);

      if (!contact) {
        logger.warn(`Contact ${enrollment.contactId} not found`);
        return;
      }

      // Execute the step based on channel
      let result: any = null;
      let status: 'success' | 'failed' | 'skipped' = 'success';

      switch (step.channel) {
        case SequenceStepChannelType.EMAIL:
          result = await this.executeEmailStep(step, enrollment, contact);
          break;

        case SequenceStepChannelType.LINKEDIN:
        case SequenceStepChannelType.LINKEDIN_INMAIL:
        case SequenceStepChannelType.LINKEDIN_VOICE:
          result = await this.executeLinkedInStep(step, enrollment, contact);
          break;

        case SequenceStepChannelType.CALL:
          result = await this.executeCallStep(step, enrollment, contact);
          break;

        case SequenceStepChannelType.SMS:
          result = await this.executeSmsStep(step, enrollment, contact);
          break;

        case SequenceStepChannelType.TASK:
          result = await this.executeTaskStep(step, enrollment, contact);
          break;

        default:
          logger.warn(`Unsupported channel: ${step.channel}`);
          status = 'skipped';
      }

      // Update the step history
      enrollment.stepHistory.push({
        stepNumber: step.stepNumber,
        executedAt: new Date(),
        status,
        trackingId: result?.trackingId ? new mongoose.Types.ObjectId(result.trackingId) : undefined,
        result: result?.result || undefined
      });

      // Move to the next step
      enrollment.currentStepNumber += 1;
      await enrollment.save();

      // Schedule the next step
      await this.scheduleNextStep(enrollment._id.toString());
    } catch (error) {
      logger.error(`Error executing step ${stepId} for enrollment ${enrollmentId}:`, error);
    }
  }

  /**
   * Execute an email step
   */
  private async executeEmailStep(
    step: ISequenceStep,
    enrollment: ISequenceEnrollment,
    contact: any
  ): Promise<{ trackingId?: string; result?: string }> {
    try {
      // Get the email content and subject
      let content = step.content || '';
      let subject = step.subject || '';

      // If AI dynamic is enabled, generate content
      if (step.aiDynamic) {
        const aiContent = await this.generateDynamicContent(step, enrollment, contact);
        if (aiContent) {
          content = aiContent.content;
          subject = aiContent.subject;
        }
      }

      // Get the user's email configuration
      const emailConfig = await emailService.getEmailConfig(enrollment.userId.toString());

      if (!emailConfig || !emailConfig.isEnabled) {
        throw new Error('Email configuration not found or disabled');
      }

      // Send the email
      const messageId = `seq-${enrollment.sequenceId}-step-${step.stepNumber}-${Date.now()}`;

      // Create tracking record
      const tracking = await emailTrackingService.createTracking({
        userId: enrollment.userId.toString(),
        tenantId: enrollment.tenantId?.toString(),
        contactId: contact._id.toString(),
        sequenceId: enrollment.sequenceId.toString(),
        sequenceStepId: step._id.toString(),
        messageId,
        subject,
        recipient: contact.email,
        sender: emailConfig.fromEmail,
        trackingEnabled: true,
        linkTrackingEnabled: true,
        attachmentTrackingEnabled: true
      });

      // Add tracking pixel to content
      const trackingPixel = emailTrackingService.generateTrackingPixel(tracking.pixelId);
      content = content + trackingPixel;

      // Send the email
      const result = await emailService.sendEmail(enrollment.userId.toString(), {
        to: contact.email,
        subject,
        html: content
      });

      if (!result) {
        throw new Error('Failed to send email');
      }

      // Update sequence stats
      await Sequence.findByIdAndUpdate(enrollment.sequenceId, {
        $inc: {
          'stats.totalEmails': 1
        }
      });

      return {
        trackingId: tracking._id.toString(),
        result: 'email_sent'
      };
    } catch (error) {
      logger.error(`Error executing email step:`, error);
      throw error;
    }
  }

  /**
   * Execute a LinkedIn step
   */
  private async executeLinkedInStep(
    step: ISequenceStep,
    enrollment: ISequenceEnrollment,
    contact: any
  ): Promise<{ result?: string }> {
    // Implementation would depend on LinkedIn integration
    // For now, we'll just create a task for the user
    return this.executeTaskStep(step, enrollment, contact);
  }

  /**
   * Execute a call step
   */
  private async executeCallStep(
    step: ISequenceStep,
    enrollment: ISequenceEnrollment,
    contact: any
  ): Promise<{ result?: string }> {
    // Implementation would depend on telephony integration
    // For now, we'll just create a task for the user
    return this.executeTaskStep(step, enrollment, contact);
  }

  /**
   * Execute an SMS step
   */
  private async executeSmsStep(
    step: ISequenceStep,
    enrollment: ISequenceEnrollment,
    contact: any
  ): Promise<{ result?: string }> {
    // Implementation would depend on SMS integration
    // For now, we'll just create a task for the user
    return this.executeTaskStep(step, enrollment, contact);
  }

  /**
   * Execute a task step
   */
  private async executeTaskStep(
    step: ISequenceStep,
    enrollment: ISequenceEnrollment,
    contact: any
  ): Promise<{ result?: string }> {
    try {
      // Create a task for the user
      await notificationService.createNotification({
        userId: enrollment.userId.toString(),
        type: 'sequence_task',
        title: `Sequence Task: ${step.channel}`,
        message: `Complete ${step.channel} task for ${contact.firstName} ${contact.lastName} in sequence "${enrollment.sequenceId}"`,
        data: {
          sequenceId: enrollment.sequenceId.toString(),
          stepId: step._id.toString(),
          enrollmentId: enrollment._id.toString(),
          contactId: contact._id.toString(),
          channel: step.channel,
          content: step.content,
          subject: step.subject
        },
        isRead: false
      });

      return {
        result: 'task_created'
      };
    } catch (error) {
      logger.error(`Error executing task step:`, error);
      throw error;
    }
  }

  /**
   * Generate dynamic content for a step
   */
  private async generateDynamicContent(
    step: ISequenceStep,
    enrollment: ISequenceEnrollment,
    contact: any
  ): Promise<{ content: string; subject: string } | null> {
    try {
      // Call AI service to generate dynamic content
      const response = await axios.post(`${AI_SERVICE_URL}/sequence/dynamic-content`, {
        stepId: step._id.toString(),
        enrollmentId: enrollment._id.toString(),
        contactId: contact._id.toString(),
        userId: enrollment.userId.toString(),
        tenantId: enrollment.tenantId?.toString(),
        channel: step.channel,
        baseContent: step.content,
        baseSubject: step.subject
      });

      if (response.status === 200 && response.data.success) {
        return {
          content: response.data.content,
          subject: response.data.subject
        };
      } else {
        logger.error(`Failed to generate dynamic content: ${response.data.error || 'Unknown error'}`);
        return null;
      }
    } catch (error) {
      logger.error(`Error generating dynamic content:`, error);
      return null;
    }
  }

  /**
   * Update sequence policy based on performance
   */
  async updateSequencePolicy(sequenceId: string): Promise<void> {
    try {
      // This would be a complex reinforcement learning process
      // For now, we'll just update the policy version
      const sequence = await Sequence.findById(sequenceId);

      if (!sequence) {
        logger.warn(`Sequence ${sequenceId} not found`);
        return;
      }

      // Update policy version
      sequence.policyVersion = `v${Date.now()}`;
      await sequence.save();

      logger.info(`Sequence policy updated for ${sequenceId}: ${sequence.policyVersion}`);
    } catch (error) {
      logger.error(`Error updating sequence policy for ${sequenceId}:`, error);
    }
  }
}

export default new SequenceService();
