import { Express, Request, Response, NextFunction } from 'express';
import { SubscriptionService } from './subscription-service';
import { MongoSubscriptionService } from './subscription-service-mongo';
import { ISubscriptionService } from './@types/subscription-service';
import {
  insertSubscriptionPlanSchema,
  insertTenantSubscriptionSchema,
  insertFeatureSchema,
  insertTenantSchema,
  insertUserTenantSchema
} from '@shared/subscription-schema';

/**
 * Register subscription-related routes
 */
export function registerSubscriptionRoutes(app: Express, authenticateUser: (req: Request, res: Response, next: NextFunction | Function) => void) {
  // Check if MongoDB is enabled
  const useMongoDb = process.env.MONGODB_ENABLED === 'true';

  // Use the appropriate subscription service
  const subscriptionService = useMongoDb
    ? new MongoSubscriptionService()
    : new SubscriptionService();

  // Middleware to check if user is an admin
  const isAdmin = async (req: Request, res: Response, next: Function) => {
    const userId = req.session.userId;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // For now, only user ID 1 (admin) has access to subscription management
    // In a real implementation, this would check user roles
    if (userId !== '1' && userId !== '1') {
      return res.status(403).json({ message: "Forbidden" });
    }

    next();
  };

  // Middleware to check tenant access
  const hasTenantAccess = async (req: Request, res: Response, next: Function) => {
    const userId = req.session.userId;
    const tenantId = req.params.tenantId || req.body.tenantId;

    if (!userId || !tenantId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Get user's tenants
    const userTenants = await subscriptionService.getUserTenants(userId);

    // Check if user has access to the requested tenant
    const hasTenant = userTenants.some(ut => ut.tenantId.toString() === tenantId.toString());

    if (!hasTenant) {
      return res.status(403).json({ message: "Forbidden" });
    }

    next();
  };

  // Subscription Plan routes (admin only)
  app.get("/api/admin/subscription-plans", authenticateUser, isAdmin, async (req, res) => {
    try {
      const plans = await subscriptionService.getPlans();
      return res.status(200).json(plans);
    } catch (error) {
      console.error("Error getting subscription plans:", error);
      return res.status(500).json({ message: "Failed to get subscription plans" });
    }
  });

  app.get("/api/admin/subscription-plans/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const plan = await subscriptionService.getPlan(id);

      if (!plan) {
        return res.status(404).json({ message: "Subscription plan not found" });
      }

      return res.status(200).json(plan);
    } catch (error) {
      console.error("Error getting subscription plan:", error);
      return res.status(500).json({ message: "Failed to get subscription plan" });
    }
  });

  app.post("/api/admin/subscription-plans", authenticateUser, isAdmin, async (req, res) => {
    try {
      const planData = insertSubscriptionPlanSchema.parse(req.body);
      const plan = await subscriptionService.createPlan(planData);
      return res.status(201).json(plan);
    } catch (error) {
      console.error("Error creating subscription plan:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  app.put("/api/admin/subscription-plans/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const planData = insertSubscriptionPlanSchema.partial().parse(req.body);
      const plan = await subscriptionService.updatePlan(id, planData);

      if (!plan) {
        return res.status(404).json({ message: "Subscription plan not found" });
      }

      return res.status(200).json(plan);
    } catch (error) {
      console.error("Error updating subscription plan:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  app.delete("/api/admin/subscription-plans/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await subscriptionService.deletePlan(id);

      if (!success) {
        return res.status(404).json({ message: "Subscription plan not found" });
      }

      return res.status(204).send();
    } catch (error) {
      console.error("Error deleting subscription plan:", error);
      return res.status(500).json({ message: "Failed to delete subscription plan" });
    }
  });

  // Feature routes (admin only)
  app.get("/api/admin/features", authenticateUser, isAdmin, async (req, res) => {
    try {
      const features = await subscriptionService.getFeatures();
      return res.status(200).json(features);
    } catch (error) {
      console.error("Error getting features:", error);
      return res.status(500).json({ message: "Failed to get features" });
    }
  });

  app.get("/api/admin/features/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const feature = await subscriptionService.getFeature(id);

      if (!feature) {
        return res.status(404).json({ message: "Feature not found" });
      }

      return res.status(200).json(feature);
    } catch (error) {
      console.error("Error getting feature:", error);
      return res.status(500).json({ message: "Failed to get feature" });
    }
  });

  app.post("/api/admin/features", authenticateUser, isAdmin, async (req, res) => {
    try {
      const featureData = insertFeatureSchema.parse(req.body);
      const feature = await subscriptionService.registerFeature(featureData);
      return res.status(201).json(feature);
    } catch (error) {
      console.error("Error creating feature:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  app.put("/api/admin/features/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const featureData = insertFeatureSchema.partial().parse(req.body);
      const feature = await subscriptionService.updateFeature(id, featureData);

      if (!feature) {
        return res.status(404).json({ message: "Feature not found" });
      }

      return res.status(200).json(feature);
    } catch (error) {
      console.error("Error updating feature:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  app.delete("/api/admin/features/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await subscriptionService.removeFeature(id);

      if (!success) {
        return res.status(404).json({ message: "Feature not found" });
      }

      return res.status(204).send();
    } catch (error) {
      console.error("Error deleting feature:", error);
      return res.status(500).json({ message: "Failed to delete feature" });
    }
  });

  // Tenant Subscription routes (admin only)
  app.get("/api/admin/tenant-subscriptions", authenticateUser, isAdmin, async (req, res) => {
    try {
      // Get all tenant subscriptions
      const subscriptions = await subscriptionService.getTenantSubscriptions?.() || [];
      return res.status(200).json(subscriptions);
    } catch (error) {
      console.error("Error getting tenant subscriptions:", error);
      return res.status(500).json({ message: "Failed to get tenant subscriptions" });
    }
  });

  app.get("/api/admin/tenant-subscriptions/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      // Get tenant subscription by ID
      const subscription = await subscriptionService.getTenantSubscription(id.toString());

      if (!subscription) {
        return res.status(404).json({ message: "Tenant subscription not found" });
      }

      return res.status(200).json(subscription);
    } catch (error) {
      console.error("Error getting tenant subscription:", error);
      return res.status(500).json({ message: "Failed to get tenant subscription" });
    }
  });

  app.post("/api/admin/tenant-subscriptions", authenticateUser, isAdmin, async (req, res) => {
    try {
      const subscriptionData = insertTenantSubscriptionSchema.parse(req.body);
      const subscription = await subscriptionService.createTenantSubscription(subscriptionData);
      return res.status(201).json(subscription);
    } catch (error) {
      console.error("Error creating tenant subscription:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  app.put("/api/admin/tenant-subscriptions/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const subscriptionData = insertTenantSubscriptionSchema.partial().parse(req.body);
      const subscription = await subscriptionService.updateTenantSubscription(id, subscriptionData);

      if (!subscription) {
        return res.status(404).json({ message: "Tenant subscription not found" });
      }

      return res.status(200).json(subscription);
    } catch (error) {
      console.error("Error updating tenant subscription:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  // Tenant routes (admin only)
  app.get("/api/admin/tenants", authenticateUser, isAdmin, async (req, res) => {
    try {
      const tenants = await subscriptionService.getTenants();
      return res.status(200).json(tenants);
    } catch (error) {
      console.error("Error getting tenants:", error);
      return res.status(500).json({ message: "Failed to get tenants" });
    }
  });

  app.get("/api/admin/tenants/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const tenant = await subscriptionService.getTenant(id);

      if (!tenant) {
        return res.status(404).json({ message: "Tenant not found" });
      }

      return res.status(200).json(tenant);
    } catch (error) {
      console.error("Error getting tenant:", error);
      return res.status(500).json({ message: "Failed to get tenant" });
    }
  });

  app.post("/api/admin/tenants", authenticateUser, isAdmin, async (req, res) => {
    try {
      const tenantData = insertTenantSchema.parse(req.body);
      const tenant = await subscriptionService.createTenant(tenantData);
      return res.status(201).json(tenant);
    } catch (error) {
      console.error("Error creating tenant:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  app.put("/api/admin/tenants/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const tenantData = insertTenantSchema.partial().parse(req.body);
      const tenant = await subscriptionService.updateTenant(id, tenantData);

      if (!tenant) {
        return res.status(404).json({ message: "Tenant not found" });
      }

      return res.status(200).json(tenant);
    } catch (error) {
      console.error("Error updating tenant:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  // User Tenant routes (admin only)
  app.get("/api/admin/user-tenants", authenticateUser, isAdmin, async (req, res) => {
    try {
      const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
      const tenantId = req.query.tenantId ? parseInt(req.query.tenantId as string) : undefined;

      if (userId) {
        const userTenants = await subscriptionService.getUserTenants(userId);
        return res.status(200).json(userTenants);
      } else if (tenantId) {
        const tenantUsers = await subscriptionService.getTenantUsers(tenantId);
        return res.status(200).json(tenantUsers);
      } else {
        return res.status(400).json({ message: "Either userId or tenantId is required" });
      }
    } catch (error) {
      console.error("Error getting user tenants:", error);
      return res.status(500).json({ message: "Failed to get user tenants" });
    }
  });

  app.post("/api/admin/user-tenants", authenticateUser, isAdmin, async (req, res) => {
    try {
      const userTenantData = insertUserTenantSchema.parse(req.body);
      const userTenant = await subscriptionService.createUserTenant(userTenantData);
      return res.status(201).json(userTenant);
    } catch (error) {
      console.error("Error creating user tenant:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  app.put("/api/admin/user-tenants/:id", authenticateUser, isAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userTenantData = insertUserTenantSchema.partial().parse(req.body);
      const userTenant = await subscriptionService.updateUserTenant(id, userTenantData);

      if (!userTenant) {
        return res.status(404).json({ message: "User tenant not found" });
      }

      return res.status(200).json(userTenant);
    } catch (error) {
      console.error("Error updating user tenant:", error);
      return res.status(400).json({ message: error instanceof Error ? error.message : 'An error occurred' });
    }
  });

  // User-facing subscription routes
  app.get("/api/subscription", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;

      // Get user's tenants
      const userTenants = await subscriptionService.getUserTenants(userId || '');

      if (userTenants.length === 0) {
        return res.status(404).json({ message: "No tenants found for user" });
      }

      // Get the first tenant's subscription (in a real app, you might want to handle multiple tenants)
      const tenantId = userTenants[0].tenantId.toString();
      const subscriptionData = await subscriptionService.getTenantSubscriptionWithPlan(tenantId);

      if (!subscriptionData) {
        return res.status(404).json({ message: "No subscription found for tenant" });
      }

      return res.status(200).json(subscriptionData);
    } catch (error) {
      console.error("Error getting subscription:", error);
      return res.status(500).json({ message: "Failed to get subscription" });
    }
  });

  app.get("/api/subscription/features", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;

      // Get user's tenants
      const userTenants = await subscriptionService.getUserTenants(userId || '');

      if (userTenants.length === 0) {
        return res.status(404).json({ message: "No tenants found for user" });
      }

      // Get the first tenant's subscription
      const tenantId = userTenants[0].tenantId.toString();

      // Get all features
      const allFeatures = await subscriptionService.getFeatures();

      // Check which features are enabled for this tenant
      const enabledFeatures = await Promise.all(
        allFeatures.map(async (feature) => {
          const isEnabled = await subscriptionService.checkEntitlement(tenantId, feature.key);
          return {
            ...feature,
            enabled: isEnabled
          };
        })
      );

      return res.status(200).json(enabledFeatures);
    } catch (error) {
      console.error("Error getting features:", error);
      return res.status(500).json({ message: "Failed to get features" });
    }
  });

  app.get("/api/subscription/usage", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;

      // Get user's tenants
      const userTenants = await subscriptionService.getUserTenants(userId || '');

      if (userTenants.length === 0) {
        return res.status(404).json({ message: "No tenants found for user" });
      }

      // Get the first tenant's usage
      const tenantId = userTenants[0].tenantId.toString();
      const usage = await subscriptionService.getTenantUsage(tenantId);

      // Get the subscription to compare against limits
      const subscriptionData = await subscriptionService.getTenantSubscriptionWithPlan(tenantId);

      if (!subscriptionData) {
        return res.status(404).json({ message: "No subscription found for tenant" });
      }

      // Combine usage with limits
      const usageWithLimits = {
        usage: usage?.usage || {},
        featureUsage: usage?.featureUsage || {},
        limits: subscriptionData.subscription.customLimits || subscriptionData.plan.limits,
        period: usage?.period || ''
      };

      return res.status(200).json(usageWithLimits);
    } catch (error) {
      console.error("Error getting usage:", error);
      return res.status(500).json({ message: "Failed to get usage" });
    }
  });

  // Feature entitlement check endpoint
  app.get("/api/subscription/check-feature/:featureKey", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;
      const featureKey = req.params.featureKey;

      // Get user's tenants
      const userTenants = await subscriptionService.getUserTenants(userId || '');

      if (userTenants.length === 0) {
        return res.status(404).json({ message: "No tenants found for user" });
      }

      // Get the first tenant's subscription
      const tenantId = userTenants[0].tenantId.toString();

      // Check if feature is enabled
      const isEnabled = await subscriptionService.checkEntitlement(tenantId, featureKey);

      return res.status(200).json({ enabled: isEnabled });
    } catch (error) {
      console.error("Error checking feature:", error);
      return res.status(500).json({ message: "Failed to check feature" });
    }
  });

  // Resource limit check endpoint
  app.get("/api/subscription/check-limit/:resourceType", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;
      const resourceType = req.params.resourceType;
      const amount = req.query.amount ? parseInt(req.query.amount as string) : 1;

      // Get user's tenants
      const userTenants = await subscriptionService.getUserTenants(userId || '');

      if (userTenants.length === 0) {
        return res.status(404).json({ message: "No tenants found for user" });
      }

      // Get the first tenant's subscription
      const tenantId = userTenants[0].tenantId.toString();

      // Check if within limit
      const isWithinLimit = await subscriptionService.checkResourceLimit(tenantId, resourceType, amount);

      return res.status(200).json({ withinLimit: isWithinLimit });
    } catch (error) {
      console.error("Error checking limit:", error);
      return res.status(500).json({ message: "Failed to check limit" });
    }
  });

  // Subscription cache endpoint for synchronization
  app.get("/api/subscription/cache", authenticateUser, async (req, res) => {
    try {
      const cache = await subscriptionService.getSubscriptionCache();
      return res.status(200).json(cache);
    } catch (error) {
      console.error("Error getting subscription cache:", error);
      return res.status(500).json({ message: "Failed to get subscription cache" });
    }
  });
}
