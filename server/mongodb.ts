import { MongoClient, ServerApiVersion } from 'mongodb';
import mongoose from 'mongoose';
import { logger } from './utils/logger';

// Use the MongoDB URI from the environment variables
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/aizako-crm';

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
let client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  }
});

/**
 * Get MongoDB client
 */
export async function getMongoClient(): Promise<MongoClient> {
  if (!client) {
    client = new MongoClient(uri, {
      serverApi: {
        version: ServerApiVersion.v1,
        strict: true,
        deprecationErrors: true,
      }
    });
    await client.connect();
  }
  return client;
}

let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  clientPromise = client.connect();
}

// Test connection
export async function testConnection() {
  try {
    const connectedClient = await clientPromise;
    await connectedClient.db("admin").command({ ping: 1 });
    console.log("MongoDB connection successful! Connected to the database.");
    return true;
  } catch (error) {
    console.error("MongoDB connection error:", error);
    return false;
  }
}

// Export a module-scoped MongoClient promise
export default clientPromise;