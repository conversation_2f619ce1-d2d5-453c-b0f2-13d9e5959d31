import {
  User, InsertUser,
  Contact, InsertContact,
  Company, InsertCompany,
  Opportunity, InsertOpportunity,
  Activity, InsertActivity,
  Relationship, InsertRelationship,
  AiChat, InsertAiChat,
  Insight, InsertInsight
} from "@shared/schema";

import {
  SubscriptionPlan, InsertSubscriptionPlan,
  TenantSubscription, InsertTenantSubscription,
  Feature, InsertFeature,
  TenantUsage, InsertTenantUsage,
  Tenant, InsertTenant,
  UserTenant, InsertUserTenant
} from "@shared/subscription-schema";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserPreferences(id: number, preferences: Record<string, any>): Promise<User | undefined>;

  // Contact methods
  getContact(id: number): Promise<Contact | undefined>;
  getContacts(limit?: number, offset?: number): Promise<Contact[]>;
  getContactsByCompany(companyId: number): Promise<Contact[]>;
  createContact(contact: InsertContact): Promise<Contact>;
  updateContact(id: number, contact: Partial<InsertContact>): Promise<Contact | undefined>;
  deleteContact(id: number): Promise<boolean>;

  // Company methods
  getCompany(id: number): Promise<Company | undefined>;
  getCompanies(limit?: number, offset?: number): Promise<Company[]>;
  createCompany(company: InsertCompany): Promise<Company>;
  updateCompany(id: number, company: Partial<InsertCompany>): Promise<Company | undefined>;
  deleteCompany(id: number): Promise<boolean>;

  // Opportunity methods
  getOpportunity(id: number): Promise<Opportunity | undefined>;
  getOpportunities(limit?: number, offset?: number): Promise<Opportunity[]>;
  getOpportunitiesByCompany(companyId: number): Promise<Opportunity[]>;
  getOpportunitiesByContact(contactId: number): Promise<Opportunity[]>;
  createOpportunity(opportunity: InsertOpportunity): Promise<Opportunity>;
  updateOpportunity(id: number, opportunity: Partial<InsertOpportunity>): Promise<Opportunity | undefined>;
  deleteOpportunity(id: number): Promise<boolean>;

  // Activity methods
  getActivity(id: number): Promise<Activity | undefined>;
  getActivities(limit?: number, offset?: number): Promise<Activity[]>;
  getActivitiesByContact(contactId: number): Promise<Activity[]>;
  getActivitiesByCompany(companyId: number): Promise<Activity[]>;
  getActivitiesByOpportunity(opportunityId: number): Promise<Activity[]>;
  createActivity(activity: InsertActivity): Promise<Activity>;

  // Relationship methods
  getRelationship(id: number): Promise<Relationship | undefined>;
  getRelationships(): Promise<Relationship[]>;
  getRelationshipsBySource(sourceType: string, sourceId: number): Promise<Relationship[]>;
  getRelationshipsByTarget(targetType: string, targetId: number): Promise<Relationship[]>;
  createRelationship(relationship: InsertRelationship): Promise<Relationship>;

  // AI Chat methods
  getAiChatsByUser(userId: number, limit?: number): Promise<AiChat[]>;
  createAiChat(chat: InsertAiChat): Promise<AiChat>;

  // Insight methods
  getInsights(limit?: number, offset?: number): Promise<Insight[]>;
  getInsightsByTarget(targetType: string, targetId: number): Promise<Insight[]>;
  createInsight(insight: InsertInsight): Promise<Insight>;
  markInsightAsRead(id: number): Promise<Insight | undefined>;

  // Dashboard methods
  getMetrics(): Promise<{
    totalContacts: number,
    totalCompanies: number,
    openOpportunities: number,
    pipelineValue: number,
    aiInsightsGenerated: number
  }>;

  // Subscription Plan methods
  getSubscriptionPlans(): Promise<SubscriptionPlan[]>;
  getSubscriptionPlan(id: number): Promise<SubscriptionPlan | undefined>;
  createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlan>;
  updateSubscriptionPlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan | undefined>;
  deleteSubscriptionPlan(id: number): Promise<boolean>;

  // Feature methods
  getFeatures(): Promise<Feature[]>;
  getFeature(id: number): Promise<Feature | undefined>;
  getFeatureByKey(key: string): Promise<Feature | undefined>;
  createFeature(feature: InsertFeature): Promise<Feature>;
  updateFeature(id: number, feature: Partial<InsertFeature>): Promise<Feature | undefined>;
  deleteFeature(id: number): Promise<boolean>;

  // Tenant Subscription methods
  getTenantSubscriptions(): Promise<TenantSubscription[]>;
  getTenantSubscription(id: number): Promise<TenantSubscription | undefined>;
  getTenantSubscriptionByTenantId(tenantId: string): Promise<TenantSubscription | undefined>;
  createTenantSubscription(subscription: InsertTenantSubscription): Promise<TenantSubscription>;
  updateTenantSubscription(id: number, subscription: Partial<InsertTenantSubscription>): Promise<TenantSubscription | undefined>;

  // Tenant Usage methods
  getTenantUsages(): Promise<TenantUsage[]>;
  getTenantUsage(id: number): Promise<TenantUsage | undefined>;
  getTenantUsageByPeriod(tenantId: string, period: string): Promise<TenantUsage | undefined>;
  createTenantUsage(usage: InsertTenantUsage): Promise<TenantUsage>;
  updateTenantUsage(id: number, usage: Partial<InsertTenantUsage>): Promise<TenantUsage | undefined>;

  // Tenant methods
  getTenants(): Promise<Tenant[]>;
  getTenant(id: number): Promise<Tenant | undefined>;
  getTenantBySlug(slug: string): Promise<Tenant | undefined>;
  createTenant(tenant: InsertTenant): Promise<Tenant>;
  updateTenant(id: number, tenant: Partial<InsertTenant>): Promise<Tenant | undefined>;

  // User Tenant methods
  getUserTenants(userId: number): Promise<UserTenant[]>;
  getTenantUsers(tenantId: number): Promise<UserTenant[]>;
  createUserTenant(userTenant: InsertUserTenant): Promise<UserTenant>;
  updateUserTenant(id: number, userTenant: Partial<InsertUserTenant>): Promise<UserTenant | undefined>;

  // Subscription Event methods
  createSubscriptionEvent(event: { type: string, payload: any }): Promise<{ id: number, type: string, payload: any, timestamp: Date }>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private contacts: Map<number, Contact>;
  private companies: Map<number, Company>;
  private opportunities: Map<number, Opportunity>;
  private activities: Map<number, Activity>;
  private relationships: Map<number, Relationship>;
  private aiChats: Map<number, AiChat>;
  private insights: Map<number, Insight>;

  // Subscription-related maps
  private subscriptionPlans: Map<number, SubscriptionPlan>;
  private features: Map<number, Feature>;
  private tenantSubscriptions: Map<number, TenantSubscription>;
  private tenantUsages: Map<number, TenantUsage>;
  private tenants: Map<number, Tenant>;
  private userTenants: Map<number, UserTenant>;
  private subscriptionEvents: Map<number, { id: number, type: string, payload: any, timestamp: Date }>;

  private userIdCounter: number;
  private contactIdCounter: number;
  private companyIdCounter: number;
  private opportunityIdCounter: number;
  private activityIdCounter: number;
  private relationshipIdCounter: number;
  private aiChatIdCounter: number;
  private insightIdCounter: number;
  private subscriptionPlanIdCounter: number;
  private featureIdCounter: number;
  private tenantSubscriptionIdCounter: number;
  private tenantUsageIdCounter: number;
  private tenantIdCounter: number;
  private userTenantIdCounter: number;
  private subscriptionEventIdCounter: number;

  constructor() {
    this.users = new Map();
    this.contacts = new Map();
    this.companies = new Map();
    this.opportunities = new Map();
    this.activities = new Map();
    this.relationships = new Map();
    this.aiChats = new Map();
    this.insights = new Map();

    // Initialize subscription-related maps
    this.subscriptionPlans = new Map();
    this.features = new Map();
    this.tenantSubscriptions = new Map();
    this.tenantUsages = new Map();
    this.tenants = new Map();
    this.userTenants = new Map();
    this.subscriptionEvents = new Map();

    this.userIdCounter = 1;
    this.contactIdCounter = 1;
    this.companyIdCounter = 1;
    this.opportunityIdCounter = 1;
    this.activityIdCounter = 1;
    this.relationshipIdCounter = 1;
    this.aiChatIdCounter = 1;
    this.insightIdCounter = 1;
    this.subscriptionPlanIdCounter = 1;
    this.featureIdCounter = 1;
    this.tenantSubscriptionIdCounter = 1;
    this.tenantUsageIdCounter = 1;
    this.tenantIdCounter = 1;
    this.userTenantIdCounter = 1;
    this.subscriptionEventIdCounter = 1;

    // Add some initial data
    this.initializeData().catch(err => console.error('Error initializing data:', err));
  }

  private async initializeData() {
    // Add a default admin user
    await this.createUser({
      username: "admin",
      password: "$2b$10$TKpqD/fZrGSHyBx4.p1fYuWzDsY5KLNNZWMcEDQwAK.dS3NYzCPGe", // "password"
      email: "<EMAIL>",
      fullName: "Admin User"
    });

    // Create default subscription plans
    await this.createSubscriptionPlan({
      name: "Free",
      description: "Basic CRM features for individuals",
      status: "active",
      isDefault: true,
      sortOrder: 1,
      price: "0",
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 0,
      limits: {
        users: 1,
        contacts: 50,
        companies: 10,
        opportunities: 5,
        storage: 100, // 100 MB
        apiRequests: 100 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": false,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": false,
        "ai.insights": false,
        "ai.document": false,
        "api.access": false
      },
      moduleSettings: {}
    });

    await this.createSubscriptionPlan({
      name: "Basic",
      description: "Essential CRM features for small teams",
      status: "active",
      isDefault: false,
      sortOrder: 2,
      price: "15",
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 14,
      limits: {
        users: 5,
        contacts: 1000,
        companies: 100,
        opportunities: 50,
        storage: 1000, // 1 GB
        apiRequests: 1000 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": true,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": false,
        "ai.insights": true,
        "ai.document": false,
        "api.access": true
      },
      moduleSettings: {}
    });

    await this.createSubscriptionPlan({
      name: "Professional",
      description: "Advanced CRM features for growing businesses",
      status: "active",
      isDefault: false,
      sortOrder: 3,
      price: "49",
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 14,
      limits: {
        users: 20,
        contacts: 10000,
        companies: 1000,
        opportunities: 500,
        storage: 10000, // 10 GB
        apiRequests: 10000 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": true,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": true,
        "ai.insights": true,
        "ai.document": true,
        "api.access": true
      },
      moduleSettings: {}
    });

    await this.createSubscriptionPlan({
      name: "Enterprise",
      description: "Custom CRM solution for large organizations",
      status: "active",
      isDefault: false,
      sortOrder: 4,
      price: "199",
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 30,
      limits: {
        users: 100,
        contacts: 100000,
        companies: 10000,
        opportunities: 5000,
        storage: 100000, // 100 GB
        apiRequests: 100000 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": true,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": true,
        "ai.insights": true,
        "ai.document": true,
        "api.access": true
      },
      moduleSettings: {}
    });

    // Register core features
    await this.createFeature({
      key: "core.contacts",
      name: "Contact Management",
      description: "Create and manage contacts",
      category: "core",
      module: "core",
      defaultValue: true,
      requiresRestart: false
    });

    await this.createFeature({
      key: "core.companies",
      name: "Company Management",
      description: "Create and manage companies",
      category: "core",
      module: "core",
      defaultValue: true,
      requiresRestart: false
    });

    await this.createFeature({
      key: "core.opportunities",
      name: "Opportunity Management",
      description: "Create and manage sales opportunities",
      category: "core",
      module: "core",
      defaultValue: true,
      requiresRestart: false
    });

    await this.createFeature({
      key: "core.activities",
      name: "Activity Tracking",
      description: "Track activities with contacts and companies",
      category: "core",
      module: "core",
      defaultValue: true,
      requiresRestart: false
    });

    await this.createFeature({
      key: "core.relationships",
      name: "Relationship Mapping",
      description: "Map relationships between contacts and companies",
      category: "core",
      module: "core",
      defaultValue: false,
      requiresRestart: false
    });

    // Register AI features
    await this.createFeature({
      key: "ai.assistant.basic",
      name: "Basic AI Assistant",
      description: "Basic AI assistant for simple queries",
      category: "ai",
      module: "ai",
      defaultValue: true,
      requiresRestart: false
    });

    await this.createFeature({
      key: "ai.assistant.advanced",
      name: "Advanced AI Assistant",
      description: "Advanced AI assistant with CRM data integration",
      category: "ai",
      module: "ai",
      defaultValue: false,
      requiresRestart: false
    });

    await this.createFeature({
      key: "ai.insights",
      name: "AI Insights",
      description: "AI-generated insights from your CRM data",
      category: "ai",
      module: "ai",
      defaultValue: false,
      requiresRestart: false
    });

    await this.createFeature({
      key: "ai.document",
      name: "Document Intelligence",
      description: "Extract information from documents using AI",
      category: "ai",
      module: "ai",
      defaultValue: false,
      requiresRestart: false
    });

    // Register API features
    await this.createFeature({
      key: "api.access",
      name: "API Access",
      description: "Access to the CRM API",
      category: "api",
      module: "api",
      defaultValue: false,
      requiresRestart: false
    });

    // Create default tenant
    const defaultTenant = await this.createTenant({
      name: "Default Organization",
      slug: "default",
      ownerId: 1, // Admin user
      settings: {
        timezone: "UTC",
        locale: "en-US"
      },
      status: "active"
    });

    // Associate admin user with default tenant
    await this.createUserTenant({
      userId: 1, // Admin user
      tenantId: defaultTenant.id,
      role: "owner",
      invitedBy: 1,
      status: "active"
    });

    // Create subscription for default tenant
    await this.createTenantSubscription({
      tenantId: defaultTenant.id.toString(),
      planId: 3, // Professional plan
      status: "active",
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // 14 days from now
    });
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.email === email,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const now = new Date();
    const user: User = {
      ...insertUser,
      id,
      preferences: {},
      createdAt: now
    };
    this.users.set(id, user);
    return user;
  }

  async updateUserPreferences(id: number, preferences: Record<string, any>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;

    // Merge existing preferences with new ones
    const updatedPreferences = {
      ...user.preferences,
      ...preferences
    };

    const updatedUser = {
      ...user,
      preferences: updatedPreferences
    };

    this.users.set(id, updatedUser);
    return updatedUser;
  }

  // Contact methods
  async getContact(id: number): Promise<Contact | undefined> {
    return this.contacts.get(id);
  }

  async getContacts(limit = 100, offset = 0): Promise<Contact[]> {
    return Array.from(this.contacts.values())
      .sort((a, b) => b.id - a.id)
      .slice(offset, offset + limit);
  }

  async getContactsByCompany(companyId: number): Promise<Contact[]> {
    return Array.from(this.contacts.values()).filter(
      (contact) => contact.companyId === companyId
    );
  }

  async createContact(contact: InsertContact): Promise<Contact> {
    const id = this.contactIdCounter++;
    const now = new Date();
    const newContact: Contact = {
      ...contact,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.contacts.set(id, newContact);
    return newContact;
  }

  async updateContact(id: number, contact: Partial<InsertContact>): Promise<Contact | undefined> {
    const existingContact = this.contacts.get(id);
    if (!existingContact) return undefined;

    const updatedContact: Contact = {
      ...existingContact,
      ...contact,
      updatedAt: new Date()
    };
    this.contacts.set(id, updatedContact);
    return updatedContact;
  }

  async deleteContact(id: number): Promise<boolean> {
    return this.contacts.delete(id);
  }

  // Company methods
  async getCompany(id: number): Promise<Company | undefined> {
    return this.companies.get(id);
  }

  async getCompanies(limit = 100, offset = 0): Promise<Company[]> {
    return Array.from(this.companies.values())
      .sort((a, b) => b.id - a.id)
      .slice(offset, offset + limit);
  }

  async createCompany(company: InsertCompany): Promise<Company> {
    const id = this.companyIdCounter++;
    const now = new Date();
    const newCompany: Company = {
      ...company,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.companies.set(id, newCompany);
    return newCompany;
  }

  async updateCompany(id: number, company: Partial<InsertCompany>): Promise<Company | undefined> {
    const existingCompany = this.companies.get(id);
    if (!existingCompany) return undefined;

    const updatedCompany: Company = {
      ...existingCompany,
      ...company,
      updatedAt: new Date()
    };
    this.companies.set(id, updatedCompany);
    return updatedCompany;
  }

  async deleteCompany(id: number): Promise<boolean> {
    return this.companies.delete(id);
  }

  // Opportunity methods
  async getOpportunity(id: number): Promise<Opportunity | undefined> {
    return this.opportunities.get(id);
  }

  async getOpportunities(limit = 100, offset = 0): Promise<Opportunity[]> {
    return Array.from(this.opportunities.values())
      .sort((a, b) => b.id - a.id)
      .slice(offset, offset + limit);
  }

  async getOpportunitiesByCompany(companyId: number): Promise<Opportunity[]> {
    return Array.from(this.opportunities.values()).filter(
      (opportunity) => opportunity.companyId === companyId
    );
  }

  async getOpportunitiesByContact(contactId: number): Promise<Opportunity[]> {
    return Array.from(this.opportunities.values()).filter(
      (opportunity) => opportunity.contactId === contactId
    );
  }

  async createOpportunity(opportunity: InsertOpportunity): Promise<Opportunity> {
    const id = this.opportunityIdCounter++;
    const now = new Date();
    const newOpportunity: Opportunity = {
      ...opportunity,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.opportunities.set(id, newOpportunity);
    return newOpportunity;
  }

  async updateOpportunity(id: number, opportunity: Partial<InsertOpportunity>): Promise<Opportunity | undefined> {
    const existingOpportunity = this.opportunities.get(id);
    if (!existingOpportunity) return undefined;

    const updatedOpportunity: Opportunity = {
      ...existingOpportunity,
      ...opportunity,
      updatedAt: new Date()
    };
    this.opportunities.set(id, updatedOpportunity);
    return updatedOpportunity;
  }

  async deleteOpportunity(id: number): Promise<boolean> {
    return this.opportunities.delete(id);
  }

  // Activity methods
  async getActivity(id: number): Promise<Activity | undefined> {
    return this.activities.get(id);
  }

  async getActivities(limit = 100, offset = 0): Promise<Activity[]> {
    return Array.from(this.activities.values())
      .sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0))
      .slice(offset, offset + limit);
  }

  async getActivitiesByContact(contactId: number): Promise<Activity[]> {
    return Array.from(this.activities.values())
      .filter((activity) => activity.contactId === contactId)
      .sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0));
  }

  async getActivitiesByCompany(companyId: number): Promise<Activity[]> {
    return Array.from(this.activities.values())
      .filter((activity) => activity.companyId === companyId)
      .sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0));
  }

  async getActivitiesByOpportunity(opportunityId: number): Promise<Activity[]> {
    return Array.from(this.activities.values())
      .filter((activity) => activity.opportunityId === opportunityId)
      .sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0));
  }

  async createActivity(activity: InsertActivity): Promise<Activity> {
    const id = this.activityIdCounter++;
    const now = new Date();
    const newActivity: Activity = {
      ...activity,
      id,
      timestamp: now
    };
    this.activities.set(id, newActivity);
    return newActivity;
  }

  // Relationship methods
  async getRelationship(id: number): Promise<Relationship | undefined> {
    return this.relationships.get(id);
  }

  async getRelationships(): Promise<Relationship[]> {
    return Array.from(this.relationships.values());
  }

  async getRelationshipsBySource(sourceType: string, sourceId: number): Promise<Relationship[]> {
    return Array.from(this.relationships.values()).filter(
      (relationship) => relationship.sourceType === sourceType && relationship.sourceId === sourceId
    );
  }

  async getRelationshipsByTarget(targetType: string, targetId: number): Promise<Relationship[]> {
    return Array.from(this.relationships.values()).filter(
      (relationship) => relationship.targetType === targetType && relationship.targetId === targetId
    );
  }

  async createRelationship(relationship: InsertRelationship): Promise<Relationship> {
    const id = this.relationshipIdCounter++;
    const now = new Date();
    const newRelationship: Relationship = {
      ...relationship,
      id,
      createdAt: now
    };
    this.relationships.set(id, newRelationship);
    return newRelationship;
  }

  // AI Chat methods
  async getAiChatsByUser(userId: number, limit = 100): Promise<AiChat[]> {
    return Array.from(this.aiChats.values())
      .filter((chat) => chat.userId === userId)
      .sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0))
      .slice(0, limit);
  }

  async createAiChat(chat: InsertAiChat): Promise<AiChat> {
    const id = this.aiChatIdCounter++;
    const now = new Date();
    const newChat: AiChat = {
      ...chat,
      id,
      timestamp: now
    };
    this.aiChats.set(id, newChat);
    return newChat;
  }

  // Insight methods
  async getInsights(limit = 100, offset = 0): Promise<Insight[]> {
    return Array.from(this.insights.values())
      .sort((a, b) => b.importance - a.importance)
      .slice(offset, offset + limit);
  }

  async getInsightsByTarget(targetType: string, targetId: number): Promise<Insight[]> {
    return Array.from(this.insights.values())
      .filter((insight) => insight.targetType === targetType && insight.targetId === targetId)
      .sort((a, b) => b.importance - a.importance);
  }

  async createInsight(insight: InsertInsight): Promise<Insight> {
    const id = this.insightIdCounter++;
    const now = new Date();
    const newInsight: Insight = {
      ...insight,
      id,
      createdAt: now
    };
    this.insights.set(id, newInsight);
    return newInsight;
  }

  async markInsightAsRead(id: number): Promise<Insight | undefined> {
    const insight = this.insights.get(id);
    if (!insight) return undefined;

    const updatedInsight: Insight = {
      ...insight,
      read: true
    };
    this.insights.set(id, updatedInsight);
    return updatedInsight;
  }

  // Dashboard metrics
  async getMetrics(): Promise<{
    totalContacts: number,
    totalCompanies: number,
    openOpportunities: number,
    pipelineValue: number,
    aiInsightsGenerated: number
  }> {
    const openOpportunities = Array.from(this.opportunities.values())
      .filter(opp => opp.stage !== 'closed_won' && opp.stage !== 'closed_lost');

    const pipelineValue = openOpportunities.reduce((sum, opp) => sum + (opp.value || 0), 0);

    return {
      totalContacts: this.contacts.size,
      totalCompanies: this.companies.size,
      openOpportunities: openOpportunities.length,
      pipelineValue,
      aiInsightsGenerated: this.insights.size
    };
  }

  // Subscription Plan methods
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    return Array.from(this.subscriptionPlans.values())
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  async getSubscriptionPlan(id: number): Promise<SubscriptionPlan | undefined> {
    return this.subscriptionPlans.get(id);
  }

  async createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlan> {
    const id = this.subscriptionPlanIdCounter++;
    const now = new Date();
    const newPlan: SubscriptionPlan = {
      ...plan,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.subscriptionPlans.set(id, newPlan);
    return newPlan;
  }

  async updateSubscriptionPlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan | undefined> {
    const existingPlan = this.subscriptionPlans.get(id);
    if (!existingPlan) return undefined;

    const updatedPlan: SubscriptionPlan = {
      ...existingPlan,
      ...plan,
      updatedAt: new Date()
    };
    this.subscriptionPlans.set(id, updatedPlan);
    return updatedPlan;
  }

  async deleteSubscriptionPlan(id: number): Promise<boolean> {
    return this.subscriptionPlans.delete(id);
  }

  // Feature methods
  async getFeatures(): Promise<Feature[]> {
    return Array.from(this.features.values());
  }

  async getFeature(id: number): Promise<Feature | undefined> {
    return this.features.get(id);
  }

  async getFeatureByKey(key: string): Promise<Feature | undefined> {
    return Array.from(this.features.values()).find(
      (feature) => feature.key === key
    );
  }

  async createFeature(feature: InsertFeature): Promise<Feature> {
    const id = this.featureIdCounter++;
    const now = new Date();
    const newFeature: Feature = {
      ...feature,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.features.set(id, newFeature);
    return newFeature;
  }

  async updateFeature(id: number, feature: Partial<InsertFeature>): Promise<Feature | undefined> {
    const existingFeature = this.features.get(id);
    if (!existingFeature) return undefined;

    const updatedFeature: Feature = {
      ...existingFeature,
      ...feature,
      updatedAt: new Date()
    };
    this.features.set(id, updatedFeature);
    return updatedFeature;
  }

  async deleteFeature(id: number): Promise<boolean> {
    return this.features.delete(id);
  }

  // Tenant Subscription methods
  async getTenantSubscriptions(): Promise<TenantSubscription[]> {
    return Array.from(this.tenantSubscriptions.values());
  }

  async getTenantSubscription(id: number): Promise<TenantSubscription | undefined> {
    return this.tenantSubscriptions.get(id);
  }

  async getTenantSubscriptionByTenantId(tenantId: string): Promise<TenantSubscription | undefined> {
    return Array.from(this.tenantSubscriptions.values()).find(
      (subscription) => subscription.tenantId === tenantId
    );
  }

  async createTenantSubscription(subscription: InsertTenantSubscription): Promise<TenantSubscription> {
    const id = this.tenantSubscriptionIdCounter++;
    const now = new Date();
    const newSubscription: TenantSubscription = {
      ...subscription,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.tenantSubscriptions.set(id, newSubscription);
    return newSubscription;
  }

  async updateTenantSubscription(id: number, subscription: Partial<InsertTenantSubscription>): Promise<TenantSubscription | undefined> {
    const existingSubscription = this.tenantSubscriptions.get(id);
    if (!existingSubscription) return undefined;

    const updatedSubscription: TenantSubscription = {
      ...existingSubscription,
      ...subscription,
      updatedAt: new Date()
    };
    this.tenantSubscriptions.set(id, updatedSubscription);
    return updatedSubscription;
  }

  // Tenant Usage methods
  async getTenantUsages(): Promise<TenantUsage[]> {
    return Array.from(this.tenantUsages.values());
  }

  async getTenantUsage(id: number): Promise<TenantUsage | undefined> {
    return this.tenantUsages.get(id);
  }

  async getTenantUsageByPeriod(tenantId: string, period: string): Promise<TenantUsage | undefined> {
    return Array.from(this.tenantUsages.values()).find(
      (usage) => usage.tenantId === tenantId && usage.period === period
    );
  }

  async createTenantUsage(usage: InsertTenantUsage): Promise<TenantUsage> {
    const id = this.tenantUsageIdCounter++;
    const now = new Date();
    const newUsage: TenantUsage = {
      ...usage,
      id,
      updatedAt: now
    };
    this.tenantUsages.set(id, newUsage);
    return newUsage;
  }

  async updateTenantUsage(id: number, usage: Partial<InsertTenantUsage>): Promise<TenantUsage | undefined> {
    const existingUsage = this.tenantUsages.get(id);
    if (!existingUsage) return undefined;

    const updatedUsage: TenantUsage = {
      ...existingUsage,
      ...usage,
      updatedAt: new Date()
    };
    this.tenantUsages.set(id, updatedUsage);
    return updatedUsage;
  }

  // Tenant methods
  async getTenants(): Promise<Tenant[]> {
    return Array.from(this.tenants.values());
  }

  async getTenant(id: number): Promise<Tenant | undefined> {
    return this.tenants.get(id);
  }

  async getTenantBySlug(slug: string): Promise<Tenant | undefined> {
    return Array.from(this.tenants.values()).find(
      (tenant) => tenant.slug === slug
    );
  }

  async createTenant(tenant: InsertTenant): Promise<Tenant> {
    const id = this.tenantIdCounter++;
    const now = new Date();
    const newTenant: Tenant = {
      ...tenant,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.tenants.set(id, newTenant);
    return newTenant;
  }

  async updateTenant(id: number, tenant: Partial<InsertTenant>): Promise<Tenant | undefined> {
    const existingTenant = this.tenants.get(id);
    if (!existingTenant) return undefined;

    const updatedTenant: Tenant = {
      ...existingTenant,
      ...tenant,
      updatedAt: new Date()
    };
    this.tenants.set(id, updatedTenant);
    return updatedTenant;
  }

  // User Tenant methods
  async getUserTenants(userId: number): Promise<UserTenant[]> {
    return Array.from(this.userTenants.values()).filter(
      (userTenant) => userTenant.userId === userId
    );
  }

  async getTenantUsers(tenantId: number): Promise<UserTenant[]> {
    return Array.from(this.userTenants.values()).filter(
      (userTenant) => userTenant.tenantId === tenantId
    );
  }

  async createUserTenant(userTenant: InsertUserTenant): Promise<UserTenant> {
    const id = this.userTenantIdCounter++;
    const now = new Date();
    const newUserTenant: UserTenant = {
      ...userTenant,
      id,
      createdAt: now
    };
    this.userTenants.set(id, newUserTenant);
    return newUserTenant;
  }

  async updateUserTenant(id: number, userTenant: Partial<InsertUserTenant>): Promise<UserTenant | undefined> {
    const existingUserTenant = this.userTenants.get(id);
    if (!existingUserTenant) return undefined;

    const updatedUserTenant: UserTenant = {
      ...existingUserTenant,
      ...userTenant
    };
    this.userTenants.set(id, updatedUserTenant);
    return updatedUserTenant;
  }

  // Subscription Event methods
  async createSubscriptionEvent(event: { type: string, payload: any }): Promise<{ id: number, type: string, payload: any, timestamp: Date }> {
    const id = this.subscriptionEventIdCounter++;
    const now = new Date();
    const newEvent = {
      id,
      type: event.type,
      payload: event.payload,
      timestamp: now
    };
    this.subscriptionEvents.set(id, newEvent);
    return newEvent;
  }
}

import { createStorage } from './storage/storage-factory';

// Create a storage instance based on configuration
let storageInstance: IStorage = new MemStorage(); // Default to MemStorage initially

// Initialize the storage asynchronously
(async () => {
  try {
    storageInstance = await createStorage();
    console.log('Storage initialized successfully');
  } catch (error) {
    console.error('Failed to initialize storage:', error);
    console.log('Using default MemStorage as fallback');
  }
})();

// Export the storage instance
export const storage = storageInstance;
