/**
 * Query options for repository operations
 */

/**
 * MongoDB operator types for type-safe queries
 */
export type MongoOperator =
  | '$eq' | '$gt' | '$gte' | '$in' | '$lt' | '$lte' | '$ne' | '$nin'
  | '$and' | '$or' | '$not' | '$nor' | '$exists' | '$type' | '$regex'
  | '$options' | '$text' | '$search' | '$where' | '$all' | '$elemMatch'
  | '$size' | '$bitsAllClear' | '$bitsAllSet' | '$bitsAnyClear' | '$bitsAnySet';

/**
 * MongoDB comparison operators
 */
export type MongoComparisonOperator = '$eq' | '$gt' | '$gte' | '$in' | '$lt' | '$lte' | '$ne' | '$nin';

/**
 * MongoDB logical operators
 */
export type MongoLogicalOperator = '$and' | '$or' | '$not' | '$nor';

/**
 * MongoDB element operators
 */
export type MongoElementOperator = '$exists' | '$type';

/**
 * MongoDB evaluation operators
 */
export type MongoEvaluationOperator = '$regex' | '$options' | '$text' | '$search' | '$where';

/**
 * MongoDB array operators
 */
export type MongoArrayOperator = '$all' | '$elemMatch' | '$size';

/**
 * MongoDB bitwise operators
 */
export type MongoBitwiseOperator = '$bitsAllClear' | '$bitsAllSet' | '$bitsAnyClear' | '$bitsAnySet';

/**
 * Type-safe filter for MongoDB queries
 *
 * @template T The document type to filter
 */
export type TypedFilter<T> = {
  [P in keyof T]?:
    | T[P]
    | { [op in MongoComparisonOperator]?: T[P] | (T[P] extends Array<infer U> ? U[] : never) }
    | { [op in MongoElementOperator]?: boolean | string }
    | { [op in MongoEvaluationOperator]?: string | RegExp | boolean }
    | { [op in MongoArrayOperator]?: any }
    | { [op in MongoBitwiseOperator]?: number | number[] };
} & {
  [op in MongoLogicalOperator]?: Array<TypedFilter<T>>;
};

/**
 * Type-safe sort for MongoDB queries
 *
 * @template T The document type to sort
 */
export type TypedSort<T> = {
  [P in keyof T]?: 1 | -1;
};

/**
 * Type-safe projection for MongoDB queries
 *
 * @template T The document type to project
 */
export type TypedProjection<T> = {
  [P in keyof T]?: 0 | 1;
};

/**
 * Type-safe population options for MongoDB queries
 */
export interface PopulateOptions {
  /**
   * Path to populate
   */
  path: string;

  /**
   * Fields to select
   */
  select?: string;

  /**
   * Model to use for population
   */
  model?: string;

  /**
   * Match conditions
   */
  match?: Record<string, any>;

  /**
   * Additional options
   */
  options?: Record<string, any>;

  /**
   * Nested populate
   */
  populate?: PopulateOptions | PopulateOptions[];
}

/**
 * Query options interface
 *
 * This interface defines the options that can be passed to repository methods
 * to control filtering, sorting, pagination, and projection.
 *
 * @template T The document type for the query
 */
export interface QueryOptions<T = any> {
  /**
   * Filter criteria
   *
   * A MongoDB-style filter object that specifies the conditions for selecting documents.
   * Example: { status: 'active', age: { $gt: 18 } }
   */
  filter?: TypedFilter<T>;

  /**
   * Sort criteria
   *
   * A MongoDB-style sort object that specifies the sort order.
   * Example: { createdAt: -1, name: 1 }
   */
  sort?: TypedSort<T>;

  /**
   * Projection
   *
   * A MongoDB-style projection object that specifies which fields to include or exclude.
   * Example: { name: 1, email: 1, _id: 0 }
   */
  projection?: TypedProjection<T>;

  /**
   * Page number (1-based)
   *
   * The page number to return for paginated results.
   * Default: 1
   */
  page?: number;

  /**
   * Page size
   *
   * The number of items per page for paginated results.
   * Default: 20
   */
  limit?: number;

  /**
   * Population options
   *
   * Specifies which related documents to populate.
   * Can be a string array of paths or an array of PopulateOptions.
   * Example: ['company', 'createdBy'] or [{ path: 'company', select: 'name' }]
   */
  populate?: string[] | PopulateOptions[];

  /**
   * Lean option
   *
   * If true, returns plain JavaScript objects instead of Mongoose documents.
   * Default: false
   */
  lean?: boolean;

  /**
   * Collation options
   *
   * Specifies language-specific rules for string comparison.
   */
  collation?: {
    locale: string;
    strength?: number;
    caseLevel?: boolean;
    caseFirst?: string;
    numericOrdering?: boolean;
  };

  /**
   * Skip option
   *
   * Number of documents to skip.
   * Usually calculated from page and limit, but can be specified directly.
   */
  skip?: number;

  /**
   * Timeout option
   *
   * Sets a timeout for the query in milliseconds.
   */
  timeout?: number;

  /**
   * Explain option
   *
   * If true, returns information about the query plan instead of the results.
   */
  explain?: boolean;
}

/**
 * Type guard for checking if a value is a valid page number
 *
 * @param value The value to check
 * @returns True if the value is a valid page number
 */
export function isValidPageNumber(value: any): value is number {
  return typeof value === 'number' && value > 0 && Number.isInteger(value);
}

/**
 * Type guard for checking if a value is a valid limit
 *
 * @param value The value to check
 * @returns True if the value is a valid limit
 */
export function isValidLimit(value: any): value is number {
  return typeof value === 'number' && value > 0 && Number.isInteger(value);
}

/**
 * Type guard for checking if a value is a valid sort direction
 *
 * @param value The value to check
 * @returns True if the value is a valid sort direction
 */
export function isValidSortDirection(value: any): value is 1 | -1 {
  return value === 1 || value === -1;
}

export default QueryOptions;
