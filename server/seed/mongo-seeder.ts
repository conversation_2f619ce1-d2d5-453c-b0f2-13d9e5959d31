import { 
  User, 
  Contact, 
  Company, 
  Opportunity, 
  Activity,
  SubscriptionPlan,
  Feature,
  Tenant,
  TenantSubscription,
  UserTenant
} from '../models/mongoose';
import { connectToMongoDB } from '../mongodb-connection';
import bcrypt from 'bcrypt';
import mongoose from 'mongoose';

/**
 * Seed the MongoDB database with initial data
 */
export async function seedMongoDB() {
  try {
    // Connect to MongoDB
    await connectToMongoDB();
    console.log('Connected to MongoDB for seeding');
    
    // Check if data already exists
    const userCount = await User.countDocuments();
    
    if (userCount > 0) {
      console.log('Database already seeded. Skipping...');
      return;
    }
    
    console.log('Seeding MongoDB database...');
    
    // Create admin user
    const adminPassword = await bcrypt.hash('password', 10);
    const adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: adminPassword,
      fullName: 'Admin User',
      role: 'admin'
    });
    
    console.log('Created admin user');
    
    // Create subscription plans
    const freePlan = await SubscriptionPlan.create({
      name: "Free",
      description: "Basic CRM features for individuals",
      status: "active",
      isDefault: true,
      sortOrder: 1,
      price: 0,
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 0,
      limits: {
        users: 1,
        contacts: 50,
        companies: 10,
        opportunities: 5,
        storage: 100, // 100 MB
        apiRequests: 100 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": false,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": false,
        "ai.insights": false,
        "ai.document": false,
        "api.access": false
      },
      moduleSettings: {}
    });
    
    const basicPlan = await SubscriptionPlan.create({
      name: "Basic",
      description: "Essential CRM features for small teams",
      status: "active",
      isDefault: false,
      sortOrder: 2,
      price: 15,
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 14,
      limits: {
        users: 5,
        contacts: 1000,
        companies: 100,
        opportunities: 50,
        storage: 1000, // 1 GB
        apiRequests: 1000 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": true,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": false,
        "ai.insights": true,
        "ai.document": false,
        "api.access": true
      },
      moduleSettings: {}
    });
    
    const proPlan = await SubscriptionPlan.create({
      name: "Professional",
      description: "Advanced CRM features for growing businesses",
      status: "active",
      isDefault: false,
      sortOrder: 3,
      price: 49,
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 14,
      limits: {
        users: 20,
        contacts: 10000,
        companies: 1000,
        opportunities: 500,
        storage: 10000, // 10 GB
        apiRequests: 10000 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": true,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": true,
        "ai.insights": true,
        "ai.document": true,
        "api.access": true
      },
      moduleSettings: {}
    });
    
    const enterprisePlan = await SubscriptionPlan.create({
      name: "Enterprise",
      description: "Custom CRM solution for large organizations",
      status: "active",
      isDefault: false,
      sortOrder: 4,
      price: 199,
      currency: "USD",
      billingPeriod: "monthly",
      trialDays: 30,
      limits: {
        users: 100,
        contacts: 100000,
        companies: 10000,
        opportunities: 5000,
        storage: 100000, // 100 GB
        apiRequests: 100000 // per day
      },
      features: {
        "core.contacts": true,
        "core.companies": true,
        "core.opportunities": true,
        "core.activities": true,
        "core.relationships": true,
        "ai.assistant.basic": true,
        "ai.assistant.advanced": true,
        "ai.insights": true,
        "ai.document": true,
        "api.access": true
      },
      moduleSettings: {}
    });
    
    console.log('Created subscription plans');
    
    // Register features
    const coreFeatures = [
      {
        key: "core.contacts",
        name: "Contact Management",
        description: "Create and manage contacts",
        category: "core",
        module: "core",
        defaultValue: true,
        requiresRestart: false
      },
      {
        key: "core.companies",
        name: "Company Management",
        description: "Create and manage companies",
        category: "core",
        module: "core",
        defaultValue: true,
        requiresRestart: false
      },
      {
        key: "core.opportunities",
        name: "Opportunity Management",
        description: "Create and manage sales opportunities",
        category: "core",
        module: "core",
        defaultValue: true,
        requiresRestart: false
      },
      {
        key: "core.activities",
        name: "Activity Tracking",
        description: "Track activities with contacts and companies",
        category: "core",
        module: "core",
        defaultValue: true,
        requiresRestart: false
      },
      {
        key: "core.relationships",
        name: "Relationship Mapping",
        description: "Map relationships between contacts and companies",
        category: "core",
        module: "core",
        defaultValue: false,
        requiresRestart: false
      }
    ];
    
    const aiFeatures = [
      {
        key: "ai.assistant.basic",
        name: "Basic AI Assistant",
        description: "Basic AI assistant for simple queries",
        category: "ai",
        module: "ai",
        defaultValue: true,
        requiresRestart: false
      },
      {
        key: "ai.assistant.advanced",
        name: "Advanced AI Assistant",
        description: "Advanced AI assistant with CRM data integration",
        category: "ai",
        module: "ai",
        defaultValue: false,
        requiresRestart: false
      },
      {
        key: "ai.insights",
        name: "AI Insights",
        description: "AI-generated insights from your CRM data",
        category: "ai",
        module: "ai",
        defaultValue: false,
        requiresRestart: false
      },
      {
        key: "ai.document",
        name: "Document Intelligence",
        description: "Extract information from documents using AI",
        category: "ai",
        module: "ai",
        defaultValue: false,
        requiresRestart: false
      }
    ];
    
    const apiFeatures = [
      {
        key: "api.access",
        name: "API Access",
        description: "Access to the CRM API",
        category: "api",
        module: "api",
        defaultValue: false,
        requiresRestart: false
      }
    ];
    
    await Feature.insertMany([...coreFeatures, ...aiFeatures, ...apiFeatures]);
    
    console.log('Registered features');
    
    // Create default tenant
    const defaultTenant = await Tenant.create({
      name: "Default Organization",
      slug: "default",
      ownerId: adminUser._id,
      settings: {
        timezone: "UTC",
        locale: "en-US"
      },
      status: "active"
    });
    
    console.log('Created default tenant');
    
    // Associate admin user with default tenant
    await UserTenant.create({
      userId: adminUser._id,
      tenantId: defaultTenant._id,
      role: "owner",
      invitedBy: adminUser._id,
      status: "active"
    });
    
    console.log('Associated admin user with default tenant');
    
    // Create subscription for default tenant
    const now = new Date();
    const oneYearLater = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
    const twoWeeksLater = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000);
    
    await TenantSubscription.create({
      tenantId: defaultTenant._id.toString(),
      planId: proPlan._id,
      status: "active",
      startDate: now,
      endDate: oneYearLater,
      trialEndsAt: twoWeeksLater
    });
    
    console.log('Created subscription for default tenant');
    
    // Create sample companies
    const companies = await Company.insertMany([
      {
        name: "Acme Corporation",
        domain: "acme.com",
        industry: "Manufacturing",
        size: "201-500",
        location: "New York, NY",
        description: "Leading manufacturer of innovative products",
        website: "https://acme.com",
        status: "customer",
        owner: adminUser._id,
        tags: ["manufacturing", "enterprise"]
      },
      {
        name: "TechNova",
        domain: "technova.io",
        industry: "Technology",
        size: "51-200",
        location: "San Francisco, CA",
        description: "Cutting-edge technology solutions provider",
        website: "https://technova.io",
        status: "prospect",
        owner: adminUser._id,
        tags: ["technology", "startup"]
      },
      {
        name: "Global Finance",
        domain: "globalfinance.com",
        industry: "Financial Services",
        size: "1001-5000",
        location: "London, UK",
        description: "International financial services company",
        website: "https://globalfinance.com",
        status: "lead",
        owner: adminUser._id,
        tags: ["finance", "enterprise"]
      }
    ]);
    
    console.log('Created sample companies');
    
    // Create sample contacts
    const contacts = await Contact.insertMany([
      {
        firstName: "John",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "+****************",
        title: "CEO",
        companyId: companies[0]._id,
        status: "active",
        owner: adminUser._id,
        tags: ["decision-maker", "executive"]
      },
      {
        firstName: "Emily",
        lastName: "Johnson",
        email: "<EMAIL>",
        phone: "+****************",
        title: "CTO",
        companyId: companies[1]._id,
        status: "active",
        owner: adminUser._id,
        tags: ["technical", "decision-maker"]
      },
      {
        firstName: "Michael",
        lastName: "Brown",
        email: "<EMAIL>",
        phone: "+44 20 1234 5678",
        title: "CFO",
        companyId: companies[2]._id,
        status: "lead",
        owner: adminUser._id,
        tags: ["finance", "decision-maker"]
      }
    ]);
    
    console.log('Created sample contacts');
    
    // Create sample opportunities
    const opportunities = await Opportunity.insertMany([
      {
        name: "Acme Enterprise Deal",
        companyId: companies[0]._id,
        contactId: contacts[0]._id,
        value: 50000,
        currency: "USD",
        stage: "proposal",
        probability: 70,
        expectedCloseDate: new Date(now.getFullYear(), now.getMonth() + 1, 15),
        owner: adminUser._id,
        description: "Enterprise software license deal",
        tags: ["enterprise", "high-value"]
      },
      {
        name: "TechNova Integration Project",
        companyId: companies[1]._id,
        contactId: contacts[1]._id,
        value: 25000,
        currency: "USD",
        stage: "needs_analysis",
        probability: 50,
        expectedCloseDate: new Date(now.getFullYear(), now.getMonth() + 2, 10),
        owner: adminUser._id,
        description: "Custom integration project",
        tags: ["services", "technical"]
      },
      {
        name: "Global Finance Expansion",
        companyId: companies[2]._id,
        contactId: contacts[2]._id,
        value: 100000,
        currency: "USD",
        stage: "qualification",
        probability: 30,
        expectedCloseDate: new Date(now.getFullYear(), now.getMonth() + 3, 20),
        owner: adminUser._id,
        description: "Expansion to new markets",
        tags: ["expansion", "high-value"]
      }
    ]);
    
    console.log('Created sample opportunities');
    
    // Create sample activities
    await Activity.insertMany([
      {
        type: "call",
        title: "Initial call with John Smith",
        description: "Discussed their current needs and pain points",
        date: new Date(now.getFullYear(), now.getMonth() - 1, 15),
        duration: 30,
        completed: true,
        contactId: contacts[0]._id,
        companyId: companies[0]._id,
        owner: adminUser._id,
        notes: "John expressed interest in our enterprise solution"
      },
      {
        type: "meeting",
        title: "Product demo with TechNova",
        description: "Demonstrated our platform to their technical team",
        date: new Date(now.getFullYear(), now.getMonth() - 1, 20),
        duration: 60,
        completed: true,
        contactId: contacts[1]._id,
        companyId: companies[1]._id,
        opportunityId: opportunities[1]._id,
        owner: adminUser._id,
        notes: "The team was impressed with our integration capabilities"
      },
      {
        type: "email",
        title: "Follow-up with Michael Brown",
        description: "Sent proposal and pricing information",
        date: new Date(now.getFullYear(), now.getMonth(), 5),
        completed: true,
        contactId: contacts[2]._id,
        companyId: companies[2]._id,
        opportunityId: opportunities[2]._id,
        owner: adminUser._id,
        notes: "Michael requested additional information about our global support"
      },
      {
        type: "task",
        title: "Prepare proposal for Acme",
        description: "Create detailed proposal based on requirements",
        date: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 3),
        duration: 120,
        completed: false,
        companyId: companies[0]._id,
        opportunityId: opportunities[0]._id,
        owner: adminUser._id
      }
    ]);
    
    console.log('Created sample activities');
    
    console.log('MongoDB database seeded successfully');
  } catch (error) {
    console.error('Error seeding MongoDB database:', error);
    throw error;
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedMongoDB()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
