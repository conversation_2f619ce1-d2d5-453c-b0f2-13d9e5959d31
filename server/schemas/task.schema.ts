/**
 * Task Schema
 * 
 * This file contains Zod schemas for validating task data.
 */
import { z } from 'zod';
import { ObjectIdSchema } from './common.schema';

/**
 * Task status enum
 */
export const TaskStatusEnum = z.enum([
  'todo',
  'in_progress',
  'completed',
  'deferred',
  'canceled'
]);

/**
 * Task priority enum
 */
export const TaskPriorityEnum = z.enum([
  'low',
  'medium',
  'high',
  'urgent'
]);

/**
 * Related entity type enum
 */
export const RelatedEntityTypeEnum = z.enum([
  'contact',
  'company',
  'opportunity',
  'activity'
]);

/**
 * Related entity schema
 */
export const RelatedEntitySchema = z.object({
  type: RelatedEntityTypeEnum,
  id: ObjectIdSchema,
});

/**
 * Task schema
 */
export const TaskSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1),
  description: z.string().optional(),
  status: TaskStatusEnum.default('todo'),
  priority: TaskPriorityEnum.default('medium'),
  dueDate: z.date().optional(),
  completedAt: z.date().optional(),
  assignedTo: ObjectIdSchema,
  assignedBy: ObjectIdSchema,
  relatedTo: RelatedEntitySchema.optional(),
  tags: z.array(z.string()).default([]),
  reminderAt: z.date().optional(),
  notes: z.string().optional(),
  tenantId: z.string(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Task creation schema
 */
export const CreateTaskSchema = TaskSchema.omit({
  id: true,
  completedAt: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Task update schema
 */
export const UpdateTaskSchema = TaskSchema.partial().omit({
  id: true,
  tenantId: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Task completion schema
 */
export const CompleteTaskSchema = z.object({
  taskId: z.string(),
  notes: z.string().optional(),
});

/**
 * Task search schema
 */
export const TaskSearchSchema = z.object({
  query: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  sortBy: z.enum(['title', 'status', 'priority', 'dueDate', 'createdAt', 'updatedAt']).default('dueDate'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  status: TaskStatusEnum.optional(),
  priority: TaskPriorityEnum.optional(),
  assignedTo: ObjectIdSchema.optional(),
  dueDateStart: z.date().optional(),
  dueDateEnd: z.date().optional(),
  tags: z.array(z.string()).optional(),
  relatedToType: RelatedEntityTypeEnum.optional(),
  relatedToId: ObjectIdSchema.optional(),
});

export type Task = z.infer<typeof TaskSchema>;
export type CreateTask = z.infer<typeof CreateTaskSchema>;
export type UpdateTask = z.infer<typeof UpdateTaskSchema>;
export type CompleteTask = z.infer<typeof CompleteTaskSchema>;
export type TaskSearch = z.infer<typeof TaskSearchSchema>;
export type RelatedEntity = z.infer<typeof RelatedEntitySchema>;
