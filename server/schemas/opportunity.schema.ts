/**
 * Opportunity Schema
 * 
 * This file contains Zod schemas for validating opportunity data.
 */
import { z } from 'zod';
import { ObjectIdSchema } from './common.schema';

/**
 * Opportunity stage enum
 */
export const OpportunityStageEnum = z.enum([
  'prospecting',
  'qualification',
  'needs_analysis',
  'value_proposition',
  'decision_makers',
  'proposal',
  'negotiation',
  'closed_won',
  'closed_lost'
]);

/**
 * Key objection schema
 */
export const KeyObjectionSchema = z.object({
  id: z.string(),
  text: z.string(),
  class: z.string(),
  response: z.string().optional(),
});

/**
 * Relevant activity schema
 */
export const RelevantActivitySchema = z.object({
  id: z.string(),
  type: z.string(),
  title: z.string(),
  date: z.date(),
  summary: z.string(),
});

/**
 * Relevant document schema
 */
export const RelevantDocumentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  url: z.string().url().optional(),
});

/**
 * Next step schema
 */
export const NextStepSchema = z.object({
  id: z.string(),
  description: z.string(),
  dueDate: z.date().optional(),
  priority: z.enum(['low', 'medium', 'high']),
});

/**
 * Deal brief schema
 */
export const DealBriefSchema = z.object({
  opportunityId: z.string(),
  summary: z.string(),
  keyObjections: z.array(KeyObjectionSchema),
  relevantActivities: z.array(RelevantActivitySchema),
  relevantDocuments: z.array(RelevantDocumentSchema),
  nextSteps: z.array(NextStepSchema),
  aiConfidence: z.number().min(0).max(1),
  generatedAt: z.date(),
});

/**
 * Key indicator schema
 */
export const KeyIndicatorSchema = z.object({
  name: z.string(),
  status: z.enum(['positive', 'neutral', 'negative']),
  description: z.string(),
});

/**
 * Suggested action schema
 */
export const SuggestedActionSchema = z.object({
  description: z.string(),
  priority: z.enum(['low', 'medium', 'high']),
});

/**
 * Stage analysis schema
 */
export const StageAnalysisSchema = z.object({
  opportunityId: z.string(),
  stage: z.string(),
  summary: z.string(),
  completionPercentage: z.number().min(0).max(100),
  keyIndicators: z.array(KeyIndicatorSchema),
  suggestedActions: z.array(SuggestedActionSchema),
  aiConfidence: z.number().min(0).max(1),
  generatedAt: z.date(),
});

/**
 * Stage transition schema
 */
export const StageTransitionSchema = z.object({
  id: z.string(),
  opportunityId: z.string(),
  fromStage: z.string(),
  toStage: z.string(),
  suggestedAt: z.date(),
  appliedAt: z.date().optional(),
  approved: z.boolean().optional(),
  confidence: z.number().min(0).max(1),
  reasons: z.array(z.string()),
  tenantId: z.string(),
});

/**
 * Opportunity schema
 */
export const OpportunitySchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  companyId: ObjectIdSchema.optional(),
  contactId: ObjectIdSchema.optional(),
  value: z.number().min(0),
  currency: z.string().default('USD'),
  stage: OpportunityStageEnum.default('prospecting'),
  probability: z.number().min(0).max(100).default(0),
  expectedCloseDate: z.date().optional(),
  actualCloseDate: z.date().optional(),
  source: z.string().optional(),
  owner: ObjectIdSchema,
  description: z.string().optional(),
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  customFields: z.record(z.any()).default({}),
  tenantId: z.string(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Opportunity creation schema
 */
export const CreateOpportunitySchema = OpportunitySchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Opportunity update schema
 */
export const UpdateOpportunitySchema = OpportunitySchema.partial().omit({
  id: true,
  tenantId: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Opportunity search schema
 */
export const OpportunitySearchSchema = z.object({
  query: z.string().min(1),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  sortBy: z.enum(['name', 'value', 'stage', 'probability', 'expectedCloseDate', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  stage: OpportunityStageEnum.optional(),
  minValue: z.number().min(0).optional(),
  maxValue: z.number().min(0).optional(),
  minProbability: z.number().min(0).max(100).optional(),
  maxProbability: z.number().min(0).max(100).optional(),
  tags: z.array(z.string()).optional(),
  companyId: ObjectIdSchema.optional(),
  contactId: ObjectIdSchema.optional(),
  owner: ObjectIdSchema.optional(),
});

export type Opportunity = z.infer<typeof OpportunitySchema>;
export type CreateOpportunity = z.infer<typeof CreateOpportunitySchema>;
export type UpdateOpportunity = z.infer<typeof UpdateOpportunitySchema>;
export type OpportunitySearch = z.infer<typeof OpportunitySearchSchema>;
export type DealBrief = z.infer<typeof DealBriefSchema>;
export type StageAnalysis = z.infer<typeof StageAnalysisSchema>;
export type StageTransition = z.infer<typeof StageTransitionSchema>;
