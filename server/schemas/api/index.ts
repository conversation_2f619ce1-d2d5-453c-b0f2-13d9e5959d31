/**
 * API schemas for the Aizako CRM project
 */

import { z } from 'zod';

/**
 * Contact schemas
 */
export const createContactRequestSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  title: z.string().optional(),
  status: z.enum(['active', 'inactive', 'lead', 'prospect', 'customer']),
  notes: z.string().optional(),
  source: z.string().optional(),
  companyId: z.string().optional(),
});

export const updateContactRequestSchema = createContactRequestSchema.partial();

/**
 * Company schemas
 */
export const createCompanyRequestSchema = z.object({
  name: z.string().min(1, 'Company name is required'),
  industry: z.string().optional(),
  website: z.string().optional(),
  employees: z.number().optional(),
  status: z.enum(['active', 'inactive', 'lead', 'prospect', 'customer']),
  notes: z.string().optional(),
});

export const updateCompanyRequestSchema = createCompanyRequestSchema.partial();

/**
 * Opportunity schemas
 */
export const createOpportunityRequestSchema = z.object({
  name: z.string().min(1, 'Opportunity name is required'),
  value: z.number().min(0, 'Value must be a positive number'),
  currency: z.string().optional(),
  stage: z.enum(['discovery', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']),
  closeDate: z.string().optional(),
  probability: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
});

export const updateOpportunityRequestSchema = createOpportunityRequestSchema.partial();

/**
 * Activity schemas
 */
export const createActivityRequestSchema = z.object({
  type: z.string().min(1, 'Activity type is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  completed: z.boolean(),
  completedAt: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  opportunityId: z.string().optional(),
  assignedTo: z.string().optional(),
});

export const updateActivityRequestSchema = createActivityRequestSchema.partial();

/**
 * Task schemas
 */
export const createTaskRequestSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  completed: z.boolean(),
  completedAt: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  opportunityId: z.string().optional(),
  assignedTo: z.string().optional(),
});

export const updateTaskRequestSchema = createTaskRequestSchema.partial();

/**
 * Document schemas
 */
export const createDocumentRequestSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  fileUrl: z.string().min(1, 'File URL is required'),
  fileType: z.string().min(1, 'File type is required'),
  fileSize: z.number().min(0, 'File size must be a positive number'),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  opportunityId: z.string().optional(),
});

export const updateDocumentRequestSchema = createDocumentRequestSchema.partial();

/**
 * Proposal schemas
 */
export const createProposalRequestSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  content: z.string().min(1, 'Content is required'),
  sections: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      type: z.string(),
      content: z.string(),
      order: z.number(),
    })
  ),
  value: z.number().min(0, 'Value must be a positive number'),
  currency: z.string().optional(),
  validUntil: z.string().optional(),
  opportunityId: z.string().min(1, 'Opportunity ID is required'),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
});

export const updateProposalRequestSchema = createProposalRequestSchema.partial();
