/**
 * Document Schema
 * 
 * This file contains Zod schemas for validating document data.
 */
import { z } from 'zod';
import { ObjectIdSchema } from './common.schema';
import { RelatedEntityTypeEnum, RelatedEntitySchema } from './task.schema';

/**
 * File type enum
 */
export const FileTypeEnum = z.enum([
  'image',
  'document',
  'spreadsheet',
  'presentation',
  'pdf',
  'audio',
  'video',
  'archive',
  'other'
]);

/**
 * Document schema
 */
export const DocumentSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  fileType: z.string(),
  mimeType: z.string(),
  size: z.number().int().positive(),
  url: z.string().url(),
  path: z.string(),
  isPublic: z.boolean().default(false),
  owner: ObjectIdSchema,
  relatedTo: RelatedEntitySchema.optional(),
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
  tenantId: z.string(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Document creation schema
 */
export const CreateDocumentSchema = DocumentSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Document update schema
 */
export const UpdateDocumentSchema = DocumentSchema.partial().omit({
  id: true,
  tenantId: true,
  createdAt: true,
  updatedAt: true,
  fileType: true,
  mimeType: true,
  size: true,
  url: true,
  path: true,
});

/**
 * Document upload schema
 */
export const DocumentUploadSchema = z.object({
  file: z.any(),
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  isPublic: z.boolean().default(false),
  relatedTo: RelatedEntitySchema.optional(),
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
});

/**
 * Document search schema
 */
export const DocumentSearchSchema = z.object({
  query: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  sortBy: z.enum(['name', 'fileType', 'size', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  fileType: z.string().optional(),
  owner: ObjectIdSchema.optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  relatedToType: RelatedEntityTypeEnum.optional(),
  relatedToId: ObjectIdSchema.optional(),
});

/**
 * Document share schema
 */
export const DocumentShareSchema = z.object({
  documentId: z.string(),
  isPublic: z.boolean(),
  expiresAt: z.date().optional(),
});

export type Document = z.infer<typeof DocumentSchema>;
export type CreateDocument = z.infer<typeof CreateDocumentSchema>;
export type UpdateDocument = z.infer<typeof UpdateDocumentSchema>;
export type DocumentUpload = z.infer<typeof DocumentUploadSchema>;
export type DocumentSearch = z.infer<typeof DocumentSearchSchema>;
export type DocumentShare = z.infer<typeof DocumentShareSchema>;
