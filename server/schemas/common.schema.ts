/**
 * Common Schemas
 * 
 * This file contains common Zod schemas used across the application.
 */
import { z } from 'zod';
import mongoose from 'mongoose';

/**
 * MongoDB ObjectId schema
 * 
 * Validates that a value is a valid MongoDB ObjectId
 */
export const ObjectIdSchema = z.custom<mongoose.Types.ObjectId | string>(
  (val) => {
    if (val instanceof mongoose.Types.ObjectId) return true;
    if (typeof val !== 'string') return false;
    return /^[0-9a-fA-F]{24}$/.test(val);
  },
  {
    message: 'Invalid ObjectId format',
  }
);

/**
 * Pagination schema
 * 
 * Used for paginated API requests
 */
export const PaginationSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
});

/**
 * Sort schema
 * 
 * Used for sorting API requests
 */
export const SortSchema = z.object({
  sortBy: z.string(),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * Date range schema
 * 
 * Used for filtering by date range
 */
export const DateRangeSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
}).refine(
  (data) => data.startDate <= data.endDate,
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
);

/**
 * Tenant ID schema
 * 
 * Used for validating tenant IDs
 */
export const TenantIdSchema = z.string().min(1);

/**
 * User ID schema
 * 
 * Used for validating user IDs
 */
export const UserIdSchema = z.string().min(1);

/**
 * Email schema
 * 
 * Used for validating email addresses
 */
export const EmailSchema = z.string().email();

/**
 * Phone schema
 * 
 * Used for validating phone numbers
 */
export const PhoneSchema = z.string().regex(
  /^(\+\d{1,3})?[-.\s]?\(?\d{1,4}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,9}$/,
  'Invalid phone number format'
);

/**
 * URL schema
 * 
 * Used for validating URLs
 */
export const UrlSchema = z.string().url();

/**
 * Tags schema
 * 
 * Used for validating tags
 */
export const TagsSchema = z.array(z.string().min(1)).default([]);

/**
 * Custom fields schema
 * 
 * Used for validating custom fields
 */
export const CustomFieldsSchema = z.record(z.any()).default({});

/**
 * ID parameter schema
 * 
 * Used for validating ID parameters in API routes
 */
export const IdParamSchema = z.object({
  id: z.string().min(1),
});

/**
 * Tenant ID parameter schema
 * 
 * Used for validating tenant ID parameters in API routes
 */
export const TenantIdParamSchema = z.object({
  tenantId: TenantIdSchema,
});

/**
 * Success response schema
 * 
 * Used for standardizing API success responses
 */
export const SuccessResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().optional(),
  data: z.any().optional(),
});

/**
 * Error response schema
 * 
 * Used for standardizing API error responses
 */
export const ErrorResponseSchema = z.object({
  success: z.literal(false),
  message: z.string(),
  errors: z.array(
    z.object({
      field: z.string().optional(),
      message: z.string(),
    })
  ).optional(),
});

/**
 * API response schema
 * 
 * Used for standardizing all API responses
 */
export const ApiResponseSchema = z.union([
  SuccessResponseSchema,
  ErrorResponseSchema,
]);

export type ObjectId = z.infer<typeof ObjectIdSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;
export type Sort = z.infer<typeof SortSchema>;
export type DateRange = z.infer<typeof DateRangeSchema>;
export type IdParam = z.infer<typeof IdParamSchema>;
export type TenantIdParam = z.infer<typeof TenantIdParamSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
export type ApiResponse = z.infer<typeof ApiResponseSchema>;
