/**
 * User Schema
 * 
 * This file contains Zod schemas for validating user data.
 */
import { z } from 'zod';

/**
 * User role enum
 */
export const UserRoleEnum = z.enum([
  'user',
  'admin',
  'superadmin'
]);

/**
 * OAuth integration schema
 */
export const OAuthIntegrationSchema = z.object({
  accessToken: z.string(),
  refreshToken: z.string().optional(),
  email: z.string().email().optional(),
  expiresAt: z.date().optional(),
  personalUrl: z.string().optional(),
  organizationUri: z.string().optional(),
});

/**
 * Calendly integration schema
 */
export const CalendlyIntegrationSchema = z.object({
  apiKey: z.string(),
  personalUrl: z.string().optional(),
  organizationUri: z.string().optional(),
});

/**
 * Telephony provider schema
 */
export const TelephonyProviderSchema = z.object({
  accountSid: z.string().optional(),
  authToken: z.string().optional(),
  authId: z.string().optional(),
  apiKey: z.string().optional(),
  apiSecret: z.string().optional(),
  phoneNumber: z.string().optional(),
});

/**
 * Social media profile schema
 */
export const SocialMediaProfileSchema = z.object({
  username: z.string().optional(),
  profileId: z.string().optional(),
  pageId: z.string().optional(),
});

/**
 * User integrations schema
 */
export const UserIntegrationsSchema = z.object({
  oauth: z.object({
    google: OAuthIntegrationSchema.optional(),
    microsoft: OAuthIntegrationSchema.optional(),
    calendly: OAuthIntegrationSchema.optional(),
    linkedin: OAuthIntegrationSchema.optional(),
    twitter: OAuthIntegrationSchema.optional(),
    facebook: OAuthIntegrationSchema.optional(),
  }).optional(),
  calendly: CalendlyIntegrationSchema.optional(),
  telephony: z.object({
    twilio: TelephonyProviderSchema.optional(),
    vonage: TelephonyProviderSchema.optional(),
    plivo: TelephonyProviderSchema.optional(),
  }).optional(),
  socialMedia: z.object({
    linkedin: SocialMediaProfileSchema.optional(),
    twitter: SocialMediaProfileSchema.optional(),
    facebook: SocialMediaProfileSchema.optional(),
    instagram: SocialMediaProfileSchema.optional(),
  }).optional(),
}).optional();

/**
 * User preferences schema
 */
export const UserPreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.string().default('en'),
  timezone: z.string().default('UTC'),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    inApp: z.boolean().default(true),
  }).optional(),
  dashboard: z.object({
    layout: z.string().optional(),
    widgets: z.array(z.string()).optional(),
  }).optional(),
  emailSignature: z.string().optional(),
}).optional();

/**
 * User schema
 */
export const UserSchema = z.object({
  id: z.string().optional(),
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(8).max(100),
  fullName: z.string().min(1),
  avatar: z.string().optional(),
  role: UserRoleEnum.default('user'),
  preferences: UserPreferencesSchema.default({}),
  integrations: UserIntegrationsSchema,
  lastLogin: z.date().optional(),
  tenantId: z.string(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * User creation schema
 */
export const CreateUserSchema = UserSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * User update schema
 */
export const UpdateUserSchema = UserSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  password: true,
});

/**
 * User password update schema
 */
export const UpdateUserPasswordSchema = z.object({
  currentPassword: z.string().min(8).max(100),
  newPassword: z.string().min(8).max(100),
  confirmPassword: z.string().min(8).max(100),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

/**
 * User login schema
 */
export const UserLoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(100),
  rememberMe: z.boolean().default(false),
});

/**
 * User search schema
 */
export const UserSearchSchema = z.object({
  query: z.string().min(1),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  sortBy: z.enum(['username', 'email', 'fullName', 'role', 'createdAt', 'lastLogin']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  role: UserRoleEnum.optional(),
});

export type User = z.infer<typeof UserSchema>;
export type CreateUser = z.infer<typeof CreateUserSchema>;
export type UpdateUser = z.infer<typeof UpdateUserSchema>;
export type UpdateUserPassword = z.infer<typeof UpdateUserPasswordSchema>;
export type UserLogin = z.infer<typeof UserLoginSchema>;
export type UserSearch = z.infer<typeof UserSearchSchema>;
export type UserIntegrations = z.infer<typeof UserIntegrationsSchema>;
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
