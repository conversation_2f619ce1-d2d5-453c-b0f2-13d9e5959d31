/**
 * Contact Schema
 * 
 * This file contains Zod schemas for validating contact data.
 */
import { z } from 'zod';
import { ObjectIdSchema } from './common.schema';

/**
 * Interaction schema for Smart Interaction Timeline
 */
export const InteractionSchema = z.object({
  id: z.string(),
  type: z.enum(['email', 'call', 'meeting', 'chat', 'social', 'note', 'task', 'other']),
  source: z.string(),
  sourceId: z.string().optional(),
  timestamp: z.date(),
  summary: z.string(),
  sentiment: z.enum(['positive', 'neutral', 'negative']).optional(),
  direction: z.enum(['inbound', 'outbound']).optional(),
  participants: z.array(
    z.object({
      id: z.string().optional(),
      email: z.string().email().optional(),
      name: z.string().optional(),
      role: z.string().optional(),
    })
  ).optional(),
  content: z.object({
    text: z.string().optional(),
    html: z.string().optional(),
    attachments: z.array(
      z.object({
        name: z.string(),
        type: z.string(),
        url: z.string().url().optional(),
      })
    ).optional(),
  }).optional(),
  metadata: z.record(z.any()).optional(),
  nextAction: z.object({
    type: z.enum(['email', 'call', 'meeting', 'task', 'other']),
    description: z.string(),
    dueDate: z.date().optional(),
    priority: z.enum(['low', 'medium', 'high']).optional(),
  }).optional(),
  aiGenerated: z.boolean(),
  aiConfidence: z.number().min(0).max(1).optional(),
});

/**
 * Contact persona schema
 */
export const ContactPersonaSchema = z.object({
  summary: z.string().optional(),
  communicationPreferences: z.object({
    preferredChannel: z.enum(['email', 'phone', 'in-person', 'video']).optional(),
    bestTimeToContact: z.string().optional(),
    responseTime: z.enum(['fast', 'medium', 'slow']).optional(),
  }).optional(),
  interests: z.array(z.string()).optional(),
  painPoints: z.array(z.string()).optional(),
  decisionFactors: z.array(z.string()).optional(),
  aiConfidence: z.number().min(0).max(1).optional(),
  lastUpdated: z.date().optional(),
});

/**
 * Contact score schema
 */
export const ContactScoreSchema = z.object({
  current: z.number().min(0).max(100),
  previous: z.number().min(0).max(100).optional(),
  change: z.number().optional(),
  lastUpdated: z.date(),
  factors: z.object({
    interactions: z.number().min(0).max(100).default(0),
    sentiment: z.number().min(0).max(100).default(0),
    opportunities: z.number().min(0).max(100).default(0),
    engagement: z.number().min(0).max(100).default(0),
    recency: z.number().min(0).max(100).default(0),
    similarContacts: z.number().min(0).max(100).default(0),
  }),
  conversionProbability: z.number().min(0).max(100).default(0),
  history: z.array(
    z.object({
      value: z.number().min(0).max(100),
      timestamp: z.date(),
      factors: z.record(z.number()).optional(),
    })
  ).optional(),
});

/**
 * Contact schema
 */
export const ContactSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  title: z.string().optional(),
  companyId: ObjectIdSchema.optional(),
  status: z.enum(['active', 'inactive', 'lead', 'customer', 'prospect']).default('active'),
  source: z.string().optional(),
  owner: ObjectIdSchema,
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  lastContactedAt: z.date().optional(),
  customFields: z.record(z.any()).default({}),
  aiEnrichment: z.record(z.any()).optional(),
  createdBy: ObjectIdSchema.optional(),
  interactions: z.array(InteractionSchema).optional(),
  persona: ContactPersonaSchema.optional(),
  score: ContactScoreSchema.optional(),
  tenantId: z.string(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Contact creation schema
 */
export const CreateContactSchema = ContactSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Contact update schema
 */
export const UpdateContactSchema = ContactSchema.partial().omit({
  id: true,
  tenantId: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Contact search schema
 */
export const ContactSearchSchema = z.object({
  query: z.string().min(1),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  sortBy: z.enum(['firstName', 'lastName', 'email', 'createdAt', 'updatedAt', 'lastContactedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  status: z.enum(['active', 'inactive', 'lead', 'customer', 'prospect']).optional(),
  tags: z.array(z.string()).optional(),
  companyId: ObjectIdSchema.optional(),
  owner: ObjectIdSchema.optional(),
});

export type Contact = z.infer<typeof ContactSchema>;
export type CreateContact = z.infer<typeof CreateContactSchema>;
export type UpdateContact = z.infer<typeof UpdateContactSchema>;
export type ContactSearch = z.infer<typeof ContactSearchSchema>;
export type Interaction = z.infer<typeof InteractionSchema>;
export type ContactPersona = z.infer<typeof ContactPersonaSchema>;
export type ContactScore = z.infer<typeof ContactScoreSchema>;
