/**
 * Company Schema
 * 
 * This file contains Zod schemas for validating company data.
 */
import { z } from 'zod';
import { ObjectIdSchema } from './common.schema';

/**
 * Company size enum
 */
export const CompanySizeEnum = z.enum([
  '1-10',
  '11-50',
  '51-200',
  '201-500',
  '501-1000',
  '1001-5000',
  '5001-10000',
  '10001+'
]);

/**
 * Company status enum
 */
export const CompanyStatusEnum = z.enum([
  'active',
  'inactive',
  'lead',
  'customer',
  'prospect'
]);

/**
 * Company schema
 */
export const CompanySchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  domain: z.string().optional(),
  industry: z.string().optional(),
  size: CompanySizeEnum.optional(),
  location: z.string().optional(),
  description: z.string().optional(),
  website: z.string().url().optional(),
  logo: z.string().optional(),
  status: CompanyStatusEnum.default('active'),
  owner: ObjectIdSchema,
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  customFields: z.record(z.any()).default({}),
  employees: z.number().int().positive().optional(),
  aiEnrichment: z.record(z.any()).optional(),
  createdBy: ObjectIdSchema.optional(),
  tenantId: z.string(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * Company creation schema
 */
export const CreateCompanySchema = CompanySchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Company update schema
 */
export const UpdateCompanySchema = CompanySchema.partial().omit({
  id: true,
  tenantId: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Company search schema
 */
export const CompanySearchSchema = z.object({
  query: z.string().min(1),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  sortBy: z.enum(['name', 'industry', 'size', 'status', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  status: CompanyStatusEnum.optional(),
  industry: z.string().optional(),
  size: CompanySizeEnum.optional(),
  tags: z.array(z.string()).optional(),
  owner: ObjectIdSchema.optional(),
});

/**
 * Company enrichment schema
 */
export const CompanyEnrichmentSchema = z.object({
  companyId: z.string(),
  tenantId: z.string(),
  enrichmentSource: z.string(),
  enrichmentDate: z.date(),
  confidence: z.number().min(0).max(1),
  data: z.object({
    industry: z.string().optional(),
    size: z.string().optional(),
    location: z.string().optional(),
    description: z.string().optional(),
    website: z.string().url().optional(),
    logo: z.string().optional(),
    employees: z.number().int().positive().optional(),
    revenue: z.number().optional(),
    founded: z.number().int().optional(),
    socialProfiles: z.record(z.string()).optional(),
    technologies: z.array(z.string()).optional(),
    competitors: z.array(z.string()).optional(),
    news: z.array(
      z.object({
        title: z.string(),
        url: z.string().url(),
        date: z.date(),
        source: z.string(),
      })
    ).optional(),
  }),
});

export type Company = z.infer<typeof CompanySchema>;
export type CreateCompany = z.infer<typeof CreateCompanySchema>;
export type UpdateCompany = z.infer<typeof UpdateCompanySchema>;
export type CompanySearch = z.infer<typeof CompanySearchSchema>;
export type CompanyEnrichment = z.infer<typeof CompanyEnrichmentSchema>;
