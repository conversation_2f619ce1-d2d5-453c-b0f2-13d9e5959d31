import { storage } from './storage';
import {
  SubscriptionPlan,
  TenantSubscription,
  Feature,
  TenantUsage,
  InsertSubscriptionPlan,
  InsertTenantSubscription,
  InsertFeature,
  SubscriptionEventType
} from '@shared/subscription-schema';

/**
 * Service for managing subscriptions, plans, features, and usage
 */
export class SubscriptionService {
  /**
   * Get all subscription plans
   */
  async getPlans(): Promise<SubscriptionPlan[]> {
    return await storage.getSubscriptionPlans();
  }

  /**
   * Get a specific subscription plan by ID
   */
  async getPlan(id: number): Promise<SubscriptionPlan | undefined> {
    return await storage.getSubscriptionPlan(id);
  }

  /**
   * Create a new subscription plan
   */
  async createPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlan> {
    const newPlan = await storage.createSubscriptionPlan(plan);

    // Publish event
    await this.publishEvent(SubscriptionEventType.PLAN_CREATED, newPlan);

    return newPlan;
  }

  /**
   * Update an existing subscription plan
   */
  async updatePlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan | undefined> {
    const updatedPlan = await storage.updateSubscriptionPlan(id, plan);

    if (updatedPlan) {
      // Publish event
      await this.publishEvent(SubscriptionEventType.PLAN_UPDATED, updatedPlan);
    }

    return updatedPlan;
  }

  /**
   * Delete a subscription plan
   */
  async deletePlan(id: number): Promise<boolean> {
    const success = await storage.deleteSubscriptionPlan(id);

    if (success) {
      // Publish event
      await this.publishEvent(SubscriptionEventType.PLAN_DELETED, { id });
    }

    return success;
  }

  /**
   * Get all registered features
   */
  async getFeatures(): Promise<Feature[]> {
    return await storage.getFeatures();
  }

  /**
   * Get a specific feature by ID
   */
  async getFeature(id: number): Promise<Feature | undefined> {
    return await storage.getFeature(id);
  }

  /**
   * Get a specific feature by key
   */
  async getFeatureByKey(key: string): Promise<Feature | undefined> {
    return await storage.getFeatureByKey(key);
  }

  /**
   * Register a new feature
   */
  async registerFeature(feature: InsertFeature): Promise<Feature> {
    const newFeature = await storage.createFeature(feature);

    // Publish event
    await this.publishEvent(SubscriptionEventType.FEATURE_REGISTERED, newFeature);

    return newFeature;
  }

  /**
   * Update an existing feature
   */
  async updateFeature(id: number, feature: Partial<InsertFeature>): Promise<Feature | undefined> {
    const updatedFeature = await storage.updateFeature(id, feature);

    if (updatedFeature) {
      // Publish event
      await this.publishEvent(SubscriptionEventType.FEATURE_UPDATED, updatedFeature);
    }

    return updatedFeature;
  }

  /**
   * Remove a feature
   */
  async removeFeature(id: number): Promise<boolean> {
    const success = await storage.deleteFeature(id);

    if (success) {
      // Publish event
      await this.publishEvent(SubscriptionEventType.FEATURE_REMOVED, { id });
    }

    return success;
  }

  /**
   * Get a tenant's subscription
   */
  async getTenantSubscription(tenantId: string): Promise<TenantSubscription | undefined> {
    return await storage.getTenantSubscriptionByTenantId(tenantId);
  }

  /**
   * Get a tenant's subscription with plan details
   */
  async getTenantSubscriptionWithPlan(tenantId: string): Promise<{ subscription: TenantSubscription, plan: SubscriptionPlan } | undefined> {
    const subscription = await storage.getTenantSubscriptionByTenantId(tenantId);

    if (!subscription) {
      return undefined;
    }

    const plan = subscription.planId ? await storage.getSubscriptionPlan(subscription.planId) : undefined;

    if (!plan) {
      return undefined;
    }

    return { subscription, plan };
  }

  /**
   * Create a new tenant subscription
   */
  async createTenantSubscription(subscription: InsertTenantSubscription): Promise<TenantSubscription> {
    const newSubscription = await storage.createTenantSubscription(subscription);

    // Publish event
    await this.publishEvent(SubscriptionEventType.TENANT_SUBSCRIPTION_CREATED, newSubscription);

    return newSubscription;
  }

  /**
   * Update an existing tenant subscription
   */
  async updateTenantSubscription(id: number, subscription: Partial<InsertTenantSubscription>): Promise<TenantSubscription | undefined> {
    const updatedSubscription = await storage.updateTenantSubscription(id, subscription);

    if (updatedSubscription) {
      // Publish event
      await this.publishEvent(SubscriptionEventType.TENANT_SUBSCRIPTION_UPDATED, updatedSubscription);
    }

    return updatedSubscription;
  }

  /**
   * Cancel a tenant subscription
   */
  async cancelTenantSubscription(id: number): Promise<TenantSubscription | undefined> {
    const subscription = await storage.getTenantSubscription(id);

    if (!subscription) {
      return undefined;
    }

    const updatedSubscription = await storage.updateTenantSubscription(id, {
      status: 'canceled'
      // Add canceledAt to the database schema if needed
    });

    if (updatedSubscription) {
      // Publish event
      await this.publishEvent(SubscriptionEventType.TENANT_SUBSCRIPTION_CANCELED, updatedSubscription);
    }

    return updatedSubscription;
  }

  /**
   * Record resource usage for a tenant
   */
  async recordUsage(tenantId: string, resourceType: string, amount: number): Promise<void> {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    // Get current usage for this period
    let usage = await storage.getTenantUsageByPeriod(tenantId, period);

    if (!usage) {
      // Create new usage record if none exists
      usage = await storage.createTenantUsage({
        tenantId,
        period,
        usage: { [resourceType]: amount },
        featureUsage: {}
      });
    } else {
      // Update existing usage record
      const currentUsage = usage.usage as Record<string, number>;
      const updatedUsage = {
        ...currentUsage,
        [resourceType]: (currentUsage[resourceType] || 0) + amount
      };

      usage = await storage.updateTenantUsage(usage.id, {
        usage: updatedUsage
      });
    }
  }

  /**
   * Record feature usage for a tenant
   */
  async recordFeatureUsage(tenantId: string, featureKey: string, amount: number): Promise<void> {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    // Get current usage for this period
    let usage = await storage.getTenantUsageByPeriod(tenantId, period);

    if (!usage) {
      // Create new usage record if none exists
      usage = await storage.createTenantUsage({
        tenantId,
        period,
        usage: {},
        featureUsage: { [featureKey]: amount }
      });
    } else {
      // Update existing usage record
      const currentFeatureUsage = usage.featureUsage as Record<string, number>;
      const updatedFeatureUsage = {
        ...currentFeatureUsage,
        [featureKey]: (currentFeatureUsage[featureKey] || 0) + amount
      };

      usage = await storage.updateTenantUsage(usage.id, {
        featureUsage: updatedFeatureUsage
      });
    }
  }

  /**
   * Get tenant usage for a specific period
   */
  async getTenantUsage(tenantId: string, period?: string): Promise<TenantUsage | undefined> {
    if (period) {
      return await storage.getTenantUsageByPeriod(tenantId, period);
    } else {
      const currentDate = new Date();
      const currentPeriod = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
      return await storage.getTenantUsageByPeriod(tenantId, currentPeriod);
    }
  }

  /**
   * Check if a tenant is entitled to use a feature
   */
  async checkEntitlement(tenantId: string, featureKey: string): Promise<boolean> {
    const subscriptionData = await this.getTenantSubscriptionWithPlan(tenantId);

    if (!subscriptionData) {
      return false;
    }

    const { subscription, plan } = subscriptionData;

    // Check if subscription is active
    if (subscription.status !== 'active' && subscription.status !== 'trialing') {
      return false;
    }

    // Check if subscription is expired
    if (subscription.endDate < new Date()) {
      return false;
    }

    // Check custom features first
    if (subscription.customFeatures) {
      const customFeatures = subscription.customFeatures as Record<string, boolean>;
      if (featureKey in customFeatures) {
        return customFeatures[featureKey];
      }
    }

    // Then check plan features
    const features = plan.features as Record<string, boolean>;
    return features[featureKey] || false;
  }

  /**
   * Check if a tenant is within resource limits
   */
  async checkResourceLimit(tenantId: string, resourceType: string, additionalAmount: number = 0): Promise<boolean> {
    const subscriptionData = await this.getTenantSubscriptionWithPlan(tenantId);

    if (!subscriptionData) {
      return false;
    }

    const { subscription, plan } = subscriptionData;

    // Check if subscription is active
    if (subscription.status !== 'active' && subscription.status !== 'trialing') {
      return false;
    }

    // Check if subscription is expired
    if (subscription.endDate < new Date()) {
      return false;
    }

    // Get current usage
    const usage = await this.getTenantUsage(tenantId);
    const currentUsage = usage?.usage as Record<string, number> || {};
    const currentAmount = currentUsage[resourceType] || 0;
    const totalAmount = currentAmount + additionalAmount;

    // Check custom limits first
    if (subscription.customLimits) {
      const customLimits = subscription.customLimits as Record<string, number>;
      if (resourceType in customLimits) {
        return totalAmount <= customLimits[resourceType];
      }
    }

    // Then check plan limits
    const limits = plan.limits as Record<string, number>;
    return totalAmount <= (limits[resourceType] || 0);
  }

  /**
   * Get subscription cache for synchronization
   */
  async getSubscriptionCache(): Promise<{
    plans: SubscriptionPlan[];
    features: Feature[];
    tenantSubscriptions: TenantSubscription[];
  }> {
    const [plans, features, tenantSubscriptions] = await Promise.all([
      this.getPlans(),
      this.getFeatures(),
      storage.getTenantSubscriptions()
    ]);

    return {
      plans,
      features,
      tenantSubscriptions
    };
  }

  /**
   * Get user-tenant relationships for a user
   */
  async getUserTenants(userId: string | number): Promise<any[]> {
    return await storage.getUserTenants(Number(userId));
  }

  /**
   * Update a user-tenant relationship
   */
  async updateUserTenant(id: number, data: any): Promise<any> {
    return await storage.updateUserTenant(id, data);
  }

  /**
   * Get users for a tenant
   */
  async getTenantUsers(tenantId: number): Promise<any[]> {
    return await storage.getTenantUsers(tenantId);
  }

  /**
   * Create a user-tenant relationship
   */
  async createUserTenant(data: any): Promise<any> {
    return await storage.createUserTenant(data);
  }

  /**
   * Update a tenant
   */
  async updateTenant(id: number, data: any): Promise<any> {
    return await storage.updateTenant(id, data);
  }

  /**
   * Get all tenants
   */
  async getTenants(): Promise<any[]> {
    return await storage.getTenants();
  }

  /**
   * Get a tenant by ID
   */
  async getTenant(id: number): Promise<any> {
    return await storage.getTenant(id);
  }

  /**
   * Create a tenant
   */
  async createTenant(data: any): Promise<any> {
    return await storage.createTenant(data);
  }

  /**
   * Get all tenant subscriptions
   */
  async getTenantSubscriptions(): Promise<any[]> {
    return await storage.getTenantSubscriptions();
  }

  /**
   * Publish a subscription event
   */
  private async publishEvent(type: SubscriptionEventType, payload: any): Promise<void> {
    await storage.createSubscriptionEvent({
      type,
      payload
    });

    // TODO: Implement event publishing to message queue
    // This could use Redis, RabbitMQ, or a cloud service
    console.log(`Event published: ${type}`, payload);
  }
}
