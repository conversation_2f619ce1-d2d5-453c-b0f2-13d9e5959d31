import { Request, Response } from 'express';
import OpenAI from 'openai';
import { Agent, Crew, Task } from 'crewai-js';
import { storage } from './storage';
import { MongoAiAdapter } from './ai/mongo-ai-adapter';

// Helper function to check if OpenAI API is available
export function isOpenAIAvailable(): boolean {
  return !!(process.env.CRM_OPENAI_API_KEY || process.env.OPENAI_API_KEY) && !!openai;
}

// Initialize OpenAI client if API key is available
let openai: OpenAI | null = null;
try {
  const apiKey = process.env.CRM_OPENAI_API_KEY || process.env.OPENAI_API_KEY;
  if (apiKey) {
    openai = new OpenAI({
      apiKey,
    });
  }
} catch (error) {
  console.warn('Failed to initialize OpenAI client:', error);
}

// Create helper functions for CrewAI integration
interface AITask {
  name: string;
  description: string;
  expected_output: string;
}

// Create sales agent for handling customer relationship tasks
const createSalesAgent = () => {
  return new Agent({
    name: 'Sales Agent',
    role: 'Sales Agent',
    goal: 'Provide the best sales insights and customer relationship management assistance',
    backstory: 'You are an expert sales professional with years of experience in identifying opportunities and managing customer relationships.',
    allowDelegation: true,
    verbose: true,
  });
};

// Create research agent for finding insights from data
const createResearchAgent = () => {
  return new Agent({
    name: 'Research Analyst',
    role: 'Research Analyst',
    goal: 'Analyze CRM data and provide actionable insights',
    backstory: 'You are a data-driven researcher specialized in turning complex business data into clear, actionable insights.',
    allowDelegation: true,
    verbose: true,
  });
};

// Create a strategy agent for business advice
const createStrategyAgent = () => {
  return new Agent({
    name: 'Strategy Consultant',
    role: 'Strategy Consultant',
    goal: 'Develop strategic recommendations for business growth',
    backstory: 'You are a strategic thinker with expertise in business development and growth planning.',
    allowDelegation: true,
    verbose: true,
  });
};

// Crew manager to orchestrate agents
const createCrmCrew = () => {
  const salesAgent = createSalesAgent();
  const researchAgent = createResearchAgent();
  const strategyAgent = createStrategyAgent();

  return new Crew({
    name: 'CRM Analysis Crew',
    agents: [salesAgent, researchAgent, strategyAgent],
    tasks: [],
    verbose: true,
  });
};

// API handlers
export async function handleChatCompletion(req: Request, res: Response) {
  try {
    // Check if MongoDB is enabled
    const useMongoDb = process.env.MONGODB_ENABLED === 'true';

    // Support both message array format (from CopilotKit) and direct message format
    let userMessage: string;
    let userId: any; // Can be number or string depending on database

    // Check if we're receiving a single message (from direct API endpoint)
    if (req.body.message && typeof req.body.message === 'string') {
      userMessage = req.body.message;
      userId = (req.session as any)?.userId;

      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
    }
    // Otherwise, use the CopilotKit messages array format
    else {
      const { messages } = req.body;

      if (!messages || !Array.isArray(messages)) {
        return res.status(400).json({ error: 'Invalid messages format' });
      }

      const lastMessage = messages[messages.length - 1];
      userMessage = lastMessage.content;
      userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
    }

    // If MongoDB is enabled, use the MongoDB adapter
    if (useMongoDb) {
      try {
        const { response, chatId } = await MongoAiAdapter.handleChatCompletion(userMessage, userId);

        // Return in the appropriate format based on the request type
        if (req.body.message && typeof req.body.message === 'string') {
          return res.json({
            id: chatId,
            userId: userId,
            message: userMessage,
            response: response,
            timestamp: new Date()
          });
        } else {
          return res.json({
            choices: [{
              message: {
                role: 'assistant',
                content: response
              }
            }]
          });
        }
      } catch (error: any) {
        console.error('Error in MongoDB chat completion:', error);
        return res.status(500).json({ error: error.message || 'An error occurred while processing the request' });
      }
    }

    // Check if OpenAI client is available
    if (!openai) {
      const errorMessage = "I'm sorry, but the AI service is not available at the moment. Please ensure the CRM_OPENAI_API_KEY is set in your environment.";

      // Still save the user's message
      const chat = await storage.createAiChat({
        userId: userId,
        message: userMessage,
        response: "AI service unavailable - CRM_OPENAI_API_KEY not provided",
        metadata: { error: "api_key_missing" }
      });

      // Return in the appropriate format based on the request type
      if (req.body.message && typeof req.body.message === 'string') {
        return res.json({
          id: chat.id,
          userId: chat.userId,
          message: chat.message,
          response: errorMessage,
          timestamp: chat.timestamp
        });
      } else {
        return res.json({
          choices: [{
            message: {
              role: 'assistant',
              content: errorMessage
            }
          }]
        });
      }
    }

    // Simple chat completion for basic queries
    if (!requiresAgentCrew(userMessage)) {
      const messages = [
        { role: 'system', content: 'You are a helpful CRM assistant for the Aizako CRM platform. Provide concise, accurate information to help users manage their contacts, companies, and business relationships effectively.' },
        { role: 'user', content: userMessage }
      ];

      const response = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: messages,
      });

      const assistantResponse = response.choices[0].message.content || '';

      // Save chat history
      const chat = await storage.createAiChat({
        userId: userId,
        message: userMessage,
        response: assistantResponse,
        metadata: { model: "gpt-4o" }
      });

      // Return in the appropriate format based on the request type
      if (req.body.message && typeof req.body.message === 'string') {
        return res.json({
          id: chat.id,
          userId: chat.userId,
          message: chat.message,
          response: assistantResponse,
          timestamp: chat.timestamp
        });
      } else {
        return res.json(response);
      }
    }
    // For complex queries, leverage CrewAI
    else {
      const analysisResult = await runCrewAnalysis(userMessage, userId);

      // Save chat history
      const chat = await storage.createAiChat({
        userId: userId,
        message: userMessage,
        response: analysisResult,
        metadata: { usedCrew: true }
      });

      // Return in the appropriate format based on the request type
      if (req.body.message && typeof req.body.message === 'string') {
        return res.json({
          id: chat.id,
          userId: chat.userId,
          message: chat.message,
          response: analysisResult,
          timestamp: chat.timestamp
        });
      } else {
        // Create a response in the format expected by CopilotKit
        return res.json({
          choices: [{
            message: {
              role: 'assistant',
              content: analysisResult
            }
          }]
        });
      }
    }
  } catch (error) {
    console.error('Error in chat completion:', error);
    return res.status(500).json({ error: 'An error occurred while processing the request' });
  }
}

// Helper to determine if a query needs multiple agents
function requiresAgentCrew(query: string): boolean {
  const complexTopics = [
    'strategy', 'analysis', 'recommendation', 'insight', 'forecast',
    'predict', 'trend', 'opportunity', 'growth', 'optimize',
    'improve', 'compare', 'relationship', 'network', 'prioritize'
  ];

  return complexTopics.some(topic =>
    query.toLowerCase().includes(topic)
  );
}

// Run the CrewAI analysis
async function runCrewAnalysis(query: string, userId: number): Promise<string> {
  try {
    // Get user-specific data for context
    const userContacts = await storage.getContacts();
    const userCompanies = await storage.getCompanies();
    const userOpportunities = await storage.getOpportunities();

    // Create context for the crew
    const contextData = {
      query,
      contacts: userContacts,
      companies: userCompanies,
      opportunities: userOpportunities
    };

    // Create task based on the query
    const analysisTask = new Task({
      description: `Analyze the following query and provide insights: "${query}".
      Use the following CRM data for context: ${JSON.stringify(contextData)}`,
      expected_output: 'A detailed analysis with actionable recommendations',
      agent: createResearchAgent() // Assign to research agent
    });

    // Initialize the crew with the task
    const crmCrew = new Crew({
      name: 'CRM Analysis Crew',
      agents: [createSalesAgent(), createResearchAgent(), createStrategyAgent()],
      tasks: [analysisTask],
      verbose: true,
    });

    // Run the crew analysis
    const result = await crmCrew.kickoff();
    return result || 'No insights available at this time.';
  } catch (error) {
    console.error('Error running crew analysis:', error);
    return 'I encountered an error while analyzing your request. Please try again later.';
  }
}

// Function to generate insights based on CRM data
export async function generateInsights(userId: any) { // Can be number or string depending on database
  try {
    // Check if MongoDB is enabled
    const useMongoDb = process.env.MONGODB_ENABLED === 'true';

    // If MongoDB is enabled, use the MongoDB adapter
    if (useMongoDb) {
      try {
        return await MongoAiAdapter.generateInsights(userId);
      } catch (error: any) {
        console.error('Error in MongoDB insights generation:', error);
        return [
          {
            id: 0,
            createdAt: new Date(),
            title: "AI Service Error",
            description: error.message || "An error occurred while generating insights.",
            targetType: "system",
            targetId: null,
            importance: 3,
            category: "system",
            read: false
          }
        ];
      }
    }

    // Check if OpenAI is available
    if (!openai) {
      console.warn('OpenAI client not available for generating insights');
      return [
        {
          id: 0,
          createdAt: new Date(),
          title: "AI Service Unavailable",
          description: "The AI service is currently unavailable. Please ensure the CRM_OPENAI_API_KEY is set in your environment.",
          targetType: "system",
          targetId: null,
          importance: 3,
          category: "system",
          read: false
        }
      ];
    }

    // Get relevant data from storage
    const contacts = await storage.getContacts();
    const companies = await storage.getCompanies();
    const opportunities = await storage.getOpportunities();

    // Use OpenAI for initial analysis
    const prompt = `
      As a CRM AI assistant, analyze the following data and generate 3 key insights that could help improve business relationships or identify opportunities:

      Contacts: ${JSON.stringify(contacts)}
      Companies: ${JSON.stringify(companies)}
      Opportunities: ${JSON.stringify(opportunities)}

      For each insight, provide:
      1. A clear title
      2. A brief description of the finding
      3. A specific, actionable recommendation

      Format each insight as a JSON object with these fields: title, description, recommendation.
      Return an array of these 3 insight objects.
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [{ role: 'user', content: prompt }],
      response_format: { type: "json_object" }
    });

    // Parse the insights from the response
    const content = response.choices[0].message.content;
    if (!content) return [];

    const parsedInsights = JSON.parse(content);
    const insights = Array.isArray(parsedInsights.insights)
      ? parsedInsights.insights
      : [parsedInsights]; // Handle different response formats

    // Store the insights in the database
    const storedInsights = [];
    for (const insight of insights) {
      const storedInsight = await storage.createInsight({
        targetType: 'general',
        targetId: 0,
        title: insight.title,
        description: insight.description || '',
        importance: 2,
        category: 'ai-generated',
        read: false
      });
      storedInsights.push(storedInsight);
    }

    return storedInsights;
  } catch (error) {
    console.error('Error generating insights:', error);
    return [];
  }
}

// Function to enrich contact data with AI
export async function enrichContactData(contactId: any) { // Can be number or string depending on database
  try {
    // Check if MongoDB is enabled
    const useMongoDb = process.env.MONGODB_ENABLED === 'true';

    // If MongoDB is enabled, use the MongoDB adapter
    if (useMongoDb) {
      try {
        return await MongoAiAdapter.enrichContactData(contactId);
      } catch (error: any) {
        console.error('Error in MongoDB contact enrichment:', error);
        return {
          message: "AI service error",
          error: error.message || "An error occurred while enriching contact data"
        };
      }
    }

    // Check if OpenAI is available
    if (!openai) {
      console.warn('OpenAI client not available for enriching contact data');
      return {
        message: "AI service unavailable",
        error: "CRM_OPENAI_API_KEY not provided"
      };
    }

    const contact = await storage.getContact(contactId);
    if (!contact) {
      throw new Error('Contact not found');
    }

    // Get related company info for context
    const company = contact.companyId
      ? await storage.getCompany(contact.companyId)
      : null;

    // Get opportunities related to this contact
    const opportunities = await storage.getOpportunitiesByContact(contactId);

    // Use OpenAI to enrich the contact data
    const prompt = `
      As a CRM AI assistant, analyze this contact and provide enrichment insights:

      Contact: ${JSON.stringify(contact)}
      ${company ? `Company: ${JSON.stringify(company)}` : ''}
      ${opportunities.length > 0 ? `Opportunities: ${JSON.stringify(opportunities)}` : ''}

      Provide the following enrichment data in JSON format:
      1. interest_areas: An array of 3-5 likely professional interests based on their role and company
      2. communication_style: A brief description of the likely preferred communication style
      3. opportunity_insights: A specific, actionable recommendation for engaging with this contact
      4. relationship_strength: A score from 1-10 representing the estimated relationship strength
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [{ role: 'user', content: prompt }],
      response_format: { type: "json_object" }
    });

    const content = response.choices[0].message.content;
    if (!content) return null;

    return JSON.parse(content);
  } catch (error) {
    console.error('Error enriching contact data:', error);
    return null;
  }
}