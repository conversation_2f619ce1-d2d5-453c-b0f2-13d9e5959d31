import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { validateRequest, validateQuery } from '@shared/utils/validation';
import { FeatureFlagService } from '../services/feature-flag-service';
import { requireAdmin } from '../middleware/auth-middleware';
import {
  createFeatureFlagRequestSchema,
  updateFeatureFlagRequestSchema,
  checkFeatureFlagsRequestSchema
} from '@schemas/feature-flags';

const router = Router();

/**
 * GET /api/feature-flags
 * Get all feature flags (admin only)
 */
router.get('/', requireAdmin, validateQuery(
  z.object({
    enabled: z.string().optional().transform(val => val === 'true'),
    tags: z.string().optional().transform(val => val ? val.split(',') : undefined),
    limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 100),
    offset: z.string().optional().transform(val => val ? parseInt(val, 10) : 0),
  })
), async (req: Request, res: Response) => {
  try {
    const { enabled, tags, limit, offset } = req.validatedQuery;

    const featureFlags = await FeatureFlagService.getAllFlags({
      enabled,
      tags,
      limit,
      offset,
    });

    res.json({
      success: true,
      featureFlags,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch feature flags',
    });
  }
});

/**
 * POST /api/feature-flags
 * Create a new feature flag (admin only)
 */
router.post('/', requireAdmin, validateRequest(createFeatureFlagRequestSchema), async (req: Request, res: Response) => {
  try {
    const featureFlagData = req.validatedBody;
    const userId = req.session.userId;

    const featureFlag = await FeatureFlagService.createFlag({
      ...featureFlagData,
      createdBy: userId,
    });

    res.status(201).json({
      success: true,
      featureFlag,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create feature flag',
    });
  }
});

/**
 * GET /api/feature-flags/:key
 * Get a feature flag by key (admin only)
 */
router.get('/:key', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    const featureFlag = await FeatureFlagService.getAllFlags({
      limit: 1,
    }).then(flags => flags.find(flag => flag.key === key));

    if (!featureFlag) {
      return res.status(404).json({
        success: false,
        message: 'Feature flag not found',
      });
    }

    res.json({
      success: true,
      featureFlag,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch feature flag',
    });
  }
});

/**
 * PATCH /api/feature-flags/:key
 * Update a feature flag (admin only)
 */
router.patch('/:key', requireAdmin, validateRequest(updateFeatureFlagRequestSchema), async (req: Request, res: Response) => {
  try {
    const { key } = req.params;
    const featureFlagData = req.validatedBody;

    const featureFlag = await FeatureFlagService.updateFlag(key, featureFlagData);

    if (!featureFlag) {
      return res.status(404).json({
        success: false,
        message: 'Feature flag not found',
      });
    }

    res.json({
      success: true,
      featureFlag,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update feature flag',
    });
  }
});

/**
 * DELETE /api/feature-flags/:key
 * Delete a feature flag (admin only)
 */
router.delete('/:key', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    const success = await FeatureFlagService.deleteFlag(key);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Feature flag not found',
      });
    }

    res.json({
      success: true,
      message: 'Feature flag deleted successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete feature flag',
    });
  }
});

/**
 * GET /api/feature-flags/check/:key
 * Check if a feature flag is enabled for the current user
 */
router.get('/check/:key', async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    // Get context from request
    const context = FeatureFlagService.getContextFromRequest(req);

    // Check if the feature flag is enabled
    const enabled = await FeatureFlagService.isEnabled(key, context);

    res.json({
      success: true,
      enabled,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to check feature flag',
    });
  }
});

/**
 * POST /api/feature-flags/check-batch
 * Check multiple feature flags at once
 */
router.post('/check-batch', validateRequest(checkFeatureFlagsRequestSchema), async (req: Request, res: Response) => {
  try {
    const { keys } = req.validatedBody;

    // Get context from request
    const context = FeatureFlagService.getContextFromRequest(req);

    // Check each feature flag
    const flags: Record<string, boolean> = {};
    for (const key of keys) {
      flags[key] = await FeatureFlagService.isEnabled(key, context);
    }

    res.json({
      success: true,
      flags,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to check feature flags',
    });
  }
});

export default router;
