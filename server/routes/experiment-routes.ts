import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { validateRequest, validateQuery } from '@shared/utils/validation';
import { ExperimentService } from '../services/experiment-service';
import { requireAdmin } from '../middleware/auth-middleware';
import {
  createExperimentRequestSchema,
  updateExperimentRequestSchema,
  trackConversionRequestSchema,
  funnelAnalysisRequestSchema
} from '@schemas/experiments';

const router = Router();

/**
 * GET /api/experiments
 * Get all experiments (admin only)
 */
router.get('/', requireAdmin, validateQuery(
  z.object({
    status: z.string().optional(),
    limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 100),
    offset: z.string().optional().transform(val => val ? parseInt(val, 10) : 0),
  })
), async (req: Request, res: Response) => {
  try {
    const { status, limit, offset } = req.validatedQuery;

    const experiments = await ExperimentService.getAllExperiments({
      status: status as any,
      limit,
      offset,
    });

    res.json({
      success: true,
      experiments,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch experiments',
    });
  }
});

/**
 * POST /api/experiments
 * Create a new experiment (admin only)
 */
router.post('/', requireAdmin, validateRequest(createExperimentRequestSchema), async (req: Request, res: Response) => {
  try {
    const experimentData = req.validatedBody;
    const userId = req.session.userId;

    const experiment = await ExperimentService.createExperiment({
      ...experimentData,
      createdBy: userId,
    });

    res.status(201).json({
      success: true,
      experiment,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create experiment',
    });
  }
});

/**
 * GET /api/experiments/:key
 * Get an experiment by key (admin only)
 */
router.get('/:key', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    const experiment = await ExperimentService.getExperiment(key);

    if (!experiment) {
      return res.status(404).json({
        success: false,
        message: 'Experiment not found',
      });
    }

    res.json({
      success: true,
      experiment,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch experiment',
    });
  }
});

/**
 * PATCH /api/experiments/:key
 * Update an experiment (admin only)
 */
router.patch('/:key', requireAdmin, validateRequest(updateExperimentRequestSchema), async (req: Request, res: Response) => {
  try {
    const { key } = req.params;
    const experimentData = req.validatedBody;

    const experiment = await ExperimentService.updateExperiment(key, experimentData);

    if (!experiment) {
      return res.status(404).json({
        success: false,
        message: 'Experiment not found',
      });
    }

    res.json({
      success: true,
      experiment,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update experiment',
    });
  }
});

/**
 * DELETE /api/experiments/:key
 * Delete an experiment (admin only)
 */
router.delete('/:key', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    const success = await ExperimentService.deleteExperiment(key);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Experiment not found',
      });
    }

    res.json({
      success: true,
      message: 'Experiment deleted successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete experiment',
    });
  }
});

/**
 * GET /api/experiments/:key/variant
 * Get the variant for an experiment
 */
router.get('/:key/variant', async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    // Check if user is authenticated
    if (!req.session.userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get context from request
    const context = ExperimentService.getContextFromRequest(req);

    // Get variant
    const variant = await ExperimentService.getVariant(key, context);

    res.json({
      success: true,
      variant: variant || 'control',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get experiment variant',
    });
  }
});

/**
 * POST /api/experiments/:key/conversion
 * Track a conversion for an experiment
 */
router.post('/:key/conversion', validateRequest(trackConversionRequestSchema), async (req: Request, res: Response) => {
  try {
    const { key } = req.params;
    const { goalKey, value, metadata } = req.validatedBody;

    // Check if user is authenticated
    if (!req.session.userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get context from request
    const context = ExperimentService.getContextFromRequest(req);

    // Track conversion
    const success = await ExperimentService.trackConversion(key, goalKey, context, value, metadata);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Experiment or goal not found, or user not in experiment',
      });
    }

    res.json({
      success: true,
      message: 'Conversion tracked successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to track conversion',
    });
  }
});

/**
 * GET /api/experiments/:key/results
 * Get results for an experiment (admin only)
 */
router.get('/:key/results', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    const results = await ExperimentService.getResults(key);

    res.json({
      success: true,
      ...results,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get experiment results',
    });
  }
});

export default router;
