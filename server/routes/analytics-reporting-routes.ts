import { Express } from 'express';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/subscription';
import cbiRoutes from './cbi-routes';
import attributionRoutes from './attribution-routes';
import eventsCollectionRoutes from './events-collection-routes';
import analyticsDatasetRoutes from './analytics-dataset-routes';

/**
 * Register Analytics & Reporting routes
 */
export function registerAnalyticsReportingRoutes(app: Express): void {
  // Register Conversational BI routes
  app.use('/api/cbi', 
    authenticateUser, 
    checkFeatureEntitlement('analytics.conversational_bi'), 
    cbiRoutes
  );

  // Register Attribution routes
  app.use('/api/attribution', 
    authenticateUser, 
    checkFeatureEntitlement('analytics.attribution'), 
    attributionRoutes
  );

  // Register Events Collection routes
  app.use('/api/events', 
    eventsCollectionRoutes
  );

  // Register Analytics Dataset routes
  app.use('/api/datasets', 
    authenticateUser, 
    checkFeatureEntitlement('analytics.conversational_bi'), 
    analyticsDatasetRoutes
  );
}
