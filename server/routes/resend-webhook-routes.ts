/**
 * Resend Webhook Routes
 *
 * This module defines the API routes for handling Resend webhook events.
 */

import { Express, Request, Response } from 'express';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import resendService from '../services/resend-service';

// Webhook secret for verifying Resend webhook events
const RESEND_WEBHOOK_SECRET = process.env.RESEND_WEBHOOK_SECRET || '';

/**
 * Verify the webhook signature from Resend
 */
async function verifyWebhookSignature(req: Request): Promise<{ isValid: boolean; tenantId?: string }> {
  const signature = req.headers['resend-signature'] as string;

  if (!signature) {
    logger.warn('No Resend signature found in webhook request');
    return { isValid: false };
  }

  try {
    // Parse the signature
    const [timestamp, signatureHash] = signature.split(',');
    const timestampValue = timestamp.split('=')[1];
    const signatureValue = signatureHash.split('=')[1];

    // Check if the timestamp is within 5 minutes
    const timestampDate = new Date(parseInt(timestampValue) * 1000);
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    if (timestampDate < fiveMinutesAgo) {
      logger.warn('Webhook timestamp is too old');
      return { isValid: false };
    }

    // Extract domain ID from the event data
    const domainId = req.body?.data?.domain_id;

    if (!domainId) {
      // If no domain ID is present, use the global webhook secret
      if (!RESEND_WEBHOOK_SECRET) {
        logger.warn('No domain ID in webhook and global webhook secret not configured');
        return { isValid: false };
      }

      // Verify with global secret
      const payload = `${timestampValue}.${JSON.stringify(req.body)}`;
      const expectedSignature = crypto
        .createHmac('sha256', RESEND_WEBHOOK_SECRET)
        .update(payload)
        .digest('hex');

      const isValid = crypto.timingSafeEqual(
        Buffer.from(signatureValue),
        Buffer.from(expectedSignature)
      );

      return { isValid };
    }

    // Get tenant domain by Resend domain ID
    const tenantDomainService = require('../services/tenant-domain-service').default;
    const tenantDomain = await tenantDomainService.getByResendDomainId(domainId);

    if (!tenantDomain || !tenantDomain.webhookSecret) {
      logger.warn(`No tenant domain found for Resend domain ID ${domainId} or webhook secret not set`);
      return { isValid: false };
    }

    // Verify with tenant-specific webhook secret
    const payload = `${timestampValue}.${JSON.stringify(req.body)}`;
    const expectedSignature = crypto
      .createHmac('sha256', tenantDomain.webhookSecret)
      .update(payload)
      .digest('hex');

    const isValid = crypto.timingSafeEqual(
      Buffer.from(signatureValue),
      Buffer.from(expectedSignature)
    );

    return {
      isValid,
      tenantId: tenantDomain.tenantId.toString()
    };
  } catch (error) {
    logger.error('Error verifying webhook signature:', error);
    return { isValid: false };
  }
}

export function registerResendWebhookRoutes(app: Express) {
  /**
   * Webhook endpoint for Resend events
   */
  app.post('/api/webhooks/resend', async (req: Request, res: Response) => {
    try {
      // Verify the webhook signature
      const verification = await verifyWebhookSignature(req);

      if (!verification.isValid) {
        logger.warn('Invalid webhook signature');
        return res.status(401).json({ message: 'Invalid signature' });
      }

      const { type, data } = req.body;

      if (!type || !data) {
        logger.warn('Invalid webhook payload');
        return res.status(400).json({ message: 'Invalid payload' });
      }

      logger.info(`Received Resend webhook event: ${type}${verification.tenantId ? ` for tenant ${verification.tenantId}` : ''}`);

      // Add tenant ID to data if available
      if (verification.tenantId) {
        data.tenantId = verification.tenantId;
      }

      // Process the webhook event
      await resendService.processWebhookEvent({ type, data });

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error processing Resend webhook:', error);
      res.status(500).json({ message: 'Error processing webhook' });
    }
  });

  /**
   * Endpoint to update domain tracking settings
   */
  app.patch('/api/resend/domains/:domainId/tracking',
    async (req: Request, res: Response) => {
      try {
        const { domainId } = req.params;
        const { openTracking, clickTracking } = req.body;

        if (openTracking === undefined && clickTracking === undefined) {
          return res.status(400).json({ message: 'At least one tracking option must be specified' });
        }

        // Update tracking settings
        const result = await resendService.updateDomainTracking(domainId, {
          openTracking,
          clickTracking
        });

        if (result.success) {
          res.status(200).json({ success: true });
        } else {
          res.status(500).json({ message: result.error || 'Failed to update tracking settings' });
        }
      } catch (error) {
        logger.error('Error updating domain tracking settings:', error);
        res.status(500).json({ message: 'Error updating tracking settings' });
      }
    }
  );
}

export default registerResendWebhookRoutes;
