import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { AttributionService } from '../services/attribution-service';
import { authenticateUser, requireAdmin } from '../middleware/auth';
import { checkFeatureEntitlement } from '../middleware/subscription';
import { validateRequest, validateQuery } from '../middleware/validation';
import { 
  runAttributionRequestSchema,
  getAttributionResultsRequestSchema,
  budgetOptimizationRequestSchema
} from '@schemas/analytics-reporting';
import { logger } from '../utils/logger';

const router = Router();

/**
 * POST /api/attribution/run
 * Run attribution model
 */
router.post('/run', 
  authenticateUser,
  checkFeatureEntitlement('analytics.attribution'),
  validateRequest(runAttributionRequestSchema),
  async (req: Request, res: Response) => {
    try {
      const requestData = req.validatedBody;
      
      // Add tenant_id from session if not provided
      if (!requestData.tenant_id && req.session?.tenantId) {
        requestData.tenant_id = req.session.tenantId;
      }
      
      const response = await AttributionService.runAttribution(requestData);
      
      res.status(response.success ? 200 : 400).json(response);
    } catch (error: any) {
      logger.error('Error in /api/attribution/run:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * GET /api/attribution/results
 * Get attribution results
 */
router.get('/results',
  authenticateUser,
  checkFeatureEntitlement('analytics.attribution'),
  validateQuery(
    z.object({
      window: z.string().min(1),
      model_type: z.enum(['markov', 'shapley', 'first_touch', 'last_touch', 'linear']),
      group_by: z.enum(['channel', 'campaign', 'medium', 'source', 'creative', 'keyword']).optional(),
      limit: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
      sort_by: z.enum(['credit_pct', 'roi', 'revenue', 'cost', 'conversions']).optional(),
      sort_order: z.enum(['asc', 'desc']).optional(),
    })
  ),
  async (req: Request, res: Response) => {
    try {
      const queryParams = req.validatedQuery;
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      const requestData = {
        ...queryParams,
        tenant_id: tenantId
      };
      
      const response = await AttributionService.getAttributionResults(requestData);
      
      res.status(response.success ? 200 : 400).json(response);
    } catch (error: any) {
      logger.error('Error in /api/attribution/results:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * POST /api/attribution/optimize-budget
 * Optimize budget allocation
 */
router.post('/optimize-budget',
  authenticateUser,
  checkFeatureEntitlement('analytics.attribution'),
  validateRequest(budgetOptimizationRequestSchema),
  async (req: Request, res: Response) => {
    try {
      const requestData = req.validatedBody;
      
      // Add tenant_id from session if not provided
      if (!requestData.tenant_id && req.session?.tenantId) {
        requestData.tenant_id = req.session.tenantId;
      }
      
      // This is a placeholder - in a real implementation, this would call a budget optimization service
      const response = {
        success: true,
        recommendations: [
          {
            channel: 'ads',
            campaign: 'google_search',
            current_budget: 1000,
            recommended_budget: 1200,
            change_amount: 200,
            change_percentage: 20,
            projected_impact: {
              revenue: 3600,
              conversions: 12,
              roi: 3.0
            }
          },
          {
            channel: 'social',
            campaign: 'linkedin_sponsored',
            current_budget: 800,
            recommended_budget: 600,
            change_amount: -200,
            change_percentage: -25,
            projected_impact: {
              revenue: 1800,
              conversions: 6,
              roi: 3.0
            }
          }
        ],
        summary: {
          total_current_budget: 1800,
          total_recommended_budget: 1800,
          projected_revenue_increase: 400,
          projected_conversion_increase: 2,
          projected_roi_increase: 0.2
        }
      };
      
      res.json(response);
    } catch (error: any) {
      logger.error('Error in /api/attribution/optimize-budget:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

export default router;
