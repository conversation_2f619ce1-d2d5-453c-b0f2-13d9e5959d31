import { Express, Request, Response } from 'express';
import { authenticateUser } from '../middleware/auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import { followUpCoachService } from '../services/follow-up-coach-service';

/**
 * Register follow-up coach routes
 */
export function registerFollowUpCoachRoutes(app: Express) {
  // Get all follow-ups
  app.get('/api/follow-ups',
    authenticateUser,
    checkFeatureEntitlement('ai.follow-up-coach'),
    async (req: Request, res: Response) => {
      try {
        const {
          status,
          type,
          priority,
          opportunityId,
          contactId,
          companyId,
          startDate,
          endDate,
          search
        } = req.query;
        
        const userId = req.session.userId;
        
        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }
        
        const followUps = await followUpCoachService.getFollowUps({
          userId: userId.toString(),
          status: status as any,
          type: type as any,
          priority: priority as any,
          opportunityId: opportunityId as string,
          contactId: contactId as string,
          companyId: companyId as string,
          startDate: startDate ? new Date(startDate as string) : undefined,
          endDate: endDate ? new Date(endDate as string) : undefined,
          search: search as string
        });
        
        res.json(followUps);
      } catch (error) {
        console.error('Error getting follow-ups:', error);
        res.status(500).json({ message: 'Failed to get follow-ups' });
      }
    }
  );
  
  // Get follow-up by ID
  app.get('/api/follow-ups/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.follow-up-coach'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        
        const followUp = await followUpCoachService.getFollowUpById(id);
        
        if (!followUp) {
          return res.status(404).json({ message: 'Follow-up not found' });
        }
        
        res.json(followUp);
      } catch (error) {
        console.error(`Error getting follow-up with ID ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get follow-up' });
      }
    }
  );
  
  // Create a new follow-up
  app.post('/api/follow-ups',
    authenticateUser,
    checkFeatureEntitlement('ai.follow-up-coach'),
    async (req: Request, res: Response) => {
      try {
        const {
          title,
          description,
          opportunityId,
          contactId,
          companyId,
          activityId,
          scheduledDate,
          completedDate,
          status,
          type,
          priority,
          template,
          content,
          reminderDate,
          isAIGenerated,
          tags,
          customFields
        } = req.body;
        
        if (!title || !scheduledDate) {
          return res.status(400).json({ message: 'Title and scheduled date are required' });
        }
        
        const userId = req.session.userId;
        
        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }
        
        const followUp = await followUpCoachService.createFollowUp(
          {
            title,
            description,
            opportunityId,
            contactId,
            companyId,
            activityId,
            scheduledDate: new Date(scheduledDate),
            completedDate: completedDate ? new Date(completedDate) : undefined,
            status,
            type,
            priority,
            template,
            content,
            reminderDate: reminderDate ? new Date(reminderDate) : undefined,
            isAIGenerated,
            tags,
            customFields
          },
          userId.toString()
        );
        
        res.status(201).json(followUp);
      } catch (error) {
        console.error('Error creating follow-up:', error);
        res.status(500).json({ message: 'Failed to create follow-up' });
      }
    }
  );
  
  // Update a follow-up
  app.put('/api/follow-ups/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.follow-up-coach'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const {
          title,
          description,
          scheduledDate,
          completedDate,
          status,
          type,
          priority,
          template,
          content,
          reminderDate,
          tags,
          customFields
        } = req.body;
        
        const followUp = await followUpCoachService.updateFollowUp(
          id,
          {
            title,
            description,
            scheduledDate: scheduledDate ? new Date(scheduledDate) : undefined,
            completedDate: completedDate ? new Date(completedDate) : undefined,
            status,
            type,
            priority,
            template,
            content,
            reminderDate: reminderDate ? new Date(reminderDate) : undefined,
            tags,
            customFields
          }
        );
        
        if (!followUp) {
          return res.status(404).json({ message: 'Follow-up not found' });
        }
        
        res.json(followUp);
      } catch (error) {
        console.error(`Error updating follow-up with ID ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to update follow-up' });
      }
    }
  );
  
  // Delete a follow-up
  app.delete('/api/follow-ups/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.follow-up-coach'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        
        const success = await followUpCoachService.deleteFollowUp(id);
        
        if (!success) {
          return res.status(404).json({ message: 'Follow-up not found' });
        }
        
        res.status(204).send();
      } catch (error) {
        console.error(`Error deleting follow-up with ID ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to delete follow-up' });
      }
    }
  );
  
  // Generate follow-up recommendations
  app.post('/api/follow-ups/generate',
    authenticateUser,
    checkFeatureEntitlement('ai.follow-up-coach'),
    async (req: Request, res: Response) => {
      try {
        const { opportunityId, contactId, companyId, activityId, count } = req.body;
        
        if (!opportunityId && !contactId && !companyId && !activityId) {
          return res.status(400).json({ 
            message: 'At least one of opportunityId, contactId, companyId, or activityId must be provided' 
          });
        }
        
        const userId = req.session.userId;
        
        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }
        
        const followUps = await followUpCoachService.generateFollowUpRecommendations(
          {
            opportunityId,
            contactId,
            companyId,
            activityId,
            count
          },
          userId.toString()
        );
        
        res.json(followUps);
      } catch (error) {
        console.error('Error generating follow-up recommendations:', error);
        res.status(500).json({ message: 'Failed to generate follow-up recommendations' });
      }
    }
  );
}
