import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { validateRequest, validateQuery } from '@shared/utils/validation';
import { AnalyticsService } from '../services/analytics-service';
import { requireAdmin } from '../middleware/auth-middleware';
import {
  trackEventRequestSchema,
  getEventsRequestSchema,
  getEventCountsRequestSchema,
  getSessionsRequestSchema,
  funnelAnalysisRequestSchema
} from '@schemas/analytics';

const router = Router();

/**
 * POST /api/analytics/events
 * Track an analytics event
 */
router.post('/events', validateRequest(trackEventRequestSchema), async (req: Request, res: Response) => {
  try {
    const eventData = req.validatedBody;
    const userId = req.session?.userId;
    const tenantId = req.session?.tenantId;

    // Get context from request if not provided
    const context = eventData.context || AnalyticsService.getContextFromRequest(req);

    // Track the event
    const event = await AnalyticsService.trackEvent({
      ...eventData,
      userId,
      tenantId,
      context,
    });

    res.status(201).json({
      success: true,
      event,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to track event',
    });
  }
});

/**
 * GET /api/analytics/events
 * Get analytics events (admin only)
 */
router.get('/events', requireAdmin, validateQuery(
  z.object({
    userId: z.string().optional(),
    tenantId: z.string().optional(),
    sessionId: z.string().optional(),
    eventType: z.string().optional(),
    eventName: z.string().optional(),
    startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 100),
    offset: z.string().optional().transform(val => val ? parseInt(val, 10) : 0),
  })
), async (req: Request, res: Response) => {
  try {
    const {
      userId,
      tenantId,
      sessionId,
      eventType,
      eventName,
      startDate,
      endDate,
      limit,
      offset,
    } = req.validatedQuery;

    const events = await AnalyticsService.getEvents({
      userId,
      tenantId,
      sessionId,
      eventType,
      eventName,
      startDate,
      endDate,
      limit,
      offset,
    });

    res.json({
      success: true,
      events,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch events',
    });
  }
});

/**
 * GET /api/analytics/counts
 * Get event counts (admin only)
 */
router.get('/counts', requireAdmin, validateQuery(
  z.object({
    userId: z.string().optional(),
    tenantId: z.string().optional(),
    eventType: z.string().optional(),
    startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    groupBy: z.enum(['eventType', 'eventName', 'day', 'week', 'month']).optional(),
  })
), async (req: Request, res: Response) => {
  try {
    const {
      userId,
      tenantId,
      eventType,
      startDate,
      endDate,
      groupBy,
    } = req.validatedQuery;

    const counts = await AnalyticsService.getEventCounts({
      userId,
      tenantId,
      eventType,
      startDate,
      endDate,
      groupBy,
    });

    res.json({
      success: true,
      counts,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch event counts',
    });
  }
});

/**
 * GET /api/analytics/sessions
 * Get user sessions (admin only)
 */
router.get('/sessions', requireAdmin, validateQuery(
  z.object({
    userId: z.string().optional(),
    tenantId: z.string().optional(),
    startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 100),
    offset: z.string().optional().transform(val => val ? parseInt(val, 10) : 0),
  })
), async (req: Request, res: Response) => {
  try {
    const {
      userId,
      tenantId,
      startDate,
      endDate,
      limit,
      offset,
    } = req.validatedQuery;

    const sessions = await AnalyticsService.getUserSessions({
      userId,
      tenantId,
      startDate,
      endDate,
      limit,
      offset,
    });

    res.json({
      success: true,
      sessions,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch sessions',
    });
  }
});

/**
 * POST /api/analytics/funnel
 * Get funnel analysis (admin only)
 */
router.post('/funnel', requireAdmin, validateRequest(funnelAnalysisRequestSchema), async (req: Request, res: Response) => {
  try {
    const {
      steps,
      startDate,
      endDate,
      windowHours,
    } = req.validatedBody;

    const userId = req.query.userId as string;
    const tenantId = req.query.tenantId as string;

    const funnel = await AnalyticsService.getFunnelAnalysis({
      steps,
      userId,
      tenantId,
      startDate,
      endDate,
      windowHours,
    });

    res.json({
      success: true,
      funnel,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to analyze funnel',
    });
  }
});

/**
 * GET /api/analytics/user
 * Get current user's analytics
 */
router.get('/user', async (req: Request, res: Response) => {
  try {
    const userId = req.session?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get recent events for the user
    const events = await AnalyticsService.getEvents({
      userId,
      limit: 50,
    });

    // Get event counts by type
    const counts = await AnalyticsService.getEventCounts({
      userId,
      groupBy: 'eventType',
    });

    // Get user sessions
    const sessions = await AnalyticsService.getUserSessions({
      userId,
      limit: 10,
    });

    res.json({
      success: true,
      events,
      counts,
      sessions,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch user analytics',
    });
  }
});

export default router;
