import { Router, Request, Response } from 'express';
import mongoose from 'mongoose';
import { z } from 'zod';
import { Activity } from '../models/mongoose';
import { validateRequest, validateQuery } from '@shared/utils/validation';
import { createActivityRequestSchema, updateActivityRequestSchema } from '@schemas/api';
import { isActivity, isActivityArray } from '@types/guards';
import { Activity as ActivityType } from '@types/core';
import { CreateActivityRequest, UpdateActivityRequest } from '@types/api';

const router = Router();

/**
 * GET /api/activities
 * Get all activities with pagination and filtering
 */
router.get('/', validateQuery(
  z.object({
    page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
    pageSize: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
    type: z.string().optional(),
    completed: z.string().optional().transform(val => val === 'true'),
    search: z.string().optional(),
    contactId: z.string().optional(),
    companyId: z.string().optional(),
    opportunityId: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  })
), async (req: Request, res: Response) => {
  try {
    const { 
      page, pageSize, type, completed, search, 
      contactId, companyId, opportunityId, 
      sortBy, sortOrder 
    } = req.validatedQuery;
    const userId = req.session.userId;
    
    // Build query
    let query: any = { owner: userId };
    
    if (type) {
      query.type = type;
    }
    
    if (completed !== undefined) {
      query.completed = completed;
    }
    
    if (contactId) {
      query.contactId = contactId;
    }
    
    if (companyId) {
      query.companyId = companyId;
    }
    
    if (opportunityId) {
      query.opportunityId = opportunityId;
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Build sort options
    const sort: any = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.date = 1; // Default sort by date asc
    }
    
    // Execute query with pagination
    const skip = (page - 1) * pageSize;
    const activities = await Activity.find(query)
      .sort(sort)
      .skip(skip)
      .limit(pageSize)
      .lean();
    
    // Get total count for pagination
    const total = await Activity.countDocuments(query);
    
    // Map MongoDB documents to our type system
    const mappedActivities = activities.map(activity => ({
      id: activity._id.toString(),
      type: activity.type,
      title: activity.title,
      description: activity.description,
      dueDate: activity.date,
      completed: activity.completed,
      completedAt: activity.completedAt,
      priority: activity.priority,
      contactId: activity.contactId?.toString(),
      companyId: activity.companyId?.toString(),
      opportunityId: activity.opportunityId?.toString(),
      assignedTo: activity.assignedTo?.toString(),
      createdAt: activity.createdAt,
      updatedAt: activity.updatedAt,
      createdBy: activity.owner.toString(),
    }));
    
    // Return paginated response
    res.json({
      success: true,
      activities: mappedActivities,
      total,
      page,
      pageSize,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch activities',
    });
  }
});

/**
 * GET /api/activities/:id
 * Get a single activity by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid activity ID format"
      });
    }
    
    const activity = await Activity.findOne({ _id: id, owner: userId }).lean();
    
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: 'Activity not found',
      });
    }
    
    // Map MongoDB document to our type system
    const mappedActivity: ActivityType = {
      id: activity._id.toString(),
      type: activity.type,
      title: activity.title,
      description: activity.description,
      dueDate: activity.date,
      completed: activity.completed,
      completedAt: activity.completedAt,
      priority: activity.priority,
      contactId: activity.contactId?.toString(),
      companyId: activity.companyId?.toString(),
      opportunityId: activity.opportunityId?.toString(),
      assignedTo: activity.assignedTo?.toString(),
      createdAt: activity.createdAt,
      updatedAt: activity.updatedAt,
      createdBy: activity.owner.toString(),
    };
    
    res.json({
      success: true,
      activity: mappedActivity,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch activity',
    });
  }
});

/**
 * POST /api/activities
 * Create a new activity
 */
router.post('/', validateRequest(createActivityRequestSchema), async (req: Request, res: Response) => {
  try {
    const activityData: CreateActivityRequest = req.validatedBody;
    const userId = req.session.userId;
    
    // Create activity
    const activity = new Activity({
      ...activityData,
      date: activityData.dueDate ? new Date(activityData.dueDate) : new Date(),
      owner: userId,
      completed: false
    });
    
    await activity.save();
    
    // Map MongoDB document to our type system
    const mappedActivity: ActivityType = {
      id: activity._id.toString(),
      type: activity.type,
      title: activity.title,
      description: activity.description,
      dueDate: activity.date,
      completed: activity.completed,
      completedAt: activity.completedAt,
      priority: activity.priority,
      contactId: activity.contactId?.toString(),
      companyId: activity.companyId?.toString(),
      opportunityId: activity.opportunityId?.toString(),
      assignedTo: activity.assignedTo?.toString(),
      createdAt: activity.createdAt,
      updatedAt: activity.updatedAt,
      createdBy: activity.owner.toString(),
    };
    
    res.status(201).json({
      success: true,
      activity: mappedActivity,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create activity',
    });
  }
});

/**
 * PATCH /api/activities/:id
 * Update an existing activity
 */
router.patch('/:id', validateRequest(updateActivityRequestSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const activityData: UpdateActivityRequest = req.validatedBody;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid activity ID format"
      });
    }
    
    // Prepare update data
    const updateData: any = { ...activityData };
    
    // Convert date string to Date object
    if (updateData.dueDate) {
      updateData.date = new Date(updateData.dueDate);
      delete updateData.dueDate;
    }
    
    // If activity is marked as completed, set completedAt
    if (updateData.completed === true) {
      updateData.completedAt = new Date();
    }
    
    // Update activity
    const activity = await Activity.findOneAndUpdate(
      { _id: id, owner: userId },
      { $set: updateData },
      { new: true }
    ).lean();
    
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: 'Activity not found',
      });
    }
    
    // Map MongoDB document to our type system
    const mappedActivity: ActivityType = {
      id: activity._id.toString(),
      type: activity.type,
      title: activity.title,
      description: activity.description,
      dueDate: activity.date,
      completed: activity.completed,
      completedAt: activity.completedAt,
      priority: activity.priority,
      contactId: activity.contactId?.toString(),
      companyId: activity.companyId?.toString(),
      opportunityId: activity.opportunityId?.toString(),
      assignedTo: activity.assignedTo?.toString(),
      createdAt: activity.createdAt,
      updatedAt: activity.updatedAt,
      createdBy: activity.owner.toString(),
    };
    
    res.json({
      success: true,
      activity: mappedActivity,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update activity',
    });
  }
});

/**
 * DELETE /api/activities/:id
 * Delete an activity
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid activity ID format"
      });
    }
    
    const result = await Activity.deleteOne({ _id: id, owner: userId });
    
    if (result.deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Activity not found',
      });
    }
    
    res.json({
      success: true,
      message: 'Activity deleted successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete activity',
    });
  }
});

/**
 * PATCH /api/activities/:id/complete
 * Mark an activity as complete
 */
router.patch('/:id/complete', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid activity ID format"
      });
    }
    
    // Update activity
    const activity = await Activity.findOneAndUpdate(
      { _id: id, owner: userId },
      { 
        $set: { 
          completed: true,
          completedAt: new Date()
        } 
      },
      { new: true }
    ).lean();
    
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: 'Activity not found',
      });
    }
    
    // Map MongoDB document to our type system
    const mappedActivity: ActivityType = {
      id: activity._id.toString(),
      type: activity.type,
      title: activity.title,
      description: activity.description,
      dueDate: activity.date,
      completed: activity.completed,
      completedAt: activity.completedAt,
      priority: activity.priority,
      contactId: activity.contactId?.toString(),
      companyId: activity.companyId?.toString(),
      opportunityId: activity.opportunityId?.toString(),
      assignedTo: activity.assignedTo?.toString(),
      createdAt: activity.createdAt,
      updatedAt: activity.updatedAt,
      createdBy: activity.owner.toString(),
    };
    
    res.json({
      success: true,
      activity: mappedActivity,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to complete activity',
    });
  }
});

export default router;
