/**
 * Email Tracking Routes
 * 
 * This module defines the API routes for email tracking functionality.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import emailTrackingService from '../services/email-tracking-service';
import { EmailTracking } from '../models/mongoose';
import { logger } from '../utils/logger';

export function registerEmailTrackingRoutes(app: Express) {
  // Get all email tracking records for a user
  app.get('/api/email-tracking',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { contactId, status, page = '1', limit = '20' } = req.query;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Build query
        const query: any = { userId: new mongoose.Types.ObjectId(userId.toString()) };

        if (contactId) {
          query.contactId = new mongoose.Types.ObjectId(contactId.toString());
        }

        if (status) {
          query.status = status;
        }

        // Pagination
        const pageNum = parseInt(page as string);
        const limitNum = parseInt(limit as string);
        const skip = (pageNum - 1) * limitNum;

        // Get tracking records
        const trackingRecords = await EmailTracking.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum);

        // Get total count
        const totalCount = await EmailTracking.countDocuments(query);

        res.status(200).json({
          data: trackingRecords,
          pagination: {
            page: pageNum,
            limit: limitNum,
            totalCount,
            totalPages: Math.ceil(totalCount / limitNum)
          }
        });
      } catch (error) {
        logger.error('Error getting email tracking records:', error);
        res.status(500).json({ message: 'Failed to get email tracking records' });
      }
    }
  );

  // Get a specific email tracking record
  app.get('/api/email-tracking/:id',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get tracking record
        const trackingRecord = await EmailTracking.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!trackingRecord) {
          return res.status(404).json({ message: 'Email tracking record not found' });
        }

        res.status(200).json(trackingRecord);
      } catch (error) {
        logger.error(`Error getting email tracking record ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get email tracking record' });
      }
    }
  );

  // Create a new email tracking record
  app.post('/api/email-tracking',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const {
          contactId,
          sequenceId,
          sequenceStepId,
          messageId,
          subject,
          recipient,
          sender,
          trackingEnabled,
          linkTrackingEnabled,
          attachmentTrackingEnabled,
          customFields
        } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Validate required fields
        if (!messageId || !subject || !recipient || !sender) {
          return res.status(400).json({ message: 'Message ID, subject, recipient, and sender are required' });
        }

        // Create tracking record
        const trackingRecord = await emailTrackingService.createTracking({
          userId: userId.toString(),
          tenantId: req.headers['x-tenant-id'] as string,
          contactId,
          sequenceId,
          sequenceStepId,
          messageId,
          subject,
          recipient,
          sender,
          trackingEnabled,
          linkTrackingEnabled,
          attachmentTrackingEnabled,
          customFields
        });

        res.status(201).json(trackingRecord);
      } catch (error) {
        logger.error('Error creating email tracking record:', error);
        res.status(500).json({ message: 'Failed to create email tracking record' });
      }
    }
  );

  // Generate a reply draft
  app.post('/api/email-tracking/:id/reply-draft',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if tracking record exists and belongs to the user
        const trackingRecord = await EmailTracking.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!trackingRecord) {
          return res.status(404).json({ message: 'Email tracking record not found' });
        }

        // Generate reply draft
        const draftId = await emailTrackingService.generateReplyDraft(id);

        if (!draftId) {
          return res.status(500).json({ message: 'Failed to generate reply draft' });
        }

        res.status(200).json({ draftId });
      } catch (error) {
        logger.error(`Error generating reply draft for tracking record ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to generate reply draft' });
      }
    }
  );

  // Webhook for tracking pixel
  app.get('/pixel/:pixelId',
    async (req: Request, res: Response) => {
      try {
        const { pixelId } = req.params;

        // Process open event
        await emailTrackingService.processOpenEvent(pixelId, {
          ip: req.ip || req.socket.remoteAddress || 'unknown',
          userAgent: req.headers['user-agent'] || 'unknown',
          device: 'unknown', // Would need a device detection library
          location: {} // Would need a geolocation service
        });

        // Return a transparent 1x1 pixel
        res.set('Content-Type', 'image/gif');
        res.send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
      } catch (error) {
        logger.error(`Error processing pixel tracking for ${req.params.pixelId}:`, error);
        // Still return the pixel to avoid errors in the email client
        res.set('Content-Type', 'image/gif');
        res.send(Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'));
      }
    }
  );

  // Webhook for link tracking
  app.get('/link/:linkId',
    async (req: Request, res: Response) => {
      try {
        const { linkId } = req.params;
        const { url } = req.query;

        if (!url) {
          return res.status(400).json({ message: 'URL parameter is required' });
        }

        // Process click event
        await emailTrackingService.processClickEvent(linkId, {
          ip: req.ip || req.socket.remoteAddress || 'unknown',
          userAgent: req.headers['user-agent'] || 'unknown',
          device: 'unknown', // Would need a device detection library
          location: {}, // Would need a geolocation service
          url: url as string
        });

        // Redirect to the original URL
        res.redirect(url as string);
      } catch (error) {
        logger.error(`Error processing link tracking for ${req.params.linkId}:`, error);
        res.status(500).json({ message: 'Failed to process link tracking' });
      }
    }
  );

  // Webhook for email replies
  app.post('/api/webhooks/email/reply',
    async (req: Request, res: Response) => {
      try {
        const { messageId, subject, snippet, timestamp } = req.body;

        if (!messageId) {
          return res.status(400).json({ message: 'Message ID is required' });
        }

        // Process reply event
        await emailTrackingService.processReplyEvent(messageId, {
          subject,
          snippet,
          timestamp: timestamp ? new Date(timestamp) : undefined
        });

        res.status(200).json({ success: true });
      } catch (error) {
        logger.error('Error processing email reply webhook:', error);
        res.status(500).json({ message: 'Failed to process email reply' });
      }
    }
  );
}

export default registerEmailTrackingRoutes;
