/**
 * Smart Interaction Timeline Routes
 * 
 * This module defines the API routes for the Smart Interaction Timeline.
 */

import { Express, Request, Response } from 'express';
import { interactionTimelineService } from '../services/interaction-timeline-service';
import { authenticateUser } from '../middleware/auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import { MCPSourceType } from '../mcp';

/**
 * Register interaction timeline routes
 */
export function registerInteractionTimelineRoutes(app: Express) {
  // Get interactions for a contact
  app.get('/api/contacts/:id/interactions',
    authenticateUser,
    checkFeatureEntitlement('ai.interaction-timeline'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const {
          limit,
          skip,
          startDate,
          endDate,
          types,
          sources,
          sentiment,
          search
        } = req.query;
        
        const options: any = {};
        
        if (limit) options.limit = parseInt(limit as string);
        if (skip) options.skip = parseInt(skip as string);
        if (startDate) options.startDate = new Date(startDate as string);
        if (endDate) options.endDate = new Date(endDate as string);
        if (types) options.types = (types as string).split(',');
        if (sources) options.sources = (sources as string).split(',');
        if (sentiment) options.sentiment = sentiment as 'positive' | 'neutral' | 'negative';
        if (search) options.search = search as string;
        
        const interactions = await interactionTimelineService.getInteractions(id, options);
        
        res.json(interactions);
      } catch (error) {
        console.error(`Error getting interactions for contact ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get interactions' });
      }
    }
  );
  
  // Add an interaction to a contact's timeline
  app.post('/api/contacts/:id/interactions',
    authenticateUser,
    checkFeatureEntitlement('ai.interaction-timeline'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const {
          type,
          source,
          sourceId,
          timestamp,
          summary,
          sentiment,
          direction,
          participants,
          content,
          metadata,
          nextAction,
          aiGenerated,
          aiConfidence
        } = req.body;
        
        if (!type || !source || !timestamp || !summary) {
          return res.status(400).json({ message: 'Type, source, timestamp, and summary are required' });
        }
        
        const interaction = await interactionTimelineService.addInteraction(id, {
          type,
          source,
          sourceId,
          timestamp: new Date(timestamp),
          summary,
          sentiment,
          direction,
          participants,
          content,
          metadata,
          nextAction,
          aiGenerated: aiGenerated !== undefined ? aiGenerated : false,
          aiConfidence
        });
        
        res.status(201).json(interaction);
      } catch (error) {
        console.error(`Error adding interaction for contact ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to add interaction' });
      }
    }
  );
  
  // Update an interaction in a contact's timeline
  app.put('/api/contacts/:contactId/interactions/:interactionId',
    authenticateUser,
    checkFeatureEntitlement('ai.interaction-timeline'),
    async (req: Request, res: Response) => {
      try {
        const { contactId, interactionId } = req.params;
        const updates = req.body;
        
        const interaction = await interactionTimelineService.updateInteraction(
          contactId,
          interactionId,
          updates
        );
        
        if (!interaction) {
          return res.status(404).json({ message: 'Interaction not found' });
        }
        
        res.json(interaction);
      } catch (error) {
        console.error(`Error updating interaction ${req.params.interactionId} for contact ${req.params.contactId}:`, error);
        res.status(500).json({ message: 'Failed to update interaction' });
      }
    }
  );
  
  // Delete an interaction from a contact's timeline
  app.delete('/api/contacts/:contactId/interactions/:interactionId',
    authenticateUser,
    checkFeatureEntitlement('ai.interaction-timeline'),
    async (req: Request, res: Response) => {
      try {
        const { contactId, interactionId } = req.params;
        
        const success = await interactionTimelineService.deleteInteraction(
          contactId,
          interactionId
        );
        
        if (!success) {
          return res.status(404).json({ message: 'Interaction not found' });
        }
        
        res.status(204).send();
      } catch (error) {
        console.error(`Error deleting interaction ${req.params.interactionId} for contact ${req.params.contactId}:`, error);
        res.status(500).json({ message: 'Failed to delete interaction' });
      }
    }
  );
  
  // Sync interactions from MCP sources
  app.post('/api/contacts/:id/interactions/sync',
    authenticateUser,
    checkFeatureEntitlement('ai.interaction-timeline'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const {
          sources,
          startDate,
          endDate,
          limit
        } = req.body;
        
        const options: any = {};
        
        if (sources) {
          options.sources = sources.map((s: string) => {
            switch (s) {
              case 'email':
                return MCPSourceType.EMAIL;
              case 'calendar':
                return MCPSourceType.CALENDAR;
              case 'telephony':
                return MCPSourceType.TELEPHONY;
              case 'social':
                return MCPSourceType.SOCIAL;
              default:
                return s;
            }
          });
        }
        
        if (startDate) options.startDate = new Date(startDate);
        if (endDate) options.endDate = new Date(endDate);
        if (limit) options.limit = parseInt(limit);
        
        const result = await interactionTimelineService.syncInteractions(id, options);
        
        res.json(result);
      } catch (error) {
        console.error(`Error syncing interactions for contact ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to sync interactions' });
      }
    }
  );
}
