import { Router, Request, Response } from 'express';
import mongoose from 'mongoose';
import { z } from 'zod';
import { Contact } from '../models/mongoose';
import { validateRequest, validateQuery } from '../utils/validation';
import { createContactRequestSchema, updateContactRequestSchema } from '../schemas/api';
import { isContact, isContactArray } from '../@types/guards';
import { Contact as ContactType } from '../@types/core';
import { CreateContactRequest, UpdateContactRequest } from '../@types/api';

const router = Router();

/**
 * GET /api/contacts
 * Get all contacts with pagination and filtering
 */
router.get('/', validateQuery(
  z.object({
    page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
    pageSize: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
    status: z.string().optional(),
    search: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  })
), async (req: Request, res: Response) => {
  try {
    const { page, pageSize, status, search, sortBy, sortOrder } = req.validatedQuery;
    const userId = req.session.userId;

    // Build query
    let query: any = { owner: userId };

    if (status) {
      query.status = status;
    }

    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    // Build sort options
    const sort: any = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by createdAt desc
    }

    // Execute query with pagination
    const skip = (page - 1) * pageSize;
    const contacts = await Contact.find(query)
      .sort(sort)
      .skip(skip)
      .limit(pageSize)
      .lean();

    // Get total count for pagination
    const total = await Contact.countDocuments(query);

    // Map MongoDB documents to our type system
    const mappedContacts = contacts.map(contact => ({
      id: contact._id.toString(),
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email,
      phone: contact.phone,
      title: contact.title,
      status: contact.status,
      notes: contact.notes,
      source: contact.source,
      companyId: contact.companyId?.toString(),
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
      createdBy: contact.owner.toString(),
    }));

    // Return paginated response
    res.json({
      success: true,
      contacts: mappedContacts,
      total,
      page,
      pageSize,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch contacts',
    });
  }
});

/**
 * GET /api/contacts/:id
 * Get a single contact by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;

    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid contact ID format"
      });
    }

    const contact = await Contact.findOne({ _id: id, owner: userId }).lean();

    if (!contact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found',
      });
    }

    // Map MongoDB document to our type system
    const mappedContact: ContactType = {
      id: contact._id.toString(),
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email,
      phone: contact.phone,
      title: contact.title,
      status: contact.status,
      notes: contact.notes,
      source: contact.source,
      companyId: contact.companyId?.toString(),
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
      createdBy: contact.owner.toString(),
    };

    res.json({
      success: true,
      contact: mappedContact,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch contact',
    });
  }
});

/**
 * POST /api/contacts
 * Create a new contact
 */
router.post('/', validateRequest(createContactRequestSchema), async (req: Request, res: Response) => {
  try {
    const contactData: CreateContactRequest = req.validatedBody;
    const userId = req.session.userId;

    // Create contact
    const contact = new Contact({
      ...contactData,
      owner: userId
    });

    await contact.save();

    // Map MongoDB document to our type system
    const mappedContact: ContactType = {
      id: contact._id.toString(),
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email,
      phone: contact.phone,
      title: contact.title,
      status: contact.status,
      notes: contact.notes,
      source: contact.source,
      companyId: contact.companyId?.toString(),
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
      createdBy: contact.owner.toString(),
    };

    res.status(201).json({
      success: true,
      contact: mappedContact,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create contact',
    });
  }
});

/**
 * PATCH /api/contacts/:id
 * Update an existing contact
 */
router.patch('/:id', validateRequest(updateContactRequestSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const contactData: UpdateContactRequest = req.validatedBody;
    const userId = req.session.userId;

    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid contact ID format"
      });
    }

    // Update contact
    const contact = await Contact.findOneAndUpdate(
      { _id: id, owner: userId },
      { $set: contactData },
      { new: true }
    ).lean();

    if (!contact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found',
      });
    }

    // Map MongoDB document to our type system
    const mappedContact: ContactType = {
      id: contact._id.toString(),
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email,
      phone: contact.phone,
      title: contact.title,
      status: contact.status,
      notes: contact.notes,
      source: contact.source,
      companyId: contact.companyId?.toString(),
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
      createdBy: contact.owner.toString(),
    };

    res.json({
      success: true,
      contact: mappedContact,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update contact',
    });
  }
});

/**
 * DELETE /api/contacts/:id
 * Delete a contact
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;

    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid contact ID format"
      });
    }

    const result = await Contact.deleteOne({ _id: id, owner: userId });

    if (result.deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found',
      });
    }

    res.json({
      success: true,
      message: 'Contact deleted successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete contact',
    });
  }
});

export default router;
