import { Express, Request, Response } from 'express';
import { Proposal, DocumentModel } from '../models/mongoose';
import { ProposalAnalyticsService } from '../services/proposal-analytics-service';

const analyticsService = new ProposalAnalyticsService();

export function registerPublicProposalRoutes(app: Express) {
  // Get public proposal by token
  app.get('/api/public/proposals/:token',
    async (req: Request, res: Response) => {
      try {
        const { token } = req.params;

        if (!token) {
          return res.status(400).json({ message: 'Token is required' });
        }

        // Find the proposal with the given token
        const proposal = await Proposal.findOne({
          'customFields.shareableLinks': {
            $elemMatch: {
              token,
              expiresAt: { $gt: new Date() } // Check if not expired
            }
          }
        });

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found or link has expired' });
        }

        // Find the share link details
        const shareLink = proposal.customFields?.shareableLinks?.find(
          (link: any) => link.token === token && new Date(link.expiresAt) > new Date()
        );

        if (!shareLink) {
          return res.status(404).json({ message: 'Share link not found or has expired' });
        }

        // Get the document format from the share link
        const format = shareLink.format || 'claude-html';

        // Get the document
        const document = await DocumentModel.findById(proposal.documentId);

        if (!document) {
          return res.status(404).json({ message: 'Document not found' });
        }

        // Track the view
        await analyticsService.trackView(proposal._id.toString(), req, token);

        // Update the proposal status if it's not already viewed or accepted
        if (proposal.status === 'sent') {
          proposal.status = 'viewed';
          proposal.viewedAt = new Date();
          await proposal.save();
        }

        // Return the proposal data
        const publicProposal = {
          id: proposal._id,
          name: proposal.name,
          description: proposal.description,
          documentUrl: document.url,
          format,
          expiresAt: shareLink.expiresAt,
          isDownloadable: ['pdf', 'docx', 'markdown'].includes(format)
        };

        return res.status(200).json(publicProposal);
      } catch (error: any) {
        console.error('Error getting public proposal:', error);
        return res.status(500).json({ message: error.message || 'Failed to get public proposal' });
      }
    }
  );

  // Get public proposal document content
  app.get('/api/public/proposals/:token/content',
    async (req: Request, res: Response) => {
      try {
        const { token } = req.params;

        if (!token) {
          return res.status(400).json({ message: 'Token is required' });
        }

        // Find the proposal with the given token
        const proposal = await Proposal.findOne({
          'customFields.shareableLinks': {
            $elemMatch: {
              token,
              expiresAt: { $gt: new Date() } // Check if not expired
            }
          }
        });

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found or link has expired' });
        }

        // Find the share link details
        const shareLink = proposal.customFields?.shareableLinks?.find(
          (link: any) => link.token === token && new Date(link.expiresAt) > new Date()
        );

        if (!shareLink) {
          return res.status(404).json({ message: 'Share link not found or has expired' });
        }

        // Get the document format from the share link
        const format = shareLink.format || 'claude-html';

        // Get the document
        const document = await DocumentModel.findById(proposal.documentId);

        if (!document) {
          return res.status(404).json({ message: 'Document not found' });
        }

        // Track the view
        await analyticsService.trackView(proposal._id.toString(), req, token);

        // Get the file content
        const fs = require('fs');
        const path = require('path');
        const filePath = path.join(process.cwd(), 'uploads', `proposal_${proposal._id}.${format === 'markdown' ? 'md' : format === 'claude-html' ? 'html' : format}`);

        if (!fs.existsSync(filePath)) {
          return res.status(404).json({ message: 'Document content not found' });
        }

        const fileContent = fs.readFileSync(filePath);

        // Set the appropriate content type
        let contentType = 'text/html';
        if (format === 'pdf') {
          contentType = 'application/pdf';
        } else if (format === 'docx') {
          contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        } else if (format === 'markdown') {
          contentType = 'text/markdown';
        }

        res.setHeader('Content-Type', contentType);
        
        // For downloadable formats, set the content disposition
        if (['pdf', 'docx', 'markdown'].includes(format)) {
          res.setHeader('Content-Disposition', `attachment; filename="proposal_${proposal.name}.${format === 'markdown' ? 'md' : format}"`);
          
          // Track the download
          await analyticsService.trackDownload(proposal._id.toString(), format, req, token);
        }

        return res.send(fileContent);
      } catch (error: any) {
        console.error('Error getting public proposal content:', error);
        return res.status(500).json({ message: error.message || 'Failed to get public proposal content' });
      }
    }
  );

  // Track proposal interaction
  app.post('/api/public/proposals/:token/track',
    async (req: Request, res: Response) => {
      try {
        const { token } = req.params;
        const { type, data, duration } = req.body;

        if (!token) {
          return res.status(400).json({ message: 'Token is required' });
        }

        if (!type) {
          return res.status(400).json({ message: 'Interaction type is required' });
        }

        // Find the proposal with the given token
        const proposal = await Proposal.findOne({
          'customFields.shareableLinks': {
            $elemMatch: {
              token,
              expiresAt: { $gt: new Date() } // Check if not expired
            }
          }
        });

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found or link has expired' });
        }

        // Track the interaction based on type
        if (type === 'view_duration' && duration) {
          await analyticsService.updateViewDuration(proposal._id.toString(), duration, req, token);
        } else {
          await analyticsService.trackInteraction(proposal._id.toString(), type, data || {}, req, token);
        }

        return res.status(200).json({ message: 'Interaction tracked successfully' });
      } catch (error: any) {
        console.error('Error tracking proposal interaction:', error);
        return res.status(500).json({ message: error.message || 'Failed to track proposal interaction' });
      }
    }
  );
}
