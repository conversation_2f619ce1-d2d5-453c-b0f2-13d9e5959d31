import { Router, Request, Response } from 'express';
import { workflowService } from '../services/workflow-service';
import { nlParserService } from '../services/nl-parser-service';
import { workflowCompilerService } from '../services/workflow-compiler-service';
import { authenticateUser } from '../middleware/auth-middleware';
import { checkFeatureEntitlement } from '../middleware/subscription-middleware';
import { validateRequest } from '../middleware/validation-middleware';
import { logger } from '../utils/logger';

const router = Router();

/**
 * @route   POST /api/workflows
 * @desc    Create a new workflow
 * @access  Private
 */
router.post(
  '/api/workflows',
  authenticateUser,
  checkFeatureEntitlement('workflow.create'),
  validateRequest({
    body: {
      name: { type: 'string', required: true },
      description: { type: 'string', required: false },
      dsl_yaml: { type: 'string', required: false },
      original_prompt: { type: 'string', required: false },
      tags: { type: 'array', required: false }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { name, description, dsl_yaml, original_prompt, tags } = req.body;
      const tenant_id = req.user.tenant_id;
      const created_by = req.user.id;
      
      const workflow = await workflowService.createWorkflow({
        name,
        description,
        dsl_yaml,
        original_prompt,
        tenant_id,
        created_by,
        tags
      });
      
      res.status(201).json(workflow);
    } catch (error) {
      logger.error(`Error creating workflow: ${error}`);
      res.status(500).json({ error: `Failed to create workflow: ${error.message}` });
    }
  }
);

/**
 * @route   GET /api/workflows
 * @desc    List workflows
 * @access  Private
 */
router.get(
  '/api/workflows',
  authenticateUser,
  checkFeatureEntitlement('workflow.read'),
  async (req: Request, res: Response) => {
    try {
      const tenant_id = req.user.tenant_id;
      const status = req.query.status as any;
      const tags = req.query.tags ? (req.query.tags as string).split(',') : undefined;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
      const skip = req.query.skip ? parseInt(req.query.skip as string) : 0;
      
      const result = await workflowService.listWorkflows(tenant_id, {
        status,
        tags,
        limit,
        skip
      });
      
      res.json(result);
    } catch (error) {
      logger.error(`Error listing workflows: ${error}`);
      res.status(500).json({ error: `Failed to list workflows: ${error.message}` });
    }
  }
);

/**
 * @route   GET /api/workflows/:id
 * @desc    Get workflow by ID
 * @access  Private
 */
router.get(
  '/api/workflows/:id',
  authenticateUser,
  checkFeatureEntitlement('workflow.read'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenant_id = req.user.tenant_id;
      
      const workflow = await workflowService.getWorkflow(id, tenant_id);
      
      res.json(workflow);
    } catch (error) {
      logger.error(`Error getting workflow: ${error}`);
      res.status(404).json({ error: `Failed to get workflow: ${error.message}` });
    }
  }
);

/**
 * @route   PUT /api/workflows/:id
 * @desc    Update workflow
 * @access  Private
 */
router.put(
  '/api/workflows/:id',
  authenticateUser,
  checkFeatureEntitlement('workflow.update'),
  validateRequest({
    body: {
      name: { type: 'string', required: false },
      description: { type: 'string', required: false },
      dsl_yaml: { type: 'string', required: false },
      nodes: { type: 'array', required: false },
      edges: { type: 'array', required: false },
      status: { type: 'string', required: false },
      tags: { type: 'array', required: false }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenant_id = req.user.tenant_id;
      const updated_by = req.user.id;
      
      const workflow = await workflowService.updateWorkflow(id, tenant_id, {
        ...req.body,
        updated_by
      });
      
      res.json(workflow);
    } catch (error) {
      logger.error(`Error updating workflow: ${error}`);
      res.status(500).json({ error: `Failed to update workflow: ${error.message}` });
    }
  }
);

/**
 * @route   DELETE /api/workflows/:id
 * @desc    Delete workflow
 * @access  Private
 */
router.delete(
  '/api/workflows/:id',
  authenticateUser,
  checkFeatureEntitlement('workflow.delete'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenant_id = req.user.tenant_id;
      
      await workflowService.deleteWorkflow(id, tenant_id);
      
      res.json({ success: true });
    } catch (error) {
      logger.error(`Error deleting workflow: ${error}`);
      res.status(500).json({ error: `Failed to delete workflow: ${error.message}` });
    }
  }
);

/**
 * @route   GET /api/workflows/:id/versions
 * @desc    Get workflow versions
 * @access  Private
 */
router.get(
  '/api/workflows/:id/versions',
  authenticateUser,
  checkFeatureEntitlement('workflow.read'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenant_id = req.user.tenant_id;
      
      const versions = await workflowService.getWorkflowVersions(id, tenant_id);
      
      res.json(versions);
    } catch (error) {
      logger.error(`Error getting workflow versions: ${error}`);
      res.status(500).json({ error: `Failed to get workflow versions: ${error.message}` });
    }
  }
);

/**
 * @route   POST /api/workflows/:id/simulate
 * @desc    Simulate workflow execution
 * @access  Private
 */
router.post(
  '/api/workflows/:id/simulate',
  authenticateUser,
  checkFeatureEntitlement('workflow.simulate'),
  validateRequest({
    body: {
      trigger_event: {
        type: 'object',
        required: true,
        properties: {
          type: { type: 'string', required: true },
          data: { type: 'object', required: true }
        }
      }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { trigger_event } = req.body;
      const tenant_id = req.user.tenant_id;
      const user_id = req.user.id;
      
      const run = await workflowService.simulateWorkflow({
        workflow_id: id,
        trigger_event,
        tenant_id,
        user_id
      });
      
      res.json(run);
    } catch (error) {
      logger.error(`Error simulating workflow: ${error}`);
      res.status(500).json({ error: `Failed to simulate workflow: ${error.message}` });
    }
  }
);

/**
 * @route   POST /api/workflows/:id/execute
 * @desc    Execute workflow
 * @access  Private
 */
router.post(
  '/api/workflows/:id/execute',
  authenticateUser,
  checkFeatureEntitlement('workflow.execute'),
  validateRequest({
    body: {
      trigger_event: {
        type: 'object',
        required: true,
        properties: {
          type: { type: 'string', required: true },
          data: { type: 'object', required: true }
        }
      }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { trigger_event } = req.body;
      const tenant_id = req.user.tenant_id;
      const user_id = req.user.id;
      
      const run = await workflowService.executeWorkflow({
        workflow_id: id,
        trigger_event,
        tenant_id,
        user_id
      });
      
      res.json(run);
    } catch (error) {
      logger.error(`Error executing workflow: ${error}`);
      res.status(500).json({ error: `Failed to execute workflow: ${error.message}` });
    }
  }
);

/**
 * @route   GET /api/workflows/:id/runs
 * @desc    Get workflow runs
 * @access  Private
 */
router.get(
  '/api/workflows/:id/runs',
  authenticateUser,
  checkFeatureEntitlement('workflow.read'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenant_id = req.user.tenant_id;
      const status = req.query.status as any;
      const is_simulation = req.query.is_simulation === 'true';
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
      const skip = req.query.skip ? parseInt(req.query.skip as string) : 0;
      
      const result = await workflowService.getWorkflowRuns(id, tenant_id, {
        status,
        is_simulation,
        limit,
        skip
      });
      
      res.json(result);
    } catch (error) {
      logger.error(`Error getting workflow runs: ${error}`);
      res.status(500).json({ error: `Failed to get workflow runs: ${error.message}` });
    }
  }
);

/**
 * @route   POST /api/workflows/parse
 * @desc    Parse natural language to workflow DSL
 * @access  Private
 */
router.post(
  '/api/workflows/parse',
  authenticateUser,
  checkFeatureEntitlement('workflow.nl_parser'),
  validateRequest({
    body: {
      prompt: { type: 'string', required: true }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { prompt } = req.body;
      const tenant_id = req.user.tenant_id;
      const user_id = req.user.id;
      
      const result = await nlParserService.parsePrompt({
        prompt,
        tenant_id,
        user_id
      });
      
      res.json(result);
    } catch (error) {
      logger.error(`Error parsing workflow: ${error}`);
      res.status(500).json({ error: `Failed to parse workflow: ${error.message}` });
    }
  }
);

/**
 * @route   POST /api/workflows/compile
 * @desc    Compile workflow DSL to graph
 * @access  Private
 */
router.post(
  '/api/workflows/compile',
  authenticateUser,
  checkFeatureEntitlement('workflow.compile'),
  validateRequest({
    body: {
      dsl_yaml: { type: 'string', required: true }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { dsl_yaml } = req.body;
      
      const result = workflowCompilerService.compileWorkflow(dsl_yaml);
      
      res.json(result);
    } catch (error) {
      logger.error(`Error compiling workflow: ${error}`);
      res.status(500).json({ error: `Failed to compile workflow: ${error.message}` });
    }
  }
);

export default router;
