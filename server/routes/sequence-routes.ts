/**
 * Sequence Routes
 * 
 * This module defines the API routes for sales sequence functionality.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import sequenceService from '../services/sequence-service';
import { Sequence, SequenceStep, SequenceEnrollment } from '../models/mongoose';
import { logger } from '../utils/logger';

export function registerSequenceRoutes(app: Express) {
  // Get all sequences for a user
  app.get('/api/sequences',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { isTemplate, isActive, page = '1', limit = '20' } = req.query;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Build query
        const query: any = { userId: new mongoose.Types.ObjectId(userId.toString()) };

        if (isTemplate !== undefined) {
          query.isTemplate = isTemplate === 'true';
        }

        if (isActive !== undefined) {
          query.isActive = isActive === 'true';
        }

        // Pagination
        const pageNum = parseInt(page as string);
        const limitNum = parseInt(limit as string);
        const skip = (pageNum - 1) * limitNum;

        // Get sequences
        const sequences = await Sequence.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum);

        // Get total count
        const totalCount = await Sequence.countDocuments(query);

        res.status(200).json({
          data: sequences,
          pagination: {
            page: pageNum,
            limit: limitNum,
            totalCount,
            totalPages: Math.ceil(totalCount / limitNum)
          }
        });
      } catch (error) {
        logger.error('Error getting sequences:', error);
        res.status(500).json({ message: 'Failed to get sequences' });
      }
    }
  );

  // Get a specific sequence
  app.get('/api/sequences/:id',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get sequence
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Get sequence steps
        const steps = await SequenceStep.find({
          sequenceId: new mongoose.Types.ObjectId(id)
        }).sort({ stepNumber: 1 });

        res.status(200).json({
          ...sequence.toObject(),
          steps
        });
      } catch (error) {
        logger.error(`Error getting sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get sequence' });
      }
    }
  );

  // Create a new sequence
  app.post('/api/sequences',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const {
          name,
          description,
          goal,
          targetConversionDays,
          isTemplate,
          tags,
          customFields
        } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Validate required fields
        if (!name || !goal) {
          return res.status(400).json({ message: 'Name and goal are required' });
        }

        // Create sequence
        const sequence = await sequenceService.createSequence({
          userId: userId.toString(),
          tenantId: req.headers['x-tenant-id'] as string,
          name,
          description,
          goal,
          targetConversionDays,
          isTemplate,
          tags,
          customFields
        });

        res.status(201).json(sequence);
      } catch (error) {
        logger.error('Error creating sequence:', error);
        res.status(500).json({ message: 'Failed to create sequence' });
      }
    }
  );

  // Update a sequence
  app.put('/api/sequences/:id',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;
        const {
          name,
          description,
          goal,
          targetConversionDays,
          isActive,
          isTemplate,
          tags,
          customFields
        } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Update sequence
        if (name !== undefined) sequence.name = name;
        if (description !== undefined) sequence.description = description;
        if (goal !== undefined) sequence.goal = goal;
        if (targetConversionDays !== undefined) sequence.targetConversionDays = targetConversionDays;
        if (isActive !== undefined) sequence.isActive = isActive;
        if (isTemplate !== undefined) sequence.isTemplate = isTemplate;
        if (tags !== undefined) sequence.tags = tags;
        if (customFields !== undefined) sequence.customFields = customFields;

        await sequence.save();
        res.status(200).json(sequence);
      } catch (error) {
        logger.error(`Error updating sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to update sequence' });
      }
    }
  );

  // Delete a sequence
  app.delete('/api/sequences/:id',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Check if there are active enrollments
        const activeEnrollments = await SequenceEnrollment.countDocuments({
          sequenceId: new mongoose.Types.ObjectId(id),
          status: 'active'
        });

        if (activeEnrollments > 0) {
          return res.status(400).json({ 
            message: 'Cannot delete sequence with active enrollments',
            activeEnrollments
          });
        }

        // Delete sequence steps
        await SequenceStep.deleteMany({
          sequenceId: new mongoose.Types.ObjectId(id)
        });

        // Delete sequence
        await Sequence.deleteOne({
          _id: new mongoose.Types.ObjectId(id)
        });

        res.status(200).json({ success: true });
      } catch (error) {
        logger.error(`Error deleting sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to delete sequence' });
      }
    }
  );

  // Add a step to a sequence
  app.post('/api/sequences/:id/steps',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;
        const {
          stepNumber,
          channel,
          templateId,
          content,
          subject,
          timing,
          aiDynamic,
          conditions,
          metadata
        } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Validate required fields
        if (!stepNumber || !channel || !timing) {
          return res.status(400).json({ message: 'Step number, channel, and timing are required' });
        }

        // Check if step number already exists
        const existingStep = await SequenceStep.findOne({
          sequenceId: new mongoose.Types.ObjectId(id),
          stepNumber
        });

        if (existingStep) {
          return res.status(400).json({ message: `Step number ${stepNumber} already exists` });
        }

        // Add step
        const step = await sequenceService.addSequenceStep({
          sequenceId: id,
          stepNumber,
          channel,
          templateId,
          content,
          subject,
          timing,
          aiDynamic,
          conditions,
          metadata
        });

        res.status(201).json(step);
      } catch (error) {
        logger.error(`Error adding step to sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to add step to sequence' });
      }
    }
  );

  // Update a sequence step
  app.put('/api/sequences/:id/steps/:stepId',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id, stepId } = req.params;
        const {
          stepNumber,
          channel,
          templateId,
          content,
          subject,
          timing,
          aiDynamic,
          conditions,
          metadata
        } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Check if step exists
        const step = await SequenceStep.findOne({
          _id: new mongoose.Types.ObjectId(stepId),
          sequenceId: new mongoose.Types.ObjectId(id)
        });

        if (!step) {
          return res.status(404).json({ message: 'Sequence step not found' });
        }

        // If step number is changing, check if new number already exists
        if (stepNumber !== undefined && stepNumber !== step.stepNumber) {
          const existingStep = await SequenceStep.findOne({
            sequenceId: new mongoose.Types.ObjectId(id),
            stepNumber
          });

          if (existingStep && !existingStep._id.equals(step._id)) {
            return res.status(400).json({ message: `Step number ${stepNumber} already exists` });
          }
        }

        // Update step
        if (stepNumber !== undefined) step.stepNumber = stepNumber;
        if (channel !== undefined) step.channel = channel;
        if (templateId !== undefined) step.templateId = templateId ? new mongoose.Types.ObjectId(templateId) : undefined;
        if (content !== undefined) step.content = content;
        if (subject !== undefined) step.subject = subject;
        if (timing !== undefined) step.timing = timing;
        if (aiDynamic !== undefined) step.aiDynamic = aiDynamic;
        if (conditions !== undefined) step.conditions = conditions;
        if (metadata !== undefined) step.metadata = metadata;

        await step.save();
        res.status(200).json(step);
      } catch (error) {
        logger.error(`Error updating step ${req.params.stepId} in sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to update sequence step' });
      }
    }
  );

  // Delete a sequence step
  app.delete('/api/sequences/:id/steps/:stepId',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id, stepId } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Delete step
        const result = await SequenceStep.deleteOne({
          _id: new mongoose.Types.ObjectId(stepId),
          sequenceId: new mongoose.Types.ObjectId(id)
        });

        if (result.deletedCount === 0) {
          return res.status(404).json({ message: 'Sequence step not found' });
        }

        res.status(200).json({ success: true });
      } catch (error) {
        logger.error(`Error deleting step ${req.params.stepId} from sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to delete sequence step' });
      }
    }
  );

  // Enroll a contact in a sequence
  app.post('/api/sequences/:id/enroll',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;
        const { contactId, startDate, customFields } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Validate required fields
        if (!contactId) {
          return res.status(400).json({ message: 'Contact ID is required' });
        }

        // Enroll contact
        const enrollment = await sequenceService.enrollContact({
          sequenceId: id,
          contactId,
          userId: userId.toString(),
          tenantId: req.headers['x-tenant-id'] as string,
          startDate: startDate ? new Date(startDate) : undefined,
          customFields
        });

        res.status(201).json(enrollment);
      } catch (error) {
        logger.error(`Error enrolling contact in sequence ${req.params.id}:`, error);
        
        // Check for specific error messages
        if (error instanceof Error && error.message.includes('already enrolled')) {
          return res.status(400).json({ message: error.message });
        }
        
        res.status(500).json({ message: 'Failed to enroll contact in sequence' });
      }
    }
  );

  // Get enrollments for a sequence
  app.get('/api/sequences/:id/enrollments',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;
        const { status, page = '1', limit = '20' } = req.query;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Build query
        const query: any = { 
          sequenceId: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        };

        if (status) {
          query.status = status;
        }

        // Pagination
        const pageNum = parseInt(page as string);
        const limitNum = parseInt(limit as string);
        const skip = (pageNum - 1) * limitNum;

        // Get enrollments
        const enrollments = await SequenceEnrollment.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .populate('contactId', 'firstName lastName email');

        // Get total count
        const totalCount = await SequenceEnrollment.countDocuments(query);

        res.status(200).json({
          data: enrollments,
          pagination: {
            page: pageNum,
            limit: limitNum,
            totalCount,
            totalPages: Math.ceil(totalCount / limitNum)
          }
        });
      } catch (error) {
        logger.error(`Error getting enrollments for sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get sequence enrollments' });
      }
    }
  );

  // Update a sequence enrollment
  app.put('/api/sequences/:id/enrollments/:enrollmentId',
    authenticateUser,
    checkFeatureEntitlement('sales.sequences'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id, enrollmentId } = req.params;
        const { status, pauseReason, stopReason, customFields } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Check if sequence exists and belongs to the user
        const sequence = await Sequence.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!sequence) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        // Check if enrollment exists
        const enrollment = await SequenceEnrollment.findOne({
          _id: new mongoose.Types.ObjectId(enrollmentId),
          sequenceId: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!enrollment) {
          return res.status(404).json({ message: 'Enrollment not found' });
        }

        // Update enrollment
        if (status !== undefined) {
          const oldStatus = enrollment.status;
          enrollment.status = status;
          
          // If completing or stopping, set completion date
          if ((status === 'completed' || status === 'stopped') && oldStatus === 'active') {
            enrollment.completionDate = new Date();
            
            // Update sequence stats
            await Sequence.findByIdAndUpdate(id, {
              $inc: {
                'stats.activeEnrolled': -1,
                'stats.completedEnrolled': status === 'completed' ? 1 : 0
              }
            });
          }
          
          // If reactivating
          if (status === 'active' && (oldStatus === 'paused' || oldStatus === 'stopped')) {
            // Update sequence stats
            await Sequence.findByIdAndUpdate(id, {
              $inc: {
                'stats.activeEnrolled': 1
              }
            });
            
            // Schedule next step
            await sequenceService.scheduleNextStep(enrollmentId);
          }
        }
        
        if (pauseReason !== undefined) enrollment.pauseReason = pauseReason;
        if (stopReason !== undefined) enrollment.stopReason = stopReason;
        if (customFields !== undefined) enrollment.customFields = customFields;

        await enrollment.save();
        res.status(200).json(enrollment);
      } catch (error) {
        logger.error(`Error updating enrollment ${req.params.enrollmentId} for sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to update sequence enrollment' });
      }
    }
  );
}

export default registerSequenceRoutes;
