import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { AnalyticsDataService } from '../services/analytics-data-service';
import { authenticateUser, requireAdmin } from '../middleware/auth';
import { checkFeatureEntitlement } from '../middleware/subscription';
import { validateRequest, validateQuery } from '../middleware/validation';
import { trackEventRequestSchema } from '@schemas/analytics-reporting';
import { logger } from '../utils/logger';

const router = Router();

/**
 * POST /api/events/track
 * Track an event
 */
router.post('/track', 
  validateRequest(trackEventRequestSchema),
  async (req: Request, res: Response) => {
    try {
      const requestData = req.validatedBody;
      
      // Add tenant_id from session if not provided
      if (!requestData.tenant_id && req.session?.tenantId) {
        requestData.tenant_id = req.session.tenantId;
      }
      
      // Check if tenant_id is provided
      if (!requestData.tenant_id) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      // Track event
      const eventId = await AnalyticsDataService.trackEvent(requestData);
      
      res.status(201).json({
        success: true,
        event_id: eventId
      });
    } catch (error: any) {
      logger.error('Error in /api/events/track:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * GET /api/events
 * Get events
 */
router.get('/',
  authenticateUser,
  checkFeatureEntitlement('analytics.event_tracking'),
  validateQuery(
    z.object({
      visitor_id: z.string().optional(),
      contact_id: z.string().optional(),
      channel: z.string().optional(),
      event_type: z.string().optional(),
      start_date: z.string().optional().transform(val => val ? new Date(val) : undefined),
      end_date: z.string().optional().transform(val => val ? new Date(val) : undefined),
      limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 100),
      offset: z.string().optional().transform(val => val ? parseInt(val, 10) : 0),
    })
  ),
  async (req: Request, res: Response) => {
    try {
      const {
        visitor_id,
        contact_id,
        channel,
        event_type,
        start_date,
        end_date,
        limit,
        offset,
      } = req.validatedQuery;
      
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      const events = await AnalyticsDataService.getEvents({
        tenant_id: tenantId,
        visitor_id,
        contact_id,
        channel,
        event_type,
        start_date,
        end_date,
        limit,
        offset,
      });
      
      res.json({
        success: true,
        events,
        count: events.length,
        limit,
        offset,
      });
    } catch (error: any) {
      logger.error('Error in /api/events:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * GET /api/events/counts
 * Get event counts
 */
router.get('/counts',
  authenticateUser,
  checkFeatureEntitlement('analytics.event_tracking'),
  validateQuery(
    z.object({
      group_by: z.enum(['channel', 'event_type', 'campaign', 'source', 'medium', 'day', 'week', 'month']),
      start_date: z.string().optional().transform(val => val ? new Date(val) : undefined),
      end_date: z.string().optional().transform(val => val ? new Date(val) : undefined),
    })
  ),
  async (req: Request, res: Response) => {
    try {
      const {
        group_by,
        start_date,
        end_date,
      } = req.validatedQuery;
      
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      const counts = await AnalyticsDataService.getEventCounts({
        tenant_id: tenantId,
        group_by,
        start_date,
        end_date,
      });
      
      res.json({
        success: true,
        counts,
      });
    } catch (error: any) {
      logger.error('Error in /api/events/counts:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * GET /api/events/pixel.gif
 * Tracking pixel endpoint
 */
router.get('/pixel.gif',
  validateQuery(
    z.object({
      tenant_id: z.string().min(1),
      visitor_id: z.string().optional(),
      event_type: z.string().min(1),
      channel: z.enum(['web', 'email', 'ads', 'social', 'voice', 'offline']),
      campaign: z.string().optional(),
      medium: z.string().optional(),
      source: z.string().optional(),
      meta: z.string().optional().transform(val => val ? JSON.parse(val) : {}),
    })
  ),
  async (req: Request, res: Response) => {
    try {
      const {
        tenant_id,
        visitor_id,
        event_type,
        channel,
        campaign,
        medium,
        source,
        meta,
      } = req.validatedQuery;
      
      // Track event
      await AnalyticsDataService.trackEvent({
        tenant_id,
        visitor_id,
        channel,
        campaign,
        medium,
        source,
        event_type,
        meta_json: meta,
      });
      
      // Return a 1x1 transparent GIF
      const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
      res.setHeader('Content-Type', 'image/gif');
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.end(pixel);
    } catch (error: any) {
      logger.error('Error in /api/events/pixel.gif:', error);
      
      // Still return a pixel even if there's an error
      const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
      res.setHeader('Content-Type', 'image/gif');
      res.end(pixel);
    }
  }
);

export default router;
