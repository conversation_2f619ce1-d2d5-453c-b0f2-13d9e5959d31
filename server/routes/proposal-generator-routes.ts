import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { proposalGeneratorService } from '../services/proposal-generator-service';
import { graphRAGService } from '../services/graph-rag-service';
import { authenticateUser } from '../middleware/auth';
import { checkFeatureEntitlement } from '../middleware/subscription';

/**
 * Register proposal generator routes
 */
export function registerProposalGeneratorRoutes(app: Express) {
  // Get all proposal templates
  app.get('/api/proposal-templates',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const filter = {
          category: req.query.category as string | undefined,
          isDefault: req.query.isDefault === 'true' ? true :
                    req.query.isDefault === 'false' ? false : undefined,
          createdBy: req.query.createdBy as string | undefined,
          search: req.query.search as string | undefined
        };

        const templates = await proposalGeneratorService.getAllTemplates(filter);
        return res.status(200).json(templates);
      } catch (error: any) {
        console.error('Error getting proposal templates:', error);
        return res.status(500).json({ message: error.message || 'Failed to get proposal templates' });
      }
    }
  );

  // Get template by ID
  app.get('/api/proposal-templates/:id',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const template = await proposalGeneratorService.getTemplateById(req.params.id);

        if (!template) {
          return res.status(404).json({ message: 'Proposal template not found' });
        }

        return res.status(200).json(template);
      } catch (error: any) {
        console.error(`Error getting proposal template with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to get proposal template' });
      }
    }
  );

  // Create a new proposal template
  app.post('/api/proposal-templates',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const {
          name,
          description,
          category,
          content,
          sections,
          variables,
          tags,
          isDefault,
          customFields
        } = req.body;

        if (!name || !category || !content || !sections) {
          return res.status(400).json({ message: 'Name, category, content, and sections are required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const template = await proposalGeneratorService.createTemplate(
          {
            name,
            description,
            category,
            content,
            sections,
            variables,
            tags,
            isDefault,
            customFields
          },
          userId.toString()
        );

        return res.status(201).json(template);
      } catch (error: any) {
        console.error('Error creating proposal template:', error);
        return res.status(500).json({ message: error.message || 'Failed to create proposal template' });
      }
    }
  );

  // Update a proposal template
  app.put('/api/proposal-templates/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const {
          name,
          description,
          category,
          content,
          sections,
          variables,
          tags,
          isDefault,
          customFields
        } = req.body;

        const template = await proposalGeneratorService.updateTemplate(
          req.params.id,
          {
            name,
            description,
            category,
            content,
            sections,
            variables,
            tags,
            isDefault,
            customFields
          }
        );

        if (!template) {
          return res.status(404).json({ message: 'Proposal template not found' });
        }

        return res.status(200).json(template);
      } catch (error: any) {
        console.error(`Error updating proposal template with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to update proposal template' });
      }
    }
  );

  // Delete a proposal template
  app.delete('/api/proposal-templates/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const success = await proposalGeneratorService.deleteTemplate(req.params.id);

        if (!success) {
          return res.status(404).json({ message: 'Proposal template not found' });
        }

        return res.status(200).json({ message: 'Proposal template deleted successfully' });
      } catch (error: any) {
        console.error(`Error deleting proposal template with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to delete proposal template' });
      }
    }
  );

  // Record template usage
  app.post('/api/proposal-templates/:id/usage',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const { wasSuccessful } = req.body;

        const template = await proposalGeneratorService.recordTemplateUsage(
          req.params.id,
          wasSuccessful
        );

        if (!template) {
          return res.status(404).json({ message: 'Proposal template not found' });
        }

        return res.status(200).json(template);
      } catch (error: any) {
        console.error(`Error recording usage for proposal template with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to record template usage' });
      }
    }
  );

  // Get all proposals
  app.get('/api/proposals',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const filter = {
          opportunityId: req.query.opportunityId as string | undefined,
          contactId: req.query.contactId as string | undefined,
          companyId: req.query.companyId as string | undefined,
          status: req.query.status as string | undefined,
          createdBy: req.query.createdBy as string | undefined,
          search: req.query.search as string | undefined
        };

        const proposals = await proposalGeneratorService.getAllProposals(filter);
        return res.status(200).json(proposals);
      } catch (error: any) {
        console.error('Error getting proposals:', error);
        return res.status(500).json({ message: error.message || 'Failed to get proposals' });
      }
    }
  );

  // Get proposal by ID
  app.get('/api/proposals/:id',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const proposal = await proposalGeneratorService.getProposalById(req.params.id);

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json(proposal);
      } catch (error: any) {
        console.error(`Error getting proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to get proposal' });
      }
    }
  );

  // Create a new proposal
  app.post('/api/proposals',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const {
          name,
          description,
          opportunityId,
          contactId,
          companyId,
          templateId,
          content,
          sections,
          value,
          currency,
          validUntil,
          tags,
          notes,
          customFields
        } = req.body;

        if (!name || !opportunityId || !content || !sections || value === undefined) {
          return res.status(400).json({ message: 'Name, opportunityId, content, sections, and value are required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const proposal = await proposalGeneratorService.createProposal(
          {
            name,
            description,
            opportunityId,
            contactId,
            companyId,
            templateId,
            content,
            sections,
            value,
            currency,
            validUntil,
            tags,
            notes,
            customFields
          },
          userId.toString()
        );

        return res.status(201).json(proposal);
      } catch (error: any) {
        console.error('Error creating proposal:', error);
        return res.status(500).json({ message: error.message || 'Failed to create proposal' });
      }
    }
  );

  // Update a proposal
  app.put('/api/proposals/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const {
          name,
          description,
          content,
          sections,
          status,
          value,
          currency,
          validUntil,
          sentAt,
          viewedAt,
          acceptedAt,
          rejectedAt,
          documentUrl,
          documentId,
          tags,
          notes,
          customFields
        } = req.body;

        const proposal = await proposalGeneratorService.updateProposal(
          req.params.id,
          {
            name,
            description,
            content,
            sections,
            status,
            value,
            currency,
            validUntil,
            sentAt,
            viewedAt,
            acceptedAt,
            rejectedAt,
            documentUrl,
            documentId,
            tags,
            notes,
            customFields
          }
        );

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json(proposal);
      } catch (error: any) {
        console.error(`Error updating proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to update proposal' });
      }
    }
  );

  // Delete a proposal
  app.delete('/api/proposals/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const success = await proposalGeneratorService.deleteProposal(req.params.id);

        if (!success) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json({ message: 'Proposal deleted successfully' });
      } catch (error: any) {
        console.error(`Error deleting proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to delete proposal' });
      }
    }
  );

  // Generate a proposal using AI
  app.post('/api/proposals/generate',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const { opportunityId, templateId, contactId, companyId } = req.body;

        if (!opportunityId) {
          return res.status(400).json({ message: 'OpportunityId is required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const proposal = await proposalGeneratorService.generateProposal(
          {
            opportunityId,
            templateId,
            contactId,
            companyId
          },
          userId.toString()
        );

        return res.status(201).json(proposal);
      } catch (error: any) {
        console.error('Error generating proposal:', error);
        return res.status(500).json({ message: error.message || 'Failed to generate proposal' });
      }
    }
  );

  // Generate a document from a proposal in the specified format
  app.post('/api/proposals/:id/document',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const { format } = req.body;
        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Validate format
        const validFormats = ['html', 'pdf', 'docx', 'markdown', 'claude-html'];
        const documentFormat = validFormats.includes(format) ? format : 'pdf';

        const result = await proposalGeneratorService.generateProposalDocument(
          req.params.id,
          userId.toString(),
          documentFormat as any
        );

        return res.status(200).json({
          documentUrl: result.documentUrl,
          format: result.format,
          isDownloadable: result.isDownloadable
        });
      } catch (error: any) {
        console.error(`Error generating document for proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to generate proposal document' });
      }
    }
  );

  // Send a proposal to the client
  app.post('/api/proposals/:id/send',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const proposal = await proposalGeneratorService.sendProposal(req.params.id);

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json(proposal);
      } catch (error: any) {
        console.error(`Error sending proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to send proposal' });
      }
    }
  );

  // Mark a proposal as viewed
  app.post('/api/proposals/:id/viewed',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const proposal = await proposalGeneratorService.markProposalAsViewed(req.params.id);

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json(proposal);
      } catch (error: any) {
        console.error(`Error marking proposal with ID ${req.params.id} as viewed:`, error);
        return res.status(500).json({ message: error.message || 'Failed to mark proposal as viewed' });
      }
    }
  );

  // Mark a proposal as accepted
  app.post('/api/proposals/:id/accept',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const proposal = await proposalGeneratorService.markProposalAsAccepted(req.params.id);

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json(proposal);
      } catch (error: any) {
        console.error(`Error marking proposal with ID ${req.params.id} as accepted:`, error);
        return res.status(500).json({ message: error.message || 'Failed to mark proposal as accepted' });
      }
    }
  );

  // Mark a proposal as rejected
  app.post('/api/proposals/:id/reject',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const { reason } = req.body;

        const proposal = await proposalGeneratorService.markProposalAsRejected(req.params.id, reason);

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json(proposal);
      } catch (error: any) {
        console.error(`Error marking proposal with ID ${req.params.id} as rejected:`, error);
        return res.status(500).json({ message: error.message || 'Failed to mark proposal as rejected' });
      }
    }
  );

  // Update proposal legal clauses
  app.put('/api/proposals/:id/clauses',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const { clauses } = req.body;

        if (!Array.isArray(clauses)) {
          return res.status(400).json({ message: 'Clauses must be an array of clause IDs' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const proposal = await proposalGeneratorService.updateProposal(
          req.params.id,
          {
            customFields: {
              legalClauses: clauses
            }
          }
        );

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        return res.status(200).json(proposal);
      } catch (error: any) {
        console.error(`Error updating clauses for proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to update proposal clauses' });
      }
    }
  );

  // Get industry-specific clauses for a company
  app.get('/api/companies/:id/clauses',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const companyId = req.params.id;

        if (!companyId) {
          return res.status(400).json({ message: 'Company ID is required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get the user to get the tenant ID
        const user = await mongoose.model('User').findById(userId);
        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }
        const tenantId = user.tenantId?.toString() || 'default';

        // Get industry-specific clauses from GraphRAG
        const clauses = await graphRAGService.getIndustrySpecificClauses(companyId, tenantId);

        return res.status(200).json(clauses);
      } catch (error: any) {
        console.error(`Error getting clauses for company with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to get company clauses' });
      }
    }
  );

  // Create a shareable link for a proposal
  app.post('/api/proposals/:id/share',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const { expiresIn, format } = req.body;

        // Default to 7 days if not specified
        const expirationDays = expiresIn ? parseInt(expiresIn) : 7;

        // Validate format
        const validFormats = ['html', 'pdf', 'docx', 'markdown', 'claude-html'];
        const documentFormat = validFormats.includes(format) ? format : 'claude-html';

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const shareableLink = await proposalGeneratorService.createShareableLink(
          req.params.id,
          userId.toString(),
          {
            expirationDays,
            format: documentFormat as any
          }
        );

        if (!shareableLink) {
          return res.status(404).json({ message: 'Proposal not found or could not create shareable link' });
        }

        return res.status(200).json(shareableLink);
      } catch (error: any) {
        console.error(`Error creating shareable link for proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to create shareable link' });
      }
    }
  );

  // Export proposal to email
  app.post('/api/proposals/:id/export/email',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const { email, format, message } = req.body;

        if (!email) {
          return res.status(400).json({ message: 'Email address is required' });
        }

        // Validate format
        const validFormats = ['pdf', 'docx', 'markdown'];
        const documentFormat = validFormats.includes(format) ? format : 'pdf';

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const result = await proposalGeneratorService.exportProposalToEmail(
          req.params.id,
          userId.toString(),
          {
            email,
            format: documentFormat as any,
            message: message || ''
          }
        );

        if (!result) {
          return res.status(404).json({ message: 'Proposal not found or could not export to email' });
        }

        return res.status(200).json({ message: 'Proposal exported to email successfully' });
      } catch (error: any) {
        console.error(`Error exporting proposal with ID ${req.params.id} to email:`, error);
        return res.status(500).json({ message: error.message || 'Failed to export proposal to email' });
      }
    }
  );

  // Export proposal to social media
  app.post('/api/proposals/:id/export/social',
    authenticateUser,
    checkFeatureEntitlement('ai.proposal-generator'),
    async (req: Request, res: Response) => {
      try {
        const { platform, message } = req.body;

        if (!platform) {
          return res.status(400).json({ message: 'Social media platform is required' });
        }

        // Validate platform
        const validPlatforms = ['linkedin', 'twitter', 'facebook'];
        if (!validPlatforms.includes(platform)) {
          return res.status(400).json({ message: 'Invalid social media platform' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const result = await proposalGeneratorService.exportProposalToSocial(
          req.params.id,
          userId.toString(),
          {
            platform,
            message: message || ''
          }
        );

        if (!result) {
          return res.status(404).json({ message: 'Proposal not found or could not export to social media' });
        }

        return res.status(200).json({ message: `Proposal exported to ${platform} successfully` });
      } catch (error: any) {
        console.error(`Error exporting proposal with ID ${req.params.id} to social media:`, error);
        return res.status(500).json({ message: error.message || 'Failed to export proposal to social media' });
      }
    }
  );
}
