import { Express, Request, Response } from 'express';
import { MongoDocumentStorage } from '../storage/mongo-document-storage';
import multer from 'multer';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkResourceLimit } from '../middleware/mongo-subscription-middleware';

// Configure multer for file uploads
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

/**
 * Register MongoDB-specific document routes
 */
export function registerMongoDocumentRoutes(app: Express): void {
  const documentStorage = new MongoDocumentStorage();

  // Get all documents
  app.get('/api/documents', authenticateUser, async (req, res) => {
    try {
      const limit = parseInt(req.query.limit as string || '100');
      const offset = parseInt(req.query.offset as string || '0');
      
      const documents = await documentStorage.getDocuments(limit, offset);
      
      return res.status(200).json(documents);
    } catch (error: any) {
      console.error('Error getting documents:', error);
      return res.status(500).json({ message: error.message || 'Failed to get documents' });
    }
  });

  // Get documents by entity
  app.get('/api/documents/entity/:entityType/:entityId', authenticateUser, async (req, res) => {
    try {
      const { entityType, entityId } = req.params;
      
      const documents = await documentStorage.getDocumentsByEntity(entityType, entityId);
      
      return res.status(200).json(documents);
    } catch (error: any) {
      console.error('Error getting documents by entity:', error);
      return res.status(500).json({ message: error.message || 'Failed to get documents by entity' });
    }
  });

  // Get document by ID
  app.get('/api/documents/:id', authenticateUser, async (req, res) => {
    try {
      const id = req.params.id;
      
      const document = await documentStorage.getDocument(id);
      
      return res.status(200).json(document);
    } catch (error: any) {
      console.error('Error getting document:', error);
      return res.status(404).json({ message: error.message || 'Document not found' });
    }
  });

  // Upload a new document
  app.post('/api/documents', 
    authenticateUser, 
    checkResourceLimit('documents'),
    upload.single('file'),
    async (req, res) => {
      try {
        if (!req.file) {
          return res.status(400).json({ message: 'No file uploaded' });
        }
        
        const userId = req.session.userId;
        
        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }
        
        const documentData = {
          name: req.body.name || req.file.originalname,
          description: req.body.description || '',
          entityType: req.body.entityType || 'general',
          entityId: req.body.entityId || null,
          contentType: req.file.mimetype,
          originalFilename: req.file.originalname,
          tags: req.body.tags ? req.body.tags.split(',').map((tag: string) => tag.trim()) : [],
          uploadedBy: userId.toString()
        };
        
        const document = await documentStorage.createDocument(documentData, req.file.buffer);
        
        return res.status(201).json(document);
      } catch (error: any) {
        console.error('Error uploading document:', error);
        return res.status(500).json({ message: error.message || 'Failed to upload document' });
      }
    }
  );

  // Update document metadata
  app.patch('/api/documents/:id', authenticateUser, async (req, res) => {
    try {
      const id = req.params.id;
      const documentData = {
        name: req.body.name,
        description: req.body.description,
        tags: req.body.tags ? req.body.tags.split(',').map((tag: string) => tag.trim()) : undefined
      };
      
      const document = await documentStorage.updateDocument(id, documentData);
      
      return res.status(200).json(document);
    } catch (error: any) {
      console.error('Error updating document:', error);
      return res.status(500).json({ message: error.message || 'Failed to update document' });
    }
  });

  // Delete a document
  app.delete('/api/documents/:id', authenticateUser, async (req, res) => {
    try {
      const id = req.params.id;
      
      const success = await documentStorage.deleteDocument(id);
      
      if (!success) {
        return res.status(404).json({ message: 'Document not found' });
      }
      
      return res.status(204).send();
    } catch (error: any) {
      console.error('Error deleting document:', error);
      return res.status(500).json({ message: error.message || 'Failed to delete document' });
    }
  });

  // Download document content
  app.get('/api/documents/:id/content', authenticateUser, async (req, res) => {
    try {
      const id = req.params.id;
      
      const { buffer, contentType, filename } = await documentStorage.getDocumentContent(id);
      
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
      
      return res.send(buffer);
    } catch (error: any) {
      console.error('Error downloading document:', error);
      return res.status(500).json({ message: error.message || 'Failed to download document' });
    }
  });

  // Search documents
  app.get('/api/documents/search/:query', authenticateUser, async (req, res) => {
    try {
      const query = req.params.query;
      
      const documents = await documentStorage.searchDocuments(query);
      
      return res.status(200).json(documents);
    } catch (error: any) {
      console.error('Error searching documents:', error);
      return res.status(500).json({ message: error.message || 'Failed to search documents' });
    }
  });
}
