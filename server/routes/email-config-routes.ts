import { Express, Request, Response } from 'express';
import { authenticateUser } from '../middleware/auth-middleware';
import { checkFeatureEntitlement } from '../middleware/subscription-middleware';
import { EmailService } from '../services/email-service';

const emailService = new EmailService();

export function registerEmailConfigRoutes(app: Express) {
  // Get email configuration
  app.get('/api/email-config',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const config = await emailService.getEmailConfig(userId.toString());

        if (!config) {
          return res.status(404).json({ message: 'Email configuration not found' });
        }

        // Don't return sensitive information
        const safeConfig = {
          ...config.toObject(),
          smtpPassword: config.smtpPassword ? '********' : undefined,
          apiKey: config.apiKey ? '********' : undefined,
          apiSecret: config.apiSecret ? '********' : undefined
        };

        return res.status(200).json(safeConfig);
      } catch (error: any) {
        console.error('Error getting email configuration:', error);
        return res.status(500).json({ message: error.message || 'Failed to get email configuration' });
      }
    }
  );

  // Save email configuration
  app.post('/api/email-config',
    authenticateUser,
    checkFeatureEntitlement('email.configuration'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const {
          provider,
          isEnabled,
          fromName,
          fromEmail,
          replyToEmail,
          smtpHost,
          smtpPort,
          smtpUsername,
          smtpPassword,
          smtpSecure,
          apiKey,
          apiSecret,
          region,
          templates
        } = req.body;

        // Validate required fields
        if (!provider || !fromName || !fromEmail) {
          return res.status(400).json({ message: 'Provider, from name, and from email are required' });
        }

        // Validate provider-specific fields
        if (provider === 'smtp' && (!smtpHost || !smtpPort || !smtpUsername || !smtpPassword)) {
          return res.status(400).json({ message: 'SMTP configuration requires host, port, username, and password' });
        }

        if ((provider === 'sendgrid' || provider === 'mailgun') && !apiKey) {
          return res.status(400).json({ message: `${provider} configuration requires an API key` });
        }

        if (provider === 'ses' && (!apiKey || !apiSecret || !region)) {
          return res.status(400).json({ message: 'AWS SES configuration requires API key, API secret, and region' });
        }

        // Save configuration
        const config = await emailService.saveEmailConfig(userId.toString(), {
          provider,
          isEnabled: isEnabled !== undefined ? isEnabled : true,
          fromName,
          fromEmail,
          replyToEmail,
          smtpHost,
          smtpPort: smtpPort ? parseInt(smtpPort) : undefined,
          smtpUsername,
          smtpPassword,
          smtpSecure: smtpSecure !== undefined ? smtpSecure : true,
          apiKey,
          apiSecret,
          region,
          templates
        });

        if (!config) {
          return res.status(500).json({ message: 'Failed to save email configuration' });
        }

        // Don't return sensitive information
        const safeConfig = {
          ...config.toObject(),
          smtpPassword: config.smtpPassword ? '********' : undefined,
          apiKey: config.apiKey ? '********' : undefined,
          apiSecret: config.apiSecret ? '********' : undefined
        };

        return res.status(200).json(safeConfig);
      } catch (error: any) {
        console.error('Error saving email configuration:', error);
        return res.status(500).json({ message: error.message || 'Failed to save email configuration' });
      }
    }
  );

  // Test email configuration
  app.post('/api/email-config/test',
    authenticateUser,
    checkFeatureEntitlement('email.configuration'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const { testEmail } = req.body;

        if (!testEmail) {
          return res.status(400).json({ message: 'Test email address is required' });
        }

        // Get the user
        const user = await mongoose.model('User').findById(userId);

        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }

        // Send a test email
        const result = await emailService.sendEmail(userId.toString(), {
          to: testEmail,
          subject: 'Aizako CRM Email Configuration Test',
          html: `
            <h1>Email Configuration Test</h1>
            <p>This is a test email from Aizako CRM to verify your email configuration.</p>
            <p>If you received this email, your email configuration is working correctly!</p>
            <p>Sent by: ${user.fullName}</p>
            <p>Date: ${new Date().toLocaleString()}</p>
          `
        });

        if (!result) {
          return res.status(500).json({ message: 'Failed to send test email' });
        }

        return res.status(200).json({ message: 'Test email sent successfully' });
      } catch (error: any) {
        console.error('Error testing email configuration:', error);
        return res.status(500).json({ message: error.message || 'Failed to test email configuration' });
      }
    }
  );
}
