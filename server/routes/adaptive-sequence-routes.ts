/**
 * Adaptive Sequence Routes
 * 
 * This module defines the API routes for adaptive sequences.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import adaptiveSequenceService from '../services/adaptive-sequence-service';
import { logger } from '../utils/logger';

export function registerAdaptiveSequenceRoutes(app: Express) {
  /**
   * Get sequence adaptive configuration
   */
  app.get('/api/sequences/:id/adaptive-config',
    authenticateUser,
    checkFeatureEntitlement('sequence.adaptive'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;

        // Get adaptive configuration
        const config = await adaptiveSequenceService.getSequenceAdaptiveConfig(id);

        if (!config) {
          return res.status(404).json({ message: 'Adaptive configuration not found' });
        }

        res.status(200).json(config);
      } catch (error) {
        logger.error(`Error getting adaptive configuration for sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get adaptive configuration' });
      }
    }
  );

  /**
   * Update sequence adaptive configuration
   */
  app.put('/api/sequences/:id/adaptive-config',
    authenticateUser,
    checkFeatureEntitlement('sequence.adaptive'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const config = req.body;

        // Validate configuration
        if (!config) {
          return res.status(400).json({ message: 'Configuration is required' });
        }

        // Update configuration
        const success = await adaptiveSequenceService.updateSequenceAdaptiveConfig(id, config);

        if (!success) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        res.status(200).json({ success: true });
      } catch (error) {
        logger.error(`Error updating adaptive configuration for sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to update adaptive configuration' });
      }
    }
  );

  /**
   * Create default adaptive rules for a sequence
   */
  app.post('/api/sequences/:id/default-adaptive-rules',
    authenticateUser,
    checkFeatureEntitlement('sequence.adaptive'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;

        // Create default rules
        const success = await adaptiveSequenceService.createDefaultAdaptiveRules(id);

        if (!success) {
          return res.status(404).json({ message: 'Sequence not found' });
        }

        res.status(200).json({ success: true });
      } catch (error) {
        logger.error(`Error creating default adaptive rules for sequence ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to create default adaptive rules' });
      }
    }
  );

  /**
   * Process adaptive rules for a sequence enrollment
   */
  app.post('/api/sequence-enrollments/:id/process-adaptive-rules',
    authenticateUser,
    checkFeatureEntitlement('sequence.adaptive'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;

        // Process rules
        const result = await adaptiveSequenceService.processAdaptiveRules(id);

        res.status(200).json(result);
      } catch (error) {
        logger.error(`Error processing adaptive rules for enrollment ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to process adaptive rules' });
      }
    }
  );
}

export default registerAdaptiveSequenceRoutes;
