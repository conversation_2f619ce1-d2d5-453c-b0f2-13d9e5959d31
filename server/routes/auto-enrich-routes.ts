/**
 * Auto-Enrich Routes
 * 
 * API routes for auto-enrichment operations.
 */

import express from 'express';
import { autoEnrichService } from '../services/auto-enrich-service';
import { authenticateJWT, requireTenantId } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = express.Router();

// Middleware
router.use(authenticateJWT);
router.use(requireTenantId);

/**
 * @route POST /api/auto-enrich/contact/:contactId
 * @desc Enrich a contact with data from various sources
 * @access Private
 */
router.post('/contact/:contactId', async (req, res) => {
  try {
    const { contactId } = req.params;
    const tenantId = req.tenantId as string;
    
    const result = await autoEnrichService.enrichContact(contactId, tenantId);
    
    res.json(result);
  } catch (error: any) {
    logger.error('Error enriching contact:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/auto-enrich/generate-persona/:contactId
 * @desc Generate an AI persona for a contact
 * @access Private
 */
router.post('/generate-persona/:contactId', async (req, res) => {
  try {
    const { contactId } = req.params;
    const tenantId = req.tenantId as string;
    
    const persona = await autoEnrichService.generatePersona(contactId, tenantId);
    
    if (!persona) {
      return res.status(404).json({ error: 'Could not generate persona. Insufficient data.' });
    }
    
    res.json(persona);
  } catch (error: any) {
    logger.error('Error generating persona:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/auto-enrich/batch
 * @desc Batch enrich contacts for a tenant
 * @access Private
 */
router.post('/batch', async (req, res) => {
  try {
    const tenantId = req.tenantId as string;
    const { limit } = req.body;
    
    const result = await autoEnrichService.batchEnrichContacts(tenantId, limit);
    
    res.json(result);
  } catch (error: any) {
    logger.error('Error in batch enrichment:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
