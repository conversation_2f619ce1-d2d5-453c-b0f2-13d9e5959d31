/**
 * Email Analytics Routes
 * 
 * This module defines the API routes for email analytics.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import emailAnalyticsService from '../services/email-analytics-service';
import { logger } from '../utils/logger';

export function registerEmailAnalyticsRoutes(app: Express) {
  /**
   * Get email analytics
   */
  app.get('/api/email-analytics',
    authenticateUser,
    checkFeatureEntitlement('email.analytics'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { 
          startDate, 
          endDate, 
          groupBy, 
          templateIds, 
          userIds, 
          includeABTesting 
        } = req.query;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Validate date range
        if (!startDate || !endDate) {
          return res.status(400).json({ message: 'Start date and end date are required' });
        }

        // Parse options
        const options: any = {
          timeRange: {
            startDate: new Date(startDate as string),
            endDate: new Date(endDate as string)
          }
        };

        // Parse groupBy
        if (groupBy) {
          options.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];
        }

        // Parse templateIds
        if (templateIds) {
          options.templateIds = Array.isArray(templateIds) ? templateIds : [templateIds];
        }

        // Parse userIds
        if (userIds) {
          options.userIds = Array.isArray(userIds) ? userIds : [userIds];
        }

        // Parse includeABTesting
        if (includeABTesting) {
          options.includeABTesting = includeABTesting === 'true';
        }

        // Get analytics
        const analytics = await emailAnalyticsService.getEmailAnalytics(tenantId, options);

        res.status(200).json(analytics);
      } catch (error) {
        logger.error('Error getting email analytics:', error);
        res.status(500).json({ message: 'Failed to get email analytics' });
      }
    }
  );

  /**
   * Get email analytics dashboard data
   */
  app.get('/api/email-analytics/dashboard',
    authenticateUser,
    checkFeatureEntitlement('email.analytics'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { period = '30d' } = req.query;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Calculate date range based on period
        const endDate = new Date();
        let startDate = new Date();

        switch (period) {
          case '7d':
            startDate.setDate(startDate.getDate() - 7);
            break;
          case '30d':
            startDate.setDate(startDate.getDate() - 30);
            break;
          case '90d':
            startDate.setDate(startDate.getDate() - 90);
            break;
          case '1y':
            startDate.setFullYear(startDate.getFullYear() - 1);
            break;
          default:
            startDate.setDate(startDate.getDate() - 30);
        }

        // Get analytics with all groupings
        const analytics = await emailAnalyticsService.getEmailAnalytics(tenantId, {
          timeRange: { startDate, endDate },
          groupBy: ['day', 'template', 'device', 'location'],
          includeABTesting: true
        });

        // Format dashboard data
        const dashboard = {
          summary: {
            sent: analytics.sent,
            opened: analytics.opened,
            clicked: analytics.clicked,
            replied: analytics.replied,
            openRate: analytics.openRate.toFixed(2),
            clickRate: analytics.clickRate.toFixed(2),
            replyRate: analytics.replyRate.toFixed(2)
          },
          trends: analytics.byDay,
          topTemplates: analytics.byTemplate
            ?.sort((a, b) => b.metrics.openRate - a.metrics.openRate)
            .slice(0, 5),
          deviceBreakdown: analytics.byDevice,
          locationBreakdown: analytics.byLocation
            ?.sort((a, b) => b.metrics.sent - a.metrics.sent)
            .slice(0, 10),
          abTestingResults: analytics.abTestingResults
        };

        res.status(200).json(dashboard);
      } catch (error) {
        logger.error('Error getting email analytics dashboard:', error);
        res.status(500).json({ message: 'Failed to get email analytics dashboard' });
      }
    }
  );

  /**
   * Get email template performance comparison
   */
  app.get('/api/email-analytics/template-comparison',
    authenticateUser,
    checkFeatureEntitlement('email.analytics'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { templateIds, startDate, endDate } = req.query;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        if (!templateIds || !Array.isArray(templateIds)) {
          return res.status(400).json({ message: 'Template IDs are required' });
        }

        // Validate date range
        if (!startDate || !endDate) {
          return res.status(400).json({ message: 'Start date and end date are required' });
        }

        // Get analytics for specified templates
        const analytics = await emailAnalyticsService.getEmailAnalytics(tenantId, {
          timeRange: {
            startDate: new Date(startDate as string),
            endDate: new Date(endDate as string)
          },
          templateIds: templateIds as string[],
          groupBy: ['template']
        });

        res.status(200).json(analytics.byTemplate || []);
      } catch (error) {
        logger.error('Error getting template comparison:', error);
        res.status(500).json({ message: 'Failed to get template comparison' });
      }
    }
  );

  /**
   * Get A/B testing results
   */
  app.get('/api/email-analytics/ab-testing/:templateId',
    authenticateUser,
    checkFeatureEntitlement('email.analytics'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { templateId } = req.params;
        const { startDate, endDate } = req.query;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Validate date range
        if (!startDate || !endDate) {
          return res.status(400).json({ message: 'Start date and end date are required' });
        }

        // Get analytics for the specified template with A/B testing results
        const analytics = await emailAnalyticsService.getEmailAnalytics(tenantId, {
          timeRange: {
            startDate: new Date(startDate as string),
            endDate: new Date(endDate as string)
          },
          templateIds: [templateId],
          includeABTesting: true
        });

        // Find the A/B testing results for the specified template
        const abTestingResults = analytics.abTestingResults?.find(
          result => result.templateId === templateId
        );

        if (!abTestingResults) {
          return res.status(404).json({ message: 'A/B testing results not found for this template' });
        }

        res.status(200).json(abTestingResults);
      } catch (error) {
        logger.error(`Error getting A/B testing results for template ${req.params.templateId}:`, error);
        res.status(500).json({ message: 'Failed to get A/B testing results' });
      }
    }
  );
}

export default registerEmailAnalyticsRoutes;
