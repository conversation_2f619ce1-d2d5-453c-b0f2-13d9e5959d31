/**
 * Lead Scoring Routes
 * 
 * API routes for lead scoring operations.
 */

import express from 'express';
import { leadScoringService } from '../services/lead-scoring-service';
import { authenticateJWT, requireTenantId } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = express.Router();

// Middleware
router.use(authenticateJWT);
router.use(requireTenantId);

/**
 * @route GET /api/lead-scoring/score/:contactId
 * @desc Get lead score for a contact
 * @access Private
 */
router.get('/score/:contactId', async (req, res) => {
  try {
    const { contactId } = req.params;
    const tenantId = req.tenantId as string;
    
    const leadScore = await leadScoringService.calculateLeadScore(contactId, tenantId);
    
    res.json(leadScore);
  } catch (error: any) {
    logger.error('Error getting lead score:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/lead-scoring/sync/contact/:contactId
 * @desc Sync a contact to the graph database
 * @access Private
 */
router.post('/sync/contact/:contactId', async (req, res) => {
  try {
    const { contactId } = req.params;
    const tenantId = req.tenantId as string;
    
    await leadScoringService.syncContact(contactId, tenantId);
    
    res.json({ success: true, message: 'Contact synced successfully' });
  } catch (error: any) {
    logger.error('Error syncing contact:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/lead-scoring/sync/interaction/:interactionId
 * @desc Sync an interaction to the graph database
 * @access Private
 */
router.post('/sync/interaction/:interactionId', async (req, res) => {
  try {
    const { interactionId } = req.params;
    const tenantId = req.tenantId as string;
    
    await leadScoringService.syncInteraction(interactionId, tenantId);
    
    res.json({ success: true, message: 'Interaction synced successfully' });
  } catch (error: any) {
    logger.error('Error syncing interaction:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/lead-scoring/sync/opportunity/:opportunityId
 * @desc Sync an opportunity to the graph database
 * @access Private
 */
router.post('/sync/opportunity/:opportunityId', async (req, res) => {
  try {
    const { opportunityId } = req.params;
    const tenantId = req.tenantId as string;
    
    await leadScoringService.syncOpportunity(opportunityId, tenantId);
    
    res.json({ success: true, message: 'Opportunity synced successfully' });
  } catch (error: any) {
    logger.error('Error syncing opportunity:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route GET /api/lead-scoring/similar-contacts/:contactId
 * @desc Get similar contacts for a contact
 * @access Private
 */
router.get('/similar-contacts/:contactId', async (req, res) => {
  try {
    const { contactId } = req.params;
    const tenantId = req.tenantId as string;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 5;
    
    const similarContacts = await leadScoringService.getSimilarContacts(contactId, tenantId, limit);
    
    res.json(similarContacts);
  } catch (error: any) {
    logger.error('Error getting similar contacts:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/lead-scoring/batch-update
 * @desc Batch update lead scores for all contacts in a tenant
 * @access Private
 */
router.post('/batch-update', async (req, res) => {
  try {
    const tenantId = req.tenantId as string;
    
    const result = await leadScoringService.batchUpdateLeadScores(tenantId);
    
    res.json(result);
  } catch (error: any) {
    logger.error('Error in batch update:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route POST /api/lead-scoring/sync-all
 * @desc Sync all data for a tenant to the graph database
 * @access Private
 */
router.post('/sync-all', async (req, res) => {
  try {
    const tenantId = req.tenantId as string;
    
    const result = await leadScoringService.syncAllData(tenantId);
    
    res.json(result);
  } catch (error: any) {
    logger.error('Error syncing all data:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
