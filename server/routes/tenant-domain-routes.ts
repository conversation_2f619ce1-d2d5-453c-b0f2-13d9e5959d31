/**
 * Tenant Domain Routes
 * 
 * This module defines the API routes for tenant domain management.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import tenantDomainService from '../services/tenant-domain-service';
import { TenantDomain } from '../models/mongoose/tenant-domain-model';
import { logger } from '../utils/logger';

export function registerTenantDomainRoutes(app: Express) {
  /**
   * Get all domains for a tenant
   */
  app.get('/api/tenant/domains',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get domains
        const domains = await TenantDomain.find({
          tenantId: new mongoose.Types.ObjectId(tenantId)
        }).sort({ isDefault: -1, domain: 1 });

        res.status(200).json(domains);
      } catch (error) {
        logger.error('Error getting tenant domains:', error);
        res.status(500).json({ message: 'Failed to get tenant domains' });
      }
    }
  );

  /**
   * Get a specific domain
   */
  app.get('/api/tenant/domains/:id',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get domain
        const domain = await TenantDomain.findOne({
          _id: new mongoose.Types.ObjectId(id),
          tenantId: new mongoose.Types.ObjectId(tenantId)
        });

        if (!domain) {
          return res.status(404).json({ message: 'Domain not found' });
        }

        res.status(200).json(domain);
      } catch (error) {
        logger.error(`Error getting tenant domain ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get tenant domain' });
      }
    }
  );

  /**
   * Register a new domain
   */
  app.post('/api/tenant/domains',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const { domain, isDefault } = req.body;
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        if (!domain) {
          return res.status(400).json({ message: 'Domain is required' });
        }

        // Register domain
        const tenantDomain = await tenantDomainService.registerDomain(
          tenantId,
          domain,
          isDefault
        );

        res.status(201).json(tenantDomain);
      } catch (error) {
        logger.error('Error registering tenant domain:', error);
        res.status(500).json({ 
          message: 'Failed to register tenant domain',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  );

  /**
   * Set up tracking domain
   */
  app.post('/api/tenant/domains/:id/tracking',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const { trackingSubdomain } = req.body;
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get domain
        const domain = await TenantDomain.findOne({
          _id: new mongoose.Types.ObjectId(id),
          tenantId: new mongoose.Types.ObjectId(tenantId)
        });

        if (!domain) {
          return res.status(404).json({ message: 'Domain not found' });
        }

        // Set up tracking domain
        const updatedDomain = await tenantDomainService.setupTrackingDomain(
          tenantId,
          domain.domain,
          trackingSubdomain
        );

        res.status(200).json(updatedDomain);
      } catch (error) {
        logger.error(`Error setting up tracking domain for ${req.params.id}:`, error);
        res.status(500).json({ 
          message: 'Failed to set up tracking domain',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  );

  /**
   * Check domain verification status
   */
  app.get('/api/tenant/domains/:id/verification',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get domain
        const domain = await TenantDomain.findOne({
          _id: new mongoose.Types.ObjectId(id),
          tenantId: new mongoose.Types.ObjectId(tenantId)
        });

        if (!domain) {
          return res.status(404).json({ message: 'Domain not found' });
        }

        // Check verification status
        const updatedDomain = await tenantDomainService.checkVerificationStatus(id);

        res.status(200).json(updatedDomain);
      } catch (error) {
        logger.error(`Error checking verification status for domain ${req.params.id}:`, error);
        res.status(500).json({ 
          message: 'Failed to check verification status',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  );

  /**
   * Check DNS records
   */
  app.get('/api/tenant/domains/:id/dns-check',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get domain
        const domain = await TenantDomain.findOne({
          _id: new mongoose.Types.ObjectId(id),
          tenantId: new mongoose.Types.ObjectId(tenantId)
        });

        if (!domain) {
          return res.status(404).json({ message: 'Domain not found' });
        }

        // Check DNS records
        const dnsCheck = await tenantDomainService.checkDNSRecords(id);

        res.status(200).json(dnsCheck);
      } catch (error) {
        logger.error(`Error checking DNS records for domain ${req.params.id}:`, error);
        res.status(500).json({ 
          message: 'Failed to check DNS records',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  );

  /**
   * Rotate webhook secret
   */
  app.post('/api/tenant/domains/:id/rotate-secret',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get domain
        const domain = await TenantDomain.findOne({
          _id: new mongoose.Types.ObjectId(id),
          tenantId: new mongoose.Types.ObjectId(tenantId)
        });

        if (!domain) {
          return res.status(404).json({ message: 'Domain not found' });
        }

        // Rotate webhook secret
        const updatedDomain = await tenantDomainService.rotateWebhookSecret(id);

        res.status(200).json({
          success: true,
          webhookSecret: updatedDomain.webhookSecret
        });
      } catch (error) {
        logger.error(`Error rotating webhook secret for domain ${req.params.id}:`, error);
        res.status(500).json({ 
          message: 'Failed to rotate webhook secret',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  );

  /**
   * Delete a domain
   */
  app.delete('/api/tenant/domains/:id',
    authenticateUser,
    checkFeatureEntitlement('email.tracking'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get domain
        const domain = await TenantDomain.findOne({
          _id: new mongoose.Types.ObjectId(id),
          tenantId: new mongoose.Types.ObjectId(tenantId)
        });

        if (!domain) {
          return res.status(404).json({ message: 'Domain not found' });
        }

        // Check if it's the default domain
        if (domain.isDefault) {
          return res.status(400).json({ 
            message: 'Cannot delete the default domain. Set another domain as default first.' 
          });
        }

        // Delete domain from Resend
        // Note: This is commented out because Resend doesn't currently support domain deletion via API
        // const result = await resendService.deleteDomain(domain.resendDomainId!);

        // Delete domain from database
        await TenantDomain.deleteOne({ _id: new mongoose.Types.ObjectId(id) });

        res.status(200).json({ success: true });
      } catch (error) {
        logger.error(`Error deleting domain ${req.params.id}:`, error);
        res.status(500).json({ 
          message: 'Failed to delete domain',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  );
}

export default registerTenantDomainRoutes;
