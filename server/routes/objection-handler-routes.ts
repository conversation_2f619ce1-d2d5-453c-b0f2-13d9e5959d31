import { Express, Request, Response } from 'express';
import { objectionHandlerService } from '../services/objection-handler-service';
import { authenticateUser } from '../middleware/auth';
import { checkFeatureEntitlement } from '../middleware/subscription';

/**
 * Register objection handler routes
 */
export function registerObjectionHandlerRoutes(app: Express) {
  // Get all objection categories
  app.get('/api/objections/categories',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const categories = await objectionHandlerService.getObjectionCategories();
        return res.status(200).json(categories);
      } catch (error: any) {
        console.error('Error getting objection categories:', error);
        return res.status(500).json({ message: error.message || 'Failed to get objection categories' });
      }
    }
  );

  // Get all objections
  app.get('/api/objections',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const filter = {
          category: req.query.category as string | undefined,
          isCommon: req.query.isCommon === 'true' ? true :
                    req.query.isCommon === 'false' ? false : undefined,
          createdBy: req.query.createdBy as string | undefined,
          search: req.query.search as string | undefined
        };

        const objections = await objectionHandlerService.getAllObjections(filter);
        return res.status(200).json(objections);
      } catch (error: any) {
        console.error('Error getting objections:', error);
        return res.status(500).json({ message: error.message || 'Failed to get objections' });
      }
    }
  );

  // Get objection by ID
  app.get('/api/objections/:id',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const objection = await objectionHandlerService.getObjectionById(req.params.id);

        if (!objection) {
          return res.status(404).json({ message: 'Objection not found' });
        }

        return res.status(200).json(objection);
      } catch (error: any) {
        console.error(`Error getting objection with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to get objection' });
      }
    }
  );

  // Create a new objection
  app.post('/api/objections',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const { name, category, description, tags, isCommon, customFields } = req.body;

        if (!name || !category || !description) {
          return res.status(400).json({ message: 'Name, category, and description are required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const objection = await objectionHandlerService.createObjection(
          {
            name,
            category,
            description,
            tags,
            isCommon,
            customFields
          },
          userId.toString()
        );

        return res.status(201).json(objection);
      } catch (error: any) {
        console.error('Error creating objection:', error);
        return res.status(500).json({ message: error.message || 'Failed to create objection' });
      }
    }
  );

  // Update an objection
  app.put('/api/objections/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const { name, category, description, tags, isCommon, customFields } = req.body;

        const objection = await objectionHandlerService.updateObjection(
          req.params.id,
          {
            name,
            category,
            description,
            tags,
            isCommon,
            customFields
          }
        );

        if (!objection) {
          return res.status(404).json({ message: 'Objection not found' });
        }

        return res.status(200).json(objection);
      } catch (error: any) {
        console.error(`Error updating objection with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to update objection' });
      }
    }
  );

  // Delete an objection
  app.delete('/api/objections/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const success = await objectionHandlerService.deleteObjection(req.params.id);

        if (!success) {
          return res.status(404).json({ message: 'Objection not found' });
        }

        return res.status(200).json({ message: 'Objection deleted successfully' });
      } catch (error: any) {
        console.error(`Error deleting objection with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to delete objection' });
      }
    }
  );

  // Get responses for an objection
  app.get('/api/objections/:id/responses',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const filter = {
          effectiveness: req.query.effectiveness ? parseInt(req.query.effectiveness as string) : undefined,
          isAIGenerated: req.query.isAIGenerated === 'true' ? true :
                         req.query.isAIGenerated === 'false' ? false : undefined,
          createdBy: req.query.createdBy as string | undefined,
          opportunityId: req.query.opportunityId as string | undefined,
          contactId: req.query.contactId as string | undefined,
          companyId: req.query.companyId as string | undefined
        };

        const responses = await objectionHandlerService.getResponsesForObjection(req.params.id, filter);
        return res.status(200).json(responses);
      } catch (error: any) {
        console.error(`Error getting responses for objection with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to get responses' });
      }
    }
  );

  // Create a response for an objection
  app.post('/api/objections/:id/responses',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const {
          response,
          context,
          effectiveness,
          opportunityId,
          contactId,
          companyId,
          isAIGenerated,
          customFields
        } = req.body;

        if (!response || !context) {
          return res.status(400).json({ message: 'Response and context are required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const objectionResponse = await objectionHandlerService.createResponse(
          {
            objectionId: req.params.id,
            response,
            context,
            effectiveness,
            opportunityId,
            contactId,
            companyId,
            isAIGenerated,
            customFields
          },
          userId.toString()
        );

        return res.status(201).json(objectionResponse);
      } catch (error: any) {
        console.error('Error creating objection response:', error);
        return res.status(500).json({ message: error.message || 'Failed to create objection response' });
      }
    }
  );

  // Update a response
  app.put('/api/objection-responses/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const { response, context, effectiveness, usedCount, successCount, customFields } = req.body;

        const objectionResponse = await objectionHandlerService.updateResponse(
          req.params.id,
          {
            response,
            context,
            effectiveness,
            usedCount,
            successCount,
            customFields
          }
        );

        if (!objectionResponse) {
          return res.status(404).json({ message: 'Objection response not found' });
        }

        return res.status(200).json(objectionResponse);
      } catch (error: any) {
        console.error(`Error updating objection response with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to update objection response' });
      }
    }
  );

  // Delete a response
  app.delete('/api/objection-responses/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const success = await objectionHandlerService.deleteResponse(req.params.id);

        if (!success) {
          return res.status(404).json({ message: 'Objection response not found' });
        }

        return res.status(200).json({ message: 'Objection response deleted successfully' });
      } catch (error: any) {
        console.error(`Error deleting objection response with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to delete objection response' });
      }
    }
  );

  // Record response usage
  app.post('/api/objection-responses/:id/usage',
    authenticateUser,
    async (req: Request, res: Response) => {
      try {
        const { wasSuccessful } = req.body;

        const objectionResponse = await objectionHandlerService.recordResponseUsage(
          req.params.id,
          wasSuccessful
        );

        if (!objectionResponse) {
          return res.status(404).json({ message: 'Objection response not found' });
        }

        return res.status(200).json(objectionResponse);
      } catch (error: any) {
        console.error(`Error recording usage for objection response with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to record response usage' });
      }
    }
  );

  // Generate AI response for an objection
  app.post('/api/objections/:id/generate-response',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const { opportunityId, contactId, companyId } = req.body;

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const objectionResponse = await objectionHandlerService.generateAIResponse(
          {
            objectionId: req.params.id,
            opportunityId,
            contactId,
            companyId
          },
          userId.toString()
        );

        return res.status(200).json(objectionResponse);
      } catch (error: any) {
        console.error('Error generating AI response:', error);
        return res.status(500).json({ message: error.message || 'Failed to generate AI response' });
      }
    }
  );

  // Classify an objection
  app.post('/api/objections/classify',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const { objectionText, opportunityId, contactId, companyId } = req.body;

        if (!objectionText) {
          return res.status(400).json({ message: 'Objection text is required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const classification = await objectionHandlerService.classifyObjection(
          objectionText,
          {
            opportunityId,
            contactId,
            companyId,
            userId: userId.toString()
          }
        );

        return res.status(200).json(classification);
      } catch (error: any) {
        console.error('Error classifying objection:', error);
        return res.status(500).json({ message: error.message || 'Failed to classify objection' });
      }
    }
  );

  // Handle a real-time objection
  app.post('/api/objections/handle-realtime',
    authenticateUser,
    checkFeatureEntitlement('ai.objection-handler'),
    async (req: Request, res: Response) => {
      try {
        const { objectionText, opportunityId, contactId, companyId } = req.body;

        if (!objectionText) {
          return res.status(400).json({ message: 'Objection text is required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const result = await objectionHandlerService.handleRealTimeObjection(
          {
            objectionText,
            opportunityId,
            contactId,
            companyId
          },
          userId.toString()
        );

        return res.status(200).json(result);
      } catch (error: any) {
        console.error('Error handling real-time objection:', error);
        return res.status(500).json({ message: error.message || 'Failed to handle real-time objection' });
      }
    }
  );
}
