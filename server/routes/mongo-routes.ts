import { Express, Request, Response, NextFunction } from 'express';
import { MongoRoutesAdapter } from './mongo-routes-adapter';
import { requireFeature, checkResourceLimit } from '../middleware/mongo-subscription-middleware';
import { handleChatCompletion, generateInsights, enrichContactData } from '../ai';
import { registerAIBridgeRoutes } from '../ai-bridge';
import { registerSubscriptionRoutes } from '../subscription-routes';
import { registerMongoDocumentRoutes } from './mongo-document-routes';
import { registerMeetingPrepRoutes } from './meeting-prep-routes';
import { registerObjectionHandlerRoutes } from './objection-handler-routes';
import { registerProposalGeneratorRoutes } from './proposal-generator-routes';
import { registerFollowUpCoachRoutes } from './follow-up-coach-routes';
import { registerWinLossAnalyzerRoutes } from './win-loss-analyzer-routes';
import { registerEmailConfigRoutes } from './email-config-routes';
import { registerPublicProposalRoutes } from './public-proposal-routes';
import { registerProposalAnalyticsRoutes } from './proposal-analytics-routes';
import { registerInteractionTimelineRoutes } from './interaction-timeline-routes';
import { registerEmailTrackingRoutes } from './email-tracking-routes';
import { registerSequenceRoutes } from './sequence-routes';
import { registerForecastRoutes } from './forecast-routes';
import { registerResendWebhookRoutes } from './resend-webhook-routes';
import { registerTenantDomainRoutes } from './tenant-domain-routes';
import { registerEmailTemplateRoutes } from './email-template-routes';
import { registerEmailAnalyticsRoutes } from './email-analytics-routes';
import { registerEmailPredictiveRoutes } from './email-predictive-routes';
import { registerAdaptiveSequenceRoutes } from './adaptive-sequence-routes';
import { registerWorkflowAutomationRoutes } from './workflow-automation-routes';
import { registerAnalyticsReportingRoutes } from './analytics-reporting-routes';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { MongoAuthService } from '../auth/mongo-auth-service';
import { PipelineNarratorService } from '../services/pipeline-narrator-service';
import { DealBriefService } from '../services/deal-brief-service';
import { StageTransitionService } from '../services/stage-transition-service';
import { Opportunity, Insight, StageTransition } from '../models/mongoose';
import leadScoringRoutes from './lead-scoring-routes';
import autoEnrichRoutes from './auto-enrich-routes';
import contactRoutes from './mongo-contacts-routes';
import companyRoutes from './mongo-companies-routes';
import opportunityRoutes from './mongo-opportunities-routes';
import activityRoutes from './mongo-activities-routes';
import featureFlagRoutes from './feature-flag-routes';
import experimentRoutes from './experiment-routes';
import analyticsRoutes from './analytics-routes';

// Extend Express Request to include session
declare module 'express-serve-static-core' {
  interface Request {
    session: {
      userId?: string;
      destroy: (callback?: (err?: any) => void) => void;
    };
  }
}

/**
 * Register MongoDB-specific routes
 */
export function registerMongoRoutes(app: Express): void {
  // Use the MongoDB-specific authentication middleware

  // Auth routes
  app.post("/api/auth/register", async (req, res) => {
    try {
      const userData = req.body;

      // Register user using auth service
      const user = await MongoAuthService.registerUser(userData);

      // Create session
      req.session.userId = user._id;

      return res.status(201).json(user);
    } catch (error: any) {
      return res.status(400).json({ message: error.message || 'Failed to register user' });
    }
  });

  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      // Login user using auth service
      const user = await MongoAuthService.loginUser(username, password);

      // Create session
      req.session.userId = user._id;

      return res.status(200).json(user);
    } catch (error: any) {
      return res.status(401).json({ message: error.message || 'Failed to login' });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Could not log out" });
      }
      res.clearCookie("connect.sid");
      return res.status(200).json({ message: "Logged out successfully" });
    });
  });

  app.get("/api/auth/me", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;

      if (!userId) {
        req.session.destroy(() => {});
        return res.status(401).json({ message: "User ID not found in session" });
      }

      // Get user using auth service
      const user = await MongoAuthService.getUserById(userId.toString());

      return res.status(200).json(user);
    } catch (error: any) {
      req.session.destroy(() => {});
      return res.status(401).json({ message: error.message || 'Failed to get user' });
    }
  });

  // API Key management
  app.post("/api/auth/api-key", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;

      if (!userId) {
        return res.status(401).json({ message: "User ID not found in session" });
      }

      // Generate API key using auth service
      const apiKey = await MongoAuthService.generateApiKey(userId.toString());

      return res.status(201).json({ apiKey });
    } catch (error: any) {
      return res.status(400).json({ message: error.message || 'Failed to generate API key' });
    }
  });

  app.delete("/api/auth/api-key/:key", authenticateUser, async (req, res) => {
    try {
      const userId = req.session.userId;
      const apiKey = req.params.key;

      if (!userId) {
        return res.status(401).json({ message: "User ID not found in session" });
      }

      // Revoke API key using auth service
      const success = await MongoAuthService.revokeApiKey(userId.toString(), apiKey);

      if (!success) {
        return res.status(404).json({ message: "API key not found" });
      }

      return res.status(204).send();
    } catch (error: any) {
      return res.status(400).json({ message: error.message || 'Failed to revoke API key' });
    }
  });

  // Contact routes - using the new type system
  app.use("/api/contacts", authenticateUser, checkResourceLimit("contacts"), contactRoutes);

  // Company routes - using the new type system
  app.use("/api/companies", authenticateUser, checkResourceLimit("companies"), companyRoutes);

  // Opportunity routes - using the new type system
  app.use("/api/opportunities", authenticateUser, checkResourceLimit("opportunities"), opportunityRoutes);

  // Activity routes - using the new type system
  app.use("/api/activities", authenticateUser, activityRoutes);

  // Feature flag routes
  app.use("/api/feature-flags", authenticateUser, featureFlagRoutes);

  // Experiment routes
  app.use("/api/experiments", authenticateUser, experimentRoutes);

  // Analytics routes
  app.use("/api/analytics", authenticateUser, analyticsRoutes);

  // Relationship routes
  app.get("/api/relationships", authenticateUser, (req, res) => {
    MongoRoutesAdapter.getRelationships(req, res);
  });

  app.post("/api/relationships", authenticateUser, (req, res) => {
    MongoRoutesAdapter.createRelationship(req, res);
  });

  // AI Chat routes
  app.post("/api/ai/chat",
    authenticateUser,
    requireFeature("ai.assistant.basic"),
    checkResourceLimit("ai.tokens", () => 100),
    async (req, res) => {
      handleChatCompletion(req, res);
    }
  );

  app.get("/api/ai/history", authenticateUser, (req, res) => {
    MongoRoutesAdapter.getAiChatsByUser(req, res);
  });

  // Contact enrichment with AI
  app.post("/api/ai/enrich-contact/:id",
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 200),
    async (req, res) => {
      try {
        const contactId = req.params.id;

        // Check if ID is valid ObjectId
        if (!MongoRoutesAdapter.toObjectId(contactId)) {
          return res.status(400).json({ message: "Invalid contact ID format" });
        }

        const enrichedData = await enrichContactData(contactId);

        if (!enrichedData) {
          return res.status(404).json({ error: 'Contact not found or failed to enrich data' });
        }

        return res.status(200).json(enrichedData);
      } catch (error: any) {
        console.error('Error enriching contact data:', error);
        return res.status(500).json({ error: error.message || 'Failed to enrich contact data' });
      }
    }
  );

  // Dashboard metrics
  app.get("/api/dashboard/metrics", authenticateUser, (req, res) => {
    MongoRoutesAdapter.getMetrics(req, res);
  });

  // User preferences
  app.get("/api/user/preferences", authenticateUser, (req, res) => {
    MongoRoutesAdapter.getUserPreferences(req, res);
  });

  app.post("/api/user/preferences", authenticateUser, (req, res) => {
    MongoRoutesAdapter.updateUserPreferences(req, res);
  });

  // Insights
  app.get("/api/insights", authenticateUser, (req, res) => {
    MongoRoutesAdapter.getInsights(req, res);
  });

  app.patch("/api/insights/:id/read", authenticateUser, (req, res) => {
    MongoRoutesAdapter.markInsightAsRead(req, res);
  });

  // Copilot insights
  app.get('/api/copilot/insights',
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 500),
    async (req, res) => {
      try {
        const userId = req.session.userId;
        const insights = await generateInsights(userId);
        return res.status(200).json(insights);
      } catch (error: any) {
        console.error('Error generating insights:', error);
        return res.status(500).json({ error: error.message || 'Failed to generate insights' });
      }
    }
  );

  // Pipeline insights endpoints
  app.get('/api/pipeline/insights',
    authenticateUser,
    requireFeature("ai.insights"),
    async (req, res) => {
      try {
        // Get pipeline insights for the user
        const userId = req.session.userId;
        const limit = parseInt(req.query.limit as string || "20");
        const offset = parseInt(req.query.offset as string || "0");

        // Get insights related to opportunities
        const insights = await Insight.find({
          targetType: 'opportunity',
          generatedBy: 'ai'
        })
        .sort({ createdAt: -1 })
        .skip(offset)
        .limit(limit)
        .exec();

        return res.status(200).json(insights);
      } catch (error: any) {
        console.error('Error getting pipeline insights:', error);
        return res.status(500).json({ error: error.message || 'Failed to get pipeline insights' });
      }
    }
  );

  // Manually trigger pipeline checks
  app.post('/api/pipeline/check',
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 300),
    async (req, res) => {
      try {
        const pipelineService = PipelineNarratorService.getInstance();

        // Check which type of check to run
        const checkType = req.query.type as string || 'all';

        if (checkType === 'stalled' || checkType === 'all') {
          await pipelineService.checkStalledOpportunities();
        }

        if (checkType === 'meetings' || checkType === 'all') {
          await pipelineService.checkMeetingsWithoutProposals();
        }

        // Get the latest insights
        const insights = await Insight.find({
          targetType: 'opportunity',
          generatedBy: 'ai'
        })
        .sort({ createdAt: -1 })
        .limit(10)
        .exec();

        return res.status(200).json({
          message: 'Pipeline checks completed successfully',
          insights
        });
      } catch (error: any) {
        console.error('Error running pipeline checks:', error);
        return res.status(500).json({ error: error.message || 'Failed to run pipeline checks' });
      }
    }
  );

  // Get insights for a specific opportunity
  app.get('/api/pipeline/insights/opportunity/:id',
    authenticateUser,
    async (req, res) => {
      try {
        const opportunityId = req.params.id;

        // Check if ID is valid ObjectId
        if (!MongoRoutesAdapter.toObjectId(opportunityId)) {
          return res.status(400).json({ message: "Invalid opportunity ID format" });
        }

        // Get insights for this opportunity
        const insights = await Insight.find({
          targetType: 'opportunity',
          targetId: MongoRoutesAdapter.toObjectId(opportunityId)
        })
        .sort({ createdAt: -1 })
        .exec();

        return res.status(200).json(insights);
      } catch (error: any) {
        console.error('Error getting opportunity insights:', error);
        return res.status(500).json({ error: error.message || 'Failed to get opportunity insights' });
      }
    }
  );

  // Generate a deal brief for an opportunity
  app.get('/api/opportunities/:id/deal-brief',
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 500),
    async (req, res) => {
      try {
        const opportunityId = req.params.id;

        // Check if ID is valid ObjectId
        if (!MongoRoutesAdapter.toObjectId(opportunityId)) {
          return res.status(400).json({ message: "Invalid opportunity ID format" });
        }

        // Get the deal brief service
        const dealBriefService = DealBriefService.getInstance();

        // Generate the deal brief
        const dealBrief = await dealBriefService.generateDealBrief(opportunityId);

        return res.status(200).json(dealBrief);
      } catch (error: any) {
        console.error('Error generating deal brief:', error);
        return res.status(500).json({ error: error.message || 'Failed to generate deal brief' });
      }
    }
  );

  // Analyze opportunity stage
  app.get('/api/opportunities/:id/stage-analysis',
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 300),
    async (req, res) => {
      try {
        const opportunityId = req.params.id;

        // Check if ID is valid ObjectId
        if (!MongoRoutesAdapter.toObjectId(opportunityId)) {
          return res.status(400).json({ message: "Invalid opportunity ID format" });
        }

        // Get the stage transition service
        const stageService = StageTransitionService.getInstance();

        // Analyze the opportunity stage
        const analysis = await stageService.analyzeOpportunity(opportunityId);

        return res.status(200).json(analysis);
      } catch (error: any) {
        console.error('Error analyzing opportunity stage:', error);
        return res.status(500).json({ error: error.message || 'Failed to analyze opportunity stage' });
      }
    }
  );

  // Get stage transitions for an opportunity
  app.get('/api/opportunities/:id/stage-transitions',
    authenticateUser,
    async (req, res) => {
      try {
        const opportunityId = req.params.id;

        // Check if ID is valid ObjectId
        if (!MongoRoutesAdapter.toObjectId(opportunityId)) {
          return res.status(400).json({ message: "Invalid opportunity ID format" });
        }

        // Get the stage transition service
        const stageService = StageTransitionService.getInstance();

        // Get stage transitions for this opportunity
        const transitions = await stageService.getStageTransitions(opportunityId);

        return res.status(200).json(transitions);
      } catch (error: any) {
        console.error('Error getting stage transitions:', error);
        return res.status(500).json({ error: error.message || 'Failed to get stage transitions' });
      }
    }
  );

  // Apply a stage transition
  app.post('/api/stage-transitions/:id/apply',
    authenticateUser,
    async (req, res) => {
      try {
        const transitionId = req.params.id;
        const userId = req.session.userId as string;
        const { approved } = req.body;

        if (typeof approved !== 'boolean') {
          return res.status(400).json({ message: "Approved status is required" });
        }

        // Get the stage transition service
        const stageService = StageTransitionService.getInstance();

        // Apply the stage transition
        const result = await stageService.applyStageTransition(transitionId, userId, approved);

        return res.status(200).json(result);
      } catch (error: any) {
        console.error('Error applying stage transition:', error);
        return res.status(500).json({ error: error.message || 'Failed to apply stage transition' });
      }
    }
  );

  // Manually trigger stage analysis for all opportunities
  app.post('/api/stage-transitions/analyze-all',
    authenticateUser,
    requireFeature("ai.insights"),
    checkResourceLimit("ai.tokens", () => 1000),
    async (req, res) => {
      try {
        const { autoApply } = req.body;

        // Get the stage transition service
        const stageService = StageTransitionService.getInstance();

        // Check all opportunities for stage transitions
        const transitions = await stageService.checkAllOpportunities(autoApply === true);

        return res.status(200).json({
          message: 'Stage analysis completed successfully',
          transitionsCount: transitions.length,
          transitions: transitions.slice(0, 10) // Return only the first 10 transitions
        });
      } catch (error: any) {
        console.error('Error analyzing all opportunities:', error);
        return res.status(500).json({ error: error.message || 'Failed to analyze opportunities' });
      }
    }
  );

  // Register AI Bridge routes
  registerAIBridgeRoutes(app);

  // Register Subscription routes
  registerSubscriptionRoutes(app, authenticateUser);

  // Register Lead Scoring routes
  app.use('/api/lead-scoring', leadScoringRoutes);

  // Register Auto-Enrich routes
  app.use('/api/auto-enrich', autoEnrichRoutes);

  // Register Document routes
  registerMongoDocumentRoutes(app);

  // Register Meeting Prep routes
  registerMeetingPrepRoutes(app);

  // Register Objection Handler routes
  registerObjectionHandlerRoutes(app);

  // Register Proposal Generator routes
  registerProposalGeneratorRoutes(app);

  // Register Follow-up Coach routes
  registerFollowUpCoachRoutes(app);

  // Register Win/Loss Analyzer routes
  registerWinLossAnalyzerRoutes(app);

  // Register Email Configuration routes
  registerEmailConfigRoutes(app);

  // Register Public Proposal routes
  registerPublicProposalRoutes(app);

  // Register Proposal Analytics routes
  registerProposalAnalyticsRoutes(app);

  // Register Interaction Timeline routes
  registerInteractionTimelineRoutes(app);

  // Register Email Tracking routes
  registerEmailTrackingRoutes(app);

  // Register Sequence routes
  registerSequenceRoutes(app);

  // Register Forecast routes
  registerForecastRoutes(app);

  // Register Resend webhook routes
  registerResendWebhookRoutes(app);

  // Register Tenant Domain routes
  registerTenantDomainRoutes(app);

  // Register Email Template routes
  registerEmailTemplateRoutes(app);

  // Register Email Analytics routes
  registerEmailAnalyticsRoutes(app);

  // Register Email Predictive routes
  registerEmailPredictiveRoutes(app);

  // Register Adaptive Sequence routes
  registerAdaptiveSequenceRoutes(app);

  // Register Workflow Automation routes
  registerWorkflowAutomationRoutes(app);

  // Register Analytics & Reporting routes
  registerAnalyticsReportingRoutes(app);
}
