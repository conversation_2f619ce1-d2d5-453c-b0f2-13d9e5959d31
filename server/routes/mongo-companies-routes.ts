import { Router, Request, Response } from 'express';
import mongoose from 'mongoose';
import { z } from 'zod';
import { Company } from '../models/mongoose';
import { validateRequest, validateQuery } from '@shared/utils/validation';
import { createCompanyRequestSchema, updateCompanyRequestSchema } from '@schemas/api';
import { isCompany, isCompanyArray } from '@types/guards';
import { Company as CompanyType } from '@types/core';
import { CreateCompanyRequest, UpdateCompanyRequest } from '@types/api';

const router = Router();

/**
 * GET /api/companies
 * Get all companies with pagination and filtering
 */
router.get('/', validateQuery(
  z.object({
    page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
    pageSize: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
    status: z.string().optional(),
    search: z.string().optional(),
    industry: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  })
), async (req: Request, res: Response) => {
  try {
    const { page, pageSize, status, search, industry, sortBy, sortOrder } = req.validatedQuery;
    const userId = req.session.userId;
    
    // Build query
    let query: any = { owner: userId };
    
    if (status) {
      query.status = status;
    }
    
    if (industry) {
      query.industry = industry;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { industry: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Build sort options
    const sort: any = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by createdAt desc
    }
    
    // Execute query with pagination
    const skip = (page - 1) * pageSize;
    const companies = await Company.find(query)
      .sort(sort)
      .skip(skip)
      .limit(pageSize)
      .lean();
    
    // Get total count for pagination
    const total = await Company.countDocuments(query);
    
    // Map MongoDB documents to our type system
    const mappedCompanies = companies.map(company => ({
      id: company._id.toString(),
      name: company.name,
      industry: company.industry,
      website: company.website,
      employees: company.size ? parseInt(company.size) : undefined,
      status: company.status,
      notes: company.notes,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
      createdBy: company.owner.toString(),
    }));
    
    // Return paginated response
    res.json({
      success: true,
      companies: mappedCompanies,
      total,
      page,
      pageSize,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch companies',
    });
  }
});

/**
 * GET /api/companies/:id
 * Get a single company by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid company ID format"
      });
    }
    
    const company = await Company.findOne({ _id: id, owner: userId }).lean();
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found',
      });
    }
    
    // Map MongoDB document to our type system
    const mappedCompany: CompanyType = {
      id: company._id.toString(),
      name: company.name,
      industry: company.industry,
      website: company.website,
      employees: company.size ? parseInt(company.size) : undefined,
      status: company.status,
      notes: company.notes,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
      createdBy: company.owner.toString(),
    };
    
    res.json({
      success: true,
      company: mappedCompany,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch company',
    });
  }
});

/**
 * POST /api/companies
 * Create a new company
 */
router.post('/', validateRequest(createCompanyRequestSchema), async (req: Request, res: Response) => {
  try {
    const companyData: CreateCompanyRequest = req.validatedBody;
    const userId = req.session.userId;
    
    // Create company
    const company = new Company({
      ...companyData,
      size: companyData.employees?.toString(), // Convert to string for MongoDB
      owner: userId
    });
    
    await company.save();
    
    // Map MongoDB document to our type system
    const mappedCompany: CompanyType = {
      id: company._id.toString(),
      name: company.name,
      industry: company.industry,
      website: company.website,
      employees: company.size ? parseInt(company.size) : undefined,
      status: company.status,
      notes: company.notes,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
      createdBy: company.owner.toString(),
    };
    
    res.status(201).json({
      success: true,
      company: mappedCompany,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create company',
    });
  }
});

/**
 * PATCH /api/companies/:id
 * Update an existing company
 */
router.patch('/:id', validateRequest(updateCompanyRequestSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const companyData: UpdateCompanyRequest = req.validatedBody;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid company ID format"
      });
    }
    
    // Convert employees to size for MongoDB
    const updateData = {
      ...companyData,
      size: companyData.employees?.toString(),
    };
    delete updateData.employees;
    
    // Update company
    const company = await Company.findOneAndUpdate(
      { _id: id, owner: userId },
      { $set: updateData },
      { new: true }
    ).lean();
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found',
      });
    }
    
    // Map MongoDB document to our type system
    const mappedCompany: CompanyType = {
      id: company._id.toString(),
      name: company.name,
      industry: company.industry,
      website: company.website,
      employees: company.size ? parseInt(company.size) : undefined,
      status: company.status,
      notes: company.notes,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
      createdBy: company.owner.toString(),
    };
    
    res.json({
      success: true,
      company: mappedCompany,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update company',
    });
  }
});

/**
 * DELETE /api/companies/:id
 * Delete a company
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid company ID format"
      });
    }
    
    const result = await Company.deleteOne({ _id: id, owner: userId });
    
    if (result.deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Company not found',
      });
    }
    
    res.json({
      success: true,
      message: 'Company deleted successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete company',
    });
  }
});

export default router;
