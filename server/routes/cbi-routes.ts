import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { CBIService } from '../services/cbi-service';
import { authenticateUser, requireAdmin } from '../middleware/auth';
import { checkFeatureEntitlement } from '../middleware/subscription';
import { validateRequest, validateQuery } from '../middleware/validation';
import { askBIRequestSchema } from '@schemas/analytics-reporting';
import { logger } from '../utils/logger';

const router = Router();

/**
 * POST /api/cbi/ask
 * Ask a question and get insights
 */
router.post('/ask', 
  authenticateUser,
  checkFeatureEntitlement('analytics.conversational_bi'),
  validateRequest(askBIRequestSchema),
  async (req: Request, res: Response) => {
    try {
      const requestData = req.validatedBody;
      
      // Add tenant_id from session if not provided
      if (!requestData.tenant_id && req.session?.tenantId) {
        requestData.tenant_id = req.session.tenantId;
      }
      
      const response = await CBIService.askQuestion(requestData);
      
      res.status(response.success ? 200 : 400).json(response);
    } catch (error: any) {
      logger.error('Error in /api/cbi/ask:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * GET /api/cbi/recent
 * Get recent insights
 */
router.get('/recent',
  authenticateUser,
  checkFeatureEntitlement('analytics.conversational_bi'),
  validateQuery(
    z.object({
      limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 10),
    })
  ),
  async (req: Request, res: Response) => {
    try {
      const { limit } = req.validatedQuery;
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      const insights = await CBIService.getRecentInsights(tenantId, limit);
      
      res.json({
        success: true,
        insights
      });
    } catch (error: any) {
      logger.error('Error in /api/cbi/recent:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * DELETE /api/cbi/:id
 * Delete an insight
 */
router.delete('/:id',
  authenticateUser,
  checkFeatureEntitlement('analytics.conversational_bi'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      const success = await CBIService.deleteInsight(id, tenantId);
      
      res.json({
        success,
        message: success ? 'Insight deleted successfully' : 'Insight not found or could not be deleted'
      });
    } catch (error: any) {
      logger.error('Error in /api/cbi/:id:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

export default router;
