/**
 * Forecast Routes
 * 
 * This module defines the API routes for deal forecasting functionality.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import forecastService from '../services/forecast-service';
import { DealForecast, TeamForecast } from '../models/mongoose';
import { logger } from '../utils/logger';

export function registerForecastRoutes(app: Express) {
  // Get all deal forecasts for a user
  app.get('/api/forecasts/deals',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { opportunityId, riskLevel, page = '1', limit = '20' } = req.query;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Build query
        const query: any = { userId: new mongoose.Types.ObjectId(userId.toString()) };

        if (opportunityId) {
          query.opportunityId = new mongoose.Types.ObjectId(opportunityId.toString());
        }

        if (riskLevel) {
          query.riskLevel = riskLevel;
        }

        // Pagination
        const pageNum = parseInt(page as string);
        const limitNum = parseInt(limit as string);
        const skip = (pageNum - 1) * limitNum;

        // Get forecasts
        const forecasts = await DealForecast.find(query)
          .sort({ forecastDate: -1 })
          .skip(skip)
          .limit(limitNum)
          .populate('opportunityId', 'name value stage closeDate');

        // Get total count
        const totalCount = await DealForecast.countDocuments(query);

        res.status(200).json({
          data: forecasts,
          pagination: {
            page: pageNum,
            limit: limitNum,
            totalCount,
            totalPages: Math.ceil(totalCount / limitNum)
          }
        });
      } catch (error) {
        logger.error('Error getting deal forecasts:', error);
        res.status(500).json({ message: 'Failed to get deal forecasts' });
      }
    }
  );

  // Get a specific deal forecast
  app.get('/api/forecasts/deals/:id',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get forecast
        const forecast = await DealForecast.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        }).populate('opportunityId', 'name value stage closeDate owner');

        if (!forecast) {
          return res.status(404).json({ message: 'Deal forecast not found' });
        }

        res.status(200).json(forecast);
      } catch (error) {
        logger.error(`Error getting deal forecast ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get deal forecast' });
      }
    }
  );

  // Generate a forecast for a deal
  app.post('/api/forecasts/deals',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { opportunityId } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Validate required fields
        if (!opportunityId) {
          return res.status(400).json({ message: 'Opportunity ID is required' });
        }

        // Generate forecast
        const forecast = await forecastService.generateDealForecast(
          opportunityId,
          userId.toString(),
          req.headers['x-tenant-id'] as string
        );

        res.status(201).json(forecast);
      } catch (error) {
        logger.error('Error generating deal forecast:', error);
        res.status(500).json({ message: 'Failed to generate deal forecast' });
      }
    }
  );

  // Get the latest forecast for a deal
  app.get('/api/forecasts/deals/opportunity/:opportunityId',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { opportunityId } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get the latest forecast
        const forecast = await DealForecast.findOne({
          opportunityId: new mongoose.Types.ObjectId(opportunityId),
          userId: new mongoose.Types.ObjectId(userId.toString())
        }).sort({ forecastDate: -1 });

        if (!forecast) {
          return res.status(404).json({ message: 'No forecast found for this opportunity' });
        }

        res.status(200).json(forecast);
      } catch (error) {
        logger.error(`Error getting latest forecast for opportunity ${req.params.opportunityId}:`, error);
        res.status(500).json({ message: 'Failed to get latest forecast' });
      }
    }
  );

  // Get all team forecasts
  app.get('/api/forecasts/team',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { period, page = '1', limit = '20' } = req.query;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Build query
        const query: any = { userId: new mongoose.Types.ObjectId(userId.toString()) };

        if (period) {
          query.period = period;
        }

        // Pagination
        const pageNum = parseInt(page as string);
        const limitNum = parseInt(limit as string);
        const skip = (pageNum - 1) * limitNum;

        // Get forecasts
        const forecasts = await TeamForecast.find(query)
          .sort({ forecastDate: -1 })
          .skip(skip)
          .limit(limitNum);

        // Get total count
        const totalCount = await TeamForecast.countDocuments(query);

        res.status(200).json({
          data: forecasts,
          pagination: {
            page: pageNum,
            limit: limitNum,
            totalCount,
            totalPages: Math.ceil(totalCount / limitNum)
          }
        });
      } catch (error) {
        logger.error('Error getting team forecasts:', error);
        res.status(500).json({ message: 'Failed to get team forecasts' });
      }
    }
  );

  // Get a specific team forecast
  app.get('/api/forecasts/team/:id',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { id } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get forecast
        const forecast = await TeamForecast.findOne({
          _id: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(userId.toString())
        });

        if (!forecast) {
          return res.status(404).json({ message: 'Team forecast not found' });
        }

        res.status(200).json(forecast);
      } catch (error) {
        logger.error(`Error getting team forecast ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get team forecast' });
      }
    }
  );

  // Generate a team forecast
  app.post('/api/forecasts/team',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { period, startDate, endDate, targetAmount, currency } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Validate required fields
        if (!period || !startDate || !endDate || !targetAmount) {
          return res.status(400).json({ 
            message: 'Period, start date, end date, and target amount are required' 
          });
        }

        // Generate forecast
        const forecast = await forecastService.generateTeamForecast({
          userId: userId.toString(),
          tenantId: req.headers['x-tenant-id'] as string,
          period,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          targetAmount,
          currency
        });

        res.status(201).json(forecast);
      } catch (error) {
        logger.error('Error generating team forecast:', error);
        res.status(500).json({ message: 'Failed to generate team forecast' });
      }
    }
  );

  // Get the latest team forecast for a period
  app.get('/api/forecasts/team/period/:period',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { period } = req.params;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get the latest forecast
        const forecast = await TeamForecast.findOne({
          period,
          userId: new mongoose.Types.ObjectId(userId.toString())
        }).sort({ forecastDate: -1 });

        if (!forecast) {
          return res.status(404).json({ message: 'No forecast found for this period' });
        }

        res.status(200).json(forecast);
      } catch (error) {
        logger.error(`Error getting latest forecast for period ${req.params.period}:`, error);
        res.status(500).json({ message: 'Failed to get latest forecast' });
      }
    }
  );

  // Get at-risk deals
  app.get('/api/forecasts/at-risk',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get at-risk deals
        const atRiskDeals = await DealForecast.find({
          userId: new mongoose.Types.ObjectId(userId.toString()),
          riskLevel: 'high'
        })
          .sort({ forecastDate: -1 })
          .populate('opportunityId', 'name value stage closeDate owner');

        res.status(200).json(atRiskDeals);
      } catch (error) {
        logger.error('Error getting at-risk deals:', error);
        res.status(500).json({ message: 'Failed to get at-risk deals' });
      }
    }
  );

  // Get forecast accuracy metrics
  app.get('/api/forecasts/accuracy',
    authenticateUser,
    checkFeatureEntitlement('sales.forecasting'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const { period } = req.query;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // This would typically involve complex calculations comparing
        // historical forecasts with actual outcomes
        // For now, we'll return a simplified response
        res.status(200).json({
          overall: {
            accuracy: 85, // percentage
            forecastedAmount: 1000000,
            actualAmount: 950000,
            deviation: -5 // percentage
          },
          byPeriod: [
            {
              period: 'Q1 2023',
              accuracy: 90,
              forecastedAmount: 250000,
              actualAmount: 275000,
              deviation: 10
            },
            {
              period: 'Q2 2023',
              accuracy: 82,
              forecastedAmount: 300000,
              actualAmount: 245000,
              deviation: -18
            },
            {
              period: 'Q3 2023',
              accuracy: 88,
              forecastedAmount: 200000,
              actualAmount: 180000,
              deviation: -10
            },
            {
              period: 'Q4 2023',
              accuracy: 80,
              forecastedAmount: 250000,
              actualAmount: 250000,
              deviation: 0
            }
          ]
        });
      } catch (error) {
        logger.error('Error getting forecast accuracy metrics:', error);
        res.status(500).json({ message: 'Failed to get forecast accuracy metrics' });
      }
    }
  );
}

export default registerForecastRoutes;
