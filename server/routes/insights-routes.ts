import { Router, Request, Response } from 'express';
import { insightsService } from '../services/insights-service';
import { authenticateUser } from '../middleware/auth-middleware';
import { checkFeatureEntitlement } from '../middleware/subscription-middleware';
import { validateRequest } from '../middleware/validation-middleware';
import { logger } from '../utils/logger';

const router = Router();

/**
 * @route   POST /api/insights/generate
 * @desc    Generate insight from natural language question
 * @access  Private
 */
router.post(
  '/api/insights/generate',
  authenticateUser,
  checkFeatureEntitlement('insights.generate'),
  validateRequest({
    body: {
      question: { type: 'string', required: true },
      refresh: { type: 'boolean', required: false },
      filters: { type: 'object', required: false }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { question, refresh, filters } = req.body;
      const tenant_id = req.user.tenant_id;
      const user_id = req.user.id;
      
      const result = await insightsService.generateInsight({
        question,
        tenant_id,
        user_id,
        refresh,
        filters
      });
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(500).json({ error: result.error });
      }
    } catch (error) {
      logger.error(`Error generating insight: ${error}`);
      res.status(500).json({ error: `Failed to generate insight: ${error.message}` });
    }
  }
);

/**
 * @route   GET /api/insights/cached
 * @desc    Get cached insights
 * @access  Private
 */
router.get(
  '/api/insights/cached',
  authenticateUser,
  checkFeatureEntitlement('insights.read'),
  async (req: Request, res: Response) => {
    try {
      const tenant_id = req.user.tenant_id;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
      const skip = req.query.skip ? parseInt(req.query.skip as string) : 0;
      
      const result = await insightsService.getCachedInsights(tenant_id, {
        limit,
        skip
      });
      
      res.json(result);
    } catch (error) {
      logger.error(`Error getting cached insights: ${error}`);
      res.status(500).json({ error: `Failed to get cached insights: ${error.message}` });
    }
  }
);

/**
 * @route   GET /api/insights/:id
 * @desc    Get insight by ID
 * @access  Private
 */
router.get(
  '/api/insights/:id',
  authenticateUser,
  checkFeatureEntitlement('insights.read'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenant_id = req.user.tenant_id;
      
      const insight = await insightsService.generateInsight({
        question: '', // Not used when fetching by ID
        tenant_id,
        user_id: req.user.id,
        refresh: false
      });
      
      if (insight.success && insight.insight) {
        res.json(insight.insight);
      } else {
        res.status(404).json({ error: 'Insight not found' });
      }
    } catch (error) {
      logger.error(`Error getting insight: ${error}`);
      res.status(500).json({ error: `Failed to get insight: ${error.message}` });
    }
  }
);

/**
 * @route   POST /api/insights/:id/feedback
 * @desc    Submit feedback for an insight
 * @access  Private
 */
router.post(
  '/api/insights/:id/feedback',
  authenticateUser,
  checkFeatureEntitlement('insights.feedback'),
  validateRequest({
    body: {
      helpful: { type: 'boolean', required: true },
      comment: { type: 'string', required: false }
    }
  }),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { helpful, comment } = req.body;
      const tenant_id = req.user.tenant_id;
      const submitted_by = req.user.id;
      
      const insight = await insightsService.submitFeedback(id, tenant_id, {
        helpful,
        comment,
        submitted_by
      });
      
      res.json(insight);
    } catch (error) {
      logger.error(`Error submitting feedback: ${error}`);
      res.status(500).json({ error: `Failed to submit feedback: ${error.message}` });
    }
  }
);

export default router;
