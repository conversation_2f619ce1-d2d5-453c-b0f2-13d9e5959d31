import { Express, Request, Response } from 'express';
import { authenticateUser } from '../middleware/auth-middleware';
import { checkFeatureEntitlement } from '../middleware/subscription-middleware';
import { ProposalAnalyticsService } from '../services/proposal-analytics-service';
import { Proposal } from '../models/mongoose';

const analyticsService = new ProposalAnalyticsService();

export function registerProposalAnalyticsRoutes(app: Express) {
  // Get analytics for a proposal
  app.get('/api/proposals/:id/analytics',
    authenticateUser,
    checkFeatureEntitlement('analytics.proposals'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;

        // Check if the proposal exists and the user has access to it
        const proposal = await Proposal.findById(id);

        if (!proposal) {
          return res.status(404).json({ message: 'Proposal not found' });
        }

        // Get the analytics
        const analytics = await analyticsService.getAnalytics(id);

        if (!analytics) {
          return res.status(404).json({ message: 'Analytics not found for this proposal' });
        }

        return res.status(200).json(analytics);
      } catch (error: any) {
        console.error(`Error getting analytics for proposal with ID ${req.params.id}:`, error);
        return res.status(500).json({ message: error.message || 'Failed to get proposal analytics' });
      }
    }
  );

  // Get aggregated analytics for all proposals
  app.get('/api/proposals/analytics/summary',
    authenticateUser,
    checkFeatureEntitlement('analytics.proposals'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Get all proposals for the user
        const proposals = await Proposal.find({ createdBy: userId });

        if (!proposals || proposals.length === 0) {
          return res.status(200).json({
            totalProposals: 0,
            totalViews: 0,
            uniqueViews: 0,
            totalDownloads: 0,
            totalShares: 0,
            averageViewDuration: 0,
            conversionRate: 0,
            proposals: []
          });
        }

        // Get analytics for each proposal
        const proposalIds = proposals.map(p => p._id.toString());
        const analyticsPromises = proposalIds.map(id => analyticsService.getAnalytics(id));
        const analyticsResults = await Promise.all(analyticsPromises);

        // Filter out null results
        const validAnalytics = analyticsResults.filter(a => a !== null) as any[];

        // Calculate summary statistics
        const totalViews = validAnalytics.reduce((sum, a) => sum + a.totalViews, 0);
        const uniqueViews = validAnalytics.reduce((sum, a) => sum + a.uniqueViews, 0);
        const totalDownloads = validAnalytics.reduce((sum, a) => sum + a.totalDownloads, 0);
        const totalShares = validAnalytics.reduce((sum, a) => sum + a.totalShares, 0);

        // Calculate average view duration
        const totalDuration = validAnalytics.reduce((sum, a) => sum + (a.averageViewDuration * a.totalViews), 0);
        const averageViewDuration = totalViews > 0 ? totalDuration / totalViews : 0;

        // Calculate conversion rate (accepted proposals / viewed proposals)
        const viewedProposals = proposals.filter(p => p.viewedAt).length;
        const acceptedProposals = proposals.filter(p => p.acceptedAt).length;
        const conversionRate = viewedProposals > 0 ? (acceptedProposals / viewedProposals) * 100 : 0;

        // Prepare proposal summary data
        const proposalSummaries = proposals.map(p => {
          const analytics = validAnalytics.find(a => a.proposalId.toString() === p._id.toString());
          return {
            id: p._id,
            name: p.name,
            status: p.status,
            createdAt: p.createdAt,
            sentAt: p.sentAt,
            viewedAt: p.viewedAt,
            acceptedAt: p.acceptedAt,
            rejectedAt: p.rejectedAt,
            views: analytics?.totalViews || 0,
            uniqueViews: analytics?.uniqueViews || 0,
            downloads: analytics?.totalDownloads || 0,
            shares: analytics?.totalShares || 0
          };
        });

        return res.status(200).json({
          totalProposals: proposals.length,
          totalViews,
          uniqueViews,
          totalDownloads,
          totalShares,
          averageViewDuration,
          conversionRate,
          proposals: proposalSummaries
        });
      } catch (error: any) {
        console.error('Error getting proposal analytics summary:', error);
        return res.status(500).json({ message: error.message || 'Failed to get proposal analytics summary' });
      }
    }
  );
}
