import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { AnalyticsDataService } from '../services/analytics-data-service';
import { authenticateUser, requireAdmin } from '../middleware/auth';
import { checkFeatureEntitlement } from '../middleware/subscription';
import { validateRequest, validateQuery } from '../middleware/validation';
import { getDatasetsRequestSchema } from '@schemas/analytics-reporting';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /api/datasets
 * Get datasets
 */
router.get('/',
  authenticateUser,
  checkFeatureEntitlement('analytics.conversational_bi'),
  validateQuery(
    z.object({
      is_active: z.string().optional().transform(val => val === 'true'),
    })
  ),
  async (req: Request, res: Response) => {
    try {
      const { is_active } = req.validatedQuery;
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      const datasets = await AnalyticsDataService.getDatasets({
        tenant_id: tenantId,
        is_active,
      });
      
      res.json({
        success: true,
        datasets,
      });
    } catch (error: any) {
      logger.error('Error in /api/datasets:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * POST /api/datasets
 * Create or update a dataset
 */
router.post('/',
  authenticateUser,
  requireAdmin,
  checkFeatureEntitlement('analytics.conversational_bi'),
  validateRequest(
    z.object({
      id: z.string().optional(),
      name: z.string().min(1),
      display_name: z.string().min(1),
      description: z.string().min(1),
      source_type: z.enum(['bigquery', 'mongodb', 'neo4j']),
      source_config: z.record(z.any()),
      fields: z.array(
        z.object({
          name: z.string().min(1),
          display_name: z.string().min(1),
          data_type: z.enum(['string', 'number', 'date', 'boolean']),
          description: z.string().min(1),
          is_dimension: z.boolean(),
          is_metric: z.boolean(),
          format: z.enum(['currency', 'percentage', 'date', 'number', 'text']).optional(),
          aggregation: z.enum(['sum', 'avg', 'count', 'min', 'max', 'distinct']).optional(),
        })
      ),
      is_active: z.boolean().optional(),
      refresh_frequency: z.enum(['hourly', 'daily', 'weekly']).optional(),
    })
  ),
  async (req: Request, res: Response) => {
    try {
      const datasetData = req.validatedBody;
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      const datasetId = await AnalyticsDataService.createOrUpdateDataset({
        ...datasetData,
        tenant_id: tenantId,
      });
      
      res.status(datasetData.id ? 200 : 201).json({
        success: true,
        dataset_id: datasetId,
        message: datasetData.id ? 'Dataset updated successfully' : 'Dataset created successfully'
      });
    } catch (error: any) {
      logger.error('Error in POST /api/datasets:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

/**
 * GET /api/datasets/:id
 * Get a dataset by ID
 */
router.get('/:id',
  authenticateUser,
  checkFeatureEntitlement('analytics.conversational_bi'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      // Get datasets
      const datasets = await AnalyticsDataService.getDatasets({
        tenant_id: tenantId,
      });
      
      // Find dataset by ID
      const dataset = datasets.find(d => d.id === id);
      
      if (!dataset) {
        return res.status(404).json({
          success: false,
          message: 'Dataset not found'
        });
      }
      
      res.json({
        success: true,
        dataset,
      });
    } catch (error: any) {
      logger.error(`Error in /api/datasets/${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        message: error.message || 'An error occurred while processing your request'
      });
    }
  }
);

export default router;
