import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import { 
  User, 
  Contact, 
  Company, 
  Opportunity, 
  Activity, 
  Relationship, 
  AiChat, 
  Insight 
} from '../models/mongoose';

/**
 * MongoDB routes adapter to handle MongoDB-specific operations
 */
export class MongoRoutesAdapter {
  /**
   * Convert string ID to MongoDB ObjectId
   */
  static toObjectId(id: string): mongoose.Types.ObjectId {
    try {
      return new mongoose.Types.ObjectId(id);
    } catch (error) {
      throw new Error(`Invalid ID format: ${id}`);
    }
  }

  /**
   * Get user by ID
   */
  static async getUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        res.status(400).json({ message: "Invalid user ID format" });
        return;
      }
      
      const user = await User.findById(userId).select('-password');
      
      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }
      
      res.status(200).json(user);
    } catch (error) {
      console.error('Error getting user:', error);
      res.status(500).json({ message: "Failed to get user", error: error.message });
    }
  }

  /**
   * Get contact by ID
   */
  static async getContact(req: Request, res: Response): Promise<void> {
    try {
      const contactId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(contactId)) {
        res.status(400).json({ message: "Invalid contact ID format" });
        return;
      }
      
      const contact = await Contact.findById(contactId);
      
      if (!contact) {
        res.status(404).json({ message: "Contact not found" });
        return;
      }
      
      res.status(200).json(contact);
    } catch (error) {
      console.error('Error getting contact:', error);
      res.status(500).json({ message: "Failed to get contact", error: error.message });
    }
  }

  /**
   * Get contacts with pagination
   */
  static async getContacts(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string || "100");
      const offset = parseInt(req.query.offset as string || "0");
      
      const contacts = await Contact.find()
        .skip(offset)
        .limit(limit)
        .exec();
      
      res.status(200).json(contacts);
    } catch (error) {
      console.error('Error getting contacts:', error);
      res.status(500).json({ message: "Failed to get contacts", error: error.message });
    }
  }

  /**
   * Create contact
   */
  static async createContact(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const contactData = req.body;
      
      // Create contact
      const contact = new Contact({
        ...contactData,
        owner: userId
      });
      
      await contact.save();
      
      // Create activity for the new contact
      const activity = new Activity({
        title: "Contact created",
        description: `Contact ${contact.firstName} ${contact.lastName} was created`,
        type: "contact_created",
        contactId: contact._id,
        owner: userId
      });
      
      await activity.save();
      
      res.status(201).json(contact);
    } catch (error) {
      console.error('Error creating contact:', error);
      res.status(400).json({ message: "Failed to create contact", error: error.message });
    }
  }

  /**
   * Update contact
   */
  static async updateContact(req: Request, res: Response): Promise<void> {
    try {
      const contactId = req.params.id;
      const contactData = req.body;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(contactId)) {
        res.status(400).json({ message: "Invalid contact ID format" });
        return;
      }
      
      const contact = await Contact.findByIdAndUpdate(
        contactId,
        { $set: contactData },
        { new: true }
      );
      
      if (!contact) {
        res.status(404).json({ message: "Contact not found" });
        return;
      }
      
      res.status(200).json(contact);
    } catch (error) {
      console.error('Error updating contact:', error);
      res.status(400).json({ message: "Failed to update contact", error: error.message });
    }
  }

  /**
   * Delete contact
   */
  static async deleteContact(req: Request, res: Response): Promise<void> {
    try {
      const contactId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(contactId)) {
        res.status(400).json({ message: "Invalid contact ID format" });
        return;
      }
      
      const result = await Contact.deleteOne({ _id: contactId });
      
      if (result.deletedCount === 0) {
        res.status(404).json({ message: "Contact not found" });
        return;
      }
      
      res.status(204).send();
    } catch (error) {
      console.error('Error deleting contact:', error);
      res.status(500).json({ message: "Failed to delete contact", error: error.message });
    }
  }

  /**
   * Get company by ID
   */
  static async getCompany(req: Request, res: Response): Promise<void> {
    try {
      const companyId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        res.status(400).json({ message: "Invalid company ID format" });
        return;
      }
      
      const company = await Company.findById(companyId);
      
      if (!company) {
        res.status(404).json({ message: "Company not found" });
        return;
      }
      
      res.status(200).json(company);
    } catch (error) {
      console.error('Error getting company:', error);
      res.status(500).json({ message: "Failed to get company", error: error.message });
    }
  }

  /**
   * Get companies with pagination
   */
  static async getCompanies(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string || "100");
      const offset = parseInt(req.query.offset as string || "0");
      
      const companies = await Company.find()
        .skip(offset)
        .limit(limit)
        .exec();
      
      res.status(200).json(companies);
    } catch (error) {
      console.error('Error getting companies:', error);
      res.status(500).json({ message: "Failed to get companies", error: error.message });
    }
  }

  /**
   * Create company
   */
  static async createCompany(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const companyData = req.body;
      
      // Create company
      const company = new Company({
        ...companyData,
        owner: userId
      });
      
      await company.save();
      
      // Create activity for the new company
      const activity = new Activity({
        title: "Company created",
        description: `Company ${company.name} was created`,
        type: "company_created",
        companyId: company._id,
        owner: userId
      });
      
      await activity.save();
      
      res.status(201).json(company);
    } catch (error) {
      console.error('Error creating company:', error);
      res.status(400).json({ message: "Failed to create company", error: error.message });
    }
  }

  /**
   * Update company
   */
  static async updateCompany(req: Request, res: Response): Promise<void> {
    try {
      const companyId = req.params.id;
      const companyData = req.body;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        res.status(400).json({ message: "Invalid company ID format" });
        return;
      }
      
      const company = await Company.findByIdAndUpdate(
        companyId,
        { $set: companyData },
        { new: true }
      );
      
      if (!company) {
        res.status(404).json({ message: "Company not found" });
        return;
      }
      
      res.status(200).json(company);
    } catch (error) {
      console.error('Error updating company:', error);
      res.status(400).json({ message: "Failed to update company", error: error.message });
    }
  }

  /**
   * Delete company
   */
  static async deleteCompany(req: Request, res: Response): Promise<void> {
    try {
      const companyId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        res.status(400).json({ message: "Invalid company ID format" });
        return;
      }
      
      const result = await Company.deleteOne({ _id: companyId });
      
      if (result.deletedCount === 0) {
        res.status(404).json({ message: "Company not found" });
        return;
      }
      
      res.status(204).send();
    } catch (error) {
      console.error('Error deleting company:', error);
      res.status(500).json({ message: "Failed to delete company", error: error.message });
    }
  }

  /**
   * Get opportunity by ID
   */
  static async getOpportunity(req: Request, res: Response): Promise<void> {
    try {
      const opportunityId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        res.status(400).json({ message: "Invalid opportunity ID format" });
        return;
      }
      
      const opportunity = await Opportunity.findById(opportunityId);
      
      if (!opportunity) {
        res.status(404).json({ message: "Opportunity not found" });
        return;
      }
      
      res.status(200).json(opportunity);
    } catch (error) {
      console.error('Error getting opportunity:', error);
      res.status(500).json({ message: "Failed to get opportunity", error: error.message });
    }
  }

  /**
   * Get opportunities with pagination
   */
  static async getOpportunities(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string || "100");
      const offset = parseInt(req.query.offset as string || "0");
      
      const opportunities = await Opportunity.find()
        .skip(offset)
        .limit(limit)
        .exec();
      
      res.status(200).json(opportunities);
    } catch (error) {
      console.error('Error getting opportunities:', error);
      res.status(500).json({ message: "Failed to get opportunities", error: error.message });
    }
  }

  /**
   * Create opportunity
   */
  static async createOpportunity(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const opportunityData = req.body;
      
      // Create opportunity
      const opportunity = new Opportunity({
        ...opportunityData,
        owner: userId
      });
      
      await opportunity.save();
      
      // Create activity for the new opportunity
      const activity = new Activity({
        title: "Opportunity created",
        description: `Opportunity ${opportunity.name} was created`,
        type: "opportunity_created",
        opportunityId: opportunity._id,
        owner: userId
      });
      
      await activity.save();
      
      res.status(201).json(opportunity);
    } catch (error) {
      console.error('Error creating opportunity:', error);
      res.status(400).json({ message: "Failed to create opportunity", error: error.message });
    }
  }

  /**
   * Update opportunity
   */
  static async updateOpportunity(req: Request, res: Response): Promise<void> {
    try {
      const opportunityId = req.params.id;
      const opportunityData = req.body;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        res.status(400).json({ message: "Invalid opportunity ID format" });
        return;
      }
      
      const opportunity = await Opportunity.findByIdAndUpdate(
        opportunityId,
        { $set: opportunityData },
        { new: true }
      );
      
      if (!opportunity) {
        res.status(404).json({ message: "Opportunity not found" });
        return;
      }
      
      res.status(200).json(opportunity);
    } catch (error) {
      console.error('Error updating opportunity:', error);
      res.status(400).json({ message: "Failed to update opportunity", error: error.message });
    }
  }

  /**
   * Delete opportunity
   */
  static async deleteOpportunity(req: Request, res: Response): Promise<void> {
    try {
      const opportunityId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(opportunityId)) {
        res.status(400).json({ message: "Invalid opportunity ID format" });
        return;
      }
      
      const result = await Opportunity.deleteOne({ _id: opportunityId });
      
      if (result.deletedCount === 0) {
        res.status(404).json({ message: "Opportunity not found" });
        return;
      }
      
      res.status(204).send();
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      res.status(500).json({ message: "Failed to delete opportunity", error: error.message });
    }
  }

  /**
   * Get activities with pagination
   */
  static async getActivities(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string || "100");
      const offset = parseInt(req.query.offset as string || "0");
      
      const activities = await Activity.find()
        .skip(offset)
        .limit(limit)
        .sort({ date: -1 })
        .exec();
      
      res.status(200).json(activities);
    } catch (error) {
      console.error('Error getting activities:', error);
      res.status(500).json({ message: "Failed to get activities", error: error.message });
    }
  }

  /**
   * Create activity
   */
  static async createActivity(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const activityData = req.body;
      
      // Create activity
      const activity = new Activity({
        ...activityData,
        owner: userId
      });
      
      await activity.save();
      
      res.status(201).json(activity);
    } catch (error) {
      console.error('Error creating activity:', error);
      res.status(400).json({ message: "Failed to create activity", error: error.message });
    }
  }

  /**
   * Get relationships
   */
  static async getRelationships(req: Request, res: Response): Promise<void> {
    try {
      const relationships = await Relationship.find().exec();
      
      res.status(200).json(relationships);
    } catch (error) {
      console.error('Error getting relationships:', error);
      res.status(500).json({ message: "Failed to get relationships", error: error.message });
    }
  }

  /**
   * Create relationship
   */
  static async createRelationship(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const relationshipData = req.body;
      
      // Create relationship
      const relationship = new Relationship({
        ...relationshipData,
        createdBy: userId
      });
      
      await relationship.save();
      
      res.status(201).json(relationship);
    } catch (error) {
      console.error('Error creating relationship:', error);
      res.status(400).json({ message: "Failed to create relationship", error: error.message });
    }
  }

  /**
   * Get AI chat history for a user
   */
  static async getAiChatsByUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const limit = parseInt(req.query.limit as string || "20");
      
      const chats = await AiChat.find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
      
      res.status(200).json(chats);
    } catch (error) {
      console.error('Error getting AI chats:', error);
      res.status(500).json({ message: "Failed to get AI chats", error: error.message });
    }
  }

  /**
   * Create AI chat
   */
  static async createAiChat(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const { message, response, context } = req.body;
      
      // Create chat
      const chat = new AiChat({
        userId,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
        messages: [
          { role: 'user', content: message, timestamp: new Date() },
          { role: 'assistant', content: response, timestamp: new Date() }
        ],
        context
      });
      
      await chat.save();
      
      res.status(201).json(chat);
    } catch (error) {
      console.error('Error creating AI chat:', error);
      res.status(400).json({ message: "Failed to create AI chat", error: error.message });
    }
  }

  /**
   * Get insights with pagination
   */
  static async getInsights(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string || "20");
      const offset = parseInt(req.query.offset as string || "0");
      
      const insights = await Insight.find()
        .sort({ importance: -1, createdAt: -1 })
        .skip(offset)
        .limit(limit)
        .exec();
      
      res.status(200).json(insights);
    } catch (error) {
      console.error('Error getting insights:', error);
      res.status(500).json({ message: "Failed to get insights", error: error.message });
    }
  }

  /**
   * Mark insight as read
   */
  static async markInsightAsRead(req: Request, res: Response): Promise<void> {
    try {
      const insightId = req.params.id;
      
      // Check if ID is valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(insightId)) {
        res.status(400).json({ message: "Invalid insight ID format" });
        return;
      }
      
      const insight = await Insight.findByIdAndUpdate(
        insightId,
        { $set: { isRead: true } },
        { new: true }
      );
      
      if (!insight) {
        res.status(404).json({ message: "Insight not found" });
        return;
      }
      
      res.status(200).json(insight);
    } catch (error) {
      console.error('Error marking insight as read:', error);
      res.status(500).json({ message: "Failed to mark insight as read", error: error.message });
    }
  }

  /**
   * Get dashboard metrics
   */
  static async getMetrics(req: Request, res: Response): Promise<void> {
    try {
      const [
        totalContacts,
        totalCompanies,
        openOpportunities,
        aiInsightsGenerated
      ] = await Promise.all([
        Contact.countDocuments(),
        Company.countDocuments(),
        Opportunity.find({ 
          stage: { 
            $nin: ['closed_won', 'closed_lost'] 
          } 
        }),
        Insight.countDocuments({ generatedBy: 'ai' })
      ]);
      
      const pipelineValue = openOpportunities.reduce(
        (sum, opp) => sum + (opp.value || 0), 
        0
      );
      
      const metrics = {
        totalContacts,
        totalCompanies,
        openOpportunities: openOpportunities.length,
        pipelineValue,
        aiInsightsGenerated
      };
      
      res.status(200).json(metrics);
    } catch (error) {
      console.error('Error getting metrics:', error);
      res.status(500).json({ message: "Failed to get metrics", error: error.message });
    }
  }

  /**
   * Get user preferences
   */
  static async getUserPreferences(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      
      const user = await User.findById(userId);
      
      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }
      
      res.status(200).json(user.preferences || {});
    } catch (error) {
      console.error('Error getting user preferences:', error);
      res.status(500).json({ message: "Failed to get user preferences", error: error.message });
    }
  }

  /**
   * Update user preferences
   */
  static async updateUserPreferences(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.session.userId;
      const { preferences } = req.body;
      
      if (!preferences || typeof preferences !== 'object') {
        res.status(400).json({ message: "Invalid preferences data" });
        return;
      }
      
      const user = await User.findByIdAndUpdate(
        userId,
        { $set: { preferences } },
        { new: true }
      );
      
      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }
      
      res.status(200).json(user.preferences);
    } catch (error) {
      console.error('Error updating user preferences:', error);
      res.status(500).json({ message: "Failed to update user preferences", error: error.message });
    }
  }

  /**
   * Authentication middleware for MongoDB
   */
  static authenticateUser(req: Request, res: Response, next: NextFunction): void {
    // First check traditional session authentication
    if (req.session && req.session.userId) {
      return next();
    }

    // Then check for Firebase token in Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      // TODO: Implement Firebase token validation for MongoDB
      // For now, just reject
      return res.status(401).json({ message: "Unauthorized" });
    }

    return res.status(401).json({ message: "Unauthorized" });
  }
}
