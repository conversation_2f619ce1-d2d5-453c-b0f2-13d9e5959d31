/**
 * Email Template Routes
 * 
 * This module defines the API routes for email template management.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import emailTemplateService from '../services/email-template-service';
import { EmailTemplate, EmailTemplateCategory } from '../models/mongoose/email-template-model';
import { logger } from '../utils/logger';

export function registerEmailTemplateRoutes(app: Express) {
  /**
   * Get all templates for a tenant
   */
  app.get('/api/email-templates',
    authenticateUser,
    checkFeatureEntitlement('email.templates'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { 
          category, 
          tags, 
          isActive, 
          search, 
          page = '1', 
          limit = '20',
          sort,
          sortDirection
        } = req.query;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Parse query parameters
        const options: any = {
          page: parseInt(page as string),
          limit: parseInt(limit as string)
        };

        if (category) {
          options.category = category;
        }

        if (tags) {
          options.tags = Array.isArray(tags) ? tags : [tags];
        }

        if (isActive !== undefined) {
          options.isActive = isActive === 'true';
        }

        if (search) {
          options.search = search;
        }

        if (sort) {
          options.sort = sort;
          options.sortDirection = sortDirection as 'asc' | 'desc';
        }

        // Get templates
        const result = await emailTemplateService.getTemplatesByTenant(tenantId, options);

        res.status(200).json({
          data: result.templates,
          pagination: {
            page: options.page,
            limit: options.limit,
            totalCount: result.total,
            totalPages: Math.ceil(result.total / options.limit)
          }
        });
      } catch (error) {
        logger.error('Error getting email templates:', error);
        res.status(500).json({ message: 'Failed to get email templates' });
      }
    }
  );

  /**
   * Get a specific template
   */
  app.get('/api/email-templates/:id',
    authenticateUser,
    checkFeatureEntitlement('email.templates'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        // Get template
        const template = await emailTemplateService.getTemplateById(id);

        if (!template) {
          return res.status(404).json({ message: 'Template not found' });
        }

        // Check tenant access
        if (tenantId && template.tenantId && !template.tenantId.equals(new mongoose.Types.ObjectId(tenantId))) {
          return res.status(403).json({ message: 'Access denied' });
        }

        res.status(200).json(template);
      } catch (error) {
        logger.error(`Error getting email template ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get email template' });
      }
    }
  );

  /**
   * Create a new template
   */
  app.post('/api/email-templates',
    authenticateUser,
    checkFeatureEntitlement('email.templates'),
    async (req: Request, res: Response) => {
      try {
        const userId = req.session.userId;
        const tenantId = req.headers['x-tenant-id'] as string;
        const {
          name,
          description,
          subject,
          body,
          plainText,
          category,
          tags,
          isActive,
          isDefault,
          variables,
          metadata,
          trackingSettings,
          customFields
        } = req.body;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        // Validate required fields
        if (!name || !subject || !body) {
          return res.status(400).json({ message: 'Name, subject, and body are required' });
        }

        // Create template
        const template = await emailTemplateService.createTemplate({
          userId: userId.toString(),
          tenantId,
          name,
          description,
          subject,
          body,
          plainText,
          category,
          tags,
          isActive,
          isDefault,
          variables,
          metadata,
          trackingSettings,
          customFields
        });

        res.status(201).json(template);
      } catch (error: any) {
        logger.error('Error creating email template:', error);
        res.status(500).json({ 
          message: 'Failed to create email template',
          error: error.message
        });
      }
    }
  );

  /**
   * Update a template
   */
  app.put('/api/email-templates/:id',
    authenticateUser,
    checkFeatureEntitlement('email.templates'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;
        const {
          name,
          description,
          subject,
          body,
          plainText,
          category,
          tags,
          isActive,
          isDefault,
          variables,
          metadata,
          trackingSettings,
          customFields
        } = req.body;

        // Get template
        const existingTemplate = await emailTemplateService.getTemplateById(id);

        if (!existingTemplate) {
          return res.status(404).json({ message: 'Template not found' });
        }

        // Check tenant access
        if (tenantId && existingTemplate.tenantId && !existingTemplate.tenantId.equals(new mongoose.Types.ObjectId(tenantId))) {
          return res.status(403).json({ message: 'Access denied' });
        }

        // Update template
        const template = await emailTemplateService.updateTemplate(id, {
          name,
          description,
          subject,
          body,
          plainText,
          category,
          tags,
          isActive,
          isDefault,
          variables,
          metadata,
          trackingSettings,
          customFields
        });

        res.status(200).json(template);
      } catch (error) {
        logger.error(`Error updating email template ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to update email template' });
      }
    }
  );

  /**
   * Delete a template
   */
  app.delete('/api/email-templates/:id',
    authenticateUser,
    checkFeatureEntitlement('email.templates'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        // Get template
        const template = await emailTemplateService.getTemplateById(id);

        if (!template) {
          return res.status(404).json({ message: 'Template not found' });
        }

        // Check tenant access
        if (tenantId && template.tenantId && !template.tenantId.equals(new mongoose.Types.ObjectId(tenantId))) {
          return res.status(403).json({ message: 'Access denied' });
        }

        // Check if it's a system template
        if (template.isSystem) {
          return res.status(403).json({ message: 'Cannot delete system templates' });
        }

        // Delete template
        const success = await emailTemplateService.deleteTemplate(id);

        if (success) {
          res.status(200).json({ success: true });
        } else {
          res.status(404).json({ message: 'Template not found' });
        }
      } catch (error) {
        logger.error(`Error deleting email template ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to delete email template' });
      }
    }
  );

  /**
   * Enable A/B testing for a template
   */
  app.post('/api/email-templates/:id/ab-testing',
    authenticateUser,
    checkFeatureEntitlement('email.templates.ab_testing'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;
        const { variants } = req.body;

        // Validate variants
        if (!variants || !Array.isArray(variants) || variants.length < 2) {
          return res.status(400).json({ message: 'At least two variants are required' });
        }

        // Get template
        const template = await emailTemplateService.getTemplateById(id);

        if (!template) {
          return res.status(404).json({ message: 'Template not found' });
        }

        // Check tenant access
        if (tenantId && template.tenantId && !template.tenantId.equals(new mongoose.Types.ObjectId(tenantId))) {
          return res.status(403).json({ message: 'Access denied' });
        }

        // Enable A/B testing
        const updatedTemplate = await emailTemplateService.enableABTesting(id, variants);

        res.status(200).json(updatedTemplate);
      } catch (error: any) {
        logger.error(`Error enabling A/B testing for template ${req.params.id}:`, error);
        res.status(500).json({ 
          message: 'Failed to enable A/B testing',
          error: error.message
        });
      }
    }
  );

  /**
   * Disable A/B testing for a template
   */
  app.delete('/api/email-templates/:id/ab-testing',
    authenticateUser,
    checkFeatureEntitlement('email.templates.ab_testing'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        // Get template
        const template = await emailTemplateService.getTemplateById(id);

        if (!template) {
          return res.status(404).json({ message: 'Template not found' });
        }

        // Check tenant access
        if (tenantId && template.tenantId && !template.tenantId.equals(new mongoose.Types.ObjectId(tenantId))) {
          return res.status(403).json({ message: 'Access denied' });
        }

        // Disable A/B testing
        const updatedTemplate = await emailTemplateService.disableABTesting(id);

        res.status(200).json(updatedTemplate);
      } catch (error) {
        logger.error(`Error disabling A/B testing for template ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to disable A/B testing' });
      }
    }
  );

  /**
   * Get template performance metrics
   */
  app.get('/api/email-templates/:id/performance',
    authenticateUser,
    checkFeatureEntitlement('email.templates'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;

        // Get template
        const template = await emailTemplateService.getTemplateById(id);

        if (!template) {
          return res.status(404).json({ message: 'Template not found' });
        }

        // Check tenant access
        if (tenantId && template.tenantId && !template.tenantId.equals(new mongoose.Types.ObjectId(tenantId))) {
          return res.status(403).json({ message: 'Access denied' });
        }

        // Get performance metrics
        const performance = await emailTemplateService.getTemplatePerformance(id);

        res.status(200).json(performance);
      } catch (error) {
        logger.error(`Error getting performance metrics for template ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get template performance metrics' });
      }
    }
  );

  /**
   * Render a template with variables
   */
  app.post('/api/email-templates/:id/render',
    authenticateUser,
    checkFeatureEntitlement('email.templates'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const tenantId = req.headers['x-tenant-id'] as string;
        const { variables } = req.body;

        // Get template
        const template = await emailTemplateService.getTemplateById(id);

        if (!template) {
          return res.status(404).json({ message: 'Template not found' });
        }

        // Check tenant access
        if (tenantId && template.tenantId && !template.tenantId.equals(new mongoose.Types.ObjectId(tenantId))) {
          return res.status(403).json({ message: 'Access denied' });
        }

        // Render template
        const rendered = emailTemplateService.renderTemplate(template, variables);

        res.status(200).json(rendered);
      } catch (error) {
        logger.error(`Error rendering template ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to render template' });
      }
    }
  );
}

export default registerEmailTemplateRoutes;
