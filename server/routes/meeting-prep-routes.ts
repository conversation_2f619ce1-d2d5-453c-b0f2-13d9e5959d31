import { Express, Request, Response } from 'express';
import { meetingPrepService } from '../services/meeting-prep-service';
import { authenticateUser } from '../middleware/auth';
import { checkFeatureEntitlement } from '../middleware/subscription';

/**
 * Register meeting prep routes
 */
export function registerMeetingPrepRoutes(app: Express) {
  // Generate a meeting prep document
  app.post('/api/meeting-prep',
    authenticateUser,
    checkFeatureEntitlement('ai.meeting-prep'),
    async (req: Request, res: Response) => {
      try {
        const { contactId, companyId, opportunityId } = req.body;

        if (!contactId && !companyId && !opportunityId) {
          return res.status(400).json({
            message: 'At least one of contactId, companyId, or opportunityId must be provided'
          });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const meetingPrep = await meetingPrepService.generateMeetingPrep({
          contactId,
          companyId,
          opportunityId,
          userId: userId.toString()
        });

        return res.status(200).json(meetingPrep);
      } catch (error: any) {
        console.error('Error generating meeting prep:', error);
        return res.status(500).json({ message: error.message || 'Failed to generate meeting prep' });
      }
    }
  );

  // Generate a URL to add meeting prep to a calendar agenda
  app.post('/api/meeting-prep/:id/add-to-agenda',
    authenticateUser,
    checkFeatureEntitlement('ai.meeting-prep'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const { calendarType } = req.body;

        if (!id) {
          return res.status(400).json({ message: 'Meeting prep ID is required' });
        }

        const userId = req.session.userId;

        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }

        const url = await meetingPrepService.generateAddToAgendaUrl(
          id,
          calendarType as 'google' | 'outlook'
        );

        return res.status(200).json({ url });
      } catch (error: any) {
        console.error('Error generating add to agenda URL:', error);
        return res.status(500).json({ message: error.message || 'Failed to generate add to agenda URL' });
      }
    }
  );
}
