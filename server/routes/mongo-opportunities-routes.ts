import { Router, Request, Response } from 'express';
import mongoose from 'mongoose';
import { z } from 'zod';
import { Opportunity } from '../models/mongoose';
import { validateRequest, validateQuery } from '@shared/utils/validation';
import { createOpportunityRequestSchema, updateOpportunityRequestSchema } from '@schemas/api';
import { isOpportunity, isOpportunityArray } from '@types/guards';
import { Opportunity as OpportunityType } from '@types/core';
import { CreateOpportunityRequest, UpdateOpportunityRequest } from '@types/api';

const router = Router();

/**
 * GET /api/opportunities
 * Get all opportunities with pagination and filtering
 */
router.get('/', validateQuery(
  z.object({
    page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
    pageSize: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
    stage: z.string().optional(),
    search: z.string().optional(),
    companyId: z.string().optional(),
    contactId: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  })
), async (req: Request, res: Response) => {
  try {
    const { page, pageSize, stage, search, companyId, contactId, sortBy, sortOrder } = req.validatedQuery;
    const userId = req.session.userId;
    
    // Build query
    let query: any = { owner: userId };
    
    if (stage) {
      query.stage = stage;
    }
    
    if (companyId) {
      query.companyId = companyId;
    }
    
    if (contactId) {
      query.contactId = contactId;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Build sort options
    const sort: any = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by createdAt desc
    }
    
    // Execute query with pagination
    const skip = (page - 1) * pageSize;
    const opportunities = await Opportunity.find(query)
      .sort(sort)
      .skip(skip)
      .limit(pageSize)
      .lean();
    
    // Get total count for pagination
    const total = await Opportunity.countDocuments(query);
    
    // Map MongoDB documents to our type system
    const mappedOpportunities = opportunities.map(opportunity => ({
      id: opportunity._id.toString(),
      name: opportunity.name,
      value: opportunity.value ? parseFloat(opportunity.value) : 0,
      currency: opportunity.currency || 'USD',
      stage: opportunity.stage,
      closeDate: opportunity.closeDate,
      probability: opportunity.probability ? parseFloat(opportunity.probability) : undefined,
      notes: opportunity.notes,
      contactId: opportunity.contactId?.toString(),
      companyId: opportunity.companyId?.toString(),
      createdAt: opportunity.createdAt,
      updatedAt: opportunity.updatedAt,
      createdBy: opportunity.owner.toString(),
    }));
    
    // Return paginated response
    res.json({
      success: true,
      opportunities: mappedOpportunities,
      total,
      page,
      pageSize,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch opportunities',
    });
  }
});

/**
 * GET /api/opportunities/:id
 * Get a single opportunity by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid opportunity ID format"
      });
    }
    
    const opportunity = await Opportunity.findOne({ _id: id, owner: userId }).lean();
    
    if (!opportunity) {
      return res.status(404).json({
        success: false,
        message: 'Opportunity not found',
      });
    }
    
    // Map MongoDB document to our type system
    const mappedOpportunity: OpportunityType = {
      id: opportunity._id.toString(),
      name: opportunity.name,
      value: opportunity.value ? parseFloat(opportunity.value) : 0,
      currency: opportunity.currency || 'USD',
      stage: opportunity.stage,
      closeDate: opportunity.closeDate,
      probability: opportunity.probability ? parseFloat(opportunity.probability) : undefined,
      notes: opportunity.notes,
      contactId: opportunity.contactId?.toString(),
      companyId: opportunity.companyId?.toString(),
      createdAt: opportunity.createdAt,
      updatedAt: opportunity.updatedAt,
      createdBy: opportunity.owner.toString(),
    };
    
    res.json({
      success: true,
      opportunity: mappedOpportunity,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch opportunity',
    });
  }
});

/**
 * POST /api/opportunities
 * Create a new opportunity
 */
router.post('/', validateRequest(createOpportunityRequestSchema), async (req: Request, res: Response) => {
  try {
    const opportunityData: CreateOpportunityRequest = req.validatedBody;
    const userId = req.session.userId;
    
    // Create opportunity
    const opportunity = new Opportunity({
      ...opportunityData,
      value: opportunityData.value?.toString(),
      probability: opportunityData.probability?.toString(),
      owner: userId
    });
    
    await opportunity.save();
    
    // Map MongoDB document to our type system
    const mappedOpportunity: OpportunityType = {
      id: opportunity._id.toString(),
      name: opportunity.name,
      value: opportunity.value ? parseFloat(opportunity.value) : 0,
      currency: opportunity.currency || 'USD',
      stage: opportunity.stage,
      closeDate: opportunity.closeDate,
      probability: opportunity.probability ? parseFloat(opportunity.probability) : undefined,
      notes: opportunity.notes,
      contactId: opportunity.contactId?.toString(),
      companyId: opportunity.companyId?.toString(),
      createdAt: opportunity.createdAt,
      updatedAt: opportunity.updatedAt,
      createdBy: opportunity.owner.toString(),
    };
    
    res.status(201).json({
      success: true,
      opportunity: mappedOpportunity,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create opportunity',
    });
  }
});

/**
 * PATCH /api/opportunities/:id
 * Update an existing opportunity
 */
router.patch('/:id', validateRequest(updateOpportunityRequestSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const opportunityData: UpdateOpportunityRequest = req.validatedBody;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid opportunity ID format"
      });
    }
    
    // Convert numeric values to strings for MongoDB
    const updateData = {
      ...opportunityData,
      value: opportunityData.value?.toString(),
      probability: opportunityData.probability?.toString(),
    };
    
    // Update opportunity
    const opportunity = await Opportunity.findOneAndUpdate(
      { _id: id, owner: userId },
      { $set: updateData },
      { new: true }
    ).lean();
    
    if (!opportunity) {
      return res.status(404).json({
        success: false,
        message: 'Opportunity not found',
      });
    }
    
    // Map MongoDB document to our type system
    const mappedOpportunity: OpportunityType = {
      id: opportunity._id.toString(),
      name: opportunity.name,
      value: opportunity.value ? parseFloat(opportunity.value) : 0,
      currency: opportunity.currency || 'USD',
      stage: opportunity.stage,
      closeDate: opportunity.closeDate,
      probability: opportunity.probability ? parseFloat(opportunity.probability) : undefined,
      notes: opportunity.notes,
      contactId: opportunity.contactId?.toString(),
      companyId: opportunity.companyId?.toString(),
      createdAt: opportunity.createdAt,
      updatedAt: opportunity.updatedAt,
      createdBy: opportunity.owner.toString(),
    };
    
    res.json({
      success: true,
      opportunity: mappedOpportunity,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update opportunity',
    });
  }
});

/**
 * DELETE /api/opportunities/:id
 * Delete an opportunity
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if ID is valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid opportunity ID format"
      });
    }
    
    const result = await Opportunity.deleteOne({ _id: id, owner: userId });
    
    if (result.deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Opportunity not found',
      });
    }
    
    res.json({
      success: true,
      message: 'Opportunity deleted successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete opportunity',
    });
  }
});

export default router;
