import { Express, Request, Response } from 'express';
import { authenticateUser } from '../middleware/auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import { winLossAnalyzerService } from '../services/win-loss-analyzer-service';

/**
 * Register win/loss analyzer routes
 */
export function registerWinLossAnalyzerRoutes(app: Express) {
  // Get all win/loss analyses
  app.get('/api/win-loss/analyses',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const {
          outcome,
          opportunityId,
          contactId,
          companyId,
          startDate,
          endDate,
          minValue,
          maxValue,
          search
        } = req.query;
        
        const analyses = await winLossAnalyzerService.getAnalyses({
          outcome: outcome as 'won' | 'lost',
          opportunityId: opportunityId as string,
          contactId: contactId as string,
          companyId: companyId as string,
          startDate: startDate ? new Date(startDate as string) : undefined,
          endDate: endDate ? new Date(endDate as string) : undefined,
          minValue: minValue ? parseFloat(minValue as string) : undefined,
          maxValue: maxValue ? parseFloat(maxValue as string) : undefined,
          search: search as string
        });
        
        res.json(analyses);
      } catch (error) {
        console.error('Error getting win/loss analyses:', error);
        res.status(500).json({ message: 'Failed to get win/loss analyses' });
      }
    }
  );
  
  // Get win/loss analysis by ID
  app.get('/api/win-loss/analyses/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        
        const analysis = await winLossAnalyzerService.getAnalysisById(id);
        
        if (!analysis) {
          return res.status(404).json({ message: 'Win/loss analysis not found' });
        }
        
        res.json(analysis);
      } catch (error) {
        console.error(`Error getting win/loss analysis with ID ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to get win/loss analysis' });
      }
    }
  );
  
  // Create a new win/loss analysis
  app.post('/api/win-loss/analyses',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const {
          title,
          description,
          opportunityId,
          contactId,
          companyId,
          outcome,
          value,
          currency,
          closedDate,
          stage,
          salesCycle,
          keyFactors,
          competitorInfo,
          feedback,
          learnings,
          recommendations,
          isAIGenerated,
          tags,
          customFields
        } = req.body;
        
        if (!title || !opportunityId || !outcome || value === undefined || !closedDate || !stage || salesCycle === undefined || !keyFactors || !learnings || !recommendations) {
          return res.status(400).json({ message: 'Missing required fields' });
        }
        
        const userId = req.session.userId;
        
        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }
        
        const analysis = await winLossAnalyzerService.createAnalysis(
          {
            title,
            description,
            opportunityId,
            contactId,
            companyId,
            outcome,
            value,
            currency,
            closedDate: new Date(closedDate),
            stage,
            salesCycle,
            keyFactors,
            competitorInfo,
            feedback,
            learnings,
            recommendations,
            isAIGenerated,
            tags,
            customFields
          },
          userId.toString()
        );
        
        res.status(201).json(analysis);
      } catch (error) {
        console.error('Error creating win/loss analysis:', error);
        res.status(500).json({ message: 'Failed to create win/loss analysis' });
      }
    }
  );
  
  // Update a win/loss analysis
  app.put('/api/win-loss/analyses/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        const {
          title,
          description,
          outcome,
          value,
          currency,
          closedDate,
          stage,
          salesCycle,
          keyFactors,
          competitorInfo,
          feedback,
          learnings,
          recommendations,
          tags,
          customFields
        } = req.body;
        
        const analysis = await winLossAnalyzerService.updateAnalysis(
          id,
          {
            title,
            description,
            outcome,
            value,
            currency,
            closedDate: closedDate ? new Date(closedDate) : undefined,
            stage,
            salesCycle,
            keyFactors,
            competitorInfo,
            feedback,
            learnings,
            recommendations,
            tags,
            customFields
          }
        );
        
        if (!analysis) {
          return res.status(404).json({ message: 'Win/loss analysis not found' });
        }
        
        res.json(analysis);
      } catch (error) {
        console.error(`Error updating win/loss analysis with ID ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to update win/loss analysis' });
      }
    }
  );
  
  // Delete a win/loss analysis
  app.delete('/api/win-loss/analyses/:id',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const { id } = req.params;
        
        const success = await winLossAnalyzerService.deleteAnalysis(id);
        
        if (!success) {
          return res.status(404).json({ message: 'Win/loss analysis not found' });
        }
        
        res.status(204).send();
      } catch (error) {
        console.error(`Error deleting win/loss analysis with ID ${req.params.id}:`, error);
        res.status(500).json({ message: 'Failed to delete win/loss analysis' });
      }
    }
  );
  
  // Generate a win/loss analysis for an opportunity
  app.post('/api/win-loss/analyses/generate',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const { opportunityId } = req.body;
        
        if (!opportunityId) {
          return res.status(400).json({ message: 'Opportunity ID is required' });
        }
        
        const userId = req.session.userId;
        
        if (!userId) {
          return res.status(401).json({ message: 'User ID not found in session' });
        }
        
        const analysis = await winLossAnalyzerService.generateAnalysis(
          opportunityId,
          userId.toString()
        );
        
        res.status(201).json(analysis);
      } catch (error) {
        console.error('Error generating win/loss analysis:', error);
        res.status(500).json({ 
          message: error instanceof Error ? error.message : 'Failed to generate win/loss analysis' 
        });
      }
    }
  );
  
  // Get win/loss factors
  app.get('/api/win-loss/factors',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const { category, impact, isActive, search } = req.query;
        
        const factors = await winLossAnalyzerService.getFactors({
          category: category as string,
          impact: impact as 'positive' | 'negative' | 'neutral',
          isActive: isActive !== undefined ? isActive === 'true' : undefined,
          search: search as string
        });
        
        res.json(factors);
      } catch (error) {
        console.error('Error getting win/loss factors:', error);
        res.status(500).json({ message: 'Failed to get win/loss factors' });
      }
    }
  );
  
  // Get win/loss statistics
  app.get('/api/win-loss/statistics',
    authenticateUser,
    checkFeatureEntitlement('ai.win-loss-analyzer'),
    async (req: Request, res: Response) => {
      try {
        const { startDate, endDate, companyId } = req.query;
        
        const statistics = await winLossAnalyzerService.getStatistics({
          startDate: startDate ? new Date(startDate as string) : undefined,
          endDate: endDate ? new Date(endDate as string) : undefined,
          companyId: companyId as string
        });
        
        res.json(statistics);
      } catch (error) {
        console.error('Error getting win/loss statistics:', error);
        res.status(500).json({ message: 'Failed to get win/loss statistics' });
      }
    }
  );
}
