/**
 * Email Predictive Routes
 * 
 * This module defines the API routes for email predictive analytics.
 */

import { Express, Request, Response } from 'express';
import mongoose from 'mongoose';
import { authenticateUser } from '../middleware/mongo-auth-middleware';
import { checkFeatureEntitlement } from '../middleware/mongo-subscription-middleware';
import emailPredictiveService from '../services/email-predictive-service';
import { logger } from '../utils/logger';

export function registerEmailPredictiveRoutes(app: Express) {
  /**
   * Get email open probability for a contact
   */
  app.get('/api/email-predictive/open-probability/:contactId',
    authenticateUser,
    checkFeatureEntitlement('email.predictive'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { contactId } = req.params;
        const { templateId } = req.query;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get open probability
        const prediction = await emailPredictiveService.predictOpenProbability(
          tenantId,
          contactId,
          templateId as string
        );

        res.status(200).json(prediction);
      } catch (error) {
        logger.error(`Error getting open probability for contact ${req.params.contactId}:`, error);
        res.status(500).json({ message: 'Failed to get open probability' });
      }
    }
  );

  /**
   * Get best time to send emails
   */
  app.get('/api/email-predictive/best-time',
    authenticateUser,
    checkFeatureEntitlement('email.predictive'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get best time to send
        const bestTime = await emailPredictiveService.getBestTimeToSend(tenantId);

        res.status(200).json(bestTime);
      } catch (error) {
        logger.error('Error getting best time to send:', error);
        res.status(500).json({ message: 'Failed to get best time to send' });
      }
    }
  );

  /**
   * Get best templates for a contact
   */
  app.get('/api/email-predictive/best-templates/:contactId',
    authenticateUser,
    checkFeatureEntitlement('email.predictive'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { contactId } = req.params;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get best templates
        const templates = await emailPredictiveService.getBestTemplatesForContact(
          tenantId,
          contactId
        );

        res.status(200).json(templates);
      } catch (error) {
        logger.error(`Error getting best templates for contact ${req.params.contactId}:`, error);
        res.status(500).json({ message: 'Failed to get best templates' });
      }
    }
  );

  /**
   * Get AI-powered recommendations
   */
  app.get('/api/email-predictive/recommendations',
    authenticateUser,
    checkFeatureEntitlement('email.predictive'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        // Get recommendations
        const recommendations = await emailPredictiveService.generateRecommendations(tenantId);

        res.status(200).json(recommendations);
      } catch (error) {
        logger.error('Error getting recommendations:', error);
        res.status(500).json({ message: 'Failed to get recommendations' });
      }
    }
  );

  /**
   * Generate contact predictions
   */
  app.post('/api/email-predictive/contact-predictions',
    authenticateUser,
    checkFeatureEntitlement('email.predictive'),
    async (req: Request, res: Response) => {
      try {
        const tenantId = req.headers['x-tenant-id'] as string;
        const { contactIds } = req.body;

        if (!tenantId) {
          return res.status(400).json({ message: 'Tenant ID is required' });
        }

        if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
          return res.status(400).json({ message: 'Contact IDs are required' });
        }

        // Generate predictions
        const predictions = await emailPredictiveService.generateContactPredictions(
          tenantId,
          contactIds
        );

        res.status(200).json(predictions);
      } catch (error) {
        logger.error('Error generating contact predictions:', error);
        res.status(500).json({ message: 'Failed to generate contact predictions' });
      }
    }
  );
}

export default registerEmailPredictiveRoutes;
