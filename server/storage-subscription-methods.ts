import {
  SubscriptionPlan, InsertSubscriptionPlan,
  TenantSubscription, InsertTenantSubscription,
  Feature, InsertFeature,
  TenantUsage, InsertTenantUsage,
  Tenant, InsertTenant,
  UserTenant, InsertUserTenant
} from "@shared/subscription-schema";

/**
 * Subscription-related methods for the MemStorage class
 * These methods should be included in the MemStorage class in storage.ts
 */
export class MemStorageSubscriptionMethods {
  // Maps for subscription-related data
  protected subscriptionPlans: Map<number, SubscriptionPlan>;
  protected features: Map<number, Feature>;
  protected tenantSubscriptions: Map<number, TenantSubscription>;
  protected tenantUsages: Map<number, TenantUsage>;
  protected tenants: Map<number, Tenant>;
  protected userTenants: Map<number, UserTenant>;
  protected subscriptionEvents: Map<number, { id: number, type: string, payload: any, timestamp: Date }>;

  // Counters for generating IDs
  protected subscriptionPlanIdCounter: number;
  protected featureIdCounter: number;
  protected tenantSubscriptionIdCounter: number;
  protected tenantUsageIdCounter: number;
  protected tenantIdCounter: number;
  protected userTenantIdCounter: number;
  protected subscriptionEventIdCounter: number;

  constructor() {
    // Initialize subscription-related maps
    this.subscriptionPlans = new Map();
    this.features = new Map();
    this.tenantSubscriptions = new Map();
    this.tenantUsages = new Map();
    this.tenants = new Map();
    this.userTenants = new Map();
    this.subscriptionEvents = new Map();

    // Initialize counters
    this.subscriptionPlanIdCounter = 1;
    this.featureIdCounter = 1;
    this.tenantSubscriptionIdCounter = 1;
    this.tenantUsageIdCounter = 1;
    this.tenantIdCounter = 1;
    this.userTenantIdCounter = 1;
    this.subscriptionEventIdCounter = 1;
  }

  // Subscription Plan methods
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    return Array.from(this.subscriptionPlans.values())
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  async getSubscriptionPlan(id: number): Promise<SubscriptionPlan | undefined> {
    return this.subscriptionPlans.get(id);
  }

  async createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlan> {
    const id = this.subscriptionPlanIdCounter++;
    const now = new Date();
    const newPlan: SubscriptionPlan = {
      ...plan,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.subscriptionPlans.set(id, newPlan);
    return newPlan;
  }

  async updateSubscriptionPlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan | undefined> {
    const existingPlan = this.subscriptionPlans.get(id);
    if (!existingPlan) return undefined;

    const updatedPlan: SubscriptionPlan = {
      ...existingPlan,
      ...plan,
      updatedAt: new Date()
    };
    this.subscriptionPlans.set(id, updatedPlan);
    return updatedPlan;
  }

  async deleteSubscriptionPlan(id: number): Promise<boolean> {
    return this.subscriptionPlans.delete(id);
  }

  // Feature methods
  async getFeatures(): Promise<Feature[]> {
    return Array.from(this.features.values());
  }

  async getFeature(id: number): Promise<Feature | undefined> {
    return this.features.get(id);
  }

  async getFeatureByKey(key: string): Promise<Feature | undefined> {
    return Array.from(this.features.values()).find(
      (feature) => feature.key === key
    );
  }

  async createFeature(feature: InsertFeature): Promise<Feature> {
    const id = this.featureIdCounter++;
    const now = new Date();
    const newFeature: Feature = {
      ...feature,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.features.set(id, newFeature);
    return newFeature;
  }

  async updateFeature(id: number, feature: Partial<InsertFeature>): Promise<Feature | undefined> {
    const existingFeature = this.features.get(id);
    if (!existingFeature) return undefined;

    const updatedFeature: Feature = {
      ...existingFeature,
      ...feature,
      updatedAt: new Date()
    };
    this.features.set(id, updatedFeature);
    return updatedFeature;
  }

  async deleteFeature(id: number): Promise<boolean> {
    return this.features.delete(id);
  }

  // Tenant Subscription methods
  async getTenantSubscriptions(): Promise<TenantSubscription[]> {
    return Array.from(this.tenantSubscriptions.values());
  }

  async getTenantSubscription(id: number): Promise<TenantSubscription | undefined> {
    return this.tenantSubscriptions.get(id);
  }

  async getTenantSubscriptionByTenantId(tenantId: string): Promise<TenantSubscription | undefined> {
    return Array.from(this.tenantSubscriptions.values()).find(
      (subscription) => subscription.tenantId === tenantId
    );
  }

  async createTenantSubscription(subscription: InsertTenantSubscription): Promise<TenantSubscription> {
    const id = this.tenantSubscriptionIdCounter++;
    const now = new Date();
    const newSubscription: TenantSubscription = {
      ...subscription,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.tenantSubscriptions.set(id, newSubscription);
    return newSubscription;
  }

  async updateTenantSubscription(id: number, subscription: Partial<InsertTenantSubscription>): Promise<TenantSubscription | undefined> {
    const existingSubscription = this.tenantSubscriptions.get(id);
    if (!existingSubscription) return undefined;

    const updatedSubscription: TenantSubscription = {
      ...existingSubscription,
      ...subscription,
      updatedAt: new Date()
    };
    this.tenantSubscriptions.set(id, updatedSubscription);
    return updatedSubscription;
  }

  // Tenant Usage methods
  async getTenantUsages(): Promise<TenantUsage[]> {
    return Array.from(this.tenantUsages.values());
  }

  async getTenantUsage(id: number): Promise<TenantUsage | undefined> {
    return this.tenantUsages.get(id);
  }

  async getTenantUsageByPeriod(tenantId: string, period: string): Promise<TenantUsage | undefined> {
    return Array.from(this.tenantUsages.values()).find(
      (usage) => usage.tenantId === tenantId && usage.period === period
    );
  }

  async createTenantUsage(usage: InsertTenantUsage): Promise<TenantUsage> {
    const id = this.tenantUsageIdCounter++;
    const now = new Date();
    const newUsage: TenantUsage = {
      ...usage,
      id,
      updatedAt: now
    };
    this.tenantUsages.set(id, newUsage);
    return newUsage;
  }

  async updateTenantUsage(id: number, usage: Partial<InsertTenantUsage>): Promise<TenantUsage | undefined> {
    const existingUsage = this.tenantUsages.get(id);
    if (!existingUsage) return undefined;

    const updatedUsage: TenantUsage = {
      ...existingUsage,
      ...usage,
      updatedAt: new Date()
    };
    this.tenantUsages.set(id, updatedUsage);
    return updatedUsage;
  }

  // Tenant methods
  async getTenants(): Promise<Tenant[]> {
    return Array.from(this.tenants.values());
  }

  async getTenant(id: number): Promise<Tenant | undefined> {
    return this.tenants.get(id);
  }

  async getTenantBySlug(slug: string): Promise<Tenant | undefined> {
    return Array.from(this.tenants.values()).find(
      (tenant) => tenant.slug === slug
    );
  }

  async createTenant(tenant: InsertTenant): Promise<Tenant> {
    const id = this.tenantIdCounter++;
    const now = new Date();
    const newTenant: Tenant = {
      ...tenant,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.tenants.set(id, newTenant);
    return newTenant;
  }

  async updateTenant(id: number, tenant: Partial<InsertTenant>): Promise<Tenant | undefined> {
    const existingTenant = this.tenants.get(id);
    if (!existingTenant) return undefined;

    const updatedTenant: Tenant = {
      ...existingTenant,
      ...tenant,
      updatedAt: new Date()
    };
    this.tenants.set(id, updatedTenant);
    return updatedTenant;
  }

  // User Tenant methods
  async getUserTenants(userId: number): Promise<UserTenant[]> {
    return Array.from(this.userTenants.values()).filter(
      (userTenant) => userTenant.userId === userId
    );
  }

  async getTenantUsers(tenantId: number): Promise<UserTenant[]> {
    return Array.from(this.userTenants.values()).filter(
      (userTenant) => userTenant.tenantId === tenantId
    );
  }

  async createUserTenant(userTenant: InsertUserTenant): Promise<UserTenant> {
    const id = this.userTenantIdCounter++;
    const now = new Date();
    const newUserTenant: UserTenant = {
      ...userTenant,
      id,
      createdAt: now
    };
    this.userTenants.set(id, newUserTenant);
    return newUserTenant;
  }

  async updateUserTenant(id: number, userTenant: Partial<InsertUserTenant>): Promise<UserTenant | undefined> {
    const existingUserTenant = this.userTenants.get(id);
    if (!existingUserTenant) return undefined;

    const updatedUserTenant: UserTenant = {
      ...existingUserTenant,
      ...userTenant
    };
    this.userTenants.set(id, updatedUserTenant);
    return updatedUserTenant;
  }

  // Subscription Event methods
  async createSubscriptionEvent(event: { type: string, payload: any }): Promise<{ id: number, type: string, payload: any, timestamp: Date }> {
    const id = this.subscriptionEventIdCounter++;
    const now = new Date();
    const newEvent = {
      id,
      type: event.type,
      payload: event.payload,
      timestamp: now
    };
    this.subscriptionEvents.set(id, newEvent);
    return newEvent;
  }
}
