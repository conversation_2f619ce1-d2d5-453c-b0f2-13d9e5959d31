/**
 * Type Guards
 *
 * This file contains type guard functions that help with type safety
 * by checking if a value is of a specific type at runtime.
 */
import mongoose from 'mongoose';
import {
  IContact,
  ICompany,
  IOpportunity,
  IUser,
  IObjection,
  IObjectionResponse,
  IActivity,
  IDocument,
  ITask,
  IProposal,
  IEmailTracking,
  ISequence
} from '../models/mongoose';
import {
  MongoDocument,
  ObjectionDocument,
  ObjectionResponseDocument,
  ContactDocument,
  CompanyDocument,
  OpportunityDocument,
  ActivityDocument,
  UserDocument,
  DocumentDocument,
  TaskDocument,
  ProposalDocument,
  EmailTrackingDocument,
  SequenceDocument
} from '../@types/mongoose-types';

/**
 * Type guard for checking if a value is defined (not null or undefined)
 *
 * @param value The value to check
 * @returns True if the value is defined
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Type guard for checking if a value is a string
 *
 * @param value The value to check
 * @returns True if the value is a string
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * Type guard for checking if a value is a number
 *
 * @param value The value to check
 * @returns True if the value is a number
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * Type guard for checking if a value is a boolean
 *
 * @param value The value to check
 * @returns True if the value is a boolean
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * Type guard for checking if a value is an array
 *
 * @param value The value to check
 * @returns True if the value is an array
 */
export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value);
}

/**
 * Type guard for checking if a value is an object
 *
 * @param value The value to check
 * @returns True if the value is an object
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Type guard for checking if a value is a Date
 *
 * @param value The value to check
 * @returns True if the value is a Date
 */
export function isDate(value: unknown): value is Date {
  return value instanceof Date && !isNaN(value.getTime());
}

/**
 * Type guard for checking if a value is a valid ISO date string
 *
 * @param value The value to check
 * @returns True if the value is a valid ISO date string
 */
export function isISODateString(value: unknown): value is string {
  if (!isString(value)) return false;
  const date = new Date(value);
  return !isNaN(date.getTime()) && value.includes('T');
}

/**
 * Type guard for checking if a value is a MongoDB ObjectId
 *
 * @param value The value to check
 * @returns True if the value is a MongoDB ObjectId
 */
export function isObjectId(value: unknown): value is mongoose.Types.ObjectId {
  return value instanceof mongoose.Types.ObjectId;
}

/**
 * Type guard for checking if a value is a valid MongoDB ObjectId string
 *
 * @param value The value to check
 * @returns True if the value is a valid MongoDB ObjectId string
 */
export function isObjectIdString(value: unknown): value is string {
  return isString(value) && /^[0-9a-fA-F]{24}$/.test(value);
}

/**
 * Type guard for checking if a value is a Contact
 *
 * @param value The value to check
 * @returns True if the value is a Contact
 */
export function isContact(value: unknown): value is IContact {
  return (
    isObject(value) &&
    isString((value as IContact).firstName) &&
    isString((value as IContact).lastName) &&
    (isDefined((value as IContact).email) ? isString((value as IContact).email) : true)
  );
}

/**
 * Type guard for checking if a value is a Company
 *
 * @param value The value to check
 * @returns True if the value is a Company
 */
export function isCompany(value: unknown): value is ICompany {
  return (
    isObject(value) &&
    isString((value as ICompany).name)
  );
}

/**
 * Type guard for checking if a value is an Opportunity
 *
 * @param value The value to check
 * @returns True if the value is an Opportunity
 */
export function isOpportunity(value: unknown): value is IOpportunity {
  return (
    isObject(value) &&
    isString((value as IOpportunity).name) &&
    isDefined((value as IOpportunity).amount) &&
    isNumber((value as IOpportunity).amount)
  );
}

/**
 * Type guard for checking if a value is a User
 *
 * @param value The value to check
 * @returns True if the value is a User
 */
export function isUser(value: unknown): value is IUser {
  return (
    isObject(value) &&
    isString((value as IUser).email)
  );
}

/**
 * Type guard to check if an object is a MongoDB document
 * @param obj The object to check
 * @returns True if the object is a MongoDB document
 */
export function isMongoDocument<T>(obj: any): obj is MongoDocument<T> {
  return obj &&
    typeof obj === 'object' &&
    obj._id !== undefined &&
    typeof obj.toObject === 'function' &&
    typeof obj.save === 'function';
}

/**
 * Get the ID of a MongoDB document as a string
 * @param doc The MongoDB document
 * @returns The ID as a string
 */
export function getDocumentId<T>(doc: MongoDocument<T>): string {
  return doc._id.toString();
}

/**
 * Type guard to check if an object is an Error
 * @param obj The object to check
 * @returns True if the object is an Error
 */
export function isError(obj: any): obj is Error {
  return obj instanceof Error ||
    (obj && typeof obj === 'object' && 'message' in obj && 'name' in obj);
}

/**
 * Type guard to check if an object is an Objection document
 * @param obj The object to check
 * @returns True if the object is an Objection document
 */
export function isObjectionDocument(obj: any): obj is ObjectionDocument {
  return isMongoDocument<IObjection>(obj) &&
    typeof obj.name === 'string' &&
    typeof obj.category === 'string';
}

/**
 * Type guard to check if an object is an ObjectionResponse document
 * @param obj The object to check
 * @returns True if the object is an ObjectionResponse document
 */
export function isObjectionResponseDocument(obj: any): obj is ObjectionResponseDocument {
  return isMongoDocument<IObjectionResponse>(obj) &&
    obj.objectionId !== undefined &&
    typeof obj.response === 'string';
}

/**
 * Type guard to check if an object is a Contact document
 * @param obj The object to check
 * @returns True if the object is a Contact document
 */
export function isContactDocument(obj: any): obj is ContactDocument {
  return isMongoDocument<IContact>(obj) &&
    typeof obj.firstName === 'string' &&
    typeof obj.lastName === 'string';
}

/**
 * Type guard to check if an object is a Company document
 * @param obj The object to check
 * @returns True if the object is a Company document
 */
export function isCompanyDocument(obj: any): obj is CompanyDocument {
  return isMongoDocument<ICompany>(obj) &&
    typeof obj.name === 'string';
}

/**
 * Type guard to check if an object is an Opportunity document
 * @param obj The object to check
 * @returns True if the object is an Opportunity document
 */
export function isOpportunityDocument(obj: any): obj is OpportunityDocument {
  return isMongoDocument<IOpportunity>(obj) &&
    typeof obj.name === 'string' &&
    typeof obj.value === 'number';
}

/**
 * Type guard to check if an object is an Activity document
 * @param obj The object to check
 * @returns True if the object is an Activity document
 */
export function isActivityDocument(obj: any): obj is ActivityDocument {
  return isMongoDocument<IActivity>(obj) &&
    typeof obj.type === 'string' &&
    typeof obj.title === 'string';
}

/**
 * Type guard to check if an object is a Document document
 * @param obj The object to check
 * @returns True if the object is a Document document
 */
export function isDocumentDocument(obj: any): obj is DocumentDocument {
  return isMongoDocument<IDocument>(obj) &&
    typeof obj.name === 'string' &&
    typeof obj.fileType === 'string' &&
    typeof obj.url === 'string';
}

/**
 * Type guard to check if an object is a Task document
 * @param obj The object to check
 * @returns True if the object is a Task document
 */
export function isTaskDocument(obj: any): obj is TaskDocument {
  return isMongoDocument<ITask>(obj) &&
    typeof obj.title === 'string' &&
    typeof obj.status === 'string';
}

/**
 * Type guard to check if an object is a Proposal document
 * @param obj The object to check
 * @returns True if the object is a Proposal document
 */
export function isProposalDocument(obj: any): obj is ProposalDocument {
  return isMongoDocument<IProposal>(obj) &&
    typeof obj.name === 'string' &&
    typeof obj.status === 'string';
}

/**
 * Type guard to check if an object is an EmailTracking document
 * @param obj The object to check
 * @returns True if the object is an EmailTracking document
 */
export function isEmailTrackingDocument(obj: any): obj is EmailTrackingDocument {
  return isMongoDocument<IEmailTracking>(obj) &&
    typeof obj.subject === 'string' &&
    typeof obj.status === 'string' &&
    Array.isArray(obj.events);
}

/**
 * Type guard to check if an object is a Sequence document
 * @param obj The object to check
 * @returns True if the object is a Sequence document
 */
export function isSequenceDocument(obj: any): obj is SequenceDocument {
  return isMongoDocument<ISequence>(obj) &&
    typeof obj.name === 'string' &&
    typeof obj.description === 'string';
}

/**
 * Convert a string or ObjectId to an ObjectId
 *
 * @param id The ID to convert
 * @returns The ObjectId
 * @throws Error if the ID is invalid
 */
export function toObjectId(id: string | mongoose.Types.ObjectId): mongoose.Types.ObjectId {
  if (isObjectId(id)) return id;
  if (isObjectIdString(id)) return new mongoose.Types.ObjectId(id);
  throw new Error(`Invalid ObjectId: ${id}`);
}

/**
 * Safely access a property of an object with type checking
 *
 * @param obj The object to access
 * @param key The key to access
 * @param typeGuard The type guard function to use
 * @param defaultValue The default value to return if the property is not of the expected type
 * @returns The property value or the default value
 */
export function safeGet<T, K extends keyof T, V>(
  obj: T,
  key: K,
  typeGuard: (value: unknown) => value is V,
  defaultValue: V
): V {
  const value = obj[key];
  return typeGuard(value) ? value : defaultValue;
}
