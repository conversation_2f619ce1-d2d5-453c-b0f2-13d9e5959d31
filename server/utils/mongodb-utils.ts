/**
 * MongoDB utility functions for handling ObjectId conversions and other MongoDB-specific operations
 */

import { ObjectId } from 'mongodb';

/**
 * Checks if a string is a valid MongoDB ObjectId
 * @param id The string to check
 * @returns True if the string is a valid ObjectId, false otherwise
 */
export function isValidObjectId(id: string | ObjectId): boolean {
  try {
    if (id instanceof ObjectId) return true;
    return ObjectId.isValid(id);
  } catch (error) {
    return false;
  }
}

/**
 * Converts a string ID to a MongoDB ObjectId
 * @param id The string ID to convert
 * @returns The MongoDB ObjectId
 * @throws Error if the ID is not a valid ObjectId
 */
export function toObjectId(id: string | ObjectId): ObjectId {
  if (id instanceof ObjectId) return id;
  
  if (!isValidObjectId(id)) {
    throw new Error(`Invalid ObjectId: ${id}`);
  }
  
  return new ObjectId(id);
}

/**
 * Converts a MongoDB ObjectId to a string
 * @param id The ObjectId to convert
 * @returns The string representation of the ObjectId
 */
export function fromObjectId(id: ObjectId | string | unknown): string {
  if (id instanceof ObjectId) {
    return id.toString();
  }
  
  if (typeof id === 'string') {
    return id;
  }
  
  if (id && typeof id === 'object' && '_id' in id && id._id instanceof ObjectId) {
    return id._id.toString();
  }
  
  if (id && typeof id === 'object' && 'toString' in id && typeof id.toString === 'function') {
    return id.toString();
  }
  
  throw new Error(`Cannot convert to string: ${id}`);
}

/**
 * Converts a filter object's _id field from string to ObjectId
 * @param filter The filter object
 * @returns The filter with _id converted to ObjectId
 */
export function convertFilterId<T extends Record<string, any>>(filter: T): T {
  if (!filter) return filter;
  
  const result = { ...filter };
  
  if ('_id' in result && typeof result._id === 'string') {
    result._id = toObjectId(result._id);
  }
  
  return result;
}

/**
 * Converts an array of string IDs to MongoDB ObjectIds
 * @param ids Array of string IDs
 * @returns Array of MongoDB ObjectIds
 */
export function toObjectIds(ids: string[]): ObjectId[] {
  return ids.map(id => toObjectId(id));
}

/**
 * Converts an array of MongoDB ObjectIds to strings
 * @param ids Array of MongoDB ObjectIds
 * @returns Array of strings
 */
export function fromObjectIds(ids: ObjectId[]): string[] {
  return ids.map(id => fromObjectId(id));
}
