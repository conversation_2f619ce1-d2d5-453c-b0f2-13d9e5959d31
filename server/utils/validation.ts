/**
 * Validation utilities for the Aizako CRM project
 */

import { Request, Response, NextFunction } from 'express';
import { ZodSchema } from 'zod';

/**
 * Validate request body against a Zod schema
 */
export function validateRequest(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedBody = schema.parse(req.body);
      (req as any).validatedBody = validatedBody;
      next();
    } catch (error: any) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors || error.message,
      });
    }
  };
}

/**
 * Validate query parameters against a Zod schema
 */
export function validateQuery(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedQuery = schema.parse(req.query);
      (req as any).validatedQuery = validatedQuery;
      next();
    } catch (error: any) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors || error.message,
      });
    }
  };
}

/**
 * Validate URL parameters against a Zod schema
 */
export function validateParams(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedParams = schema.parse(req.params);
      (req as any).validatedParams = validatedParams;
      next();
    } catch (error: any) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors || error.message,
      });
    }
  };
}
