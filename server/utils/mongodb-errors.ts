/**
 * MongoDB error handling utilities
 * 
 * This file contains error classes and utilities for handling MongoDB errors.
 */
import { logger } from './logger';

/**
 * Base class for MongoDB errors
 */
export class MongoDBError extends Error {
  /**
   * Original error
   */
  public originalError?: Error;

  /**
   * Create a new MongoDB error
   * @param message Error message
   * @param originalError Original error
   */
  constructor(message: string, originalError?: Error) {
    super(message);
    this.name = 'MongoDBError';
    this.originalError = originalError;
  }
}

/**
 * Error thrown when a document is not found
 */
export class DocumentNotFoundError extends MongoDBError {
  /**
   * Document ID
   */
  public id: string;

  /**
   * Entity name
   */
  public entityName: string;

  /**
   * Create a new document not found error
   * @param entityName Entity name
   * @param id Document ID
   * @param originalError Original error
   */
  constructor(entityName: string, id: string, originalError?: Error) {
    super(`${entityName} with ID ${id} not found`, originalError);
    this.name = 'DocumentNotFoundError';
    this.id = id;
    this.entityName = entityName;
  }
}

/**
 * Error thrown when a duplicate key is detected
 */
export class DuplicateKeyError extends MongoDBError {
  /**
   * Key that caused the duplicate
   */
  public key: string;

  /**
   * Value that caused the duplicate
   */
  public value: any;

  /**
   * Entity name
   */
  public entityName: string;

  /**
   * Create a new duplicate key error
   * @param entityName Entity name
   * @param key Key that caused the duplicate
   * @param value Value that caused the duplicate
   * @param originalError Original error
   */
  constructor(entityName: string, key: string, value: any, originalError?: Error) {
    super(`Duplicate key error: ${entityName} with ${key}=${value} already exists`, originalError);
    this.name = 'DuplicateKeyError';
    this.key = key;
    this.value = value;
    this.entityName = entityName;
  }
}

/**
 * Error thrown when a validation error occurs
 */
export class ValidationError extends MongoDBError {
  /**
   * Validation errors
   */
  public errors: Record<string, string>;

  /**
   * Entity name
   */
  public entityName: string;

  /**
   * Create a new validation error
   * @param entityName Entity name
   * @param errors Validation errors
   * @param originalError Original error
   */
  constructor(entityName: string, errors: Record<string, string>, originalError?: Error) {
    super(`Validation error for ${entityName}: ${Object.values(errors).join(', ')}`, originalError);
    this.name = 'ValidationError';
    this.errors = errors;
    this.entityName = entityName;
  }
}

/**
 * Error thrown when a connection error occurs
 */
export class ConnectionError extends MongoDBError {
  /**
   * Create a new connection error
   * @param message Error message
   * @param originalError Original error
   */
  constructor(message: string, originalError?: Error) {
    super(`MongoDB connection error: ${message}`, originalError);
    this.name = 'ConnectionError';
  }
}

/**
 * Convert a MongoDB error to an application error
 * @param error Error to convert
 * @param entityName Entity name
 * @returns Converted error
 */
export function convertMongoDBError(error: any, entityName: string): Error {
  // Log the original error
  logger.error(`MongoDB error for ${entityName}:`, error);

  // Check for specific error types
  if (error.name === 'DocumentNotFoundError') {
    return error;
  }

  // Check for duplicate key error (code 11000)
  if (error.code === 11000 || error.code === 11001) {
    // Extract the key and value from the error message
    const keyValueMatch = error.message.match(/index:\s+(?:.*\$)?([^\s_]+)(?:_\d+)?\s+dup key:\s+{\s+:\s+"?([^"]+)"?\s+}/);
    const key = keyValueMatch ? keyValueMatch[1] : 'unknown';
    const value = keyValueMatch ? keyValueMatch[2] : 'unknown';
    return new DuplicateKeyError(entityName, key, value, error);
  }

  // Check for validation error
  if (error.name === 'ValidationError') {
    const errors: Record<string, string> = {};
    
    // Extract validation errors
    if (error.errors) {
      Object.keys(error.errors).forEach(key => {
        errors[key] = error.errors[key].message;
      });
    }
    
    return new ValidationError(entityName, errors, error);
  }

  // Check for connection error
  if (error.name === 'MongoNetworkError' || error.name === 'MongoServerSelectionError') {
    return new ConnectionError(error.message, error);
  }

  // Default to generic MongoDB error
  return new MongoDBError(`Error performing operation on ${entityName}: ${error.message}`, error);
}

/**
 * Handle a MongoDB error
 * @param error Error to handle
 * @param entityName Entity name
 * @throws Converted error
 */
export function handleMongoDBError(error: any, entityName: string): never {
  throw convertMongoDBError(error, entityName);
}
