/**
 * Subscription client for managing tenant subscriptions
 */
import axios from 'axios';
import { getFirestore } from 'firebase-admin/firestore';

// Subscription plan types
export enum SubscriptionPlan {
  FREE = 'free',
  STARTER = 'starter',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise'
}

// Feature access levels
export enum FeatureAccess {
  NONE = 'none',
  BASIC = 'basic',
  STANDARD = 'standard',
  ADVANCED = 'advanced',
  UNLIMITED = 'unlimited'
}

// Feature types
export enum FeatureType {
  CONTACTS = 'contacts',
  COMPANIES = 'companies',
  OPPORTUNITIES = 'opportunities',
  EMAIL_TRACKING = 'email_tracking',
  ANALYTICS = 'analytics',
  AI_FEATURES = 'ai_features',
  WORKFLOW_AUTOMATION = 'workflow_automation',
  API_ACCESS = 'api_access',
  INTEGRATIONS = 'integrations',
  STORAGE = 'storage',
  USERS = 'users'
}

// Subscription details interface
export interface SubscriptionDetails {
  plan: SubscriptionPlan;
  status: 'active' | 'canceled' | 'past_due' | 'trialing';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  features: Record<FeatureType, FeatureAccess>;
  limits: {
    contacts: number;
    companies: number;
    opportunities: number;
    storage: number; // in MB
    users: number;
    aiCredits: number;
  };
  customFeatures?: Record<string, any>;
}

/**
 * Subscription client for managing tenant subscriptions
 */
export class SubscriptionClient {
  private static instance: SubscriptionClient;
  private subscriptionCache: Map<string, { details: SubscriptionDetails; expiresAt: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get singleton instance
   */
  public static getInstance(): SubscriptionClient {
    if (!SubscriptionClient.instance) {
      SubscriptionClient.instance = new SubscriptionClient();
    }
    return SubscriptionClient.instance;
  }

  /**
   * Get subscription details for a tenant
   * @param tenantId Tenant ID
   */
  public async getSubscriptionDetails(tenantId: string): Promise<SubscriptionDetails> {
    // Check cache first
    const cached = this.subscriptionCache.get(tenantId);
    if (cached && cached.expiresAt > Date.now()) {
      return cached.details;
    }

    try {
      // Get subscription from Firestore
      const db = getFirestore();
      const tenantDoc = await db.collection('tenants').doc(tenantId).get();
      
      if (!tenantDoc.exists) {
        throw new Error(`Tenant ${tenantId} not found`);
      }
      
      const tenantData = tenantDoc.data();
      
      // Get subscription details
      const subscriptionId = tenantData?.subscriptionId;
      
      if (!subscriptionId) {
        // Return free plan if no subscription
        const freePlan = this.getFreePlan();
        this.cacheSubscription(tenantId, freePlan);
        return freePlan;
      }
      
      // Get subscription from Firestore
      const subscriptionDoc = await db.collection('subscriptions').doc(subscriptionId).get();
      
      if (!subscriptionDoc.exists) {
        // Return free plan if subscription not found
        const freePlan = this.getFreePlan();
        this.cacheSubscription(tenantId, freePlan);
        return freePlan;
      }
      
      const subscriptionData = subscriptionDoc.data();
      
      // Convert Firestore timestamps to Date objects
      const details: SubscriptionDetails = {
        plan: subscriptionData?.plan || SubscriptionPlan.FREE,
        status: subscriptionData?.status || 'active',
        currentPeriodStart: subscriptionData?.currentPeriodStart?.toDate() || new Date(),
        currentPeriodEnd: subscriptionData?.currentPeriodEnd?.toDate() || new Date(),
        cancelAtPeriodEnd: subscriptionData?.cancelAtPeriodEnd || false,
        features: subscriptionData?.features || this.getDefaultFeatures(subscriptionData?.plan),
        limits: subscriptionData?.limits || this.getDefaultLimits(subscriptionData?.plan),
        customFeatures: subscriptionData?.customFeatures
      };
      
      // Cache subscription
      this.cacheSubscription(tenantId, details);
      
      return details;
    } catch (error) {
      console.error(`Error getting subscription for tenant ${tenantId}:`, error);
      
      // Return free plan on error
      const freePlan = this.getFreePlan();
      this.cacheSubscription(tenantId, freePlan);
      return freePlan;
    }
  }

  /**
   * Check if a tenant has access to a feature
   * @param tenantId Tenant ID
   * @param feature Feature to check
   * @param requiredAccess Required access level
   */
  public async hasFeatureAccess(
    tenantId: string,
    feature: FeatureType,
    requiredAccess: FeatureAccess
  ): Promise<boolean> {
    const subscription = await this.getSubscriptionDetails(tenantId);
    
    // Get feature access level
    const accessLevel = subscription.features[feature] || FeatureAccess.NONE;
    
    // Check if access level is sufficient
    const accessLevels = [
      FeatureAccess.NONE,
      FeatureAccess.BASIC,
      FeatureAccess.STANDARD,
      FeatureAccess.ADVANCED,
      FeatureAccess.UNLIMITED
    ];
    
    const requiredLevel = accessLevels.indexOf(requiredAccess);
    const currentLevel = accessLevels.indexOf(accessLevel);
    
    return currentLevel >= requiredLevel;
  }

  /**
   * Cache subscription details
   * @param tenantId Tenant ID
   * @param details Subscription details
   */
  private cacheSubscription(tenantId: string, details: SubscriptionDetails): void {
    this.subscriptionCache.set(tenantId, {
      details,
      expiresAt: Date.now() + this.CACHE_TTL
    });
  }

  /**
   * Get default features for a subscription plan
   * @param plan Subscription plan
   */
  private getDefaultFeatures(plan: SubscriptionPlan): Record<FeatureType, FeatureAccess> {
    switch (plan) {
      case SubscriptionPlan.ENTERPRISE:
        return {
          [FeatureType.CONTACTS]: FeatureAccess.UNLIMITED,
          [FeatureType.COMPANIES]: FeatureAccess.UNLIMITED,
          [FeatureType.OPPORTUNITIES]: FeatureAccess.UNLIMITED,
          [FeatureType.EMAIL_TRACKING]: FeatureAccess.UNLIMITED,
          [FeatureType.ANALYTICS]: FeatureAccess.UNLIMITED,
          [FeatureType.AI_FEATURES]: FeatureAccess.UNLIMITED,
          [FeatureType.WORKFLOW_AUTOMATION]: FeatureAccess.UNLIMITED,
          [FeatureType.API_ACCESS]: FeatureAccess.UNLIMITED,
          [FeatureType.INTEGRATIONS]: FeatureAccess.UNLIMITED,
          [FeatureType.STORAGE]: FeatureAccess.UNLIMITED,
          [FeatureType.USERS]: FeatureAccess.UNLIMITED
        };
      case SubscriptionPlan.PROFESSIONAL:
        return {
          [FeatureType.CONTACTS]: FeatureAccess.ADVANCED,
          [FeatureType.COMPANIES]: FeatureAccess.ADVANCED,
          [FeatureType.OPPORTUNITIES]: FeatureAccess.ADVANCED,
          [FeatureType.EMAIL_TRACKING]: FeatureAccess.ADVANCED,
          [FeatureType.ANALYTICS]: FeatureAccess.ADVANCED,
          [FeatureType.AI_FEATURES]: FeatureAccess.ADVANCED,
          [FeatureType.WORKFLOW_AUTOMATION]: FeatureAccess.STANDARD,
          [FeatureType.API_ACCESS]: FeatureAccess.STANDARD,
          [FeatureType.INTEGRATIONS]: FeatureAccess.STANDARD,
          [FeatureType.STORAGE]: FeatureAccess.ADVANCED,
          [FeatureType.USERS]: FeatureAccess.ADVANCED
        };
      case SubscriptionPlan.STARTER:
        return {
          [FeatureType.CONTACTS]: FeatureAccess.STANDARD,
          [FeatureType.COMPANIES]: FeatureAccess.STANDARD,
          [FeatureType.OPPORTUNITIES]: FeatureAccess.STANDARD,
          [FeatureType.EMAIL_TRACKING]: FeatureAccess.STANDARD,
          [FeatureType.ANALYTICS]: FeatureAccess.BASIC,
          [FeatureType.AI_FEATURES]: FeatureAccess.BASIC,
          [FeatureType.WORKFLOW_AUTOMATION]: FeatureAccess.BASIC,
          [FeatureType.API_ACCESS]: FeatureAccess.BASIC,
          [FeatureType.INTEGRATIONS]: FeatureAccess.BASIC,
          [FeatureType.STORAGE]: FeatureAccess.STANDARD,
          [FeatureType.USERS]: FeatureAccess.STANDARD
        };
      case SubscriptionPlan.FREE:
      default:
        return {
          [FeatureType.CONTACTS]: FeatureAccess.BASIC,
          [FeatureType.COMPANIES]: FeatureAccess.BASIC,
          [FeatureType.OPPORTUNITIES]: FeatureAccess.BASIC,
          [FeatureType.EMAIL_TRACKING]: FeatureAccess.BASIC,
          [FeatureType.ANALYTICS]: FeatureAccess.NONE,
          [FeatureType.AI_FEATURES]: FeatureAccess.NONE,
          [FeatureType.WORKFLOW_AUTOMATION]: FeatureAccess.NONE,
          [FeatureType.API_ACCESS]: FeatureAccess.NONE,
          [FeatureType.INTEGRATIONS]: FeatureAccess.NONE,
          [FeatureType.STORAGE]: FeatureAccess.BASIC,
          [FeatureType.USERS]: FeatureAccess.BASIC
        };
    }
  }

  /**
   * Get default limits for a subscription plan
   * @param plan Subscription plan
   */
  private getDefaultLimits(plan: SubscriptionPlan): SubscriptionDetails['limits'] {
    switch (plan) {
      case SubscriptionPlan.ENTERPRISE:
        return {
          contacts: 100000,
          companies: 50000,
          opportunities: 50000,
          storage: 100000, // 100 GB
          users: 100,
          aiCredits: 10000
        };
      case SubscriptionPlan.PROFESSIONAL:
        return {
          contacts: 10000,
          companies: 5000,
          opportunities: 5000,
          storage: 10000, // 10 GB
          users: 25,
          aiCredits: 1000
        };
      case SubscriptionPlan.STARTER:
        return {
          contacts: 1000,
          companies: 500,
          opportunities: 500,
          storage: 1000, // 1 GB
          users: 5,
          aiCredits: 100
        };
      case SubscriptionPlan.FREE:
      default:
        return {
          contacts: 100,
          companies: 50,
          opportunities: 50,
          storage: 100, // 100 MB
          users: 2,
          aiCredits: 10
        };
    }
  }

  /**
   * Get free plan subscription details
   */
  private getFreePlan(): SubscriptionDetails {
    return {
      plan: SubscriptionPlan.FREE,
      status: 'active',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      cancelAtPeriodEnd: false,
      features: this.getDefaultFeatures(SubscriptionPlan.FREE),
      limits: this.getDefaultLimits(SubscriptionPlan.FREE)
    };
  }
}
