/**
 * User repository implementation
 *
 * This repository handles data access for users.
 */
import { MongoDBRepository, DatabaseError } from './mongodb-repository';
import { User, IUser } from '../models/mongoose/user-model';
import { QueryOptions, TypedFilter, PaginatedResult } from '../types/query';
import { logger } from '../utils/logger';
import { isObjectId, toObjectId } from '../@types/mongoose';
import mongoose from 'mongoose';
import bcrypt from 'bcrypt';

/**
 * User repository interface
 */
export interface IUserRepository {
  /**
   * Find a user by email
   * @param email Email address
   * @param tenantId Tenant ID
   * @returns User or null if not found
   * @throws DatabaseError if an error occurs
   */
  findByEmail(email: string, tenantId: string): Promise<IUser | null>;

  /**
   * Find a user by username
   * @param username Username
   * @param tenantId Tenant ID
   * @returns User or null if not found
   * @throws DatabaseError if an error occurs
   */
  findByUsername(username: string, tenantId: string): Promise<IUser | null>;

  /**
   * Find users by role
   * @param role Role to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of users
   * @throws DatabaseError if an error occurs
   */
  findByRole(role: string, tenantId: string, options?: QueryOptions<IUser>): Promise<IUser[]>;

  /**
   * Search users
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  search(query: string, tenantId: string, options?: QueryOptions<IUser>): Promise<PaginatedResult<IUser>>;

  /**
   * Update user password
   * @param userId User ID
   * @param currentPassword Current password
   * @param newPassword New password
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found or password is incorrect
   * @throws DatabaseError if an error occurs
   */
  updatePassword(userId: string, currentPassword: string, newPassword: string, tenantId: string): Promise<IUser | null>;

  /**
   * Update user preferences
   * @param userId User ID
   * @param preferences User preferences
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found
   * @throws DatabaseError if an error occurs
   */
  updatePreferences(userId: string, preferences: Record<string, any>, tenantId: string): Promise<IUser | null>;

  /**
   * Update user integrations
   * @param userId User ID
   * @param integrations User integrations
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found
   * @throws DatabaseError if an error occurs
   */
  updateIntegrations(userId: string, integrations: Record<string, any>, tenantId: string): Promise<IUser | null>;

  /**
   * Update user last login
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found
   * @throws DatabaseError if an error occurs
   */
  updateLastLogin(userId: string, tenantId: string): Promise<IUser | null>;
}

/**
 * User repository implementation
 */
export class UserRepository extends MongoDBRepository<IUser, mongoose.Document & IUser> implements IUserRepository {
  /**
   * Create a new user repository
   */
  constructor() {
    super(User, 'User');
  }

  /**
   * Find a user by email
   * @param email Email address
   * @param tenantId Tenant ID
   * @returns User or null if not found
   * @throws DatabaseError if an error occurs
   */
  async findByEmail(email: string, tenantId: string): Promise<IUser | null> {
    try {
      // Validate inputs
      if (!email) {
        throw new DatabaseError('Email is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findOne method
      return await this.findOne(tenantId, {
        email: email.toLowerCase()
      });
    } catch (error) {
      logger.error('Error finding user by email:', { error, email, tenantId });
      throw new DatabaseError(
        `Error finding user by email: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { email, tenantId }
      );
    }
  }

  /**
   * Find a user by username
   * @param username Username
   * @param tenantId Tenant ID
   * @returns User or null if not found
   * @throws DatabaseError if an error occurs
   */
  async findByUsername(username: string, tenantId: string): Promise<IUser | null> {
    try {
      // Validate inputs
      if (!username) {
        throw new DatabaseError('Username is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findOne method
      return await this.findOne(tenantId, {
        username: username.toLowerCase()
      });
    } catch (error) {
      logger.error('Error finding user by username:', { error, username, tenantId });
      throw new DatabaseError(
        `Error finding user by username: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { username, tenantId }
      );
    }
  }

  /**
   * Find users by role
   * @param role Role to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of users
   * @throws DatabaseError if an error occurs
   */
  async findByRole(role: string, tenantId: string, options: QueryOptions<IUser> = {}): Promise<IUser[]> {
    try {
      // Validate inputs
      if (!role) {
        throw new DatabaseError('Role is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          role
        }
      });
    } catch (error) {
      logger.error('Error finding users by role:', { error, role, tenantId });
      throw new DatabaseError(
        `Error finding users by role: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { role, tenantId, options }
      );
    }
  }

  /**
   * Search users
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  async search(query: string, tenantId: string, options: QueryOptions<IUser> = {}): Promise<PaginatedResult<IUser>> {
    try {
      // Validate inputs
      if (!query) {
        throw new DatabaseError('Search query is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Create a search filter
      const searchFilter = {
        $or: [
          { username: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
          { fullName: { $regex: query, $options: 'i' } }
        ]
      };
      
      // Use the base repository's findAllPaginated method
      return await this.findAllPaginated(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          ...searchFilter
        }
      });
    } catch (error) {
      logger.error('Error searching users:', { error, query, tenantId });
      throw new DatabaseError(
        `Error searching users: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { query, tenantId, options }
      );
    }
  }

  /**
   * Update user password
   * @param userId User ID
   * @param currentPassword Current password
   * @param newPassword New password
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found or password is incorrect
   * @throws DatabaseError if an error occurs
   */
  async updatePassword(userId: string, currentPassword: string, newPassword: string, tenantId: string): Promise<IUser | null> {
    try {
      // Validate inputs
      if (!userId) {
        throw new DatabaseError('User ID is required');
      }
      
      if (!currentPassword) {
        throw new DatabaseError('Current password is required');
      }
      
      if (!newPassword) {
        throw new DatabaseError('New password is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(userId) ? userId : 
                      /^[0-9a-fA-F]{24}$/.test(userId) ? new mongoose.Types.ObjectId(userId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid user ID format: ${userId}`);
      }
      
      // Find the user
      const user = await User.findOne({ _id: objectId, tenantId });
      
      if (!user) {
        return null;
      }
      
      // Verify current password
      const isMatch = await user.comparePassword(currentPassword);
      
      if (!isMatch) {
        return null;
      }
      
      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      const hash = await bcrypt.hash(newPassword, salt);
      
      // Update the password
      user.password = hash;
      await user.save();
      
      // Return the updated user
      return this.toEntity(user);
    } catch (error) {
      logger.error('Error updating user password:', { error, userId, tenantId });
      throw new DatabaseError(
        `Error updating user password: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { userId, tenantId }
      );
    }
  }

  /**
   * Update user preferences
   * @param userId User ID
   * @param preferences User preferences
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found
   * @throws DatabaseError if an error occurs
   */
  async updatePreferences(userId: string, preferences: Record<string, any>, tenantId: string): Promise<IUser | null> {
    try {
      // Validate inputs
      if (!userId) {
        throw new DatabaseError('User ID is required');
      }
      
      if (!preferences) {
        throw new DatabaseError('Preferences are required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(userId) ? userId : 
                      /^[0-9a-fA-F]{24}$/.test(userId) ? new mongoose.Types.ObjectId(userId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid user ID format: ${userId}`);
      }
      
      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { preferences },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error updating user preferences:', { error, userId, tenantId });
      throw new DatabaseError(
        `Error updating user preferences: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { userId, tenantId }
      );
    }
  }

  /**
   * Update user integrations
   * @param userId User ID
   * @param integrations User integrations
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found
   * @throws DatabaseError if an error occurs
   */
  async updateIntegrations(userId: string, integrations: Record<string, any>, tenantId: string): Promise<IUser | null> {
    try {
      // Validate inputs
      if (!userId) {
        throw new DatabaseError('User ID is required');
      }
      
      if (!integrations) {
        throw new DatabaseError('Integrations are required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(userId) ? userId : 
                      /^[0-9a-fA-F]{24}$/.test(userId) ? new mongoose.Types.ObjectId(userId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid user ID format: ${userId}`);
      }
      
      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { integrations },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error updating user integrations:', { error, userId, tenantId });
      throw new DatabaseError(
        `Error updating user integrations: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { userId, tenantId }
      );
    }
  }

  /**
   * Update user last login
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Updated user or null if not found
   * @throws DatabaseError if an error occurs
   */
  async updateLastLogin(userId: string, tenantId: string): Promise<IUser | null> {
    try {
      // Validate inputs
      if (!userId) {
        throw new DatabaseError('User ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(userId) ? userId : 
                      /^[0-9a-fA-F]{24}$/.test(userId) ? new mongoose.Types.ObjectId(userId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid user ID format: ${userId}`);
      }
      
      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { lastLogin: new Date() },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error updating user last login:', { error, userId, tenantId });
      throw new DatabaseError(
        `Error updating user last login: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { userId, tenantId }
      );
    }
  }
}

// Create and export a singleton instance
export const userRepository = new UserRepository();

export default userRepository;
