/**
 * Opportunity repository implementation
 *
 * This repository handles data access for opportunities.
 */
import { MongoDBRepository, DatabaseError } from './mongodb-repository';
import { Opportunity, IOpportunity } from '../models/mongoose/opportunity-model';
import { QueryOptions, TypedFilter, PaginatedResult } from '../types/query';
import { logger } from '../utils/logger';
import { isObjectId, toObjectId } from '../@types/mongoose';
import mongoose from 'mongoose';

/**
 * Opportunity entity interface
 */
export interface IOpportunity {
  id: string;
  name: string;
  value: number;
  currency?: string;
  stage: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: Date;
  probability?: number;
  notes?: string;
  contactId?: string;
  companyId?: string;
  aiInsights?: Record<string, any>;
  tenantId: string;
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Deal brief interface
 */
export interface IDealBrief {
  opportunityId: string;
  summary: string;
  keyObjections: Array<{
    id: string;
    text: string;
    class: string;
    response?: string;
  }>;
  relevantActivities: Array<{
    id: string;
    type: string;
    title: string;
    date: Date;
    summary: string;
  }>;
  relevantDocuments: Array<{
    id: string;
    name: string;
    type: string;
    url?: string;
  }>;
  nextSteps: Array<{
    id: string;
    description: string;
    dueDate?: Date;
    priority: 'low' | 'medium' | 'high';
  }>;
  aiConfidence: number;
  generatedAt: Date;
}

/**
 * Stage analysis interface
 */
export interface IStageAnalysis {
  opportunityId: string;
  stage: string;
  summary: string;
  completionPercentage: number;
  keyIndicators: Array<{
    name: string;
    status: 'positive' | 'neutral' | 'negative';
    description: string;
  }>;
  suggestedActions: Array<{
    description: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  aiConfidence: number;
  generatedAt: Date;
}

/**
 * Stage transition interface
 */
export interface IStageTransition {
  id: string;
  opportunityId: string;
  fromStage: string;
  toStage: string;
  suggestedAt: Date;
  appliedAt?: Date;
  approved?: boolean;
  confidence: number;
  reasons: string[];
  tenantId: string;
}

/**
 * Opportunity repository interface
 */
export interface IOpportunityRepository {
  /**
   * Find opportunities by contact ID
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  findByContactId(contactId: string, tenantId: string, options?: QueryOptions<IOpportunity>): Promise<IOpportunity[]>;

  /**
   * Find opportunities by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  findByCompanyId(companyId: string, tenantId: string, options?: QueryOptions<IOpportunity>): Promise<IOpportunity[]>;

  /**
   * Get deal brief for an opportunity
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Deal brief
   * @throws DatabaseError if an error occurs
   */
  getDealBrief(opportunityId: string, tenantId: string): Promise<IDealBrief>;

  /**
   * Get stage analysis for an opportunity
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Stage analysis
   * @throws DatabaseError if an error occurs
   */
  getStageAnalysis(opportunityId: string, tenantId: string): Promise<IStageAnalysis>;

  /**
   * Get stage transitions for an opportunity
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Array of stage transitions
   * @throws DatabaseError if an error occurs
   */
  getStageTransitions(opportunityId: string, tenantId: string): Promise<IStageTransition[]>;

  /**
   * Find opportunities by stage
   * @param stage Stage to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  findByStage(stage: string, tenantId: string, options?: QueryOptions<IOpportunity>): Promise<IOpportunity[]>;

  /**
   * Find opportunities by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  findByOwner(ownerId: string, tenantId: string, options?: QueryOptions<IOpportunity>): Promise<IOpportunity[]>;

  /**
   * Find opportunities by value range
   * @param minValue Minimum value
   * @param maxValue Maximum value
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  findByValueRange(minValue: number, maxValue: number, tenantId: string, options?: QueryOptions<IOpportunity>): Promise<IOpportunity[]>;

  /**
   * Find opportunities by expected close date range
   * @param startDate Start date
   * @param endDate End date
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  findByExpectedCloseDateRange(startDate: Date, endDate: Date, tenantId: string, options?: QueryOptions<IOpportunity>): Promise<IOpportunity[]>;

  /**
   * Search opportunities
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  search(query: string, tenantId: string, options?: QueryOptions<IOpportunity>): Promise<PaginatedResult<IOpportunity>>;
}

/**
 * Opportunity repository implementation
 */
export class OpportunityRepository extends MongoDBRepository<IOpportunity, mongoose.Document & IOpportunity> implements IOpportunityRepository {
  /**
   * Create a new opportunity repository
   */
  constructor() {
    super(Opportunity, 'Opportunity');
  }

  /**
   * Find opportunities by contact ID
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  async findByContactId(contactId: string, tenantId: string, options: QueryOptions<IOpportunity> = {}): Promise<IOpportunity[]> {
    try {
      // Validate inputs
      if (!contactId) {
        throw new DatabaseError('Contact ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(contactId) ? contactId :
                      /^[0-9a-fA-F]{24}$/.test(contactId) ? new mongoose.Types.ObjectId(contactId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid contact ID format: ${contactId}`);
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          contactId: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding opportunities by contact ID:', { error, contactId, tenantId });
      throw new DatabaseError(
        `Error finding opportunities by contact ID: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { contactId, tenantId, options }
      );
    }
  }

  /**
   * Find opportunities by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  async findByCompanyId(companyId: string, tenantId: string, options: QueryOptions<IOpportunity> = {}): Promise<IOpportunity[]> {
    try {
      // Validate inputs
      if (!companyId) {
        throw new DatabaseError('Company ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(companyId) ? companyId :
                      /^[0-9a-fA-F]{24}$/.test(companyId) ? new mongoose.Types.ObjectId(companyId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid company ID format: ${companyId}`);
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          companyId: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding opportunities by company ID:', { error, companyId, tenantId });
      throw new DatabaseError(
        `Error finding opportunities by company ID: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { companyId, tenantId, options }
      );
    }
  }

  /**
   * Get deal brief for an opportunity
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Deal brief
   * @throws DatabaseError if an error occurs
   */
  async getDealBrief(opportunityId: string, tenantId: string): Promise<IDealBrief> {
    try {
      // Validate inputs
      if (!opportunityId) {
        throw new DatabaseError('Opportunity ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(opportunityId) ? opportunityId :
                      /^[0-9a-fA-F]{24}$/.test(opportunityId) ? new mongoose.Types.ObjectId(opportunityId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid opportunity ID format: ${opportunityId}`);
      }

      // Check if the opportunity exists
      const opportunity = await this.findById(opportunityId, tenantId);

      if (!opportunity) {
        throw new DatabaseError(`Opportunity not found: ${opportunityId}`);
      }

      // This would typically call an AI service to generate the deal brief
      // For now, we'll just return a placeholder
      const dealBrief: IDealBrief = {
        opportunityId,
        summary: 'AI-generated deal brief summary',
        keyObjections: [
          {
            id: '1',
            text: 'Price is too high',
            class: 'price',
            response: 'Our solution provides superior ROI compared to competitors'
          },
          {
            id: '2',
            text: 'Implementation timeline concerns',
            class: 'implementation'
          }
        ],
        relevantActivities: [
          {
            id: '1',
            type: 'call',
            title: 'Discovery call',
            date: new Date(),
            summary: 'Discussed requirements and timeline'
          },
          {
            id: '2',
            type: 'email',
            title: 'Follow-up email',
            date: new Date(),
            summary: 'Sent pricing information'
          }
        ],
        relevantDocuments: [
          {
            id: '1',
            name: 'Proposal.pdf',
            type: 'proposal',
            url: '/documents/proposal.pdf'
          },
          {
            id: '2',
            name: 'Requirements.docx',
            type: 'document',
            url: '/documents/requirements.docx'
          }
        ],
        nextSteps: [
          {
            id: '1',
            description: 'Schedule technical demo',
            dueDate: new Date(),
            priority: 'high'
          },
          {
            id: '2',
            description: 'Send case studies',
            dueDate: new Date(),
            priority: 'medium'
          }
        ],
        aiConfidence: 0.85,
        generatedAt: new Date()
      };

      return dealBrief;
    } catch (error) {
      logger.error('Error getting deal brief:', { error, opportunityId, tenantId });
      throw new DatabaseError(
        `Error getting deal brief: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { opportunityId, tenantId }
      );
    }
  }

  /**
   * Get stage analysis for an opportunity
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Stage analysis
   * @throws DatabaseError if an error occurs
   */
  async getStageAnalysis(opportunityId: string, tenantId: string): Promise<IStageAnalysis> {
    try {
      // Validate inputs
      if (!opportunityId) {
        throw new DatabaseError('Opportunity ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(opportunityId) ? opportunityId :
                      /^[0-9a-fA-F]{24}$/.test(opportunityId) ? new mongoose.Types.ObjectId(opportunityId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid opportunity ID format: ${opportunityId}`);
      }

      // Check if the opportunity exists
      const opportunity = await this.findById(opportunityId, tenantId);

      if (!opportunity) {
        throw new DatabaseError(`Opportunity not found: ${opportunityId}`);
      }

      // This would typically call an AI service to generate the stage analysis
      // For now, we'll just return a placeholder
      const stageAnalysis: IStageAnalysis = {
        opportunityId,
        stage: opportunity.stage,
        summary: 'AI-generated stage analysis summary',
        completionPercentage: 75,
        keyIndicators: [
          {
            name: 'Decision maker engagement',
            status: 'positive',
            description: 'Multiple meetings with key decision makers'
          },
          {
            name: 'Competitive positioning',
            status: 'neutral',
            description: 'Customer evaluating 2 other vendors'
          },
          {
            name: 'Budget alignment',
            status: 'negative',
            description: 'Current proposal exceeds customer budget'
          }
        ],
        suggestedActions: [
          {
            description: 'Revise proposal to align with budget',
            priority: 'high'
          },
          {
            description: 'Schedule meeting with finance team',
            priority: 'medium'
          }
        ],
        aiConfidence: 0.8,
        generatedAt: new Date()
      };

      return stageAnalysis;
    } catch (error) {
      logger.error('Error getting stage analysis:', { error, opportunityId, tenantId });
      throw new DatabaseError(
        `Error getting stage analysis: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { opportunityId, tenantId }
      );
    }
  }

  /**
   * Get stage transitions for an opportunity
   * @param opportunityId Opportunity ID
   * @param tenantId Tenant ID
   * @returns Array of stage transitions
   * @throws DatabaseError if an error occurs
   */
  async getStageTransitions(opportunityId: string, tenantId: string): Promise<IStageTransition[]> {
    try {
      // Validate inputs
      if (!opportunityId) {
        throw new DatabaseError('Opportunity ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(opportunityId) ? opportunityId :
                      /^[0-9a-fA-F]{24}$/.test(opportunityId) ? new mongoose.Types.ObjectId(opportunityId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid opportunity ID format: ${opportunityId}`);
      }

      // Check if the opportunity exists
      const opportunity = await this.findById(opportunityId, tenantId);

      if (!opportunity) {
        throw new DatabaseError(`Opportunity not found: ${opportunityId}`);
      }

      // This would typically query a database for stage transitions
      // For now, we'll just return placeholders
      const stageTransitions: IStageTransition[] = [
        {
          id: '1',
          opportunityId,
          fromStage: 'prospecting',
          toStage: 'qualification',
          suggestedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          appliedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
          approved: true,
          confidence: 0.9,
          reasons: [
            'All qualification criteria met',
            'Budget confirmed',
            'Decision makers identified'
          ],
          tenantId
        },
        {
          id: '2',
          opportunityId,
          fromStage: 'qualification',
          toStage: 'proposal',
          suggestedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          appliedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          approved: true,
          confidence: 0.85,
          reasons: [
            'Requirements documented',
            'Solution design completed',
            'Pricing approved'
          ],
          tenantId
        },
        {
          id: '3',
          opportunityId,
          fromStage: 'proposal',
          toStage: 'negotiation',
          suggestedAt: new Date(),
          confidence: 0.7,
          reasons: [
            'Proposal reviewed by customer',
            'Positive feedback received',
            'Customer requested pricing discussion'
          ],
          tenantId
        }
      ];

      return stageTransitions;
    } catch (error) {
      logger.error('Error getting stage transitions:', { error, opportunityId, tenantId });
      throw new DatabaseError(
        `Error getting stage transitions: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { opportunityId, tenantId }
      );
    }
  }

  /**
   * Find opportunities by stage
   * @param stage Stage to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  async findByStage(stage: string, tenantId: string, options: QueryOptions<IOpportunity> = {}): Promise<IOpportunity[]> {
    try {
      // Validate inputs
      if (!stage) {
        throw new DatabaseError('Stage is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          stage
        }
      });
    } catch (error) {
      logger.error('Error finding opportunities by stage:', { error, stage, tenantId });
      throw new DatabaseError(
        `Error finding opportunities by stage: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { stage, tenantId, options }
      );
    }
  }

  /**
   * Find opportunities by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  async findByOwner(ownerId: string, tenantId: string, options: QueryOptions<IOpportunity> = {}): Promise<IOpportunity[]> {
    try {
      // Validate inputs
      if (!ownerId) {
        throw new DatabaseError('Owner ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(ownerId) ? ownerId :
                      /^[0-9a-fA-F]{24}$/.test(ownerId) ? new mongoose.Types.ObjectId(ownerId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid owner ID format: ${ownerId}`);
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          owner: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding opportunities by owner:', { error, ownerId, tenantId });
      throw new DatabaseError(
        `Error finding opportunities by owner: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { ownerId, tenantId, options }
      );
    }
  }

  /**
   * Find opportunities by value range
   * @param minValue Minimum value
   * @param maxValue Maximum value
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  async findByValueRange(minValue: number, maxValue: number, tenantId: string, options: QueryOptions<IOpportunity> = {}): Promise<IOpportunity[]> {
    try {
      // Validate inputs
      if (typeof minValue !== 'number') {
        throw new DatabaseError('Minimum value must be a number');
      }

      if (typeof maxValue !== 'number') {
        throw new DatabaseError('Maximum value must be a number');
      }

      if (minValue > maxValue) {
        throw new DatabaseError('Minimum value cannot be greater than maximum value');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          value: {
            $gte: minValue,
            $lte: maxValue
          }
        }
      });
    } catch (error) {
      logger.error('Error finding opportunities by value range:', { error, minValue, maxValue, tenantId });
      throw new DatabaseError(
        `Error finding opportunities by value range: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { minValue, maxValue, tenantId, options }
      );
    }
  }

  /**
   * Find opportunities by expected close date range
   * @param startDate Start date
   * @param endDate End date
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of opportunities
   * @throws DatabaseError if an error occurs
   */
  async findByExpectedCloseDateRange(startDate: Date, endDate: Date, tenantId: string, options: QueryOptions<IOpportunity> = {}): Promise<IOpportunity[]> {
    try {
      // Validate inputs
      if (!(startDate instanceof Date) || isNaN(startDate.getTime())) {
        throw new DatabaseError('Start date must be a valid date');
      }

      if (!(endDate instanceof Date) || isNaN(endDate.getTime())) {
        throw new DatabaseError('End date must be a valid date');
      }

      if (startDate > endDate) {
        throw new DatabaseError('Start date cannot be after end date');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          expectedCloseDate: {
            $gte: startDate,
            $lte: endDate
          }
        }
      });
    } catch (error) {
      logger.error('Error finding opportunities by expected close date range:', { error, startDate, endDate, tenantId });
      throw new DatabaseError(
        `Error finding opportunities by expected close date range: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { startDate, endDate, tenantId, options }
      );
    }
  }

  /**
   * Search opportunities
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  async search(query: string, tenantId: string, options: QueryOptions<IOpportunity> = {}): Promise<PaginatedResult<IOpportunity>> {
    try {
      // Validate inputs
      if (!query) {
        throw new DatabaseError('Search query is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Create a search filter
      const searchFilter = {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } }
        ]
      };

      // Use the base repository's findAllPaginated method
      return await this.findAllPaginated(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          ...searchFilter
        }
      });
    } catch (error) {
      logger.error('Error searching opportunities:', { error, query, tenantId });
      throw new DatabaseError(
        `Error searching opportunities: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { query, tenantId, options }
      );
    }
  }
}

// Create and export a singleton instance
export const opportunityRepository = new OpportunityRepository();

export default opportunityRepository;
