/**
 * Document repository implementation
 *
 * This repository handles data access for documents.
 */
import { MongoDBRepository, DatabaseError } from './mongodb-repository';
import { DocumentModel, IDocument } from '../models/mongoose/document-model';
import { QueryOptions, TypedFilter, PaginatedResult } from '../types/query';
import { logger } from '../utils/logger';
import { isObjectId, toObjectId } from '../@types/mongoose';
import mongoose from 'mongoose';

/**
 * Document repository interface
 */
export interface IDocumentRepository {
  /**
   * Find documents by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  findByOwner(ownerId: string, tenantId: string, options?: QueryOptions<IDocument>): Promise<IDocument[]>;

  /**
   * Find documents by file type
   * @param fileType File type to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  findByFileType(fileType: string, tenantId: string, options?: QueryOptions<IDocument>): Promise<IDocument[]>;

  /**
   * Find documents by related entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  findByRelatedEntity(entityType: string, entityId: string, tenantId: string, options?: QueryOptions<IDocument>): Promise<IDocument[]>;

  /**
   * Find public documents
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  findPublicDocuments(tenantId: string, options?: QueryOptions<IDocument>): Promise<IDocument[]>;

  /**
   * Find documents by tag
   * @param tag Tag to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  findByTag(tag: string, tenantId: string, options?: QueryOptions<IDocument>): Promise<IDocument[]>;

  /**
   * Make a document public or private
   * @param documentId Document ID
   * @param isPublic Whether the document should be public
   * @param tenantId Tenant ID
   * @returns Updated document or null if not found
   * @throws DatabaseError if an error occurs
   */
  setDocumentPublic(documentId: string, isPublic: boolean, tenantId: string): Promise<IDocument | null>;

  /**
   * Search documents
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  search(query: string, tenantId: string, options?: QueryOptions<IDocument>): Promise<PaginatedResult<IDocument>>;
}

/**
 * Document repository implementation
 */
export class DocumentRepository extends MongoDBRepository<IDocument, mongoose.Document & IDocument> implements IDocumentRepository {
  /**
   * Create a new document repository
   */
  constructor() {
    super(DocumentModel, 'Document');
  }

  /**
   * Find documents by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  async findByOwner(ownerId: string, tenantId: string, options: QueryOptions<IDocument> = {}): Promise<IDocument[]> {
    try {
      // Validate inputs
      if (!ownerId) {
        throw new DatabaseError('Owner ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(ownerId) ? ownerId : 
                      /^[0-9a-fA-F]{24}$/.test(ownerId) ? new mongoose.Types.ObjectId(ownerId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid owner ID format: ${ownerId}`);
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          owner: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding documents by owner:', { error, ownerId, tenantId });
      throw new DatabaseError(
        `Error finding documents by owner: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { ownerId, tenantId, options }
      );
    }
  }

  /**
   * Find documents by file type
   * @param fileType File type to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  async findByFileType(fileType: string, tenantId: string, options: QueryOptions<IDocument> = {}): Promise<IDocument[]> {
    try {
      // Validate inputs
      if (!fileType) {
        throw new DatabaseError('File type is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          fileType
        }
      });
    } catch (error) {
      logger.error('Error finding documents by file type:', { error, fileType, tenantId });
      throw new DatabaseError(
        `Error finding documents by file type: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { fileType, tenantId, options }
      );
    }
  }

  /**
   * Find documents by related entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  async findByRelatedEntity(entityType: string, entityId: string, tenantId: string, options: QueryOptions<IDocument> = {}): Promise<IDocument[]> {
    try {
      // Validate inputs
      if (!entityType) {
        throw new DatabaseError('Entity type is required');
      }
      
      if (!entityId) {
        throw new DatabaseError('Entity ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(entityId) ? entityId : 
                      /^[0-9a-fA-F]{24}$/.test(entityId) ? new mongoose.Types.ObjectId(entityId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid entity ID format: ${entityId}`);
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          'relatedTo.type': entityType,
          'relatedTo.id': objectId
        }
      });
    } catch (error) {
      logger.error('Error finding documents by related entity:', { error, entityType, entityId, tenantId });
      throw new DatabaseError(
        `Error finding documents by related entity: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { entityType, entityId, tenantId, options }
      );
    }
  }

  /**
   * Find public documents
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  async findPublicDocuments(tenantId: string, options: QueryOptions<IDocument> = {}): Promise<IDocument[]> {
    try {
      // Validate inputs
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          isPublic: true
        }
      });
    } catch (error) {
      logger.error('Error finding public documents:', { error, tenantId });
      throw new DatabaseError(
        `Error finding public documents: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tenantId, options }
      );
    }
  }

  /**
   * Find documents by tag
   * @param tag Tag to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of documents
   * @throws DatabaseError if an error occurs
   */
  async findByTag(tag: string, tenantId: string, options: QueryOptions<IDocument> = {}): Promise<IDocument[]> {
    try {
      // Validate inputs
      if (!tag) {
        throw new DatabaseError('Tag is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          tags: tag
        }
      });
    } catch (error) {
      logger.error('Error finding documents by tag:', { error, tag, tenantId });
      throw new DatabaseError(
        `Error finding documents by tag: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tag, tenantId, options }
      );
    }
  }

  /**
   * Make a document public or private
   * @param documentId Document ID
   * @param isPublic Whether the document should be public
   * @param tenantId Tenant ID
   * @returns Updated document or null if not found
   * @throws DatabaseError if an error occurs
   */
  async setDocumentPublic(documentId: string, isPublic: boolean, tenantId: string): Promise<IDocument | null> {
    try {
      // Validate inputs
      if (!documentId) {
        throw new DatabaseError('Document ID is required');
      }
      
      if (typeof isPublic !== 'boolean') {
        throw new DatabaseError('isPublic must be a boolean');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(documentId) ? documentId : 
                      /^[0-9a-fA-F]{24}$/.test(documentId) ? new mongoose.Types.ObjectId(documentId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid document ID format: ${documentId}`);
      }
      
      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { isPublic },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error setting document public status:', { error, documentId, isPublic, tenantId });
      throw new DatabaseError(
        `Error setting document public status: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { documentId, isPublic, tenantId }
      );
    }
  }

  /**
   * Search documents
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  async search(query: string, tenantId: string, options: QueryOptions<IDocument> = {}): Promise<PaginatedResult<IDocument>> {
    try {
      // Validate inputs
      if (!query) {
        throw new DatabaseError('Search query is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Create a search filter
      const searchFilter = {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { tags: { $regex: query, $options: 'i' } }
        ]
      };
      
      // Use the base repository's findAllPaginated method
      return await this.findAllPaginated(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          ...searchFilter
        }
      });
    } catch (error) {
      logger.error('Error searching documents:', { error, query, tenantId });
      throw new DatabaseError(
        `Error searching documents: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { query, tenantId, options }
      );
    }
  }
}

// Create and export a singleton instance
export const documentRepository = new DocumentRepository();

export default documentRepository;
