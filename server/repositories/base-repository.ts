/**
 * Base repository interface
 *
 * This interface defines the common operations that all repositories should implement.
 * It follows the repository pattern, which abstracts the data access layer from the business logic.
 *
 * The repository pattern provides several benefits:
 * 1. Decouples business logic from data access
 * 2. Centralizes data access logic
 * 3. Makes testing easier with mock repositories
 * 4. Provides a consistent interface for data access
 */

import { QueryOptions, TypedFilter } from '../types/query';

/**
 * Repository error class for type-safe error handling
 */
export class RepositoryError extends Error {
  /**
   * Error code
   */
  public code: string;

  /**
   * Original error
   */
  public originalError?: Error;

  /**
   * Additional error details
   */
  public details?: Record<string, any>;

  /**
   * Create a new repository error
   * @param message Error message
   * @param code Error code
   * @param originalError Original error
   * @param details Additional error details
   */
  constructor(
    message: string,
    code: string = 'REPOSITORY_ERROR',
    originalError?: Error,
    details?: Record<string, any>
  ) {
    super(message);
    this.name = 'RepositoryError';
    this.code = code;
    this.originalError = originalError;
    this.details = details;
  }
}

/**
 * Pagination result interface
 *
 * @template T Entity type
 */
export interface PaginatedResult<T> {
  /**
   * Array of entities
   */
  items: T[];

  /**
   * Total number of entities
   */
  total: number;

  /**
   * Current page number
   */
  page: number;

  /**
   * Number of entities per page
   */
  limit: number;

  /**
   * Total number of pages
   */
  totalPages: number;

  /**
   * Has previous page
   */
  hasPrevPage: boolean;

  /**
   * Has next page
   */
  hasNextPage: boolean;

  /**
   * Previous page number
   */
  prevPage: number | null;

  /**
   * Next page number
   */
  nextPage: number | null;
}

/**
 * Base repository interface
 *
 * This interface defines the standard operations to be performed on a domain entity.
 * All repository implementations should implement this interface to provide
 * a consistent API for data access.
 *
 * @template T Entity type - The domain entity type this repository manages
 * @template K ID type - The type of the entity's ID (defaults to string)
 */
export interface IRepository<T, K = string> {
  /**
   * Find an entity by ID
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns Entity or null if not found
   * @throws RepositoryError if an error occurs
   */
  findById(id: K, tenantId: string): Promise<T | null>;

  /**
   * Find all entities that match the filter
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of entities
   * @throws RepositoryError if an error occurs
   */
  findAll(tenantId: string, options?: QueryOptions<T>): Promise<T[]>;

  /**
   * Find all entities that match the filter with pagination
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws RepositoryError if an error occurs
   */
  findAllPaginated(
    tenantId: string,
    options?: QueryOptions<T>
  ): Promise<PaginatedResult<T>>;

  /**
   * Create a new entity
   * @param entity Entity to create
   * @param tenantId Tenant ID
   * @returns Created entity
   * @throws RepositoryError if an error occurs
   */
  create(entity: Partial<T>, tenantId: string): Promise<T>;

  /**
   * Create multiple entities
   * @param entities Entities to create
   * @param tenantId Tenant ID
   * @returns Created entities
   * @throws RepositoryError if an error occurs
   */
  createMany(entities: Partial<T>[], tenantId: string): Promise<T[]>;

  /**
   * Update an entity
   * @param id Entity ID
   * @param entity Entity data to update
   * @param tenantId Tenant ID
   * @returns Updated entity or null if not found
   * @throws RepositoryError if an error occurs
   */
  update(id: K, entity: Partial<T>, tenantId: string): Promise<T | null>;

  /**
   * Update multiple entities
   * @param filter Filter to match entities to update
   * @param update Update to apply
   * @param tenantId Tenant ID
   * @returns Number of updated entities
   * @throws RepositoryError if an error occurs
   */
  updateMany(filter: TypedFilter<T>, update: Partial<T>, tenantId: string): Promise<number>;

  /**
   * Delete an entity
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   * @throws RepositoryError if an error occurs
   */
  delete(id: K, tenantId: string): Promise<boolean>;

  /**
   * Delete multiple entities
   * @param filter Filter to match entities to delete
   * @param tenantId Tenant ID
   * @returns Number of deleted entities
   * @throws RepositoryError if an error occurs
   */
  deleteMany(filter: TypedFilter<T>, tenantId: string): Promise<number>;

  /**
   * Count entities that match the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Count of matching entities
   * @throws RepositoryError if an error occurs
   */
  count(tenantId: string, filter?: TypedFilter<T>): Promise<number>;

  /**
   * Check if an entity exists
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if exists, false otherwise
   * @throws RepositoryError if an error occurs
   */
  exists(id: K, tenantId: string): Promise<boolean>;

  /**
   * Find one entity that matches the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Entity or null if not found
   * @throws RepositoryError if an error occurs
   */
  findOne(tenantId: string, filter: TypedFilter<T>): Promise<T | null>;

  /**
   * Find and update an entity
   * @param filter Filter to match entity
   * @param update Update to apply
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Updated entity or null if not found
   * @throws RepositoryError if an error occurs
   */
  findOneAndUpdate(
    filter: TypedFilter<T>,
    update: Partial<T>,
    tenantId: string,
    options?: { upsert?: boolean; new?: boolean }
  ): Promise<T | null>;

  /**
   * Find and delete an entity
   * @param filter Filter to match entity
   * @param tenantId Tenant ID
   * @returns Deleted entity or null if not found
   * @throws RepositoryError if an error occurs
   */
  findOneAndDelete(filter: TypedFilter<T>, tenantId: string): Promise<T | null>;
}

export default IRepository;
