/**
 * MongoDB repository implementation
 *
 * This class implements the base repository interface for MongoDB using Mongoose.
 * It provides a generic implementation that can be used for any entity.
 *
 * Key features:
 * - Implements all methods defined in the IRepository interface
 * - Uses data mappers to convert between domain entities and database models
 * - Handles errors consistently with custom error classes
 * - Enforces tenant isolation for all operations
 * - Provides logging for all database operations
 */
import mongoose, { Document, Model, FilterQuery, UpdateQuery } from 'mongoose';
import { IRepository, RepositoryError, PaginatedResult } from './base-repository';
import { QueryOptions, TypedFilter } from '../types/query';
import { logger } from '../utils/logger';
import { isObjectId, toObjectId } from '../@types/mongoose';

/**
 * Database error class
 */
export class DatabaseError extends RepositoryError {
  /**
   * Create a new database error
   * @param message Error message
   * @param originalError Original error
   * @param details Additional error details
   */
  constructor(message: string, originalError?: Error, details?: Record<string, any>) {
    super(message, 'DATABASE_ERROR', originalError, details);
    this.name = 'DatabaseError';
  }
}

/**
 * MongoDB repository implementation
 *
 * This class provides a concrete implementation of the IRepository interface for MongoDB.
 * It handles the conversion between domain entities and database models using data mappers.
 *
 * @template T Entity type - The domain entity type this repository manages
 * @template D Document type - The Mongoose document type for the database model
 */
export class MongoDBRepository<T, D extends Document> implements IRepository<T> {
  /**
   * Mongoose model
   */
  protected model: Model<D>;

  /**
   * Entity name for logging
   */
  protected entityName: string;

  /**
   * Create a new MongoDB repository
   * @param model Mongoose model
   * @param entityName Entity name for logging
   */
  constructor(model: Model<D>, entityName: string) {
    this.model = model;
    this.entityName = entityName;
  }

  /**
   * Convert a database document to a domain entity
   * @param doc Database document
   * @returns Domain entity
   */
  protected toEntity(doc: D | null): T | null {
    if (!doc) return null;

    try {
      // Default implementation if no mapper is provided
      const entity = { ...doc.toObject() } as any;

      // Convert _id to id
      if (entity._id) {
        entity.id = entity._id.toString();
        delete entity._id;
      }

      // Remove __v
      delete entity.__v;

      return entity as T;
    } catch (error) {
      logger.error(`Error converting ${this.entityName} document to entity:`, { error });
      throw new DatabaseError(
        `Error converting ${this.entityName} document to entity: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Convert a domain entity to a database document
   * @param entity Domain entity
   * @returns Database document
   */
  protected toDocument(entity: Partial<T>): Partial<D> {
    if (!entity) return {} as Partial<D>;

    try {
      // Default implementation if no mapper is provided
      const doc = { ...entity } as any;

      // Convert id to _id if present
      if (doc?.id) {
        doc._id = doc.id;
        delete doc.id;
      }

      return doc as Partial<D>;
    } catch (error) {
      logger.error(`Error converting ${this.entityName} entity to document:`, { error });
      throw new DatabaseError(
        `Error converting ${this.entityName} entity to document: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Find an entity by ID
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns Entity or null if not found
   * @throws DatabaseError if an error occurs
   */
  async findById(id: string, tenantId: string): Promise<T | null> {
    try {
      // Validate inputs
      if (!id) {
        throw new DatabaseError(`Invalid ID: ${id}`);
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(id) ? id :
                      /^[0-9a-fA-F]{24}$/.test(id) ? new mongoose.Types.ObjectId(id) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid ObjectId format: ${id}`);
      }

      // Find the document
      const doc = await this.model.findOne({
        _id: objectId,
        tenantId,
      });

      // Convert to entity
      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error finding ${this.entityName} by ID:`, { error, id, tenantId });
      throw new DatabaseError(
        `Error finding ${this.entityName} by ID: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { id, tenantId }
      );
    }
  }

  /**
   * Find all entities that match the filter
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of entities
   * @throws DatabaseError if an error occurs
   */
  async findAll(tenantId: string, options: QueryOptions<T> = {}): Promise<T[]> {
    try {
      // Validate inputs
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Extract options with defaults
      const {
        filter = {},
        sort = {},
        projection = {},
        populate = [],
        lean = false
      } = options;

      // Build the query
      const query = this.model.find({
        tenantId,
        ...filter as FilterQuery<D>,
      });

      // Apply sort if provided
      if (Object.keys(sort).length > 0) {
        query.sort(sort as Record<string, 1 | -1>);
      }

      // Apply projection if provided
      if (Object.keys(projection).length > 0) {
        query.select(projection as Record<string, 1 | 0>);
      }

      // Apply population if provided
      if (Array.isArray(populate) && populate.length > 0) {
        if (typeof populate[0] === 'string') {
          // String array format
          (populate as string[]).forEach(path => {
            query.populate(path);
          });
        } else {
          // Object format
          (populate as any[]).forEach(popObj => {
            query.populate(popObj);
          });
        }
      }

      // Apply lean option
      if (lean) {
        query.lean();
      }

      // Execute the query
      const docs = await query.exec();

      // Convert documents to entities
      return docs.map(doc => this.toEntity(doc)).filter((entity): entity is T => entity !== null);
    } catch (error) {
      logger.error(`Error finding ${this.entityName} list:`, { error, tenantId, options });
      throw new DatabaseError(
        `Error finding ${this.entityName} list: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tenantId, options }
      );
    }
  }

  /**
   * Find all entities that match the filter with pagination
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  async findAllPaginated(
    tenantId: string,
    options: QueryOptions<T> = {}
  ): Promise<PaginatedResult<T>> {
    try {
      // Validate inputs
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Extract options with defaults
      const {
        filter = {},
        sort = {},
        projection = {},
        page = 1,
        limit = 20,
        populate = [],
        lean = false,
      } = options;

      // Validate pagination parameters
      if (page < 1) {
        throw new DatabaseError(`Invalid page number: ${page}`);
      }

      if (limit < 1) {
        throw new DatabaseError(`Invalid limit: ${limit}`);
      }

      // Build the query
      const query = {
        tenantId,
        ...filter as FilterQuery<D>,
      };

      // Create the find query
      const findQuery = this.model.find(query)
        .sort(sort as Record<string, 1 | -1>)
        .select(projection as Record<string, 1 | 0>)
        .skip((page - 1) * limit)
        .limit(limit);

      // Apply population if provided
      if (Array.isArray(populate) && populate.length > 0) {
        if (typeof populate[0] === 'string') {
          // String array format
          (populate as string[]).forEach(path => {
            findQuery.populate(path);
          });
        } else {
          // Object format
          (populate as any[]).forEach(popObj => {
            findQuery.populate(popObj);
          });
        }
      }

      // Apply lean option
      if (lean) {
        findQuery.lean();
      }

      // Execute both queries in parallel
      const [docs, total] = await Promise.all([
        findQuery.exec(),
        this.model.countDocuments(query),
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasPrevPage = page > 1;
      const hasNextPage = page < totalPages;
      const prevPage = hasPrevPage ? page - 1 : null;
      const nextPage = hasNextPage ? page + 1 : null;

      // Return the paginated result
      return {
        items: docs.map(doc => this.toEntity(doc)).filter((entity): entity is T => entity !== null),
        total,
        page,
        limit,
        totalPages,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      };
    } catch (error) {
      logger.error(`Error finding paginated ${this.entityName} list:`, { error, tenantId, options });
      throw new DatabaseError(
        `Error finding paginated ${this.entityName} list: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tenantId, options }
      );
    }
  }

  /**
   * Create a new entity
   * @param entity Entity to create
   * @param tenantId Tenant ID
   * @returns Created entity
   * @throws DatabaseError if an error occurs
   */
  async create(entity: Partial<T>, tenantId: string): Promise<T> {
    try {
      // Validate inputs
      if (!entity) {
        throw new DatabaseError('Entity is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Create the document
      const doc = new this.model({
        ...this.toDocument(entity),
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Save the document
      const savedDoc = await doc.save();

      // Convert to entity
      const result = this.toEntity(savedDoc);

      if (!result) {
        throw new DatabaseError(`Failed to convert saved ${this.entityName} to entity`);
      }

      return result;
    } catch (error) {
      logger.error(`Error creating ${this.entityName}:`, { error, entity, tenantId });
      throw new DatabaseError(
        `Error creating ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { entity, tenantId }
      );
    }
  }

  /**
   * Create multiple entities
   * @param entities Entities to create
   * @param tenantId Tenant ID
   * @returns Created entities
   * @throws DatabaseError if an error occurs
   */
  async createMany(entities: Partial<T>[], tenantId: string): Promise<T[]> {
    try {
      // Validate inputs
      if (!Array.isArray(entities) || entities.length === 0) {
        throw new DatabaseError('Entities array is required and must not be empty');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Prepare documents
      const docs = entities.map(entity => ({
        ...this.toDocument(entity),
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      // Insert documents
      const result = await this.model.insertMany(docs);

      // Convert to entities
      return result
        .map(doc => this.toEntity(doc))
        .filter((entity): entity is T => entity !== null);
    } catch (error) {
      logger.error(`Error creating multiple ${this.entityName}:`, { error, count: entities?.length, tenantId });
      throw new DatabaseError(
        `Error creating multiple ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { count: entities?.length, tenantId }
      );
    }
  }

  /**
   * Update an entity
   * @param id Entity ID
   * @param entity Entity data to update
   * @param tenantId Tenant ID
   * @returns Updated entity or null if not found
   * @throws DatabaseError if an error occurs
   */
  async update(id: string, entity: Partial<T>, tenantId: string): Promise<T | null> {
    try {
      // Validate inputs
      if (!id) {
        throw new DatabaseError(`Invalid ID: ${id}`);
      }

      if (!entity) {
        throw new DatabaseError('Entity is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(id) ? id :
                      /^[0-9a-fA-F]{24}$/.test(id) ? new mongoose.Types.ObjectId(id) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid ObjectId format: ${id}`);
      }

      // Update the document
      const doc = await this.model.findOneAndUpdate(
        { _id: objectId, tenantId },
        {
          $set: {
            ...this.toDocument(entity),
            updatedAt: new Date()
          }
        },
        { new: true }
      );

      // Convert to entity
      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error updating ${this.entityName}:`, { error, id, entity, tenantId });
      throw new DatabaseError(
        `Error updating ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { id, entity, tenantId }
      );
    }
  }

  /**
   * Update multiple entities
   * @param filter Filter to match entities to update
   * @param update Update to apply
   * @param tenantId Tenant ID
   * @returns Number of updated entities
   * @throws DatabaseError if an error occurs
   */
  async updateMany(filter: TypedFilter<T>, update: Partial<T>, tenantId: string): Promise<number> {
    try {
      // Validate inputs
      if (!filter) {
        throw new DatabaseError('Filter is required');
      }

      if (!update) {
        throw new DatabaseError('Update is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Build the query
      const query = {
        tenantId,
        ...filter as FilterQuery<D>,
      };

      // Build the update
      const updateDoc = {
        $set: {
          ...this.toDocument(update),
          updatedAt: new Date()
        }
      };

      // Update the documents
      const result = await this.model.updateMany(query, updateDoc);

      // Return the number of updated documents
      return result.modifiedCount;
    } catch (error) {
      logger.error(`Error updating multiple ${this.entityName}:`, { error, filter, update, tenantId });
      throw new DatabaseError(
        `Error updating multiple ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { filter, update, tenantId }
      );
    }
  }

  /**
   * Delete an entity
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if deleted, false if not found
   * @throws DatabaseError if an error occurs
   */
  async delete(id: string, tenantId: string): Promise<boolean> {
    try {
      // Validate inputs
      if (!id) {
        throw new DatabaseError(`Invalid ID: ${id}`);
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(id) ? id :
                      /^[0-9a-fA-F]{24}$/.test(id) ? new mongoose.Types.ObjectId(id) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid ObjectId format: ${id}`);
      }

      // Delete the document
      const result = await this.model.deleteOne({ _id: objectId, tenantId });

      // Return true if deleted, false if not found
      return result.deletedCount > 0;
    } catch (error) {
      logger.error(`Error deleting ${this.entityName}:`, { error, id, tenantId });
      throw new DatabaseError(
        `Error deleting ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { id, tenantId }
      );
    }
  }

  /**
   * Delete multiple entities
   * @param filter Filter to match entities to delete
   * @param tenantId Tenant ID
   * @returns Number of deleted entities
   * @throws DatabaseError if an error occurs
   */
  async deleteMany(filter: TypedFilter<T>, tenantId: string): Promise<number> {
    try {
      // Validate inputs
      if (!filter) {
        throw new DatabaseError('Filter is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Build the query
      const query = {
        tenantId,
        ...filter as FilterQuery<D>,
      };

      // Delete the documents
      const result = await this.model.deleteMany(query);

      // Return the number of deleted documents
      return result.deletedCount;
    } catch (error) {
      logger.error(`Error deleting multiple ${this.entityName}:`, { error, filter, tenantId });
      throw new DatabaseError(
        `Error deleting multiple ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { filter, tenantId }
      );
    }
  }

  /**
   * Count entities that match the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Count of matching entities
   * @throws DatabaseError if an error occurs
   */
  async count(tenantId: string, filter: TypedFilter<T> = {}): Promise<number> {
    try {
      // Validate inputs
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Build the query
      const query = {
        tenantId,
        ...filter as FilterQuery<D>,
      };

      // Count the documents
      return await this.model.countDocuments(query);
    } catch (error) {
      logger.error(`Error counting ${this.entityName}:`, { error, tenantId, filter });
      throw new DatabaseError(
        `Error counting ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tenantId, filter }
      );
    }
  }

  /**
   * Check if an entity exists
   * @param id Entity ID
   * @param tenantId Tenant ID
   * @returns True if exists, false otherwise
   * @throws DatabaseError if an error occurs
   */
  async exists(id: string, tenantId: string): Promise<boolean> {
    try {
      // Validate inputs
      if (!id) {
        throw new DatabaseError(`Invalid ID: ${id}`);
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(id) ? id :
                      /^[0-9a-fA-F]{24}$/.test(id) ? new mongoose.Types.ObjectId(id) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid ObjectId format: ${id}`);
      }

      // Check if the document exists
      const count = await this.model.countDocuments({
        _id: objectId,
        tenantId,
      });

      // Return true if exists, false otherwise
      return count > 0;
    } catch (error) {
      logger.error(`Error checking if ${this.entityName} exists:`, { error, id, tenantId });
      throw new DatabaseError(
        `Error checking if ${this.entityName} exists: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { id, tenantId }
      );
    }
  }

  /**
   * Find one entity that matches the filter
   * @param tenantId Tenant ID
   * @param filter Filter criteria
   * @returns Entity or null if not found
   * @throws DatabaseError if an error occurs
   */
  async findOne(tenantId: string, filter: TypedFilter<T>): Promise<T | null> {
    try {
      // Validate inputs
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      if (!filter) {
        throw new DatabaseError('Filter is required');
      }

      // Build the query
      const query = {
        tenantId,
        ...filter as FilterQuery<D>,
      };

      // Find the document
      const doc = await this.model.findOne(query);

      // Convert to entity
      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error finding one ${this.entityName}:`, { error, tenantId, filter });
      throw new DatabaseError(
        `Error finding one ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tenantId, filter }
      );
    }
  }

  /**
   * Find and update an entity
   * @param filter Filter to match entity
   * @param update Update to apply
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Updated entity or null if not found
   * @throws DatabaseError if an error occurs
   */
  async findOneAndUpdate(
    filter: TypedFilter<T>,
    update: Partial<T>,
    tenantId: string,
    options: { upsert?: boolean; new?: boolean } = { new: true }
  ): Promise<T | null> {
    try {
      // Validate inputs
      if (!filter) {
        throw new DatabaseError('Filter is required');
      }

      if (!update) {
        throw new DatabaseError('Update is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Build the query
      const query = {
        tenantId,
        ...filter as FilterQuery<D>,
      };

      // Build the update
      const updateDoc = {
        $set: {
          ...this.toDocument(update),
          updatedAt: new Date()
        }
      };

      // Set default options
      const findOneAndUpdateOptions = {
        new: options.new !== false,
        upsert: options.upsert === true,
      };

      // Find and update the document
      const doc = await this.model.findOneAndUpdate(
        query,
        updateDoc,
        findOneAndUpdateOptions
      );

      // Convert to entity
      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error finding and updating ${this.entityName}:`, { error, filter, update, tenantId, options });
      throw new DatabaseError(
        `Error finding and updating ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { filter, update, tenantId, options }
      );
    }
  }

  /**
   * Find and delete an entity
   * @param filter Filter to match entity
   * @param tenantId Tenant ID
   * @returns Deleted entity or null if not found
   * @throws DatabaseError if an error occurs
   */
  async findOneAndDelete(filter: TypedFilter<T>, tenantId: string): Promise<T | null> {
    try {
      // Validate inputs
      if (!filter) {
        throw new DatabaseError('Filter is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Build the query
      const query = {
        tenantId,
        ...filter as FilterQuery<D>,
      };

      // Find and delete the document
      const doc = await this.model.findOneAndDelete(query);

      // Convert to entity
      return this.toEntity(doc);
    } catch (error) {
      logger.error(`Error finding and deleting ${this.entityName}:`, { error, filter, tenantId });
      throw new DatabaseError(
        `Error finding and deleting ${this.entityName}: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { filter, tenantId }
      );
    }
  }
}

export default MongoDBRepository;
