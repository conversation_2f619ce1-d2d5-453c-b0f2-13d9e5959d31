/**
 * Task repository implementation
 *
 * This repository handles data access for tasks.
 */
import { MongoDBRepository, DatabaseError } from './mongodb-repository';
import { Task, ITask } from '../models/mongoose/task-model';
import { QueryOptions, TypedFilter, PaginatedResult } from '../types/query';
import { logger } from '../utils/logger';
import { isObjectId, toObjectId } from '../@types/mongoose';
import mongoose from 'mongoose';

/**
 * Task repository interface
 */
export interface ITaskRepository {
  /**
   * Find tasks by assignee
   * @param assigneeId Assignee ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  findByAssignee(assigneeId: string, tenantId: string, options?: QueryOptions<ITask>): Promise<ITask[]>;

  /**
   * Find tasks by status
   * @param status Status to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  findByStatus(status: string, tenantId: string, options?: QueryOptions<ITask>): Promise<ITask[]>;

  /**
   * Find tasks by priority
   * @param priority Priority to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  findByPriority(priority: string, tenantId: string, options?: QueryOptions<ITask>): Promise<ITask[]>;

  /**
   * Find tasks by due date range
   * @param startDate Start date
   * @param endDate End date
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  findByDueDateRange(startDate: Date, endDate: Date, tenantId: string, options?: QueryOptions<ITask>): Promise<ITask[]>;

  /**
   * Find tasks by related entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  findByRelatedEntity(entityType: string, entityId: string, tenantId: string, options?: QueryOptions<ITask>): Promise<ITask[]>;

  /**
   * Find overdue tasks
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  findOverdueTasks(tenantId: string, options?: QueryOptions<ITask>): Promise<ITask[]>;

  /**
   * Complete a task
   * @param taskId Task ID
   * @param tenantId Tenant ID
   * @returns Updated task or null if not found
   * @throws DatabaseError if an error occurs
   */
  completeTask(taskId: string, tenantId: string): Promise<ITask | null>;

  /**
   * Search tasks
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  search(query: string, tenantId: string, options?: QueryOptions<ITask>): Promise<PaginatedResult<ITask>>;
}

/**
 * Task repository implementation
 */
export class TaskRepository extends MongoDBRepository<ITask, mongoose.Document & ITask> implements ITaskRepository {
  /**
   * Create a new task repository
   */
  constructor() {
    super(Task, 'Task');
  }

  /**
   * Find tasks by assignee
   * @param assigneeId Assignee ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  async findByAssignee(assigneeId: string, tenantId: string, options: QueryOptions<ITask> = {}): Promise<ITask[]> {
    try {
      // Validate inputs
      if (!assigneeId) {
        throw new DatabaseError('Assignee ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(assigneeId) ? assigneeId : 
                      /^[0-9a-fA-F]{24}$/.test(assigneeId) ? new mongoose.Types.ObjectId(assigneeId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid assignee ID format: ${assigneeId}`);
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          assignedTo: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding tasks by assignee:', { error, assigneeId, tenantId });
      throw new DatabaseError(
        `Error finding tasks by assignee: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { assigneeId, tenantId, options }
      );
    }
  }

  /**
   * Find tasks by status
   * @param status Status to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  async findByStatus(status: string, tenantId: string, options: QueryOptions<ITask> = {}): Promise<ITask[]> {
    try {
      // Validate inputs
      if (!status) {
        throw new DatabaseError('Status is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          status
        }
      });
    } catch (error) {
      logger.error('Error finding tasks by status:', { error, status, tenantId });
      throw new DatabaseError(
        `Error finding tasks by status: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { status, tenantId, options }
      );
    }
  }

  /**
   * Find tasks by priority
   * @param priority Priority to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  async findByPriority(priority: string, tenantId: string, options: QueryOptions<ITask> = {}): Promise<ITask[]> {
    try {
      // Validate inputs
      if (!priority) {
        throw new DatabaseError('Priority is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          priority
        }
      });
    } catch (error) {
      logger.error('Error finding tasks by priority:', { error, priority, tenantId });
      throw new DatabaseError(
        `Error finding tasks by priority: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { priority, tenantId, options }
      );
    }
  }

  /**
   * Find tasks by due date range
   * @param startDate Start date
   * @param endDate End date
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  async findByDueDateRange(startDate: Date, endDate: Date, tenantId: string, options: QueryOptions<ITask> = {}): Promise<ITask[]> {
    try {
      // Validate inputs
      if (!(startDate instanceof Date) || isNaN(startDate.getTime())) {
        throw new DatabaseError('Start date must be a valid date');
      }
      
      if (!(endDate instanceof Date) || isNaN(endDate.getTime())) {
        throw new DatabaseError('End date must be a valid date');
      }
      
      if (startDate > endDate) {
        throw new DatabaseError('Start date cannot be after end date');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          dueDate: {
            $gte: startDate,
            $lte: endDate
          }
        }
      });
    } catch (error) {
      logger.error('Error finding tasks by due date range:', { error, startDate, endDate, tenantId });
      throw new DatabaseError(
        `Error finding tasks by due date range: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { startDate, endDate, tenantId, options }
      );
    }
  }

  /**
   * Find tasks by related entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  async findByRelatedEntity(entityType: string, entityId: string, tenantId: string, options: QueryOptions<ITask> = {}): Promise<ITask[]> {
    try {
      // Validate inputs
      if (!entityType) {
        throw new DatabaseError('Entity type is required');
      }
      
      if (!entityId) {
        throw new DatabaseError('Entity ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(entityId) ? entityId : 
                      /^[0-9a-fA-F]{24}$/.test(entityId) ? new mongoose.Types.ObjectId(entityId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid entity ID format: ${entityId}`);
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          'relatedTo.type': entityType,
          'relatedTo.id': objectId
        }
      });
    } catch (error) {
      logger.error('Error finding tasks by related entity:', { error, entityType, entityId, tenantId });
      throw new DatabaseError(
        `Error finding tasks by related entity: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { entityType, entityId, tenantId, options }
      );
    }
  }

  /**
   * Find overdue tasks
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of tasks
   * @throws DatabaseError if an error occurs
   */
  async findOverdueTasks(tenantId: string, options: QueryOptions<ITask> = {}): Promise<ITask[]> {
    try {
      // Validate inputs
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      const now = new Date();
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          status: { $ne: 'completed' },
          dueDate: { $lt: now }
        }
      });
    } catch (error) {
      logger.error('Error finding overdue tasks:', { error, tenantId });
      throw new DatabaseError(
        `Error finding overdue tasks: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tenantId, options }
      );
    }
  }

  /**
   * Complete a task
   * @param taskId Task ID
   * @param tenantId Tenant ID
   * @returns Updated task or null if not found
   * @throws DatabaseError if an error occurs
   */
  async completeTask(taskId: string, tenantId: string): Promise<ITask | null> {
    try {
      // Validate inputs
      if (!taskId) {
        throw new DatabaseError('Task ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(taskId) ? taskId : 
                      /^[0-9a-fA-F]{24}$/.test(taskId) ? new mongoose.Types.ObjectId(taskId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid task ID format: ${taskId}`);
      }
      
      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { 
          status: 'completed',
          completedAt: new Date()
        },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error completing task:', { error, taskId, tenantId });
      throw new DatabaseError(
        `Error completing task: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { taskId, tenantId }
      );
    }
  }

  /**
   * Search tasks
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  async search(query: string, tenantId: string, options: QueryOptions<ITask> = {}): Promise<PaginatedResult<ITask>> {
    try {
      // Validate inputs
      if (!query) {
        throw new DatabaseError('Search query is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Create a search filter
      const searchFilter = {
        $or: [
          { title: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { notes: { $regex: query, $options: 'i' } }
        ]
      };
      
      // Use the base repository's findAllPaginated method
      return await this.findAllPaginated(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          ...searchFilter
        }
      });
    } catch (error) {
      logger.error('Error searching tasks:', { error, query, tenantId });
      throw new DatabaseError(
        `Error searching tasks: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { query, tenantId, options }
      );
    }
  }
}

// Create and export a singleton instance
export const taskRepository = new TaskRepository();

export default taskRepository;
