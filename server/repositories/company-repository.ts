/**
 * Company repository implementation
 *
 * This repository handles data access for companies.
 */
import { MongoDBRepository, DatabaseError } from './mongodb-repository';
import { Company, ICompany } from '../models/mongoose/company-model';
import { QueryOptions, TypedFilter, PaginatedResult } from '../types/query';
import { logger } from '../utils/logger';
import { isObjectId, toObjectId } from '../@types/mongoose';
import mongoose from 'mongoose';

/**
 * Company repository interface
 */
export interface ICompanyRepository {
  /**
   * Find companies by domain
   * @param domain Domain name
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  findByDomain(domain: string, tenantId: string, options?: QueryOptions<ICompany>): Promise<ICompany[]>;

  /**
   * Find companies by industry
   * @param industry Industry name
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  findByIndustry(industry: string, tenantId: string, options?: QueryOptions<ICompany>): Promise<ICompany[]>;

  /**
   * Find companies by status
   * @param status Status to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  findByStatus(status: string, tenantId: string, options?: QueryOptions<ICompany>): Promise<ICompany[]>;

  /**
   * Find companies by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  findByOwner(ownerId: string, tenantId: string, options?: QueryOptions<ICompany>): Promise<ICompany[]>;

  /**
   * Find companies by tag
   * @param tag Tag to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  findByTag(tag: string, tenantId: string, options?: QueryOptions<ICompany>): Promise<ICompany[]>;

  /**
   * Search companies
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  search(query: string, tenantId: string, options?: QueryOptions<ICompany>): Promise<PaginatedResult<ICompany>>;

  /**
   * Enrich a company with AI
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @returns Updated company
   * @throws DatabaseError if an error occurs
   */
  enrich(companyId: string, tenantId: string): Promise<ICompany | null>;
}

/**
 * Company repository implementation
 */
export class CompanyRepository extends MongoDBRepository<ICompany, mongoose.Document & ICompany> implements ICompanyRepository {
  /**
   * Create a new company repository
   */
  constructor() {
    super(Company, 'Company');
  }

  /**
   * Find companies by domain
   * @param domain Domain name
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  async findByDomain(domain: string, tenantId: string, options: QueryOptions<ICompany> = {}): Promise<ICompany[]> {
    try {
      // Validate inputs
      if (!domain) {
        throw new DatabaseError('Domain is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Normalize domain (remove protocol, www, and trailing slashes)
      const normalizedDomain = domain.toLowerCase()
        .replace(/^https?:\/\//, '')
        .replace(/^www\./, '')
        .replace(/\/$/, '');
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          domain: { $regex: normalizedDomain, $options: 'i' }
        }
      });
    } catch (error) {
      logger.error('Error finding companies by domain:', { error, domain, tenantId });
      throw new DatabaseError(
        `Error finding companies by domain: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { domain, tenantId, options }
      );
    }
  }

  /**
   * Find companies by industry
   * @param industry Industry name
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  async findByIndustry(industry: string, tenantId: string, options: QueryOptions<ICompany> = {}): Promise<ICompany[]> {
    try {
      // Validate inputs
      if (!industry) {
        throw new DatabaseError('Industry is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          industry: { $regex: industry, $options: 'i' }
        }
      });
    } catch (error) {
      logger.error('Error finding companies by industry:', { error, industry, tenantId });
      throw new DatabaseError(
        `Error finding companies by industry: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { industry, tenantId, options }
      );
    }
  }

  /**
   * Find companies by status
   * @param status Status to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  async findByStatus(status: string, tenantId: string, options: QueryOptions<ICompany> = {}): Promise<ICompany[]> {
    try {
      // Validate inputs
      if (!status) {
        throw new DatabaseError('Status is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          status
        }
      });
    } catch (error) {
      logger.error('Error finding companies by status:', { error, status, tenantId });
      throw new DatabaseError(
        `Error finding companies by status: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { status, tenantId, options }
      );
    }
  }

  /**
   * Find companies by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  async findByOwner(ownerId: string, tenantId: string, options: QueryOptions<ICompany> = {}): Promise<ICompany[]> {
    try {
      // Validate inputs
      if (!ownerId) {
        throw new DatabaseError('Owner ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(ownerId) ? ownerId : 
                      /^[0-9a-fA-F]{24}$/.test(ownerId) ? new mongoose.Types.ObjectId(ownerId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid owner ID format: ${ownerId}`);
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          owner: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding companies by owner:', { error, ownerId, tenantId });
      throw new DatabaseError(
        `Error finding companies by owner: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { ownerId, tenantId, options }
      );
    }
  }

  /**
   * Find companies by tag
   * @param tag Tag to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of companies
   * @throws DatabaseError if an error occurs
   */
  async findByTag(tag: string, tenantId: string, options: QueryOptions<ICompany> = {}): Promise<ICompany[]> {
    try {
      // Validate inputs
      if (!tag) {
        throw new DatabaseError('Tag is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          tags: tag
        }
      });
    } catch (error) {
      logger.error('Error finding companies by tag:', { error, tag, tenantId });
      throw new DatabaseError(
        `Error finding companies by tag: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tag, tenantId, options }
      );
    }
  }

  /**
   * Search companies
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  async search(query: string, tenantId: string, options: QueryOptions<ICompany> = {}): Promise<PaginatedResult<ICompany>> {
    try {
      // Validate inputs
      if (!query) {
        throw new DatabaseError('Search query is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Create a search filter
      const searchFilter = {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { industry: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { domain: { $regex: query, $options: 'i' } }
        ]
      };
      
      // Use the base repository's findAllPaginated method
      return await this.findAllPaginated(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          ...searchFilter
        }
      });
    } catch (error) {
      logger.error('Error searching companies:', { error, query, tenantId });
      throw new DatabaseError(
        `Error searching companies: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { query, tenantId, options }
      );
    }
  }

  /**
   * Enrich a company with AI
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @returns Updated company
   * @throws DatabaseError if an error occurs
   */
  async enrich(companyId: string, tenantId: string): Promise<ICompany | null> {
    try {
      // Validate inputs
      if (!companyId) {
        throw new DatabaseError('Company ID is required');
      }
      
      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }
      
      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(companyId) ? companyId : 
                      /^[0-9a-fA-F]{24}$/.test(companyId) ? new mongoose.Types.ObjectId(companyId) : 
                      null;
                      
      if (!objectId) {
        throw new DatabaseError(`Invalid company ID format: ${companyId}`);
      }
      
      // Check if the company exists
      const company = await this.findById(companyId, tenantId);
      
      if (!company) {
        throw new DatabaseError(`Company not found: ${companyId}`);
      }
      
      // This would typically call an AI service to enrich the company
      // For now, we'll just update with a placeholder
      const aiEnrichment = {
        industry: company.industry || 'Technology',
        size: company.size || '11-50',
        revenue: '$1M - $10M',
        founded: 2010,
        socialProfiles: {
          linkedin: `https://linkedin.com/company/${company.name.toLowerCase().replace(/\s+/g, '-')}`,
          twitter: `https://twitter.com/${company.name.toLowerCase().replace(/\s+/g, '')}`,
        },
        technologies: ['CRM', 'Marketing Automation', 'Cloud Computing'],
        competitors: ['Competitor A', 'Competitor B', 'Competitor C'],
        news: [
          {
            title: `${company.name} Announces New Product`,
            url: 'https://example.com/news/1',
            date: new Date(),
            source: 'Tech News',
          },
          {
            title: `${company.name} Expands to New Markets`,
            url: 'https://example.com/news/2',
            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            source: 'Business News',
          },
        ],
        enrichmentDate: new Date(),
        enrichmentSource: 'AI Enrichment Service',
        confidence: 0.85,
      };

      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { aiEnrichment },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error enriching company:', { error, companyId, tenantId });
      throw new DatabaseError(
        `Error enriching company: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { companyId, tenantId }
      );
    }
  }
}

// Create and export a singleton instance
export const companyRepository = new CompanyRepository();

export default companyRepository;
