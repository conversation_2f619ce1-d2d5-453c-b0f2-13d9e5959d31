/**
 * Repository exports
 */

// Export base repository interfaces and implementations
export * from './base-repository';
export * from './mongodb-repository';

// Export specific repositories
export * from './contact-repository';
export * from './opportunity-repository';
export * from './company-repository';
export * from './user-repository';
export * from './task-repository';
export * from './document-repository';

// Export repository instances
import contactRepository from './contact-repository';
import opportunityRepository from './opportunity-repository';
import companyRepository from './company-repository';
import userRepository from './user-repository';
import taskRepository from './task-repository';
import documentRepository from './document-repository';

// Export default repositories object
export default {
  contactRepository,
  opportunityRepository,
  companyRepository,
  userRepository,
  taskRepository,
  documentRepository,
};
