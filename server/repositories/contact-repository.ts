/**
 * Contact repository implementation
 *
 * This repository handles data access for contacts.
 */
import { MongoDBRepository, DatabaseError } from './mongodb-repository';
import { Contact, IContact } from '../models/mongoose/contact-model';
import { QueryOptions, TypedFilter, PaginatedResult } from '../types/query';
import { logger } from '../utils/logger';
import { isObjectId, toObjectId } from '../@types/mongoose';
import mongoose from 'mongoose';

/**
 * Contact repository interface
 */
export interface IContactRepository {
  /**
   * Find contacts by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  findByCompanyId(companyId: string, tenantId: string, options?: QueryOptions<IContact>): Promise<IContact[]>;

  /**
   * Find a contact by email
   * @param email Email address
   * @param tenantId Tenant ID
   * @returns Contact or null if not found
   * @throws DatabaseError if an error occurs
   */
  findByEmail(email: string, tenantId: string): Promise<IContact | null>;

  /**
   * Search contacts by name or email
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  search(query: string, tenantId: string, options?: QueryOptions<IContact>): Promise<PaginatedResult<IContact>>;

  /**
   * Generate a persona for a contact
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   * @throws DatabaseError if an error occurs
   */
  generatePersona(contactId: string, tenantId: string): Promise<IContact | null>;

  /**
   * Enrich a contact with AI
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   * @throws DatabaseError if an error occurs
   */
  enrich(contactId: string, tenantId: string): Promise<IContact | null>;

  /**
   * Find contacts by tag
   * @param tag Tag to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  findByTag(tag: string, tenantId: string, options?: QueryOptions<IContact>): Promise<IContact[]>;

  /**
   * Find contacts by status
   * @param status Status to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  findByStatus(status: string, tenantId: string, options?: QueryOptions<IContact>): Promise<IContact[]>;

  /**
   * Find contacts by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  findByOwner(ownerId: string, tenantId: string, options?: QueryOptions<IContact>): Promise<IContact[]>;

  /**
   * Find contacts by score range
   * @param minScore Minimum score
   * @param maxScore Maximum score
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  findByScoreRange(minScore: number, maxScore: number, tenantId: string, options?: QueryOptions<IContact>): Promise<IContact[]>;
}

/**
 * Contact repository implementation
 */
export class ContactRepository extends MongoDBRepository<IContact, mongoose.Document & IContact> implements IContactRepository {
  /**
   * Create a new contact repository
   */
  constructor() {
    super(Contact, 'Contact');
  }

  /**
   * Find contacts by company ID
   * @param companyId Company ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  async findByCompanyId(companyId: string, tenantId: string, options: QueryOptions<IContact> = {}): Promise<IContact[]> {
    try {
      // Validate inputs
      if (!companyId) {
        throw new DatabaseError('Company ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(companyId) ? companyId :
                      /^[0-9a-fA-F]{24}$/.test(companyId) ? new mongoose.Types.ObjectId(companyId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid company ID format: ${companyId}`);
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          companyId: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding contacts by company ID:', { error, companyId, tenantId });
      throw new DatabaseError(
        `Error finding contacts by company ID: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { companyId, tenantId, options }
      );
    }
  }

  /**
   * Find a contact by email
   * @param email Email address
   * @param tenantId Tenant ID
   * @returns Contact or null if not found
   * @throws DatabaseError if an error occurs
   */
  async findByEmail(email: string, tenantId: string): Promise<IContact | null> {
    try {
      // Validate inputs
      if (!email) {
        throw new DatabaseError('Email is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Use the base repository's findOne method
      return await this.findOne(tenantId, {
        email: email.toLowerCase()
      });
    } catch (error) {
      logger.error('Error finding contact by email:', { error, email, tenantId });
      throw new DatabaseError(
        `Error finding contact by email: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { email, tenantId }
      );
    }
  }

  /**
   * Search contacts by name or email
   * @param query Search query
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Paginated result
   * @throws DatabaseError if an error occurs
   */
  async search(
    query: string,
    tenantId: string,
    options: QueryOptions<IContact> = {}
  ): Promise<PaginatedResult<IContact>> {
    try {
      // Validate inputs
      if (!query) {
        throw new DatabaseError('Search query is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Create a search filter
      const searchFilter = {
        $or: [
          { firstName: { $regex: query, $options: 'i' } },
          { lastName: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
        ],
      };

      // Use the base repository's findAllPaginated method
      return await this.findAllPaginated(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          ...searchFilter
        }
      });
    } catch (error) {
      logger.error('Error searching contacts:', { error, query, tenantId });
      throw new DatabaseError(
        `Error searching contacts: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { query, tenantId, options }
      );
    }
  }

  /**
   * Generate a persona for a contact
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   * @throws DatabaseError if an error occurs
   */
  async generatePersona(contactId: string, tenantId: string): Promise<IContact | null> {
    try {
      // Validate inputs
      if (!contactId) {
        throw new DatabaseError('Contact ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(contactId) ? contactId :
                      /^[0-9a-fA-F]{24}$/.test(contactId) ? new mongoose.Types.ObjectId(contactId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid contact ID format: ${contactId}`);
      }

      // This would typically call an AI service to generate the persona
      // For now, we'll just update with a placeholder
      const persona = {
        summary: 'AI-generated persona summary',
        communicationPreferences: {
          preferredChannel: 'email',
          bestTimeToContact: 'Morning',
          responseTime: 'fast',
        },
        interests: ['Technology', 'Business'],
        painPoints: ['Time management', 'Cost control'],
        decisionFactors: ['ROI', 'Ease of use'],
        aiConfidence: 0.85,
        lastUpdated: new Date(),
      };

      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { persona },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error generating persona for contact:', { error, contactId, tenantId });
      throw new DatabaseError(
        `Error generating persona for contact: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { contactId, tenantId }
      );
    }
  }

  /**
   * Enrich a contact with AI
   * @param contactId Contact ID
   * @param tenantId Tenant ID
   * @returns Updated contact
   * @throws DatabaseError if an error occurs
   */
  async enrich(contactId: string, tenantId: string): Promise<IContact | null> {
    try {
      // Validate inputs
      if (!contactId) {
        throw new DatabaseError('Contact ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(contactId) ? contactId :
                      /^[0-9a-fA-F]{24}$/.test(contactId) ? new mongoose.Types.ObjectId(contactId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid contact ID format: ${contactId}`);
      }

      // This would typically call an AI service to enrich the contact
      // For now, we'll just update with a placeholder
      const aiEnrichment = {
        linkedinProfile: 'https://linkedin.com/in/example',
        twitterHandle: '@example',
        companyRole: 'Decision Maker',
        interests: ['AI', 'CRM', 'Sales Automation'],
        recentNews: ['Recently promoted', 'Spoke at industry conference'],
        enrichmentDate: new Date(),
        enrichmentSource: 'AI Enrichment Service',
        confidence: 0.9,
      };

      // Use the base repository's findOneAndUpdate method
      return await this.findOneAndUpdate(
        { _id: objectId },
        { aiEnrichment },
        tenantId,
        { new: true }
      );
    } catch (error) {
      logger.error('Error enriching contact:', { error, contactId, tenantId });
      throw new DatabaseError(
        `Error enriching contact: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { contactId, tenantId }
      );
    }
  }

  /**
   * Find contacts by tag
   * @param tag Tag to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  async findByTag(tag: string, tenantId: string, options: QueryOptions<IContact> = {}): Promise<IContact[]> {
    try {
      // Validate inputs
      if (!tag) {
        throw new DatabaseError('Tag is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          tags: tag
        }
      });
    } catch (error) {
      logger.error('Error finding contacts by tag:', { error, tag, tenantId });
      throw new DatabaseError(
        `Error finding contacts by tag: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { tag, tenantId, options }
      );
    }
  }

  /**
   * Find contacts by status
   * @param status Status to search for
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  async findByStatus(status: string, tenantId: string, options: QueryOptions<IContact> = {}): Promise<IContact[]> {
    try {
      // Validate inputs
      if (!status) {
        throw new DatabaseError('Status is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          status
        }
      });
    } catch (error) {
      logger.error('Error finding contacts by status:', { error, status, tenantId });
      throw new DatabaseError(
        `Error finding contacts by status: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { status, tenantId, options }
      );
    }
  }

  /**
   * Find contacts by owner
   * @param ownerId Owner ID
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  async findByOwner(ownerId: string, tenantId: string, options: QueryOptions<IContact> = {}): Promise<IContact[]> {
    try {
      // Validate inputs
      if (!ownerId) {
        throw new DatabaseError('Owner ID is required');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Convert string ID to ObjectId if needed
      const objectId = isObjectId(ownerId) ? ownerId :
                      /^[0-9a-fA-F]{24}$/.test(ownerId) ? new mongoose.Types.ObjectId(ownerId) :
                      null;

      if (!objectId) {
        throw new DatabaseError(`Invalid owner ID format: ${ownerId}`);
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          owner: objectId
        }
      });
    } catch (error) {
      logger.error('Error finding contacts by owner:', { error, ownerId, tenantId });
      throw new DatabaseError(
        `Error finding contacts by owner: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { ownerId, tenantId, options }
      );
    }
  }

  /**
   * Find contacts by score range
   * @param minScore Minimum score
   * @param maxScore Maximum score
   * @param tenantId Tenant ID
   * @param options Query options
   * @returns Array of contacts
   * @throws DatabaseError if an error occurs
   */
  async findByScoreRange(minScore: number, maxScore: number, tenantId: string, options: QueryOptions<IContact> = {}): Promise<IContact[]> {
    try {
      // Validate inputs
      if (typeof minScore !== 'number') {
        throw new DatabaseError('Minimum score must be a number');
      }

      if (typeof maxScore !== 'number') {
        throw new DatabaseError('Maximum score must be a number');
      }

      if (minScore > maxScore) {
        throw new DatabaseError('Minimum score cannot be greater than maximum score');
      }

      if (!tenantId) {
        throw new DatabaseError('Tenant ID is required');
      }

      // Use the base repository's findAll method with a filter
      return await this.findAll(tenantId, {
        ...options,
        filter: {
          ...options.filter,
          'score.current': {
            $gte: minScore,
            $lte: maxScore
          }
        }
      });
    } catch (error) {
      logger.error('Error finding contacts by score range:', { error, minScore, maxScore, tenantId });
      throw new DatabaseError(
        `Error finding contacts by score range: ${error?.message ?? 'Unknown error'}`,
        error instanceof Error ? error : undefined,
        { minScore, maxScore, tenantId, options }
      );
    }
  }
}

// Create and export a singleton instance
export const contactRepository = new ContactRepository();

export default contactRepository;
