import express from 'express';
import session from 'express-session';
import cors from 'cors';
import dotenv from 'dotenv';
import { registerRoutes } from './routes';
import { connectToMongoDB, disconnectFromMongoDB } from './mongodb-connection';
import { initializeServices, shutdownServices } from './services/service-initializer';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Register routes
registerRoutes(app);

// Start server
const server = app.listen(port, async () => {
  console.log(`Server is running on port ${port}`);

  // Connect to MongoDB if enabled
  if (process.env.MONGODB_ENABLED === 'true') {
    try {
      await connectToMongoDB();
      console.log('MongoDB connected successfully');

      // Initialize services after MongoDB connection
      await initializeServices();
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
    }
  }
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM signal received: closing HTTP server');

  // Shutdown services first
  if (process.env.MONGODB_ENABLED === 'true') {
    try {
      await shutdownServices();
    } catch (error) {
      console.error('Error shutting down services:', error);
    }

    // Then disconnect from MongoDB
    await disconnectFromMongoDB();
  }

  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});

export default app;
