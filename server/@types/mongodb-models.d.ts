/**
 * Type declarations for MongoDB models
 */

import { Document, Model, ObjectId } from 'mongoose';

// Base interface for all MongoDB documents
export interface IBaseDocument extends Document {
  _id: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Contact interface
export interface IContact extends IBaseDocument {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  companyId?: ObjectId;
  companyName?: string;
  status: 'active' | 'inactive' | 'lead' | 'customer' | 'prospect';
  owner: ObjectId;
  source?: string;
  tags: string[];
  notes?: string;
  lastContactedAt?: Date;
  aiEnrichment?: {
    linkedinProfile?: string;
    twitterHandle?: string;
    companyRole?: string;
    interests?: string[];
    recentNews?: string[];
    enrichmentDate?: Date;
    enrichmentSource?: string;
    confidence?: number;
  };
  persona?: {
    summary?: string;
    communicationPreferences?: {
      preferredChannel?: 'email' | 'phone' | 'in-person' | 'video';
      bestTimeToContact?: string;
      responseTime?: 'fast' | 'medium' | 'slow';
    };
    interests?: string[];
    painPoints?: string[];
    decisionFactors?: string[];
    aiConfidence?: number;
    lastUpdated?: Date;
  };
  score?: {
    current: number;
    history: Array<{
      value: number;
      date: Date;
      reason?: string;
    }>;
    lastUpdated: Date;
  };
  customFields?: Record<string, any>;
  tenantId: string;
}

// Company interface
export interface ICompany extends IBaseDocument {
  name: string;
  website?: string;
  industry?: string;
  size?: string;
  revenue?: string;
  location?: string;
  description?: string;
  logo?: string;
  status: 'active' | 'inactive' | 'lead' | 'customer' | 'prospect';
  owner: ObjectId;
  tags: string[];
  notes?: string;
  customFields?: Record<string, any>;
  tenantId: string;
}

// Opportunity interface
export interface IOpportunity extends IBaseDocument {
  name: string;
  value: number;
  currency: string;
  stage: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  probability?: number;
  expectedCloseDate?: Date;
  contactId?: ObjectId;
  companyId?: ObjectId;
  owner: ObjectId;
  notes?: string;
  aiInsights?: Record<string, any>;
  customFields?: Record<string, any>;
  tenantId: string;
}

// Activity interface
export interface IActivity extends IBaseDocument {
  type: 'call' | 'email' | 'meeting' | 'task' | 'note' | 'other';
  title: string;
  description?: string;
  date: Date;
  duration?: number;
  completed: boolean;
  outcome?: string;
  contactId?: ObjectId;
  companyId?: ObjectId;
  opportunityId?: ObjectId;
  owner: ObjectId;
  customFields?: Record<string, any>;
  tenantId: string;
}

// User interface
export interface IUser extends IBaseDocument {
  username: string;
  email: string;
  password: string;
  fullName: string;
  role: 'admin' | 'user' | 'manager' | 'viewer';
  status: 'active' | 'inactive' | 'pending';
  avatar?: string;
  tenantId: string;
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    notifications?: {
      email?: boolean;
      push?: boolean;
      sms?: boolean;
    };
  };
}

// AI Chat interface
export interface IAiChat extends IBaseDocument {
  userId: ObjectId;
  title: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
  }>;
  context?: Record<string, any>;
  tenantId?: string;
}

// Insight interface
export interface IInsight extends IBaseDocument {
  title: string;
  description: string;
  type: string;
  importance: number;
  targetType: string;
  targetId?: ObjectId;
  generatedBy: 'ai' | 'system' | 'user';
  isRead: boolean;
  actionTaken: boolean;
  expiresAt?: Date;
  tenantId?: string;
}

// Document interface
export interface IDocument extends IBaseDocument {
  name: string;
  type: string;
  url: string;
  size?: number;
  contactId?: ObjectId;
  companyId?: ObjectId;
  opportunityId?: ObjectId;
  owner: ObjectId;
  tags: string[];
  customFields?: Record<string, any>;
  tenantId: string;
}

// Analytics Event interface
export interface IAnalyticsEvent extends IBaseDocument {
  type: string;
  userId?: ObjectId;
  tenantId: string;
  data: Record<string, any>;
  context?: {
    page?: string;
    referrer?: string;
    userAgent?: string;
    ip?: string;
  };
  timestamp: Date;
}

// Experiment interface
export interface IExperiment extends IBaseDocument {
  name: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  startDate?: Date;
  endDate?: Date;
  variants: Array<{
    id: string;
    name: string;
    description?: string;
    weight: number;
  }>;
  tenantId: string;
}

// Experiment Result interface
export interface IExperimentResult extends IBaseDocument {
  experimentId: ObjectId;
  userId: ObjectId;
  variantId: string;
  conversion: boolean;
  conversionData?: Record<string, any>;
  tenantId: string;
}

// Feature Flag interface
export interface IFeatureFlag extends IBaseDocument {
  name: string;
  description: string;
  enabled: boolean;
  rules: Array<{
    type: 'user' | 'tenant' | 'percentage' | 'date';
    value: any;
    enabled: boolean;
  }>;
  tenantId: string;
}
