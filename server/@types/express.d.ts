import express from 'express';
import { IUser } from '../models/mongoose/user-model';

declare global {
  namespace Express {
    // Extend the Request interface
    interface Request {
      // Custom properties for the Request object
      tenantId?: string;
      userId?: string;
      user?: {
        id: string;
        email?: string;
        role?: string;
        tenantId?: string;
        permissions?: string[];
        name?: string;
        isActive?: boolean;
      };
      // Authentication properties
      isAuthenticated?: boolean;
      authToken?: string;
      // Request metadata
      startTime?: number;
      requestId?: string;
      clientIp?: string;
      userAgent?: string;
      // For file uploads
      file?: Express.Multer.File;
      files?: {
        [fieldname: string]: Express.Multer.File[];
      } | Express.Multer.File[];
      // For validation
      validationErrors?: Record<string, string>;
      // For rate limiting
      rateLimit?: {
        limit: number;
        current: number;
        remaining: number;
        resetTime: Date;
      };
    }

    // Extend the Response interface
    interface Response {
      // Custom properties for the Response object
      locals: {
        user?: Partial<IUser>;
        tenantId?: string;
        [key: string]: any;
      };
      // For sending standardized API responses
      sendSuccess?: (data?: any, message?: string, statusCode?: number) => void;
      sendError?: (message: string, statusCode?: number, errors?: any) => void;
    }
  }
}

// Define NextFunction type with improved error handling
declare namespace Express {
  export interface NextFunction {
    (err?: Error | null | undefined | any): void;
  }
}

// Define middleware function type
export type RequestHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => Promise<void> | void;

// Define error handling middleware function type
export type ErrorRequestHandler = (
  err: Error | any,
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => Promise<void> | void;

export {};
