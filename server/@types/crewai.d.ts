/**
 * Type declarations for crewai-js
 */

declare module 'crewai-js' {
  export interface AgentOptions {
    name: string;
    role: string;
    goal: string;
    backstory?: string;
    verbose?: boolean;
    memory?: boolean;
    maxIterations?: number;
    maxExecutionTime?: number;
    allowDelegation?: boolean;
    tools?: any[];
    llm?: any;
  }

  export class Agent {
    constructor(options: AgentOptions);
    name: string;
    role: string;
    goal: string;
    backstory?: string;
    verbose?: boolean;
    memory?: boolean;
    maxIterations?: number;
    maxExecutionTime?: number;
    allowDelegation?: boolean;
    tools?: any[];
    llm?: any;
  }

  export interface TaskOptions {
    description: string;
    expected_output?: string;
    expectedOutput?: string;
    agent: Agent;
    context?: string;
    tools?: any[];
    async_execution?: boolean;
    human_input?: boolean;
  }

  export class Task {
    constructor(options: TaskOptions);
    description: string;
    expected_output?: string;
    expectedOutput?: string;
    agent: Agent;
    context?: string;
    tools?: any[];
    async_execution?: boolean;
    human_input?: boolean;
  }

  export interface CrewOptions {
    name: string;
    agents: Agent[];
    tasks: Task[];
    verbose?: boolean;
    process?: 'sequential' | 'hierarchical';
    maxConcurrentTasks?: number;
    memory?: boolean;
    caching?: boolean;
  }

  export class Crew {
    constructor(options: CrewOptions);
    name: string;
    agents: Agent[];
    tasks: Task[];
    verbose?: boolean;
    process?: 'sequential' | 'hierarchical';
    maxConcurrentTasks?: number;
    memory?: boolean;
    caching?: boolean;
    
    run(): Promise<any>;
    kickoff(): Promise<any>;
  }
}
