/**
 * Type definitions for subscription service interfaces
 */

import {
  SubscriptionPlan,
  TenantSubscription,
  Feature,
  TenantUsage,
  Tenant,
  UserTenant,
  InsertSubscriptionPlan,
  InsertTenantSubscription,
  InsertFeature,
  InsertTenantUsage,
  InsertTenant,
  InsertUserTenant
} from '@shared/subscription-schema';
import { ObjectId } from 'mongoose';

/**
 * Common interface for subscription services
 */
export interface ISubscriptionService {
  // Plan methods
  getPlans(): Promise<SubscriptionPlan[]>;
  getPlan(id: number): Promise<SubscriptionPlan | null>;
  createPlan(data: InsertSubscriptionPlan): Promise<SubscriptionPlan>;
  updatePlan(id: number, data: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan | null>;
  deletePlan(id: number): Promise<boolean>;

  // Feature methods
  getFeatures(): Promise<Feature[]>;
  getFeature(id: number): Promise<Feature | null>;
  getFeatureByKey(key: string): Promise<Feature | null>;
  registerFeature(data: InsertFeature): Promise<Feature>;
  updateFeature(id: number, data: Partial<InsertFeature>): Promise<Feature | null>;
  deleteFeature(id: number): Promise<boolean>;

  // Tenant subscription methods
  getTenantSubscription(tenantId: string): Promise<TenantSubscription | null>;
  getTenantSubscriptionWithPlan(tenantId: string): Promise<{ subscription: TenantSubscription; plan: SubscriptionPlan } | null>;
  createTenantSubscription(data: InsertTenantSubscription): Promise<TenantSubscription>;
  updateTenantSubscription(id: number, data: Partial<InsertTenantSubscription>): Promise<TenantSubscription | null>;
  cancelTenantSubscription(tenantId: string): Promise<boolean>;
  getTenantSubscriptions(): Promise<TenantSubscription[]>;

  // Tenant usage methods
  getTenantUsage(tenantId: string, period?: string): Promise<TenantUsage | null>;
  updateTenantUsage(tenantId: string, resourceType: string, amount: number): Promise<TenantUsage>;
  updateFeatureUsage(tenantId: string, featureKey: string, amount: number): Promise<TenantUsage>;

  // Entitlement methods
  checkEntitlement(tenantId: string, featureKey: string): Promise<boolean>;
  checkResourceLimit(tenantId: string, resourceType: string, amount: number): Promise<boolean>;

  // Cache methods
  getSubscriptionCache(): Promise<any>;

  // User-tenant methods
  getUserTenants(userId: string | number): Promise<UserTenant[]>;
  getTenantUsers(tenantId: string | number): Promise<UserTenant[]>;
  createUserTenant(data: InsertUserTenant): Promise<UserTenant>;
  updateUserTenant(id: number, data: Partial<InsertUserTenant>): Promise<UserTenant | null>;

  // Tenant methods
  getTenants(): Promise<Tenant[]>;
  getTenant(id: number): Promise<Tenant | null>;
  createTenant(data: InsertTenant): Promise<Tenant>;
  updateTenant(id: number, data: Partial<InsertTenant>): Promise<Tenant | null>;
}
