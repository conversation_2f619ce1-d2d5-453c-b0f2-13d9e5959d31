/**
 * Type definitions for API clients
 */

import { Contact, Company, Opportunity, Activity } from '../shared/types/core';

/**
 * API request options
 */
export interface ApiRequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
}

/**
 * API response
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T> {
  items: T[];
  meta: PaginationMeta;
}

/**
 * Base API client interface
 */
export interface BaseApiClient {
  request<T>(options: ApiRequestOptions): Promise<T>;
  get<T>(url: string, params?: Record<string, any>): Promise<T>;
  post<T>(url: string, data?: any): Promise<T>;
  put<T>(url: string, data?: any): Promise<T>;
  delete<T>(url: string): Promise<T>;
  patch<T>(url: string, data?: any): Promise<T>;
}

/**
 * MongoDB API client interface
 */
export interface MongoApiClient extends BaseApiClient {
  contacts: {
    getAll(): Promise<Contact[]>;
    getById(id: string): Promise<Contact>;
    create(data: Partial<Contact>): Promise<Contact>;
    update(id: string, data: Partial<Contact>): Promise<Contact>;
    delete(id: string): Promise<boolean>;
    generatePersona(contactId: string): Promise<any>;
    enrich(contactId: string): Promise<any>;
  };
  companies: {
    getAll(): Promise<Company[]>;
    getById(id: string): Promise<Company>;
    create(data: Partial<Company>): Promise<Company>;
    update(id: string, data: Partial<Company>): Promise<Company>;
    delete(id: string): Promise<boolean>;
  };
  opportunities: {
    getAll(): Promise<Opportunity[]>;
    getById(id: string): Promise<Opportunity>;
    create(data: Partial<Opportunity>): Promise<Opportunity>;
    update(id: string, data: Partial<Opportunity>): Promise<Opportunity>;
    delete(id: string): Promise<boolean>;
  };
  activities: {
    getAll(): Promise<Activity[]>;
    getById(id: string): Promise<Activity>;
    create(data: Partial<Activity>): Promise<Activity>;
    update(id: string, data: Partial<Activity>): Promise<Activity>;
    delete(id: string): Promise<boolean>;
  };
}
