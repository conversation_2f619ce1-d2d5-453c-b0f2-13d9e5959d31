import 'express-session';
import { Types } from 'mongoose';

declare module 'express-session' {
  interface SessionData {
    userId?: string | Types.ObjectId;
    tenantId?: string | Types.ObjectId;
    isAuthenticated?: boolean;
    user?: {
      id: string | Types.ObjectId;
      username?: string;
      email?: string;
      role?: string;
      tenantId?: string | Types.ObjectId;
      permissions?: string[];
      preferences?: {
        theme?: 'light' | 'dark' | 'system';
        language?: string;
        timezone?: string;
        notifications?: {
          email?: boolean;
          push?: boolean;
          inApp?: boolean;
        };
      };
      lastLogin?: Date;
      loginCount?: number;
    };
    authToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
    createdAt?: Date;
    lastActive?: Date;
    ipAddress?: string;
    userAgent?: string;
    referrer?: string;
  }
}
