/**
 * Custom types for MongoDB documents
 */
import mongoose from 'mongoose';
import {
  IObjection,
  IObjectionResponse,
  IContact,
  ICompany,
  IOpportunity,
  IActivity,
  IUser,
  IAnalyticsEvent,
  IExperiment,
  IFeatureFlag,
  IDocument,
  ITag,
  ITask,
  INotification,
  IStageTransition,
  IProposalTemplate,
  IProposal,
  IFollowUp,
  IFollowUpTemplate,
  IWinLossAnalysis,
  IWinLossFactor,
  IEmailConfig,
  IProposalAnalytics,
  IEmailTracking,
  IEmailTemplate,
  ISequence,
  ISequenceStep,
  ISequenceEnrollment,
  ITenantDomain,
  IEdge,
  IEventsRaw,
  IAttributionResults,
  ICBICache,
  IAnalyticsDataset,
  IMarketingCampaign,
  IWorkflow,
  IWorkflowRun,
  IWorkflowVersion,
  IInsightsCache,
  ISubscriptionPlan,
  ITenant,
  ITenantSubscription,
  IFeature,
  ITenantUsage,
  ISubscriptionEvent,
  IUserTenant,
  IRelationship,
  IInteraction,
  IInsight,
  IAiChat
} from '../models/mongoose';

/**
 * Generic type for MongoDB documents
 * Combines the Mongoose Document type with a specific model interface
 */
export type MongoDocument<T> = mongoose.Document<unknown, {}, T> & T & {
  _id: mongoose.Types.ObjectId;
  __v: number;
};

/**
 * Custom document types for each model
 */
// Core models
export type ObjectionDocument = MongoDocument<IObjection>;
export type ObjectionResponseDocument = MongoDocument<IObjectionResponse>;
export type ContactDocument = MongoDocument<IContact>;
export type CompanyDocument = MongoDocument<ICompany>;
export type OpportunityDocument = MongoDocument<IOpportunity>;
export type ActivityDocument = MongoDocument<IActivity>;
export type UserDocument = MongoDocument<IUser>;
export type DocumentDocument = MongoDocument<IDocument>;
export type TagDocument = MongoDocument<ITag>;
export type TaskDocument = MongoDocument<ITask>;
export type NotificationDocument = MongoDocument<INotification>;
export type RelationshipDocument = MongoDocument<IRelationship>;
export type InteractionDocument = MongoDocument<IInteraction>;
export type InsightDocument = MongoDocument<IInsight>;
export type AiChatDocument = MongoDocument<IAiChat>;

// Sales tools models
export type StageTransitionDocument = MongoDocument<IStageTransition>;
export type ProposalTemplateDocument = MongoDocument<IProposalTemplate>;
export type ProposalDocument = MongoDocument<IProposal>;
export type FollowUpDocument = MongoDocument<IFollowUp>;
export type FollowUpTemplateDocument = MongoDocument<IFollowUpTemplate>;
export type WinLossAnalysisDocument = MongoDocument<IWinLossAnalysis>;
export type WinLossFactorDocument = MongoDocument<IWinLossFactor>;

// Email and sequences models
export type EmailConfigDocument = MongoDocument<IEmailConfig>;
export type EmailTrackingDocument = MongoDocument<IEmailTracking>;
export type EmailTemplateDocument = MongoDocument<IEmailTemplate>;
export type SequenceDocument = MongoDocument<ISequence>;
export type SequenceStepDocument = MongoDocument<ISequenceStep>;
export type SequenceEnrollmentDocument = MongoDocument<ISequenceEnrollment>;
export type TenantDomainDocument = MongoDocument<ITenantDomain>;

// Analytics models
export type AnalyticsEventDocument = MongoDocument<IAnalyticsEvent>;
export type EventsRawDocument = MongoDocument<IEventsRaw>;
export type AttributionResultsDocument = MongoDocument<IAttributionResults>;
export type CBICacheDocument = MongoDocument<ICBICache>;
export type AnalyticsDatasetDocument = MongoDocument<IAnalyticsDataset>;
export type MarketingCampaignDocument = MongoDocument<IMarketingCampaign>;
export type ProposalAnalyticsDocument = MongoDocument<IProposalAnalytics>;

// Workflow models
export type WorkflowDocument = MongoDocument<IWorkflow>;
export type WorkflowRunDocument = MongoDocument<IWorkflowRun>;
export type WorkflowVersionDocument = MongoDocument<IWorkflowVersion>;
export type InsightsCacheDocument = MongoDocument<IInsightsCache>;

// Graph models
export type EdgeDocument = MongoDocument<IEdge>;

// Subscription models
export type SubscriptionPlanDocument = MongoDocument<ISubscriptionPlan>;
export type TenantDocument = MongoDocument<ITenant>;
export type TenantSubscriptionDocument = MongoDocument<ITenantSubscription>;
export type FeatureDocument = MongoDocument<IFeature>;
export type TenantUsageDocument = MongoDocument<ITenantUsage>;
export type SubscriptionEventDocument = MongoDocument<ISubscriptionEvent>;
export type UserTenantDocument = MongoDocument<IUserTenant>;

// Experiment models
export type ExperimentDocument = MongoDocument<IExperiment>;
export type FeatureFlagDocument = MongoDocument<IFeatureFlag>;

/**
 * Type for MongoDB query results
 * Can be a single document, an array of documents, or null
 */
export type MongoQueryResult<T> = MongoDocument<T> | null;
export type MongoQueryArrayResult<T> = MongoDocument<T>[] | null;

/**
 * Type for MongoDB document creation
 * Omits the _id and __v fields that are automatically generated
 */
export type MongoDocumentCreate<T> = Omit<T, '_id' | '__v'>;

/**
 * Type for MongoDB document update
 * Makes all fields optional and omits the _id and __v fields
 */
export type MongoDocumentUpdate<T> = Partial<Omit<T, '_id' | '__v'>>;

/**
 * Type for MongoDB filter
 * Can include any field from the model interface
 */
export type MongoFilter<T> = {
  [P in keyof T]?: any;
} & {
  _id?: mongoose.Types.ObjectId | string;
};
