/**
 * Type declarations for mongodb-memory-server
 */

declare module 'mongodb-memory-server' {
  import { MongoClient } from 'mongodb';

  export interface MongoMemoryServerOpts {
    instance?: {
      port?: number;
      ip?: string;
      dbPath?: string;
      dbName?: string;
      storageEngine?: string;
      replSet?: string;
    };
    binary?: {
      version?: string;
      downloadDir?: string;
      platform?: string;
      arch?: string;
      checkMD5?: boolean;
    };
    autoStart?: boolean;
  }

  export class MongoMemoryServer {
    constructor(opts?: MongoMemoryServerOpts);
    start(): Promise<void>;
    stop(): Promise<void>;
    getUri(): Promise<string>;
    getConnectionString(): Promise<string>;
    getPort(): Promise<number>;
    getDbPath(): Promise<string>;
    getDbName(): Promise<string>;
  }
}
