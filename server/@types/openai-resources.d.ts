/**
 * Type declarations for OpenAI resources
 */

declare module 'openai/resources/chat' {
  export interface ChatCompletion {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
      index: number;
      message: ChatCompletionMessageParam;
      finish_reason: string;
    }>;
    usage: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  }

  export interface ChatCompletionChunk {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
      index: number;
      delta: Partial<ChatCompletionMessageParam>;
      finish_reason: string | null;
    }>;
  }

  export interface ChatCompletionCreateParamsBase {
    model: string;
    messages: Array<ChatCompletionMessageParam>;
    temperature?: number;
    top_p?: number;
    n?: number;
    max_tokens?: number;
    presence_penalty?: number;
    frequency_penalty?: number;
    logit_bias?: Record<string, number>;
    user?: string;
    response_format?: { type: 'json_object' } | { type: 'text' };
  }

  export interface ChatCompletionCreateParamsNonStreaming extends ChatCompletionCreateParamsBase {
    stream?: false;
  }

  export interface ChatCompletionCreateParamsStreaming extends ChatCompletionCreateParamsBase {
    stream: true;
  }

  export type Stream<T> = AsyncIterable<T>;
  export type ChatCompletionMessageParam =
    | ChatCompletionSystemMessageParam
    | ChatCompletionUserMessageParam
    | ChatCompletionAssistantMessageParam
    | ChatCompletionToolMessageParam
    | ChatCompletionFunctionMessageParam;

  export interface ChatCompletionSystemMessageParam {
    role: 'system';
    content: string;
    name?: string;
  }

  export interface ChatCompletionUserMessageParam {
    role: 'user';
    content: string | Array<ChatCompletionContentPart>;
    name?: string;
  }

  export interface ChatCompletionAssistantMessageParam {
    role: 'assistant';
    content: string | null;
    name?: string;
    tool_calls?: Array<ChatCompletionMessageToolCall>;
    function_call?: ChatCompletionMessageFunctionCall;
  }

  export interface ChatCompletionToolMessageParam {
    role: 'tool';
    content: string;
    tool_call_id: string;
  }

  export interface ChatCompletionFunctionMessageParam {
    role: 'function';
    content: string;
    name: string;
  }

  export interface ChatCompletionContentPart {
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
      detail?: 'auto' | 'low' | 'high';
    };
  }

  export interface ChatCompletionMessageToolCall {
    id: string;
    type: 'function';
    function: {
      name: string;
      arguments: string;
    };
  }

  export interface ChatCompletionMessageFunctionCall {
    name: string;
    arguments: string;
  }
}
