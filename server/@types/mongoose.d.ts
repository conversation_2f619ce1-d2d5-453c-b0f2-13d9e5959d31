import mongoose from 'mongoose';

// Extend the global namespace to include custom MongoDB types
declare global {
  // Define common MongoDB document types
  type MongoDocument<T> = mongoose.Document & T;

  // Define ObjectId type
  type ObjectId = mongoose.Types.ObjectId;

  // Define common model interfaces
  interface BaseDocument {
    _id: mongoose.Types.ObjectId;
    createdAt: Date;
    updatedAt: Date;
  }

  // Define tenant-scoped document interface
  interface TenantScopedDocument extends BaseDocument {
    tenantId: string;
  }

  // Define user-owned document interface
  interface UserOwnedDocument extends TenantScopedDocument {
    userId: string | mongoose.Types.ObjectId;
  }

  // Define MongoDB filter type
  type MongoFilter<T> = {
    [P in keyof T]?: T[P] | {
      $eq?: T[P];
      $gt?: T[P];
      $gte?: T[P];
      $in?: T[P][];
      $lt?: T[P];
      $lte?: T[P];
      $ne?: T[P];
      $nin?: T[P][];
      $regex?: RegExp | string;
      $options?: string;
    };
  } & {
    $and?: MongoFilter<T>[];
    $or?: MongoFilter<T>[];
    $nor?: MongoFilter<T>[];
  };

  // Define MongoDB update type
  type MongoUpdate<T> = {
    $set?: Partial<T>;
    $unset?: { [P in keyof T]?: '' | 1 | true };
    $inc?: { [P in keyof T]?: number };
    $push?: { [P in keyof T]?: any | { $each: any[] } };
    $pull?: { [P in keyof T]?: any };
    $addToSet?: { [P in keyof T]?: any | { $each: any[] } };
  };

  // Define MongoDB projection type
  type MongoProjection<T> = {
    [P in keyof T]?: 0 | 1;
  };

  // Define MongoDB sort type
  type MongoSort<T> = {
    [P in keyof T]?: 1 | -1;
  };

  // Define MongoDB population options
  interface MongoPopulateOptions {
    path: string;
    select?: string;
    model?: string;
    match?: Record<string, any>;
    options?: Record<string, any>;
    populate?: MongoPopulateOptions | MongoPopulateOptions[];
  }
}

// Extend mongoose namespace to include custom types
declare namespace mongoose {
  // Add custom mongoose types
  interface CustomMongooseOptions {
    lean?: boolean;
    populate?: string | string[] | Record<string, any> | Record<string, any>[];
    session?: mongoose.ClientSession;
  }

  // Type guard for ObjectId
  function isValidObjectId(id: any): boolean;

  // Type conversion utility
  function toObjectId(id: string | mongoose.Types.ObjectId): mongoose.Types.ObjectId;
}

// Type guards for MongoDB operations
export function isObjectId(value: any): value is mongoose.Types.ObjectId {
  return value instanceof mongoose.Types.ObjectId ||
         (typeof value === 'object' && value !== null &&
          typeof value.toString === 'function' &&
          /^[0-9a-fA-F]{24}$/.test(value.toString()));
}

export function isObjectIdString(value: any): value is string {
  return typeof value === 'string' && /^[0-9a-fA-F]{24}$/.test(value);
}

export function toObjectId(id: string | mongoose.Types.ObjectId): mongoose.Types.ObjectId {
  if (isObjectId(id)) return id;
  if (isObjectIdString(id)) return new mongoose.Types.ObjectId(id);
  throw new Error(`Invalid ObjectId: ${id}`);
}

export {};
