import { Request, Response } from 'express';
import OpenAI from 'openai';
import { storage } from './storage';
import { isOpenAIAvailable } from './ai';
import { isAIBridgeAvailable } from './ai-bridge';

// Use the shared OpenAI instance from ai.ts
let openai: OpenAI | null = null;
try {
  const apiKey = process.env.CRM_OPENAI_API_KEY || process.env.OPENAI_API_KEY;
  if (apiKey) {
    openai = new OpenAI({
      apiKey,
    });
  }
} catch (error) {
  console.warn('Failed to initialize OpenAI client in copilotkit.ts:', error);
}

// Define the types for CopilotKit requests
interface CopilotKitRequest {
  messages?: Array<{
    role: string;
    content: string;
    name?: string;
  }>;
  action?: string;
  context?: any;
  parameters?: Record<string, any>;
  tool_name?: string;
  tool_parameters?: Record<string, any>;
}

// Handle CopilotKit runtime request
export async function handleCopilotRuntime(req: Request, res: Response) {
  try {
    // Get user ID from session
    const userId = req.session.userId as number;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Parse request body
    const { messages, action, context, tool_name, tool_parameters } = req.body as CopilotKitRequest;

    // Handle tool calls if present
    if (tool_name) {
      return handleToolCall(tool_name, tool_parameters || {}, userId, res);
    }

    // Default action is chat completion
    if (!action || action === 'chat') {
      return handleChatRequest(messages || [], userId, context, res);
    }

    // Handle other actions
    switch (action) {
      case 'get_contacts':
        return handleGetContacts(userId, res);
      case 'get_companies':
        return handleGetCompanies(userId, res);
      case 'get_opportunities':
        return handleGetOpportunities(userId, res);
      case 'generate_insights':
        return handleGenerateInsights(userId, res);
      case 'get_activities':
        return handleGetActivities(userId, res);
      case 'get_relationships':
        return handleGetRelationships(userId, res);
      default:
        return res.status(400).json({ error: `Unknown action: ${action}` });
    }
  } catch (error) {
    console.error('Error in CopilotKit runtime:', error);
    return res.status(500).json({ error: 'An error occurred processing your request' });
  }
}

// Handle tool calls from CopilotKit
async function handleToolCall(toolName: string, parameters: Record<string, any>, userId: number, res: Response) {
  try {
    console.log(`Handling tool call: ${toolName} with parameters:`, parameters);

    switch (toolName) {
      case 'get_contacts':
        return handleGetContacts(userId, res);
      case 'get_companies':
        return handleGetCompanies(userId, res);
      case 'get_opportunities':
        return handleGetOpportunities(userId, res);
      case 'generate_insights':
        return handleGenerateInsights(userId, res);
      case 'get_activities':
        return handleGetActivities(userId, res);
      case 'get_relationships':
        return handleGetRelationships(userId, res);
      case 'search_contacts':
        return handleSearchContacts(parameters.query || '', userId, res);
      case 'search_companies':
        return handleSearchCompanies(parameters.query || '', userId, res);
      case 'search_opportunities':
        return handleSearchOpportunities(parameters.query || '', userId, res);
      default:
        return res.status(400).json({ error: `Unknown tool: ${toolName}` });
    }
  } catch (error) {
    console.error(`Error handling tool call ${toolName}:`, error);
    return res.status(500).json({ error: `Failed to execute tool ${toolName}` });
  }
}

// Handle chat requests
async function handleChatRequest(messages: any[], userId: number, context: any, res: Response) {
  try {
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'Invalid messages format' });
    }

    // Get the last user message
    const lastMessage = messages[messages.length - 1];

    // Check if OpenAI client is available
    if (!isOpenAIAvailable()) {
      const errorMessage = "I'm sorry, but the AI service is not available at the moment. Please ensure the CRM_OPENAI_API_KEY is set in your environment.";

      // Save user message with error
      await storage.createAiChat({
        userId: userId,
        message: lastMessage.content,
        response: errorMessage,
        metadata: { error: "api_key_missing" }
      });

      // Return error response in format expected by CopilotKit
      return res.json({
        id: new Date().getTime().toString(),
        object: "chat.completion",
        created: Math.floor(Date.now() / 1000),
        model: "unavailable",
        choices: [
          {
            index: 0,
            message: {
              role: "assistant",
              content: errorMessage
            },
            finish_reason: "stop"
          }
        ],
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
      });
    }

    // Try to use AI Bridge if available
    const aiBridgeAvailable = await isAIBridgeAvailable();

    // Prepare the system message with CRM context
    const systemMessage = {
      role: 'system',
      content: `You are an AI assistant for Aizako CRM, an AI-first customer relationship management system designed specifically for African markets.

      You have access to the following CRM data:
      - Contacts: People the user interacts with professionally
      - Companies: Organizations the user works with
      - Opportunities: Potential business deals in various stages
      - Relationships: Connections between contacts and companies
      - Activities: Interactions and events related to contacts and companies

      Be professional, helpful and concise in your responses. Focus on actionable insights.

      When asked about specific contacts, companies, or opportunities, you can retrieve this information.
      You can also generate insights based on the CRM data and suggest next steps.

      Ensure all advice and responses are ethical and focus on improving business relationships.
      ${context ? `\nAdditional context: ${JSON.stringify(context)}` : ''}`
    };

    // Add system message at the beginning if not already present
    const messagesWithSystem = messages[0]?.role === 'system'
      ? messages
      : [systemMessage, ...messages];

    // Generate response using OpenAI
    if (!openai) {
      throw new Error("OpenAI client is not available");
    }

    // Fetch some context data to help the AI
    const recentContacts = await storage.getContacts(5);
    const recentCompanies = await storage.getCompanies(5);
    const recentOpportunities = await storage.getOpportunities(5);

    // Add context to the system message
    messagesWithSystem[0].content += `\n\nRecent CRM data for context:
    - Recent contacts: ${JSON.stringify(recentContacts)}
    - Recent companies: ${JSON.stringify(recentCompanies)}
    - Recent opportunities: ${JSON.stringify(recentOpportunities)}`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: messagesWithSystem,
      tools: [
        {
          type: "function",
          function: {
            name: "get_contacts",
            description: "Get a list of contacts from the CRM",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          }
        },
        {
          type: "function",
          function: {
            name: "get_companies",
            description: "Get a list of companies from the CRM",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          }
        },
        {
          type: "function",
          function: {
            name: "get_opportunities",
            description: "Get a list of opportunities from the CRM",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          }
        },
        {
          type: "function",
          function: {
            name: "generate_insights",
            description: "Generate AI insights based on CRM data",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          }
        },
        {
          type: "function",
          function: {
            name: "search_contacts",
            description: "Search for contacts by name, email, or other fields",
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query"
                }
              },
              required: ["query"]
            }
          }
        }
      ]
    });

    // Save conversation to history
    await storage.createAiChat({
      userId: userId,
      message: lastMessage.content,
      response: response.choices[0].message.content || '',
      metadata: { context }
    });

    // Return the response in format expected by CopilotKit
    return res.json({
      id: new Date().getTime().toString(),
      object: "chat.completion",
      created: Math.floor(Date.now() / 1000),
      model: "gpt-4o",
      choices: [
        {
          index: 0,
          message: response.choices[0].message,
          finish_reason: response.choices[0].finish_reason,
        }
      ],
      usage: response.usage
    });
  } catch (error) {
    console.error('Error handling chat request:', error);
    return res.status(500).json({ error: 'Failed to process chat request' });
  }
}

// Handle contacts retrieval
async function handleGetContacts(userId: number, res: Response) {
  try {
    const contacts = await storage.getContacts();
    return res.json({ contacts });
  } catch (error) {
    console.error('Error getting contacts:', error);
    return res.status(500).json({ error: 'Failed to retrieve contacts' });
  }
}

// Handle companies retrieval
async function handleGetCompanies(userId: number, res: Response) {
  try {
    const companies = await storage.getCompanies();
    return res.json({ companies });
  } catch (error) {
    console.error('Error getting companies:', error);
    return res.status(500).json({ error: 'Failed to retrieve companies' });
  }
}

// Handle opportunities retrieval
async function handleGetOpportunities(userId: number, res: Response) {
  try {
    const opportunities = await storage.getOpportunities();
    return res.json({ opportunities });
  } catch (error) {
    console.error('Error getting opportunities:', error);
    return res.status(500).json({ error: 'Failed to retrieve opportunities' });
  }
}

// Handle activities retrieval
async function handleGetActivities(userId: number, res: Response) {
  try {
    const activities = await storage.getActivities();
    return res.json({ activities });
  } catch (error) {
    console.error('Error getting activities:', error);
    return res.status(500).json({ error: 'Failed to retrieve activities' });
  }
}

// Handle relationships retrieval
async function handleGetRelationships(userId: number, res: Response) {
  try {
    const relationships = await storage.getRelationships();
    return res.json({ relationships });
  } catch (error) {
    console.error('Error getting relationships:', error);
    return res.status(500).json({ error: 'Failed to retrieve relationships' });
  }
}

// Handle contact search
async function handleSearchContacts(query: string, userId: number, res: Response) {
  try {
    // For now, we'll just return all contacts since we don't have a search function
    // In a real implementation, you would filter contacts based on the query
    const contacts = await storage.getContacts();
    return res.json({ contacts });
  } catch (error) {
    console.error('Error searching contacts:', error);
    return res.status(500).json({ error: 'Failed to search contacts' });
  }
}

// Handle company search
async function handleSearchCompanies(query: string, userId: number, res: Response) {
  try {
    // For now, we'll just return all companies since we don't have a search function
    const companies = await storage.getCompanies();
    return res.json({ companies });
  } catch (error) {
    console.error('Error searching companies:', error);
    return res.status(500).json({ error: 'Failed to search companies' });
  }
}

// Handle opportunity search
async function handleSearchOpportunities(query: string, userId: number, res: Response) {
  try {
    // For now, we'll just return all opportunities since we don't have a search function
    const opportunities = await storage.getOpportunities();
    return res.json({ opportunities });
  } catch (error) {
    console.error('Error searching opportunities:', error);
    return res.status(500).json({ error: 'Failed to search opportunities' });
  }
}

// Handle insight generation
async function handleGenerateInsights(userId: number, res: Response) {
  try {
    // Check if OpenAI client is available
    if (!isOpenAIAvailable()) {
      return res.json({
        insights: [{
          title: "AI Service Unavailable",
          description: "The AI service is currently unavailable. Please ensure the CRM_OPENAI_API_KEY is set in your environment.",
          recommendation: "Please contact your system administrator to configure the OpenAI API key."
        }],
        error: "api_key_missing"
      });
    }

    const contacts = await storage.getContacts();
    const companies = await storage.getCompanies();
    const opportunities = await storage.getOpportunities();
    const activities = await storage.getActivities(20);

    // Use OpenAI for analysis
    const prompt = `
      As a CRM AI assistant for Aizako CRM, analyze the following data and generate 3 key insights that could help improve business relationships or identify opportunities:

      Contacts: ${JSON.stringify(contacts)}
      Companies: ${JSON.stringify(companies)}
      Opportunities: ${JSON.stringify(opportunities)}
      Recent Activities: ${JSON.stringify(activities)}

      For each insight, provide:
      1. A clear title
      2. A brief description of the finding
      3. A specific, actionable recommendation

      Focus on insights that are relevant to African markets and business contexts.
      Consider factors like relationship strength, opportunity progression, and potential network gaps.

      Format each insight as a JSON object with these fields: title, description, recommendation.
      Return an array of these 3 insight objects.
    `;

    if (!openai) {
      throw new Error("OpenAI client is not available");
    }
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [{ role: 'user', content: prompt }],
      response_format: { type: "json_object" }
    });

    // Parse and store insights
    const content = response.choices[0].message.content;
    if (!content) return res.json({ insights: [] });

    const parsedInsights = JSON.parse(content);
    const insights = Array.isArray(parsedInsights.insights)
      ? parsedInsights.insights
      : [parsedInsights];

    // Store insights in the database
    for (const insight of insights) {
      await storage.createInsight({
        title: insight.title,
        description: insight.description,
        targetType: "general",
        targetId: 0,
        importance: 8,
        category: "ai_generated",
        read: false
      });
    }

    return res.json({ insights });
  } catch (error) {
    console.error('Error generating insights:', error);
    return res.status(500).json({ error: 'Failed to generate insights' });
  }
}