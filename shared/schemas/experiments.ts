import { z } from 'zod';

/**
 * Experiment status schema
 */
export const experimentStatusSchema = z.enum([
  'draft',
  'running',
  'paused',
  'completed',
  'archived',
]);

/**
 * Experiment filter type schema
 */
export const experimentFilterTypeSchema = z.enum([
  'user',
  'tenant',
  'location',
  'device',
  'custom',
]);

/**
 * Experiment filter schema
 */
export const experimentFilterSchema = z.object({
  type: experimentFilterTypeSchema,
  value: z.any(),
});

/**
 * Experiment variant schema
 */
export const experimentVariantSchema = z.object({
  key: z.string().min(1).max(100),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  weight: z.number().min(0).max(100),
  config: z.record(z.any()).optional(),
});

/**
 * Experiment goal schema
 */
export const experimentGoalSchema = z.object({
  key: z.string().min(1).max(100),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  primary: z.boolean(),
});

/**
 * Experiment audience schema
 */
export const experimentAudienceSchema = z.object({
  percentage: z.number().min(0).max(100),
  userIds: z.array(z.string()).optional(),
  tenantIds: z.array(z.string()).optional(),
  filters: z.array(experimentFilterSchema).optional(),
});

/**
 * Experiment schema
 */
export const experimentSchema = z.object({
  id: z.string(),
  key: z.string().min(1).max(100).regex(/^[a-z0-9_.-]+$/),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  status: experimentStatusSchema,
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  variants: z.array(experimentVariantSchema).min(2),
  audience: experimentAudienceSchema,
  goals: z.array(experimentGoalSchema).min(1),
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Experiment context schema
 */
export const experimentContextSchema = z.object({
  userId: z.string(),
  tenantId: z.string().optional(),
  sessionId: z.string(),
  location: z.object({
    country: z.string().optional(),
    region: z.string().optional(),
  }).optional(),
  device: z.object({
    type: z.string().optional(),
    browser: z.string().optional(),
    os: z.string().optional(),
  }).optional(),
  custom: z.record(z.any()).optional(),
});

/**
 * Experiment conversion schema
 */
export const experimentConversionSchema = z.object({
  goalKey: z.string().min(1),
  timestamp: z.string(),
  value: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Experiment result schema
 */
export const experimentResultSchema = z.object({
  id: z.string(),
  experimentId: z.string(),
  userId: z.string(),
  tenantId: z.string().optional(),
  sessionId: z.string(),
  variant: z.string(),
  exposures: z.array(z.object({
    timestamp: z.string(),
    context: z.record(z.any()).optional(),
  })),
  conversions: z.array(experimentConversionSchema),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Create experiment request schema
 */
export const createExperimentRequestSchema = z.object({
  key: z.string().min(1).max(100).regex(/^[a-z0-9_.-]+$/),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  variants: z.array(z.object({
    key: z.string().min(1).max(100),
    name: z.string().min(1).max(100),
    description: z.string().optional(),
    weight: z.number().min(0).max(100),
    config: z.record(z.any()).optional(),
  })).min(2),
  audience: z.object({
    percentage: z.number().min(0).max(100).optional(),
    userIds: z.array(z.string()).optional(),
    tenantIds: z.array(z.string()).optional(),
    filters: z.array(experimentFilterSchema).optional(),
  }).optional(),
  goals: z.array(z.object({
    key: z.string().min(1).max(100),
    name: z.string().min(1).max(100),
    description: z.string().optional(),
    primary: z.boolean().optional(),
  })).min(1),
});

/**
 * Update experiment request schema
 */
export const updateExperimentRequestSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  status: experimentStatusSchema.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  variants: z.array(z.object({
    key: z.string().min(1).max(100),
    name: z.string().min(1).max(100),
    description: z.string().optional(),
    weight: z.number().min(0).max(100),
    config: z.record(z.any()).optional(),
  })).min(2).optional(),
  audience: z.object({
    percentage: z.number().min(0).max(100).optional(),
    userIds: z.array(z.string()).optional(),
    tenantIds: z.array(z.string()).optional(),
    filters: z.array(experimentFilterSchema).optional(),
  }).optional(),
  goals: z.array(z.object({
    key: z.string().min(1).max(100),
    name: z.string().min(1).max(100),
    description: z.string().optional(),
    primary: z.boolean().optional(),
  })).min(1).optional(),
});

/**
 * Track conversion request schema
 */
export const trackConversionRequestSchema = z.object({
  goalKey: z.string().min(1),
  value: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Funnel analysis request schema
 */
export const funnelAnalysisRequestSchema = z.object({
  steps: z.array(z.object({
    eventType: z.string().min(1),
    eventName: z.string().min(1),
  })).min(2),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  windowHours: z.number().optional(),
});
