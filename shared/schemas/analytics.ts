import { z } from 'zod';

/**
 * Analytics context schema
 */
export const analyticsContextSchema = z.object({
  page: z.object({
    url: z.string().optional(),
    path: z.string().optional(),
    title: z.string().optional(),
    referrer: z.string().optional(),
  }).optional(),
  userAgent: z.string().optional(),
  ip: z.string().optional(),
  location: z.object({
    country: z.string().optional(),
    region: z.string().optional(),
    city: z.string().optional(),
  }).optional(),
  device: z.object({
    type: z.string().optional(),
    browser: z.string().optional(),
    os: z.string().optional(),
  }).optional(),
});

/**
 * Analytics event schema
 */
export const analyticsEventSchema = z.object({
  id: z.string(),
  userId: z.string().optional(),
  tenantId: z.string().optional(),
  sessionId: z.string(),
  eventType: z.string().min(1),
  eventName: z.string().min(1),
  properties: z.record(z.any()),
  context: analyticsContextSchema,
  timestamp: z.string(),
});

/**
 * Analytics session schema
 */
export const analyticsSessionSchema = z.object({
  sessionId: z.string(),
  userId: z.string().optional(),
  tenantId: z.string().optional(),
  startTime: z.string(),
  endTime: z.string(),
  duration: z.number(),
  eventCount: z.number(),
  pages: z.array(z.string()),
});

/**
 * Analytics count schema
 */
export const analyticsCountSchema = z.object({
  _id: z.string(),
  count: z.number(),
});

/**
 * Funnel step schema
 */
export const funnelStepSchema = z.object({
  step: z.number(),
  eventType: z.string(),
  eventName: z.string(),
  count: z.number(),
  dropoff: z.number(),
  conversionRate: z.number(),
});

/**
 * Track event request schema
 */
export const trackEventRequestSchema = z.object({
  eventType: z.string().min(1),
  eventName: z.string().min(1),
  properties: z.record(z.any()).optional(),
  sessionId: z.string().optional(),
  timestamp: z.string().optional(),
  context: analyticsContextSchema.optional(),
});

/**
 * Get events request schema
 */
export const getEventsRequestSchema = z.object({
  userId: z.string().optional(),
  tenantId: z.string().optional(),
  sessionId: z.string().optional(),
  eventType: z.string().optional(),
  eventName: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  limit: z.number().optional(),
  offset: z.number().optional(),
});

/**
 * Get event counts request schema
 */
export const getEventCountsRequestSchema = z.object({
  userId: z.string().optional(),
  tenantId: z.string().optional(),
  eventType: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  groupBy: z.enum(['eventType', 'eventName', 'day', 'week', 'month']).optional(),
});

/**
 * Get sessions request schema
 */
export const getSessionsRequestSchema = z.object({
  userId: z.string().optional(),
  tenantId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  limit: z.number().optional(),
  offset: z.number().optional(),
});

/**
 * Funnel analysis request schema
 */
export const funnelAnalysisRequestSchema = z.object({
  steps: z.array(z.object({
    eventType: z.string().min(1),
    eventName: z.string().min(1),
  })).min(2),
  userId: z.string().optional(),
  tenantId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  windowHours: z.number().optional(),
});
