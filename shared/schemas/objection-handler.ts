/**
 * Zod schemas for Objection Handler
 */

import { z } from 'zod';

/**
 * Objection class enum schema
 */
export const objectionClassSchema = z.enum([
  'price',
  'value',
  'competition',
  'timing',
  'authority',
  'need',
  'trust',
  'implementation',
  'technical',
  'legal',
  'other'
]);

/**
 * Objection classification request schema
 */
export const objectionClassificationRequestSchema = z.object({
  objectionText: z.string().min(1, "Objection text is required"),
  opportunityId: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
});

/**
 * Objection classification response schema
 */
export const objectionClassificationResponseSchema = z.object({
  objectionClass: objectionClassSchema,
  confidence: z.number().min(0).max(1),
  alternativeClasses: z.array(
    z.object({
      objectionClass: objectionClassSchema,
      confidence: z.number().min(0).max(1),
    })
  ).optional(),
});

/**
 * Rebuttal schema
 */
export const rebuttalSchema = z.object({
  id: z.string(),
  text: z.string(),
  winRate: z.number().min(0).max(1),
  evidence: z.array(z.string()).optional(),
});

/**
 * Supporting evidence schema
 */
export const supportingEvidenceSchema = z.object({
  id: z.string(),
  title: z.string(),
  type: z.string(),
  description: z.string(),
  url: z.string().url().optional(),
});

/**
 * Real-time objection request schema
 */
export const realTimeObjectionRequestSchema = z.object({
  objectionText: z.string().min(1, "Objection text is required"),
  opportunityId: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
});

/**
 * Real-time objection response schema
 */
export const realTimeObjectionResponseSchema = z.object({
  responseId: z.string(),
  objectionClass: objectionClassSchema,
  response: z.string(),
  rebuttals: z.array(rebuttalSchema),
  supportingEvidence: z.array(supportingEvidenceSchema),
});

/**
 * Response usage request schema
 */
export const responseUsageRequestSchema = z.object({
  responseId: z.string(),
  wasSuccessful: z.boolean(),
  notes: z.string().optional(),
});

/**
 * Response usage response schema
 */
export const responseUsageResponseSchema = z.object({
  id: z.string(),
  responseId: z.string(),
  wasSuccessful: z.boolean(),
  timestamp: z.date(),
});

/**
 * Objection database record schema
 */
export const objectionSchema = z.object({
  id: z.union([z.string(), z.number()]),
  text: z.string(),
  class: objectionClassSchema,
  opportunityId: z.union([z.string(), z.number()]).optional(),
  contactId: z.union([z.string(), z.number()]).optional(),
  companyId: z.union([z.string(), z.number()]).optional(),
  userId: z.union([z.string(), z.number()]),
  tenantId: z.union([z.string(), z.number()]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Response database record schema
 */
export const objectionResponseSchema = z.object({
  id: z.union([z.string(), z.number()]),
  objectionId: z.union([z.string(), z.number()]),
  text: z.string(),
  wasSuccessful: z.boolean().optional(),
  usageCount: z.number().int().nonnegative(),
  successCount: z.number().int().nonnegative(),
  createdAt: z.date(),
  updatedAt: z.date(),
});
