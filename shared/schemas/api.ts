/**
 * API-specific Zod schemas for the Aizako CRM project
 * These schemas define validation rules for API requests and responses
 */

import { z } from 'zod';
import {
  createUserSchema,
  updateUserSchema,
  createContactSchema,
  updateContactSchema,
  createCompanySchema,
  updateCompanySchema,
  createOpportunitySchema,
  updateOpportunitySchema,
  createActivitySchema,
  updateActivitySchema,
  userBaseSchema,
  contactBaseSchema,
  companyBaseSchema,
  opportunityBaseSchema,
  activityBaseSchema,
} from './core';

/**
 * Common schemas
 */
const idSchema = z.union([z.string(), z.number()]);

const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(20),
});

const sortSchema = z.object({
  field: z.string(),
  direction: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * Authentication
 */
export const loginRequestSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

export const registerRequestSchema = createUserSchema.extend({
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export const authResponseSchema = z.object({
  user: userBaseSchema.extend({
    id: idSchema,
  }),
  token: z.string().optional(),
});

/**
 * Contacts
 */
export const createContactRequestSchema = createContactSchema;

export const updateContactRequestSchema = z.object({
  id: idSchema,
}).merge(updateContactSchema);

export const contactResponseSchema = z.object({
  contact: contactBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  }),
});

export const contactsResponseSchema = z.object({
  contacts: z.array(contactBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  })),
  total: z.number().int().nonnegative(),
  page: z.number().int().positive(),
  pageSize: z.number().int().positive(),
});

/**
 * Companies
 */
export const createCompanyRequestSchema = createCompanySchema;

export const updateCompanyRequestSchema = z.object({
  id: idSchema,
}).merge(updateCompanySchema);

export const companyResponseSchema = z.object({
  company: companyBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  }),
});

export const companiesResponseSchema = z.object({
  companies: z.array(companyBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  })),
  total: z.number().int().nonnegative(),
  page: z.number().int().positive(),
  pageSize: z.number().int().positive(),
});

/**
 * Opportunities
 */
export const createOpportunityRequestSchema = createOpportunitySchema;

export const updateOpportunityRequestSchema = z.object({
  id: idSchema,
}).merge(updateOpportunitySchema);

export const opportunityResponseSchema = z.object({
  opportunity: opportunityBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  }),
});

export const opportunitiesResponseSchema = z.object({
  opportunities: z.array(opportunityBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  })),
  total: z.number().int().nonnegative(),
  page: z.number().int().positive(),
  pageSize: z.number().int().positive(),
});

/**
 * Activities
 */
export const createActivityRequestSchema = createActivitySchema;

export const updateActivityRequestSchema = z.object({
  id: idSchema,
}).merge(updateActivitySchema);

export const activityResponseSchema = z.object({
  activity: activityBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  }),
});

export const activitiesResponseSchema = z.object({
  activities: z.array(activityBaseSchema.extend({
    id: idSchema,
    createdAt: z.date(),
    updatedAt: z.date(),
    createdBy: idSchema.optional(),
  })),
  total: z.number().int().nonnegative(),
  page: z.number().int().positive(),
  pageSize: z.number().int().positive(),
});

/**
 * Type exports
 */
export type LoginRequest = z.infer<typeof loginRequestSchema>;
export type RegisterRequest = z.infer<typeof registerRequestSchema>;
export type AuthResponse = z.infer<typeof authResponseSchema>;

export type CreateContactRequest = z.infer<typeof createContactRequestSchema>;
export type UpdateContactRequest = z.infer<typeof updateContactRequestSchema>;
export type ContactResponse = z.infer<typeof contactResponseSchema>;
export type ContactsResponse = z.infer<typeof contactsResponseSchema>;

export type CreateCompanyRequest = z.infer<typeof createCompanyRequestSchema>;
export type UpdateCompanyRequest = z.infer<typeof updateCompanyRequestSchema>;
export type CompanyResponse = z.infer<typeof companyResponseSchema>;
export type CompaniesResponse = z.infer<typeof companiesResponseSchema>;

export type CreateOpportunityRequest = z.infer<typeof createOpportunityRequestSchema>;
export type UpdateOpportunityRequest = z.infer<typeof updateOpportunityRequestSchema>;
export type OpportunityResponse = z.infer<typeof opportunityResponseSchema>;
export type OpportunitiesResponse = z.infer<typeof opportunitiesResponseSchema>;

export type CreateActivityRequest = z.infer<typeof createActivityRequestSchema>;
export type UpdateActivityRequest = z.infer<typeof updateActivityRequestSchema>;
export type ActivityResponse = z.infer<typeof activityResponseSchema>;
export type ActivitiesResponse = z.infer<typeof activitiesResponseSchema>;
