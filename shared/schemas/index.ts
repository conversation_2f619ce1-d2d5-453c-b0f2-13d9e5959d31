/**
 * Central export file for all Zod schemas in the Aizako CRM project
 * This file re-exports all schemas from the various schema modules
 */

// Core entity schemas
export * from './core';

// API request/response schemas
export * from './api';

// Form-specific schemas
export * from './forms';

// Feature flags schemas
export * from './feature-flags';

// Experiments schemas
export {
  experimentTypeSchema,
  experimentVariantSchema,
  experimentUserSchema,
  experimentResultSchema,
  experimentConfigSchema,
  funnelStepSchema,
  funnelAnalysisRequestSchema as experimentFunnelAnalysisRequestSchema
} from './experiments';

// Analytics schemas
export {
  analyticsEventSchema as coreAnalyticsEventSchema,
  analyticsEventTypeSchema,
  analyticsFilterSchema,
  analyticsTimeframeSchema,
  analyticsMetricSchema,
  analyticsDimensionSchema,
  analyticsQuerySchema,
  analyticsResultSchema,
  analyticsChartTypeSchema,
  analyticsDataPointSchema,
  analyticsInsightSchema,
  funnelAnalysisRequestSchema as analyticsFunnelAnalysisRequestSchema
} from './analytics';

// Analytics events schemas
export {
  analyticsEventSchema as trackingAnalyticsEventSchema,
  analyticsEventTypeSchema as trackingEventTypeSchema,
  analyticsEventDataSchema,
  analyticsEventContextSchema,
  analyticsEventUserSchema,
  analyticsEventPageSchema
} from './analytics-events';

// Objection Handler schemas
export * from './objection-handler';

// Proposal Generator schemas
export * from './proposal-generator';
