/**
 * Form-specific Zod schemas for the Aizako CRM project
 * These schemas define validation rules for forms in the UI
 */

import { z } from 'zod';
import {
  contactStatusEnum,
  companyStatusEnum,
  opportunityStageEnum,
  priorityEnum,
} from './core';

/**
 * Authentication Forms
 */
export const loginFormSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
  rememberMe: z.boolean().optional(),
});

export const registerFormSchema = z.object({
  displayName: z.string().min(1, "Display name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
  terms: z.boolean().refine(val => val === true, {
    message: "You must accept the terms and conditions",
  }),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export const passwordResetFormSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export const passwordChangeFormSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "New password must be at least 8 characters"),
  confirmPassword: z.string().min(1, "Please confirm your new password"),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Contact Forms
 */
export const contactFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address").optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  title: z.string().optional().or(z.literal('')),
  status: contactStatusEnum,
  notes: z.string().optional().or(z.literal('')),
  source: z.string().optional().or(z.literal('')),
  companyId: z.union([z.string(), z.number()]).optional().nullable(),
});

/**
 * Company Forms
 */
export const companyFormSchema = z.object({
  name: z.string().min(1, "Company name is required"),
  industry: z.string().optional().or(z.literal('')),
  website: z.string().optional().or(z.literal('')),
  employees: z.number().int().positive().optional().nullable(),
  status: companyStatusEnum,
  notes: z.string().optional().or(z.literal('')),
});

/**
 * Opportunity Forms
 */
export const opportunityFormSchema = z.object({
  name: z.string().min(1, "Opportunity name is required"),
  value: z.number().positive("Value must be positive"),
  currency: z.string().default('USD'),
  stage: opportunityStageEnum,
  closeDate: z.date().optional().nullable(),
  probability: z.number().min(0).max(100).optional().nullable(),
  notes: z.string().optional().or(z.literal('')),
  contactId: z.union([z.string(), z.number()]).optional().nullable(),
  companyId: z.union([z.string(), z.number()]).optional().nullable(),
});

/**
 * Activity Forms
 */
export const activityFormSchema = z.object({
  type: z.string().min(1, "Activity type is required"),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional().or(z.literal('')),
  dueDate: z.date().optional().nullable(),
  priority: priorityEnum.optional(),
  contactId: z.union([z.string(), z.number()]).optional().nullable(),
  companyId: z.union([z.string(), z.number()]).optional().nullable(),
  opportunityId: z.union([z.string(), z.number()]).optional().nullable(),
  assignedTo: z.union([z.string(), z.number()]).optional().nullable(),
});

/**
 * Interaction Forms
 */
export const interactionFormSchema = z.object({
  type: z.enum(['email', 'call', 'meeting', 'chat', 'social', 'note', 'task', 'other']),
  timestamp: z.date(),
  summary: z.string().min(1, "Summary is required"),
  content: z.string().optional().or(z.literal('')),
  sentiment: z.enum(['positive', 'neutral', 'negative']).optional(),
  direction: z.enum(['inbound', 'outbound']).optional(),
  nextAction: z.object({
    type: z.enum(['email', 'call', 'meeting', 'task', 'other']).optional(),
    description: z.string().optional().or(z.literal('')),
    dueDate: z.date().optional().nullable(),
    priority: priorityEnum.optional(),
  }).optional(),
});

/**
 * Settings Forms
 */
export const profileFormSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  title: z.string().optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  bio: z.string().optional().or(z.literal('')),
  avatarUrl: z.string().optional().or(z.literal('')),
});

export const apiKeyFormSchema = z.object({
  openaiApiKey: z.string().optional().or(z.literal('')),
  voyageApiKey: z.string().optional().or(z.literal('')),
});

/**
 * Tenant Forms
 */
export const tenantFormSchema = z.object({
  name: z.string().min(1, "Tenant name is required"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens"),
  settings: z.object({
    timezone: z.string().optional(),
    locale: z.string().optional(),
    branding: z.object({
      logo: z.string().optional().or(z.literal('')),
      primaryColor: z.string().optional().or(z.literal('')),
      accentColor: z.string().optional().or(z.literal('')),
    }).optional(),
  }).optional(),
});

/**
 * Email Configuration Forms
 */
export const emailConfigFormSchema = z.object({
  provider: z.enum(['resend', 'smtp', 'gmail', 'outlook']),
  apiKey: z.string().optional().or(z.literal('')),
  host: z.string().optional().or(z.literal('')),
  port: z.number().int().positive().optional().nullable(),
  username: z.string().optional().or(z.literal('')),
  password: z.string().optional().or(z.literal('')),
  fromEmail: z.string().email("Invalid email address"),
  fromName: z.string().min(1, "From name is required"),
  replyTo: z.string().email("Invalid email address").optional().or(z.literal('')),
  trackOpens: z.boolean(),
  trackClicks: z.boolean(),
  customDomain: z.string().optional().or(z.literal('')),
});

/**
 * Type exports
 */
export type LoginFormValues = z.infer<typeof loginFormSchema>;
export type RegisterFormValues = z.infer<typeof registerFormSchema>;
export type PasswordResetFormValues = z.infer<typeof passwordResetFormSchema>;
export type PasswordChangeFormValues = z.infer<typeof passwordChangeFormSchema>;

export type ContactFormValues = z.infer<typeof contactFormSchema>;
export type CompanyFormValues = z.infer<typeof companyFormSchema>;
export type OpportunityFormValues = z.infer<typeof opportunityFormSchema>;
export type ActivityFormValues = z.infer<typeof activityFormSchema>;
export type InteractionFormValues = z.infer<typeof interactionFormSchema>;

export type ProfileFormValues = z.infer<typeof profileFormSchema>;
export type ApiKeyFormValues = z.infer<typeof apiKeyFormSchema>;
export type TenantFormValues = z.infer<typeof tenantFormSchema>;
export type EmailConfigFormValues = z.infer<typeof emailConfigFormSchema>;
