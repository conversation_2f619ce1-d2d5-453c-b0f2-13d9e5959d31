/**
 * Zod schemas for Analytics Events
 */

import { z } from 'zod';
import { EventType } from '../types/analytics-events';

/**
 * EventType enum schema
 */
export const eventTypeSchema = z.nativeEnum(EventType);

/**
 * Base Analytics Event schema
 */
export const analyticsEventSchema = z.object({
  id: z.union([z.string(), z.number()]).optional(),
  type: eventTypeSchema,
  tenantId: z.string(),
  userId: z.string(),
  timestamp: z.date().optional(),
  entityId: z.string().optional(),
  entityType: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Wizard Event schema
 */
export const wizardEventSchema = analyticsEventSchema.extend({
  type: z.enum([
    EventType.WIZARD_USED,
    EventType.WIZARD_SUCCESS,
    EventType.WIZARD_FAILURE
  ]),
  metadata: z.object({
    wizardType: z.enum([
      'meeting_prep',
      'objection_handler',
      'proposal_generator',
      'follow_up_coach',
      'win_loss_analyzer'
    ]),
    result: z.enum(['success', 'failure']).optional(),
    duration: z.number().optional(),
  }).and(z.record(z.any())),
});

/**
 * Meeting Prep Event schema
 */
export const meetingPrepEventSchema = analyticsEventSchema.extend({
  type: z.enum([
    EventType.MEETING_PREP_GENERATED,
    EventType.MEETING_PREP_VIEWED,
    EventType.MEETING_PREP_ADDED_TO_AGENDA,
    EventType.MEETING_PREP_FEEDBACK
  ]),
  entityType: z.enum(['contact', 'company', 'opportunity']),
  metadata: z.object({
    meetingId: z.string().optional(),
    contactId: z.string().optional(),
    companyId: z.string().optional(),
    opportunityId: z.string().optional(),
    feedbackRating: z.number().min(1).max(5).optional(),
    feedbackComments: z.string().optional(),
  }).and(z.record(z.any())),
});

/**
 * Objection Handler Event schema
 */
export const objectionHandlerEventSchema = analyticsEventSchema.extend({
  type: z.enum([
    EventType.OBJECTION_CLASSIFIED,
    EventType.OBJECTION_RESPONSE_GENERATED,
    EventType.OBJECTION_RESPONSE_USED,
    EventType.OBJECTION_RESPONSE_FEEDBACK
  ]),
  metadata: z.object({
    objectionId: z.string().optional(),
    objectionText: z.string().optional(),
    objectionClass: z.string().optional(),
    responseId: z.string().optional(),
    contactId: z.string().optional(),
    companyId: z.string().optional(),
    opportunityId: z.string().optional(),
    feedbackRating: z.number().min(1).max(5).optional(),
    wasSuccessful: z.boolean().optional(),
  }).and(z.record(z.any())),
});

/**
 * Proposal Generator Event schema
 */
export const proposalGeneratorEventSchema = analyticsEventSchema.extend({
  type: z.enum([
    EventType.PROPOSAL_GENERATED,
    EventType.PROPOSAL_VIEWED,
    EventType.PROPOSAL_SENT,
    EventType.PROPOSAL_ACCEPTED,
    EventType.PROPOSAL_REJECTED,
    EventType.PROPOSAL_CLAUSES_UPDATED
  ]),
  entityId: z.string(),
  entityType: z.literal('proposal'),
  metadata: z.object({
    opportunityId: z.string(),
    templateId: z.string().optional(),
    contactId: z.string().optional(),
    companyId: z.string().optional(),
    format: z.enum(['html', 'pdf', 'docx', 'markdown', 'claude-html']).optional(),
    clauses: z.array(z.string()).optional(),
    rejectionReason: z.string().optional(),
  }).and(z.record(z.any())),
});

/**
 * BI Event schema
 */
export const biEventSchema = analyticsEventSchema.extend({
  type: z.enum([
    EventType.BI_QUERY_EXECUTED,
    EventType.BI_CHART_GENERATED,
    EventType.BI_INSIGHT_GENERATED
  ]),
  metadata: z.object({
    query: z.string().optional(),
    chartType: z.string().optional(),
    insightType: z.string().optional(),
    duration: z.number().optional(),
    datasetId: z.string().optional(),
  }).and(z.record(z.any())),
});

/**
 * Attribution Event schema
 */
export const attributionEventSchema = analyticsEventSchema.extend({
  type: z.enum([
    EventType.ATTRIBUTION_MODEL_UPDATED,
    EventType.ATTRIBUTION_TOUCHPOINT_RECORDED,
    EventType.ATTRIBUTION_REPORT_GENERATED
  ]),
  metadata: z.object({
    modelType: z.enum([
      'first_touch',
      'last_touch',
      'linear',
      'time_decay',
      'position_based',
      'custom'
    ]).optional(),
    touchpointType: z.string().optional(),
    channelId: z.string().optional(),
    campaignId: z.string().optional(),
    contactId: z.string().optional(),
    opportunityId: z.string().optional(),
  }).and(z.record(z.any())),
});
