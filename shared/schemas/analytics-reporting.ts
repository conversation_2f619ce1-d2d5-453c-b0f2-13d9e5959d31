import { z } from 'zod';

/**
 * Chart specification schema
 */
export const chartSpecSchema = z.object({
  type: z.string(),
  data: z.any(),
  options: z.record(z.any()),
});

/**
 * Suggested experiment schema
 */
export const suggestedExperimentSchema = z.object({
  title: z.string(),
  description: z.string(),
  estimated_impact: z.string(),
  confidence: z.number().min(0).max(100),
  workflow_template: z.record(z.any()).optional(),
});

/**
 * Dataset field schema
 */
export const datasetFieldSchema = z.object({
  name: z.string(),
  display_name: z.string(),
  data_type: z.enum(['string', 'number', 'date', 'boolean']),
  description: z.string(),
  is_dimension: z.boolean(),
  is_metric: z.boolean(),
  format: z.enum(['currency', 'percentage', 'date', 'number', 'text']).optional(),
  aggregation: z.enum(['sum', 'avg', 'count', 'min', 'max', 'distinct']).optional(),
});

/**
 * Campaign cost schema
 */
export const campaignCostSchema = z.object({
  date: z.string().datetime(),
  amount: z.number().min(0),
  currency: z.string(),
  source: z.enum(['manual', 'api']),
});

/**
 * Events raw schema
 */
export const eventsRawSchema = z.object({
  id: z.string(),
  tenant_id: z.string(),
  visitor_id: z.string().optional(),
  contact_id: z.string().optional(),
  timestamp: z.string().datetime(),
  channel: z.enum(['web', 'email', 'ads', 'social', 'voice', 'offline']),
  campaign: z.string().optional(),
  medium: z.string().optional(),
  source: z.string().optional(),
  event_type: z.string(),
  meta_json: z.record(z.any()),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * Attribution results schema
 */
export const attributionResultsSchema = z.object({
  id: z.string(),
  tenant_id: z.string(),
  window: z.string(),
  model_type: z.enum(['markov', 'shapley', 'first_touch', 'last_touch', 'linear']),
  channel: z.string(),
  campaign: z.string().optional(),
  medium: z.string().optional(),
  source: z.string().optional(),
  creative: z.string().optional(),
  keyword: z.string().optional(),
  credit_pct: z.number().min(0).max(100),
  cost: z.number().min(0),
  revenue: z.number().min(0),
  roi: z.number(),
  conversions: z.number().min(0),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * CBI cache schema
 */
export const cbiCacheSchema = z.object({
  id: z.string(),
  tenant_id: z.string(),
  question: z.string(),
  question_hash: z.string(),
  result_json: z.record(z.any()),
  chart_spec: chartSpecSchema,
  narrative: z.string(),
  root_cause_analysis: z.string().optional(),
  suggested_experiments: z.array(suggestedExperimentSchema).optional(),
  dataset_ref: z.string(),
  query_type: z.enum(['sql', 'cypher', 'vector']),
  raw_query: z.string().optional(),
  execution_time_ms: z.number().min(0),
  created_at: z.string().datetime(),
  expires_at: z.string().datetime(),
});

/**
 * Analytics dataset schema
 */
export const analyticsDatasetSchema = z.object({
  id: z.string(),
  tenant_id: z.string(),
  name: z.string(),
  display_name: z.string(),
  description: z.string(),
  source_type: z.enum(['bigquery', 'mongodb', 'neo4j']),
  source_config: z.record(z.any()),
  fields: z.array(datasetFieldSchema),
  is_active: z.boolean(),
  refresh_frequency: z.enum(['hourly', 'daily', 'weekly']),
  last_refreshed_at: z.string().datetime().optional(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * Marketing campaign schema
 */
export const marketingCampaignSchema = z.object({
  id: z.string(),
  tenant_id: z.string(),
  name: z.string(),
  description: z.string(),
  channel: z.enum(['web', 'email', 'ads', 'social', 'voice', 'offline']),
  platform: z.string().optional(),
  utm_campaign: z.string().optional(),
  utm_medium: z.string().optional(),
  utm_source: z.string().optional(),
  start_date: z.string().datetime(),
  end_date: z.string().datetime().optional(),
  status: z.enum(['draft', 'active', 'paused', 'completed']),
  budget: z.number().min(0),
  currency: z.string(),
  costs: z.array(campaignCostSchema),
  total_cost: z.number().min(0),
  total_impressions: z.number().min(0),
  total_clicks: z.number().min(0),
  total_conversions: z.number().min(0),
  conversion_value: z.number().min(0),
  tags: z.array(z.string()),
  created_by: z.string(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * API request/response schemas
 */

// Conversational BI
export const askBIRequestSchema = z.object({
  question: z.string().min(1),
  filters: z.record(z.any()).optional(),
  dataset_ref: z.string().optional(),
  refresh: z.boolean().optional(),
  tenant_id: z.string(),
});

// Attribution
export const runAttributionRequestSchema = z.object({
  window: z.string().min(1),
  model_type: z.enum(['markov', 'shapley', 'first_touch', 'last_touch', 'linear']),
  tenant_id: z.string(),
  force_refresh: z.boolean().optional(),
});

export const getAttributionResultsRequestSchema = z.object({
  window: z.string().min(1),
  model_type: z.enum(['markov', 'shapley', 'first_touch', 'last_touch', 'linear']),
  tenant_id: z.string(),
  group_by: z.enum(['channel', 'campaign', 'medium', 'source', 'creative', 'keyword']).optional(),
  limit: z.number().min(1).max(100).optional(),
  sort_by: z.enum(['credit_pct', 'roi', 'revenue', 'cost', 'conversions']).optional(),
  sort_order: z.enum(['asc', 'desc']).optional(),
});

// Budget optimization
export const budgetOptimizationRequestSchema = z.object({
  window: z.string().min(1),
  model_type: z.enum(['markov', 'shapley']),
  tenant_id: z.string(),
  optimization_target: z.enum(['revenue', 'conversions', 'roi']),
  budget_constraint: z.number().min(0).optional(),
});

// Events collection
export const trackEventRequestSchema = z.object({
  tenant_id: z.string(),
  visitor_id: z.string().optional(),
  contact_id: z.string().optional(),
  timestamp: z.string().datetime().optional(),
  channel: z.enum(['web', 'email', 'ads', 'social', 'voice', 'offline']),
  campaign: z.string().optional(),
  medium: z.string().optional(),
  source: z.string().optional(),
  event_type: z.string().min(1),
  meta_json: z.record(z.any()),
});

// Datasets
export const getDatasetsRequestSchema = z.object({
  tenant_id: z.string(),
  is_active: z.boolean().optional(),
});
