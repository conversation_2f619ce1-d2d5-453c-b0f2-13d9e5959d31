/**
 * Core Zod schemas for the Aizako CRM project
 * These schemas define validation rules for the core entities
 */

import { z } from 'zod';

/**
 * Common validation patterns
 */
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
const urlRegex = /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

/**
 * Status enums
 */
export const contactStatusEnum = z.enum(['active', 'inactive', 'lead', 'prospect', 'customer']);
export const companyStatusEnum = z.enum(['active', 'inactive', 'lead', 'prospect', 'customer']);
export const opportunityStageEnum = z.enum(['discovery', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']);
export const priorityEnum = z.enum(['low', 'medium', 'high']);
export const tenantStatusEnum = z.enum(['active', 'suspended', 'deleted']);
export const subscriptionStatusEnum = z.enum(['active', 'trialing', 'past_due', 'canceled', 'expired']);
export const billingPeriodEnum = z.enum(['monthly', 'yearly', 'custom']);

/**
 * Base schemas
 */

// User schema
export const userBaseSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Invalid email address"),
  fullName: z.string().optional(),
  preferences: z.record(z.any()).optional(),
});

// Contact schema
export const contactBaseSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address").optional(),
  phone: z.string().regex(phoneRegex, "Invalid phone number").optional(),
  title: z.string().optional(),
  status: contactStatusEnum.default('lead'),
  notes: z.string().optional(),
  source: z.string().optional(),
  companyId: z.union([z.string(), z.number()]).optional(),
  aiEnrichment: z.record(z.any()).optional(),
});

// Company schema
export const companyBaseSchema = z.object({
  name: z.string().min(1, "Company name is required"),
  industry: z.string().optional(),
  website: z.string().regex(urlRegex, "Invalid website URL").optional(),
  employees: z.number().int().positive().optional(),
  status: companyStatusEnum.default('lead'),
  notes: z.string().optional(),
  aiEnrichment: z.record(z.any()).optional(),
});

// Opportunity schema
export const opportunityBaseSchema = z.object({
  name: z.string().min(1, "Opportunity name is required"),
  value: z.number().positive("Value must be positive"),
  currency: z.string().default('USD'),
  stage: opportunityStageEnum.default('discovery'),
  closeDate: z.date().optional(),
  probability: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  contactId: z.union([z.string(), z.number()]).optional(),
  companyId: z.union([z.string(), z.number()]).optional(),
  aiInsights: z.record(z.any()).optional(),
});

// Activity schema
export const activityBaseSchema = z.object({
  type: z.string().min(1, "Activity type is required"),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  dueDate: z.date().optional(),
  completed: z.boolean().default(false),
  completedAt: z.date().optional(),
  priority: priorityEnum.optional(),
  contactId: z.union([z.string(), z.number()]).optional(),
  companyId: z.union([z.string(), z.number()]).optional(),
  opportunityId: z.union([z.string(), z.number()]).optional(),
  assignedTo: z.union([z.string(), z.number()]).optional(),
});

// Tenant schema
export const tenantBaseSchema = z.object({
  name: z.string().min(1, "Tenant name is required"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens"),
  ownerId: z.union([z.string(), z.number()]),
  settings: z.object({
    timezone: z.string().optional(),
    locale: z.string().optional(),
    branding: z.object({
      logo: z.string().optional(),
      primaryColor: z.string().optional(),
      accentColor: z.string().optional(),
    }).optional(),
  }).optional(),
  status: tenantStatusEnum.default('active'),
});

// Subscription plan schema
export const subscriptionPlanBaseSchema = z.object({
  name: z.string().min(1, "Plan name is required"),
  description: z.string().optional(),
  status: z.enum(['active', 'inactive', 'archived']).default('active'),
  isDefault: z.boolean().default(false),
  sortOrder: z.number().int().default(0),
  price: z.number().nonnegative("Price must be non-negative"),
  currency: z.string().default('USD'),
  billingPeriod: billingPeriodEnum.default('monthly'),
  trialDays: z.number().int().nonnegative().default(0),
  limits: z.record(z.number()),
  features: z.record(z.boolean()),
});

/**
 * Create, Update, and Response schemas
 */

// User schemas
export const createUserSchema = userBaseSchema.extend({
  password: z.string().min(8, "Password must be at least 8 characters"),
});

export const updateUserSchema = userBaseSchema.partial();

// Contact schemas
export const createContactSchema = contactBaseSchema;
export const updateContactSchema = contactBaseSchema.partial();

// Company schemas
export const createCompanySchema = companyBaseSchema;
export const updateCompanySchema = companyBaseSchema.partial();

// Opportunity schemas
export const createOpportunitySchema = opportunityBaseSchema;
export const updateOpportunitySchema = opportunityBaseSchema.partial();

// Activity schemas
export const createActivitySchema = activityBaseSchema;
export const updateActivitySchema = activityBaseSchema.partial();

// Tenant schemas
export const createTenantSchema = tenantBaseSchema;
export const updateTenantSchema = tenantBaseSchema.partial();

// Subscription plan schemas
export const createSubscriptionPlanSchema = subscriptionPlanBaseSchema;
export const updateSubscriptionPlanSchema = subscriptionPlanBaseSchema.partial();

/**
 * Type exports
 */
export type User = z.infer<typeof userBaseSchema>;
export type CreateUser = z.infer<typeof createUserSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;

export type Contact = z.infer<typeof contactBaseSchema>;
export type CreateContact = z.infer<typeof createContactSchema>;
export type UpdateContact = z.infer<typeof updateContactSchema>;

export type Company = z.infer<typeof companyBaseSchema>;
export type CreateCompany = z.infer<typeof createCompanySchema>;
export type UpdateCompany = z.infer<typeof updateCompanySchema>;

export type Opportunity = z.infer<typeof opportunityBaseSchema>;
export type CreateOpportunity = z.infer<typeof createOpportunitySchema>;
export type UpdateOpportunity = z.infer<typeof updateOpportunitySchema>;

export type Activity = z.infer<typeof activityBaseSchema>;
export type CreateActivity = z.infer<typeof createActivitySchema>;
export type UpdateActivity = z.infer<typeof updateActivitySchema>;

export type Tenant = z.infer<typeof tenantBaseSchema>;
export type CreateTenant = z.infer<typeof createTenantSchema>;
export type UpdateTenant = z.infer<typeof updateTenantSchema>;

export type SubscriptionPlan = z.infer<typeof subscriptionPlanBaseSchema>;
export type CreateSubscriptionPlan = z.infer<typeof createSubscriptionPlanSchema>;
export type UpdateSubscriptionPlan = z.infer<typeof updateSubscriptionPlanSchema>;
