import { z } from 'zod';

/**
 * Feature flag rule type schema
 */
export const featureFlagRuleTypeSchema = z.enum([
  'user',
  'tenant',
  'date',
  'time',
  'location',
  'custom',
]);

/**
 * Feature flag rule schema
 */
export const featureFlagRuleSchema = z.object({
  type: featureFlagRuleTypeSchema,
  value: z.any(),
});

/**
 * Feature flag schema
 */
export const featureFlagSchema = z.object({
  id: z.string(),
  key: z.string().min(1).max(100).regex(/^[a-z0-9_.-]+$/),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  enabled: z.boolean(),
  enabledForUsers: z.array(z.string()),
  enabledForTenants: z.array(z.string()),
  enabledForPercentage: z.number().min(0).max(100),
  rules: z.array(featureFlagRuleSchema),
  tags: z.array(z.string()),
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Feature flag context schema
 */
export const featureFlagContextSchema = z.object({
  userId: z.string().optional(),
  tenantId: z.string().optional(),
  location: z.object({
    country: z.string().optional(),
    region: z.string().optional(),
  }).optional(),
  custom: z.record(z.any()).optional(),
});

/**
 * Create feature flag request schema
 */
export const createFeatureFlagRequestSchema = z.object({
  key: z.string().min(1).max(100).regex(/^[a-z0-9_.-]+$/),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  enabled: z.boolean().optional(),
  enabledForUsers: z.array(z.string()).optional(),
  enabledForTenants: z.array(z.string()).optional(),
  enabledForPercentage: z.number().min(0).max(100).optional(),
  rules: z.array(featureFlagRuleSchema).optional(),
  tags: z.array(z.string()).optional(),
});

/**
 * Update feature flag request schema
 */
export const updateFeatureFlagRequestSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  enabled: z.boolean().optional(),
  enabledForUsers: z.array(z.string()).optional(),
  enabledForTenants: z.array(z.string()).optional(),
  enabledForPercentage: z.number().min(0).max(100).optional(),
  rules: z.array(featureFlagRuleSchema).optional(),
  tags: z.array(z.string()).optional(),
});

/**
 * Check feature flags request schema
 */
export const checkFeatureFlagsRequestSchema = z.object({
  keys: z.array(z.string()),
});
