/**
 * Zod schemas for Proposal Generator
 */

import { z } from 'zod';

/**
 * Proposal section type schema
 */
export const proposalSectionTypeSchema = z.enum([
  'text',
  'pricing',
  'timeline',
  'team',
  'testimonials',
  'terms',
  'custom'
]);

/**
 * Proposal section schema
 */
export const proposalSectionSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: proposalSectionTypeSchema,
  content: z.string(),
  order: z.number().int(),
});

/**
 * Proposal template schema
 */
export const proposalTemplateSchema = z.object({
  id: z.union([z.string(), z.number()]),
  name: z.string(),
  description: z.string().optional(),
  category: z.string(),
  content: z.string(),
  sections: z.array(proposalSectionSchema),
  variables: z.array(z.string()),
  isDefault: z.boolean(),
  usageCount: z.number().int().nonnegative(),
  createdBy: z.union([z.string(), z.number()]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Proposal status schema
 */
export const proposalStatusSchema = z.enum([
  'draft',
  'sent',
  'viewed',
  'accepted',
  'rejected'
]);

/**
 * Proposal schema
 */
export const proposalSchema = z.object({
  id: z.union([z.string(), z.number()]),
  name: z.string(),
  description: z.string().optional(),
  opportunityId: z.union([z.string(), z.number()]),
  contactId: z.union([z.string(), z.number()]).optional(),
  companyId: z.union([z.string(), z.number()]).optional(),
  templateId: z.union([z.string(), z.number()]),
  content: z.string(),
  sections: z.array(proposalSectionSchema),
  value: z.number(),
  currency: z.string(),
  validUntil: z.date().optional(),
  status: proposalStatusSchema,
  tags: z.array(z.string()),
  notes: z.string().optional(),
  customFields: z.record(z.any()).optional(),
  createdBy: z.union([z.string(), z.number()]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Proposal generation request schema
 */
export const proposalGenerationRequestSchema = z.object({
  opportunityId: z.string(),
  templateId: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
});

/**
 * Proposal generation response schema
 */
export const proposalGenerationResponseSchema = z.object({
  proposal: proposalSchema,
});

/**
 * Proposal export format schema
 */
export const proposalExportFormatSchema = z.enum([
  'html',
  'pdf',
  'docx',
  'markdown',
  'claude-html'
]);

/**
 * Proposal export request schema
 */
export const proposalExportRequestSchema = z.object({
  proposalId: z.string(),
  format: proposalExportFormatSchema,
});

/**
 * Proposal export response schema
 */
export const proposalExportResponseSchema = z.object({
  url: z.string().url(),
  expiresAt: z.date(),
});

/**
 * Proposal share request schema
 */
export const proposalShareRequestSchema = z.object({
  proposalId: z.string(),
  expiresAt: z.date().optional(),
  password: z.string().optional(),
});

/**
 * Proposal share response schema
 */
export const proposalShareResponseSchema = z.object({
  shareId: z.string(),
  shareUrl: z.string().url(),
  expiresAt: z.date().optional(),
  isPasswordProtected: z.boolean(),
});

/**
 * Proposal email request schema
 */
export const proposalEmailRequestSchema = z.object({
  proposalId: z.string(),
  to: z.array(z.string().email()),
  cc: z.array(z.string().email()).optional(),
  bcc: z.array(z.string().email()).optional(),
  subject: z.string().optional(),
  message: z.string().optional(),
  format: proposalExportFormatSchema.optional(),
});

/**
 * Proposal email response schema
 */
export const proposalEmailResponseSchema = z.object({
  messageId: z.string(),
  sentAt: z.date(),
  recipients: z.array(z.string().email()),
});

/**
 * Proposal social export request schema
 */
export const proposalSocialExportRequestSchema = z.object({
  proposalId: z.string(),
  platform: z.enum(['linkedin', 'twitter', 'facebook']),
  message: z.string().optional(),
});

/**
 * Proposal social export response schema
 */
export const proposalSocialExportResponseSchema = z.object({
  postId: z.string().optional(),
  postUrl: z.string().url().optional(),
  status: z.enum(['success', 'pending', 'failed']),
});

/**
 * Proposal clauses update request schema
 */
export const proposalClausesUpdateRequestSchema = z.object({
  proposalId: z.string(),
  clauses: z.array(z.string()),
});

/**
 * Proposal clauses update response schema
 */
export const proposalClausesUpdateResponseSchema = z.object({
  proposal: proposalSchema,
});

/**
 * Industry-specific clause schema
 */
export const industrySpecificClauseSchema = z.object({
  id: z.string(),
  name: z.string(),
  text: z.string(),
  required: z.boolean(),
});

/**
 * Proposal AI generation options schema
 */
export const proposalAIGenerationOptionsSchema = z.object({
  opportunityId: z.string().optional(),
  companyId: z.string().optional(),
  contactIds: z.array(z.string()).optional(),
  prompt: z.string(),
  model: z.string().default('claude-3-opus-20240229'),
  includeSections: z.object({
    executiveSummary: z.boolean().default(true),
    solution: z.boolean().default(true),
    timeline: z.boolean().default(true),
    pricing: z.boolean().default(true),
    team: z.boolean().default(false),
    testimonials: z.boolean().default(false),
    terms: z.boolean().default(true),
  }).optional(),
  customInstructions: z.string().optional(),
});

/**
 * Proposal AI generation response schema
 */
export const proposalAIGenerationResponseSchema = z.object({
  title: z.string(),
  description: z.string(),
  sections: z.array(z.object({
    title: z.string(),
    content: z.string(),
    type: proposalSectionTypeSchema,
    order: z.number().int(),
  })),
  pricing: z.object({
    items: z.array(z.object({
      name: z.string(),
      description: z.string().optional(),
      quantity: z.number(),
      unitPrice: z.number(),
      total: z.number(),
    })),
  }).optional(),
  terms: z.string(),
  aiPrompt: z.string(),
  aiModel: z.string(),
});

/**
 * Proposal analytics event schema
 */
export const proposalAnalyticsEventSchema = z.object({
  id: z.string(),
  type: z.enum(['view', 'download', 'share', 'accept', 'reject', 'comment']),
  timestamp: z.date(),
  data: z.record(z.any()).optional(),
  ip: z.string().optional(),
  userAgent: z.string().optional(),
  location: z.object({
    country: z.string().optional(),
    region: z.string().optional(),
    city: z.string().optional(),
  }).optional(),
});

/**
 * Proposal analytics schema
 */
export const proposalAnalyticsSchema = z.object({
  proposalId: z.string(),
  views: z.number().int().nonnegative(),
  uniqueViews: z.number().int().nonnegative(),
  downloads: z.number().int().nonnegative(),
  shares: z.number().int().nonnegative(),
  averageViewDuration: z.number().nonnegative(),
  events: z.array(proposalAnalyticsEventSchema),
  lastViewedAt: z.date().optional(),
});

/**
 * Proposal download options schema
 */
export const proposalDownloadOptionsSchema = z.object({
  format: proposalExportFormatSchema,
  includeCompanyLogo: z.boolean().default(true),
  includeSignaturePage: z.boolean().default(true),
  includeAttachments: z.boolean().default(true),
  customHeader: z.string().optional(),
  customFooter: z.string().optional(),
  watermark: z.string().optional(),
});
