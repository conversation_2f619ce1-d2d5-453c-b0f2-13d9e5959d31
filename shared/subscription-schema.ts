import { pgTable, text, serial, integer, timestamp, boolean, pgEnum, jsonb, real, decimal } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Subscription plan status enum
export const subscriptionPlanStatusEnum = pgEnum('subscription_plan_status', ['active', 'inactive', 'archived']);

// Subscription status enum
export const subscriptionStatusEnum = pgEnum('subscription_status', ['active', 'trialing', 'past_due', 'canceled', 'expired']);

// Billing period enum
export const billingPeriodEnum = pgEnum('billing_period', ['monthly', 'yearly', 'custom']);

// Subscription plans table
export const subscriptionPlans = pgTable("subscription_plans", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  status: subscriptionPlanStatusEnum("status").default('active'),
  isDefault: boolean("is_default").default(false),
  sortOrder: integer("sort_order").default(0),
  
  // Pricing information
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  currency: text("currency").default('USD'),
  billingPeriod: billingPeriodEnum("billing_period").default('monthly'),
  trialDays: integer("trial_days").default(0),
  
  // Resource limits
  limits: jsonb("limits").notNull(),
  
  // Feature flags
  features: jsonb("features").notNull(),
  
  // Module-specific settings
  moduleSettings: jsonb("module_settings").default({}),
  
  // Metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Tenant subscriptions table
export const tenantSubscriptions = pgTable("tenant_subscriptions", {
  id: serial("id").primaryKey(),
  tenantId: text("tenant_id").notNull(),
  planId: integer("plan_id").references(() => subscriptionPlans.id),
  status: subscriptionStatusEnum("status").default('active'),
  startDate: timestamp("start_date").notNull(),
  endDate: timestamp("end_date").notNull(),
  trialEndsAt: timestamp("trial_ends_at"),
  
  // Custom overrides for this specific tenant
  customLimits: jsonb("custom_limits"),
  customFeatures: jsonb("custom_features"),
  
  // Billing information
  billingDetails: jsonb("billing_details"),
  
  // Metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  canceledAt: timestamp("canceled_at"),
});

// Features table
export const features = pgTable("features", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  name: text("name").notNull(),
  description: text("description"),
  category: text("category").notNull(),
  module: text("module").notNull(),
  defaultValue: boolean("default_value").default(false),
  requiresRestart: boolean("requires_restart").default(false),
  uiComponent: text("ui_component"),
  dependencies: jsonb("dependencies"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Tenant usage table
export const tenantUsage = pgTable("tenant_usage", {
  id: serial("id").primaryKey(),
  tenantId: text("tenant_id").notNull(),
  period: text("period").notNull(), // Format: YYYY-MM
  usage: jsonb("usage").notNull(),
  featureUsage: jsonb("feature_usage").notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Subscription events table (for audit and synchronization)
export const subscriptionEvents = pgTable("subscription_events", {
  id: serial("id").primaryKey(),
  type: text("type").notNull(),
  payload: jsonb("payload").notNull(),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
});

// Tenants table
export const tenants = pgTable("tenants", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  ownerId: integer("owner_id").references(() => users.id),
  settings: jsonb("settings").default({}),
  status: text("status").default('active'),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User-tenant relationship table
export const userTenants = pgTable("user_tenants", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  tenantId: integer("tenant_id").references(() => tenants.id),
  role: text("role").notNull(), // 'owner', 'admin', 'member', 'guest'
  createdAt: timestamp("created_at").defaultNow().notNull(),
  invitedBy: integer("invited_by").references(() => users.id),
  status: text("status").notNull(), // 'active', 'invited', 'suspended'
});

// Define insert schemas
export const insertSubscriptionPlanSchema = createInsertSchema(subscriptionPlans).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertTenantSubscriptionSchema = createInsertSchema(tenantSubscriptions).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  canceledAt: true,
});

export const insertFeatureSchema = createInsertSchema(features).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertTenantUsageSchema = createInsertSchema(tenantUsage).omit({
  id: true,
  updatedAt: true,
});

export const insertTenantSchema = createInsertSchema(tenants).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertUserTenantSchema = createInsertSchema(userTenants).omit({
  id: true,
  createdAt: true,
});

// Define types
export type SubscriptionPlan = typeof subscriptionPlans.$inferSelect;
export type InsertSubscriptionPlan = z.infer<typeof insertSubscriptionPlanSchema>;

export type TenantSubscription = typeof tenantSubscriptions.$inferSelect;
export type InsertTenantSubscription = z.infer<typeof insertTenantSubscriptionSchema>;

export type Feature = typeof features.$inferSelect;
export type InsertFeature = z.infer<typeof insertFeatureSchema>;

export type TenantUsage = typeof tenantUsage.$inferSelect;
export type InsertTenantUsage = z.infer<typeof insertTenantUsageSchema>;

export type Tenant = typeof tenants.$inferSelect;
export type InsertTenant = z.infer<typeof insertTenantSchema>;

export type UserTenant = typeof userTenants.$inferSelect;
export type InsertUserTenant = z.infer<typeof insertUserTenantSchema>;

// Define subscription plan limits type
export interface SubscriptionLimits {
  users: number;
  contacts: number;
  companies: number;
  opportunities: number;
  storage: number; // in MB
  apiRequests: number; // per day
  [key: string]: number; // Allow for additional limits
}

// Define feature flags type
export interface FeatureFlags {
  [key: string]: boolean;
}

// Define module settings type
export interface ModuleSettings {
  [module: string]: {
    [setting: string]: any;
  };
}

// Define billing details type
export interface BillingDetails {
  customerId?: string;
  subscriptionId?: string;
  paymentMethodId?: string;
  [key: string]: any;
}

// Define tenant settings type
export interface TenantSettings {
  timezone?: string;
  locale?: string;
  branding?: {
    logo?: string;
    primaryColor?: string;
    accentColor?: string;
  };
  [key: string]: any;
}

// Define subscription event types
export enum SubscriptionEventType {
  PLAN_CREATED = 'subscription.plan.created',
  PLAN_UPDATED = 'subscription.plan.updated',
  PLAN_DELETED = 'subscription.plan.deleted',
  TENANT_SUBSCRIPTION_CREATED = 'subscription.tenant.created',
  TENANT_SUBSCRIPTION_UPDATED = 'subscription.tenant.updated',
  TENANT_SUBSCRIPTION_CANCELED = 'subscription.tenant.canceled',
  FEATURE_REGISTERED = 'subscription.feature.registered',
  FEATURE_UPDATED = 'subscription.feature.updated',
  FEATURE_REMOVED = 'subscription.feature.removed',
}
