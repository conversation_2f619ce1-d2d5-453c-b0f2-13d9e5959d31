/**
 * Validation utilities for the Aizako CRM project
 * These utilities help with handling Zod validation errors
 */

import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

/**
 * Converts a Zod error to a user-friendly error message
 * @param error The Zod error to convert
 * @returns A user-friendly error message
 */
export function formatZodError(error: z.ZodError): string {
  const validationError = fromZodError(error);
  return validationError.message;
}

/**
 * Safely validates data against a Zod schema
 * @param schema The Zod schema to validate against
 * @param data The data to validate
 * @returns An object containing the validation result
 */
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  error?: string;
} {
  try {
    const validatedData = schema.parse(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: formatZodError(error),
      };
    }
    return {
      success: false,
      error: 'An unknown validation error occurred',
    };
  }
}

/**
 * Validates data against a Zod schema and throws a formatted error if validation fails
 * @param schema The Zod schema to validate against
 * @param data The data to validate
 * @returns The validated data
 * @throws Error with formatted message if validation fails
 */
export function validateOrThrow<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(formatZodError(error));
    }
    throw error;
  }
}

/**
 * Validates request body against a schema
 * Useful for Express middleware
 */
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.validatedBody = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          message: formatZodError(error),
          errors: error.errors,
        });
      }
      next(error);
    }
  };
}

/**
 * Validates query parameters against a schema
 * Useful for Express middleware
 */
export function validateQuery<T>(schema: z.ZodSchema<T>) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.query);
      req.validatedQuery = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          message: formatZodError(error),
          errors: error.errors,
        });
      }
      next(error);
    }
  };
}
