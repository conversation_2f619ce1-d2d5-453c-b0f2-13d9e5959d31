/**
 * Core entity types for the Aizako CRM project
 * These types represent the fundamental data models used throughout the application
 */

import { z } from 'zod';
import { Id, Timestamps, WithId, WithTimestamps } from './utils';
import { ProposalSectionType } from './proposal-generator';

/**
 * User entity
 */
export interface User {
  id: Id;
  username: string;
  email: string;
  fullName?: string;
  preferences?: Record<string, any>;
  createdAt: Date;
}

/**
 * Contact entity
 */
export interface Contact {
  id: Id;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  title?: string;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  source?: string;
  companyId?: Id;
  aiEnrichment?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}

/**
 * Company entity
 */
export interface Company {
  id: Id;
  name: string;
  industry?: string;
  website?: string;
  employees?: number;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  aiEnrichment?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}

/**
 * Opportunity entity
 */
export interface Opportunity {
  id: Id;
  name: string;
  value: number;
  currency?: string;
  stage: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: Date;
  probability?: number;
  notes?: string;
  contactId?: Id;
  companyId?: Id;
  aiInsights?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
}

/**
 * Activity entity
 */
export interface Activity {
  id: Id;
  type: string;
  title: string;
  description?: string;
  dueDate?: Date;
  completed: boolean;
  completedAt?: Date;
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Id;
  assignedTo?: Id;
}

/**
 * Relationship entity
 */
export interface Relationship {
  id: Id;
  sourceType: 'contact' | 'company';
  sourceId: Id;
  targetType: 'contact' | 'company';
  targetId: Id;
  relationshipType?: string;
  strength?: number;
  notes?: string;
  createdAt: Date;
  createdBy?: Id;
}

/**
 * Tenant entity
 */
export interface Tenant {
  id: Id;
  name: string;
  slug: string;
  ownerId: Id;
  settings?: {
    timezone?: string;
    locale?: string;
    branding?: {
      logo?: string;
      primaryColor?: string;
      accentColor?: string;
    };
    [key: string]: any;
  };
  status: 'active' | 'suspended' | 'deleted';
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Subscription Plan entity
 */
export interface SubscriptionPlan {
  id: Id;
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'archived';
  isDefault: boolean;
  sortOrder: number;
  price: number;
  currency: string;
  billingPeriod: 'monthly' | 'yearly' | 'custom';
  trialDays: number;
  limits: Record<string, number>;
  features: Record<string, boolean>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Proposal entity
 */
export interface Proposal {
  id: Id;
  name: string;
  description?: string;
  opportunityId: Id;
  contactId?: Id;
  companyId?: Id;
  templateId: Id;
  content: string;
  sections: Array<{
    id: string;
    name: string;
    type: ProposalSectionType;
    content: string;
    order: number;
  }>;
  value: number;
  currency: string;
  validUntil?: Date;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected';
  tags: string[];
  notes?: string;
  customFields?: Record<string, any>;
  createdBy: Id;
  createdAt: Date;
  updatedAt: Date;
}
