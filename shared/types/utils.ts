/**
 * Utility types for the Aizako CRM project
 * These types are designed to be reused across the application
 */

/**
 * Represents an ID which can be either a string or number
 */
export type Id = string | number;

/**
 * Adds an ID field to any type
 */
export type WithId<T> = T & { id: Id };

/**
 * Standard timestamp fields for database records
 */
export type Timestamps = {
  createdAt: Date;
  updatedAt: Date;
};

/**
 * Adds timestamp fields to any type
 */
export type WithTimestamps<T> = T & Timestamps;

/**
 * Standard API response wrapper
 */
export type ApiResponse<T> = {
  data: T;
  success: boolean;
  message?: string;
};

/**
 * Paginated API response
 */
export type PaginatedResponse<T> = ApiResponse<{
  items: T[];
  total: number;
  page: number;
  pageSize: number;
}>;

/**
 * Makes all properties of T optional and adds an id field
 * Useful for update operations
 */
export type UpdateType<T> = Partial<T> & { id: Id };

/**
 * Tenant-specific record
 */
export type TenantRecord = {
  tenantId: string;
};

/**
 * Adds tenant ID to any type
 */
export type WithTenant<T> = T & TenantRecord;

/**
 * User reference for tracking who created/updated records
 */
export type UserReference = {
  createdBy?: Id;
  updatedBy?: Id;
};

/**
 * Adds user reference fields to any type
 */
export type WithUserReference<T> = T & UserReference;

/**
 * Complete record with ID, timestamps, tenant, and user references
 */
export type CompleteRecord<T> = WithId<WithTimestamps<WithTenant<WithUserReference<T>>>>;
