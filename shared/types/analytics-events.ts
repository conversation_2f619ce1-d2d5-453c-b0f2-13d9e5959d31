/**
 * Analytics Events types for the Aizako CRM project
 * These types represent events that can be tracked across the application
 */

import { Id } from './utils';

/**
 * Event types for the Analytics Event Bus
 */
export enum EventType {
  // Wizard events
  WIZARD_USED = 'wizard.used',
  WIZARD_SUCCESS = 'wizard.success',
  WIZARD_FAILURE = 'wizard.failure',
  
  // Meeting Prep events
  MEETING_PREP_GENERATED = 'meeting_prep.generated',
  MEETING_PREP_VIEWED = 'meeting_prep.viewed',
  MEETING_PREP_ADDED_TO_AGENDA = 'meeting_prep.added_to_agenda',
  MEETING_PREP_FEEDBACK = 'meeting_prep.feedback',
  
  // Objection Handler events
  OBJECTION_CLASSIFIED = 'objection.classified',
  OBJECTION_RESPONSE_GENERATED = 'objection.response_generated',
  OBJECTION_RESPONSE_USED = 'objection.response_used',
  OBJECTION_RESPONSE_FEEDBACK = 'objection.response_feedback',
  
  // Proposal Generator events
  PROPOSAL_GENERATED = 'proposal.generated',
  PROPOSAL_VIEWED = 'proposal.viewed',
  PROPOSAL_SENT = 'proposal.sent',
  PROPOSAL_ACCEPTED = 'proposal.accepted',
  PROPOSAL_REJECTED = 'proposal.rejected',
  PROPOSAL_CLAUSES_UPDATED = 'proposal.clauses_updated',
  
  // BI events
  BI_QUERY_EXECUTED = 'bi.query_executed',
  BI_CHART_GENERATED = 'bi.chart_generated',
  BI_INSIGHT_GENERATED = 'bi.insight_generated',
  
  // Attribution events
  ATTRIBUTION_MODEL_UPDATED = 'attribution.model_updated',
  ATTRIBUTION_TOUCHPOINT_RECORDED = 'attribution.touchpoint_recorded',
  ATTRIBUTION_REPORT_GENERATED = 'attribution.report_generated'
}

/**
 * Base Analytics Event interface
 */
export interface AnalyticsEvent {
  id?: Id;
  type: EventType;
  tenantId: string;
  userId: string;
  timestamp?: Date;
  entityId?: string;
  entityType?: string;
  metadata?: Record<string, any>;
}

/**
 * Wizard Event interface
 */
export interface WizardEvent extends AnalyticsEvent {
  type: EventType.WIZARD_USED | EventType.WIZARD_SUCCESS | EventType.WIZARD_FAILURE;
  metadata: {
    wizardType: 'meeting_prep' | 'objection_handler' | 'proposal_generator' | 'follow_up_coach' | 'win_loss_analyzer';
    result?: 'success' | 'failure';
    duration?: number; // in milliseconds
    [key: string]: any;
  };
}

/**
 * Meeting Prep Event interface
 */
export interface MeetingPrepEvent extends AnalyticsEvent {
  type: EventType.MEETING_PREP_GENERATED | EventType.MEETING_PREP_VIEWED | 
        EventType.MEETING_PREP_ADDED_TO_AGENDA | EventType.MEETING_PREP_FEEDBACK;
  entityType: 'contact' | 'company' | 'opportunity';
  metadata: {
    meetingId?: string;
    contactId?: string;
    companyId?: string;
    opportunityId?: string;
    feedbackRating?: number; // 1-5 scale
    feedbackComments?: string;
    [key: string]: any;
  };
}

/**
 * Objection Handler Event interface
 */
export interface ObjectionHandlerEvent extends AnalyticsEvent {
  type: EventType.OBJECTION_CLASSIFIED | EventType.OBJECTION_RESPONSE_GENERATED | 
        EventType.OBJECTION_RESPONSE_USED | EventType.OBJECTION_RESPONSE_FEEDBACK;
  metadata: {
    objectionId?: string;
    objectionText?: string;
    objectionClass?: string;
    responseId?: string;
    contactId?: string;
    companyId?: string;
    opportunityId?: string;
    feedbackRating?: number; // 1-5 scale
    wasSuccessful?: boolean;
    [key: string]: any;
  };
}

/**
 * Proposal Generator Event interface
 */
export interface ProposalGeneratorEvent extends AnalyticsEvent {
  type: EventType.PROPOSAL_GENERATED | EventType.PROPOSAL_VIEWED | 
        EventType.PROPOSAL_SENT | EventType.PROPOSAL_ACCEPTED | 
        EventType.PROPOSAL_REJECTED | EventType.PROPOSAL_CLAUSES_UPDATED;
  entityId: string; // proposalId
  entityType: 'proposal';
  metadata: {
    opportunityId: string;
    templateId?: string;
    contactId?: string;
    companyId?: string;
    format?: 'html' | 'pdf' | 'docx' | 'markdown' | 'claude-html';
    clauses?: string[];
    rejectionReason?: string;
    [key: string]: any;
  };
}

/**
 * BI Event interface
 */
export interface BIEvent extends AnalyticsEvent {
  type: EventType.BI_QUERY_EXECUTED | EventType.BI_CHART_GENERATED | EventType.BI_INSIGHT_GENERATED;
  metadata: {
    query?: string;
    chartType?: string;
    insightType?: string;
    duration?: number; // in milliseconds
    datasetId?: string;
    [key: string]: any;
  };
}

/**
 * Attribution Event interface
 */
export interface AttributionEvent extends AnalyticsEvent {
  type: EventType.ATTRIBUTION_MODEL_UPDATED | EventType.ATTRIBUTION_TOUCHPOINT_RECORDED | 
        EventType.ATTRIBUTION_REPORT_GENERATED;
  metadata: {
    modelType?: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based' | 'custom';
    touchpointType?: string;
    channelId?: string;
    campaignId?: string;
    contactId?: string;
    opportunityId?: string;
    [key: string]: any;
  };
}
