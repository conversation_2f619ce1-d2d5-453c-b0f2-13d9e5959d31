/**
 * Form-specific types for the Aizako CRM project
 * These types represent form state and validation for UI components
 */

import { Id } from './utils';

/**
 * Authentication Forms
 */
export interface LoginFormValues {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterFormValues {
  displayName: string;
  email: string;
  password: string;
  confirmPassword: string;
  terms: boolean;
}

export interface PasswordResetFormValues {
  email: string;
}

export interface PasswordChangeFormValues {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Contact Forms
 */
export interface ContactFormValues {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  title?: string;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  source?: string;
  companyId?: Id;
}

/**
 * Company Forms
 */
export interface CompanyFormValues {
  name: string;
  industry?: string;
  website?: string;
  employees?: number;
  status: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
}

/**
 * Opportunity Forms
 */
export interface OpportunityFormValues {
  name: string;
  value: number;
  currency?: string;
  stage: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: Date | string;
  probability?: number;
  notes?: string;
  contactId?: Id;
  companyId?: Id;
}

/**
 * Activity Forms
 */
export interface ActivityFormValues {
  type: string;
  title: string;
  description?: string;
  dueDate?: Date | string;
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  assignedTo?: Id;
}

/**
 * Interaction Forms
 */
export interface InteractionFormValues {
  type: 'email' | 'call' | 'meeting' | 'chat' | 'social' | 'note' | 'task' | 'other';
  timestamp: Date;
  summary: string;
  content?: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  direction?: 'inbound' | 'outbound';
  nextAction?: {
    type?: 'email' | 'call' | 'meeting' | 'task' | 'other';
    description?: string;
    dueDate?: Date | string;
    priority?: 'low' | 'medium' | 'high';
  };
}

/**
 * Settings Forms
 */
export interface ProfileFormValues {
  fullName: string;
  email: string;
  title?: string;
  phone?: string;
  bio?: string;
  avatarUrl?: string;
}

export interface ApiKeyFormValues {
  openaiApiKey?: string;
  voyageApiKey?: string;
}

/**
 * Tenant Forms
 */
export interface TenantFormValues {
  name: string;
  slug: string;
  settings?: {
    timezone?: string;
    locale?: string;
    branding?: {
      logo?: string;
      primaryColor?: string;
      accentColor?: string;
    };
  };
}

/**
 * Email Configuration Forms
 */
export interface EmailConfigFormValues {
  provider: 'resend' | 'smtp' | 'gmail' | 'outlook';
  apiKey?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  fromEmail: string;
  fromName: string;
  replyTo?: string;
  trackOpens: boolean;
  trackClicks: boolean;
  customDomain?: string;
}

/**
 * Proposal Generator Forms
 */
export interface ProposalFormValues {
  name: string;
  description?: string;
  opportunityId: Id;
  contactId?: Id;
  companyId?: Id;
  templateId: Id;
  value: number;
  currency: string;
  validUntil?: Date | string;
  tags?: string[];
  notes?: string;
}

export interface ProposalAIGeneratorFormValues {
  title: string;
  description?: string;
  opportunityId?: Id;
  companyId?: Id;
  contactIds?: Id[];
  prompt: string;
  model: string;
  includeSections: {
    executiveSummary: boolean;
    solution: boolean;
    timeline: boolean;
    pricing: boolean;
    team: boolean;
    testimonials: boolean;
    terms: boolean;
  };
  customInstructions?: string;
}

export interface ProposalSectionFormValues {
  name: string;
  type: 'text' | 'pricing' | 'timeline' | 'team' | 'testimonials' | 'terms' | 'custom';
  content: string;
  order: number;
}

export interface ProposalShareFormValues {
  expiresAt?: Date | string;
  password?: string;
  sendEmail?: boolean;
  recipients?: string[];
  message?: string;
}

/**
 * Objection Handler Forms
 */
export interface ObjectionHandlerFormValues {
  objectionText: string;
  objectionClass?: string;
  opportunityId?: Id;
  contactId?: Id;
  companyId?: Id;
  includeEvidence?: boolean;
  maxResponses?: number;
}

export interface ObjectionResponseFormValues {
  responseText: string;
  wasSuccessful?: boolean;
  notes?: string;
}

/**
 * Email Template Forms
 */
export interface EmailTemplateFormValues {
  name: string;
  subject: string;
  body: string;
  category?: string;
  tags?: string[];
  isDefault?: boolean;
}

/**
 * Sequence Forms
 */
export interface SequenceFormValues {
  name: string;
  description?: string;
  steps: SequenceStepFormValues[];
  isActive: boolean;
  tags?: string[];
}

export interface SequenceStepFormValues {
  type: 'email' | 'task' | 'call' | 'wait';
  name: string;
  description?: string;
  delay: number;
  delayUnit: 'minutes' | 'hours' | 'days';
  emailTemplateId?: Id;
  taskDescription?: string;
  waitCondition?: string;
}
