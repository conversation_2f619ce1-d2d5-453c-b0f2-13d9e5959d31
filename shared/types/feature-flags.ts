/**
 * Feature flag types
 */

export interface FeatureFlag {
  id: string;
  key: string;
  name: string;
  description?: string;
  enabled: boolean;
  enabledForUsers: string[];
  enabledForTenants: string[];
  enabledForPercentage: number;
  rules: FeatureFlagRule[];
  tags: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type FeatureFlagRule = {
  type: FeatureFlagRuleType;
  value: any;
};

export type FeatureFlagRuleType = 'user' | 'tenant' | 'date' | 'time' | 'location' | 'custom';

export interface FeatureFlagContext {
  userId?: string;
  tenantId?: string;
  location?: {
    country?: string;
    region?: string;
  };
  custom?: Record<string, any>;
}

/**
 * API request/response types
 */

export interface CreateFeatureFlagRequest {
  key: string;
  name: string;
  description?: string;
  enabled?: boolean;
  enabledForUsers?: string[];
  enabledForTenants?: string[];
  enabledForPercentage?: number;
  rules?: FeatureFlagRule[];
  tags?: string[];
}

export interface UpdateFeatureFlagRequest {
  id: string;
  name?: string;
  description?: string;
  enabled?: boolean;
  enabledForUsers?: string[];
  enabledForTenants?: string[];
  enabledForPercentage?: number;
  rules?: FeatureFlagRule[];
  tags?: string[];
}

export interface GetFeatureFlagsResponse {
  success: boolean;
  featureFlags: FeatureFlag[];
}

export interface GetFeatureFlagResponse {
  success: boolean;
  featureFlag: FeatureFlag;
}

export interface CreateFeatureFlagResponse {
  success: boolean;
  featureFlag: FeatureFlag;
}

export interface UpdateFeatureFlagResponse {
  success: boolean;
  featureFlag: FeatureFlag;
}

export interface DeleteFeatureFlagResponse {
  success: boolean;
  message: string;
}

export interface CheckFeatureFlagResponse {
  success: boolean;
  enabled: boolean;
}

export interface CheckFeatureFlagsRequest {
  keys: string[];
}

export interface CheckFeatureFlagsResponse {
  success: boolean;
  flags: Record<string, boolean>;
}
