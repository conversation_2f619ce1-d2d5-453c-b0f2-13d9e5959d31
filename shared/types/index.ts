/**
 * Central export file for all types in the Aizako CRM project
 * This file re-exports all types from the various type modules
 */

// Core entity types
export * from './core';

// API request/response types
export * from './api';

// Form-specific types
export * from './forms';

// Utility types
export * from './utils';

// Feature flags types
export * from './feature-flags';

// Experiments types
export {
  ExperimentType,
  ExperimentVariant,
  ExperimentUser,
  ExperimentResult,
  ExperimentConfig,
  FunnelStep,
  FunnelAnalysisRequest as ExperimentFunnelAnalysisRequest
} from './experiments';

// Analytics types
export {
  AnalyticsEvent as CoreAnalyticsEvent,
  AnalyticsEventType,
  AnalyticsFilter,
  AnalyticsTimeframe,
  AnalyticsMetric,
  AnalyticsDimension,
  AnalyticsQuery,
  AnalyticsResult,
  AnalyticsChartType,
  AnalyticsDataPoint,
  AnalyticsInsight,
  FunnelAnalysisRequest as AnalyticsFunnelAnalysisRequest
} from './analytics';

// Analytics events types
export {
  AnalyticsEvent as TrackingAnalyticsEvent,
  AnalyticsEventType as TrackingEventType,
  AnalyticsEventData,
  AnalyticsEventContext,
  AnalyticsEventUser,
  AnalyticsEventPage
} from './analytics-events';

// GraphRAG types
export * from './graph-rag';

// Objection Handler types
export {
  Objection,
  ObjectionResponse,
  ObjectionCategory,
  ObjectionHandlerRequest,
  ObjectionHandlerResponse,
  VoiceObjectionRequest,
  VoiceObjectionResponse,
  SupportingEvidence as ObjectionSupportingEvidence
} from './objection-handler';

// Proposal Generator types
export {
  ProposalTemplate,
  ProposalSection,
  ProposalSectionType,
  Proposal,
  ProposalGenerationRequest,
  ProposalGenerationResponse,
  ProposalExportFormat,
  ProposalExportRequest,
  ProposalExportResponse,
  ProposalShareRequest,
  ProposalShareResponse,
  ProposalEmailRequest,
  ProposalEmailResponse,
  ProposalSocialExportRequest,
  ProposalSocialExportResponse,
  ProposalClausesUpdateRequest,
  ProposalClausesUpdateResponse,
  IndustrySpecificClause,
  LegalClausePickerProps,
  ProposalAIGenerationOptions,
  ProposalAIGenerationResponse,
  ProposalAnalyticsEvent,
  ProposalAnalytics,
  ProposalDownloadOptions,
  ProposalView,
  ProposalShare
} from './proposal-generator';

// Type guards
export * from './guards';
