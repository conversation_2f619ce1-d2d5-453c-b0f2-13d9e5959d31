/**
 * GraphRAG types for the Aizako CRM project
 * These types represent the context and data structures used by the GraphRAG service
 */

import { Id } from './utils';

/**
 * Base GraphRAG context request parameters
 */
export interface GraphRAGContextParams {
  tenantId: string;
  opportunityId?: string;
  contactId?: string;
  companyId?: string;
}

/**
 * Meeting Prep context request parameters
 */
export interface MeetingPrepContextParams extends GraphRAGContextParams {
  meetingId?: string;
}

/**
 * Objection Handler context request parameters
 */
export interface ObjectionHandlerContextParams extends GraphRAGContextParams {
  objectionText: string;
  objectionClass?: string;
}

/**
 * Proposal Generator context request parameters
 */
export interface ProposalGeneratorContextParams extends GraphRAGContextParams {
  opportunityId: string; // Required for proposal generation
}

/**
 * Relationship History item
 */
export interface RelationshipHistoryItem {
  timestamp: string;
  type: string;
  nodeType: string;
  nodeId: string;
  nodeName: string;
  properties: Record<string, any>;
}

/**
 * Stakeholder information
 */
export interface Stakeholder {
  id: string;
  name: string;
  title?: string;
  email?: string;
  phone?: string;
  importance: 'low' | 'medium' | 'high';
}

/**
 * Buying Signal
 */
export interface BuyingSignal {
  id: string;
  content: string;
  type: string;
  date: string;
  source: string;
}

/**
 * Predicted Question
 */
export interface PredictedQuestion {
  question: string;
  category: string;
  importance: 'low' | 'medium' | 'high';
}

/**
 * Suggested Collateral
 */
export interface SuggestedCollateral {
  id: string;
  name: string;
  type: string;
  url: string;
}

/**
 * Company News item
 */
export interface CompanyNewsItem {
  date: Date;
  title: string;
  source: string;
  summary: string;
  url: string;
}

/**
 * Meeting Prep context
 */
export interface MeetingPrepContext {
  relationshipHistory: RelationshipHistoryItem[];
  stakeholders: Stakeholder[];
  buyingSignals: BuyingSignal[];
  predictedQuestions: PredictedQuestion[];
  suggestedCollateral: SuggestedCollateral[];
  companyNews?: CompanyNewsItem[];
}

/**
 * Similar Objection
 */
export interface SimilarObjection {
  id: string;
  text: string;
  class: string;
  successRate: number;
}

/**
 * Effective Rebuttal
 */
export interface EffectiveRebuttal {
  id: string;
  text: string;
  winRate: number;
  evidence: string[];
}

/**
 * Supporting Evidence
 */
export interface SupportingEvidence {
  id: string;
  title: string;
  type: string;
  description: string;
  url?: string;
}

/**
 * Objection Handler context
 */
export interface ObjectionHandlerContext {
  objectionClass: string;
  similarObjections: SimilarObjection[];
  effectiveRebuttals: EffectiveRebuttal[];
  supportingEvidence: SupportingEvidence[];
}

/**
 * Pricing Tier
 */
export interface PricingTier {
  id: string;
  name: string;
  price: number;
  billingPeriod: 'monthly' | 'quarterly' | 'annually' | 'one-time';
  features: string[];
}

/**
 * Discount Policy
 */
export interface DiscountPolicy {
  maxDiscount: number;
  approvalThresholds: Array<{
    threshold: number;
    approver: string;
  }>;
  volumeDiscounts: Array<{
    threshold: number;
    discount: number;
  }>;
}

/**
 * Legal Boilerplate
 */
export interface LegalBoilerplate {
  termsAndConditions: string;
  privacyPolicy: string;
  paymentTerms: string;
  cancellationPolicy: string;
}

/**
 * Suggested Upsell
 */
export interface SuggestedUpsell {
  id: string;
  name: string;
  description: string;
  price: number;
  billingPeriod: 'monthly' | 'quarterly' | 'annually' | 'one-time';
  conversionRate: number;
}

/**
 * Industry-specific Clause
 */
export interface IndustrySpecificClause {
  id: string;
  name: string;
  text: string;
  required: boolean;
}

/**
 * Similar Deal
 */
export interface SimilarDeal {
  id: string;
  name: string;
  value: number;
  products: string[];
  closedDate: Date;
}

/**
 * Proposal Generator context
 */
export interface ProposalGeneratorContext {
  pricingTiers: PricingTier[];
  discountPolicy: DiscountPolicy;
  legalBoilerplate: LegalBoilerplate;
  suggestedUpsells: SuggestedUpsell[];
  industrySpecificClauses: IndustrySpecificClause[];
  similarDeals: SimilarDeal[];
}

/**
 * Wizard Usage Record parameters
 */
export interface WizardUsageParams {
  wizardType: 'meeting_prep' | 'objection_handler' | 'proposal_generator' | 'follow_up_coach' | 'win_loss_analyzer';
  userId: string;
  tenantId: string;
  entityId: string;
  entityType: string;
  result: 'success' | 'failure';
  metadata?: Record<string, any>;
}
