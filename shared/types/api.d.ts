/**
 * API type definitions for the Aizako CRM project
 */

import { Contact, Company, Opportunity, Activity } from './core';

// Base request types
export interface BaseRequest {
  tenantId?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface FilterParams {
  [key: string]: any;
}

// Contact API types
export interface CreateContactRequest extends BaseRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  company?: string;
  companyId?: string | number;
  status?: string;
  source?: string;
  notes?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  website?: string;
  linkedin?: string;
  twitter?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface UpdateContactRequest extends BaseRequest {
  id: string | number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  company?: string;
  companyId?: string | number;
  status?: string;
  source?: string;
  notes?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  website?: string;
  linkedin?: string;
  twitter?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

// Company API types
export interface CreateCompanyRequest extends BaseRequest {
  name: string;
  industry?: string;
  website?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  employees?: number;
  revenue?: number;
  status?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface UpdateCompanyRequest extends BaseRequest {
  id: string | number;
  name?: string;
  industry?: string;
  website?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  employees?: number;
  revenue?: number;
  status?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

// Opportunity API types
export interface CreateOpportunityRequest extends BaseRequest {
  name: string;
  contactId?: string | number;
  companyId?: string | number;
  amount?: number;
  currency?: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: Date | string;
  status?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  products?: string[];
}

export interface UpdateOpportunityRequest extends BaseRequest {
  id: string | number;
  name?: string;
  contactId?: string | number;
  companyId?: string | number;
  amount?: number;
  currency?: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: Date | string;
  actualCloseDate?: Date | string;
  status?: string;
  reason?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  products?: string[];
}

// Activity API types
export interface CreateActivityRequest extends BaseRequest {
  type: 'note' | 'task' | 'email' | 'call' | 'meeting' | 'custom';
  title?: string;
  description?: string;
  contactId?: string | number;
  companyId?: string | number;
  opportunityId?: string | number;
  dueDate?: Date | string;
  status?: string;
  priority?: string;
  // Type-specific fields
  content?: string;
  subject?: string;
  body?: string;
  to?: string;
  duration?: number;
  startTime?: string;
  endTime?: string;
  location?: string;
  attendees?: string[];
  customFields?: Record<string, any>;
}

export interface UpdateActivityRequest extends BaseRequest {
  id: string | number;
  type?: 'note' | 'task' | 'email' | 'call' | 'meeting' | 'custom';
  title?: string;
  description?: string;
  contactId?: string | number;
  companyId?: string | number;
  opportunityId?: string | number;
  dueDate?: Date | string;
  completedDate?: Date | string;
  status?: string;
  priority?: string;
  // Type-specific fields
  content?: string;
  subject?: string;
  body?: string;
  to?: string;
  duration?: number;
  startTime?: string;
  endTime?: string;
  location?: string;
  attendees?: string[];
  customFields?: Record<string, any>;
}
