/**
 * Core type definitions for the Aizako CRM project
 */

export interface BaseEntity {
  id: string | number;
  createdAt: Date | string;
  updatedAt?: Date | string;
  tenantId?: string;
}

export interface Contact extends BaseEntity {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  company?: string;
  companyId?: string | number;
  status?: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer' | null;
  source?: string;
  notes?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  website?: string;
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  aiEnrichment?: any;
  persona?: any;
  lastInteraction?: Date | string;
  nextAction?: string;
  nextActionDate?: Date | string;
  assignedTo?: string | number;
  createdBy?: string | number;
}

export interface Company extends BaseEntity {
  name: string;
  industry?: string;
  website?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  employees?: number;
  revenue?: number;
  status?: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer' | null;
  notes?: string;
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  aiEnrichment?: any;
  assignedTo?: string | number;
  createdBy?: string | number;
}

export interface Opportunity extends BaseEntity {
  name: string;
  contactId?: string | number;
  companyId?: string | number;
  amount?: number;
  currency?: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: Date | string;
  actualCloseDate?: Date | string;
  status?: 'open' | 'won' | 'lost' | 'stalled' | null;
  reason?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  products?: string[];
  assignedTo?: string | number;
  createdBy?: string | number;
}

export interface Activity extends BaseEntity {
  type: 'note' | 'task' | 'email' | 'call' | 'meeting' | 'custom';
  title?: string;
  description?: string;
  contactId?: string | number;
  companyId?: string | number;
  opportunityId?: string | number;
  dueDate?: Date | string;
  completedDate?: Date | string;
  status?: 'pending' | 'completed' | 'cancelled' | null;
  priority?: 'low' | 'medium' | 'high' | null;
  assignedTo?: string | number;
  createdBy?: string | number;
  // For specific activity types
  content?: string; // For notes
  subject?: string; // For emails
  body?: string; // For emails
  to?: string; // For emails
  duration?: number; // For calls
  startTime?: string; // For meetings
  endTime?: string; // For meetings
  location?: string; // For meetings
  attendees?: string[]; // For meetings
  customFields?: Record<string, any>;
}

// Specific activity types
export interface Note extends Activity {
  type: 'note';
  content: string;
}

export interface Task extends Activity {
  type: 'task';
  dueDate: Date | string;
  priority: 'low' | 'medium' | 'high';
}

export interface Email extends Activity {
  type: 'email';
  subject: string;
  body: string;
  to: string;
  from?: string;
  cc?: string[];
  bcc?: string[];
  attachments?: string[];
  sentDate?: Date | string;
  openedDate?: Date | string;
  clickedDate?: Date | string;
  repliedDate?: Date | string;
}

export interface Call extends Activity {
  type: 'call';
  duration: number; // in seconds
  outcome?: string;
  recordingUrl?: string;
  transcription?: string;
}

export interface Meeting extends Activity {
  type: 'meeting';
  startTime: string;
  endTime: string;
  location?: string;
  attendees?: string[];
  agenda?: string;
  notes?: string;
  followUp?: string;
}
