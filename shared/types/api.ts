/**
 * API-specific types for the Aizako CRM project
 * These types represent request and response structures for API endpoints
 */

import { Id } from './utils';
import { Contact, Company, Opportunity, Activity, User, Proposal } from './core';
import {
  ObjectionHandlerResponse,
  RealTimeObjectionResponse,
  VoiceObjectionResponse
} from './objection-handler';
import {
  ProposalAIGenerationResponse,
  ProposalDocumentGenerationResponse,
  ProposalShareResponse
} from './proposal-generator';

/**
 * Authentication
 */
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  fullName?: string;
}

export interface AuthResponse {
  user: User;
  token?: string;
}

/**
 * Contacts
 */
export interface CreateContactRequest {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  title?: string;
  status?: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  source?: string;
  companyId?: Id;
}

export interface UpdateContactRequest {
  id: Id;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  status?: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
  source?: string;
  companyId?: Id;
}

export interface ContactResponse {
  contact: Contact;
}

export interface ContactsResponse {
  contacts: Contact[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * Companies
 */
export interface CreateCompanyRequest {
  name: string;
  industry?: string;
  website?: string;
  employees?: number;
  status?: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
}

export interface UpdateCompanyRequest {
  id: Id;
  name?: string;
  industry?: string;
  website?: string;
  employees?: number;
  status?: 'active' | 'inactive' | 'lead' | 'prospect' | 'customer';
  notes?: string;
}

export interface CompanyResponse {
  company: Company;
}

export interface CompaniesResponse {
  companies: Company[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * Opportunities
 */
export interface CreateOpportunityRequest {
  name: string;
  value: number;
  currency?: string;
  stage?: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: string; // ISO date string
  probability?: number;
  notes?: string;
  contactId?: Id;
  companyId?: Id;
}

export interface UpdateOpportunityRequest {
  id: Id;
  name?: string;
  value?: number;
  currency?: string;
  stage?: 'discovery' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  closeDate?: string; // ISO date string
  probability?: number;
  notes?: string;
  contactId?: Id;
  companyId?: Id;
}

export interface OpportunityResponse {
  opportunity: Opportunity;
}

export interface OpportunitiesResponse {
  opportunities: Opportunity[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * Activities
 */
export interface CreateActivityRequest {
  type: string;
  title: string;
  description?: string;
  dueDate?: string; // ISO date string
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  assignedTo?: Id;
}

export interface UpdateActivityRequest {
  id: Id;
  type?: string;
  title?: string;
  description?: string;
  dueDate?: string; // ISO date string
  completed?: boolean;
  completedAt?: string; // ISO date string
  priority?: 'low' | 'medium' | 'high';
  contactId?: Id;
  companyId?: Id;
  opportunityId?: Id;
  assignedTo?: Id;
}

export interface ActivityResponse {
  activity: Activity;
}

export interface ActivitiesResponse {
  activities: Activity[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * Objection Handler
 */
export interface ObjectionHandlerRequest {
  objectionText: string;
  objectionClass?: string;
  opportunityId?: Id;
  contactId?: Id;
  companyId?: Id;
  includeEvidence?: boolean;
  maxResponses?: number;
}

export interface ObjectionHandlerResponseWrapper {
  response: ObjectionHandlerResponse;
}

export interface RealTimeObjectionRequest {
  objectionText: string;
  opportunityId?: Id;
  contactId?: Id;
  companyId?: Id;
}

export interface RealTimeObjectionResponseWrapper {
  response: RealTimeObjectionResponse;
}

export interface VoiceObjectionRequest {
  audioData: Blob;
  opportunityId?: Id;
  contactId?: Id;
  companyId?: Id;
}

export interface VoiceObjectionResponseWrapper {
  response: VoiceObjectionResponse;
}

/**
 * Proposal Generator
 */
export interface GenerateProposalRequest {
  opportunityId: Id;
  templateId?: Id;
  contactId?: Id;
  companyId?: Id;
}

export interface GenerateProposalResponse {
  proposal: Proposal;
}

export interface GenerateProposalWithAIRequest {
  title: string;
  description?: string;
  opportunityId?: Id;
  companyId?: Id;
  contactIds?: Id[];
  prompt: string;
  model: string;
  includeSections?: {
    executiveSummary?: boolean;
    solution?: boolean;
    timeline?: boolean;
    pricing?: boolean;
    team?: boolean;
    testimonials?: boolean;
    terms?: boolean;
  };
  customInstructions?: string;
}

export interface GenerateProposalWithAIResponse {
  proposal: ProposalAIGenerationResponse;
}

export interface GenerateProposalDocumentRequest {
  proposalId: Id;
  format: 'html' | 'pdf' | 'docx' | 'markdown' | 'claude-html';
  options?: {
    includeCompanyLogo?: boolean;
    includeSignaturePage?: boolean;
    includeAttachments?: boolean;
    customHeader?: string;
    customFooter?: string;
    watermark?: string;
  };
}

export interface GenerateProposalDocumentResponse {
  document: ProposalDocumentGenerationResponse;
}

export interface ShareProposalRequest {
  proposalId: Id;
  expiresAt?: string; // ISO date string
  password?: string;
}

export interface ShareProposalResponse {
  share: ProposalShareResponse;
}

export interface ProposalAnalyticsRequest {
  proposalId: Id;
  startDate?: string; // ISO date string
  endDate?: string; // ISO date string
}

export interface ProposalAnalyticsResponse {
  proposalId: Id;
  views: number;
  uniqueViews: number;
  downloads: number;
  shares: number;
  averageViewDuration: number;
  events: Array<{
    id: string;
    type: 'view' | 'download' | 'share' | 'accept' | 'reject' | 'comment';
    timestamp: string; // ISO date string
    data?: Record<string, any>;
  }>;
}
