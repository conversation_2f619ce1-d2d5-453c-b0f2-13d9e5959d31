import {
  Contact,
  Company,
  Opportunity,
  Activity
} from '../core';

import { User } from '../user';

/**
 * Type guard for User
 */
export function isUser(value: any): value is User {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.email === 'string' &&
    typeof value.username === 'string';
}

/**
 * Type guard for Contact
 */
export function isContact(value: any): value is Contact {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.firstName === 'string' &&
    typeof value.lastName === 'string' &&
    typeof value.email === 'string';
}

/**
 * Type guard for Company
 */
export function isCompany(value: any): value is Company {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.name === 'string';
}

/**
 * Type guard for Opportunity
 */
export function isOpportunity(value: any): value is Opportunity {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.name === 'string' &&
    typeof value.stage === 'string' &&
    typeof value.amount === 'number';
}

/**
 * Type guard for Activity
 */
export function isActivity(value: any): value is Activity {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.type === 'string' &&
    typeof value.title === 'string';
}

/**
 * Type guard for Note activity
 */
export function isNote(value: any): value is Activity {
  return isActivity(value) &&
    value.type === 'note' &&
    typeof value.description === 'string';
}

/**
 * Type guard for Task activity
 */
export function isTask(value: any): value is Activity {
  return isActivity(value) &&
    value.type === 'task' &&
    typeof value.dueDate === 'string';
}

/**
 * Type guard for Email activity
 */
export function isEmail(value: any): value is Activity {
  return isActivity(value) &&
    value.type === 'email' &&
    typeof value.description === 'string';
}

/**
 * Type guard for Call activity
 */
export function isCall(value: any): value is Activity {
  return isActivity(value) &&
    value.type === 'call';
}

/**
 * Type guard for Meeting activity
 */
export function isMeeting(value: any): value is Activity {
  return isActivity(value) &&
    value.type === 'meeting';
}

/**
 * Type guard for an array of Users
 */
export function isUserArray(value: any): value is User[] {
  return Array.isArray(value) && value.every(isUser);
}

/**
 * Type guard for an array of Contacts
 */
export function isContactArray(value: any): value is Contact[] {
  return Array.isArray(value) && value.every(isContact);
}

/**
 * Type guard for an array of Companies
 */
export function isCompanyArray(value: any): value is Company[] {
  return Array.isArray(value) && value.every(isCompany);
}

/**
 * Type guard for an array of Opportunities
 */
export function isOpportunityArray(value: any): value is Opportunity[] {
  return Array.isArray(value) && value.every(isOpportunity);
}

/**
 * Type guard for an array of Activities
 */
export function isActivityArray(value: any): value is Activity[] {
  return Array.isArray(value) && value.every(isActivity);
}
