import {
  EventsRawType,
  AttributionResultsType,
  ChartSpecType,
  SuggestedExperimentType,
  CBICacheType,
  DatasetFieldType,
  AnalyticsDatasetType,
  CampaignCostType,
  MarketingCampaignType
} from '../analytics-reporting';

/**
 * Type guard for EventsRawType
 */
export function isEventsRaw(value: any): value is EventsRawType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.tenant_id === 'string' &&
    (value.visitor_id === undefined || typeof value.visitor_id === 'string') &&
    (value.contact_id === undefined || typeof value.contact_id === 'string') &&
    typeof value.timestamp === 'string' &&
    typeof value.channel === 'string' &&
    ['web', 'email', 'ads', 'social', 'voice', 'offline'].includes(value.channel) &&
    (value.campaign === undefined || typeof value.campaign === 'string') &&
    (value.medium === undefined || typeof value.medium === 'string') &&
    (value.source === undefined || typeof value.source === 'string') &&
    typeof value.event_type === 'string' &&
    typeof value.meta_json === 'object' &&
    typeof value.created_at === 'string' &&
    typeof value.updated_at === 'string';
}

/**
 * Type guard for AttributionResultsType
 */
export function isAttributionResults(value: any): value is AttributionResultsType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.tenant_id === 'string' &&
    typeof value.window === 'string' &&
    typeof value.model_type === 'string' &&
    ['markov', 'shapley', 'first_touch', 'last_touch', 'linear'].includes(value.model_type) &&
    typeof value.channel === 'string' &&
    (value.campaign === undefined || typeof value.campaign === 'string') &&
    (value.medium === undefined || typeof value.medium === 'string') &&
    (value.source === undefined || typeof value.source === 'string') &&
    (value.creative === undefined || typeof value.creative === 'string') &&
    (value.keyword === undefined || typeof value.keyword === 'string') &&
    typeof value.credit_pct === 'number' &&
    typeof value.cost === 'number' &&
    typeof value.revenue === 'number' &&
    typeof value.roi === 'number' &&
    typeof value.conversions === 'number' &&
    typeof value.created_at === 'string' &&
    typeof value.updated_at === 'string';
}

/**
 * Type guard for ChartSpecType
 */
export function isChartSpec(value: any): value is ChartSpecType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.type === 'string' &&
    value.data !== undefined &&
    typeof value.options === 'object';
}

/**
 * Type guard for SuggestedExperimentType
 */
export function isSuggestedExperiment(value: any): value is SuggestedExperimentType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.title === 'string' &&
    typeof value.description === 'string' &&
    typeof value.estimated_impact === 'string' &&
    typeof value.confidence === 'number' &&
    (value.workflow_template === undefined || typeof value.workflow_template === 'object');
}

/**
 * Type guard for CBICacheType
 */
export function isCBICache(value: any): value is CBICacheType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.tenant_id === 'string' &&
    typeof value.question === 'string' &&
    typeof value.question_hash === 'string' &&
    typeof value.result_json === 'object' &&
    isChartSpec(value.chart_spec) &&
    typeof value.narrative === 'string' &&
    (value.root_cause_analysis === undefined || typeof value.root_cause_analysis === 'string') &&
    (value.suggested_experiments === undefined || (Array.isArray(value.suggested_experiments) && 
      value.suggested_experiments.every((exp: any) => isSuggestedExperiment(exp)))) &&
    typeof value.dataset_ref === 'string' &&
    typeof value.query_type === 'string' &&
    ['sql', 'cypher', 'vector'].includes(value.query_type) &&
    (value.raw_query === undefined || typeof value.raw_query === 'string') &&
    typeof value.execution_time_ms === 'number' &&
    typeof value.created_at === 'string' &&
    typeof value.expires_at === 'string';
}

/**
 * Type guard for DatasetFieldType
 */
export function isDatasetField(value: any): value is DatasetFieldType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.name === 'string' &&
    typeof value.display_name === 'string' &&
    typeof value.data_type === 'string' &&
    ['string', 'number', 'date', 'boolean'].includes(value.data_type) &&
    typeof value.description === 'string' &&
    typeof value.is_dimension === 'boolean' &&
    typeof value.is_metric === 'boolean' &&
    (value.format === undefined || (typeof value.format === 'string' && 
      ['currency', 'percentage', 'date', 'number', 'text'].includes(value.format))) &&
    (value.aggregation === undefined || (typeof value.aggregation === 'string' && 
      ['sum', 'avg', 'count', 'min', 'max', 'distinct'].includes(value.aggregation)));
}

/**
 * Type guard for AnalyticsDatasetType
 */
export function isAnalyticsDataset(value: any): value is AnalyticsDatasetType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.tenant_id === 'string' &&
    typeof value.name === 'string' &&
    typeof value.display_name === 'string' &&
    typeof value.description === 'string' &&
    typeof value.source_type === 'string' &&
    ['bigquery', 'mongodb', 'neo4j'].includes(value.source_type) &&
    typeof value.source_config === 'object' &&
    Array.isArray(value.fields) &&
    value.fields.every((field: any) => isDatasetField(field)) &&
    typeof value.is_active === 'boolean' &&
    typeof value.refresh_frequency === 'string' &&
    ['hourly', 'daily', 'weekly'].includes(value.refresh_frequency) &&
    (value.last_refreshed_at === undefined || typeof value.last_refreshed_at === 'string') &&
    typeof value.created_at === 'string' &&
    typeof value.updated_at === 'string';
}

/**
 * Type guard for CampaignCostType
 */
export function isCampaignCost(value: any): value is CampaignCostType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.date === 'string' &&
    typeof value.amount === 'number' &&
    typeof value.currency === 'string' &&
    typeof value.source === 'string' &&
    ['manual', 'api'].includes(value.source);
}

/**
 * Type guard for MarketingCampaignType
 */
export function isMarketingCampaign(value: any): value is MarketingCampaignType {
  return value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.tenant_id === 'string' &&
    typeof value.name === 'string' &&
    typeof value.description === 'string' &&
    typeof value.channel === 'string' &&
    ['web', 'email', 'ads', 'social', 'voice', 'offline'].includes(value.channel) &&
    (value.platform === undefined || typeof value.platform === 'string') &&
    (value.utm_campaign === undefined || typeof value.utm_campaign === 'string') &&
    (value.utm_medium === undefined || typeof value.utm_medium === 'string') &&
    (value.utm_source === undefined || typeof value.utm_source === 'string') &&
    typeof value.start_date === 'string' &&
    (value.end_date === undefined || typeof value.end_date === 'string') &&
    typeof value.status === 'string' &&
    ['draft', 'active', 'paused', 'completed'].includes(value.status) &&
    typeof value.budget === 'number' &&
    typeof value.currency === 'string' &&
    Array.isArray(value.costs) &&
    value.costs.every((cost: any) => isCampaignCost(cost)) &&
    typeof value.total_cost === 'number' &&
    typeof value.total_impressions === 'number' &&
    typeof value.total_clicks === 'number' &&
    typeof value.total_conversions === 'number' &&
    typeof value.conversion_value === 'number' &&
    Array.isArray(value.tags) &&
    value.tags.every((tag: any) => typeof tag === 'string') &&
    typeof value.created_by === 'string' &&
    typeof value.created_at === 'string' &&
    typeof value.updated_at === 'string';
}
