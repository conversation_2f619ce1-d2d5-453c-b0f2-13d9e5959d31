/**
 * Type guards for GraphRAG types
 */

import {
  GraphRAGContextParams,
  MeetingPrepContextParams,
  ObjectionHandlerContextParams,
  ProposalGeneratorContextParams,
  MeetingPrepContext,
  ObjectionHandlerContext,
  ProposalGeneratorContext,
  WizardUsageParams,
  RelationshipHistoryItem,
  Stakeholder,
  BuyingSignal,
  PredictedQuestion,
  SuggestedCollateral,
  CompanyNewsItem,
  SimilarObjection,
  EffectiveRebuttal,
  SupportingEvidence,
  PricingTier,
  DiscountPolicy,
  LegalBoilerplate,
  SuggestedUpsell,
  IndustrySpecificClause,
  SimilarDeal
} from '../graph-rag';

/**
 * Type guard for GraphRAGContextParams
 */
export function isGraphRAGContextParams(value: any): value is GraphRAGContextParams {
  return (
    value !== null &&
    typeof value === 'object' &&
    typeof value.tenantId === 'string' &&
    (value.opportunityId === undefined || typeof value.opportunityId === 'string') &&
    (value.contactId === undefined || typeof value.contactId === 'string') &&
    (value.companyId === undefined || typeof value.companyId === 'string')
  );
}

/**
 * Type guard for MeetingPrepContextParams
 */
export function isMeetingPrepContextParams(value: any): value is MeetingPrepContextParams {
  return (
    isGraphRAGContextParams(value) &&
    (value.meetingId === undefined || typeof value.meetingId === 'string')
  );
}

/**
 * Type guard for ObjectionHandlerContextParams
 */
export function isObjectionHandlerContextParams(value: any): value is ObjectionHandlerContextParams {
  return (
    isGraphRAGContextParams(value) &&
    typeof value.objectionText === 'string' &&
    (value.objectionClass === undefined || typeof value.objectionClass === 'string')
  );
}

/**
 * Type guard for ProposalGeneratorContextParams
 */
export function isProposalGeneratorContextParams(value: any): value is ProposalGeneratorContextParams {
  return (
    isGraphRAGContextParams(value) &&
    typeof value.opportunityId === 'string'
  );
}

/**
 * Type guard for RelationshipHistoryItem
 */
export function isRelationshipHistoryItem(value: any): value is RelationshipHistoryItem {
  return (
    value !== null &&
    typeof value === 'object' &&
    typeof value.timestamp === 'string' &&
    typeof value.type === 'string' &&
    typeof value.nodeType === 'string' &&
    typeof value.nodeId === 'string' &&
    typeof value.nodeName === 'string' &&
    typeof value.properties === 'object'
  );
}

/**
 * Type guard for Stakeholder
 */
export function isStakeholder(value: any): value is Stakeholder {
  return (
    value !== null &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.name === 'string' &&
    (value.title === undefined || typeof value.title === 'string') &&
    (value.email === undefined || typeof value.email === 'string') &&
    (value.phone === undefined || typeof value.phone === 'string') &&
    typeof value.importance === 'string' &&
    ['low', 'medium', 'high'].includes(value.importance)
  );
}

/**
 * Type guard for MeetingPrepContext
 */
export function isMeetingPrepContext(value: any): value is MeetingPrepContext {
  return (
    value !== null &&
    typeof value === 'object' &&
    Array.isArray(value.relationshipHistory) &&
    Array.isArray(value.stakeholders) &&
    Array.isArray(value.buyingSignals) &&
    Array.isArray(value.predictedQuestions) &&
    Array.isArray(value.suggestedCollateral) &&
    (value.companyNews === undefined || Array.isArray(value.companyNews))
  );
}

/**
 * Type guard for ObjectionHandlerContext
 */
export function isObjectionHandlerContext(value: any): value is ObjectionHandlerContext {
  return (
    value !== null &&
    typeof value === 'object' &&
    typeof value.objectionClass === 'string' &&
    Array.isArray(value.similarObjections) &&
    Array.isArray(value.effectiveRebuttals) &&
    Array.isArray(value.supportingEvidence)
  );
}

/**
 * Type guard for ProposalGeneratorContext
 */
export function isProposalGeneratorContext(value: any): value is ProposalGeneratorContext {
  return (
    value !== null &&
    typeof value === 'object' &&
    Array.isArray(value.pricingTiers) &&
    typeof value.discountPolicy === 'object' &&
    typeof value.legalBoilerplate === 'object' &&
    Array.isArray(value.suggestedUpsells) &&
    Array.isArray(value.industrySpecificClauses) &&
    Array.isArray(value.similarDeals)
  );
}

/**
 * Type guard for WizardUsageParams
 */
export function isWizardUsageParams(value: any): value is WizardUsageParams {
  return (
    value !== null &&
    typeof value === 'object' &&
    typeof value.wizardType === 'string' &&
    [
      'meeting_prep',
      'objection_handler',
      'proposal_generator',
      'follow_up_coach',
      'win_loss_analyzer'
    ].includes(value.wizardType) &&
    typeof value.userId === 'string' &&
    typeof value.tenantId === 'string' &&
    typeof value.entityId === 'string' &&
    typeof value.entityType === 'string' &&
    typeof value.result === 'string' &&
    ['success', 'failure'].includes(value.result) &&
    (value.metadata === undefined || typeof value.metadata === 'object')
  );
}
