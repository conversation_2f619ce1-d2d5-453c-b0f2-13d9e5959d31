import { 
  Experiment, 
  ExperimentStatus, 
  ExperimentVariant, 
  ExperimentGoal,
  ExperimentAudience,
  ExperimentFilter,
  ExperimentFilterType,
  ExperimentResult,
  ExperimentConversion
} from '../experiments';

/**
 * Type guard for ExperimentStatus
 */
export function isExperimentStatus(value: any): value is ExperimentStatus {
  return typeof value === 'string' && 
    ['draft', 'running', 'paused', 'completed', 'archived'].includes(value);
}

/**
 * Type guard for ExperimentFilterType
 */
export function isExperimentFilterType(value: any): value is ExperimentFilterType {
  return typeof value === 'string' && 
    ['user', 'tenant', 'location', 'device', 'custom'].includes(value);
}

/**
 * Type guard for ExperimentFilter
 */
export function isExperimentFilter(value: any): value is ExperimentFilter {
  return value !== null && 
    typeof value === 'object' && 
    isExperimentFilterType(value.type) && 
    'value' in value;
}

/**
 * Type guard for ExperimentVariant
 */
export function isExperimentVariant(value: any): value is ExperimentVariant {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.key === 'string' && 
    typeof value.name === 'string' && 
    typeof value.weight === 'number' && 
    (value.description === undefined || typeof value.description === 'string') && 
    (value.config === undefined || (typeof value.config === 'object' && value.config !== null));
}

/**
 * Type guard for ExperimentGoal
 */
export function isExperimentGoal(value: any): value is ExperimentGoal {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.key === 'string' && 
    typeof value.name === 'string' && 
    typeof value.primary === 'boolean' && 
    (value.description === undefined || typeof value.description === 'string');
}

/**
 * Type guard for ExperimentAudience
 */
export function isExperimentAudience(value: any): value is ExperimentAudience {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.percentage === 'number' && 
    (value.userIds === undefined || (Array.isArray(value.userIds) && 
      value.userIds.every((id: any) => typeof id === 'string'))) && 
    (value.tenantIds === undefined || (Array.isArray(value.tenantIds) && 
      value.tenantIds.every((id: any) => typeof id === 'string'))) && 
    (value.filters === undefined || (Array.isArray(value.filters) && 
      value.filters.every((filter: any) => isExperimentFilter(filter))));
}

/**
 * Type guard for Experiment
 */
export function isExperiment(value: any): value is Experiment {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.id === 'string' && 
    typeof value.key === 'string' && 
    typeof value.name === 'string' && 
    isExperimentStatus(value.status) && 
    (value.startDate === undefined || typeof value.startDate === 'string') && 
    (value.endDate === undefined || typeof value.endDate === 'string') && 
    Array.isArray(value.variants) && 
    value.variants.every((variant: any) => isExperimentVariant(variant)) && 
    isExperimentAudience(value.audience) && 
    Array.isArray(value.goals) && 
    value.goals.every((goal: any) => isExperimentGoal(goal)) && 
    typeof value.createdBy === 'string' && 
    typeof value.createdAt === 'string' && 
    typeof value.updatedAt === 'string';
}

/**
 * Type guard for ExperimentConversion
 */
export function isExperimentConversion(value: any): value is ExperimentConversion {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.goalKey === 'string' && 
    typeof value.timestamp === 'string' && 
    (value.value === undefined || typeof value.value === 'number') && 
    (value.metadata === undefined || (typeof value.metadata === 'object' && value.metadata !== null));
}

/**
 * Type guard for ExperimentResult
 */
export function isExperimentResult(value: any): value is ExperimentResult {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.id === 'string' && 
    typeof value.experimentId === 'string' && 
    typeof value.userId === 'string' && 
    (value.tenantId === undefined || typeof value.tenantId === 'string') && 
    typeof value.sessionId === 'string' && 
    typeof value.variant === 'string' && 
    Array.isArray(value.exposures) && 
    value.exposures.every((exposure: any) => 
      typeof exposure === 'object' && 
      typeof exposure.timestamp === 'string' && 
      (exposure.context === undefined || (typeof exposure.context === 'object' && exposure.context !== null))
    ) && 
    Array.isArray(value.conversions) && 
    value.conversions.every((conversion: any) => isExperimentConversion(conversion)) && 
    typeof value.createdAt === 'string' && 
    typeof value.updatedAt === 'string';
}

/**
 * Type guard for an array of Experiments
 */
export function isExperimentArray(value: any): value is Experiment[] {
  return Array.isArray(value) && value.every(isExperiment);
}

/**
 * Type guard for an array of ExperimentResults
 */
export function isExperimentResultArray(value: any): value is ExperimentResult[] {
  return Array.isArray(value) && value.every(isExperimentResult);
}
