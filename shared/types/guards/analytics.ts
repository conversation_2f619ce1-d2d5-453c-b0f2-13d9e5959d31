import { 
  AnalyticsEvent, 
  AnalyticsContext,
  AnalyticsSession,
  AnalyticsCount,
  FunnelStep
} from '../analytics';

/**
 * Type guard for AnalyticsContext
 */
export function isAnalyticsContext(value: any): value is AnalyticsContext {
  return value !== null && 
    typeof value === 'object' && 
    (value.page === undefined || (
      typeof value.page === 'object' && 
      value.page !== null && 
      (value.page.url === undefined || typeof value.page.url === 'string') && 
      (value.page.path === undefined || typeof value.page.path === 'string') && 
      (value.page.title === undefined || typeof value.page.title === 'string') && 
      (value.page.referrer === undefined || typeof value.page.referrer === 'string')
    )) && 
    (value.userAgent === undefined || typeof value.userAgent === 'string') && 
    (value.ip === undefined || typeof value.ip === 'string') && 
    (value.location === undefined || (
      typeof value.location === 'object' && 
      value.location !== null && 
      (value.location.country === undefined || typeof value.location.country === 'string') && 
      (value.location.region === undefined || typeof value.location.region === 'string') && 
      (value.location.city === undefined || typeof value.location.city === 'string')
    )) && 
    (value.device === undefined || (
      typeof value.device === 'object' && 
      value.device !== null && 
      (value.device.type === undefined || typeof value.device.type === 'string') && 
      (value.device.browser === undefined || typeof value.device.browser === 'string') && 
      (value.device.os === undefined || typeof value.device.os === 'string')
    ));
}

/**
 * Type guard for AnalyticsEvent
 */
export function isAnalyticsEvent(value: any): value is AnalyticsEvent {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.id === 'string' && 
    (value.userId === undefined || typeof value.userId === 'string') && 
    (value.tenantId === undefined || typeof value.tenantId === 'string') && 
    typeof value.sessionId === 'string' && 
    typeof value.eventType === 'string' && 
    typeof value.eventName === 'string' && 
    typeof value.properties === 'object' && 
    value.properties !== null && 
    isAnalyticsContext(value.context) && 
    typeof value.timestamp === 'string';
}

/**
 * Type guard for AnalyticsSession
 */
export function isAnalyticsSession(value: any): value is AnalyticsSession {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.sessionId === 'string' && 
    (value.userId === undefined || typeof value.userId === 'string') && 
    (value.tenantId === undefined || typeof value.tenantId === 'string') && 
    typeof value.startTime === 'string' && 
    typeof value.endTime === 'string' && 
    typeof value.duration === 'number' && 
    typeof value.eventCount === 'number' && 
    Array.isArray(value.pages) && 
    value.pages.every((page: any) => typeof page === 'string');
}

/**
 * Type guard for AnalyticsCount
 */
export function isAnalyticsCount(value: any): value is AnalyticsCount {
  return value !== null && 
    typeof value === 'object' && 
    typeof value._id === 'string' && 
    typeof value.count === 'number';
}

/**
 * Type guard for FunnelStep
 */
export function isFunnelStep(value: any): value is FunnelStep {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.step === 'number' && 
    typeof value.eventType === 'string' && 
    typeof value.eventName === 'string' && 
    typeof value.count === 'number' && 
    typeof value.dropoff === 'number' && 
    typeof value.conversionRate === 'number';
}

/**
 * Type guard for an array of AnalyticsEvents
 */
export function isAnalyticsEventArray(value: any): value is AnalyticsEvent[] {
  return Array.isArray(value) && value.every(isAnalyticsEvent);
}

/**
 * Type guard for an array of AnalyticsSessions
 */
export function isAnalyticsSessionArray(value: any): value is AnalyticsSession[] {
  return Array.isArray(value) && value.every(isAnalyticsSession);
}

/**
 * Type guard for an array of AnalyticsCounts
 */
export function isAnalyticsCountArray(value: any): value is AnalyticsCount[] {
  return Array.isArray(value) && value.every(isAnalyticsCount);
}

/**
 * Type guard for an array of FunnelSteps
 */
export function isFunnelStepArray(value: any): value is FunnelStep[] {
  return Array.isArray(value) && value.every(isFunnelStep);
}
