/**
 * Type guards for Analytics Events
 */

import { 
  AnalyticsEvent, 
  EventType,
  WizardEvent,
  MeetingPrepEvent,
  ObjectionHandlerEvent,
  ProposalGeneratorEvent,
  BIEvent,
  AttributionEvent
} from '../analytics-events';

/**
 * Type guard for EventType
 */
export function isEventType(value: any): value is EventType {
  return typeof value === 'string' && Object.values(EventType).includes(value as EventType);
}

/**
 * Type guard for AnalyticsEvent
 */
export function isAnalyticsEvent(value: any): value is AnalyticsEvent {
  return (
    value !== null &&
    typeof value === 'object' &&
    isEventType(value.type) &&
    typeof value.tenantId === 'string' &&
    typeof value.userId === 'string' &&
    (value.timestamp === undefined || value.timestamp instanceof Date) &&
    (value.entityId === undefined || typeof value.entityId === 'string') &&
    (value.entityType === undefined || typeof value.entityType === 'string') &&
    (value.metadata === undefined || typeof value.metadata === 'object')
  );
}

/**
 * Type guard for WizardEvent
 */
export function isWizardEvent(value: any): value is WizardEvent {
  return (
    isAnalyticsEvent(value) &&
    (
      value.type === EventType.WIZARD_USED ||
      value.type === EventType.WIZARD_SUCCESS ||
      value.type === EventType.WIZARD_FAILURE
    ) &&
    typeof value.metadata === 'object' &&
    typeof value.metadata.wizardType === 'string' &&
    [
      'meeting_prep',
      'objection_handler',
      'proposal_generator',
      'follow_up_coach',
      'win_loss_analyzer'
    ].includes(value.metadata.wizardType)
  );
}

/**
 * Type guard for MeetingPrepEvent
 */
export function isMeetingPrepEvent(value: any): value is MeetingPrepEvent {
  return (
    isAnalyticsEvent(value) &&
    (
      value.type === EventType.MEETING_PREP_GENERATED ||
      value.type === EventType.MEETING_PREP_VIEWED ||
      value.type === EventType.MEETING_PREP_ADDED_TO_AGENDA ||
      value.type === EventType.MEETING_PREP_FEEDBACK
    ) &&
    typeof value.entityType === 'string' &&
    ['contact', 'company', 'opportunity'].includes(value.entityType) &&
    typeof value.metadata === 'object'
  );
}

/**
 * Type guard for ObjectionHandlerEvent
 */
export function isObjectionHandlerEvent(value: any): value is ObjectionHandlerEvent {
  return (
    isAnalyticsEvent(value) &&
    (
      value.type === EventType.OBJECTION_CLASSIFIED ||
      value.type === EventType.OBJECTION_RESPONSE_GENERATED ||
      value.type === EventType.OBJECTION_RESPONSE_USED ||
      value.type === EventType.OBJECTION_RESPONSE_FEEDBACK
    ) &&
    typeof value.metadata === 'object'
  );
}

/**
 * Type guard for ProposalGeneratorEvent
 */
export function isProposalGeneratorEvent(value: any): value is ProposalGeneratorEvent {
  return (
    isAnalyticsEvent(value) &&
    (
      value.type === EventType.PROPOSAL_GENERATED ||
      value.type === EventType.PROPOSAL_VIEWED ||
      value.type === EventType.PROPOSAL_SENT ||
      value.type === EventType.PROPOSAL_ACCEPTED ||
      value.type === EventType.PROPOSAL_REJECTED ||
      value.type === EventType.PROPOSAL_CLAUSES_UPDATED
    ) &&
    typeof value.entityId === 'string' &&
    value.entityType === 'proposal' &&
    typeof value.metadata === 'object' &&
    typeof value.metadata.opportunityId === 'string'
  );
}

/**
 * Type guard for BIEvent
 */
export function isBIEvent(value: any): value is BIEvent {
  return (
    isAnalyticsEvent(value) &&
    (
      value.type === EventType.BI_QUERY_EXECUTED ||
      value.type === EventType.BI_CHART_GENERATED ||
      value.type === EventType.BI_INSIGHT_GENERATED
    ) &&
    typeof value.metadata === 'object'
  );
}

/**
 * Type guard for AttributionEvent
 */
export function isAttributionEvent(value: any): value is AttributionEvent {
  return (
    isAnalyticsEvent(value) &&
    (
      value.type === EventType.ATTRIBUTION_MODEL_UPDATED ||
      value.type === EventType.ATTRIBUTION_TOUCHPOINT_RECORDED ||
      value.type === EventType.ATTRIBUTION_REPORT_GENERATED
    ) &&
    typeof value.metadata === 'object'
  );
}
