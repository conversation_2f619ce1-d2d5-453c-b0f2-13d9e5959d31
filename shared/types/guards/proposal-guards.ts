/**
 * Type guards for Proposal Generator types
 */

import {
  Proposal,
  ProposalSection,
  ProposalTemplate,
  ProposalAIGenerationResponse,
  ProposalAnalytics,
  ProposalAnalyticsEvent,
  ProposalShare,
  ProposalView
} from '../proposal-generator';

/**
 * Type guard for Proposal
 */
export function isProposal(value: unknown): value is Proposal {
  if (!value || typeof value !== 'object') return false;
  
  const proposal = value as Partial<Proposal>;
  
  return (
    typeof proposal.id !== 'undefined' &&
    typeof proposal.name === 'string' &&
    typeof proposal.content === 'string' &&
    Array.isArray(proposal.sections) &&
    typeof proposal.value === 'number' &&
    typeof proposal.currency === 'string' &&
    typeof proposal.status === 'string' &&
    Array.isArray(proposal.tags) &&
    typeof proposal.createdBy !== 'undefined' &&
    proposal.createdAt instanceof Date &&
    proposal.updatedAt instanceof Date
  );
}

/**
 * Type guard for ProposalSection
 */
export function isProposalSection(value: unknown): value is ProposalSection {
  if (!value || typeof value !== 'object') return false;
  
  const section = value as Partial<ProposalSection>;
  
  return (
    typeof section.id === 'string' &&
    typeof section.name === 'string' &&
    typeof section.type === 'string' &&
    typeof section.content === 'string' &&
    typeof section.order === 'number'
  );
}

/**
 * Type guard for ProposalTemplate
 */
export function isProposalTemplate(value: unknown): value is ProposalTemplate {
  if (!value || typeof value !== 'object') return false;
  
  const template = value as Partial<ProposalTemplate>;
  
  return (
    typeof template.id !== 'undefined' &&
    typeof template.name === 'string' &&
    typeof template.category === 'string' &&
    typeof template.content === 'string' &&
    Array.isArray(template.sections) &&
    Array.isArray(template.variables) &&
    typeof template.isDefault === 'boolean' &&
    typeof template.usageCount === 'number' &&
    typeof template.createdBy !== 'undefined' &&
    template.createdAt instanceof Date &&
    template.updatedAt instanceof Date
  );
}

/**
 * Type guard for ProposalAIGenerationResponse
 */
export function isProposalAIGenerationResponse(value: unknown): value is ProposalAIGenerationResponse {
  if (!value || typeof value !== 'object') return false;
  
  const response = value as Partial<ProposalAIGenerationResponse>;
  
  return (
    typeof response.title === 'string' &&
    typeof response.description === 'string' &&
    Array.isArray(response.sections) &&
    typeof response.terms === 'string' &&
    typeof response.aiPrompt === 'string' &&
    typeof response.aiModel === 'string'
  );
}

/**
 * Type guard for ProposalAnalytics
 */
export function isProposalAnalytics(value: unknown): value is ProposalAnalytics {
  if (!value || typeof value !== 'object') return false;
  
  const analytics = value as Partial<ProposalAnalytics>;
  
  return (
    typeof analytics.proposalId === 'string' &&
    typeof analytics.views === 'number' &&
    typeof analytics.uniqueViews === 'number' &&
    typeof analytics.downloads === 'number' &&
    typeof analytics.shares === 'number' &&
    typeof analytics.averageViewDuration === 'number' &&
    Array.isArray(analytics.events)
  );
}

/**
 * Type guard for ProposalAnalyticsEvent
 */
export function isProposalAnalyticsEvent(value: unknown): value is ProposalAnalyticsEvent {
  if (!value || typeof value !== 'object') return false;
  
  const event = value as Partial<ProposalAnalyticsEvent>;
  
  return (
    typeof event.id === 'string' &&
    typeof event.type === 'string' &&
    event.timestamp instanceof Date
  );
}

/**
 * Type guard for ProposalShare
 */
export function isProposalShare(value: unknown): value is ProposalShare {
  if (!value || typeof value !== 'object') return false;
  
  const share = value as Partial<ProposalShare>;
  
  return (
    typeof share.id === 'string' &&
    typeof share.proposalId === 'string' &&
    typeof share.token === 'string' &&
    typeof share.url === 'string' &&
    typeof share.createdBy === 'string' &&
    share.createdAt instanceof Date &&
    typeof share.accessCount === 'number'
  );
}

/**
 * Type guard for ProposalView
 */
export function isProposalView(value: unknown): value is ProposalView {
  if (!value || typeof value !== 'object') return false;
  
  const view = value as Partial<ProposalView>;
  
  return (
    typeof view.id === 'string' &&
    typeof view.proposalId === 'string' &&
    view.timestamp instanceof Date &&
    typeof view.isUnique === 'boolean'
  );
}
