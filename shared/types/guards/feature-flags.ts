import { FeatureFlag, FeatureFlagRule, FeatureFlagRuleType } from '../feature-flags';

/**
 * Type guard for FeatureFlagRuleType
 */
export function isFeatureFlagRuleType(value: any): value is FeatureFlagRuleType {
  return typeof value === 'string' && 
    ['user', 'tenant', 'date', 'time', 'location', 'custom'].includes(value);
}

/**
 * Type guard for FeatureFlagRule
 */
export function isFeatureFlagRule(value: any): value is FeatureFlagRule {
  return value !== null && 
    typeof value === 'object' && 
    isFeatureFlagRuleType(value.type) && 
    'value' in value;
}

/**
 * Type guard for FeatureFlag
 */
export function isFeatureFlag(value: any): value is FeatureFlag {
  return value !== null && 
    typeof value === 'object' && 
    typeof value.id === 'string' && 
    typeof value.key === 'string' && 
    typeof value.name === 'string' && 
    typeof value.enabled === 'boolean' && 
    Array.isArray(value.enabledForUsers) && 
    value.enabledForUsers.every((id: any) => typeof id === 'string') && 
    Array.isArray(value.enabledForTenants) && 
    value.enabledForTenants.every((id: any) => typeof id === 'string') && 
    typeof value.enabledForPercentage === 'number' && 
    Array.isArray(value.rules) && 
    value.rules.every((rule: any) => isFeatureFlagRule(rule)) && 
    Array.isArray(value.tags) && 
    value.tags.every((tag: any) => typeof tag === 'string') && 
    typeof value.createdBy === 'string' && 
    typeof value.createdAt === 'string' && 
    typeof value.updatedAt === 'string';
}

/**
 * Type guard for an array of FeatureFlags
 */
export function isFeatureFlagArray(value: any): value is FeatureFlag[] {
  return Array.isArray(value) && value.every(isFeatureFlag);
}
