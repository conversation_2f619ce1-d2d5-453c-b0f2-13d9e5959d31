/**
 * Proposal Generator types for the Aizako CRM project
 * These types represent the data structures used by the Proposal Generator feature
 */

import { Id } from './utils';

/**
 * Proposal section type
 */
export type ProposalSectionType =
  | 'text'
  | 'pricing'
  | 'timeline'
  | 'team'
  | 'testimonials'
  | 'terms'
  | 'custom';

/**
 * Proposal document type
 */
export type ProposalDocumentType =
  | 'proposal'
  | 'contract'
  | 'statement_of_work'
  | 'quote'
  | 'estimate';

/**
 * Proposal section
 */
export interface ProposalSection {
  id: string;
  name: string;
  type: ProposalSectionType;
  content: string;
  order: number;
}

/**
 * Proposal template
 */
export interface ProposalTemplate {
  id: Id;
  name: string;
  description?: string;
  category: string;
  content: string;
  sections: ProposalSection[];
  variables: string[];
  isDefault: boolean;
  usageCount: number;
  createdBy: Id;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Proposal
 */
export interface Proposal {
  id: Id;
  name: string;
  description?: string;
  opportunityId: Id;
  contactId?: Id;
  companyId?: Id;
  templateId: Id;
  content: string;
  sections: ProposalSection[];
  value: number;
  currency: string;
  validUntil?: Date;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected';
  tags: string[];
  notes?: string;
  customFields?: Record<string, any>;
  createdBy: Id;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Proposal generation request
 */
export interface ProposalGenerationRequest {
  opportunityId: string;
  templateId?: string;
  contactId?: string;
  companyId?: string;
}

/**
 * Proposal generation response
 */
export interface ProposalGenerationResponse {
  proposal: Proposal;
}

/**
 * Proposal export format
 */
export type ProposalExportFormat =
  | 'html'
  | 'pdf'
  | 'docx'
  | 'markdown'
  | 'claude-html';

/**
 * Proposal export request
 */
export interface ProposalExportRequest {
  proposalId: string;
  format: ProposalExportFormat;
}

/**
 * Proposal export response
 */
export interface ProposalExportResponse {
  url: string;
  expiresAt: Date;
}

/**
 * Proposal share request
 */
export interface ProposalShareRequest {
  proposalId: string;
  expiresAt?: Date;
  password?: string;
  format?: ProposalExportFormat;
  expiresIn?: number;
}

/**
 * Proposal share response
 */
export interface ProposalShareResponse {
  shareId: string;
  shareUrl: string;
  expiresAt?: Date;
  isPasswordProtected: boolean;
}

/**
 * Proposal email request
 */
export interface ProposalEmailRequest {
  proposalId: string;
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject?: string;
  message?: string;
  format?: ProposalExportFormat;
  email?: string; // For single recipient email
}

/**
 * Proposal email response
 */
export interface ProposalEmailResponse {
  messageId: string;
  sentAt: Date;
  recipients: string[];
}

/**
 * Proposal social export request
 */
export interface ProposalSocialExportRequest {
  proposalId: string;
  platform: 'linkedin' | 'twitter' | 'facebook';
  message?: string;
}

/**
 * Proposal social export response
 */
export interface ProposalSocialExportResponse {
  postId?: string;
  postUrl?: string;
  status: 'success' | 'pending' | 'failed';
}

/**
 * Proposal clauses update request
 */
export interface ProposalClausesUpdateRequest {
  proposalId: string;
  clauses: string[];
}

/**
 * Proposal clauses update response
 */
export interface ProposalClausesUpdateResponse {
  proposal: Proposal;
}

/**
 * Industry-specific clause
 */
export interface IndustrySpecificClause {
  id: string;
  name: string;
  text: string;
  required: boolean;
}

/**
 * Props for LegalClausePicker component
 */
export interface LegalClausePickerProps {
  proposalId: string;
  companyId?: string;
  industrySpecificClauses?: IndustrySpecificClause[];
  onSuccess?: () => void;
}

/**
 * Proposal AI generation options
 */
export interface ProposalAIGenerationOptions {
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
  prompt: string;
  model?: string;
  includeSections?: {
    executiveSummary?: boolean;
    solution?: boolean;
    timeline?: boolean;
    pricing?: boolean;
    team?: boolean;
    testimonials?: boolean;
    terms?: boolean;
  };
  customInstructions?: string;
}

/**
 * Proposal AI generation response
 */
export interface ProposalAIGenerationResponse {
  title: string;
  description: string;
  sections: Array<{
    title: string;
    content: string;
    type: ProposalSectionType;
    order: number;
  }>;
  pricing?: {
    items: Array<{
      name: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      total: number;
    }>;
  };
  terms: string;
  aiPrompt: string;
  aiModel: string;
}

/**
 * Proposal analytics event
 */
export interface ProposalAnalyticsEvent {
  id: string;
  type: 'view' | 'download' | 'share' | 'accept' | 'reject' | 'comment';
  timestamp: Date;
  data?: Record<string, any>;
  ip?: string;
  userAgent?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
}

/**
 * Proposal analytics
 */
export interface ProposalAnalytics {
  proposalId: string;
  views: number;
  uniqueViews: number;
  downloads: number;
  shares: number;
  averageViewDuration: number;
  events: ProposalAnalyticsEvent[];
  lastViewedAt?: Date;
}

/**
 * Proposal download options
 */
export interface ProposalDownloadOptions {
  format: ProposalExportFormat;
  includeCompanyLogo?: boolean;
  includeSignaturePage?: boolean;
  includeAttachments?: boolean;
  customHeader?: string;
  customFooter?: string;
  watermark?: string;
}

/**
 * Proposal view
 */
export interface ProposalView {
  id: string;
  proposalId: string;
  viewerIp?: string;
  viewerUserAgent?: string;
  viewerLocation?: string;
  viewDuration?: number;
  timestamp: Date;
  isUnique: boolean;
}

/**
 * Proposal share
 */
export interface ProposalShare {
  id: string;
  proposalId: string;
  token: string;
  url: string;
  expiresAt?: Date;
  createdBy: string;
  createdAt: Date;
  accessCount: number;
  lastAccessedAt?: Date;
}

/**
 * Proposal document generation request
 */
export interface ProposalDocumentGenerationRequest {
  proposalId: string;
  format: ProposalExportFormat;
  options?: {
    includeCompanyLogo?: boolean;
    includeSignaturePage?: boolean;
    includeAttachments?: boolean;
    customHeader?: string;
    customFooter?: string;
    watermark?: string;
  };
}

/**
 * Proposal document generation response
 */
export interface ProposalDocumentGenerationResponse {
  documentId: string;
  url: string;
  format: ProposalExportFormat;
  expiresAt: Date;
  size?: number;
}

/**
 * Proposal AI generator component props
 */
export interface ProposalAIGeneratorProps {
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
  onGenerate?: (options: ProposalAIGenerationOptions) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

/**
 * Proposal download options component props
 */
export interface ProposalDownloadOptionsProps {
  proposalId: string;
  onDownload?: (options: ProposalDownloadOptions) => void;
  onCancel?: () => void;
  availableFormats?: ProposalExportFormat[];
}

/**
 * Proposal generator dialog props
 */
export interface ProposalGeneratorDialogProps {
  open: boolean;
  opportunityId?: string;
  companyId?: string;
  contactIds?: string[];
  onClose: () => void;
  onSuccess?: (proposalId: string) => void;
}

/**
 * Proposal share dialog props
 */
export interface ProposalShareDialogProps {
  open: boolean;
  proposalId: string;
  onClose: () => void;
  onSuccess?: (shareUrl: string) => void;
}
