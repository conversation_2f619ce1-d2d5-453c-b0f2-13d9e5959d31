/**
 * Experiment types
 */

export type ExperimentStatus = 'draft' | 'running' | 'paused' | 'completed' | 'archived';

export interface ExperimentVariant {
  key: string;
  name: string;
  description?: string;
  weight: number;
  config?: Record<string, any>;
}

export interface ExperimentGoal {
  key: string;
  name: string;
  description?: string;
  primary: boolean;
}

export interface ExperimentAudience {
  percentage: number;
  userIds?: string[];
  tenantIds?: string[];
  filters?: ExperimentFilter[];
}

export interface ExperimentFilter {
  type: ExperimentFilterType;
  value: any;
}

export type ExperimentFilterType = 'user' | 'tenant' | 'location' | 'device' | 'custom';

export interface Experiment {
  id: string;
  key: string;
  name: string;
  description?: string;
  status: ExperimentStatus;
  startDate?: string;
  endDate?: string;
  variants: ExperimentVariant[];
  audience: ExperimentAudience;
  goals: ExperimentGoal[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ExperimentContext {
  userId: string;
  tenantId?: string;
  sessionId: string;
  location?: {
    country?: string;
    region?: string;
  };
  device?: {
    type?: string;
    browser?: string;
    os?: string;
  };
  custom?: Record<string, any>;
}

export interface ExperimentConversion {
  goalKey: string;
  timestamp: string;
  value?: number;
  metadata?: Record<string, any>;
}

export interface ExperimentResult {
  id: string;
  experimentId: string;
  userId: string;
  tenantId?: string;
  sessionId: string;
  variant: string;
  exposures: {
    timestamp: string;
    context?: Record<string, any>;
  }[];
  conversions: ExperimentConversion[];
  createdAt: string;
  updatedAt: string;
}

/**
 * API request/response types
 */

export interface CreateExperimentRequest {
  key: string;
  name: string;
  description?: string;
  variants: {
    key: string;
    name: string;
    description?: string;
    weight: number;
    config?: Record<string, any>;
  }[];
  audience?: {
    percentage?: number;
    userIds?: string[];
    tenantIds?: string[];
    filters?: {
      type: ExperimentFilterType;
      value: any;
    }[];
  };
  goals: {
    key: string;
    name: string;
    description?: string;
    primary?: boolean;
  }[];
}

export interface UpdateExperimentRequest {
  id: string;
  name?: string;
  description?: string;
  status?: ExperimentStatus;
  startDate?: string;
  endDate?: string;
  variants?: {
    key: string;
    name: string;
    description?: string;
    weight: number;
    config?: Record<string, any>;
  }[];
  audience?: {
    percentage?: number;
    userIds?: string[];
    tenantIds?: string[];
    filters?: {
      type: ExperimentFilterType;
      value: any;
    }[];
  };
  goals?: {
    key: string;
    name: string;
    description?: string;
    primary?: boolean;
  }[];
}

export interface TrackConversionRequest {
  goalKey: string;
  value?: number;
  metadata?: Record<string, any>;
}

export interface GetExperimentsResponse {
  success: boolean;
  experiments: Experiment[];
}

export interface GetExperimentResponse {
  success: boolean;
  experiment: Experiment;
}

export interface CreateExperimentResponse {
  success: boolean;
  experiment: Experiment;
}

export interface UpdateExperimentResponse {
  success: boolean;
  experiment: Experiment;
}

export interface DeleteExperimentResponse {
  success: boolean;
  message: string;
}

export interface GetVariantResponse {
  success: boolean;
  variant: string;
}

export interface TrackConversionResponse {
  success: boolean;
  message: string;
}

export interface GetResultsResponse {
  success: boolean;
  experiment: Experiment;
  variants: {
    key: string;
    name: string;
    users: number;
    conversions: {
      goalKey: string;
      count: number;
      conversionRate: number;
      value?: number;
    }[];
  }[];
}

export interface FunnelAnalysisRequest {
  steps: { eventType: string; eventName: string }[];
  startDate?: string;
  endDate?: string;
  windowHours?: number;
}
