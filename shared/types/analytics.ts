/**
 * Analytics types
 */

export interface AnalyticsEvent {
  id: string;
  userId?: string;
  tenantId?: string;
  sessionId: string;
  eventType: string;
  eventName: string;
  properties: Record<string, any>;
  context: AnalyticsContext;
  timestamp: string;
}

export interface AnalyticsContext {
  page?: {
    url?: string;
    path?: string;
    title?: string;
    referrer?: string;
  };
  userAgent?: string;
  ip?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  device?: {
    type?: string;
    browser?: string;
    os?: string;
  };
}

export interface AnalyticsSession {
  sessionId: string;
  userId?: string;
  tenantId?: string;
  startTime: string;
  endTime: string;
  duration: number;
  eventCount: number;
  pages: string[];
}

export interface AnalyticsCount {
  _id: string;
  count: number;
}

export interface FunnelStep {
  step: number;
  eventType: string;
  eventName: string;
  count: number;
  dropoff: number;
  conversionRate: number;
}

/**
 * API request/response types
 */

export interface TrackEventRequest {
  eventType: string;
  eventName: string;
  properties?: Record<string, any>;
  sessionId?: string;
  timestamp?: string;
  context?: AnalyticsContext;
}

export interface TrackEventResponse {
  success: boolean;
  event: AnalyticsEvent;
}

export interface GetEventsRequest {
  userId?: string;
  tenantId?: string;
  sessionId?: string;
  eventType?: string;
  eventName?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

export interface GetEventsResponse {
  success: boolean;
  events: AnalyticsEvent[];
}

export interface GetEventCountsRequest {
  userId?: string;
  tenantId?: string;
  eventType?: string;
  startDate?: string;
  endDate?: string;
  groupBy?: 'eventType' | 'eventName' | 'day' | 'week' | 'month';
}

export interface GetEventCountsResponse {
  success: boolean;
  counts: AnalyticsCount[];
}

export interface GetSessionsRequest {
  userId?: string;
  tenantId?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

export interface GetSessionsResponse {
  success: boolean;
  sessions: AnalyticsSession[];
}

export interface FunnelAnalysisRequest {
  steps: { eventType: string; eventName: string }[];
  userId?: string;
  tenantId?: string;
  startDate?: string;
  endDate?: string;
  windowHours?: number;
}

export interface FunnelAnalysisResponse {
  success: boolean;
  funnel: FunnelStep[];
}

export interface GetUserAnalyticsResponse {
  success: boolean;
  events: AnalyticsEvent[];
  counts: AnalyticsCount[];
  sessions: AnalyticsSession[];
}
