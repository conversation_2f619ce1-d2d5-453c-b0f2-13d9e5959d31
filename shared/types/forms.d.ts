/**
 * Form type definitions for the Aizako CRM project
 */

// Contact form values
export interface ContactFormValues {
  id?: string | number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  company?: string;
  companyId?: string | number;
  status?: string;
  source?: string;
  notes?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  website?: string;
  linkedin?: string;
  twitter?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

// Company form values
export interface CompanyFormValues {
  id?: string | number;
  name: string;
  industry?: string;
  website?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  employees?: number;
  revenue?: number;
  status?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

// Opportunity form values
export interface OpportunityFormValues {
  id?: string | number;
  name: string;
  contactId?: string | number;
  companyId?: string | number;
  amount?: number;
  currency?: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: Date | string;
  status?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  products?: string[];
}

// Activity form values
export interface ActivityFormValues {
  id?: string | number;
  type: 'note' | 'task' | 'email' | 'call' | 'meeting' | 'custom';
  title?: string;
  description?: string;
  contactId?: string | number;
  companyId?: string | number;
  opportunityId?: string | number;
  dueDate?: Date | string;
  status?: string;
  priority?: string;
  // Type-specific fields
  content?: string;
  subject?: string;
  body?: string;
  to?: string;
  duration?: number;
  startTime?: string;
  endTime?: string;
  location?: string;
  attendees?: string[];
  customFields?: Record<string, any>;
}

// Email template form values
export interface EmailTemplateFormValues {
  id?: string | number;
  name: string;
  subject: string;
  body: string;
  category?: string;
  tags?: string[];
  isDefault?: boolean;
  variables?: string[];
}

// Feature flag form values
export interface FeatureFlagFormValues {
  id?: string | number;
  name: string;
  key: string;
  description?: string;
  enabled: boolean;
  tenantId?: string;
  rules?: {
    type: string;
    value: any;
  }[];
  tags?: string[];
}

// Objection form values
export interface ObjectionFormValues {
  id?: string | number;
  name: string;
  category: string;
  description?: string;
  tags?: string[];
  isCommon?: boolean;
}

// Objection response form values
export interface ObjectionResponseFormValues {
  id?: string | number;
  objectionId: string | number;
  title: string;
  content: string;
  effectiveness?: number;
  tags?: string[];
  isApproved?: boolean;
}

// Proposal form values
export interface ProposalFormValues {
  id?: string | number;
  title: string;
  opportunityId?: string | number;
  contactId?: string | number;
  companyId?: string | number;
  content?: string;
  sections?: {
    title: string;
    content: string;
    order: number;
  }[];
  status?: string;
  expirationDate?: Date | string;
  tags?: string[];
  customFields?: Record<string, any>;
}
