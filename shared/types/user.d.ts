/**
 * User type definitions for the Aizako CRM project
 */

export interface User {
  id: string;
  email: string;
  username?: string;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  role?: 'admin' | 'user' | 'guest';
  tenantId?: string;
  status?: 'active' | 'inactive' | 'pending';
  preferences?: Record<string, any>;
  avatar?: string;
  createdAt: Date | string;
  updatedAt?: Date | string;
  lastLogin?: Date | string;
}

export interface UserSession {
  id: string;
  userId: string;
  tenantId?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date | string;
  expiresAt?: Date | string;
}

export interface UserPreferences {
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  timezone?: string;
  notifications?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
  };
  dashboardLayout?: Record<string, any>;
  [key: string]: any;
}
