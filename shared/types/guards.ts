/**
 * Type guards for the Aizako CRM project
 * These functions check if a value matches a specific type at runtime
 */

import { Contact, Company, Opportunity, Activity, User, Tenant } from './core';

/**
 * Type guard for User
 */
export function isUser(obj: any): obj is User {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id !== 'undefined' &&
    typeof obj.username === 'string' &&
    typeof obj.email === 'string'
  );
}

/**
 * Type guard for Contact
 */
export function isContact(obj: any): obj is Contact {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id !== 'undefined' &&
    typeof obj.firstName === 'string' &&
    typeof obj.lastName === 'string' &&
    typeof obj.status === 'string'
  );
}

/**
 * Type guard for Company
 */
export function isCompany(obj: any): obj is Company {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id !== 'undefined' &&
    typeof obj.name === 'string' &&
    typeof obj.status === 'string'
  );
}

/**
 * Type guard for Opportunity
 */
export function isOpportunity(obj: any): obj is Opportunity {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id !== 'undefined' &&
    typeof obj.name === 'string' &&
    typeof obj.value === 'number' &&
    typeof obj.stage === 'string'
  );
}

/**
 * Type guard for Activity
 */
export function isActivity(obj: any): obj is Activity {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id !== 'undefined' &&
    typeof obj.type === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.completed === 'boolean'
  );
}

/**
 * Type guard for Tenant
 */
export function isTenant(obj: any): obj is Tenant {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id !== 'undefined' &&
    typeof obj.name === 'string' &&
    typeof obj.slug === 'string' &&
    typeof obj.status === 'string'
  );
}

/**
 * Type guard for array of a specific type
 */
export function isArrayOf<T>(
  arr: any,
  guard: (item: any) => item is T
): arr is T[] {
  return Array.isArray(arr) && arr.every(item => guard(item));
}

/**
 * Type guard for Contact array
 */
export function isContactArray(arr: any): arr is Contact[] {
  return isArrayOf(arr, isContact);
}

/**
 * Type guard for Company array
 */
export function isCompanyArray(arr: any): arr is Company[] {
  return isArrayOf(arr, isCompany);
}

/**
 * Type guard for Opportunity array
 */
export function isOpportunityArray(arr: any): arr is Opportunity[] {
  return isArrayOf(arr, isOpportunity);
}

/**
 * Type guard for Activity array
 */
export function isActivityArray(arr: any): arr is Activity[] {
  return isArrayOf(arr, isActivity);
}

/**
 * Type guard for checking if an object has specific properties
 */
export function hasProperties<T extends object>(
  obj: any,
  properties: (keyof T)[]
): obj is T {
  return (
    obj &&
    typeof obj === 'object' &&
    properties.every(prop => prop in obj)
  );
}
