module.exports = {
  ci: {
    collect: {
      // URL patterns to test
      url: [
        'http://localhost:3000/',
        'http://localhost:3000/contacts',
        'http://localhost:3000/companies',
        'http://localhost:3000/opportunities',
        'http://localhost:3000/activities',
      ],
      // Number of runs per URL
      numberOfRuns: 3,
      // Use desktop configuration
      settings: {
        preset: 'desktop',
      },
      // Start server before collecting
      startServerCommand: 'npm run start:test',
      // Wait for server to be ready
      startServerReadyPattern: 'Server started on port 3000',
    },
    upload: {
      // Upload to temporary public storage
      target: 'temporary-public-storage',
    },
    assert: {
      // Performance score thresholds
      assertions: {
        'categories:performance': ['warn', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['warn', { minScore: 0.8 }],
        
        // Specific metrics
        'first-contentful-paint': ['warn', { maxNumericValue: 2000 }],
        'interactive': ['warn', { maxNumericValue: 3500 }],
        'max-potential-fid': ['warn', { maxNumericValue: 300 }],
        'cumulative-layout-shift': ['warn', { maxNumericValue: 0.1 }],
        'largest-contentful-paint': ['warn', { maxNumericValue: 2500 }],
        
        // Ensure important resources are preloaded
        'uses-rel-preload': 'off',
        // Ensure text remains visible during webfont load
        'font-display': 'error',
        // Ensure there's no render-blocking resources
        'render-blocking-resources': ['warn', { maxLength: 2 }],
      },
    },
  },
};
