import os
import tempfile
import base64
from typing import Optional, Dict, Any
import weasyprint
from datetime import datetime

class DocumentGenerator:
    """Utility class for document generation"""
    
    @staticmethod
    def html_to_pdf(html_content: str, filename: Optional[str] = None) -> bytes:
        """
        Convert HTML content to PDF
        
        Args:
            html_content: HTML content to convert
            filename: Optional filename for the PDF
            
        Returns:
            PDF content as bytes
        """
        try:
            # Generate PDF from HTML
            pdf_content = weasyprint.HTML(string=html_content).write_pdf()
            return pdf_content
        except Exception as e:
            print(f"Error converting HTML to PDF: {str(e)}")
            raise
    
    @staticmethod
    def create_meeting_prep_html(meeting_data: Dict[str, Any]) -> str:
        """
        Create HTML for meeting prep document
        
        Args:
            meeting_data: Meeting preparation data
            
        Returns:
            HTML content
        """
        # Format date for display
        def format_date(date_str):
            try:
                if isinstance(date_str, str):
                    date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                else:
                    date = date_str
                return date.strftime('%b %d, %Y')
            except:
                return date_str
        
        # Create HTML content
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{meeting_data.get('title', 'Meeting Preparation')}</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                h1 {{
                    color: #2563eb;
                    border-bottom: 1px solid #e5e7eb;
                    padding-bottom: 10px;
                }}
                h2 {{
                    color: #4b5563;
                    margin-top: 25px;
                    border-bottom: 1px solid #e5e7eb;
                    padding-bottom: 5px;
                }}
                .summary {{
                    background-color: #f3f4f6;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .event {{
                    margin-bottom: 10px;
                }}
                .event-date {{
                    font-weight: bold;
                    color: #6b7280;
                }}
                .event-type {{
                    color: #2563eb;
                    font-size: 0.9em;
                    text-transform: uppercase;
                }}
                .stakeholder {{
                    margin-bottom: 15px;
                }}
                .stakeholder-name {{
                    font-weight: bold;
                }}
                .stakeholder-role {{
                    color: #6b7280;
                    font-style: italic;
                }}
                .news-item {{
                    margin-bottom: 15px;
                }}
                .news-date {{
                    color: #6b7280;
                    font-size: 0.9em;
                }}
                .news-title {{
                    font-weight: bold;
                }}
                .news-source {{
                    color: #6b7280;
                    font-style: italic;
                }}
                .talking-point {{
                    margin-bottom: 20px;
                }}
                .talking-point-topic {{
                    font-weight: bold;
                    color: #2563eb;
                }}
                .priority {{
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 10px;
                    font-size: 0.8em;
                    margin-left: 10px;
                }}
                .priority-1 {{
                    background-color: #fee2e2;
                    color: #b91c1c;
                }}
                .priority-2 {{
                    background-color: #ffedd5;
                    color: #c2410c;
                }}
                .priority-3 {{
                    background-color: #fef3c7;
                    color: #92400e;
                }}
                .priority-4 {{
                    background-color: #ecfccb;
                    color: #4d7c0f;
                }}
                .priority-5 {{
                    background-color: #d1fae5;
                    color: #065f46;
                }}
                ul {{
                    padding-left: 20px;
                }}
                .next-steps {{
                    background-color: #dbeafe;
                    padding: 15px;
                    border-radius: 5px;
                }}
                .footer {{
                    margin-top: 40px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #9ca3af;
                }}
            </style>
        </head>
        <body>
            <h1>{meeting_data.get('title', 'Meeting Preparation')}</h1>
            
            <div class="summary">
                <p>{meeting_data.get('summary', 'No summary available.')}</p>
            </div>
        """
        
        # Account History
        html += """
            <h2>Account History</h2>
            <div>
        """
        
        account_history = meeting_data.get('accountHistory', {})
        key_events = account_history.get('keyEvents', [])
        
        for event in key_events:
            html += f"""
                <div class="event">
                    <span class="event-date">{format_date(event.get('date', ''))}</span> - 
                    <span class="event-type">{event.get('type', '')}</span>: 
                    {event.get('description', '')}
                </div>
            """
        
        html += f"""
                <p>{account_history.get('timeline', 'No timeline available.')}</p>
            </div>
        """
        
        # Stakeholders
        html += """
            <h2>Stakeholders</h2>
            <div>
        """
        
        stakeholders = meeting_data.get('stakeholders', [])
        
        for stakeholder in stakeholders:
            html += f"""
                <div class="stakeholder">
                    <div class="stakeholder-name">{stakeholder.get('name', '')}</div>
                    <div class="stakeholder-role">{stakeholder.get('role', '')} - {stakeholder.get('influence', '')}</div>
                    <p>{stakeholder.get('notes', '')}</p>
                </div>
            """
        
        html += """
            </div>
        """
        
        # Company News
        html += """
            <h2>Recent Company News</h2>
            <div>
        """
        
        company_news = meeting_data.get('companyNews', [])
        
        for news in company_news:
            html += f"""
                <div class="news-item">
                    <div class="news-date">{format_date(news.get('date', ''))}</div>
                    <div class="news-title">{news.get('title', '')}</div>
                    <div class="news-source">{news.get('source', '')}</div>
                    <p>{news.get('summary', '')}</p>
                </div>
            """
        
        html += """
            </div>
        """
        
        # Talking Points
        html += """
            <h2>Talking Points</h2>
            <div>
        """
        
        talking_points = meeting_data.get('talkingPoints', [])
        
        for point in talking_points:
            html += f"""
                <div class="talking-point">
                    <div>
                        <span class="talking-point-topic">{point.get('topic', '')}</span>
                        <span class="priority priority-{point.get('priority', 3)}">Priority {point.get('priority', 3)}</span>
                    </div>
                    <ul>
            """
            
            for p in point.get('points', []):
                html += f"""
                        <li>{p}</li>
                """
            
            html += """
                    </ul>
                </div>
            """
        
        html += """
            </div>
        """
        
        # Next Steps
        html += """
            <h2>Next Steps</h2>
            <div class="next-steps">
                <ul>
        """
        
        next_steps = meeting_data.get('nextSteps', [])
        
        for step in next_steps:
            html += f"""
                    <li>{step}</li>
            """
        
        html += """
                </ul>
            </div>
        """
        
        # Footer
        html += f"""
            <div class="footer">
                <p>Generated by Aizako CRM on {format_date(datetime.now())}</p>
            </div>
        </body>
        </html>
        """
        
        return html

# Create a singleton instance
document_generator = DocumentGenerator()
