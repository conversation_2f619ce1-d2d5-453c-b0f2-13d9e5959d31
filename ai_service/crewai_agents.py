from crewai import Agent, Task, Crew, Process
from langchain_openai import ChatOpenAI
from typing import List, Dict, Any, Optional
import os

class CRMAgentFactory:
    """
    Factory for creating specialized CRM agents
    """
    
    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        
    def get_llm(self):
        """Get the language model"""
        if not self.api_key:
            raise ValueError("OpenAI API key not set")
            
        return ChatOpenAI(
            model="gpt-4o",  # the newest OpenAI model is "gpt-4o" which was released May 13, 2024
            temperature=0.7,
            api_key=self.api_key
        )
    
    def create_lead_qualification_agent(self) -> Agent:
        """Create an agent specialized in lead qualification"""
        return Agent(
            role='Lead Qualification Specialist',
            goal='Accurately qualify leads based on industry standards and company criteria',
            backstory='''You are an expert in lead qualification with 15 years of experience 
            in B2B sales. You've worked with companies of all sizes across Africa and understand
            the unique challenges of the African market. You excel at identifying high-potential
            opportunities based on limited data.''',
            verbose=True,
            allow_delegation=True,
            llm=self.get_llm()
        )
    
    def create_opportunity_analysis_agent(self) -> Agent:
        """Create an agent specialized in opportunity analysis"""
        return Agent(
            role='Opportunity Analysis Expert',
            goal='Provide in-depth analysis of sales opportunities and recommend optimal strategies',
            backstory='''You are a seasoned sales strategist who has helped close over $500M 
            in deals throughout your career. You have deep knowledge of sales methodologies
            and can quickly identify the strengths and weaknesses of a potential deal.
            You understand the business landscape in various African regions.''',
            verbose=True,
            allow_delegation=True,
            llm=self.get_llm()
        )
    
    def create_customer_relationship_agent(self) -> Agent:
        """Create an agent specialized in customer relationship management"""
        return Agent(
            role='Relationship Management Specialist',
            goal='Strengthen customer relationships and identify growth opportunities',
            backstory='''You are an expert in customer success and relationship building.
            You've managed portfolios of enterprise clients and know how to maintain
            long-term business relationships. You understand the cultural nuances of
            doing business in different African countries and have strategies for
            maintaining strong relationships despite geographic challenges.''',
            verbose=True,
            allow_delegation=True,
            llm=self.get_llm()
        )
    
    def create_market_intelligence_agent(self) -> Agent:
        """Create an agent specialized in market intelligence"""
        return Agent(
            role='Market Intelligence Analyst',
            goal='Provide relevant market insights to support sales and strategy',
            backstory='''You are a market research expert with deep knowledge of 
            industry trends, competitive landscapes, and market dynamics in Africa.
            You know how to gather and analyze information to provide actionable
            intelligence that drives business decisions.''',
            verbose=True,
            allow_delegation=True,
            llm=self.get_llm()
        )
    
    def create_data_quality_agent(self) -> Agent:
        """Create an agent specialized in CRM data quality"""
        return Agent(
            role='Data Quality Specialist',
            goal='Ensure CRM data is accurate, complete, and actionable',
            backstory='''You are a meticulous data quality expert who understands
            the importance of clean data in a CRM system. You know how to identify
            data issues, recommend corrections, and establish processes to maintain
            high-quality information. You have experience with African business contexts
            where data collection can be challenging.''',
            verbose=True,
            allow_delegation=True,
            llm=self.get_llm()
        )


class CRMCrewFactory:
    """
    Factory for creating specialized CRM agent crews for different tasks
    """
    
    def __init__(self):
        self.agent_factory = CRMAgentFactory()
    
    def create_lead_processing_crew(self) -> Crew:
        """
        Create a crew specialized in processing and qualifying leads
        """
        # Create agents
        qualification_agent = self.agent_factory.create_lead_qualification_agent()
        market_agent = self.agent_factory.create_market_intelligence_agent()
        data_agent = self.agent_factory.create_data_quality_agent()
        
        # Define tasks
        qualification_task = Task(
            description='''Analyze the lead information and determine if they meet 
            our qualification criteria. Consider their budget, authority, need, and 
            timeline. Provide a detailed qualification assessment.''',
            expected_output="Lead qualification report with detailed justification",
            agent=qualification_agent
        )
        
        market_task = Task(
            description='''Research the lead's industry and provide relevant market 
            insights that might impact their buying decision. Identify any trends,
            challenges, or opportunities specific to their market in Africa.''',
            expected_output="Market intelligence report specific to the lead's industry and region",
            agent=market_agent
        )
        
        data_task = Task(
            description='''Review the lead data for completeness and accuracy. 
            Identify any missing or potentially inaccurate information. Recommend
            additional data points that should be collected.''',
            expected_output="Data quality assessment with recommendations",
            agent=data_agent
        )
        
        # Create and return crew
        return Crew(
            agents=[qualification_agent, market_agent, data_agent],
            tasks=[qualification_task, market_task, data_task],
            verbose=True,
            process=Process.sequential,
            name="Lead Processing Crew"
        )
    
    def create_opportunity_analysis_crew(self) -> Crew:
        """
        Create a crew specialized in analyzing sales opportunities
        """
        # Create agents
        opportunity_agent = self.agent_factory.create_opportunity_analysis_agent()
        relationship_agent = self.agent_factory.create_customer_relationship_agent()
        market_agent = self.agent_factory.create_market_intelligence_agent()
        
        # Define tasks
        opportunity_task = Task(
            description='''Analyze this sales opportunity in detail. Evaluate its 
            strengths, weaknesses, potential value, and probability of closing.
            Consider the specific challenges of this deal in the African market context.''',
            expected_output="Comprehensive opportunity analysis with risk assessment",
            agent=opportunity_agent
        )
        
        relationship_task = Task(
            description='''Assess the current relationship with this prospect/customer.
            Identify key stakeholders, relationship history, and recommendations for
            strengthening the relationship. Consider cultural and regional factors.''',
            expected_output="Relationship assessment with actionable recommendations",
            agent=relationship_agent
        )
        
        competitive_task = Task(
            description='''Analyze the competitive landscape for this opportunity.
            Identify likely competitors, our relative strengths and weaknesses,
            and strategies to position ourselves favorably. Consider local competitors
            in the relevant African markets.''',
            expected_output="Competitive analysis with positioning recommendations",
            agent=market_agent
        )
        
        # Create and return crew
        return Crew(
            agents=[opportunity_agent, relationship_agent, market_agent],
            tasks=[opportunity_task, relationship_task, competitive_task],
            verbose=True,
            process=Process.sequential,
            name="Opportunity Analysis Crew"
        )
    
    def create_customer_success_crew(self) -> Crew:
        """
        Create a crew specialized in customer success and relationship management
        """
        # Create agents
        relationship_agent = self.agent_factory.create_customer_relationship_agent()
        opportunity_agent = self.agent_factory.create_opportunity_analysis_agent()
        data_agent = self.agent_factory.create_data_quality_agent()
        
        # Define tasks
        relationship_task = Task(
            description='''Analyze the current state of this customer relationship.
            Identify potential issues, opportunities for growth, and strategies to
            improve satisfaction and loyalty. Consider the specific requirements of
            doing business in the customer's region of Africa.''',
            expected_output="Relationship health assessment with improvement plan",
            agent=relationship_agent
        )
        
        growth_task = Task(
            description='''Identify potential expansion opportunities with this customer.
            Analyze their current usage, unmet needs, and potential for cross-selling
            or upselling. Consider regional growth opportunities.''',
            expected_output="Growth opportunity analysis with specific recommendations",
            agent=opportunity_agent
        )
        
        data_task = Task(
            description='''Review the customer data for completeness and accuracy.
            Identify information gaps that might hinder relationship management.
            Suggest additional data points to collect and track.''',
            expected_output="Customer data assessment and enrichment plan",
            agent=data_agent
        )
        
        # Create and return crew
        return Crew(
            agents=[relationship_agent, opportunity_agent, data_agent],
            tasks=[relationship_task, growth_task, data_task],
            verbose=True,
            process=Process.sequential,
            name="Customer Success Crew"
        )


# Crew selector function
def select_crew_for_task(task_description: str, crm_data: Optional[Dict[str, Any]] = None) -> Crew:
    """
    Select the appropriate crew based on the task description
    
    Args:
        task_description: Description of the task to be performed
        crm_data: Optional CRM data relevant to the task
        
    Returns:
        The appropriate crew for the task
    """
    factory = CRMCrewFactory()
    
    # Determine which crew to use based on the task description
    task_lower = task_description.lower()
    
    if any(keyword in task_lower for keyword in ["lead", "prospect", "qualify", "qualification"]):
        return factory.create_lead_processing_crew()
    
    elif any(keyword in task_lower for keyword in ["opportunity", "deal", "sales", "pipeline"]):
        return factory.create_opportunity_analysis_crew()
    
    elif any(keyword in task_lower for keyword in ["customer", "relationship", "account", "satisfaction"]):
        return factory.create_customer_success_crew()
    
    # Default to opportunity analysis crew if no match
    return factory.create_opportunity_analysis_crew()