import os
import json
import datetime
import openai
from typing import List, Dict, Any, Optional, Union, Tuple

class CRMDocument:
    """Class representing a CRM document with metadata"""
    def __init__(
        self, 
        text: str, 
        doc_id: str,
        doc_type: str,
        title: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.text = text
        self.doc_id = doc_id
        self.doc_type = doc_type
        self.title = title or f"Document {doc_id}"
        self.metadata = metadata or {}
        self.created_at = datetime.datetime.now().isoformat()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CRMDocument':
        """Create a CRMDocument from a dictionary"""
        return cls(
            text=data["text"],
            doc_id=data["doc_id"],
            doc_type=data["doc_type"],
            title=data.get("title"),
            metadata={k: v for k, v in data.items() 
                     if k not in ["text", "doc_id", "doc_type", "title"]}
        )

class LlamaIndexService:
    """A simplified CRM document management service with OpenAI integration"""
    
    def __init__(self, documents_dir: str = "data"):
        """
        Initialize the service with a directory containing documents
        
        Args:
            documents_dir: Path to the directory containing documents
        """
        self.documents_dir = documents_dir
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.documents_cache = {}  # Cache of documents by ID
        self.client = None
        
        # Initialize if API key is available
        if self.api_key:
            self._initialize()
            self._create_sample_crm_documents()
    
    def _initialize(self):
        """Initialize the OpenAI client"""
        try:
            # Initialize OpenAI client
            openai.api_key = self.api_key
            self.client = openai.OpenAI(api_key=self.api_key)
            
            
            # Initialize embedding model
            embed_model = OpenAIEmbedding(
                model="text-embedding-3-small",
                api_key=self.api_key
            )
            
            # Set up global settings
            Settings.llm = self.llm
            Settings.embed_model = embed_model
            
            # Create node parser for chunking
            node_parser = SentenceSplitter(
                chunk_size=512,
                chunk_overlap=50
            )
            
            # Create service context
            self.service_context = ServiceContext.from_defaults(
                llm=self.llm,
                embed_model=embed_model,
                node_parser=node_parser
            )
            
            # Ensure directories exist
            os.makedirs(self.documents_dir, exist_ok=True)
            os.makedirs(self.index_dir, exist_ok=True)
            
            # Create extractors for advanced document processing
            extractors = [
                TitleExtractor(nodes=5),
                SummaryExtractor(summaries=["self"]),
                QuestionsAnsweredExtractor(questions=3)
            ]
            
            # Create ingestion pipeline
            self.ingestion_pipeline = IngestionPipeline(
                transformations=[
                    node_parser,
                    *extractors
                ]
            )
            
            # Load or create index
            self._load_or_create_index()
            
            # Create CRM sample docs if no documents exist
            if len(os.listdir(self.documents_dir)) <= 1:  # Only index dir exists
                self._create_sample_crm_documents()
            
        except Exception as e:
            print(f"Error initializing LlamaIndex: {str(e)}")
    
    def _load_or_create_index(self):
        """Load existing index or create a new one"""
        try:
            # Find all document files (not in index dir)
            doc_files = []
            for root, dirs, files in os.walk(self.documents_dir):
                if os.path.basename(root) != os.path.basename(self.index_dir):
                    for file in files:
                        if file.endswith('.txt') or file.endswith('.json'):
                            doc_files.append(os.path.join(root, file))
            
            llama_docs = []
            
            # Load documents from files
            for file_path in doc_files:
                if file_path.endswith('.json'):
                    # Load JSON document with metadata
                    try:
                        with open(file_path, 'r') as f:
                            doc_data = json.load(f)
                            crm_doc = CRMDocument.from_dict(doc_data)
                            llama_docs.append(crm_doc.to_llama_document())
                            self.documents_cache[crm_doc.doc_id] = crm_doc
                    except Exception as e:
                        print(f"Error loading JSON document {file_path}: {e}")
                else:
                    # Load text document
                    try:
                        with open(file_path, 'r') as f:
                            text = f.read()
                            doc_id = os.path.basename(file_path).replace('.txt', '')
                            # Simple document with default metadata
                            doc = Document(text=text, metadata={
                                "doc_id": doc_id,
                                "doc_type": "text",
                                "source": file_path
                            })
                            llama_docs.append(doc)
                    except Exception as e:
                        print(f"Error loading text document {file_path}: {e}")
            
            # Process documents through the ingestion pipeline
            if llama_docs:
                nodes = self.ingestion_pipeline.run(documents=llama_docs)
                
                # Create the vector index
                self.index = VectorStoreIndex(
                    nodes,
                    service_context=self.service_context
                )
                
                print(f"LlamaIndex initialized with {len(llama_docs)} documents, " + 
                      f"created {len(nodes)} nodes")
            else:
                print("No documents found, initializing empty index")
                self.index = VectorStoreIndex([], service_context=self.service_context)
                
        except Exception as e:
            print(f"Error creating index: {str(e)}")
            # Initialize an empty index as fallback
            self.index = VectorStoreIndex([], service_context=self.service_context)
    
    def _create_sample_crm_documents(self):
        """Create sample CRM documents for testing"""
        # Sample CRM documents
        crm_samples = [
            {
                "doc_id": "sales_process_guide",
                "doc_type": "guide",
                "title": "Effective B2B Sales Process Guide",
                "text": """# Effective B2B Sales Process Guide

## Introduction
This guide outlines the best practices for B2B sales in the African market.

## Key Stages
1. **Lead Qualification**: Assess potential fit using BANT criteria.
2. **Discovery**: Understand the client's business challenges and goals.
3. **Solution Presentation**: Present tailored solutions to address specific needs.
4. **Negotiation**: Discuss terms, pricing, and implementation details.
5. **Closing**: Finalize the agreement and establish next steps.

## African Market Considerations
- Build relationships before discussing business
- Understand local regulatory environments
- Consider mobile-first communication strategies
- Plan for longer sales cycles in enterprise deals
- Leverage local partnerships when possible

## Follow-up Best Practices
- Send meeting summaries within 24 hours
- Provide additional resources relevant to discussed challenges
- Schedule regular check-ins during the consideration phase
- Involve technical teams early for complex solutions""",
                "industry": "general",
                "audience": "sales_team",
                "importance": "high"
            },
            {
                "doc_id": "opportunity_scoring",
                "doc_type": "methodology",
                "title": "Opportunity Scoring Framework",
                "text": """# Opportunity Scoring Framework

## Purpose
This framework helps sales teams prioritize opportunities based on potential value and close probability.

## Scoring Criteria
Each opportunity should be scored from 1-10 on these dimensions:

### Budget Alignment (25%)
- Does the client have sufficient budget allocated?
- Is the decision maker involved in budget discussions?
- How stable is their financial situation?

### Need Validation (25%)
- How urgent is the client's problem?
- What measurable impact will our solution provide?
- Does our solution align with their strategic goals?

### Authority Mapping (20%)
- Are we engaged with the key decision makers?
- How many stakeholders need to approve?
- What level of executive sponsorship exists?

### Timeline Clarity (15%)
- Is there a clear implementation timeline?
- Are there external deadlines driving the purchase?
- What is the maximum acceptable timeframe for ROI?

### Competitive Position (15%)
- How does our solution compare to alternatives?
- What unique value do we provide?
- What existing relationships do we leverage?

## Calculation
Final score = Sum of (Category Score × Category Weight)

## Recommended Actions
- Score 8-10: High priority, daily engagement
- Score 6-8: Medium priority, weekly engagement
- Score 4-6: Low priority, maintain monthly contact
- Score <4: Consider disqualifying or long-term nurture""",
                "industry": "general",
                "audience": "sales_managers",
                "importance": "high"
            },
            {
                "doc_id": "telecom_industry_insights",
                "doc_type": "market_research",
                "title": "African Telecom Industry Insights 2023",
                "text": """# African Telecom Industry Insights 2023

## Market Overview
The African telecommunications market continues to expand rapidly with mobile penetration reaching 80% across the continent.

## Key Trends
1. **5G Deployment**: South Africa, Nigeria, and Kenya lead in infrastructure development
2. **Mobile Money Growth**: 57% increase in transaction volume since 2020
3. **Rural Connectivity**: Major investment in last-mile solutions
4. **Data Usage**: 38% annual growth in mobile data consumption
5. **Regulatory Changes**: New regional frameworks for cross-border services

## Challenges
- Infrastructure cost and maintenance
- Regulatory fragmentation across markets
- Talent retention in technical roles
- Energy reliability for network operations
- Cybersecurity concerns

## Opportunities
- Enterprise cloud services growing at 29% annually
- IoT applications in agriculture and logistics
- Fintech partnerships for expanded service offerings
- Digital identity solutions
- Edge computing infrastructure

## Key Players
- MTN Group (Pan-African)
- Vodacom/Safaricom (East & Southern Africa)
- Orange (West & Central Africa)
- Airtel Africa (Pan-African)
- Telkom (South Africa)

## Recommendation
Focus on integrated solutions combining connectivity, cloud services, and specialized industry applications.""",
                "industry": "telecom",
                "region": "africa",
                "audience": "sales_team",
                "importance": "medium"
            }
        ]
        
        # Save documents
        for sample in crm_samples:
            doc_id = sample["doc_id"]
            doc_type = sample["doc_type"]
            text = sample["text"]
            title = sample["title"]
            
            # Create metadata by removing text and other specific fields
            metadata = {k: v for k, v in sample.items() 
                       if k not in ["doc_id", "doc_type", "text", "title"]}
            
            # Create and save document
            self.add_crm_document(
                text=text,
                doc_id=doc_id,
                doc_type=doc_type,
                title=title,
                metadata=metadata
            )
    
    def is_initialized(self) -> bool:
        """Check if the service is properly initialized"""
        return self.index is not None and self.llm is not None
    
    def query(self, query_text: str, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Query the index with the given text and optional filters
        
        Args:
            query_text: The query text
            filters: Optional metadata filters, e.g. {"doc_type": "guide", "industry": "telecom"}
            
        Returns:
            Dict containing response and metadata
        """
        if not self.is_initialized():
            return {
                "response": "LlamaIndex service is not initialized. Please check your OpenAI API key.",
                "source_documents": [],
                "success": False
            }
        
        try:
            # Apply metadata filters if provided
            retriever = self.index.as_retriever(similarity_top_k=3)
            
            if filters:
                metadata_filters = []
                for key, value in filters.items():
                    metadata_filters.append(
                        MetadataFilter(key=key, value=value, operator=FilterOperator.EQ)
                    )
                
                # Apply metadata filter to retriever
                retriever = self.index.as_retriever(
                    similarity_top_k=3,
                    filters=MetadataFilters(
                        filters=metadata_filters,
                        condition=FilterCondition.AND
                    )
                )
            
            # Create query engine with retriever and postprocessing
            query_engine = RetrieverQueryEngine.from_args(
                retriever=retriever,
                service_context=self.service_context,
                node_postprocessors=[
                    SimilarityPostprocessor(similarity_cutoff=0.7)
                ]
            )
            
            # Execute query
            response = query_engine.query(query_text)
            
            # Extract source documents
            source_docs = []
            if hasattr(response, 'source_nodes'):
                for node in response.source_nodes:
                    source_docs.append({
                        "text": node.node.text,
                        "score": float(node.score) if hasattr(node, 'score') else None,
                        "document_id": node.node.metadata.get("doc_id"),
                        "document_type": node.node.metadata.get("doc_type"),
                        "title": node.node.metadata.get("title", "Untitled"),
                        "metadata": {k: v for k, v in node.node.metadata.items() 
                                  if k not in ["doc_id", "doc_type", "title"]}
                    })
            
            return {
                "response": str(response),
                "source_documents": source_docs,
                "success": True
            }
            
        except Exception as e:
            print(f"Error querying LlamaIndex: {str(e)}")
            return {
                "response": f"Error: {str(e)}",
                "source_documents": [],
                "success": False
            }
    
    def add_document(self, document_text: str, document_id: str = None, metadata: Dict[str, Any] = None) -> bool:
        """
        Add a simple document to the index
        
        Args:
            document_text: The text content of the document
            document_id: Optional document ID
            metadata: Optional metadata
            
        Returns:
            True if successful, False otherwise
        """
        return self.add_crm_document(
            text=document_text, 
            doc_id=document_id, 
            doc_type="text",
            metadata=metadata
        )
    
    def add_crm_document(
        self, 
        text: str, 
        doc_id: Optional[str] = None, 
        doc_type: str = "generic", 
        title: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Add a CRM document with rich metadata
        
        Args:
            text: Document text content
            doc_id: Unique document ID
            doc_type: Type of document (guide, case_study, market_research, etc.)
            title: Document title
            metadata: Additional metadata fields
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_initialized():
            return False
        
        try:
            # Generate doc_id if not provided
            if not doc_id:
                doc_id = f"doc_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # Create CRM document
            crm_doc = CRMDocument(
                text=text,
                doc_id=doc_id,
                doc_type=doc_type,
                title=title,
                metadata=metadata
            )
            
            # Save document to file
            file_path = os.path.join(self.documents_dir, f"{doc_id}.json")
            with open(file_path, 'w') as f:
                # Convert to dictionary for JSON serialization
                doc_dict = {
                    "text": crm_doc.text,
                    "doc_id": crm_doc.doc_id,
                    "doc_type": crm_doc.doc_type,
                    "title": crm_doc.title,
                    "created_at": crm_doc.created_at,
                    **crm_doc.metadata
                }
                json.dump(doc_dict, f, indent=2)
            
            # Convert to LlamaIndex document
            llama_doc = crm_doc.to_llama_document()
            
            # Process through ingestion pipeline
            nodes = self.ingestion_pipeline.run(documents=[llama_doc])
            
            # Add to index
            self.index.insert_nodes(nodes)
            
            # Add to cache
            self.documents_cache[doc_id] = crm_doc
            
            print(f"Added document {doc_id} with {len(nodes)} nodes")
            return True
            
        except Exception as e:
            print(f"Error adding document: {str(e)}")
            return False
    
    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a document by ID
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document data or None if not found
        """
        # Check cache first
        if doc_id in self.documents_cache:
            crm_doc = self.documents_cache[doc_id]
            return {
                "text": crm_doc.text,
                "doc_id": crm_doc.doc_id,
                "doc_type": crm_doc.doc_type,
                "title": crm_doc.title,
                "metadata": crm_doc.metadata
            }
        
        # Check file system
        file_path = os.path.join(self.documents_dir, f"{doc_id}.json")
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    # Create CRM document and add to cache
                    crm_doc = CRMDocument.from_dict(data)
                    self.documents_cache[doc_id] = crm_doc
                    return {
                        "text": crm_doc.text,
                        "doc_id": crm_doc.doc_id,
                        "doc_type": crm_doc.doc_type,
                        "title": crm_doc.title,
                        "metadata": crm_doc.metadata
                    }
            except Exception as e:
                print(f"Error reading document {doc_id}: {str(e)}")
        
        return None
    
    def list_documents(self, doc_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all documents, optionally filtered by type
        
        Args:
            doc_type: Optional document type filter
            
        Returns:
            List of document summaries
        """
        results = []
        
        # Scan the documents directory
        for file_name in os.listdir(self.documents_dir):
            if file_name.endswith('.json'):
                try:
                    file_path = os.path.join(self.documents_dir, file_name)
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        
                        # Apply doc_type filter if specified
                        if doc_type and data.get("doc_type") != doc_type:
                            continue
                            
                        # Add document summary
                        results.append({
                            "doc_id": data.get("doc_id"),
                            "title": data.get("title", "Untitled"),
                            "doc_type": data.get("doc_type", "unknown"),
                            "created_at": data.get("created_at"),
                            # Include key metadata but not the full text
                            "metadata": {k: v for k, v in data.items() 
                                       if k not in ["text", "doc_id", "title", "doc_type", "created_at"]}
                        })
                except Exception as e:
                    print(f"Error reading document {file_name}: {str(e)}")
        
        return results
    
    def delete_document(self, doc_id: str) -> bool:
        """
        Delete a document from the index
        
        Args:
            doc_id: Document ID
            
        Returns:
            True if successful, False otherwise
        """
        file_path = os.path.join(self.documents_dir, f"{doc_id}.json")
        if os.path.exists(file_path):
            try:
                # Remove the file
                os.remove(file_path)
                
                # Remove from cache
                if doc_id in self.documents_cache:
                    del self.documents_cache[doc_id]
                
                # Rebuild the index (simpler approach)
                self._load_or_create_index()
                
                return True
            except Exception as e:
                print(f"Error deleting document {doc_id}: {str(e)}")
        
        return False


# Global instance for use in the FastAPI app
llama_index_service = LlamaIndexService()