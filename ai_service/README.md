# Aizako CRM AI Service

This is the Python-based AI microservice component of the Aizako CRM system. It provides advanced AI capabilities through CrewAI agents and LlamaIndex for Retrieval Augmented Generation (RAG).

## Architecture

This service implements the AI Layer component from the Aizako V2.0 Hybrid Architecture:

- **CrewAI agents** for complex workflows and specialized tasks
- **LlamaIndex** for ERP data retrieval and augmentation
- **FastAPI** for API endpoints
- **Protobuf-based RPC** for communication with the Node.js backend
- **Redis streams** for inter-process communication

## System Requirements

- Modern web browser (Chrome, Firefox, Safari, or Edge)
- Internet connection
- Aizako CRM account

## Service Access

The AI service is a fully managed component of the Aizako CRM cloud platform. There is no need to set up or deploy this service yourself. It is automatically provisioned and maintained as part of your Aizako CRM subscription.

## API Endpoints

- **GET /status**: Check the status of the AI service
- **POST /general**: Process a general AI query
- **POST /crew**: Execute a specialized task using CrewAI agents

## Cloud Infrastructure

The AI service runs on our secure, scalable cloud infrastructure with automatic scaling and high availability. This ensures optimal performance for all users without any deployment or maintenance requirements.

## Integration with Node.js

This service is designed to be called from the Node.js backend through the AI Bridge component. It provides a fallback mechanism when the service is unavailable.

## Africa-Optimized Infrastructure

For optimal performance in African markets, our cloud infrastructure includes:
- Strategic data centers in Johannesburg and Lagos regions
- CBOR compression for 83% smaller payloads in low-bandwidth areas
- Intelligent caching to reduce latency and API calls