import os
import json
from typing import Dict, Any, List, Optional, Union
from fastapi import FastAPI, HTTPException, Request, Body, Query, Path, Depends, Header
from fastapi.responses import Response
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from crewai.tasks import TaskOutput
from langchain_openai import ChatOpenAI
import uvicorn
import redis
from contextlib import asynccontextmanager
import logging

# Import our workflow and insights agents
from langraph_agents.workflow_agent import process_workflow_request
from langraph_agents.insights_agent import process_insights_request

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our document service
from document_service import document_service, CRMDocument
from subscription_client import SubscriptionClient
from document_utils import document_generator

# Import langraph agents
try:
    from langraph_agents.supervisor import process_user_input as langraph_process_user_input
    langraph_available = True
    logger.info("Langraph agents loaded successfully")
except ImportError as e:
    langraph_available = False
    logger.warning(f"Langraph agents not available: {str(e)}")

# Load environment variables
load_dotenv()

# Check for OpenAI API Key
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    logger.warning("OPENAI_API_KEY not found in environment variables")

# Initialize Redis if available
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
API_URL = os.getenv("API_URL", "http://localhost:5000")

try:
    redis_client = redis.from_url(REDIS_URL)
    redis_available = True
    logger.info(f"Connected to Redis at {REDIS_URL}")
except Exception as e:
    redis_available = False
    logger.warning(f"Could not connect to Redis at {REDIS_URL}, some features will be limited: {str(e)}")

# Initialize subscription client
subscription_client = SubscriptionClient(API_URL)

# Dependency for checking feature entitlement
async def check_feature_entitlement(
    feature_key: str,
    tenant_id: str = Header(None, alias="X-Tenant-ID"),
    api_key: str = Header(None, alias="X-API-Key")
):
    if not tenant_id:
        raise HTTPException(status_code=401, detail="Tenant ID is required")

    # Check if tenant is entitled to use this feature
    if not subscription_client.check_entitlement(tenant_id, feature_key):
        raise HTTPException(
            status_code=403,
            detail=f"Feature '{feature_key}' is not available in your subscription plan"
        )

    # Record feature usage
    subscription_client.record_feature_usage(tenant_id, feature_key)

    return tenant_id

# Define request models
class GeneralQueryRequest(BaseModel):
    query: str
    userId: Optional[int] = None
    context: Optional[Dict[str, Any]] = None

class CrewTaskRequest(BaseModel):
    task: str
    userId: Optional[int] = None
    context: Optional[Dict[str, Any]] = None

class LangGraphRequest(BaseModel):
    input: str
    userId: Optional[str] = None
    tenantId: Optional[str] = None
    conversation_history: Optional[List[Dict[str, Any]]] = None
    context: Optional[Dict[str, Any]] = None

class DocumentRequest(BaseModel):
    text: str
    doc_id: Optional[str] = None
    doc_type: str = "generic"
    title: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class DocumentFilterRequest(BaseModel):
    filters: Dict[str, Any]

class DocumentQueryRequest(BaseModel):
    query: str
    filters: Optional[Dict[str, Any]] = None

class HtmlToPdfRequest(BaseModel):
    html: str
    filename: Optional[str] = None

# Workflow and Insights models
class WorkflowParseRequest(BaseModel):
    prompt: str
    tenant_id: str
    user_id: str

class WorkflowParseResponse(BaseModel):
    success: bool
    dsl_yaml: Optional[str] = None
    nodes: Optional[List[Dict[str, Any]]] = None
    edges: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None

class InsightsRequest(BaseModel):
    question: str
    tenant_id: str
    user_id: str
    refresh: Optional[bool] = False
    filters: Optional[Dict[str, Any]] = None

class InsightsResponse(BaseModel):
    success: bool
    narrative: Optional[str] = None
    chart_spec: Optional[Dict[str, Any]] = None
    why_it_matters: Optional[List[str]] = None
    recommended_plays: Optional[List[Dict[str, Any]]] = None
    sql_query: Optional[str] = None
    graph_query: Optional[str] = None
    feature_weights: Optional[Dict[str, float]] = None
    dataset_ref: Optional[str] = None
    error: Optional[str] = None

# Define response models
class AIResponse(BaseModel):
    response: str
    source: str = "ai_service"
    source_documents: Optional[List[Dict[str, Any]]] = None

class DocumentResponse(BaseModel):
    doc_id: str
    title: str
    doc_type: str
    success: bool
    message: Optional[str] = None

class DocumentListResponse(BaseModel):
    documents: List[Dict[str, Any]]
    count: int

class StatusResponse(BaseModel):
    status: str
    openai_available: bool
    redis_available: bool
    llama_index_available: bool
    langraph_available: bool
    document_count: int
    version: str

# Initialize language model
def get_llm():
    if OPENAI_API_KEY:
        return ChatOpenAI(
            model="gpt-4o",  # the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
            temperature=0.7,
            api_key=OPENAI_API_KEY
        )
    else:
        # Return a dummy LLM that will be caught and handled
        class DummyLLM:
            def invoke(self, prompt):
                raise Exception("OpenAI API key not configured")
        return DummyLLM()

# Define agents
def create_research_agent():
    return Agent(
        role='Market Research Analyst',
        goal='Analyze market trends and competitive landscapes',
        backstory='You are an expert in market research with 20 years of experience in B2B SaaS.',
        verbose=True,
        allow_delegation=True,
        llm=get_llm()
    )

def create_sales_agent():
    return Agent(
        role='Sales Strategy Expert',
        goal='Identify sales opportunities and develop strategies',
        backstory='You have a long history of exceeding sales targets in enterprise software.',
        verbose=True,
        allow_delegation=True,
        llm=get_llm()
    )

def create_crm_expert_agent():
    return Agent(
        role='CRM Systems Expert',
        goal='Optimize CRM usage and customer relationship processes',
        backstory='You are an expert in CRM systems and best practices for managing customer data.',
        verbose=True,
        allow_delegation=True,
        llm=get_llm()
    )

# Handle general queries
async def process_general_query(query: str, userId: Optional[int] = None, context: Optional[Dict[str, Any]] = None) -> AIResponse:
    """Process general queries using LlamaIndex if available, or fall back to direct LLM"""
    try:
        # Store the interaction in Redis if available
        if redis_available and userId:
            redis_client.lpush(f"user:{userId}:queries", json.dumps({
                "query": query,
                "timestamp": import_datetime().datetime.now().isoformat()
            }))

        # Try using document service first if available and initialized
        if document_service.is_initialized():
            # Extract filters from context if provided
            filters = context.get("filters") if context else None

            # Get response from document service
            result = document_service.query(query, filters)

            if result["success"]:
                return AIResponse(
                    response=result["response"],
                    source="document_service",
                    source_documents=result["source_documents"]
                )

        # Fallback to direct LLM if document service is not available or query failed
        llm = get_llm()
        response = llm.invoke(query)

        return AIResponse(
            response=response.content,
            source="openai_direct"
        )
    except Exception as e:
        print(f"Error processing query: {str(e)}")
        return AIResponse(
            response=f"Failed to process query: {str(e)}",
            source="error"
        )

# Handle CrewAI tasks
async def process_crew_task(task_request: CrewTaskRequest) -> AIResponse:
    try:
        # Create agents
        research_agent = create_research_agent()
        sales_agent = create_sales_agent()
        crm_expert = create_crm_expert_agent()

        # Define task with context if available
        task_desc = task_request.task
        if task_request.context:
            # Add context to task description
            context_str = "\n\nContext: " + json.dumps(task_request.context)
            task_desc += context_str

        # Define task
        task = Task(
            description=task_desc,
            expected_output="Detailed analysis with actionable insights",
            agent=crm_expert
        )

        # Create crew
        crew = Crew(
            agents=[research_agent, sales_agent, crm_expert],
            tasks=[task],
            verbose=True,
            process=Process.sequential
        )

        # Execute task
        result = crew.kickoff()

        # Store the result in Redis if available
        if redis_available and task_request.userId:
            redis_client.lpush(f"user:{task_request.userId}:crew_tasks", json.dumps({
                "task": task_request.task,
                "timestamp": import_datetime().datetime.now().isoformat(),
                "result_summary": result[:100] + "..." if len(result) > 100 else result
            }))

        # If we have document service available, store the task result as a document
        if document_service.is_initialized():
            # Create a unique ID for the document
            task_doc_id = f"crew_task_{import_datetime().datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Add document with task and result
            document_service.add_crm_document(
                text=f"# Task: {task_request.task}\n\n## Result\n\n{result}",
                doc_id=task_doc_id,
                doc_type="crew_task_result",
                title=f"CrewAI Task: {task_request.task[:50]}...",
                metadata={
                    "task_type": "crew_analysis",
                    "user_id": task_request.userId,
                    "context": task_request.context
                }
            )

        return AIResponse(
            response=result,
            source="crew_ai"
        )
    except Exception as e:
        print(f"Error processing CrewAI task: {str(e)}")
        return AIResponse(
            response=f"Failed to process CrewAI task: {str(e)}",
            source="error"
        )

# Helper function to import datetime module (to avoid cyclic imports)
def import_datetime():
    import datetime
    return datetime

# FastAPI app
app = FastAPI(
    title="Aizako CRM AI Service",
    description="AI microservice for Aizako CRM using CrewAI and Document Service",
    version="1.0.0"
)

@app.get("/")
async def root():
    return {"message": "Aizako CRM AI Service is running"}

@app.get("/status", response_model=StatusResponse)
async def status():
    # Get document count if document service is initialized
    document_count = 0
    if document_service.is_initialized():
        documents = document_service.list_documents()
        document_count = len(documents)

    return StatusResponse(
        status="online",
        openai_available=bool(OPENAI_API_KEY),
        redis_available=redis_available,
        llama_index_available=document_service.is_initialized(),  # For backwards compatibility
        langraph_available=langraph_available,
        document_count=document_count,
        version="1.0.0"
    )

@app.post("/general", response_model=AIResponse)
async def general_query(
    request: GeneralQueryRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("ai.assistant.basic"))
):
    # Record usage for AI tokens
    subscription_client.record_usage(tenant_id, "ai.tokens", 100)  # Estimate token usage

    return await process_general_query(request.query, request.userId, request.context)

@app.post("/crew", response_model=AIResponse)
async def crew_task(
    request: CrewTaskRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("ai.assistant.advanced"))
):
    # Record usage for AI tokens (crew tasks use more tokens)
    subscription_client.record_usage(tenant_id, "ai.tokens", 500)  # Estimate token usage

    return await process_crew_task(request)

@app.post("/langraph", response_model=AIResponse)
async def langraph_agent(
    request: LangGraphRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("ai.assistant.langraph"))
):
    """Process user input through the langraph agent system"""
    try:
        # Check if langraph is available
        if not langraph_available:
            return AIResponse(
                response="Langraph agents are not available. Please contact support.",
                source="error"
            )

        # Record usage for AI tokens
        subscription_client.record_usage(tenant_id, "ai.tokens", 300)  # Estimate token usage

        # Convert conversation history to the format expected by langraph
        from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

        conversation_history = []
        if request.conversation_history:
            for msg in request.conversation_history:
                if msg.get("role") == "user":
                    conversation_history.append(HumanMessage(content=msg.get("content", "")))
                elif msg.get("role") == "assistant":
                    conversation_history.append(AIMessage(content=msg.get("content", "")))
                elif msg.get("role") == "system":
                    conversation_history.append(SystemMessage(content=msg.get("content", "")))

        # Process the input through langraph
        result = langraph_process_user_input(
            user_input=request.input,
            user_id=request.userId,
            tenant_id=request.tenantId or tenant_id,
            conversation_history=conversation_history,
            context=request.context
        )

        # Store the result in Redis if available
        if redis_available and request.userId:
            redis_client.lpush(f"user:{request.userId}:langraph", json.dumps({
                "input": request.input,
                "timestamp": import_datetime().datetime.now().isoformat(),
                "response": result.get("response", "")
            }))

        return AIResponse(
            response=result.get("response", "I couldn't process your request."),
            source="langraph"
        )
    except Exception as e:
        logger.error(f"Error processing langraph request: {str(e)}")
        return AIResponse(
            response=f"Failed to process request: {str(e)}",
            source="error"
        )

# Document management endpoints
@app.post("/documents", response_model=DocumentResponse)
async def add_document(
    request: DocumentRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("ai.document"))
):
    """Add a new document to the knowledge base"""
    # Record usage for document storage
    document_size = len(request.text) / 1024  # Size in KB
    subscription_client.record_usage(tenant_id, "storage", document_size)

    success = document_service.add_crm_document(
        text=request.text,
        doc_id=request.doc_id,
        doc_type=request.doc_type,
        title=request.title,
        metadata=request.metadata
    )

    if success:
        # If doc_id wasn't provided, it was auto-generated
        doc_id = request.doc_id or f"doc_{import_datetime().datetime.now().strftime('%Y%m%d%H%M%S')}"
        return DocumentResponse(
            doc_id=doc_id,
            title=request.title or f"Document {doc_id}",
            doc_type=request.doc_type,
            success=True,
            message="Document added successfully"
        )
    else:
        return DocumentResponse(
            doc_id="",
            title="",
            doc_type="",
            success=False,
            message="Failed to add document"
        )

@app.get("/documents", response_model=DocumentListResponse)
async def list_documents(doc_type: Optional[str] = None):
    """List all documents in the knowledge base"""
    documents = document_service.list_documents(doc_type)
    return DocumentListResponse(
        documents=documents,
        count=len(documents)
    )

@app.get("/documents/{doc_id}")
async def get_document(doc_id: str):
    """Get a document by ID"""
    document = document_service.get_document(doc_id)
    if document:
        return document
    else:
        raise HTTPException(status_code=404, detail="Document not found")

@app.delete("/documents/{doc_id}", response_model=DocumentResponse)
async def delete_document(doc_id: str):
    """Delete a document by ID"""
    # Check if document exists first
    document = document_service.get_document(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    success = document_service.delete_document(doc_id)
    return DocumentResponse(
        doc_id=doc_id,
        title=document.get("title", ""),
        doc_type=document.get("doc_type", ""),
        success=success,
        message="Document deleted successfully" if success else "Failed to delete document"
    )

@app.post("/documents/query", response_model=AIResponse)
async def query_documents(
    request: DocumentQueryRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("ai.document"))
):
    """Query documents using the document service"""
    if not document_service.is_initialized():
        raise HTTPException(status_code=503, detail="Document service is not available")

    # Record usage for AI tokens
    subscription_client.record_usage(tenant_id, "ai.tokens", 200)  # Estimate token usage

    result = document_service.query(request.query, request.filters)

    return AIResponse(
        response=result["response"],
        source="document_service",
        source_documents=result["source_documents"]
    )

@app.post("/documents/html-to-pdf")
async def html_to_pdf(
    request: HtmlToPdfRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("ai.document"))
):
    """Convert HTML to PDF"""
    try:
        # Record usage for document generation
        subscription_client.record_usage(tenant_id, "ai.document.generation", 1)

        # Generate PDF from HTML
        pdf_content = document_generator.html_to_pdf(request.html, request.filename)

        # Return PDF as binary response
        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=\"{request.filename or 'document.pdf'}\""
            }
        )
    except Exception as e:
        logger.error(f"Error converting HTML to PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to convert HTML to PDF: {str(e)}")

# Workflow and Insights endpoints
@app.post("/workflow/parse", response_model=WorkflowParseResponse)
async def parse_workflow(
    request: WorkflowParseRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("workflow.nl_parser"))
):
    """Parse a natural language description into a workflow DSL"""
    try:
        # Record usage for AI tokens
        subscription_client.record_usage(tenant_id, "ai.tokens", 300)  # Estimate token usage

        # Process the workflow request
        result = process_workflow_request(request.prompt)

        return result
    except Exception as e:
        logger.error(f"Error parsing workflow: {str(e)}")
        return WorkflowParseResponse(
            success=False,
            error=f"Failed to parse workflow: {str(e)}"
        )

@app.post("/insights/generate", response_model=InsightsResponse)
async def generate_insight(
    request: InsightsRequest,
    tenant_id: str = Depends(lambda: check_feature_entitlement("insights.generate"))
):
    """Generate an insight from a natural language question"""
    try:
        # Record usage for AI tokens
        subscription_client.record_usage(tenant_id, "ai.tokens", 500)  # Estimate token usage

        # Process the insights request
        result = process_insights_request(request.question, request.filters)

        return result
    except Exception as e:
        logger.error(f"Error generating insight: {str(e)}")
        return InsightsResponse(
            success=False,
            error=f"Failed to generate insight: {str(e)}"
        )

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)