syntax = "proto3";

package kairo;

// Service definition for the Kairo CRM AI Bridge
service KairoAIService {
  // Process a general AI query
  rpc ProcessQuery (QueryRequest) returns (QueryResponse) {}
  
  // Execute a CrewAI task
  rpc ExecuteCrewTask (CrewTaskRequest) returns (CrewTaskResponse) {}
  
  // Retrieve system status
  rpc GetStatus (StatusRequest) returns (StatusResponse) {}
  
  // Add data to the RAG system
  rpc AddDocument (DocumentRequest) returns (DocumentResponse) {}
}

// Request for processing a general query
message QueryRequest {
  string query = 1;
  int32 user_id = 2;
  optional string session_id = 3;
  optional map<string, string> metadata = 4;
}

// Response from processing a query
message QueryResponse {
  string response = 1;
  string source = 2;
  optional double confidence = 3;
  optional repeated string source_documents = 4;
  optional map<string, string> metadata = 5;
}

// Request for executing a CrewAI task
message CrewTaskRequest {
  string task = 1;
  int32 user_id = 2;
  optional string session_id = 3;
  optional map<string, string> context = 4;
}

// Response from executing a CrewAI task
message CrewTaskResponse {
  string response = 1;
  string source = 2;
  optional string crew_name = 3;
  optional repeated string agents_involved = 4;
  optional map<string, string> metadata = 5;
}

// Request for system status
message StatusRequest {
  optional bool include_details = 1;
}

// Response for system status
message StatusResponse {
  bool available = 1;
  string region = 2;
  bool compression = 3;
  optional string version = 4;
  optional map<string, bool> service_status = 5;
}

// Request to add a document to the RAG system
message DocumentRequest {
  string content = 1;
  optional string document_id = 2;
  optional string document_type = 3;
  optional int32 user_id = 4;
  optional map<string, string> metadata = 5;
}

// Response after adding a document
message DocumentResponse {
  bool success = 1;
  optional string document_id = 2;
  optional string error_message = 3;
}