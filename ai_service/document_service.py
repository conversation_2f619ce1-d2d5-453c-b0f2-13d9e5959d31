import os
import json
import datetime
import openai
from typing import List, Dict, Any, Optional, Union, Tuple

class CRMDocument:
    """Class representing a CRM document with metadata"""
    def __init__(
        self, 
        text: str, 
        doc_id: str,
        doc_type: str,
        title: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.text = text
        self.doc_id = doc_id
        self.doc_type = doc_type
        self.title = title or f"Document {doc_id}"
        self.metadata = metadata or {}
        self.created_at = datetime.datetime.now().isoformat()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CRMDocument':
        """Create a CRMDocument from a dictionary"""
        return cls(
            text=data["text"],
            doc_id=data["doc_id"],
            doc_type=data["doc_type"],
            title=data.get("title"),
            metadata={k: v for k, v in data.items() 
                     if k not in ["text", "doc_id", "doc_type", "title"]}
        )

class DocumentService:
    """A simplified CRM document management service with OpenAI integration"""
    
    def __init__(self, documents_dir: str = "data"):
        """
        Initialize the service with a directory containing documents
        
        Args:
            documents_dir: Path to the directory containing documents
        """
        self.documents_dir = documents_dir
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.documents_cache = {}  # Cache of documents by ID
        self.client = None
        
        # Initialize if API key is available
        if self.api_key:
            self._initialize()
    
    def _initialize(self):
        """Initialize the OpenAI client"""
        try:
            # Initialize OpenAI client
            openai.api_key = self.api_key
            self.client = openai.OpenAI(api_key=self.api_key)
            
            # Ensure documents directory exists
            os.makedirs(self.documents_dir, exist_ok=True)
            
            # Load existing documents into cache
            self._load_documents()
            
            # Create sample documents if no documents exist
            if not self.documents_cache:
                self._create_sample_crm_documents()
                
            print("Document service initialized successfully")
            
        except Exception as e:
            print(f"Error initializing document service: {str(e)}")
    
    def _load_documents(self):
        """Load existing documents from the file system into cache"""
        try:
            # Find all document files
            doc_files = []
            for root, _, files in os.walk(self.documents_dir):
                for file in files:
                    if file.endswith('.json'):
                        doc_files.append(os.path.join(root, file))
            
            # Load documents from files
            for file_path in doc_files:
                try:
                    with open(file_path, 'r') as f:
                        doc_data = json.load(f)
                        crm_doc = CRMDocument.from_dict(doc_data)
                        self.documents_cache[crm_doc.doc_id] = crm_doc
                except Exception as e:
                    print(f"Error loading document {file_path}: {e}")
            
            print(f"Loaded {len(self.documents_cache)} documents into cache")
                
        except Exception as e:
            print(f"Error loading documents: {str(e)}")
    
    def _create_sample_crm_documents(self):
        """Create sample CRM documents for testing"""
        # Sample CRM documents
        crm_samples = [
            {
                "doc_id": "sales_process_guide",
                "doc_type": "guide",
                "title": "Effective B2B Sales Process Guide",
                "text": """# Effective B2B Sales Process Guide

## Introduction
This guide outlines the best practices for B2B sales in the African market.

## Key Stages
1. **Lead Qualification**: Assess potential fit using BANT criteria.
2. **Discovery**: Understand the client's business challenges and goals.
3. **Solution Presentation**: Present tailored solutions to address specific needs.
4. **Negotiation**: Discuss terms, pricing, and implementation details.
5. **Closing**: Finalize the agreement and establish next steps.

## African Market Considerations
- Build relationships before discussing business
- Understand local regulatory environments
- Consider mobile-first communication strategies
- Plan for longer sales cycles in enterprise deals
- Leverage local partnerships when possible

## Follow-up Best Practices
- Send meeting summaries within 24 hours
- Provide additional resources relevant to discussed challenges
- Schedule regular check-ins during the consideration phase
- Involve technical teams early for complex solutions""",
                "industry": "general",
                "audience": "sales_team",
                "importance": "high"
            },
            {
                "doc_id": "opportunity_scoring",
                "doc_type": "methodology",
                "title": "Opportunity Scoring Framework",
                "text": """# Opportunity Scoring Framework

## Purpose
This framework helps sales teams prioritize opportunities based on potential value and close probability.

## Scoring Criteria
Each opportunity should be scored from 1-10 on these dimensions:

### Budget Alignment (25%)
- Does the client have sufficient budget allocated?
- Is the decision maker involved in budget discussions?
- How stable is their financial situation?

### Need Validation (25%)
- How urgent is the client's problem?
- What measurable impact will our solution provide?
- Does our solution align with their strategic goals?

### Authority Mapping (20%)
- Are we engaged with the key decision makers?
- How many stakeholders need to approve?
- What level of executive sponsorship exists?

### Timeline Clarity (15%)
- Is there a clear implementation timeline?
- Are there external deadlines driving the purchase?
- What is the maximum acceptable timeframe for ROI?

### Competitive Position (15%)
- How does our solution compare to alternatives?
- What unique value do we provide?
- What existing relationships do we leverage?

## Calculation
Final score = Sum of (Category Score × Category Weight)

## Recommended Actions
- Score 8-10: High priority, daily engagement
- Score 6-8: Medium priority, weekly engagement
- Score 4-6: Low priority, maintain monthly contact
- Score <4: Consider disqualifying or long-term nurture""",
                "industry": "general",
                "audience": "sales_managers",
                "importance": "high"
            },
            {
                "doc_id": "telecom_industry_insights",
                "doc_type": "market_research",
                "title": "African Telecom Industry Insights 2023",
                "text": """# African Telecom Industry Insights 2023

## Market Overview
The African telecommunications market continues to expand rapidly with mobile penetration reaching 80% across the continent.

## Key Trends
1. **5G Deployment**: South Africa, Nigeria, and Kenya lead in infrastructure development
2. **Mobile Money Growth**: 57% increase in transaction volume since 2020
3. **Rural Connectivity**: Major investment in last-mile solutions
4. **Data Usage**: 38% annual growth in mobile data consumption
5. **Regulatory Changes**: New regional frameworks for cross-border services

## Challenges
- Infrastructure cost and maintenance
- Regulatory fragmentation across markets
- Talent retention in technical roles
- Energy reliability for network operations
- Cybersecurity concerns

## Opportunities
- Enterprise cloud services growing at 29% annually
- IoT applications in agriculture and logistics
- Fintech partnerships for expanded service offerings
- Digital identity solutions
- Edge computing infrastructure

## Key Players
- MTN Group (Pan-African)
- Vodacom/Safaricom (East & Southern Africa)
- Orange (West & Central Africa)
- Airtel Africa (Pan-African)
- Telkom (South Africa)

## Recommendation
Focus on integrated solutions combining connectivity, cloud services, and specialized industry applications.""",
                "industry": "telecom",
                "region": "africa",
                "audience": "sales_team",
                "importance": "medium"
            }
        ]
        
        # Save documents
        for sample in crm_samples:
            doc_id = sample["doc_id"]
            doc_type = sample["doc_type"]
            text = sample["text"]
            title = sample["title"]
            
            # Create metadata by removing text and other specific fields
            metadata = {k: v for k, v in sample.items() 
                      if k not in ["doc_id", "doc_type", "text", "title"]}
            
            # Create and save document
            self.add_crm_document(
                text=text,
                doc_id=doc_id,
                doc_type=doc_type,
                title=title,
                metadata=metadata
            )
    
    def is_initialized(self) -> bool:
        """Check if the service is properly initialized"""
        return self.client is not None and self.api_key is not None
    
    def query(self, query_text: str, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Query the OpenAI model with relevant documents for context
        
        Args:
            query_text: The query text
            filters: Optional metadata filters, e.g. {"doc_type": "guide", "industry": "telecom"}
            
        Returns:
            Dict containing response and metadata
        """
        if not self.is_initialized():
            return {
                "response": "Service is not initialized. Please check your OpenAI API key.",
                "source_documents": [],
                "success": False
            }
        
        try:
            # Get filtered documents to use as context
            relevant_docs = []
            doc_list = self.list_documents(doc_type=filters.get("doc_type") if filters else None)
            
            # Apply additional filters if provided
            if filters:
                for doc_summary in doc_list:
                    # Skip if any filter doesn't match
                    include = True
                    for key, value in filters.items():
                        if key == "doc_type":
                            # Already filtered by doc_type in list_documents
                            continue
                        
                        doc_value = None
                        if key in doc_summary["metadata"]:
                            doc_value = doc_summary["metadata"][key]
                        
                        if doc_value != value:
                            include = False
                            break
                    
                    if include:
                        # Get full document
                        doc = self.get_document(doc_summary["doc_id"])
                        if doc:
                            relevant_docs.append(doc)
            else:
                # Get all documents
                for doc_summary in doc_list:
                    doc = self.get_document(doc_summary["doc_id"])
                    if doc:
                        relevant_docs.append(doc)
            
            # Prepare context for the prompt
            context = ""
            source_docs = []
            
            # Include up to 3 most relevant documents
            for i, doc in enumerate(relevant_docs[:3]):
                context += f"\n\nDOCUMENT {i+1}: {doc['title']}\n{doc['text']}\n"
                source_docs.append({
                    "text": doc["text"][:500] + ("..." if len(doc["text"]) > 500 else ""),
                    "document_id": doc["doc_id"],
                    "document_type": doc["doc_type"],
                    "title": doc["title"],
                    "metadata": doc["metadata"]
                })
            
            # Create prompt with context
            prompt = f"""Please answer the following question based on the provided CRM documents.
            
Question: {query_text}

Context Documents:
{context}

Answer the question directly based on the information from the documents. If the documents don't contain relevant information, say so.
"""
            
            # Query OpenAI
            response = self.client.chat.completions.create(
                model="gpt-4o",  # the newest OpenAI model is "gpt-4o" which was released May 13, 2024
                messages=[
                    {"role": "system", "content": "You are a helpful CRM assistant with access to internal CRM documents. Provide concise, accurate answers based on the documents."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            return {
                "response": response.choices[0].message.content,
                "source_documents": source_docs,
                "success": True
            }
            
        except Exception as e:
            print(f"Error querying OpenAI: {str(e)}")
            return {
                "response": f"Error: {str(e)}",
                "source_documents": [],
                "success": False
            }
    
    def add_document(self, document_text: str, document_id: str = None, metadata: Dict[str, Any] = None) -> bool:
        """
        Add a simple document
        
        Args:
            document_text: The text content of the document
            document_id: Optional document ID
            metadata: Optional metadata
            
        Returns:
            True if successful, False otherwise
        """
        return self.add_crm_document(
            text=document_text, 
            doc_id=document_id, 
            doc_type="generic",
            title=None,
            metadata=metadata
        )
    
    def add_crm_document(
        self, 
        text: str, 
        doc_id: Optional[str] = None, 
        doc_type: str = "generic", 
        title: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Add a CRM document with rich metadata
        
        Args:
            text: Document text content
            doc_id: Unique document ID
            doc_type: Type of document (guide, case_study, market_research, etc.)
            title: Document title
            metadata: Additional metadata fields
            
        Returns:
            True if successful, False otherwise
        """
        if not doc_id:
            doc_id = f"doc_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
            
        try:
            # Create CRM document
            crm_doc = CRMDocument(
                text=text,
                doc_id=doc_id,
                doc_type=doc_type,
                title=title,
                metadata=metadata
            )
            
            # Save document as JSON
            file_path = os.path.join(self.documents_dir, f"{doc_id}.json")
            with open(file_path, 'w') as f:
                doc_data = {
                    "text": text,
                    "doc_id": doc_id,
                    "doc_type": doc_type,
                    "title": title,
                    **(metadata or {})
                }
                json.dump(doc_data, f, indent=2)
            
            # Add to cache
            self.documents_cache[doc_id] = crm_doc
            
            return True
            
        except Exception as e:
            print(f"Error adding document: {str(e)}")
            return False
    
    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a document by ID
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document data or None if not found
        """
        # Check cache first
        if doc_id in self.documents_cache:
            crm_doc = self.documents_cache[doc_id]
            return {
                "text": crm_doc.text,
                "doc_id": crm_doc.doc_id,
                "doc_type": crm_doc.doc_type,
                "title": crm_doc.title,
                "metadata": crm_doc.metadata
            }
        
        # Check file system
        file_path = os.path.join(self.documents_dir, f"{doc_id}.json")
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    # Create CRM document and add to cache
                    crm_doc = CRMDocument.from_dict(data)
                    self.documents_cache[doc_id] = crm_doc
                    return {
                        "text": crm_doc.text,
                        "doc_id": crm_doc.doc_id,
                        "doc_type": crm_doc.doc_type,
                        "title": crm_doc.title,
                        "metadata": crm_doc.metadata
                    }
            except Exception as e:
                print(f"Error reading document {doc_id}: {str(e)}")
        
        return None
    
    def list_documents(self, doc_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all documents, optionally filtered by type
        
        Args:
            doc_type: Optional document type filter
            
        Returns:
            List of document summaries
        """
        # Refresh document cache from file system
        self._load_documents()
        
        # Load all documents and filter
        result = []
        for doc_id, crm_doc in self.documents_cache.items():
            # Skip if doc_type filter is set and doesn't match
            if doc_type and crm_doc.doc_type != doc_type:
                continue
            
            # Add to result
            result.append({
                "doc_id": crm_doc.doc_id,
                "doc_type": crm_doc.doc_type,
                "title": crm_doc.title,
                "metadata": crm_doc.metadata
            })
        
        return result
    
    def delete_document(self, doc_id: str) -> bool:
        """
        Delete a document
        
        Args:
            doc_id: Document ID
            
        Returns:
            True if successful, False otherwise
        """
        file_path = os.path.join(self.documents_dir, f"{doc_id}.json")
        if os.path.exists(file_path):
            try:
                # Remove the file
                os.remove(file_path)
                
                # Remove from cache
                if doc_id in self.documents_cache:
                    del self.documents_cache[doc_id]
                
                return True
            except Exception as e:
                print(f"Error deleting document {doc_id}: {str(e)}")
        
        return False

# Global instance for use in the FastAPI app
document_service = DocumentService()