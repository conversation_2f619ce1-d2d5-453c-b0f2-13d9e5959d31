"""
Deal Agent for Aizako CRM

This module implements the deal agent that handles opportunity/deal management operations
in the Aizako CRM system.
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
import logging
from langchain.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
API_URL = os.environ.get("API_URL", "http://localhost:5000")

# Initialize language model
def get_llm(temperature=0.2, model="gpt-4o"):
    """Get the language model with specified parameters."""
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable not set")
    
    return ChatOpenAI(
        model=model,
        temperature=temperature,
        api_key=OPENAI_API_KEY
    )

# Define deal-related tools
class SearchOpportunitiesTool(BaseTool):
    name = "search_opportunities"
    description = "Search for opportunities/deals in the CRM system based on name, stage, contact, company, or other criteria."
    
    def _run(self, query: str, tenant_id: str = None) -> str:
        """Search for opportunities based on the query."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/opportunities/search",
                params={"q": query},
                headers=headers
            )
            
            if response.status_code == 200:
                opportunities = response.json()
                if not opportunities:
                    return "No opportunities found matching your search criteria."
                
                result = "Found the following opportunities:\n\n"
                for i, opportunity in enumerate(opportunities[:5], 1):
                    result += f"{i}. {opportunity.get('name', 'Unnamed Opportunity')}\n"
                    result += f"   Stage: {opportunity.get('stage', 'N/A').replace('_', ' ').title()}\n"
                    result += f"   Value: {opportunity.get('currency', '$')}{opportunity.get('value', 0):,}\n"
                    result += f"   Close Date: {opportunity.get('closeDate', 'N/A')}\n"
                    result += f"   ID: {opportunity.get('_id', '')}\n\n"
                
                if len(opportunities) > 5:
                    result += f"...and {len(opportunities) - 5} more opportunities. Please refine your search if needed."
                
                return result
            else:
                return f"Error searching opportunities: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in search_opportunities: {str(e)}")
            return f"Failed to search opportunities: {str(e)}"

class GetOpportunityDetailsTool(BaseTool):
    name = "get_opportunity_details"
    description = "Get detailed information about a specific opportunity by ID."
    
    def _run(self, opportunity_id: str, tenant_id: str = None) -> str:
        """Get detailed information about an opportunity."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/opportunities/{opportunity_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                opportunity = response.json()
                result = f"Opportunity Details for {opportunity.get('name', 'Unnamed Opportunity')}:\n\n"
                result += f"ID: {opportunity.get('_id', '')}\n"
                result += f"Stage: {opportunity.get('stage', 'N/A').replace('_', ' ').title()}\n"
                result += f"Value: {opportunity.get('currency', '$')}{opportunity.get('value', 0):,}\n"
                result += f"Close Date: {opportunity.get('closeDate', 'N/A')}\n"
                result += f"Probability: {opportunity.get('probability', 0)}%\n"
                
                if opportunity.get('contactName'):
                    result += f"Contact: {opportunity.get('contactName', 'N/A')}\n"
                
                if opportunity.get('companyName'):
                    result += f"Company: {opportunity.get('companyName', 'N/A')}\n"
                
                if opportunity.get('description'):
                    result += f"\nDescription: {opportunity.get('description')}\n"
                
                if opportunity.get('nextSteps'):
                    result += f"\nNext Steps: {opportunity.get('nextSteps')}\n"
                
                return result
            else:
                return f"Error getting opportunity details: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_opportunity_details: {str(e)}")
            return f"Failed to get opportunity details: {str(e)}"

class CreateOpportunityTool(BaseTool):
    name = "create_opportunity"
    description = "Create a new opportunity/deal in the CRM system."
    
    def _run(
        self, 
        name: str, 
        stage: str = "discovery", 
        value: float = None, 
        currency: str = "$",
        close_date: str = None,
        probability: int = None,
        contact_id: str = None,
        company_id: str = None,
        description: str = None,
        next_steps: str = None,
        tenant_id: str = None
    ) -> str:
        """Create a new opportunity."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            data = {
                "name": name,
                "stage": stage
            }
            
            if value is not None:
                data["value"] = value
            if currency:
                data["currency"] = currency
            if close_date:
                data["closeDate"] = close_date
            if probability is not None:
                data["probability"] = probability
            if contact_id:
                data["contactId"] = contact_id
            if company_id:
                data["companyId"] = company_id
            if description:
                data["description"] = description
            if next_steps:
                data["nextSteps"] = next_steps
                
            response = requests.post(
                f"{API_URL}/api/opportunities",
                json=data,
                headers=headers
            )
            
            if response.status_code == 201:
                opportunity = response.json()
                return f"Opportunity created successfully!\n\nID: {opportunity.get('_id')}\nName: {opportunity.get('name')}\nStage: {opportunity.get('stage').replace('_', ' ').title()}"
            else:
                return f"Error creating opportunity: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in create_opportunity: {str(e)}")
            return f"Failed to create opportunity: {str(e)}"

class UpdateOpportunityTool(BaseTool):
    name = "update_opportunity"
    description = "Update an existing opportunity/deal in the CRM system."
    
    def _run(
        self, 
        opportunity_id: str,
        name: str = None, 
        stage: str = None, 
        value: float = None, 
        currency: str = None,
        close_date: str = None,
        probability: int = None,
        contact_id: str = None,
        company_id: str = None,
        description: str = None,
        next_steps: str = None,
        tenant_id: str = None
    ) -> str:
        """Update an existing opportunity."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            data = {}
            
            if name:
                data["name"] = name
            if stage:
                data["stage"] = stage
            if value is not None:
                data["value"] = value
            if currency:
                data["currency"] = currency
            if close_date:
                data["closeDate"] = close_date
            if probability is not None:
                data["probability"] = probability
            if contact_id:
                data["contactId"] = contact_id
            if company_id:
                data["companyId"] = company_id
            if description:
                data["description"] = description
            if next_steps:
                data["nextSteps"] = next_steps
                
            if not data:
                return "No updates provided. Please specify at least one field to update."
                
            response = requests.put(
                f"{API_URL}/api/opportunities/{opportunity_id}",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                opportunity = response.json()
                return f"Opportunity updated successfully!\n\nID: {opportunity.get('_id')}\nName: {opportunity.get('name')}\nStage: {opportunity.get('stage').replace('_', ' ').title()}"
            else:
                return f"Error updating opportunity: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in update_opportunity: {str(e)}")
            return f"Failed to update opportunity: {str(e)}"

class GetPipelineStatsTool(BaseTool):
    name = "get_pipeline_stats"
    description = "Get statistics about the sales pipeline, including deal counts and values by stage."
    
    def _run(self, tenant_id: str = None) -> str:
        """Get pipeline statistics."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/opportunities/pipeline-stats",
                headers=headers
            )
            
            if response.status_code == 200:
                stats = response.json()
                
                result = "Sales Pipeline Statistics:\n\n"
                
                # Overall stats
                total_count = stats.get('totalCount', 0)
                total_value = stats.get('totalValue', 0)
                result += f"Total Opportunities: {total_count}\n"
                result += f"Total Pipeline Value: ${total_value:,.2f}\n\n"
                
                # Stats by stage
                result += "Breakdown by Stage:\n"
                stages = stats.get('byStage', {})
                for stage, stage_stats in stages.items():
                    stage_name = stage.replace('_', ' ').title()
                    count = stage_stats.get('count', 0)
                    value = stage_stats.get('value', 0)
                    result += f"- {stage_name}: {count} opportunities, ${value:,.2f}\n"
                
                return result
            else:
                return f"Error getting pipeline stats: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_pipeline_stats: {str(e)}")
            return f"Failed to get pipeline stats: {str(e)}"

class GetForecastTool(BaseTool):
    name = "get_sales_forecast"
    description = "Get the sales forecast for the current quarter or a specified time period."
    
    def _run(
        self, 
        period: str = "current_quarter",
        tenant_id: str = None
    ) -> str:
        """Get sales forecast."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/opportunities/forecast",
                params={"period": period},
                headers=headers
            )
            
            if response.status_code == 200:
                forecast = response.json()
                
                result = f"Sales Forecast for {forecast.get('periodName', period)}:\n\n"
                
                # Forecast summary
                result += f"Forecast Total: ${forecast.get('forecastTotal', 0):,.2f}\n"
                result += f"Weighted Pipeline: ${forecast.get('weightedPipeline', 0):,.2f}\n"
                result += f"Closed Won: ${forecast.get('closedWon', 0):,.2f}\n"
                result += f"Target: ${forecast.get('target', 0):,.2f}\n"
                
                if forecast.get('target', 0) > 0:
                    percentage = (forecast.get('forecastTotal', 0) / forecast.get('target', 0)) * 100
                    result += f"Progress to Target: {percentage:.1f}%\n"
                
                # Breakdown by stage
                if forecast.get('byStage'):
                    result += "\nBreakdown by Stage:\n"
                    for stage, value in forecast.get('byStage', {}).items():
                        stage_name = stage.replace('_', ' ').title()
                        result += f"- {stage_name}: ${value:,.2f}\n"
                
                return result
            else:
                return f"Error getting sales forecast: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_sales_forecast: {str(e)}")
            return f"Failed to get sales forecast: {str(e)}"

# Define the Deal Agent
def create_deal_agent(tenant_id: str = None):
    """Create the deal agent with tools."""
    # Initialize tools
    tools = [
        SearchOpportunitiesTool(),
        GetOpportunityDetailsTool(),
        CreateOpportunityTool(),
        UpdateOpportunityTool(),
        GetPipelineStatsTool(),
        GetForecastTool()
    ]
    
    # Define the system prompt
    system_prompt = """You are the Deal Agent for Aizako CRM, an AI-native customer relationship management system.
Your role is to help users manage their opportunities/deals, including searching, creating, updating, and viewing deal details and pipeline statistics.

As the Deal Agent, you should:
1. Help users find opportunities based on their search criteria
2. Provide detailed information about specific opportunities
3. Create new opportunities with the information provided by the user
4. Update existing opportunities with new information
5. Show pipeline statistics and sales forecasts
6. Provide insights and recommendations based on the pipeline data

When using tools, make sure to provide all the necessary information.
When creating or updating opportunities, ask for any missing required information.
When showing opportunity details or pipeline stats, format the information in a clear and readable way.

Remember that you are part of an AI-native CRM system designed to minimize button clicking and maximize natural language interaction.
"""
    
    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ])
    
    # Initialize the LLM
    llm = get_llm()
    
    # Create the agent
    agent = create_openai_tools_agent(llm, tools, prompt)
    
    # Create the agent executor
    agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True,
    )
    
    return agent_executor

# Function to process user input
def process_deal_request(
    user_input: str,
    chat_history: List[Union[HumanMessage, AIMessage, SystemMessage]],
    tenant_id: str = None
) -> Dict[str, Any]:
    """
    Process user input related to deal management.
    
    Args:
        user_input: The user's message
        chat_history: The conversation history
        tenant_id: The tenant ID for multi-tenancy
        
    Returns:
        Dict containing the response and updated conversation history
    """
    try:
        # Create the deal agent
        agent_executor = create_deal_agent(tenant_id)
        
        # Process the input
        result = agent_executor.invoke({
            "input": user_input,
            "chat_history": chat_history
        })
        
        # Extract the response
        response = result.get("output", "I couldn't process your request about deals/opportunities.")
        
        # Update conversation history
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=response))
        
        return {
            "response": response,
            "chat_history": chat_history
        }
    except Exception as e:
        logger.error(f"Error processing deal request: {str(e)}")
        error_message = f"I encountered an error while processing your deal request: {str(e)}. Please try again or contact support."
        
        # Update conversation history with error
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=error_message))
        
        return {
            "response": error_message,
            "chat_history": chat_history
        }
