"""
Supervisor Agent for Aizako CRM

This module implements the supervisor agent that coordinates the activities
of specialized agents in the Aizako CRM system.
"""

import os
import json
from typing import Dict, Any, List, Optional, Union, Literal
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.tools import BaseTool
import langchain
from langchain_core.tools import tool
from langchain_core.runnables import Runnable
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
from langchain_core.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.messages import AIMessage, HumanMessage
import langchain_core.messages as messages
import logging
from langraph import graph
from langraph.graph import END, StateGraph
from langraph.prebuilt import ToolNode

# Import specialized agents
from .contact_agent import process_contact_request
from .deal_agent import process_deal_request
from .meeting_agent import process_meeting_request
from .analytics_agent import process_analytics_request
from .task_agent import process_task_request

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")

# Initialize language model
def get_llm(temperature=0.7, model="gpt-4o"):
    """Get the language model with specified parameters."""
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable not set")

    return ChatOpenAI(
        model=model,
        temperature=temperature,
        api_key=OPENAI_API_KEY
    )

class SupervisorState(BaseModel):
    """State maintained by the supervisor agent."""
    conversation_history: List[Union[HumanMessage, AIMessage, SystemMessage]] = Field(default_factory=list)
    current_task: Optional[str] = None
    active_agents: List[str] = Field(default_factory=list)
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None
    context: Dict[str, Any] = Field(default_factory=dict)

class SupervisorAction(BaseModel):
    """Action to be taken by the supervisor agent."""
    action_type: Literal["delegate", "respond", "clarify", "error"]
    agent: Optional[str] = None
    message: str
    context_updates: Optional[Dict[str, Any]] = None

# Define the supervisor system prompt
SUPERVISOR_SYSTEM_PROMPT = """You are the Supervisor Agent for Aizako CRM, an AI-native customer relationship management system.
Your role is to understand user requests, maintain conversation context, and coordinate specialized agents to fulfill tasks.

As the Supervisor, you should:
1. Understand the user's intent and determine which specialized agent(s) to delegate tasks to
2. Maintain conversation context and pass relevant information to specialized agents
3. Synthesize responses from specialized agents into coherent replies
4. Ask clarifying questions when the user's request is ambiguous
5. Handle errors gracefully and provide helpful feedback

The specialized agents you can delegate to include:
- ContactAgent: Manages contact information, enrichment, and interaction timelines
- DealAgent: Handles opportunity pipeline, stages, and forecasting
- MeetingAgent: Schedules meetings, prepares briefings, and captures follow-ups
- AnalyticsAgent: Generates reports, answers data questions, and surfaces insights
- TaskAgent: Creates, assigns, and tracks tasks across the team

When delegating to an agent, provide all relevant context from the conversation.
When responding directly to the user, be concise, professional, and helpful.
When asking clarifying questions, explain why you need the information.

Remember that you are part of an AI-native CRM system designed to minimize button clicking and maximize natural language interaction.
"""

# Define the supervisor agent
def create_supervisor_agent():
    """Create the supervisor agent."""
    llm = get_llm(temperature=0.2)

    # Define the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", SUPERVISOR_SYSTEM_PROMPT),
        MessagesPlaceholder(variable_name="conversation_history"),
        ("human", "{input}"),
    ])

    # Define the chain
    chain = (
        {"conversation_history": lambda x: x["conversation_history"], "input": lambda x: x["input"]}
        | prompt
        | llm
        | StrOutputParser()
    )

    return chain

# Define the supervisor node for the graph
def supervisor_node(state):
    """Process the user input and decide on the next action."""
    # Extract relevant information from state
    user_input = state["input"]
    conversation_history = state.get("conversation_history", [])

    # Add the user input to conversation history
    conversation_history.append(HumanMessage(content=user_input))

    # Create the supervisor agent
    supervisor_agent = create_supervisor_agent()

    # Get the supervisor's response
    try:
        response = supervisor_agent.invoke({
            "conversation_history": conversation_history,
            "input": user_input
        })

        # Add the supervisor's response to conversation history
        conversation_history.append(AIMessage(content=response))

        # Analyze the user input to determine the appropriate agent
        user_input_lower = user_input.lower()

        # Contact-related keywords
        contact_keywords = [
            "contact", "person", "lead", "customer", "client", "add contact", "find contact",
            "update contact", "contact details", "contact information", "enrichment",
            "interaction timeline", "interactions", "contact history"
        ]

        # Deal-related keywords
        deal_keywords = [
            "deal", "opportunity", "pipeline", "sales", "revenue", "forecast", "stage",
            "close date", "win", "lose", "lost", "won", "deal value", "deal size",
            "probability", "conversion rate", "sales cycle"
        ]

        # Meeting-related keywords
        meeting_keywords = [
            "meeting", "schedule", "calendar", "appointment", "invite", "attendee",
            "meeting prep", "preparation", "meeting notes", "agenda", "available time",
            "time slot", "booking", "calendly"
        ]

        # Analytics-related keywords
        analytics_keywords = [
            "analytics", "report", "dashboard", "metric", "kpi", "performance", "statistics",
            "data", "chart", "graph", "trend", "analysis", "insight", "measure", "roi",
            "conversion", "funnel", "source analysis"
        ]

        # Task-related keywords
        task_keywords = [
            "task", "todo", "to-do", "to do", "reminder", "assign", "due date", "priority",
            "complete task", "mark as done", "follow up", "follow-up", "action item",
            "checklist", "deadline"
        ]

        # Check for keyword matches
        if any(keyword in user_input_lower for keyword in contact_keywords):
            return {"next": "contact_agent", "conversation_history": conversation_history}
        elif any(keyword in user_input_lower for keyword in deal_keywords):
            return {"next": "deal_agent", "conversation_history": conversation_history}
        elif any(keyword in user_input_lower for keyword in meeting_keywords):
            return {"next": "meeting_agent", "conversation_history": conversation_history}
        elif any(keyword in user_input_lower for keyword in analytics_keywords):
            return {"next": "analytics_agent", "conversation_history": conversation_history}
        elif any(keyword in user_input_lower for keyword in task_keywords):
            return {"next": "task_agent", "conversation_history": conversation_history}
        else:
            # Use the supervisor's response to determine the next action
            response_lower = response.lower()

            if "contact" in response_lower or "lead" in response_lower or "interaction" in response_lower:
                return {"next": "contact_agent", "conversation_history": conversation_history}
            elif "opportunity" in response_lower or "deal" in response_lower or "pipeline" in response_lower:
                return {"next": "deal_agent", "conversation_history": conversation_history}
            elif "meeting" in response_lower or "schedule" in response_lower or "calendar" in response_lower:
                return {"next": "meeting_agent", "conversation_history": conversation_history}
            elif "report" in response_lower or "analytics" in response_lower or "dashboard" in response_lower:
                return {"next": "analytics_agent", "conversation_history": conversation_history}
            elif "task" in response_lower or "reminder" in response_lower or "to-do" in response_lower:
                return {"next": "task_agent", "conversation_history": conversation_history}
            else:
                # Supervisor handled it directly
                return {"next": END, "response": response, "conversation_history": conversation_history}

    except Exception as e:
        logger.error(f"Error in supervisor node: {str(e)}")
        error_message = f"I encountered an error while processing your request: {str(e)}. Please try again or rephrase your request."
        conversation_history.append(AIMessage(content=error_message))
        return {"next": END, "response": error_message, "conversation_history": conversation_history}

# Specialized agent nodes
def contact_agent_node(state):
    """Process contact-related requests using the Contact Agent."""
    try:
        # Extract the user input and conversation history
        user_input = state.get("input", "")
        conversation_history = state.get("conversation_history", [])
        tenant_id = state.get("tenant_id")

        # Process the request using the Contact Agent
        result = process_contact_request(
            user_input=user_input,
            chat_history=conversation_history,
            tenant_id=tenant_id
        )

        # Extract the response and updated conversation history
        response = result.get("response", "I couldn't process your contact management request.")
        updated_history = result.get("chat_history", conversation_history)

        return {
            "next": END,
            "response": response,
            "conversation_history": updated_history
        }
    except Exception as e:
        logger.error(f"Error in contact_agent_node: {str(e)}")
        error_message = f"I encountered an error while processing your contact request: {str(e)}. Please try again or contact support."
        state["conversation_history"].append(AIMessage(content=error_message))
        return {"next": END, "response": error_message, "conversation_history": state["conversation_history"]}

def deal_agent_node(state):
    """Process deal-related requests using the Deal Agent."""
    try:
        # Extract the user input and conversation history
        user_input = state.get("input", "")
        conversation_history = state.get("conversation_history", [])
        tenant_id = state.get("tenant_id")

        # Process the request using the Deal Agent
        result = process_deal_request(
            user_input=user_input,
            chat_history=conversation_history,
            tenant_id=tenant_id
        )

        # Extract the response and updated conversation history
        response = result.get("response", "I couldn't process your deal/opportunity request.")
        updated_history = result.get("chat_history", conversation_history)

        return {
            "next": END,
            "response": response,
            "conversation_history": updated_history
        }
    except Exception as e:
        logger.error(f"Error in deal_agent_node: {str(e)}")
        error_message = f"I encountered an error while processing your deal request: {str(e)}. Please try again or contact support."
        state["conversation_history"].append(AIMessage(content=error_message))
        return {"next": END, "response": error_message, "conversation_history": state["conversation_history"]}

def meeting_agent_node(state):
    """Process meeting-related requests using the Meeting Agent."""
    try:
        # Extract the user input and conversation history
        user_input = state.get("input", "")
        conversation_history = state.get("conversation_history", [])
        tenant_id = state.get("tenant_id")

        # Process the request using the Meeting Agent
        result = process_meeting_request(
            user_input=user_input,
            chat_history=conversation_history,
            tenant_id=tenant_id
        )

        # Extract the response and updated conversation history
        response = result.get("response", "I couldn't process your meeting request.")
        updated_history = result.get("chat_history", conversation_history)

        return {
            "next": END,
            "response": response,
            "conversation_history": updated_history
        }
    except Exception as e:
        logger.error(f"Error in meeting_agent_node: {str(e)}")
        error_message = f"I encountered an error while processing your meeting request: {str(e)}. Please try again or contact support."
        state["conversation_history"].append(AIMessage(content=error_message))
        return {"next": END, "response": error_message, "conversation_history": state["conversation_history"]}

def analytics_agent_node(state):
    """Process analytics-related requests using the Analytics Agent."""
    try:
        # Extract the user input and conversation history
        user_input = state.get("input", "")
        conversation_history = state.get("conversation_history", [])
        tenant_id = state.get("tenant_id")

        # Process the request using the Analytics Agent
        result = process_analytics_request(
            user_input=user_input,
            chat_history=conversation_history,
            tenant_id=tenant_id
        )

        # Extract the response and updated conversation history
        response = result.get("response", "I couldn't process your analytics request.")
        updated_history = result.get("chat_history", conversation_history)

        return {
            "next": END,
            "response": response,
            "conversation_history": updated_history
        }
    except Exception as e:
        logger.error(f"Error in analytics_agent_node: {str(e)}")
        error_message = f"I encountered an error while processing your analytics request: {str(e)}. Please try again or contact support."
        state["conversation_history"].append(AIMessage(content=error_message))
        return {"next": END, "response": error_message, "conversation_history": state["conversation_history"]}

def task_agent_node(state):
    """Process task-related requests using the Task Agent."""
    try:
        # Extract the user input and conversation history
        user_input = state.get("input", "")
        conversation_history = state.get("conversation_history", [])
        tenant_id = state.get("tenant_id")

        # Process the request using the Task Agent
        result = process_task_request(
            user_input=user_input,
            chat_history=conversation_history,
            tenant_id=tenant_id
        )

        # Extract the response and updated conversation history
        response = result.get("response", "I couldn't process your task management request.")
        updated_history = result.get("chat_history", conversation_history)

        return {
            "next": END,
            "response": response,
            "conversation_history": updated_history
        }
    except Exception as e:
        logger.error(f"Error in task_agent_node: {str(e)}")
        error_message = f"I encountered an error while processing your task request: {str(e)}. Please try again or contact support."
        state["conversation_history"].append(AIMessage(content=error_message))
        return {"next": END, "response": error_message, "conversation_history": state["conversation_history"]}

# Create the agent graph
def create_agent_graph():
    """Create the graph of agents."""
    workflow = StateGraph(SupervisorState)

    # Add nodes
    workflow.add_node("supervisor", supervisor_node)
    workflow.add_node("contact_agent", contact_agent_node)
    workflow.add_node("deal_agent", deal_agent_node)
    workflow.add_node("meeting_agent", meeting_agent_node)
    workflow.add_node("analytics_agent", analytics_agent_node)
    workflow.add_node("task_agent", task_agent_node)

    # Add edges
    workflow.add_edge("supervisor", "contact_agent")
    workflow.add_edge("supervisor", "deal_agent")
    workflow.add_edge("supervisor", "meeting_agent")
    workflow.add_edge("supervisor", "analytics_agent")
    workflow.add_edge("supervisor", "task_agent")
    workflow.add_edge("supervisor", END)

    # All specialized agents return to END
    workflow.add_edge("contact_agent", END)
    workflow.add_edge("deal_agent", END)
    workflow.add_edge("meeting_agent", END)
    workflow.add_edge("analytics_agent", END)
    workflow.add_edge("task_agent", END)

    # Set the entry point
    workflow.set_entry_point("supervisor")

    return workflow.compile()

# Create the compiled agent graph
agent_graph = create_agent_graph()

# Function to process user input
def process_user_input(
    user_input: str,
    user_id: Optional[str] = None,
    tenant_id: Optional[str] = None,
    conversation_history: Optional[List[Union[HumanMessage, AIMessage, SystemMessage]]] = None,
    context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Process user input through the agent graph.

    Args:
        user_input: The user's message
        user_id: Optional user ID
        tenant_id: Optional tenant ID
        conversation_history: Optional conversation history
        context: Optional additional context

    Returns:
        Dict containing the response and updated conversation history
    """
    if conversation_history is None:
        conversation_history = []

    if context is None:
        context = {}

    # Create the initial state
    initial_state = SupervisorState(
        conversation_history=conversation_history,
        user_id=user_id,
        tenant_id=tenant_id,
        context=context
    )

    # Process the input through the agent graph
    try:
        # Use model_dump() instead of dict() for Pydantic v2 compatibility
        state_dict = initial_state.model_dump() if hasattr(initial_state, 'model_dump') else initial_state.dict()

        result = agent_graph.invoke({
            "input": user_input,
            "tenant_id": tenant_id,  # Explicitly pass tenant_id to ensure it's available in all nodes
            **state_dict
        })

        return {
            "response": result.get("response", "I'm sorry, I couldn't process your request."),
            "conversation_history": result.get("conversation_history", conversation_history)
        }
    except Exception as e:
        logger.error(f"Error processing user input: {str(e)}")
        return {
            "response": f"I encountered an error while processing your request: {str(e)}. Please try again or contact support.",
            "conversation_history": conversation_history
        }

if __name__ == "__main__":
    # Test the supervisor agent
    result = process_user_input("I need to add a new contact named John Smith")
    print(f"Response: {result['response']}")
