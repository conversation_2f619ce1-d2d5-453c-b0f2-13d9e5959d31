"""
Task Agent for Aizako CRM

This module implements the task agent that manages tasks and reminders
in the Aizako CRM system.
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
import logging
from langchain.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
API_URL = os.environ.get("API_URL", "http://localhost:5000")

# Initialize language model
def get_llm(temperature=0.2, model="gpt-4o"):
    """Get the language model with specified parameters."""
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable not set")
    
    return ChatOpenAI(
        model=model,
        temperature=temperature,
        api_key=OPENAI_API_KEY
    )

# Define task-related tools
class GetTasksTool(BaseTool):
    name = "get_tasks"
    description = "Get a list of tasks based on status, due date, or assignee."
    
    def _run(
        self, 
        status: str = "open",
        due_date_range: str = "all",
        assignee_id: str = None,
        tenant_id: str = None
    ) -> str:
        """Get tasks based on filters."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            params = {"status": status}
            
            # Handle due date range
            if due_date_range == "today":
                params["dueDateStart"] = datetime.now().strftime("%Y-%m-%d")
                params["dueDateEnd"] = datetime.now().strftime("%Y-%m-%d")
            elif due_date_range == "tomorrow":
                tomorrow = datetime.now() + timedelta(days=1)
                params["dueDateStart"] = tomorrow.strftime("%Y-%m-%d")
                params["dueDateEnd"] = tomorrow.strftime("%Y-%m-%d")
            elif due_date_range == "this_week":
                today = datetime.now()
                end_of_week = today + timedelta(days=(6 - today.weekday()))
                params["dueDateStart"] = today.strftime("%Y-%m-%d")
                params["dueDateEnd"] = end_of_week.strftime("%Y-%m-%d")
            elif due_date_range == "overdue":
                yesterday = datetime.now() - timedelta(days=1)
                params["dueDateEnd"] = yesterday.strftime("%Y-%m-%d")
            
            if assignee_id:
                params["assigneeId"] = assignee_id
                
            response = requests.get(
                f"{API_URL}/api/tasks",
                params=params,
                headers=headers
            )
            
            if response.status_code == 200:
                tasks = response.json()
                if not tasks:
                    return f"No {status} tasks found for the specified filters."
                
                result = f"{status.capitalize()} Tasks"
                if due_date_range != "all":
                    result += f" ({due_date_range.replace('_', ' ').title()})"
                result += ":\n\n"
                
                for i, task in enumerate(tasks, 1):
                    result += f"{i}. {task.get('title', 'Untitled Task')}\n"
                    
                    if task.get('dueDate'):
                        due_date = datetime.fromisoformat(task.get('dueDate').replace('Z', '+00:00')).strftime('%Y-%m-%d')
                        result += f"   Due: {due_date}\n"
                    
                    if task.get('priority'):
                        result += f"   Priority: {task.get('priority').capitalize()}\n"
                    
                    if task.get('assigneeName'):
                        result += f"   Assigned to: {task.get('assigneeName')}\n"
                    
                    if task.get('relatedTo'):
                        related = task.get('relatedTo', {})
                        related_type = related.get('type', '')
                        related_name = related.get('name', '')
                        if related_type and related_name:
                            result += f"   Related to: {related_type.capitalize()} - {related_name}\n"
                    
                    result += f"   ID: {task.get('_id', '')}\n\n"
                
                return result
            else:
                return f"Error getting tasks: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_tasks: {str(e)}")
            return f"Failed to get tasks: {str(e)}"

class GetTaskDetailsTool(BaseTool):
    name = "get_task_details"
    description = "Get detailed information about a specific task by ID."
    
    def _run(
        self, 
        task_id: str,
        tenant_id: str = None
    ) -> str:
        """Get detailed information about a task."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/tasks/{task_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                task = response.json()
                
                result = f"Task Details for {task.get('title', 'Untitled Task')}:\n\n"
                result += f"ID: {task.get('_id', '')}\n"
                result += f"Status: {task.get('status', 'Unknown').capitalize()}\n"
                
                if task.get('dueDate'):
                    due_date = datetime.fromisoformat(task.get('dueDate').replace('Z', '+00:00')).strftime('%Y-%m-%d')
                    result += f"Due Date: {due_date}\n"
                
                if task.get('priority'):
                    result += f"Priority: {task.get('priority').capitalize()}\n"
                
                if task.get('assigneeName'):
                    result += f"Assigned to: {task.get('assigneeName')}\n"
                
                if task.get('description'):
                    result += f"\nDescription: {task.get('description')}\n"
                
                if task.get('relatedTo'):
                    related = task.get('relatedTo', {})
                    related_type = related.get('type', '')
                    related_name = related.get('name', '')
                    related_id = related.get('id', '')
                    if related_type and related_name:
                        result += f"\nRelated to: {related_type.capitalize()} - {related_name} (ID: {related_id})\n"
                
                if task.get('createdAt'):
                    created_at = datetime.fromisoformat(task.get('createdAt').replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
                    result += f"\nCreated: {created_at}\n"
                
                if task.get('updatedAt'):
                    updated_at = datetime.fromisoformat(task.get('updatedAt').replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
                    result += f"Last Updated: {updated_at}\n"
                
                return result
            else:
                return f"Error getting task details: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_task_details: {str(e)}")
            return f"Failed to get task details: {str(e)}"

class CreateTaskTool(BaseTool):
    name = "create_task"
    description = "Create a new task in the CRM system."
    
    def _run(
        self, 
        title: str,
        due_date: str = None,
        priority: str = "medium",
        assignee_id: str = None,
        description: str = None,
        related_type: str = None,
        related_id: str = None,
        tenant_id: str = None
    ) -> str:
        """Create a new task."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            data = {
                "title": title,
                "status": "open",
                "priority": priority
            }
            
            if due_date:
                data["dueDate"] = due_date
            
            if assignee_id:
                data["assigneeId"] = assignee_id
            
            if description:
                data["description"] = description
            
            if related_type and related_id:
                data["relatedTo"] = {
                    "type": related_type,
                    "id": related_id
                }
                
            response = requests.post(
                f"{API_URL}/api/tasks",
                json=data,
                headers=headers
            )
            
            if response.status_code == 201:
                task = response.json()
                return f"Task created successfully!\n\nTitle: {task.get('title')}\nID: {task.get('_id')}\nDue Date: {due_date if due_date else 'Not set'}\nPriority: {priority.capitalize()}"
            else:
                return f"Error creating task: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in create_task: {str(e)}")
            return f"Failed to create task: {str(e)}"

class UpdateTaskTool(BaseTool):
    name = "update_task"
    description = "Update an existing task in the CRM system."
    
    def _run(
        self, 
        task_id: str,
        title: str = None,
        status: str = None,
        due_date: str = None,
        priority: str = None,
        assignee_id: str = None,
        description: str = None,
        tenant_id: str = None
    ) -> str:
        """Update an existing task."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            data = {}
            
            if title:
                data["title"] = title
            
            if status:
                data["status"] = status
            
            if due_date:
                data["dueDate"] = due_date
            
            if priority:
                data["priority"] = priority
            
            if assignee_id:
                data["assigneeId"] = assignee_id
            
            if description:
                data["description"] = description
                
            if not data:
                return "No updates provided. Please specify at least one field to update."
                
            response = requests.put(
                f"{API_URL}/api/tasks/{task_id}",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                task = response.json()
                result = f"Task updated successfully!\n\nTitle: {task.get('title')}\nID: {task.get('_id')}"
                
                if status:
                    result += f"\nStatus: {status.capitalize()}"
                
                if due_date:
                    result += f"\nDue Date: {due_date}"
                
                if priority:
                    result += f"\nPriority: {priority.capitalize()}"
                
                return result
            else:
                return f"Error updating task: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in update_task: {str(e)}")
            return f"Failed to update task: {str(e)}"

class CompleteTaskTool(BaseTool):
    name = "complete_task"
    description = "Mark a task as completed."
    
    def _run(
        self, 
        task_id: str,
        completion_notes: str = None,
        tenant_id: str = None
    ) -> str:
        """Mark a task as completed."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            data = {
                "status": "completed",
                "completedAt": datetime.now().isoformat()
            }
            
            if completion_notes:
                data["completionNotes"] = completion_notes
                
            response = requests.put(
                f"{API_URL}/api/tasks/{task_id}",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                task = response.json()
                return f"Task marked as completed!\n\nTitle: {task.get('title')}\nID: {task.get('_id')}"
            else:
                return f"Error completing task: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in complete_task: {str(e)}")
            return f"Failed to complete task: {str(e)}"

class GetTaskRemindersTool(BaseTool):
    name = "get_task_reminders"
    description = "Get a list of upcoming task reminders."
    
    def _run(
        self, 
        days_ahead: int = 7,
        tenant_id: str = None
    ) -> str:
        """Get upcoming task reminders."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            # Calculate date range
            start_date = datetime.now().strftime("%Y-%m-%d")
            end_date = (datetime.now() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
            
            response = requests.get(
                f"{API_URL}/api/tasks/reminders",
                params={"startDate": start_date, "endDate": end_date},
                headers=headers
            )
            
            if response.status_code == 200:
                reminders = response.json()
                if not reminders:
                    return f"No task reminders found for the next {days_ahead} days."
                
                result = f"Upcoming Task Reminders for the next {days_ahead} days:\n\n"
                for i, reminder in enumerate(reminders, 1):
                    task_title = reminder.get('taskTitle', 'Untitled Task')
                    reminder_time = datetime.fromisoformat(reminder.get('reminderTime').replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
                    
                    result += f"{i}. {task_title} - {reminder_time}\n"
                    
                    if reminder.get('taskDueDate'):
                        due_date = datetime.fromisoformat(reminder.get('taskDueDate').replace('Z', '+00:00')).strftime('%Y-%m-%d')
                        result += f"   Due Date: {due_date}\n"
                    
                    if reminder.get('taskPriority'):
                        result += f"   Priority: {reminder.get('taskPriority').capitalize()}\n"
                    
                    result += f"   Task ID: {reminder.get('taskId', '')}\n\n"
                
                return result
            else:
                return f"Error getting task reminders: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_task_reminders: {str(e)}")
            return f"Failed to get task reminders: {str(e)}"

# Define the Task Agent
def create_task_agent(tenant_id: str = None):
    """Create the task agent with tools."""
    # Initialize tools
    tools = [
        GetTasksTool(),
        GetTaskDetailsTool(),
        CreateTaskTool(),
        UpdateTaskTool(),
        CompleteTaskTool(),
        GetTaskRemindersTool()
    ]
    
    # Define the system prompt
    system_prompt = """You are the Task Agent for Aizako CRM, an AI-native customer relationship management system.
Your role is to help users manage their tasks, including creating, updating, and tracking tasks and reminders.

As the Task Agent, you should:
1. Help users view their tasks based on status, due date, or assignee
2. Provide detailed information about specific tasks
3. Create new tasks with the information provided by the user
4. Update existing tasks with new information
5. Mark tasks as completed
6. Show upcoming task reminders

When using tools, make sure to provide all the necessary information.
When creating or updating tasks, ask for any missing required information like title, due date, or priority.
When showing task details or lists, format the information in a clear and readable way.

Remember that you are part of an AI-native CRM system designed to minimize button clicking and maximize natural language interaction.
"""
    
    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ])
    
    # Initialize the LLM
    llm = get_llm()
    
    # Create the agent
    agent = create_openai_tools_agent(llm, tools, prompt)
    
    # Create the agent executor
    agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True,
    )
    
    return agent_executor

# Function to process user input
def process_task_request(
    user_input: str,
    chat_history: List[Union[HumanMessage, AIMessage, SystemMessage]],
    tenant_id: str = None
) -> Dict[str, Any]:
    """
    Process user input related to task management.
    
    Args:
        user_input: The user's message
        chat_history: The conversation history
        tenant_id: The tenant ID for multi-tenancy
        
    Returns:
        Dict containing the response and updated conversation history
    """
    try:
        # Create the task agent
        agent_executor = create_task_agent(tenant_id)
        
        # Process the input
        result = agent_executor.invoke({
            "input": user_input,
            "chat_history": chat_history
        })
        
        # Extract the response
        response = result.get("output", "I couldn't process your request about tasks.")
        
        # Update conversation history
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=response))
        
        return {
            "response": response,
            "chat_history": chat_history
        }
    except Exception as e:
        logger.error(f"Error processing task request: {str(e)}")
        error_message = f"I encountered an error while processing your task request: {str(e)}. Please try again or contact support."
        
        # Update conversation history with error
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=error_message))
        
        return {
            "response": error_message,
            "chat_history": chat_history
        }
