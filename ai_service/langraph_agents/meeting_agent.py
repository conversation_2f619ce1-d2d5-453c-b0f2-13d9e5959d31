"""
Meeting Agent for Aizako CRM

This module implements the meeting agent that handles scheduling, preparation,
and follow-up for meetings in the Aizako CRM system.
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
import logging
from langchain.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
API_URL = os.environ.get("API_URL", "http://localhost:5000")

# Initialize language model
def get_llm(temperature=0.2, model="gpt-4o"):
    """Get the language model with specified parameters."""
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable not set")
    
    return ChatOpenAI(
        model=model,
        temperature=temperature,
        api_key=OPENAI_API_KEY
    )

# Define meeting-related tools
class GetUpcomingMeetingsTool(BaseTool):
    name = "get_upcoming_meetings"
    description = "Get a list of upcoming meetings from connected calendars."
    
    def _run(
        self, 
        days_ahead: int = 7,
        tenant_id: str = None
    ) -> str:
        """Get upcoming meetings."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            # Calculate date range
            start_date = datetime.now().isoformat()
            end_date = (datetime.now() + timedelta(days=days_ahead)).isoformat()
            
            response = requests.get(
                f"{API_URL}/api/meetings/upcoming",
                params={"startDate": start_date, "endDate": end_date},
                headers=headers
            )
            
            if response.status_code == 200:
                meetings = response.json()
                if not meetings:
                    return f"No upcoming meetings found in the next {days_ahead} days."
                
                result = f"Upcoming meetings for the next {days_ahead} days:\n\n"
                for i, meeting in enumerate(meetings, 1):
                    start_time = datetime.fromisoformat(meeting.get('start', {}).get('dateTime', '')).strftime('%Y-%m-%d %H:%M')
                    result += f"{i}. {meeting.get('title', 'Untitled Meeting')} - {start_time}\n"
                    
                    if meeting.get('attendees'):
                        attendees = ", ".join([a.get('name', a.get('email', 'Unknown')) for a in meeting.get('attendees', [])])
                        result += f"   Attendees: {attendees}\n"
                        
                    if meeting.get('location'):
                        result += f"   Location: {meeting.get('location')}\n"
                        
                    result += f"   ID: {meeting.get('id', '')}\n\n"
                
                return result
            else:
                return f"Error getting upcoming meetings: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_upcoming_meetings: {str(e)}")
            return f"Failed to get upcoming meetings: {str(e)}"

class GetMeetingDetailsTool(BaseTool):
    name = "get_meeting_details"
    description = "Get detailed information about a specific meeting by ID."
    
    def _run(
        self, 
        meeting_id: str,
        tenant_id: str = None
    ) -> str:
        """Get detailed information about a meeting."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/meetings/{meeting_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                meeting = response.json()
                
                # Format start and end times
                start_time = datetime.fromisoformat(meeting.get('start', {}).get('dateTime', '')).strftime('%Y-%m-%d %H:%M')
                end_time = datetime.fromisoformat(meeting.get('end', {}).get('dateTime', '')).strftime('%Y-%m-%d %H:%M')
                
                result = f"Meeting Details for {meeting.get('title', 'Untitled Meeting')}:\n\n"
                result += f"ID: {meeting.get('id', '')}\n"
                result += f"Start: {start_time}\n"
                result += f"End: {end_time}\n"
                
                if meeting.get('location'):
                    result += f"Location: {meeting.get('location')}\n"
                    
                if meeting.get('description'):
                    result += f"Description: {meeting.get('description')}\n"
                
                if meeting.get('attendees'):
                    result += "\nAttendees:\n"
                    for attendee in meeting.get('attendees', []):
                        name = attendee.get('name', 'Unknown')
                        email = attendee.get('email', 'No email')
                        status = attendee.get('status', 'Unknown status')
                        result += f"- {name} ({email}) - {status}\n"
                
                if meeting.get('conferenceData'):
                    result += f"\nConference Link: {meeting.get('conferenceData', {}).get('joinUrl', 'No link available')}\n"
                
                return result
            else:
                return f"Error getting meeting details: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_meeting_details: {str(e)}")
            return f"Failed to get meeting details: {str(e)}"

class ScheduleMeetingTool(BaseTool):
    name = "schedule_meeting"
    description = "Schedule a new meeting with contacts or companies."
    
    def _run(
        self, 
        title: str,
        start_time: str,
        end_time: str,
        attendees: str,
        location: str = None,
        description: str = None,
        contact_ids: str = None,
        company_ids: str = None,
        opportunity_ids: str = None,
        tenant_id: str = None
    ) -> str:
        """Schedule a new meeting."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            # Parse attendees string into a list of email addresses
            attendee_list = [email.strip() for email in attendees.split(',')]
            
            # Parse IDs if provided
            contact_id_list = contact_ids.split(',') if contact_ids else []
            company_id_list = company_ids.split(',') if company_ids else []
            opportunity_id_list = opportunity_ids.split(',') if opportunity_ids else []
            
            data = {
                "title": title,
                "start": {
                    "dateTime": start_time
                },
                "end": {
                    "dateTime": end_time
                },
                "attendees": [{"email": email} for email in attendee_list],
                "contactIds": contact_id_list,
                "companyIds": company_id_list,
                "opportunityIds": opportunity_id_list
            }
            
            if location:
                data["location"] = location
            if description:
                data["description"] = description
                
            response = requests.post(
                f"{API_URL}/api/meetings",
                json=data,
                headers=headers
            )
            
            if response.status_code == 201:
                meeting = response.json()
                return f"Meeting scheduled successfully!\n\nTitle: {meeting.get('title')}\nTime: {start_time} to {end_time}\nAttendees: {attendees}"
            else:
                return f"Error scheduling meeting: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in schedule_meeting: {str(e)}")
            return f"Failed to schedule meeting: {str(e)}"

class GenerateMeetingPrepTool(BaseTool):
    name = "generate_meeting_prep"
    description = "Generate a meeting preparation document with key information about attendees, companies, and opportunities."
    
    def _run(
        self, 
        meeting_id: str = None,
        contact_ids: str = None,
        company_ids: str = None,
        opportunity_ids: str = None,
        tenant_id: str = None
    ) -> str:
        """Generate meeting preparation document."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            # Parse IDs if provided
            data = {}
            
            if meeting_id:
                data["meetingId"] = meeting_id
            
            if contact_ids:
                data["contactIds"] = contact_ids.split(',')
            
            if company_ids:
                data["companyIds"] = company_ids.split(',')
            
            if opportunity_ids:
                data["opportunityIds"] = opportunity_ids.split(',')
                
            if not data:
                return "Please provide at least one of: meeting_id, contact_ids, company_ids, or opportunity_ids."
                
            response = requests.post(
                f"{API_URL}/api/meeting-prep/generate",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                prep_doc = response.json()
                
                # Return a summary of the generated document
                return f"Meeting preparation document generated successfully!\n\nTitle: {prep_doc.get('title')}\n\nSummary:\n{prep_doc.get('summary')}\n\nDocument ID: {prep_doc.get('id')}"
            else:
                return f"Error generating meeting prep: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in generate_meeting_prep: {str(e)}")
            return f"Failed to generate meeting prep: {str(e)}"

class CreateMeetingNotesTool(BaseTool):
    name = "create_meeting_notes"
    description = "Create notes for a meeting, which will be associated with the meeting and relevant contacts, companies, and opportunities."
    
    def _run(
        self, 
        meeting_id: str,
        notes: str,
        action_items: str = None,
        tenant_id: str = None
    ) -> str:
        """Create meeting notes."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            # Parse action items if provided
            action_item_list = []
            if action_items:
                for item in action_items.split('\n'):
                    if item.strip():
                        action_item_list.append({"description": item.strip()})
                
            data = {
                "meetingId": meeting_id,
                "notes": notes,
                "actionItems": action_item_list
            }
                
            response = requests.post(
                f"{API_URL}/api/meeting-notes",
                json=data,
                headers=headers
            )
            
            if response.status_code == 201:
                meeting_notes = response.json()
                return f"Meeting notes created successfully!\n\nID: {meeting_notes.get('id')}\nMeeting ID: {meeting_id}\nAction Items: {len(action_item_list)}"
            else:
                return f"Error creating meeting notes: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in create_meeting_notes: {str(e)}")
            return f"Failed to create meeting notes: {str(e)}"

class GetAvailableTimeSlotsTool(BaseTool):
    name = "get_available_time_slots"
    description = "Get available time slots for scheduling a meeting based on attendees' calendars."
    
    def _run(
        self, 
        attendees: str,
        duration_minutes: int = 60,
        days_ahead: int = 7,
        tenant_id: str = None
    ) -> str:
        """Get available time slots for scheduling."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            # Parse attendees string into a list of email addresses
            attendee_list = [email.strip() for email in attendees.split(',')]
            
            # Calculate date range
            start_date = datetime.now().isoformat()
            end_date = (datetime.now() + timedelta(days=days_ahead)).isoformat()
            
            data = {
                "attendees": attendee_list,
                "durationMinutes": duration_minutes,
                "startDate": start_date,
                "endDate": end_date
            }
                
            response = requests.post(
                f"{API_URL}/api/meetings/available-slots",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                slots = response.json()
                
                if not slots:
                    return f"No available time slots found for the next {days_ahead} days with all attendees."
                
                result = f"Available time slots for a {duration_minutes}-minute meeting with all attendees:\n\n"
                for i, slot in enumerate(slots[:10], 1):
                    start_time = datetime.fromisoformat(slot.get('start')).strftime('%Y-%m-%d %H:%M')
                    end_time = datetime.fromisoformat(slot.get('end')).strftime('%H:%M')
                    result += f"{i}. {start_time} - {end_time}\n"
                
                if len(slots) > 10:
                    result += f"\n...and {len(slots) - 10} more slots available."
                
                return result
            else:
                return f"Error getting available time slots: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_available_time_slots: {str(e)}")
            return f"Failed to get available time slots: {str(e)}"

# Define the Meeting Agent
def create_meeting_agent(tenant_id: str = None):
    """Create the meeting agent with tools."""
    # Initialize tools
    tools = [
        GetUpcomingMeetingsTool(),
        GetMeetingDetailsTool(),
        ScheduleMeetingTool(),
        GenerateMeetingPrepTool(),
        CreateMeetingNotesTool(),
        GetAvailableTimeSlotsTool()
    ]
    
    # Define the system prompt
    system_prompt = """You are the Meeting Agent for Aizako CRM, an AI-native customer relationship management system.
Your role is to help users schedule, prepare for, and follow up on meetings with contacts, companies, and opportunities.

As the Meeting Agent, you should:
1. Help users view their upcoming meetings
2. Provide detailed information about specific meetings
3. Schedule new meetings with contacts and companies
4. Generate meeting preparation documents with key information
5. Create meeting notes and action items
6. Find available time slots for scheduling meetings

When using tools, make sure to provide all the necessary information.
When scheduling meetings, ask for any missing required information like title, start time, end time, and attendees.
When generating meeting prep documents, make sure to include relevant contact, company, or opportunity IDs.
When creating meeting notes, ask for detailed notes and any action items.

Remember that you are part of an AI-native CRM system designed to minimize button clicking and maximize natural language interaction.
"""
    
    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ])
    
    # Initialize the LLM
    llm = get_llm()
    
    # Create the agent
    agent = create_openai_tools_agent(llm, tools, prompt)
    
    # Create the agent executor
    agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True,
    )
    
    return agent_executor

# Function to process user input
def process_meeting_request(
    user_input: str,
    chat_history: List[Union[HumanMessage, AIMessage, SystemMessage]],
    tenant_id: str = None
) -> Dict[str, Any]:
    """
    Process user input related to meeting management.
    
    Args:
        user_input: The user's message
        chat_history: The conversation history
        tenant_id: The tenant ID for multi-tenancy
        
    Returns:
        Dict containing the response and updated conversation history
    """
    try:
        # Create the meeting agent
        agent_executor = create_meeting_agent(tenant_id)
        
        # Process the input
        result = agent_executor.invoke({
            "input": user_input,
            "chat_history": chat_history
        })
        
        # Extract the response
        response = result.get("output", "I couldn't process your request about meetings.")
        
        # Update conversation history
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=response))
        
        return {
            "response": response,
            "chat_history": chat_history
        }
    except Exception as e:
        logger.error(f"Error processing meeting request: {str(e)}")
        error_message = f"I encountered an error while processing your meeting request: {str(e)}. Please try again or contact support."
        
        # Update conversation history with error
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=error_message))
        
        return {
            "response": error_message,
            "chat_history": chat_history
        }
