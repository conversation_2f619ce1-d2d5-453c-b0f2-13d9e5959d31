"""
Contact Agent for Aizako CRM

This module implements the contact agent that handles contact management operations
in the Aizako CRM system.
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
import logging
from langchain.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
API_URL = os.environ.get("API_URL", "http://localhost:5000")

# Initialize language model
def get_llm(temperature=0.2, model="gpt-4o"):
    """Get the language model with specified parameters."""
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable not set")
    
    return ChatOpenAI(
        model=model,
        temperature=temperature,
        api_key=OPENAI_API_KEY
    )

# Define contact-related tools
class SearchContactsTool(BaseTool):
    name = "search_contacts"
    description = "Search for contacts in the CRM system based on name, email, company, or other criteria."
    
    def _run(self, query: str, tenant_id: str = None) -> str:
        """Search for contacts based on the query."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/contacts/search",
                params={"q": query},
                headers=headers
            )
            
            if response.status_code == 200:
                contacts = response.json()
                if not contacts:
                    return "No contacts found matching your search criteria."
                
                result = "Found the following contacts:\n\n"
                for i, contact in enumerate(contacts[:5], 1):
                    result += f"{i}. {contact.get('firstName', '')} {contact.get('lastName', '')}\n"
                    result += f"   Email: {contact.get('email', 'N/A')}\n"
                    result += f"   Phone: {contact.get('phone', 'N/A')}\n"
                    result += f"   Title: {contact.get('title', 'N/A')}\n"
                    result += f"   ID: {contact.get('_id', '')}\n\n"
                
                if len(contacts) > 5:
                    result += f"...and {len(contacts) - 5} more contacts. Please refine your search if needed."
                
                return result
            else:
                return f"Error searching contacts: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in search_contacts: {str(e)}")
            return f"Failed to search contacts: {str(e)}"

class GetContactDetailsTool(BaseTool):
    name = "get_contact_details"
    description = "Get detailed information about a specific contact by ID."
    
    def _run(self, contact_id: str, tenant_id: str = None) -> str:
        """Get detailed information about a contact."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/contacts/{contact_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                contact = response.json()
                result = f"Contact Details for {contact.get('firstName', '')} {contact.get('lastName', '')}:\n\n"
                result += f"ID: {contact.get('_id', '')}\n"
                result += f"Email: {contact.get('email', 'N/A')}\n"
                result += f"Phone: {contact.get('phone', 'N/A')}\n"
                result += f"Title: {contact.get('title', 'N/A')}\n"
                result += f"Company: {contact.get('companyName', 'N/A')}\n"
                result += f"Status: {contact.get('status', 'N/A')}\n"
                result += f"Source: {contact.get('source', 'N/A')}\n"
                
                if contact.get('notes'):
                    result += f"\nNotes: {contact.get('notes')}\n"
                
                if contact.get('lastContactedAt'):
                    result += f"\nLast Contacted: {contact.get('lastContactedAt')}\n"
                
                if contact.get('score') and contact.get('score').get('current'):
                    result += f"\nLead Score: {contact.get('score').get('current')}/100\n"
                
                return result
            else:
                return f"Error getting contact details: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_contact_details: {str(e)}")
            return f"Failed to get contact details: {str(e)}"

class CreateContactTool(BaseTool):
    name = "create_contact"
    description = "Create a new contact in the CRM system."
    
    def _run(
        self, 
        first_name: str, 
        last_name: str, 
        email: str = None, 
        phone: str = None,
        title: str = None,
        company_id: str = None,
        status: str = "lead",
        source: str = None,
        notes: str = None,
        tenant_id: str = None
    ) -> str:
        """Create a new contact."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            data = {
                "firstName": first_name,
                "lastName": last_name,
                "status": status
            }
            
            if email:
                data["email"] = email
            if phone:
                data["phone"] = phone
            if title:
                data["title"] = title
            if company_id:
                data["companyId"] = company_id
            if source:
                data["source"] = source
            if notes:
                data["notes"] = notes
                
            response = requests.post(
                f"{API_URL}/api/contacts",
                json=data,
                headers=headers
            )
            
            if response.status_code == 201:
                contact = response.json()
                return f"Contact created successfully!\n\nID: {contact.get('_id')}\nName: {contact.get('firstName')} {contact.get('lastName')}"
            else:
                return f"Error creating contact: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in create_contact: {str(e)}")
            return f"Failed to create contact: {str(e)}"

class UpdateContactTool(BaseTool):
    name = "update_contact"
    description = "Update an existing contact in the CRM system."
    
    def _run(
        self, 
        contact_id: str,
        first_name: str = None,
        last_name: str = None,
        email: str = None, 
        phone: str = None,
        title: str = None,
        company_id: str = None,
        status: str = None,
        source: str = None,
        notes: str = None,
        tenant_id: str = None
    ) -> str:
        """Update an existing contact."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            data = {}
            
            if first_name:
                data["firstName"] = first_name
            if last_name:
                data["lastName"] = last_name
            if email:
                data["email"] = email
            if phone:
                data["phone"] = phone
            if title:
                data["title"] = title
            if company_id:
                data["companyId"] = company_id
            if status:
                data["status"] = status
            if source:
                data["source"] = source
            if notes:
                data["notes"] = notes
                
            if not data:
                return "No updates provided. Please specify at least one field to update."
                
            response = requests.put(
                f"{API_URL}/api/contacts/{contact_id}",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                contact = response.json()
                return f"Contact updated successfully!\n\nID: {contact.get('_id')}\nName: {contact.get('firstName')} {contact.get('lastName')}"
            else:
                return f"Error updating contact: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in update_contact: {str(e)}")
            return f"Failed to update contact: {str(e)}"

class GetContactInteractionsTool(BaseTool):
    name = "get_contact_interactions"
    description = "Get the interaction timeline for a specific contact."
    
    def _run(
        self, 
        contact_id: str, 
        limit: int = 5,
        interaction_type: str = None,
        tenant_id: str = None
    ) -> str:
        """Get interactions for a contact."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            params = {"limit": limit}
            if interaction_type:
                params["types"] = interaction_type
                
            response = requests.get(
                f"{API_URL}/api/contacts/{contact_id}/interactions",
                params=params,
                headers=headers
            )
            
            if response.status_code == 200:
                interactions = response.json()
                if not interactions:
                    return "No interactions found for this contact."
                
                result = f"Recent interactions for contact (ID: {contact_id}):\n\n"
                for i, interaction in enumerate(interactions, 1):
                    result += f"{i}. {interaction.get('type', 'Interaction').capitalize()} on {interaction.get('timestamp', 'Unknown date')}\n"
                    result += f"   Summary: {interaction.get('summary', 'No summary available')}\n"
                    
                    if interaction.get('sentiment'):
                        result += f"   Sentiment: {interaction.get('sentiment').capitalize()}\n"
                        
                    if interaction.get('nextAction'):
                        result += f"   Next Action: {interaction.get('nextAction').get('description', 'None')}\n"
                        
                    result += "\n"
                
                return result
            else:
                return f"Error getting contact interactions: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_contact_interactions: {str(e)}")
            return f"Failed to get contact interactions: {str(e)}"

class SyncContactInteractionsTool(BaseTool):
    name = "sync_contact_interactions"
    description = "Sync interactions for a contact from external sources like email and calendar."
    
    def _run(
        self, 
        contact_id: str,
        sources: str = "email,calendar",
        tenant_id: str = None
    ) -> str:
        """Sync interactions for a contact."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            source_list = sources.split(",")
            data = {"sources": source_list}
                
            response = requests.post(
                f"{API_URL}/api/contacts/{contact_id}/interactions/sync",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                return f"Successfully synced interactions!\n\nAdded: {result.get('added', 0)}\nUpdated: {result.get('updated', 0)}\nErrors: {result.get('errors', 0)}"
            else:
                return f"Error syncing interactions: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in sync_contact_interactions: {str(e)}")
            return f"Failed to sync interactions: {str(e)}"

# Define the Contact Agent
def create_contact_agent(tenant_id: str = None):
    """Create the contact agent with tools."""
    # Initialize tools
    tools = [
        SearchContactsTool(),
        GetContactDetailsTool(),
        CreateContactTool(),
        UpdateContactTool(),
        GetContactInteractionsTool(),
        SyncContactInteractionsTool()
    ]
    
    # Define the system prompt
    system_prompt = """You are the Contact Agent for Aizako CRM, an AI-native customer relationship management system.
Your role is to help users manage their contacts, including searching, creating, updating, and viewing contact details and interactions.

As the Contact Agent, you should:
1. Help users find contacts based on their search criteria
2. Provide detailed information about specific contacts
3. Create new contacts with the information provided by the user
4. Update existing contacts with new information
5. Show recent interactions for contacts
6. Sync interactions from external sources like email and calendar

When using tools, make sure to provide all the necessary information.
When creating or updating contacts, ask for any missing required information.
When showing contact details or interactions, format the information in a clear and readable way.

Remember that you are part of an AI-native CRM system designed to minimize button clicking and maximize natural language interaction.
"""
    
    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ])
    
    # Initialize the LLM
    llm = get_llm()
    
    # Create the agent
    agent = create_openai_tools_agent(llm, tools, prompt)
    
    # Create the agent executor
    agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True,
    )
    
    return agent_executor

# Function to process user input
def process_contact_request(
    user_input: str,
    chat_history: List[Union[HumanMessage, AIMessage, SystemMessage]],
    tenant_id: str = None
) -> Dict[str, Any]:
    """
    Process user input related to contact management.
    
    Args:
        user_input: The user's message
        chat_history: The conversation history
        tenant_id: The tenant ID for multi-tenancy
        
    Returns:
        Dict containing the response and updated conversation history
    """
    try:
        # Create the contact agent
        agent_executor = create_contact_agent(tenant_id)
        
        # Process the input
        result = agent_executor.invoke({
            "input": user_input,
            "chat_history": chat_history
        })
        
        # Extract the response
        response = result.get("output", "I couldn't process your request about contacts.")
        
        # Update conversation history
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=response))
        
        return {
            "response": response,
            "chat_history": chat_history
        }
    except Exception as e:
        logger.error(f"Error processing contact request: {str(e)}")
        error_message = f"I encountered an error while processing your contact request: {str(e)}. Please try again or contact support."
        
        # Update conversation history with error
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=error_message))
        
        return {
            "response": error_message,
            "chat_history": chat_history
        }
