import os
import yaml
import json
from typing import Dict, Any, List, Optional
from langchain.agents import AgentExecutor
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.messages import AIMessage, HumanMessage
from langchain_openai import ChatOpenAI
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
VOYAGE_API_KEY = os.getenv("VOYAGE_API_KEY")

# Define the system prompt for workflow parsing
WORKFLOW_SYSTEM_PROMPT = """You are an expert workflow compiler that converts natural language descriptions into YAML workflow definitions.

Your task is to parse the user's natural language description of a workflow and convert it into a structured YAML format that follows this schema:

```yaml
workflow:
  name: string
  description: string
  trigger:
    type: string
    config: object
  nodes:
    - id: string
      type: string (one of: action, condition)
      data:
        type: string
        config: object
  edges:
    - source: string (node id)
      target: string (node id)
      condition: object (optional)
```

For triggers, use these types:
- Basic triggers:
  - lead_score_change: Triggered when a lead's score changes
  - email_event: Triggered by email events (open, click, reply)
  - form_submission: Triggered when a form is submitted
  - record_update: Triggered when a record is updated
  - webhook: Triggered by an external webhook
- Time-based triggers:
  - scheduled: Run at a specific time or on a recurring schedule
  - anniversary: Run on anniversaries like customer sign-up date
- Behavioral triggers:
  - website_activity: Triggered by specific website activity patterns
  - product_usage: Triggered by product usage metrics
- External system triggers:
  - linkedin_event: Triggered by LinkedIn events
- Advanced CRM triggers:
  - relationship_change: Triggered when a relationship between contacts changes
  - pipeline_velocity: Triggered when deal movement slows or speeds up
  - team_performance: Triggered based on team performance metrics

For actions, use these types:
- Basic actions:
  - send_email: Send an email to a contact
  - update_record: Update a record in the CRM
  - create_task: Create a task for a user
  - call_webhook: Call an external webhook
- Communication actions:
  - send_sms: Send text messages to contacts
  - schedule_meeting: Automatically schedule meetings
  - create_video_message: Generate personalized video messages
- Data enrichment actions:
  - enrich_contact: Automatically enrich contact data
  - score_lead: Update lead scoring based on custom algorithms
  - generate_ai_content: Create personalized content using AI
- Integration actions:
  - create_document: Generate documents like proposals
  - update_external_system: Update records in external systems
  - post_to_linkedin: Create LinkedIn posts
- Advanced CRM actions:
  - create_relationship: Establish relationships between contacts
  - assign_territory: Automatically assign territories
  - forecast_update: Update sales forecasts based on deal changes

For conditions, use these types:
- Basic conditions:
  - if: Check if a condition is true
  - wait_until: Wait until a condition is true
  - for_each: Loop through a collection
- Advanced conditions:
  - switch: Branch based on multiple conditions
  - parallel: Execute multiple paths in parallel
  - retry: Retry an action if it fails
  - delay: Wait for a specific amount of time
- Data conditions:
  - data_exists: Check if data exists
  - data_equals: Check if data equals a value
  - data_greater_than: Check if data is greater than a value
  - data_less_than: Check if data is less than a value
- Time conditions:
  - time_of_day: Check if current time is within a range
  - day_of_week: Check if current day is a specific day
  - business_hours: Check if current time is within business hours

Make sure to:
1. Create a clear, descriptive name for the workflow
2. Identify the correct trigger type based on the description
3. Create appropriate nodes for each action and condition
4. Connect the nodes with edges in the correct order
5. Add any necessary conditions to the edges

You can use the following functions to help you:
- parse_workflow: Parse a natural language description into a workflow DSL
- validate_workflow: Validate a workflow DSL against the schema
"""

# Define the functions for the agent
WORKFLOW_FUNCTIONS = [
    {
        "name": "parse_workflow",
        "description": "Parse a natural language description into a workflow DSL",
        "parameters": {
            "type": "object",
            "properties": {
                "dsl_yaml": {
                    "type": "string",
                    "description": "The YAML representation of the workflow DSL"
                }
            },
            "required": ["dsl_yaml"]
        }
    },
    {
        "name": "validate_workflow",
        "description": "Validate a workflow DSL against the schema",
        "parameters": {
            "type": "object",
            "properties": {
                "dsl_yaml": {
                    "type": "string",
                    "description": "The YAML representation of the workflow DSL"
                }
            },
            "required": ["dsl_yaml"]
        }
    }
]

# Create the prompt template
workflow_prompt = ChatPromptTemplate.from_messages([
    ("system", WORKFLOW_SYSTEM_PROMPT),
    MessagesPlaceholder(variable_name="agent_scratchpad"),
    ("human", "{input}")
])

# Create the LLM
def get_llm():
    """Get the LLM to use for the agent."""
    if OPENAI_API_KEY:
        return ChatOpenAI(temperature=0.2, model="gpt-4o")
    elif VOYAGE_API_KEY:
        return ChatOpenAI(
            temperature=0.2,
            model="voyage-3",
            openai_api_key=VOYAGE_API_KEY,
            openai_api_base="https://api.voyageai.com/v1"
        )
    else:
        raise ValueError("No API key found for OpenAI or Voyage")

# Create the agent
def create_workflow_agent():
    """Create the workflow agent."""
    llm = get_llm()
    llm_with_tools = llm.bind_functions(WORKFLOW_FUNCTIONS)

    agent = (
        {
            "input": lambda x: x["input"],
            "agent_scratchpad": lambda x: format_to_openai_function_messages(x["intermediate_steps"])
        }
        | workflow_prompt
        | llm_with_tools
        | OpenAIFunctionsAgentOutputParser()
    )

    return AgentExecutor(agent=agent, tools=[], verbose=True)

# Process a workflow request
def process_workflow_request(prompt: str) -> Dict[str, Any]:
    """Process a workflow request."""
    try:
        # Create the agent
        agent = create_workflow_agent()

        # Run the agent
        result = agent.invoke({"input": prompt})

        # Extract the DSL YAML from the result
        output = result.get("output", "")

        # Try to find YAML content in the output
        dsl_yaml = output

        # If the output contains markdown code blocks, extract the YAML
        if "```yaml" in output:
            dsl_yaml = output.split("```yaml")[1].split("```")[0].strip()
        elif "```" in output:
            dsl_yaml = output.split("```")[1].split("```")[0].strip()

        # Parse the YAML to validate it
        try:
            workflow_data = yaml.safe_load(dsl_yaml)

            # Generate a simple graph representation
            nodes = []
            edges = []

            if isinstance(workflow_data, dict) and "workflow" in workflow_data:
                workflow = workflow_data["workflow"]

                # Add trigger node
                trigger_id = "trigger"
                nodes.append({
                    "id": trigger_id,
                    "type": "trigger",
                    "position": {"x": 100, "y": 100},
                    "data": {
                        "type": workflow.get("trigger", {}).get("type", "unknown"),
                        "config": workflow.get("trigger", {}).get("config", {}),
                        "description": f"Trigger: {workflow.get('trigger', {}).get('type', 'unknown')}"
                    }
                })

                # Add other nodes
                y_position = 200
                for i, node in enumerate(workflow.get("nodes", [])):
                    node_id = node.get("id", f"node_{i}")
                    nodes.append({
                        "id": node_id,
                        "type": node.get("type", "action"),
                        "position": {"x": 100, "y": y_position},
                        "data": {
                            "type": node.get("data", {}).get("type", "unknown"),
                            "config": node.get("data", {}).get("config", {}),
                            "description": f"{node.get('type', 'Action')}: {node.get('data', {}).get('type', 'unknown')}"
                        }
                    })
                    y_position += 100

                # Add edges
                for i, edge in enumerate(workflow.get("edges", [])):
                    edges.append({
                        "id": f"edge_{i}",
                        "source": edge.get("source", ""),
                        "target": edge.get("target", ""),
                        "label": "if" if edge.get("condition") else None,
                        "condition": edge.get("condition")
                    })

            return {
                "success": True,
                "dsl_yaml": dsl_yaml,
                "nodes": nodes,
                "edges": edges
            }
        except Exception as e:
            logger.error(f"Error parsing YAML: {e}")
            return {
                "success": False,
                "error": f"Invalid YAML: {str(e)}"
            }
    except Exception as e:
        logger.error(f"Error processing workflow request: {e}")
        return {
            "success": False,
            "error": f"Failed to process workflow request: {str(e)}"
        }
