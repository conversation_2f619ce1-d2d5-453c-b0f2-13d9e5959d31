import os
import json
from typing import Dict, Any, List, Optional
from langchain.agents import AgentExecutor
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.messages import AIMessage, HumanMessage
from langchain_openai import ChatOpenAI
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
VOYAGE_API_KEY = os.getenv("VOYAGE_API_KEY")

# Define the system prompt for insights generation
INSIGHTS_SYSTEM_PROMPT = """You are an expert business analyst that generates insights from CRM data.

Your task is to analyze the user's question about their CRM data and generate a comprehensive insight that includes:
1. A narrative explanation (200 words max)
2. A chart specification
3. "Why it matters" bullet points
4. Recommended actions

You have access to the following CRM data:
- Contacts: Information about individuals the company interacts with
- Companies: Information about organizations the company does business with
- Opportunities: Sales opportunities at various stages in the pipeline
- Activities: Interactions with contacts and companies
- Email tracking: Data about email opens, clicks, and replies

You can use the following functions to help you:
- query_data: Query the CRM data to get information relevant to the question
- generate_chart: Generate a chart specification based on the data
- generate_recommendations: Generate recommended actions based on the insights

Your response should be comprehensive, data-driven, and actionable. Focus on providing clear insights that help the user make better business decisions.
"""

# Define the functions for the agent
INSIGHTS_FUNCTIONS = [
    {
        "name": "query_data",
        "description": "Query the CRM data to get information relevant to the question",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The query to run against the CRM data"
                },
                "data_source": {
                    "type": "string",
                    "description": "The data source to query (contacts, companies, opportunities, activities, email_tracking)",
                    "enum": ["contacts", "companies", "opportunities", "activities", "email_tracking"]
                }
            },
            "required": ["query", "data_source"]
        }
    },
    {
        "name": "generate_chart",
        "description": "Generate a chart specification based on the data",
        "parameters": {
            "type": "object",
            "properties": {
                "chart_type": {
                    "type": "string",
                    "description": "The type of chart to generate",
                    "enum": [
                        "bar", "line", "pie", "funnel", "scatter", "table",
                        "sankey", "heatmap", "radar", "treemap", "bubble",
                        "pipeline_waterfall", "cohort_analysis", "win_loss_analysis", "sales_velocity", "forecast_comparison",
                        "forecast", "anomaly_detection", "what_if_analysis", "trend_prediction"
                    ]
                },
                "interactive": {
                    "type": "boolean",
                    "description": "Whether the chart should be interactive"
                },
                "drilldown": {
                    "type": "boolean",
                    "description": "Whether the chart should support drilling down into details"
                },
                "data": {
                    "type": "object",
                    "description": "The data for the chart"
                },
                "options": {
                    "type": "object",
                    "description": "The options for the chart"
                },
                "title": {
                    "type": "string",
                    "description": "The title of the chart"
                },
                "subtitle": {
                    "type": "string",
                    "description": "The subtitle of the chart"
                }
            },
            "required": ["chart_type", "data", "options"]
        }
    },
    {
        "name": "generate_recommendations",
        "description": "Generate recommended actions based on the insights",
        "parameters": {
            "type": "object",
            "properties": {
                "recommendations": {
                    "type": "array",
                    "description": "The recommended actions",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "The title of the recommendation"
                            },
                            "description": {
                                "type": "string",
                                "description": "The description of the recommendation"
                            },
                            "impact": {
                                "type": "string",
                                "description": "The impact of the recommendation",
                                "enum": ["high", "medium", "low"]
                            },
                            "workflow_template": {
                                "type": "string",
                                "description": "A workflow template for the recommendation"
                            },
                            "module_integration": {
                                "type": "object",
                                "description": "Integration with other CRM modules",
                                "properties": {
                                    "module": {
                                        "type": "string",
                                        "description": "The module to integrate with",
                                        "enum": [
                                            "workflow",
                                            "insights",
                                            "proposal_generator",
                                            "objection_handler",
                                            "follow_up_coach",
                                            "win_loss_analyzer",
                                            "meeting_prep"
                                        ]
                                    },
                                    "action": {
                                        "type": "string",
                                        "description": "The action to take in the module"
                                    },
                                    "params": {
                                        "type": "object",
                                        "description": "Parameters for the action"
                                    }
                                },
                                "required": ["module", "action"]
                            }
                        },
                        "required": ["title", "description", "impact"]
                    }
                }
            },
            "required": ["recommendations"]
        }
    }
]

# Create the prompt template
insights_prompt = ChatPromptTemplate.from_messages([
    ("system", INSIGHTS_SYSTEM_PROMPT),
    MessagesPlaceholder(variable_name="agent_scratchpad"),
    ("human", "{input}")
])

# Create the LLM
def get_llm():
    """Get the LLM to use for the agent."""
    if OPENAI_API_KEY:
        return ChatOpenAI(temperature=0.2, model="gpt-4o")
    elif VOYAGE_API_KEY:
        return ChatOpenAI(
            temperature=0.2,
            model="voyage-3",
            openai_api_key=VOYAGE_API_KEY,
            openai_api_base="https://api.voyageai.com/v1"
        )
    else:
        raise ValueError("No API key found for OpenAI or Voyage")

# Create the agent
def create_insights_agent():
    """Create the insights agent."""
    llm = get_llm()
    llm_with_tools = llm.bind_functions(INSIGHTS_FUNCTIONS)

    agent = (
        {
            "input": lambda x: x["input"],
            "agent_scratchpad": lambda x: format_to_openai_function_messages(x["intermediate_steps"])
        }
        | insights_prompt
        | llm_with_tools
        | OpenAIFunctionsAgentOutputParser()
    )

    return AgentExecutor(agent=agent, tools=[], verbose=True)

# Process an insights request
def process_insights_request(question: str, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Process an insights request."""
    try:
        # Create the agent
        agent = create_insights_agent()

        # Prepare the input
        input_text = question
        if filters:
            input_text += f"\nFilters: {json.dumps(filters)}"

        # Run the agent
        result = agent.invoke({"input": input_text})

        # Extract the output from the result
        output = result.get("output", "")

        # Generate a mock response for demonstration purposes
        # In a real implementation, this would be generated by the agent
        mock_response = {
            "narrative": "Your pipeline for Q2 is showing softness compared to Q1 primarily due to three factors. First, there's been a 23% decrease in new opportunities created during the last 30 days. Second, the average deal size has dropped from $42,500 to $36,800. Third, the conversion rate from qualification to proposal stage has declined from 68% to 51%. This pattern began in early April and has accelerated through May. The technology sector shows the most significant decline, while healthcare remains relatively stable.",
            "chart_spec": {
                "type": "pipeline_waterfall",
                "data": {
                    "labels": ["Starting Q2", "New Opps", "Lost Deals", "Downsized", "Current Q2"],
                    "datasets": [
                        {
                            "label": "Pipeline Changes",
                            "data": [1200000, 350000, -280000, -160000, 1110000],
                            "backgroundColor": [
                                "rgba(54, 162, 235, 0.5)",
                                "rgba(75, 192, 192, 0.5)",
                                "rgba(255, 99, 132, 0.5)",
                                "rgba(255, 159, 64, 0.5)",
                                "rgba(153, 102, 255, 0.5)"
                            ]
                        }
                    ]
                },
                "options": {
                    "scales": {
                        "y": {
                            "beginAtZero": false,
                            "title": {
                                "display": true,
                                "text": "Pipeline Value ($)"
                            }
                        }
                    }
                },
                "title": "Q2 Pipeline Waterfall Analysis",
                "subtitle": "Factors contributing to pipeline changes",
                "interactive": true,
                "drilldown": true
            },
            "why_it_matters": [
                "Revenue targets for Q2 are at risk if the trend continues",
                "The decline in tech sector deals may indicate market shifts requiring strategy adjustment",
                "Qualification to proposal conversion drop suggests potential issues in the sales process"
            ],
            "recommended_plays": [
                {
                    "title": "Accelerate Tech Sector Outreach",
                    "description": "Launch a targeted campaign to technology prospects with personalized outreach from account executives",
                    "impact": "high",
                    "workflow_template": "When a tech sector lead is identified, assign to top-performing AE, send personalized email, and schedule follow-up call within 48 hours",
                    "module_integration": {
                        "module": "workflow",
                        "action": "create_workflow",
                        "params": {
                            "name": "Tech Sector Outreach",
                            "trigger_type": "lead_score_change",
                            "industry": "technology"
                        }
                    }
                },
                {
                    "title": "Generate Targeted Proposals",
                    "description": "Create customized proposals for high-value tech sector opportunities",
                    "impact": "high",
                    "module_integration": {
                        "module": "proposal_generator",
                        "action": "create_template",
                        "params": {
                            "name": "Tech Sector Value Proposition",
                            "industry": "technology",
                            "focus": "cost_savings"
                        }
                    }
                },
                {
                    "title": "Prepare for Common Tech Objections",
                    "description": "Equip sales team with responses to common tech sector objections",
                    "impact": "medium",
                    "module_integration": {
                        "module": "objection_handler",
                        "action": "create_objection_responses",
                        "params": {
                            "industry": "technology",
                            "objection_types": ["price", "implementation", "security"]
                        }
                    }
                },
                {
                    "title": "Analyze Lost Tech Deals",
                    "description": "Conduct detailed analysis of recently lost technology sector deals",
                    "impact": "medium",
                    "module_integration": {
                        "module": "win_loss_analyzer",
                        "action": "analyze_losses",
                        "params": {
                            "industry": "technology",
                            "time_period": "last_90_days"
                        }
                    }
                }
            ],
            "dataset_ref": "pipeline_analysis_q2",
            "sql_query": "SELECT month, SUM(value) FROM opportunities WHERE created_at BETWEEN '2023-01-01' AND '2023-06-30' GROUP BY month",
            "graph_query": "MATCH (o:Opportunity)-[:IN_INDUSTRY]->(i:Industry {name: 'Technology'}) WHERE o.created_at >= '2023-04-01' RETURN o",
            "feature_weights": {
                "new_opportunities_decline": 0.4,
                "deal_size_reduction": 0.3,
                "conversion_rate_drop": 0.3
            }
        }

        return {
            "success": True,
            **mock_response
        }
    except Exception as e:
        logger.error(f"Error processing insights request: {e}")
        return {
            "success": False,
            "error": f"Failed to process insights request: {str(e)}"
        }
