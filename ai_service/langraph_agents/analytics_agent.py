"""
Analytics Agent for Aizako CRM

This module implements the analytics agent that generates reports, answers data questions,
and surfaces insights in the Aizako CRM system.
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
import logging
from langchain.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
API_URL = os.environ.get("API_URL", "http://localhost:5000")

# Initialize language model
def get_llm(temperature=0.2, model="gpt-4o"):
    """Get the language model with specified parameters."""
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable not set")
    
    return ChatOpenAI(
        model=model,
        temperature=temperature,
        api_key=OPENAI_API_KEY
    )

# Define analytics-related tools
class GetSalesDashboardTool(BaseTool):
    name = "get_sales_dashboard"
    description = "Get a summary of key sales metrics and KPIs for the dashboard."
    
    def _run(
        self, 
        period: str = "current_month",
        compare_to: str = "previous_period",
        tenant_id: str = None
    ) -> str:
        """Get sales dashboard metrics."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/analytics/sales-dashboard",
                params={"period": period, "compareTo": compare_to},
                headers=headers
            )
            
            if response.status_code == 200:
                dashboard = response.json()
                
                result = f"Sales Dashboard for {dashboard.get('periodName', period)}:\n\n"
                
                # Revenue metrics
                revenue = dashboard.get('revenue', {})
                result += "Revenue Metrics:\n"
                result += f"- Total Revenue: ${revenue.get('current', 0):,.2f}"
                if revenue.get('previous') is not None:
                    change = ((revenue.get('current', 0) - revenue.get('previous', 0)) / revenue.get('previous', 1)) * 100
                    result += f" ({change:+.1f}% vs {dashboard.get('compareToName', 'previous period')})\n"
                else:
                    result += "\n"
                
                # Deal metrics
                deals = dashboard.get('deals', {})
                result += "\nDeal Metrics:\n"
                result += f"- New Deals: {deals.get('new', 0)}\n"
                result += f"- Won Deals: {deals.get('won', 0)}\n"
                result += f"- Lost Deals: {deals.get('lost', 0)}\n"
                
                if deals.get('winRate') is not None:
                    result += f"- Win Rate: {deals.get('winRate', 0):.1f}%\n"
                
                # Pipeline metrics
                pipeline = dashboard.get('pipeline', {})
                result += "\nPipeline Metrics:\n"
                result += f"- Pipeline Value: ${pipeline.get('value', 0):,.2f}\n"
                result += f"- Average Deal Size: ${pipeline.get('avgDealSize', 0):,.2f}\n"
                result += f"- Conversion Rate: {pipeline.get('conversionRate', 0):.1f}%\n"
                
                # Activity metrics
                activity = dashboard.get('activity', {})
                result += "\nActivity Metrics:\n"
                result += f"- Meetings: {activity.get('meetings', 0)}\n"
                result += f"- Emails: {activity.get('emails', 0)}\n"
                result += f"- Calls: {activity.get('calls', 0)}\n"
                
                return result
            else:
                return f"Error getting sales dashboard: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_sales_dashboard: {str(e)}")
            return f"Failed to get sales dashboard: {str(e)}"

class GetSalesPerformanceTool(BaseTool):
    name = "get_sales_performance"
    description = "Get detailed sales performance metrics by rep, product, or region."
    
    def _run(
        self, 
        dimension: str = "rep",
        period: str = "current_quarter",
        tenant_id: str = None
    ) -> str:
        """Get sales performance metrics."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/analytics/sales-performance",
                params={"dimension": dimension, "period": period},
                headers=headers
            )
            
            if response.status_code == 200:
                performance = response.json()
                
                result = f"Sales Performance by {dimension.capitalize()} for {performance.get('periodName', period)}:\n\n"
                
                # Performance data
                items = performance.get('items', [])
                if not items:
                    return f"No sales performance data available for {dimension} in {period}."
                
                for i, item in enumerate(items, 1):
                    name = item.get('name', f'Item {i}')
                    revenue = item.get('revenue', 0)
                    deals = item.get('deals', 0)
                    win_rate = item.get('winRate', 0)
                    
                    result += f"{i}. {name}\n"
                    result += f"   Revenue: ${revenue:,.2f}\n"
                    result += f"   Deals: {deals}\n"
                    result += f"   Win Rate: {win_rate:.1f}%\n"
                    
                    if item.get('target') and item.get('target') > 0:
                        attainment = (revenue / item.get('target')) * 100
                        result += f"   Target Attainment: {attainment:.1f}%\n"
                    
                    result += "\n"
                
                # Summary
                if performance.get('summary'):
                    summary = performance.get('summary', {})
                    result += "Summary:\n"
                    result += f"- Total Revenue: ${summary.get('totalRevenue', 0):,.2f}\n"
                    result += f"- Total Deals: {summary.get('totalDeals', 0)}\n"
                    result += f"- Average Win Rate: {summary.get('avgWinRate', 0):.1f}%\n"
                
                return result
            else:
                return f"Error getting sales performance: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_sales_performance: {str(e)}")
            return f"Failed to get sales performance: {str(e)}"

class GetPipelineAnalysisTool(BaseTool):
    name = "get_pipeline_analysis"
    description = "Get detailed analysis of the sales pipeline, including stage conversion rates and velocity."
    
    def _run(
        self, 
        period: str = "last_90_days",
        tenant_id: str = None
    ) -> str:
        """Get pipeline analysis."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/analytics/pipeline-analysis",
                params={"period": period},
                headers=headers
            )
            
            if response.status_code == 200:
                analysis = response.json()
                
                result = f"Pipeline Analysis for {analysis.get('periodName', period)}:\n\n"
                
                # Stage metrics
                stages = analysis.get('stages', [])
                if stages:
                    result += "Pipeline Stages:\n"
                    for stage in stages:
                        name = stage.get('name', '').replace('_', ' ').title()
                        count = stage.get('count', 0)
                        value = stage.get('value', 0)
                        conversion = stage.get('conversionRate', 0)
                        velocity = stage.get('velocity', 0)
                        
                        result += f"- {name}:\n"
                        result += f"  Count: {count} deals\n"
                        result += f"  Value: ${value:,.2f}\n"
                        result += f"  Conversion Rate: {conversion:.1f}%\n"
                        result += f"  Avg. Time in Stage: {velocity} days\n\n"
                
                # Funnel metrics
                funnel = analysis.get('funnel', {})
                if funnel:
                    result += "Pipeline Funnel:\n"
                    result += f"- Overall Conversion (Lead to Won): {funnel.get('overallConversion', 0):.1f}%\n"
                    result += f"- Average Sales Cycle: {funnel.get('avgSalesCycle', 0)} days\n"
                    result += f"- Deals in Pipeline: {funnel.get('dealsInPipeline', 0)}\n"
                
                # Bottlenecks
                bottlenecks = analysis.get('bottlenecks', [])
                if bottlenecks:
                    result += "\nPipeline Bottlenecks:\n"
                    for bottleneck in bottlenecks:
                        stage = bottleneck.get('stage', '').replace('_', ' ').title()
                        issue = bottleneck.get('issue', '')
                        impact = bottleneck.get('impact', '')
                        
                        result += f"- {stage}: {issue} (Impact: {impact})\n"
                
                return result
            else:
                return f"Error getting pipeline analysis: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_pipeline_analysis: {str(e)}")
            return f"Failed to get pipeline analysis: {str(e)}"

class GetLeadSourceAnalysisTool(BaseTool):
    name = "get_lead_source_analysis"
    description = "Get analysis of lead sources, including conversion rates and ROI."
    
    def _run(
        self, 
        period: str = "last_90_days",
        tenant_id: str = None
    ) -> str:
        """Get lead source analysis."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            response = requests.get(
                f"{API_URL}/api/analytics/lead-source-analysis",
                params={"period": period},
                headers=headers
            )
            
            if response.status_code == 200:
                analysis = response.json()
                
                result = f"Lead Source Analysis for {analysis.get('periodName', period)}:\n\n"
                
                # Source metrics
                sources = analysis.get('sources', [])
                if not sources:
                    return f"No lead source data available for {period}."
                
                result += "Lead Sources:\n"
                for source in sources:
                    name = source.get('name', 'Unknown')
                    leads = source.get('leads', 0)
                    opportunities = source.get('opportunities', 0)
                    won = source.get('won', 0)
                    revenue = source.get('revenue', 0)
                    cost = source.get('cost', 0)
                    
                    result += f"- {name}:\n"
                    result += f"  Leads: {leads}\n"
                    result += f"  Opportunities: {opportunities}\n"
                    result += f"  Won Deals: {won}\n"
                    result += f"  Revenue: ${revenue:,.2f}\n"
                    
                    if leads > 0:
                        lead_to_opp = (opportunities / leads) * 100
                        result += f"  Lead-to-Opportunity: {lead_to_opp:.1f}%\n"
                    
                    if opportunities > 0:
                        opp_to_won = (won / opportunities) * 100
                        result += f"  Opportunity-to-Won: {opp_to_won:.1f}%\n"
                    
                    if cost > 0:
                        roi = ((revenue - cost) / cost) * 100
                        result += f"  ROI: {roi:.1f}%\n"
                        result += f"  Cost per Lead: ${cost / leads if leads > 0 else 0:,.2f}\n"
                    
                    result += "\n"
                
                # Recommendations
                recommendations = analysis.get('recommendations', [])
                if recommendations:
                    result += "Recommendations:\n"
                    for recommendation in recommendations:
                        result += f"- {recommendation}\n"
                
                return result
            else:
                return f"Error getting lead source analysis: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in get_lead_source_analysis: {str(e)}")
            return f"Failed to get lead source analysis: {str(e)}"

class GenerateCustomReportTool(BaseTool):
    name = "generate_custom_report"
    description = "Generate a custom report based on specific metrics, dimensions, and filters."
    
    def _run(
        self, 
        metrics: str,
        dimensions: str = None,
        filters: str = None,
        period: str = "last_90_days",
        tenant_id: str = None
    ) -> str:
        """Generate a custom report."""
        try:
            headers = {"Content-Type": "application/json"}
            if tenant_id:
                headers["X-Tenant-ID"] = tenant_id
                
            # Parse metrics, dimensions, and filters
            metric_list = [m.strip() for m in metrics.split(',')]
            dimension_list = [d.strip() for d in dimensions.split(',')] if dimensions else []
            filter_list = [f.strip() for f in filters.split(',')] if filters else []
            
            data = {
                "metrics": metric_list,
                "dimensions": dimension_list,
                "filters": filter_list,
                "period": period
            }
                
            response = requests.post(
                f"{API_URL}/api/analytics/custom-report",
                json=data,
                headers=headers
            )
            
            if response.status_code == 200:
                report = response.json()
                
                result = f"Custom Report for {report.get('periodName', period)}:\n\n"
                
                # Report data
                rows = report.get('data', [])
                if not rows:
                    return f"No data available for the specified metrics, dimensions, and filters."
                
                # Get headers
                headers = []
                if dimension_list:
                    headers.extend(dimension_list)
                headers.extend(metric_list)
                
                # Print headers
                header_row = " | ".join(h.replace('_', ' ').title() for h in headers)
                result += f"{header_row}\n"
                result += "-" * len(header_row) + "\n"
                
                # Print data rows
                for row in rows:
                    row_values = []
                    
                    # Add dimension values
                    for dim in dimension_list:
                        row_values.append(str(row.get(dim, 'N/A')))
                    
                    # Add metric values
                    for metric in metric_list:
                        value = row.get(metric, 0)
                        # Format as currency if it looks like a monetary value
                        if metric.lower() in ['revenue', 'value', 'cost', 'amount', 'price']:
                            row_values.append(f"${value:,.2f}")
                        # Format as percentage if it looks like a rate
                        elif metric.lower() in ['rate', 'ratio', 'percentage', 'conversion']:
                            row_values.append(f"{value:.1f}%")
                        else:
                            row_values.append(str(value))
                    
                    result += " | ".join(row_values) + "\n"
                
                # Summary
                if report.get('summary'):
                    result += "\nSummary:\n"
                    for metric, value in report.get('summary', {}).items():
                        metric_name = metric.replace('_', ' ').title()
                        
                        # Format based on metric type
                        if metric.lower() in ['revenue', 'value', 'cost', 'amount', 'price']:
                            result += f"- Total {metric_name}: ${value:,.2f}\n"
                        elif metric.lower() in ['rate', 'ratio', 'percentage', 'conversion']:
                            result += f"- Average {metric_name}: {value:.1f}%\n"
                        else:
                            result += f"- Total {metric_name}: {value}\n"
                
                return result
            else:
                return f"Error generating custom report: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error in generate_custom_report: {str(e)}")
            return f"Failed to generate custom report: {str(e)}"

# Define the Analytics Agent
def create_analytics_agent(tenant_id: str = None):
    """Create the analytics agent with tools."""
    # Initialize tools
    tools = [
        GetSalesDashboardTool(),
        GetSalesPerformanceTool(),
        GetPipelineAnalysisTool(),
        GetLeadSourceAnalysisTool(),
        GenerateCustomReportTool()
    ]
    
    # Define the system prompt
    system_prompt = """You are the Analytics Agent for Aizako CRM, an AI-native customer relationship management system.
Your role is to help users analyze their sales data, generate reports, and surface insights to improve their business.

As the Analytics Agent, you should:
1. Provide key sales metrics and KPIs for the dashboard
2. Generate detailed sales performance reports by rep, product, or region
3. Analyze the sales pipeline, including stage conversion rates and velocity
4. Evaluate lead sources and their effectiveness
5. Create custom reports based on specific metrics, dimensions, and filters
6. Provide actionable insights and recommendations based on the data

When using tools, make sure to provide all the necessary information.
When generating reports, ask for any missing required information like metrics, dimensions, or time periods.
When presenting data, format it in a clear and readable way, highlighting key insights.

Remember that you are part of an AI-native CRM system designed to minimize button clicking and maximize natural language interaction.
"""
    
    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ])
    
    # Initialize the LLM
    llm = get_llm()
    
    # Create the agent
    agent = create_openai_tools_agent(llm, tools, prompt)
    
    # Create the agent executor
    agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True,
    )
    
    return agent_executor

# Function to process user input
def process_analytics_request(
    user_input: str,
    chat_history: List[Union[HumanMessage, AIMessage, SystemMessage]],
    tenant_id: str = None
) -> Dict[str, Any]:
    """
    Process user input related to analytics and reporting.
    
    Args:
        user_input: The user's message
        chat_history: The conversation history
        tenant_id: The tenant ID for multi-tenancy
        
    Returns:
        Dict containing the response and updated conversation history
    """
    try:
        # Create the analytics agent
        agent_executor = create_analytics_agent(tenant_id)
        
        # Process the input
        result = agent_executor.invoke({
            "input": user_input,
            "chat_history": chat_history
        })
        
        # Extract the response
        response = result.get("output", "I couldn't process your analytics request.")
        
        # Update conversation history
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=response))
        
        return {
            "response": response,
            "chat_history": chat_history
        }
    except Exception as e:
        logger.error(f"Error processing analytics request: {str(e)}")
        error_message = f"I encountered an error while processing your analytics request: {str(e)}. Please try again or contact support."
        
        # Update conversation history with error
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=error_message))
        
        return {
            "response": error_message,
            "chat_history": chat_history
        }
