import requests
import json
from datetime import datetime
import logging
from typing import Dict, Any, Optional, List, Union

logger = logging.getLogger(__name__)

class SubscriptionClient:
    """Client for interacting with the subscription system"""
    
    def __init__(self, api_url: str, api_key: Optional[str] = None):
        """Initialize the subscription client
        
        Args:
            api_url: Base URL for the API
            api_key: Optional API key for authentication
        """
        self.api_url = api_url
        self.api_key = api_key
        self.cache = {}
        self.cache_timestamp = None
        self.refresh_cache()
    
    def refresh_cache(self) -> None:
        """Refresh the subscription cache from the API"""
        try:
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            response = requests.get(
                f"{self.api_url}/api/subscription/cache",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                self.cache = response.json()
                self.cache_timestamp = datetime.now()
                logger.info("Subscription cache refreshed successfully")
            else:
                logger.error(f"Failed to refresh subscription cache: {response.status_code} {response.text}")
        except Exception as e:
            logger.error(f"Error refreshing subscription cache: {str(e)}")
    
    def check_entitlement(self, tenant_id: str, feature_key: str) -> bool:
        """Check if a tenant is entitled to use a feature
        
        Args:
            tenant_id: ID of the tenant
            feature_key: Key of the feature to check
            
        Returns:
            True if the tenant is entitled to use the feature, False otherwise
        """
        # Check if cache is stale (older than 5 minutes)
        if not self.cache_timestamp or (datetime.now() - self.cache_timestamp).seconds > 300:
            self.refresh_cache()
        
        # Check entitlement from cache
        tenant_subscriptions = self.cache.get('tenantSubscriptions', [])
        tenant_subscription = next((sub for sub in tenant_subscriptions if sub.get('tenantId') == tenant_id), None)
        
        if not tenant_subscription:
            logger.warning(f"No subscription found for tenant {tenant_id}")
            return False
        
        # Check if subscription is active
        if tenant_subscription.get('status') not in ['active', 'trialing']:
            logger.warning(f"Subscription for tenant {tenant_id} is not active")
            return False
        
        # Check if subscription is expired
        end_date = tenant_subscription.get('endDate')
        if end_date:
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            if end_date < datetime.now():
                logger.warning(f"Subscription for tenant {tenant_id} is expired")
                return False
        
        # Check custom features first
        custom_features = tenant_subscription.get('customFeatures', {})
        if feature_key in custom_features:
            return custom_features[feature_key]
        
        # Then check plan features
        plan_id = tenant_subscription.get('planId')
        plans = self.cache.get('plans', [])
        plan = next((p for p in plans if p.get('id') == plan_id), None)
        
        if not plan:
            logger.warning(f"No plan found for ID {plan_id}")
            return False
        
        features = plan.get('features', {})
        return features.get(feature_key, False)
    
    def record_usage(self, tenant_id: str, resource_type: str, amount: int = 1) -> bool:
        """Record resource usage for a tenant
        
        Args:
            tenant_id: ID of the tenant
            resource_type: Type of resource being used
            amount: Amount of resource being used
            
        Returns:
            True if usage was recorded successfully, False otherwise
        """
        try:
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
                headers['Content-Type'] = 'application/json'
            
            response = requests.post(
                f"{self.api_url}/api/subscription/usage",
                headers=headers,
                json={
                    'tenantId': tenant_id,
                    'resourceType': resource_type,
                    'amount': amount
                },
                timeout=10
            )
            
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Error recording usage: {str(e)}")
            return False
    
    def record_feature_usage(self, tenant_id: str, feature_key: str, amount: int = 1) -> bool:
        """Record feature usage for a tenant
        
        Args:
            tenant_id: ID of the tenant
            feature_key: Key of the feature being used
            amount: Amount of feature being used
            
        Returns:
            True if usage was recorded successfully, False otherwise
        """
        try:
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
                headers['Content-Type'] = 'application/json'
            
            response = requests.post(
                f"{self.api_url}/api/subscription/feature-usage",
                headers=headers,
                json={
                    'tenantId': tenant_id,
                    'featureKey': feature_key,
                    'amount': amount
                },
                timeout=10
            )
            
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Error recording feature usage: {str(e)}")
            return False
    
    def check_resource_limit(self, tenant_id: str, resource_type: str, amount: int = 1) -> bool:
        """Check if a tenant is within resource limits
        
        Args:
            tenant_id: ID of the tenant
            resource_type: Type of resource to check
            amount: Amount of resource to check
            
        Returns:
            True if the tenant is within resource limits, False otherwise
        """
        try:
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            response = requests.get(
                f"{self.api_url}/api/subscription/check-limit/{resource_type}",
                headers=headers,
                params={
                    'tenantId': tenant_id,
                    'amount': amount
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('withinLimit', False)
            
            return False
        except Exception as e:
            logger.error(f"Error checking resource limit: {str(e)}")
            return False
