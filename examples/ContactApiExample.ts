import express from 'express';
import { validateRequest, validateQuery } from '@shared/utils/validation';
import { createContactRequestSchema, updateContactRequestSchema } from '@schemas/api';
import { Contact } from '@types/core';
import { CreateContactRequest, UpdateContactRequest } from '@types/api';
import { isContact, isContactArray } from '@types/guards';

// Import MongoDB models
import { Contact as ContactModel } from '@models/mongoose';

const router = express.Router();

/**
 * GET /api/contacts
 * Get all contacts with pagination and filtering
 */
router.get('/contacts', validateQuery(
  // Define a schema for query parameters
  z.object({
    page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
    pageSize: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
    status: z.string().optional(),
    search: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  })
), async (req, res) => {
  try {
    const { page, pageSize, status, search, sortBy, sortOrder } = req.validatedQuery;
    
    // Build query
    let query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Build sort options
    const sort: any = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by createdAt desc
    }
    
    // Execute query with pagination
    const skip = (page - 1) * pageSize;
    const contacts = await ContactModel.find(query)
      .sort(sort)
      .skip(skip)
      .limit(pageSize)
      .lean();
    
    // Get total count for pagination
    const total = await ContactModel.countDocuments(query);
    
    // Validate response data using type guard
    if (!isContactArray(contacts)) {
      throw new Error('Invalid contact data returned from database');
    }
    
    // Return paginated response
    res.json({
      success: true,
      contacts,
      total,
      page,
      pageSize,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch contacts',
    });
  }
});

/**
 * GET /api/contacts/:id
 * Get a single contact by ID
 */
router.get('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const contact = await ContactModel.findById(id).lean();
    
    if (!contact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found',
      });
    }
    
    // Validate response data using type guard
    if (!isContact(contact)) {
      throw new Error('Invalid contact data returned from database');
    }
    
    res.json({
      success: true,
      contact,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch contact',
    });
  }
});

/**
 * POST /api/contacts
 * Create a new contact
 */
router.post('/contacts', validateRequest(createContactRequestSchema), async (req, res) => {
  try {
    const contactData: CreateContactRequest = req.validatedBody;
    
    // Add user reference
    const createdBy = req.user?.id;
    
    // Create contact
    const contact = await ContactModel.create({
      ...contactData,
      createdBy,
    });
    
    res.status(201).json({
      success: true,
      contact,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create contact',
    });
  }
});

/**
 * PUT /api/contacts/:id
 * Update an existing contact
 */
router.put('/contacts/:id', validateRequest(updateContactRequestSchema), async (req, res) => {
  try {
    const { id } = req.params;
    const contactData: UpdateContactRequest = req.validatedBody;
    
    // Add user reference
    const updatedBy = req.user?.id;
    
    // Update contact
    const contact = await ContactModel.findByIdAndUpdate(
      id,
      {
        ...contactData,
        updatedBy,
      },
      { new: true }
    ).lean();
    
    if (!contact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found',
      });
    }
    
    // Validate response data using type guard
    if (!isContact(contact)) {
      throw new Error('Invalid contact data returned from database');
    }
    
    res.json({
      success: true,
      contact,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update contact',
    });
  }
});

/**
 * DELETE /api/contacts/:id
 * Delete a contact
 */
router.delete('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const contact = await ContactModel.findByIdAndDelete(id);
    
    if (!contact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found',
      });
    }
    
    res.json({
      success: true,
      message: 'Contact deleted successfully',
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete contact',
    });
  }
});

export default router;
