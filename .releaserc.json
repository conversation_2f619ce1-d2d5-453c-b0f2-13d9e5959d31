{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", ["@semantic-release/git", {"assets": ["package.json", "package-lock.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"], "preset": "angular", "releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "style", "release": "patch"}, {"type": "refactor", "release": "patch"}, {"type": "test", "release": "patch"}, {"type": "build", "release": "patch"}, {"type": "ci", "release": "patch"}, {"type": "chore", "release": "patch"}, {"scope": "breaking", "release": "major"}]}