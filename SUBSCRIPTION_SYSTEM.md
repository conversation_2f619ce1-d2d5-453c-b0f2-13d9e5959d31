# Aizako CRM Flexible Subscription System

This document outlines the architecture and implementation guidelines for a flexible subscription system in Aizako CRM that can be easily modified through a UI and propagated to all modules.

## Table of Contents

1. [Subscription System Overview](#subscription-system-overview)
2. [Core Subscription Data Model](#core-subscription-data-model)
3. [Subscription Management UI](#subscription-management-ui)
4. [Module Integration](#module-integration)
5. [Synchronization Mechanism](#synchronization-mechanism)
6. [Implementation Guidelines](#implementation-guidelines)

## Subscription System Overview

The Aizako CRM subscription system is designed with the following key principles:

1. **Centralized Definition, Distributed Enforcement**: Subscription plans are defined centrally but enforced by each module.
2. **Dynamic Configuration**: All subscription parameters can be modified through an admin UI without code changes.
3. **Real-time Propagation**: Changes to subscription plans are immediately propagated to all modules.
4. **Feature Flagging**: Individual features can be enabled/disabled per subscription plan.
5. **Usage Tracking**: Resource usage is tracked against subscription limits.
6. **Multi-tenant Support**: The system supports different subscription plans for different tenants.

## Core Subscription Data Model

### Subscription Plans

The core of the system is the `SubscriptionPlan` entity, which defines the available plans:

```typescript
// Subscription plan entity
interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  isDefault: boolean;
  sortOrder: number;
  
  // Pricing information
  price: number;
  currency: string;
  billingPeriod: 'monthly' | 'yearly' | 'custom';
  trialDays: number;
  
  // Resource limits
  limits: {
    users: number;
    contacts: number;
    companies: number;
    opportunities: number;
    storage: number; // in MB
    apiRequests: number; // per day
  };
  
  // Feature flags
  features: Record<string, boolean>;
  
  // Module-specific settings
  moduleSettings: Record<string, any>;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}
```

### Tenant Subscriptions

Each tenant is associated with a subscription:

```typescript
// Tenant subscription entity
interface TenantSubscription {
  id: string;
  tenantId: string;
  planId: string;
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'expired';
  startDate: Date;
  endDate: Date;
  trialEndsAt?: Date;
  
  // Custom overrides for this specific tenant
  customLimits?: Partial<SubscriptionPlan['limits']>;
  customFeatures?: Partial<Record<string, boolean>>;
  
  // Billing information
  billingDetails?: {
    customerId: string;
    subscriptionId: string;
    paymentMethodId: string;
  };
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  canceledAt?: Date;
}
```

### Feature Registry

To support dynamic feature flagging, a central feature registry is maintained:

```typescript
// Feature definition
interface Feature {
  id: string;
  name: string;
  description: string;
  category: string;
  module: string; // Which module this feature belongs to
  defaultValue: boolean;
  requiresRestart: boolean;
  uiComponent?: string; // Optional UI component to render for this feature
  dependencies?: string[]; // Other features this depends on
}

// The feature registry is a collection of all available features
type FeatureRegistry = Record<string, Feature>;
```

### Usage Tracking

Usage is tracked against subscription limits:

```typescript
// Tenant usage entity
interface TenantUsage {
  id: string;
  tenantId: string;
  period: string; // e.g., '2023-05' for May 2023
  
  // Resource usage
  usage: {
    users: number;
    contacts: number;
    companies: number;
    opportunities: number;
    storage: number; // in MB
    apiRequests: number;
  };
  
  // Feature-specific usage
  featureUsage: Record<string, number>;
  
  // Last updated
  updatedAt: Date;
}
```

## Subscription Management UI

The subscription management UI allows administrators to:

1. **Create and Edit Plans**: Define new subscription plans or modify existing ones
2. **Manage Features**: Enable/disable features for specific plans
3. **Set Resource Limits**: Configure resource limits for each plan
4. **View Usage**: Monitor tenant usage against subscription limits
5. **Override Settings**: Create tenant-specific overrides for special cases

### UI Components

The UI consists of the following key components:

1. **Plan Editor**: A form for creating and editing subscription plans
2. **Feature Matrix**: A grid showing which features are enabled for each plan
3. **Limit Configuration**: UI for setting resource limits per plan
4. **Tenant Subscription Manager**: UI for assigning plans to tenants and creating overrides
5. **Usage Dashboard**: Visualizations of tenant usage against limits

### Admin API Endpoints

The following API endpoints support the subscription management UI:

```
GET    /api/admin/subscription-plans       # List all subscription plans
POST   /api/admin/subscription-plans       # Create a new subscription plan
GET    /api/admin/subscription-plans/:id   # Get a specific subscription plan
PUT    /api/admin/subscription-plans/:id   # Update a subscription plan
DELETE /api/admin/subscription-plans/:id   # Delete a subscription plan

GET    /api/admin/features                 # List all registered features
POST   /api/admin/features                 # Register a new feature
PUT    /api/admin/features/:id             # Update a feature
DELETE /api/admin/features/:id             # Remove a feature

GET    /api/admin/tenant-subscriptions     # List all tenant subscriptions
POST   /api/admin/tenant-subscriptions     # Create a tenant subscription
PUT    /api/admin/tenant-subscriptions/:id # Update a tenant subscription

GET    /api/admin/usage                    # Get usage statistics
```

## Module Integration

Each module in the Aizako CRM system needs to integrate with the subscription system to enforce limits and feature flags.

### Core Backend Module

The core Node.js backend should:

1. **Host the Subscription Database**: Maintain the central subscription data store
2. **Provide the Subscription API**: Expose endpoints for subscription management
3. **Implement the Admin UI**: Serve the subscription management interface
4. **Track Usage**: Monitor and record resource usage
5. **Enforce Core Limits**: Apply subscription limits to core features

### AI Service Module

The Python-based AI service should:

1. **Register Features**: Register AI-specific features with the feature registry
2. **Check Entitlements**: Verify tenant access to AI features before processing requests
3. **Track Usage**: Record AI-specific usage metrics
4. **Apply Limits**: Enforce AI-specific limits (e.g., max tokens, requests per day)

### Frontend Module

The React frontend should:

1. **Adapt UI**: Show/hide features based on subscription entitlements
2. **Display Limits**: Show usage against limits
3. **Upsell Opportunities**: Provide upgrade paths when limits are reached
4. **Admin Interface**: Implement the subscription management UI

## Synchronization Mechanism

To ensure all modules have up-to-date subscription information, a robust synchronization mechanism is implemented:

### Event-Based Propagation

When subscription plans or tenant subscriptions change, events are published to notify all modules:

```typescript
// Event types
enum SubscriptionEventType {
  PLAN_CREATED = 'subscription.plan.created',
  PLAN_UPDATED = 'subscription.plan.updated',
  PLAN_DELETED = 'subscription.plan.deleted',
  TENANT_SUBSCRIPTION_CREATED = 'subscription.tenant.created',
  TENANT_SUBSCRIPTION_UPDATED = 'subscription.tenant.updated',
  TENANT_SUBSCRIPTION_CANCELED = 'subscription.tenant.canceled',
  FEATURE_REGISTERED = 'subscription.feature.registered',
  FEATURE_UPDATED = 'subscription.feature.updated',
  FEATURE_REMOVED = 'subscription.feature.removed',
}

// Event structure
interface SubscriptionEvent {
  id: string;
  type: SubscriptionEventType;
  payload: any;
  timestamp: Date;
}
```

### Subscription Cache

Each module maintains a local cache of subscription data:

```typescript
// Subscription cache
interface SubscriptionCache {
  plans: Record<string, SubscriptionPlan>;
  features: Record<string, Feature>;
  tenantSubscriptions: Record<string, TenantSubscription>;
  lastUpdated: Date;
}
```

### Synchronization Protocol

1. **Initial Load**: On startup, each module loads the subscription cache from the core backend
2. **Event Subscription**: Modules subscribe to subscription events
3. **Cache Updates**: When events are received, the local cache is updated
4. **Periodic Refresh**: A background process periodically refreshes the cache to ensure consistency
5. **Fallback Mechanism**: If event delivery fails, the next API request will check for cache staleness

## Implementation Guidelines

### Database Schema

Add the following tables to the database:

```sql
-- Subscription plans table
CREATE TABLE subscription_plans (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  sort_order INTEGER NOT NULL DEFAULT 0,
  price DECIMAL(10, 2) NOT NULL,
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  billing_period VARCHAR(10) NOT NULL,
  trial_days INTEGER NOT NULL DEFAULT 0,
  limits JSONB NOT NULL,
  features JSONB NOT NULL,
  module_settings JSONB NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Tenant subscriptions table
CREATE TABLE tenant_subscriptions (
  id VARCHAR(36) PRIMARY KEY,
  tenant_id VARCHAR(36) NOT NULL,
  plan_id VARCHAR(36) NOT NULL REFERENCES subscription_plans(id),
  status VARCHAR(20) NOT NULL,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  trial_ends_at TIMESTAMP,
  custom_limits JSONB,
  custom_features JSONB,
  billing_details JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  canceled_at TIMESTAMP
);

-- Features table
CREATE TABLE features (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL,
  module VARCHAR(50) NOT NULL,
  default_value BOOLEAN NOT NULL DEFAULT FALSE,
  requires_restart BOOLEAN NOT NULL DEFAULT FALSE,
  ui_component VARCHAR(100),
  dependencies JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Tenant usage table
CREATE TABLE tenant_usage (
  id VARCHAR(36) PRIMARY KEY,
  tenant_id VARCHAR(36) NOT NULL,
  period VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  usage JSONB NOT NULL,
  feature_usage JSONB NOT NULL,
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE(tenant_id, period)
);

-- Subscription events table (for audit and synchronization)
CREATE TABLE subscription_events (
  id VARCHAR(36) PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  payload JSONB NOT NULL,
  timestamp TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### API Implementation

Implement the subscription API in the core backend:

```typescript
// Subscription service
class SubscriptionService {
  // Plan management
  async getPlans(): Promise<SubscriptionPlan[]> { /* ... */ }
  async getPlan(id: string): Promise<SubscriptionPlan> { /* ... */ }
  async createPlan(plan: Omit<SubscriptionPlan, 'id' | 'createdAt' | 'updatedAt'>): Promise<SubscriptionPlan> { /* ... */ }
  async updatePlan(id: string, plan: Partial<SubscriptionPlan>): Promise<SubscriptionPlan> { /* ... */ }
  async deletePlan(id: string): Promise<boolean> { /* ... */ }
  
  // Feature management
  async getFeatures(): Promise<Feature[]> { /* ... */ }
  async getFeature(id: string): Promise<Feature> { /* ... */ }
  async registerFeature(feature: Omit<Feature, 'id' | 'createdAt' | 'updatedAt'>): Promise<Feature> { /* ... */ }
  async updateFeature(id: string, feature: Partial<Feature>): Promise<Feature> { /* ... */ }
  async removeFeature(id: string): Promise<boolean> { /* ... */ }
  
  // Tenant subscription management
  async getTenantSubscription(tenantId: string): Promise<TenantSubscription> { /* ... */ }
  async createTenantSubscription(subscription: Omit<TenantSubscription, 'id' | 'createdAt' | 'updatedAt'>): Promise<TenantSubscription> { /* ... */ }
  async updateTenantSubscription(id: string, subscription: Partial<TenantSubscription>): Promise<TenantSubscription> { /* ... */ }
  async cancelTenantSubscription(id: string): Promise<TenantSubscription> { /* ... */ }
  
  // Usage tracking
  async recordUsage(tenantId: string, resourceType: string, amount: number): Promise<void> { /* ... */ }
  async getTenantUsage(tenantId: string, period?: string): Promise<TenantUsage> { /* ... */ }
  
  // Entitlement checking
  async checkEntitlement(tenantId: string, featureId: string): Promise<boolean> { /* ... */ }
  async checkResourceLimit(tenantId: string, resourceType: string, amount: number): Promise<boolean> { /* ... */ }
  
  // Event publishing
  private publishEvent(type: SubscriptionEventType, payload: any): Promise<void> { /* ... */ }
}
```

### Middleware Implementation

Implement middleware for checking entitlements:

```typescript
// Entitlement middleware
const checkEntitlement = (featureId: string) => {
  return async (req: Request, res: Response, next: Function) => {
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(401).json({ message: "Tenant not specified" });
    }
    
    const subscriptionService = new SubscriptionService();
    const isEntitled = await subscriptionService.checkEntitlement(tenantId, featureId);
    
    if (!isEntitled) {
      return res.status(403).json({
        message: "Feature not available in your subscription plan",
        featureId,
        upgradeUrl: `/upgrade?feature=${featureId}`
      });
    }
    
    next();
  };
};

// Resource limit middleware
const checkResourceLimit = (resourceType: string, getAmount: (req: Request) => number) => {
  return async (req: Request, res: Response, next: Function) => {
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return res.status(401).json({ message: "Tenant not specified" });
    }
    
    const amount = getAmount(req);
    const subscriptionService = new SubscriptionService();
    const isWithinLimit = await subscriptionService.checkResourceLimit(tenantId, resourceType, amount);
    
    if (!isWithinLimit) {
      return res.status(403).json({
        message: `${resourceType} limit reached for your subscription plan`,
        resourceType,
        upgradeUrl: `/upgrade?resource=${resourceType}`
      });
    }
    
    next();
  };
};

// Usage tracking middleware
const trackUsage = (resourceType: string, getAmount: (req: Request) => number) => {
  return async (req: Request, res: Response, next: Function) => {
    const tenantId = req.tenantId;
    
    if (!tenantId) {
      return next();
    }
    
    const amount = getAmount(req);
    const subscriptionService = new SubscriptionService();
    
    // Use response events to ensure we only track successful requests
    res.on('finish', () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        subscriptionService.recordUsage(tenantId, resourceType, amount)
          .catch(err => console.error(`Failed to record usage: ${err}`));
      }
    });
    
    next();
  };
};
```

### Example Usage in Routes

```typescript
// Apply middleware to routes
app.post("/api/contacts", 
  authenticateUser,
  tenantIsolationMiddleware,
  checkResourceLimit('contacts', req => 1),
  trackUsage('contacts', req => 1),
  async (req, res) => {
    // Create contact logic
  }
);

app.post("/api/ai/analyze", 
  authenticateUser,
  tenantIsolationMiddleware,
  checkEntitlement('ai.analysis'),
  trackUsage('ai.tokens', req => estimateTokens(req.body.text)),
  async (req, res) => {
    // AI analysis logic
  }
);
```

### AI Service Integration

The AI service should implement a subscription client:

```python
class SubscriptionClient:
    def __init__(self, api_url):
        self.api_url = api_url
        self.cache = {}
        self.cache_timestamp = None
        self.refresh_cache()
    
    def refresh_cache(self):
        # Fetch subscription data from core backend
        response = requests.get(f"{self.api_url}/api/subscription/cache")
        if response.status_code == 200:
            self.cache = response.json()
            self.cache_timestamp = datetime.now()
    
    def check_entitlement(self, tenant_id, feature_id):
        # Check if cache is stale
        if not self.cache_timestamp or (datetime.now() - self.cache_timestamp).seconds > 300:
            self.refresh_cache()
        
        # Check entitlement from cache
        tenant_subscription = self.cache.get('tenantSubscriptions', {}).get(tenant_id)
        if not tenant_subscription:
            return False
        
        plan_id = tenant_subscription.get('planId')
        plan = self.cache.get('plans', {}).get(plan_id)
        if not plan:
            return False
        
        # Check custom features first
        custom_features = tenant_subscription.get('customFeatures', {})
        if feature_id in custom_features:
            return custom_features[feature_id]
        
        # Then check plan features
        return plan.get('features', {}).get(feature_id, False)
    
    def record_usage(self, tenant_id, resource_type, amount):
        # Record usage via API
        requests.post(
            f"{self.api_url}/api/subscription/usage",
            json={
                'tenantId': tenant_id,
                'resourceType': resource_type,
                'amount': amount
            }
        )
```

### Frontend Integration

The React frontend should implement a subscription hook:

```typescript
// Subscription hook
function useSubscription() {
  const { user } = useAuth();
  const tenantId = user?.tenantId;
  const [subscription, setSubscription] = useState<TenantSubscription | null>(null);
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (!tenantId) {
      setLoading(false);
      return;
    }
    
    const fetchSubscription = async () => {
      try {
        const response = await apiRequest('GET', `/api/subscription/tenant/${tenantId}`);
        setSubscription(response.subscription);
        setPlan(response.plan);
      } catch (error) {
        console.error('Error fetching subscription:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchSubscription();
  }, [tenantId]);
  
  // Check if a feature is enabled
  const hasFeature = useCallback((featureId: string) => {
    if (!subscription || !plan) return false;
    
    // Check custom features first
    if (subscription.customFeatures && featureId in subscription.customFeatures) {
      return subscription.customFeatures[featureId];
    }
    
    // Then check plan features
    return plan.features[featureId] || false;
  }, [subscription, plan]);
  
  // Check if within resource limits
  const checkResourceLimit = useCallback((resourceType: string, currentUsage: number) => {
    if (!subscription || !plan) return false;
    
    // Check custom limits first
    if (subscription.customLimits && resourceType in subscription.customLimits) {
      return currentUsage < subscription.customLimits[resourceType];
    }
    
    // Then check plan limits
    return currentUsage < (plan.limits[resourceType] || 0);
  }, [subscription, plan]);
  
  return {
    subscription,
    plan,
    loading,
    hasFeature,
    checkResourceLimit
  };
}
```

### Feature-Gated UI Components

Create components that are conditionally rendered based on subscription:

```tsx
// Feature-gated component
function FeatureGated({ featureId, fallback = null, children }) {
  const { hasFeature, loading } = useSubscription();
  
  if (loading) return <div>Loading...</div>;
  
  if (!hasFeature(featureId)) {
    return fallback ? (
      <div className="upgrade-prompt">
        <h3>Feature not available</h3>
        <p>This feature requires a higher subscription plan.</p>
        <Button as={Link} to="/upgrade">Upgrade Now</Button>
      </div>
    ) : null;
  }
  
  return children;
}

// Usage in components
function AdvancedAnalytics() {
  return (
    <FeatureGated featureId="analytics.advanced">
      <div className="advanced-analytics">
        {/* Advanced analytics UI */}
      </div>
    </FeatureGated>
  );
}
```

## Conclusion

This flexible subscription system provides a robust foundation for Aizako CRM's multi-tenant, feature-rich platform. By centralizing subscription definitions while distributing enforcement, the system allows for easy modification through the admin UI while ensuring changes are propagated to all modules in real-time.

The architecture supports:

1. **Dynamic Plan Configuration**: Easily create and modify subscription plans
2. **Granular Feature Control**: Enable/disable specific features per plan
3. **Custom Tenant Overrides**: Create special arrangements for specific tenants
4. **Usage-Based Limits**: Track and enforce resource usage limits
5. **Cross-Module Consistency**: Ensure all modules have the same view of subscriptions

By implementing this system, Aizako CRM can offer a flexible, tiered pricing model that can evolve with the product and market needs without requiring code changes for each adjustment.
