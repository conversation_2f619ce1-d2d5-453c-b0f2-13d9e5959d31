{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "start:test": "NODE_ENV=test node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:seed:mongo": "tsx server/seed/mongo-seeder.ts", "db:mongo": "MONGODB_ENABLED=true tsx server/index.ts", "test:mongo": "tsx scripts/test-mongodb.js", "test:mongo:unit": "MONGODB_ENABLED=true jest tests/mongodb.test.ts", "test:email-tracking": "MONGODB_ENABLED=true jest tests/email-tracking.test.ts", "test": "MONGODB_ENABLED=true jest", "test:unit": "vitest run", "test:unit:watch": "vitest", "test:unit:coverage": "vitest run --coverage", "test:integration": "jest", "test:ui": "playwright test", "test:ui:headed": "playwright test --headed", "test:performance": "lhci autorun", "test:performance:mobile": "lhci autorun --collect.settings.preset=mobile", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@copilotkit/react-core": "^1.8.4", "@copilotkit/react-textarea": "^1.8.4", "@copilotkit/react-ui": "^1.8.4", "@copilotkit/runtime": "^1.8.4", "@copilotkit/shared": "^1.8.4", "@firebase/auth": "^1.10.0", "@google-cloud/bigquery": "^8.0.0", "@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@tanstack/react-query": "^5.60.5", "axios": "^1.6.7", "bcrypt": "^5.1.1", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "crewai-js": "^0.0.1", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-session": "^1.18.1", "firebase": "^11.6.0", "framer-motion": "^11.13.1", "html-docx-js": "^0.3.1", "input-otp": "^1.2.4", "ioredis": "^5.6.1", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "mongodb": "^6.15.0", "mongoose": "^8.2.0", "multer": "^1.4.5-lts.2", "neo4j-driver": "^5.18.0", "nodemailer": "^6.9.12", "openai": "^4.92.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-force-graph-2d": "^1.27.1", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "react-router-dom": "^7.5.3", "reactflow": "^11.11.4", "recharts": "^2.13.0", "redis": "^4.6.13", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "uuid": "^9.0.1", "vaul": "^1.1.0", "winston": "^3.12.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.0.11", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.12", "@types/mongodb-memory-server": "^2.3.0", "@types/node": "20.16.11", "@types/nodemailer": "^6.4.14", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/uuid": "^9.0.8", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "ts-jest": "^29.1.2", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14", "vitest": "^3.1.3"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}