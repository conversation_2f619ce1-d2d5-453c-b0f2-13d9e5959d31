# Proposal Generator User Guide

## Overview

The Proposal Generator is a powerful feature of Aizako CRM that allows you to create professional, AI-enhanced proposals for your clients. This guide covers the key features and how to use them effectively.

## Table of Contents

1. [Creating Proposals](#creating-proposals)
2. [Document Formats](#document-formats)
3. [Email Configuration](#email-configuration)
4. [Sharing Proposals](#sharing-proposals)
5. [Analytics and Tracking](#analytics-and-tracking)
6. [Best Practices](#best-practices)

## Creating Proposals <a name="creating-proposals"></a>

### Starting a New Proposal

1. Navigate to the **Proposal Generator** section in the sidebar
2. Click the **Create Proposal** button
3. Select an opportunity to associate with the proposal
4. Choose a template or start from scratch
5. Fill in the required information:
   - Proposal name
   - Description
   - Sections (Executive Summary, Solution, Pricing, etc.)
6. Click **Generate Proposal** to create your proposal

### Editing Proposals

1. Select an existing proposal from the list
2. Click the **Edit** button
3. Make your changes to any section
4. Click **Save Changes** to update the proposal

### Using AI Enhancement

The AI enhancement feature uses <PERSON> to improve your proposal's content and formatting:

1. Create or edit a proposal
2. Click the **Enhance with AI** button
3. The system will analyze your proposal and suggest improvements
4. Review the suggestions and apply the ones you want
5. Save the enhanced proposal

## Document Formats <a name="document-formats"></a>

The Proposal Generator supports multiple document formats to meet different needs:

### Available Formats

1. **PDF Document**
   - Professional, print-ready format
   - Consistent appearance across devices
   - Ideal for formal business proposals
   - Can be easily printed or attached to emails
   - **Downloadable**: Yes

2. **Word Document (DOCX)**
   - Fully editable Microsoft Word format
   - Can be customized after generation
   - Supports tracked changes for collaboration
   - Familiar format for most business users
   - **Downloadable**: Yes

3. **Markdown**
   - Lightweight plain text format
   - Easy to edit in any text editor
   - Can be converted to other formats
   - Great for technical documentation
   - **Downloadable**: Yes

4. **Claude-Enhanced Design**
   - AI-generated beautiful design
   - Interactive elements and animations
   - Responsive layout for all devices
   - Modern visual styling with gradients and colors
   - **Downloadable**: No (web view only)

### Selecting a Format

1. Open a proposal
2. Click the **Select Format** button
3. Choose your preferred format from the options
4. Click **Generate** to create the document in that format

## Email Configuration <a name="email-configuration"></a>

### Setting Up Email Integration

To send proposals directly via email, you need to configure your email settings:

1. Navigate to **Email Configuration** in the sidebar
2. Fill in the following information:
   - **Provider**: Choose from SMTP, Gmail, Outlook, SendGrid, Mailgun, or Amazon SES
   - **From Name**: Your name or company name
   - **From Email**: Your email address
   - **Reply-To Email** (optional): Alternative email for replies

### SMTP Configuration

If using SMTP, provide these additional details:
- **SMTP Host**: Your email server address (e.g., smtp.gmail.com)
- **SMTP Port**: Usually 587 for TLS or 465 for SSL
- **SMTP Username**: Your email username
- **SMTP Password**: Your email password
- **Use Secure Connection**: Enable for TLS/SSL (recommended)

### API-Based Providers

For SendGrid, Mailgun, or Amazon SES:
- **API Key**: Your provider's API key
- **API Secret** (if required): Your provider's API secret
- **Region** (for AWS SES): Your AWS region (e.g., us-east-1)

### Email Templates

Customize email templates for different scenarios:
1. **Proposal Share Template**: Used when sharing proposals via email
2. **Proposal Accepted Template**: Sent when a proposal is accepted
3. **Proposal Rejected Template**: Sent when a proposal is rejected

Each template has:
- **Subject Line**: The email subject
- **Email Body**: The HTML content of the email

You can use variables in templates:
- `{{senderName}}`: Your name
- `{{recipientName}}`: Client's name
- `{{proposalName}}`: Proposal title
- `{{proposalUrl}}`: Link to view the proposal
- `{{expirationDate}}`: When the link expires
- `{{message}}`: Custom message
- `{{companyName}}`: Client company name
- `{{value}}`: Proposal value

### Testing Your Configuration

1. Go to the **Test & Verify** tab
2. Enter a test email address
3. Click **Send Test Email**
4. Check the recipient inbox to confirm delivery

## Sharing Proposals <a name="sharing-proposals"></a>

### Creating Shareable Links

1. Open a proposal
2. Click the **Share** button
3. Select the **Shareable Link** tab
4. Choose a format (PDF, DOCX, Markdown, or Claude-Enhanced)
5. Set an expiration period (1, 7, 30, or 90 days)
6. Click **Generate Shareable Link**
7. Copy the link to share with your client

### Sending via Email

1. Open a proposal
2. Click the **Share** button
3. Select the **Email** tab
4. Enter the recipient's email address
5. Choose a format (PDF, DOCX, or Markdown)
6. Add an optional message
7. Click **Send Email**

### Social Media Sharing

1. Open a proposal
2. Click the **Share** button
3. Select the **Social Media** tab
4. Choose a platform (LinkedIn, Twitter, or Facebook)
5. Add a message to accompany the share
6. Click **Share to [Platform]**

## Analytics and Tracking <a name="analytics-and-tracking"></a>

### Viewing Proposal Analytics

1. Navigate to **Proposal Analytics** in the sidebar
2. View the dashboard for overall metrics:
   - Total proposals
   - Total views
   - Total downloads
   - Conversion rate

### Understanding the Dashboard

The analytics dashboard provides several visualizations:
1. **Proposal Status Distribution**: Breakdown of proposals by status
2. **Conversion Funnel**: Proposal journey from creation to acceptance
3. **Top Proposals**: Most viewed and downloaded proposals
4. **Engagement Metrics**: Views, downloads, and shares over time
5. **Average View Duration**: How long viewers spend looking at proposals

### Detailed Proposal Performance

1. Go to the **Proposals** tab in the analytics dashboard
2. View detailed metrics for each proposal:
   - Status
   - Creation date
   - Last activity
   - Views
   - Downloads
   - Shares

### Tracking Individual Proposals

1. Open a proposal
2. Click the **Analytics** button
3. View detailed analytics for that specific proposal:
   - View history
   - Download history
   - Share history
   - Viewer engagement

## Best Practices <a name="best-practices"></a>

### Creating Effective Proposals

1. **Know your audience**: Tailor the proposal to the specific client
2. **Focus on benefits**: Emphasize how your solution solves their problems
3. **Be concise**: Keep content clear and to the point
4. **Include social proof**: Add testimonials or case studies
5. **Clear pricing**: Make your pricing structure transparent
6. **Strong call to action**: Make next steps clear

### Optimizing for Conversion

1. **Use Claude-Enhanced Design** for initial sharing (more engaging)
2. **Provide downloadable formats** for client review and sharing
3. **Set reasonable expiration dates** (7-30 days recommended)
4. **Follow up** after sending based on analytics insights
5. **Personalize email messages** when sharing
6. **Monitor view duration** to identify sections that need improvement

### Using Analytics Effectively

1. **Track conversion rates** to measure proposal effectiveness
2. **Compare performance** across different proposals
3. **Identify drop-off points** in the conversion funnel
4. **Optimize underperforming proposals** based on analytics
5. **Test different formats** to see which performs better
6. **Use view duration** to identify engaging vs. problematic sections

## Troubleshooting

### Common Issues

1. **Email not sending**:
   - Check your email configuration
   - Verify SMTP credentials
   - Ensure your email provider allows application access

2. **Proposal link expired**:
   - Generate a new link with a longer expiration
   - Consider using email to send the proposal directly

3. **Analytics not updating**:
   - Allow up to 5 minutes for analytics to process
   - Refresh the analytics page

4. **Format conversion issues**:
   - Try regenerating the document in the desired format
   - Check for complex formatting that might not convert well

### Getting Help

If you encounter issues not covered in this guide, please contact <NAME_EMAIL> or use the in-app help feature.
