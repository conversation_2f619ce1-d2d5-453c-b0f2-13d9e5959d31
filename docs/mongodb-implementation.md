# MongoDB Implementation for Aizako CRM

This document provides an overview of the MongoDB implementation for Aizako CRM.

## Overview

Aizako CRM now supports MongoDB as the primary database, with the ability to switch between MongoDB and PostgreSQL using environment variables.

## Configuration

To use MongoDB, set the following environment variables:

```
# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/aizako-crm?retryWrites=true&w=majority
MONGODB_ENABLED=true # Set to true to use MongoDB, false to use PostgreSQL
```

## Data Models

The following MongoDB models have been implemented:

1. **User**: User accounts and authentication
2. **Contact**: Customer and prospect contacts
3. **Company**: Organizations and businesses
4. **Opportunity**: Sales opportunities and deals
5. **Activity**: User activities like calls, meetings, emails
6. **Relationship**: Relationships between contacts and companies
7. **AiChat**: AI chat conversations
8. **Insight**: AI-generated insights
9. **Document**: Files and documents
10. **Tag**: Centralized tag management
11. **Task**: Task management
12. **Notification**: User notifications
13. **Subscription Models**: Subscription plans, features, tenants, etc.

## Directory Structure

```
server/
  models/
    mongoose/
      user-model.ts
      contact-model.ts
      company-model.ts
      opportunity-model.ts
      activity-model.ts
      relationship-model.ts
      ai-chat-model.ts
      insight-model.ts
      document-model.ts
      tag-model.ts
      task-model.ts
      notification-model.ts
      subscription-models.ts
      index.ts
  storage/
    mongo-storage.ts
    mongo-subscription-storage.ts
    storage-factory.ts
  mongodb-connection.ts
  seed/
    mongo-seeder.ts
```

## Usage

### Starting the Server with MongoDB

```bash
npm run db:mongo
```

### Seeding the MongoDB Database

```bash
npm run db:seed:mongo
```

## Implementation Details

### Connection Management

The MongoDB connection is managed in `mongodb-connection.ts`, which provides:

- Connection pooling
- Automatic reconnection
- Error handling
- Connection caching for development

### Storage Factory

The `storage-factory.ts` file determines which storage implementation to use based on the `MONGODB_ENABLED` environment variable:

```typescript
export async function createStorage(): Promise<IStorage> {
  const useMongoDb = process.env.MONGODB_ENABLED === 'true';
  
  if (useMongoDb) {
    try {
      // Test MongoDB connection
      await connectToMongoDB();
      console.log("MongoDB connection successful! Using MongoDB storage.");
      
      // Initialize all Mongoose models
      await initializeModels();
      
      // Create and return MongoDB storage implementation
      return new MongoStorage();
    } catch (error) {
      console.error("MongoDB connection failed:", error);
      console.log("Falling back to PostgreSQL storage.");
      return new MemStorage();
    }
  } else {
    console.log("Using PostgreSQL storage.");
    return new MemStorage();
  }
}
```

### Mongoose Models

All Mongoose models follow a consistent pattern:

1. Define an interface extending `Document`
2. Create a Mongoose schema
3. Add indexes for performance
4. Add virtual properties and methods as needed
5. Export the model

Example:

```typescript
export interface IContact extends Document {
  firstName: string;
  lastName: string;
  email?: string;
  // ...
}

const ContactSchema = new Schema<IContact>({
  firstName: { 
    type: String, 
    required: true,
    trim: true
  },
  // ...
});

// Virtual for full name
ContactSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Text index for search
ContactSchema.index({ firstName: 'text', lastName: 'text', email: 'text' });

// Create and export the model
export const Contact: Model<IContact> = mongoose.models.Contact || mongoose.model<IContact>('Contact', ContactSchema);
```

### Storage Implementation

The MongoDB storage implementation (`MongoStorage`) implements the same interface as the PostgreSQL implementation (`MemStorage`), allowing them to be used interchangeably.

## Subscription System Integration

The MongoDB implementation includes full support for the subscription system, with:

- Mongoose models for subscription plans, features, tenants, etc.
- MongoDB-specific storage implementation for subscription data
- Integration with the subscription service

## Performance Considerations

1. **Indexes**: All models include appropriate indexes for common query patterns
2. **Compound Indexes**: Compound indexes are used for frequently combined query fields
3. **Text Indexes**: Text indexes are used for search functionality
4. **Connection Pooling**: MongoDB connections are pooled for better performance

## Security Considerations

1. **Password Hashing**: User passwords are hashed using bcrypt
2. **Input Validation**: All input is validated using Mongoose schema validation
3. **Connection String**: The MongoDB connection string is stored in environment variables
4. **Access Control**: MongoDB Atlas access is restricted by IP and user credentials

## Migration Considerations

When migrating from PostgreSQL to MongoDB:

1. Export data from PostgreSQL
2. Transform data to match MongoDB schema
3. Import data into MongoDB
4. Update environment variables to use MongoDB
5. Restart the server

## Testing

To test the MongoDB implementation:

1. Set up a test MongoDB instance
2. Run the seeder to populate test data
3. Run the server with MongoDB enabled
4. Verify that all functionality works as expected
