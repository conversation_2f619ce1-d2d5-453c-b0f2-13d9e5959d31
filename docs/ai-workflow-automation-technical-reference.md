# AI & Workflow Automation Technical Reference

This technical reference provides detailed information about the AI & Workflow Automation module for developers working with Aizako CRM.

## Architecture Overview

The AI & Workflow Automation module consists of several components:

1. **MongoDB Models**: Store workflow definitions, runs, versions, and insights
2. **Backend Services**: Handle workflow parsing, compilation, execution, and insights generation
3. **API Routes**: Expose functionality to the frontend
4. **Frontend Components**: Provide user interfaces for workflow creation and insights generation
5. **AI Service**: Process natural language and generate workflows and insights

### System Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   React Client  │────▶│  Express Server │────▶│  MongoDB Atlas  │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                      │                        ▲
         │                      │                        │
         │                      ▼                        │
         │              ┌─────────────────┐              │
         └─────────────▶│   AI Service    │──────────────┘
                        └─────────────────┘
```

## Data Models

### Workflow Models

#### Workflow

```typescript
interface IWorkflow extends Document {
  tenant_id: string;
  name: string;
  description?: string;
  dsl_yaml: string;
  nodes: IWorkflowNode[];
  edges: IWorkflowEdge[];
  version: number;
  status: 'draft' | 'active' | 'paused' | 'archived';
  created_by: string;
  updated_by: string;
  created_at: Date;
  updated_at: Date;
  original_prompt?: string;
  tags?: string[];
  simulation_results?: {
    run_at: Date;
    expected_volume: number;
    edge_cases: string[];
    performance_metrics: Record<string, any>;
  };
  publishWorkflow(): Promise<IWorkflow>;
  rollbackToVersion(version: number): Promise<IWorkflow>;
  duplicateWorkflow(newName: string): Promise<IWorkflow>;
}
```

#### WorkflowRun

```typescript
interface IWorkflowRun extends Document {
  workflow_id: mongoose.Types.ObjectId;
  tenant_id: string;
  trigger_event: {
    type: string;
    data: Record<string, any>;
  };
  start_ts: Date;
  end_ts?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  error_log?: string;
  step_executions: IWorkflowStepExecution[];
  context_data?: Record<string, any>;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
  duration_ms?: number;
  is_simulation: boolean;
  affected_records?: {
    type: string;
    id: string;
    action: string;
  }[];
}
```

#### WorkflowVersion

```typescript
interface IWorkflowVersion extends Document {
  workflow_id: mongoose.Types.ObjectId;
  tenant_id: string;
  version: number;
  dsl_yaml: string;
  nodes: IWorkflowNode[];
  edges: IWorkflowEdge[];
  created_by: string;
  created_at: Date;
  comment?: string;
}
```

### Insights Models

#### InsightsCache

```typescript
interface IInsightsCache extends Document {
  tenant_id: string;
  question: string;
  question_hash: string;
  dataset_ref: string;
  narrative: string;
  chart_spec: IChartSpec;
  why_it_matters: string[];
  recommended_plays: IRecommendedPlay[];
  sql_query?: string;
  graph_query?: string;
  feature_weights?: Record<string, number>;
  created_at: Date;
  updated_at: Date;
  expires_at: Date;
  refresh_count: number;
  last_refresh_at?: Date;
  feedback?: {
    helpful: boolean;
    comment?: string;
    submitted_by: string;
    submitted_at: Date;
  }[];
  isExpired(): boolean;
  refreshInsight(): Promise<IInsightsCache>;
}
```

## API Endpoints

### Workflow API

#### Create Workflow

```
POST /api/workflows
```

Request:
```json
{
  "name": "High-Value Lead Follow-up",
  "description": "Follow up with high-value leads",
  "dsl_yaml": "workflow:\n  name: High-Value Lead Follow-up\n  ...",
  "original_prompt": "When a lead's score reaches 80...",
  "tags": ["lead", "follow-up"]
}
```

Response:
```json
{
  "_id": "60f7b0b3e6b1f3001c8e9b1a",
  "tenant_id": "tenant123",
  "name": "High-Value Lead Follow-up",
  "description": "Follow up with high-value leads",
  "dsl_yaml": "workflow:\n  name: High-Value Lead Follow-up\n  ...",
  "nodes": [...],
  "edges": [...],
  "version": 1,
  "status": "draft",
  "created_by": "user123",
  "updated_by": "user123",
  "created_at": "2023-07-21T12:00:00.000Z",
  "updated_at": "2023-07-21T12:00:00.000Z",
  "original_prompt": "When a lead's score reaches 80...",
  "tags": ["lead", "follow-up"]
}
```

#### Parse Natural Language to Workflow DSL

```
POST /api/workflows/parse
```

Request:
```json
{
  "prompt": "When a lead's score reaches 80 and there's no reply in 2 days, send a 'Nudge' email template to the lead, then create a follow-up task for the assigned sales rep with a due date of 1 day later."
}
```

Response:
```json
{
  "success": true,
  "dsl_yaml": "workflow:\n  name: Lead Nudge Workflow\n  ...",
  "nodes": [...],
  "edges": [...]
}
```

### Insights API

#### Generate Insight

```
POST /api/insights/generate
```

Request:
```json
{
  "question": "Why is our Q2 pipeline soft compared to last quarter?",
  "refresh": false,
  "filters": {
    "region": "North America"
  }
}
```

Response:
```json
{
  "success": true,
  "insight": {
    "_id": "60f7b0b3e6b1f3001c8e9b1b",
    "tenant_id": "tenant123",
    "question": "Why is our Q2 pipeline soft compared to last quarter?",
    "question_hash": "a1b2c3d4e5f6",
    "dataset_ref": "pipeline_analysis_q2",
    "narrative": "Your pipeline for Q2 is showing softness compared to Q1 primarily due to three factors...",
    "chart_spec": {...},
    "why_it_matters": [...],
    "recommended_plays": [...],
    "created_at": "2023-07-21T12:00:00.000Z",
    "updated_at": "2023-07-21T12:00:00.000Z",
    "expires_at": "2023-07-22T12:00:00.000Z",
    "refresh_count": 0
  },
  "source": "ai_service"
}
```

## Workflow DSL Format

The workflow DSL is a YAML format that defines the workflow structure:

```yaml
workflow:
  name: "Lead Nudge Workflow"
  description: "Send a nudge email to leads with high scores and no reply"
  trigger:
    type: "lead_score_change"
    config:
      threshold: 80
  nodes:
    - id: "check_reply"
      type: "condition"
      data:
        type: "wait_until"
        config:
          days: 2
          condition: "no_reply"
    - id: "send_email"
      type: "action"
      data:
        type: "send_email"
        config:
          template: "Nudge"
          to: "{{lead.email}}"
    - id: "create_task"
      type: "action"
      data:
        type: "create_task"
        config:
          assignee: "{{lead.owner}}"
          due_date: "{{now() + 1 day}}"
          title: "Follow up with {{lead.name}}"
  edges:
    - source: "trigger"
      target: "check_reply"
    - source: "check_reply"
      target: "send_email"
    - source: "send_email"
      target: "create_task"
```

## AI Service Integration

The AI & Workflow Automation module integrates with the AI service through two main endpoints:

### Workflow Parsing

```
POST /workflow/parse
```

This endpoint takes a natural language description and converts it to a workflow DSL.

### Insights Generation

```
POST /insights/generate
```

This endpoint takes a natural language question and generates an insight with a chart, narrative, and recommendations.

## Extending the Module

### Trigger Types

The module supports the following trigger types:

#### Basic Triggers
- `lead_score_change`: Triggered when a lead's score changes
- `email_event`: Triggered by email events (open, click, reply)
- `form_submission`: Triggered when a form is submitted
- `record_update`: Triggered when a record is updated
- `webhook`: Triggered by an external webhook

#### Time-Based Triggers
- `scheduled`: Run at a specific time or on a recurring schedule
- `anniversary`: Run on anniversaries like customer sign-up date

#### Behavioral Triggers
- `website_activity`: Triggered by specific website activity patterns
- `product_usage`: Triggered by product usage metrics

#### External System Triggers
- `linkedin_event`: Triggered by LinkedIn events

#### Advanced CRM Triggers
- `relationship_change`: Triggered when a relationship between contacts changes
- `pipeline_velocity`: Triggered when deal movement slows or speeds up
- `team_performance`: Triggered based on team performance metrics

### Action Types

The module supports the following action types:

#### Basic Actions
- `send_email`: Send an email to a contact
- `update_record`: Update a record in the CRM
- `create_task`: Create a task for a user
- `call_webhook`: Call an external webhook

#### Communication Actions
- `send_sms`: Send text messages to contacts
- `schedule_meeting`: Automatically schedule meetings
- `create_video_message`: Generate personalized video messages

#### Data Enrichment Actions
- `enrich_contact`: Automatically enrich contact data
- `score_lead`: Update lead scoring based on custom algorithms
- `generate_ai_content`: Create personalized content using AI

#### Integration Actions
- `create_document`: Generate documents like proposals
- `update_external_system`: Update records in external systems
- `post_to_linkedin`: Create LinkedIn posts

#### Advanced CRM Actions
- `create_relationship`: Establish relationships between contacts
- `assign_territory`: Automatically assign territories
- `forecast_update`: Update sales forecasts based on deal changes

### Condition Types

The module supports the following condition types:

#### Basic Conditions
- `if`: Check if a condition is true
- `wait_until`: Wait until a condition is true
- `for_each`: Loop through a collection

#### Advanced Conditions
- `switch`: Branch based on multiple conditions
- `parallel`: Execute multiple paths in parallel
- `retry`: Retry an action if it fails
- `delay`: Wait for a specific amount of time

#### Data Conditions
- `data_exists`: Check if data exists
- `data_equals`: Check if data equals a value
- `data_greater_than`: Check if data is greater than a value
- `data_less_than`: Check if data is less than a value

#### Time Conditions
- `time_of_day`: Check if current time is within a range
- `day_of_week`: Check if current day is a specific day
- `business_hours`: Check if current time is within business hours

### Chart Types

The module supports the following chart types:

#### Basic Charts
- `bar`: Bar chart for comparing values across categories
- `line`: Line chart for showing trends over time
- `pie`: Pie chart for showing proportions of a whole
- `funnel`: Funnel chart for showing stages in a process
- `scatter`: Scatter chart for showing correlation between variables
- `table`: Table for showing detailed data

#### Advanced Visualization Types
- `sankey`: Sankey diagram for showing flow between nodes
- `heatmap`: Heatmap for showing intensity of values across two dimensions
- `radar`: Radar chart for comparing multiple variables
- `treemap`: Treemap for showing hierarchical data
- `bubble`: Bubble chart for showing three dimensions of data

#### Business-Specific Charts
- `pipeline_waterfall`: Waterfall chart for showing pipeline changes
- `cohort_analysis`: Chart for analyzing cohorts over time
- `win_loss_analysis`: Chart for analyzing factors in wins and losses
- `sales_velocity`: Chart for analyzing sales velocity
- `forecast_comparison`: Chart for comparing forecasts to actuals

#### Predictive Charts
- `forecast`: Chart for showing forecasted values with confidence intervals
- `anomaly_detection`: Chart for highlighting anomalies in data
- `what_if_analysis`: Interactive chart for exploring different scenarios
- `trend_prediction`: Chart for showing predicted trends

### Module Integrations

The module supports integration with the following Aizako CRM modules:

- `workflow`: Create and manage workflows
- `insights`: Generate and analyze insights
- `proposal_generator`: Create and manage proposals
- `objection_handler`: Handle objections in the sales process
- `follow_up_coach`: Create and manage follow-up sequences
- `win_loss_analyzer`: Analyze wins and losses
- `meeting_prep`: Prepare for meetings

### Adding New Features

To add new features to the module:

1. Update the relevant model files to include the new feature types
2. Update the service files to handle the new feature types
3. Update the AI service to generate content for the new feature types
4. Update the frontend components to display the new feature types
5. Update the documentation to include the new feature types

## Security Considerations

- All workflows and insights are tenant-scoped to prevent data leakage.
- Workflow execution is subject to the same permissions as the user who created the workflow.
- Insights are cached but expire after 24 hours to ensure data freshness.
- All natural language processing is done through secure API calls to the AI service.

## Performance Considerations

- Workflow execution is asynchronous to prevent blocking the main thread.
- Insights are cached to reduce the load on the AI service.
- Large workflows are paginated to prevent memory issues.
- Chart rendering is done client-side to reduce server load.

## Error Handling

- Workflow parsing errors are returned to the user with suggestions for improvement.
- Workflow execution errors are logged and can be viewed in the workflow run history.
- Insight generation errors are handled gracefully with fallback to simpler insights.
- All errors are logged for monitoring and debugging.
