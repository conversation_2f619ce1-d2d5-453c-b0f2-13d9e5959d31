# Proposal Generator Quick Reference

## Overview

This quick reference guide provides a summary of the key features and functionality of the Proposal Generator in Aizako CRM.

## Key Features

### Document Generation
- Create professional proposals with AI assistance
- Multiple formats: PDF, DOCX, Markdown, Claude-Enhanced HTML
- Customizable templates and sections
- AI enhancement for content and design

### Email Integration
- Configure multiple email providers (SMTP, SendGrid, Mailgun, SES, Gmail, Outlook)
- Customizable email templates
- Attachment support for proposal documents
- Tracking of email opens and interactions

### Public Sharing
- Secure shareable links with expiration dates
- Public viewing page for clients
- Download options for supported formats
- No login required for clients to view

### Analytics and Tracking
- View and download tracking
- Engagement metrics (view duration, interactions)
- Conversion funnel analysis
- Performance comparison across proposals

## Quick Start

### Creating a Proposal
1. Go to Proposal Generator
2. Click "Create Proposal"
3. Select opportunity and template
4. Fill in details and generate

### Configuring Email
1. Go to Email Configuration
2. Select provider and enter credentials
3. Customize email templates
4. Test configuration

### Sharing a Proposal
1. Open a proposal
2. Click "Share"
3. Choose sharing method (link or email)
4. Set expiration and format

### Viewing Analytics
1. Go to Proposal Analytics
2. View dashboard for overall metrics
3. Check individual proposal performance
4. Use insights to optimize future proposals

## Format Comparison

| Feature | PDF | DOCX | Markdown | Claude-Enhanced |
|---------|-----|------|----------|-----------------|
| Downloadable | ✅ | ✅ | ✅ | ❌ |
| Editable | ❌ | ✅ | ✅ | ❌ |
| Interactive | ❌ | ❌ | ❌ | ✅ |
| Print-ready | ✅ | ✅ | ❌ | ❌ |
| Visual design | Good | Good | Basic | Excellent |
| File size | Large | Medium | Small | N/A (web) |

## Email Provider Comparison

| Provider | Setup Complexity | Cost | Features | Best For |
|----------|------------------|------|----------|----------|
| SMTP | Medium | Free* | Basic | Self-hosted email |
| Gmail | Low | Free/Paid | OAuth, Reliable | Small teams |
| Outlook | Low | Paid | OAuth, Business | Microsoft users |
| SendGrid | Low | Free/Paid | Analytics, Scaling | High volume |
| Mailgun | Medium | Free/Paid | Deliverability | Developers |
| Amazon SES | High | Pay-per-use | Reliable, Cheap | AWS users |

*Depends on your email server

## Analytics Metrics Explained

| Metric | Description | Why It Matters |
|--------|-------------|----------------|
| Views | Number of times proposal was viewed | Indicates reach |
| Unique Views | Number of unique viewers | True audience size |
| Downloads | Number of times proposal was downloaded | Interest level |
| Average View Duration | Average time spent viewing | Engagement quality |
| Conversion Rate | % of viewed proposals that were accepted | Effectiveness |

## Common Tasks

### Regenerating a Document in a Different Format
1. Open the proposal
2. Click "Generate Document"
3. Select the desired format
4. Click "Generate"

### Testing Email Configuration
1. Go to Email Configuration
2. Navigate to "Test & Verify" tab
3. Enter a test email address
4. Click "Send Test Email"

### Creating a Shareable Link with Expiration
1. Open the proposal
2. Click "Share"
3. Select "Shareable Link"
4. Choose format and expiration period
5. Click "Generate Link"

### Tracking Proposal Performance
1. Go to Proposal Analytics
2. Find your proposal in the list
3. Click the analytics icon
4. View detailed performance metrics

## Troubleshooting

### Email Not Sending
- Check email configuration credentials
- Verify SMTP port and security settings
- Ensure email provider allows application access
- Check for rate limiting or sending quotas

### Document Generation Issues
- Try regenerating the document
- Check for complex formatting that might cause issues
- Ensure content is properly formatted in the editor
- For Claude-Enhanced format, ensure AI service is available

### Analytics Not Updating
- Allow up to 5 minutes for processing
- Refresh the analytics page
- Check that tracking is not blocked by client's browser
- Verify the shareable link is valid and not expired

### Public Viewing Issues
- Verify the link hasn't expired
- Check that the document was generated successfully
- Try regenerating the shareable link
- Ensure the client's browser supports the document format

## Best Practices

1. **Use Claude-Enhanced format** for initial sharing (more engaging)
2. **Provide downloadable formats** for client review and sharing
3. **Set reasonable expiration dates** (7-30 days recommended)
4. **Follow up** after sending based on analytics insights
5. **Personalize email messages** when sharing
6. **Monitor view duration** to identify sections that need improvement
7. **Test your email configuration** before sending important proposals
8. **Use analytics** to refine your proposal strategy over time

## Keyboard Shortcuts

| Action | Shortcut |
|--------|----------|
| Create New Proposal | Ctrl/Cmd + N |
| Save Proposal | Ctrl/Cmd + S |
| Generate Document | Ctrl/Cmd + G |
| Share Proposal | Ctrl/Cmd + Shift + S |
| View Analytics | Ctrl/Cmd + A |
| Email Configuration | Ctrl/Cmd + E |

## API Endpoints

### Proposal Management
- `GET /api/proposals` - List all proposals
- `GET /api/proposals/:id` - Get a specific proposal
- `POST /api/proposals` - Create a new proposal
- `PATCH /api/proposals/:id` - Update a proposal
- `DELETE /api/proposals/:id` - Delete a proposal

### Document Generation
- `POST /api/proposals/:id/generate` - Generate a document
- `GET /api/proposals/:id/document` - Get the document

### Sharing
- `POST /api/proposals/:id/share/link` - Create a shareable link
- `POST /api/proposals/:id/share/email` - Share via email
- `POST /api/proposals/:id/share/social` - Share on social media

### Public Access
- `GET /api/public/proposals/:token` - Get public proposal data
- `GET /api/public/proposals/:token/content` - Get document content
- `POST /api/public/proposals/:token/track` - Track interaction

### Analytics
- `GET /api/proposals/:id/analytics` - Get proposal analytics
- `GET /api/proposals/analytics/summary` - Get summary analytics

### Email Configuration
- `GET /api/email-config` - Get email configuration
- `POST /api/email-config` - Save email configuration
- `POST /api/email-config/test` - Test email configuration
