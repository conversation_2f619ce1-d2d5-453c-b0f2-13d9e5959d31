# Aizako CRM Subscription System - Product Requirements Document

## 1. Overview

The Aizako CRM Subscription System provides a flexible, centralized way to manage subscription plans, features, and resource limits across all modules of the application. This document outlines the requirements and implementation guidelines for integrating the subscription system into each module.

## 2. Core Principles

1. **Centralized Definition**: All subscription plans, features, and limits are defined in a central location
2. **Distributed Enforcement**: Each module is responsible for enforcing its own features and limits
3. **Real-time Propagation**: Changes to subscription plans are immediately propagated to all modules
4. **Feature Flagging**: Individual features can be enabled/disabled per subscription plan
5. **Usage Tracking**: Resource usage is tracked against subscription limits

## 3. System Architecture

### 3.1 Data Model

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ SubscriptionPlan│     │ Feature         │     │ Tenant          │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ id              │     │ id              │     │ id              │
│ name            │     │ key             │     │ name            │
│ description     │     │ name            │     │ slug            │
│ status          │     │ description     │     │ ownerId         │
│ isDefault       │     │ category        │     │ settings        │
│ price           │     │ module          │     │ status          │
│ limits          │     │ defaultValue    │     └─────────────────┘
│ features        │     └─────────────────┘             │
└─────────────────┘                                     │
        │                                               │
        │                                               │
        ▼                                               ▼
┌─────────────────┐                           ┌─────────────────┐
│TenantSubscription│◄────────────────────────►│ TenantUsage     │
├─────────────────┤                           ├─────────────────┤
│ id              │                           │ id              │
│ tenantId        │                           │ tenantId        │
│ planId          │                           │ period          │
│ status          │                           │ usage           │
│ startDate       │                           │ featureUsage    │
│ endDate         │                           └─────────────────┘
│ customLimits    │
│ customFeatures  │
└─────────────────┘
```

### 3.2 Component Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                     Subscription Service                     │
├─────────────────────────────────────────────────────────────┤
│ - Manages subscription plans, features, and limits          │
│ - Handles tenant subscriptions                              │
│ - Tracks resource usage                                     │
│ - Publishes subscription events                             │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                     Event Distribution                       │
├─────────────────────────────────────────────────────────────┤
│ - Propagates subscription changes to all modules            │
│ - Provides real-time updates                                │
└───┬───────────────────┬───────────────────┬────────────────┘
    │                   │                   │
    ▼                   ▼                   ▼
┌──────────┐      ┌──────────┐      ┌──────────┐
│ Module A │      │ Module B │      │ Module C │
└──────────┘      └──────────┘      └──────────┘
```

## 4. Module Integration Requirements

Each module must implement the following to integrate with the subscription system:

### 4.1 Subscription Client

Each module must implement a client to communicate with the subscription service:

```typescript
// Example for JavaScript/TypeScript modules
class SubscriptionClient {
  constructor(apiUrl) {
    this.apiUrl = apiUrl;
    this.cache = {};
    this.cacheTimestamp = null;
    this.refreshCache();
  }

  async refreshCache() {
    const response = await fetch(`${this.apiUrl}/api/subscription/cache`);
    if (response.ok) {
      this.cache = await response.json();
      this.cacheTimestamp = Date.now();
    }
  }

  checkEntitlement(tenantId, featureKey) {
    // Check if cache is stale (older than 5 minutes)
    if (!this.cacheTimestamp || (Date.now() - this.cacheTimestamp) > 300000) {
      this.refreshCache();
    }
    
    // Implementation details...
    return isEnabled;
  }

  // Other methods...
}
```

```python
# Example for Python modules
class SubscriptionClient:
    def __init__(self, api_url):
        self.api_url = api_url
        self.cache = {}
        self.cache_timestamp = None
        self.refresh_cache()
    
    def refresh_cache(self):
        response = requests.get(f"{self.api_url}/api/subscription/cache")
        if response.status_code == 200:
            self.cache = response.json()
            self.cache_timestamp = datetime.now()
    
    def check_entitlement(self, tenant_id, feature_key):
        # Check if cache is stale (older than 5 minutes)
        if not self.cache_timestamp or (datetime.now() - self.cache_timestamp).seconds > 300:
            self.refresh_cache()
        
        # Implementation details...
        return is_enabled
    
    # Other methods...
```

### 4.2 Event Listeners

Each module must implement event listeners to receive subscription change events:

```typescript
// Example for JavaScript/TypeScript modules
class SubscriptionEventListener {
  constructor(eventSource) {
    this.eventSource = eventSource;
    this.subscriptionClient = new SubscriptionClient();
    this.setupListeners();
  }

  setupListeners() {
    this.eventSource.addEventListener('subscription.plan.updated', (event) => {
      this.handlePlanUpdated(JSON.parse(event.data));
    });

    this.eventSource.addEventListener('subscription.tenant.updated', (event) => {
      this.handleTenantSubscriptionUpdated(JSON.parse(event.data));
    });

    // Other event listeners...
  }

  handlePlanUpdated(data) {
    // Refresh the subscription cache
    this.subscriptionClient.refreshCache();
    // Additional handling...
  }

  handleTenantSubscriptionUpdated(data) {
    // Refresh the subscription cache
    this.subscriptionClient.refreshCache();
    // Additional handling...
  }
}
```

```python
# Example for Python modules
class SubscriptionEventListener:
    def __init__(self, event_source):
        self.event_source = event_source
        self.subscription_client = SubscriptionClient()
        self.setup_listeners()
    
    def setup_listeners(self):
        self.event_source.on('subscription.plan.updated', self.handle_plan_updated)
        self.event_source.on('subscription.tenant.updated', self.handle_tenant_subscription_updated)
        # Other event listeners...
    
    def handle_plan_updated(self, data):
        # Refresh the subscription cache
        self.subscription_client.refresh_cache()
        # Additional handling...
    
    def handle_tenant_subscription_updated(self, data):
        # Refresh the subscription cache
        self.subscription_client.refresh_cache()
        # Additional handling...
```

### 4.3 Feature Registration

Each module must register its features with the subscription service:

```typescript
// Example for JavaScript/TypeScript modules
async function registerFeatures() {
  const features = [
    {
      key: 'module.feature1',
      name: 'Feature 1',
      description: 'Description of Feature 1',
      category: 'category',
      module: 'module-name',
      defaultValue: false
    },
    // Other features...
  ];

  for (const feature of features) {
    await fetch('/api/admin/features', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(feature)
    });
  }
}
```

```python
# Example for Python modules
def register_features():
    features = [
        {
            "key": "module.feature1",
            "name": "Feature 1",
            "description": "Description of Feature 1",
            "category": "category",
            "module": "module-name",
            "defaultValue": False
        },
        # Other features...
    ]
    
    for feature in features:
        requests.post(
            f"{api_url}/api/admin/features",
            json=feature
        )
```

### 4.4 Feature Enforcement

Each module must enforce feature entitlements:

```typescript
// Example for JavaScript/TypeScript modules
async function handleRequest(req, res, next) {
  const tenantId = req.headers['x-tenant-id'];
  const featureKey = 'module.feature1';
  
  if (!tenantId) {
    return res.status(401).json({ message: 'Tenant ID is required' });
  }
  
  const isEnabled = await subscriptionClient.checkEntitlement(tenantId, featureKey);
  
  if (!isEnabled) {
    return res.status(403).json({ 
      message: `Feature '${featureKey}' is not available in your subscription plan`
    });
  }
  
  // Record feature usage
  await subscriptionClient.recordFeatureUsage(tenantId, featureKey);
  
  // Continue with the request
  next();
}
```

```python
# Example for Python modules
def handle_request(request):
    tenant_id = request.headers.get('X-Tenant-ID')
    feature_key = 'module.feature1'
    
    if not tenant_id:
        return JSONResponse(
            status_code=401,
            content={"message": "Tenant ID is required"}
        )
    
    is_enabled = subscription_client.check_entitlement(tenant_id, feature_key)
    
    if not is_enabled:
        return JSONResponse(
            status_code=403,
            content={"message": f"Feature '{feature_key}' is not available in your subscription plan"}
        )
    
    # Record feature usage
    subscription_client.record_feature_usage(tenant_id, feature_key)
    
    # Continue with the request
    # ...
```

### 4.5 Resource Tracking

Each module must track resource usage:

```typescript
// Example for JavaScript/TypeScript modules
async function trackResourceUsage(tenantId, resourceType, amount) {
  await subscriptionClient.recordUsage(tenantId, resourceType, amount);
}

// Example usage
async function createDocument(tenantId, document) {
  // Check if within storage limits
  const documentSize = calculateDocumentSize(document); // in MB
  const isWithinLimit = await subscriptionClient.checkResourceLimit(tenantId, 'storage', documentSize);
  
  if (!isWithinLimit) {
    throw new Error('Storage limit exceeded');
  }
  
  // Create the document
  const result = await documentService.createDocument(document);
  
  // Record usage
  await trackResourceUsage(tenantId, 'storage', documentSize);
  
  return result;
}
```

```python
# Example for Python modules
def track_resource_usage(tenant_id, resource_type, amount):
    subscription_client.record_usage(tenant_id, resource_type, amount)

# Example usage
def create_document(tenant_id, document):
    # Check if within storage limits
    document_size = calculate_document_size(document)  # in MB
    is_within_limit = subscription_client.check_resource_limit(tenant_id, 'storage', document_size)
    
    if not is_within_limit:
        raise Exception('Storage limit exceeded')
    
    # Create the document
    result = document_service.create_document(document)
    
    # Record usage
    track_resource_usage(tenant_id, 'storage', document_size)
    
    return result
```

## 5. Subscription Change Propagation

### 5.1 Event Types

The subscription system publishes the following events:

| Event Type | Description | Payload |
|------------|-------------|---------|
| `subscription.plan.created` | A new subscription plan is created | Plan data |
| `subscription.plan.updated` | A subscription plan is updated | Plan data |
| `subscription.plan.deleted` | A subscription plan is deleted | Plan ID |
| `subscription.tenant.created` | A new tenant subscription is created | Subscription data |
| `subscription.tenant.updated` | A tenant subscription is updated | Subscription data |
| `subscription.tenant.canceled` | A tenant subscription is canceled | Subscription data |
| `subscription.feature.registered` | A new feature is registered | Feature data |
| `subscription.feature.updated` | A feature is updated | Feature data |
| `subscription.feature.removed` | A feature is removed | Feature ID |

### 5.2 Event Distribution Methods

The subscription system supports multiple methods for distributing events:

#### 5.2.1 Server-Sent Events (SSE)

```typescript
// Server implementation
app.get('/api/subscription/events', (req, res) => {
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  
  // Send initial data
  res.write(`data: ${JSON.stringify({ type: 'connected' })}\n\n`);
  
  // Add this client to the list of connected clients
  const clientId = Date.now();
  clients.set(clientId, res);
  
  // Remove client when connection is closed
  req.on('close', () => {
    clients.delete(clientId);
  });
});

// Function to publish events to all connected clients
function publishEvent(type, data) {
  const event = `event: ${type}\ndata: ${JSON.stringify(data)}\n\n`;
  for (const client of clients.values()) {
    client.write(event);
  }
}
```

```javascript
// Client implementation
const eventSource = new EventSource('/api/subscription/events');

eventSource.addEventListener('subscription.plan.updated', (event) => {
  const data = JSON.parse(event.data);
  // Handle plan update
});

eventSource.addEventListener('subscription.tenant.updated', (event) => {
  const data = JSON.parse(event.data);
  // Handle tenant subscription update
});
```

#### 5.2.2 WebSockets

```typescript
// Server implementation
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
  // Send initial data
  ws.send(JSON.stringify({ type: 'connected' }));
  
  // Add this client to the list of connected clients
  const clientId = Date.now();
  clients.set(clientId, ws);
  
  // Remove client when connection is closed
  ws.on('close', () => {
    clients.delete(clientId);
  });
});

// Function to publish events to all connected clients
function publishEvent(type, data) {
  const event = JSON.stringify({ type, data });
  for (const client of clients.values()) {
    if (client.readyState === WebSocket.OPEN) {
      client.send(event);
    }
  }
}
```

```javascript
// Client implementation
const socket = new WebSocket('ws://localhost:5000');

socket.onmessage = (event) => {
  const { type, data } = JSON.parse(event.data);
  
  if (type === 'subscription.plan.updated') {
    // Handle plan update
  } else if (type === 'subscription.tenant.updated') {
    // Handle tenant subscription update
  }
};
```

#### 5.2.3 Message Queue (Redis, RabbitMQ, etc.)

```typescript
// Publisher implementation
async function publishEvent(type, data) {
  await redisClient.publish('subscription-events', JSON.stringify({ type, data }));
}

// Subscriber implementation
redisClient.subscribe('subscription-events');
redisClient.on('message', (channel, message) => {
  if (channel === 'subscription-events') {
    const { type, data } = JSON.parse(message);
    
    if (type === 'subscription.plan.updated') {
      // Handle plan update
    } else if (type === 'subscription.tenant.updated') {
      // Handle tenant subscription update
    }
  }
});
```

### 5.3 Cache Invalidation

When a subscription change occurs, modules should invalidate their cache:

```typescript
// Example cache invalidation
function handleSubscriptionChange() {
  // Clear the cache
  subscriptionCache = null;
  cacheTimestamp = null;
  
  // Refresh the cache
  refreshCache();
}
```

## 6. Module-Specific Implementation Guidelines

### 6.1 Core Backend Module

1. **Features to Register**:
   - `core.contacts`
   - `core.companies`
   - `core.opportunities`
   - `core.activities`
   - `core.relationships`

2. **Resources to Track**:
   - `users`
   - `contacts`
   - `companies`
   - `opportunities`
   - `storage`

3. **Implementation Points**:
   - Add middleware to check feature entitlements for each API endpoint
   - Add middleware to check resource limits for creation endpoints
   - Record resource usage after successful creation operations
   - Listen for subscription events and update local cache

### 6.2 AI Service Module

1. **Features to Register**:
   - `ai.assistant.basic`
   - `ai.assistant.advanced`
   - `ai.insights`
   - `ai.document`

2. **Resources to Track**:
   - `ai.tokens`

3. **Implementation Points**:
   - Check feature entitlements before processing AI requests
   - Track token usage for each AI request
   - Enforce different capabilities based on subscription plan
   - Listen for subscription events and update local cache

### 6.3 Frontend Module

1. **Implementation Points**:
   - Use the `useSubscription` hook to access subscription data
   - Use the `FeatureGate` component to conditionally render features
   - Display usage information using the `UsageDisplay` component
   - Allow users to select plans using the `PlanSelector` component
   - Listen for subscription events and update local cache

## 7. Testing Requirements

Each module must include tests for subscription integration:

1. **Unit Tests**:
   - Test feature entitlement checks
   - Test resource limit checks
   - Test usage tracking

2. **Integration Tests**:
   - Test subscription event handling
   - Test cache invalidation
   - Test subscription changes propagation

3. **End-to-End Tests**:
   - Test the complete subscription flow across modules
   - Test upgrading and downgrading subscription plans
   - Test feature access based on subscription plan

## 8. Deployment Considerations

1. **Database Migrations**:
   - Create migration scripts for the subscription schema
   - Include seed data for default subscription plans and features

2. **Environment Configuration**:
   - Configure environment variables for subscription service endpoints
   - Configure event distribution mechanism

3. **Monitoring**:
   - Set up monitoring for subscription service
   - Track subscription events and usage patterns
   - Alert on subscription-related errors

## 9. Implementation Timeline

1. **Phase 1: Core Infrastructure** (Week 1-2)
   - Implement subscription data model
   - Create subscription service
   - Set up event distribution mechanism

2. **Phase 2: Module Integration** (Week 3-4)
   - Integrate with Core Backend Module
   - Integrate with AI Service Module
   - Integrate with Frontend Module

3. **Phase 3: Testing and Refinement** (Week 5-6)
   - Conduct unit, integration, and end-to-end tests
   - Refine implementation based on test results
   - Document the subscription system

## 10. Appendix

### 10.1 API Reference

#### Subscription Plans API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/admin/subscription-plans` | GET | Get all subscription plans |
| `/api/admin/subscription-plans/:id` | GET | Get a specific subscription plan |
| `/api/admin/subscription-plans` | POST | Create a new subscription plan |
| `/api/admin/subscription-plans/:id` | PUT | Update a subscription plan |
| `/api/admin/subscription-plans/:id` | DELETE | Delete a subscription plan |

#### Features API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/admin/features` | GET | Get all features |
| `/api/admin/features/:id` | GET | Get a specific feature |
| `/api/admin/features` | POST | Register a new feature |
| `/api/admin/features/:id` | PUT | Update a feature |
| `/api/admin/features/:id` | DELETE | Remove a feature |

#### Tenant Subscriptions API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/admin/tenant-subscriptions` | GET | Get all tenant subscriptions |
| `/api/admin/tenant-subscriptions/:id` | GET | Get a specific tenant subscription |
| `/api/admin/tenant-subscriptions` | POST | Create a new tenant subscription |
| `/api/admin/tenant-subscriptions/:id` | PUT | Update a tenant subscription |

#### User-Facing API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/subscription` | GET | Get the current user's subscription |
| `/api/subscription/features` | GET | Get features available to the current user |
| `/api/subscription/usage` | GET | Get the current user's resource usage |
| `/api/subscription/check-feature/:featureKey` | GET | Check if a feature is enabled |
| `/api/subscription/check-limit/:resourceType` | GET | Check if within resource limits |
| `/api/subscription/cache` | GET | Get subscription cache for synchronization |

### 10.2 Event Payload Examples

#### Subscription Plan Updated

```json
{
  "type": "subscription.plan.updated",
  "data": {
    "id": 1,
    "name": "Professional",
    "description": "Advanced CRM features for growing businesses",
    "status": "active",
    "isDefault": false,
    "price": "49",
    "currency": "USD",
    "billingPeriod": "monthly",
    "limits": {
      "users": 20,
      "contacts": 10000,
      "companies": 1000,
      "opportunities": 500,
      "storage": 10000,
      "apiRequests": 10000
    },
    "features": {
      "core.contacts": true,
      "core.companies": true,
      "core.opportunities": true,
      "core.activities": true,
      "core.relationships": true,
      "ai.assistant.basic": true,
      "ai.assistant.advanced": true,
      "ai.insights": true,
      "ai.document": true,
      "api.access": true
    }
  }
}
```

#### Tenant Subscription Updated

```json
{
  "type": "subscription.tenant.updated",
  "data": {
    "id": 1,
    "tenantId": "1",
    "planId": 3,
    "status": "active",
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2024-01-01T00:00:00.000Z",
    "customLimits": {
      "users": 25,
      "storage": 15000
    },
    "customFeatures": {
      "ai.document": true
    }
  }
}
```

### 10.3 Feature Registration Examples

#### Core Module Features

```json
[
  {
    "key": "core.contacts",
    "name": "Contact Management",
    "description": "Create and manage contacts",
    "category": "core",
    "module": "core",
    "defaultValue": true
  },
  {
    "key": "core.companies",
    "name": "Company Management",
    "description": "Create and manage companies",
    "category": "core",
    "module": "core",
    "defaultValue": true
  },
  {
    "key": "core.opportunities",
    "name": "Opportunity Management",
    "description": "Create and manage sales opportunities",
    "category": "core",
    "module": "core",
    "defaultValue": true
  },
  {
    "key": "core.activities",
    "name": "Activity Tracking",
    "description": "Track activities with contacts and companies",
    "category": "core",
    "module": "core",
    "defaultValue": true
  },
  {
    "key": "core.relationships",
    "name": "Relationship Mapping",
    "description": "Map relationships between contacts and companies",
    "category": "core",
    "module": "core",
    "defaultValue": false
  }
]
```

#### AI Module Features

```json
[
  {
    "key": "ai.assistant.basic",
    "name": "Basic AI Assistant",
    "description": "Basic AI assistant for simple queries",
    "category": "ai",
    "module": "ai",
    "defaultValue": true
  },
  {
    "key": "ai.assistant.advanced",
    "name": "Advanced AI Assistant",
    "description": "Advanced AI assistant with CRM data integration",
    "category": "ai",
    "module": "ai",
    "defaultValue": false
  },
  {
    "key": "ai.insights",
    "name": "AI Insights",
    "description": "AI-generated insights from your CRM data",
    "category": "ai",
    "module": "ai",
    "defaultValue": false
  },
  {
    "key": "ai.document",
    "name": "Document Intelligence",
    "description": "Extract information from documents using AI",
    "category": "ai",
    "module": "ai",
    "defaultValue": false
  }
]
```
