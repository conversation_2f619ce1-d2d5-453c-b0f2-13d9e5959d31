# Code Quality Guide

This document outlines the code quality standards and tools used in the Aizako CRM project.

## Tools

The project uses the following tools to maintain code quality:

1. **TypeScript** - For static type checking
2. **ESLint** - For code linting
3. **Prettier** - For code formatting
4. **Vitest** - For unit testing
5. **Jest** - For integration testing
6. **Playwright** - For UI testing
7. **GitHub Actions** - For continuous integration

## TypeScript

TypeScript is used throughout the project to provide static type checking. The project uses a centralized type system located in the `shared/types` directory.

### Type System

The type system is organized as follows:

- `shared/types/core.ts` - Core entity types
- `shared/types/api.ts` - API request/response types
- `shared/types/forms.ts` - Form-specific types
- `shared/types/utils.ts` - Utility types
- `shared/types/guards.ts` - Type guards
- `shared/types/index.ts` - Re-exports all types

For more information, see the [Type System Guide](./type-system.md).

## ESLint

ESLint is used to enforce code quality rules. The configuration is located in `.eslintrc.js`.

### Running ESLint

```bash
# Check for linting errors
npm run lint

# Fix linting errors
npm run lint:fix
```

### Key ESLint Rules

- No unused variables
- No explicit `any` types (warning)
- Proper import ordering
- React hooks rules
- Accessibility rules

## Prettier

Prettier is used to enforce consistent code formatting. The configuration is located in `.prettierrc`.

### Running Prettier

```bash
# Format all files
npm run format

# Check if files are formatted correctly
npm run format:check
```

### Prettier Configuration

```json
{
  "semi": true,
  "singleQuote": true,
  "trailingComma": "es5",
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "jsxSingleQuote": false,
  "bracketSameLine": false
}
```

## Testing

The project uses a hybrid testing approach with different tools for different types of tests.

### Unit Tests (Vitest)

Unit tests are used to test individual functions, utilities, and business logic.

```bash
# Run unit tests
npm run test:unit

# Run unit tests in watch mode
npm run test:unit:watch

# Run unit tests with coverage
npm run test:unit:coverage
```

### Integration Tests (Jest)

Integration tests are used to test API endpoints and database interactions.

```bash
# Run integration tests
npm run test:integration
```

### UI Tests (Playwright)

UI tests are used to test user interfaces and user flows.

```bash
# Run UI tests
npm run test:ui

# Run UI tests in headed mode
npm run test:ui:headed
```

For more information, see the [Testing Guide](./testing-guide.md).

## Continuous Integration

The project uses GitHub Actions for continuous integration. The workflow is defined in `.github/workflows/tests.yml`.

### CI Workflow

The CI workflow runs the following jobs:

1. **Lint** - Runs ESLint and Prettier
2. **Unit Tests** - Runs unit tests with Vitest
3. **Integration Tests** - Runs integration tests with Jest
4. **UI Tests** - Runs UI tests with Playwright
5. **Build** - Runs type checking and builds the project

### Running CI Locally

You can run the CI workflow locally using the following commands:

```bash
# Run linting
npm run lint && npm run format:check

# Run unit tests
npm run test:unit

# Run integration tests
npm run test:integration

# Run UI tests
npm run test:ui

# Run type checking and build
npm run check && npm run build
```

## Best Practices

### Code Organization

- Keep files small and focused on a single responsibility
- Use meaningful file and directory names
- Group related files together
- Use index files to re-export from directories

### Naming Conventions

- Use PascalCase for component names, interfaces, and types
- Use camelCase for variables, functions, and methods
- Use UPPER_CASE for constants
- Use kebab-case for file names

### Comments

- Use JSDoc comments for functions, classes, and interfaces
- Use inline comments sparingly and only when necessary
- Focus on explaining "why" rather than "what" or "how"

### Error Handling

- Use try/catch blocks for error handling
- Provide meaningful error messages
- Log errors with appropriate context
- Use custom error classes for specific error types

### Performance

- Avoid unnecessary re-renders in React components
- Use memoization for expensive calculations
- Use pagination for large data sets
- Optimize database queries

### Accessibility

- Use semantic HTML elements
- Provide alt text for images
- Use ARIA attributes when necessary
- Ensure keyboard navigation works
- Test with screen readers

### Security

- Validate user input
- Use parameterized queries for database operations
- Implement proper authentication and authorization
- Use HTTPS for all API requests
- Sanitize data before rendering
