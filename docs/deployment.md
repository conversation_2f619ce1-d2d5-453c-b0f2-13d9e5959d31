# Deployment Guide

This document outlines the deployment process for the Aizako CRM project.

## Environments

The project has two environments:

1. **Staging** - Used for testing and validation before production deployment
2. **Production** - The live environment used by customers

## Deployment Process

The deployment process is automated using GitHub Actions. The workflow is defined in `.github/workflows/deploy.yml`.

### Automatic Deployment

The deployment process is triggered automatically when:

1. A new release is created (deploys to staging)
2. Manually triggered via GitHub Actions (can deploy to staging or production)

### Manual Deployment

To manually trigger a deployment:

1. Go to the GitHub repository
2. Click on "Actions"
3. Select the "Deploy" workflow
4. Click "Run workflow"
5. Select the environment (staging or production)
6. Click "Run workflow"

## Deployment Workflow

The deployment workflow consists of the following steps:

1. **Prepare** - Determines the environment and version to deploy
2. **Deploy** - Builds and deploys the application to the selected environment
3. **Rollback** - Automatically rolls back to the previous version if deployment fails

### Deployment Steps

1. Checkout code
2. Install dependencies
3. Build the application
4. Create deployment package
5. Upload package to S3
6. Deploy to EC2 instance
7. Create deployment record
8. Verify deployment
9. Notify on success

### Rollback Steps

If the deployment fails, the system will automatically:

1. Revert to the previous deployment
2. Restart the service
3. Send a notification about the rollback

## Infrastructure

The application is deployed to AWS infrastructure:

- **S3** - Stores deployment packages
- **EC2** - Hosts the application
- **Route 53** - Manages DNS
- **CloudFront** - CDN for static assets
- **RDS** - Hosts the PostgreSQL database
- **MongoDB Atlas** - Hosts the MongoDB database

## Environment Variables

Environment-specific variables are stored in `.env.staging` and `.env.production` files. These files are not committed to the repository for security reasons.

Required environment variables:

- `NODE_ENV` - The environment (staging or production)
- `PORT` - The port to run the server on
- `DATABASE_URL` - PostgreSQL connection string
- `MONGODB_URI` - MongoDB connection string
- `SESSION_SECRET` - Secret for session encryption
- `AWS_ACCESS_KEY_ID` - AWS access key
- `AWS_SECRET_ACCESS_KEY` - AWS secret key
- `S3_BUCKET` - S3 bucket for file storage
- `OPENAI_API_KEY` - OpenAI API key
- `VOYAGE_API_KEY` - Voyage API key
- `RESEND_API_KEY` - Resend API key

## Deployment History

The system keeps the last 5 deployments for each environment. This allows for quick rollbacks if needed.

Deployment packages are stored in:

- `/opt/aizako-crm-staging-{version}`
- `/opt/aizako-crm-production-{version}`

The current deployment is symlinked to:

- `/opt/aizako-crm-staging-current`
- `/opt/aizako-crm-production-current`

## Monitoring

The application is monitored using:

- **CloudWatch** - For logs and metrics
- **Sentry** - For error tracking
- **Datadog** - For application performance monitoring

## Troubleshooting

### Common Issues

1. **Deployment fails with "Permission denied"**
   - Check SSH key permissions
   - Verify AWS credentials

2. **Application starts but returns 500 errors**
   - Check environment variables
   - Verify database connection
   - Check logs for errors

3. **Rollback fails**
   - Manually SSH into the server
   - Check available deployments in `/opt`
   - Manually create symlink to working deployment
   - Restart service

### Logs

Logs are available in:

- CloudWatch Logs
- `/var/log/aizako-crm-{environment}.log`

### Manual Rollback

To manually roll back to a previous version:

1. SSH into the server
2. List available deployments: `ls -la /opt | grep aizako-crm-{environment}`
3. Create symlink to desired version: `ln -sfn /opt/aizako-crm-{environment}-{version} /opt/aizako-crm-{environment}-current`
4. Restart service: `sudo systemctl restart aizako-crm-{environment}`

## Deployment Schedule

- **Staging** - Deployed automatically after each release
- **Production** - Deployed manually after validation in staging

## Security Considerations

1. **Secrets Management**
   - All secrets are stored in GitHub Secrets
   - Environment variables are encrypted

2. **Access Control**
   - Only authorized users can trigger deployments
   - SSH access is restricted to deployment keys

3. **Data Protection**
   - Database backups are taken before each deployment
   - Sensitive data is encrypted at rest and in transit
