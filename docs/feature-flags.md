# Feature Flags

This document outlines the feature flag system used in the Aizako CRM project.

## Overview

Feature flags (also known as feature toggles) allow you to enable or disable features in your application without deploying new code. This is useful for:

- Gradual rollouts of new features
- A/B testing
- Canary releases
- Turning features on/off for specific users or tenants
- Emergency kill switches

## Architecture

The feature flag system consists of the following components:

1. **MongoDB Model**: Stores feature flag definitions
2. **Feature Flag Service**: Core logic for evaluating feature flags
3. **API Routes**: Endpoints for managing and checking feature flags
4. **React Hooks**: Client-side hooks for using feature flags in components
5. **Admin UI**: Interface for managing feature flags
6. **Type System**: Shared types, type guards, and Zod schemas for type safety and validation

### Type System Integration

The feature flag system is fully integrated with the centralized type system:

- **Shared Types**: Defined in `shared/types/feature-flags.ts`
- **Type Guards**: Defined in `shared/types/guards/feature-flags.ts`
- **Zod <PERSON>**: Defined in `shared/schemas/feature-flags.ts`
- **Mapping Functions**: Defined in `server/models/mongoose/feature-flag-model.ts`

This integration ensures type safety and validation throughout the feature flag system. For more details, see the [Type System Guide](./type-system.md).

## Feature Flag Model

Each feature flag has the following properties:

- `key`: Unique identifier for the feature flag (e.g., `new-dashboard`)
- `name`: Human-readable name (e.g., "New Dashboard")
- `description`: Optional description of the feature
- `enabled`: Global on/off switch
- `enabledForUsers`: List of specific user IDs for which the feature is enabled
- `enabledForTenants`: List of specific tenant IDs for which the feature is enabled
- `enabledForPercentage`: Percentage of users for which the feature is enabled (0-100)
- `rules`: Complex rules for enabling the feature (e.g., by location, time, etc.)
- `tags`: Categories or labels for organizing feature flags
- `createdBy`: User who created the feature flag
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp

## Using Feature Flags

### Server-Side

#### Middleware

Use the `requireFeatureFlag` middleware to protect routes:

```typescript
import { requireFeatureFlag } from '../middleware/feature-flag-middleware';

// Require a feature flag for a route
app.get('/api/new-feature',
  authenticateUser,
  requireFeatureFlag('new-feature'),
  (req, res) => {
    // This code only runs if the feature flag is enabled
    res.json({ success: true });
  }
);

// With options
app.get('/api/experimental',
  authenticateUser,
  requireFeatureFlag('experimental-api', {
    redirectTo: '/api/stable',
    errorMessage: 'Experimental API is not available'
  }),
  (req, res) => {
    // Experimental code
  }
);
```

#### Direct Check

Use the `FeatureFlagService` to check if a feature is enabled:

```typescript
import { FeatureFlagService } from '../services/feature-flag-service';

async function someFunction(req) {
  const context = FeatureFlagService.getContextFromRequest(req);

  if (await FeatureFlagService.isEnabled('new-feature', context)) {
    // New feature code
  } else {
    // Old feature code
  }
}
```

### Client-Side

#### React Hook

Use the `useFeatureFlag` hook in React components:

```tsx
import { useFeatureFlag } from '@/hooks/use-feature-flag';

function MyComponent() {
  const { isEnabled, isLoading } = useFeatureFlag('new-ui');

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {isEnabled ? (
        <NewUIComponent />
      ) : (
        <OldUIComponent />
      )}
    </div>
  );
}
```

#### Multiple Flags

Use the `useFeatureFlags` hook to check multiple flags at once:

```tsx
import { useFeatureFlags } from '@/hooks/use-feature-flag';

function MyComponent() {
  const { featureFlags, isLoading } = useFeatureFlags([
    'new-ui',
    'advanced-search',
    'analytics-dashboard'
  ]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {featureFlags['new-ui'] && <NewUIComponent />}
      {featureFlags['advanced-search'] && <AdvancedSearchComponent />}
      {featureFlags['analytics-dashboard'] && <AnalyticsDashboard />}
    </div>
  );
}
```

#### Component

Use the `FeatureFlag` component for conditional rendering:

```tsx
import { FeatureFlag } from '@/hooks/use-feature-flag';

function MyComponent() {
  return (
    <div>
      <FeatureFlag flag="new-ui">
        <NewUIComponent />
      </FeatureFlag>

      <FeatureFlag flag="beta-feature" fallback={<BetaSignupComponent />}>
        <BetaFeatureComponent />
      </FeatureFlag>
    </div>
  );
}
```

## Managing Feature Flags

### Admin UI

The admin UI provides a user-friendly interface for managing feature flags. It allows you to:

1. Create new feature flags
2. Edit existing feature flags
3. Enable/disable feature flags
4. Set percentage rollouts
5. Add/remove users and tenants
6. Define complex rules
7. Delete feature flags

To access the admin UI, navigate to `/admin/feature-flags` (requires admin privileges).

### API Endpoints

The following API endpoints are available for managing feature flags:

- `GET /api/feature-flags`: Get all feature flags
- `POST /api/feature-flags`: Create a new feature flag
- `GET /api/feature-flags/:key`: Get a specific feature flag
- `PATCH /api/feature-flags/:key`: Update a feature flag
- `DELETE /api/feature-flags/:key`: Delete a feature flag
- `GET /api/feature-flags/check/:key`: Check if a feature flag is enabled
- `POST /api/feature-flags/check-batch`: Check multiple feature flags at once

## Best Practices

### Naming Conventions

Use consistent naming for feature flags:

- Use kebab-case for keys (e.g., `new-dashboard`)
- Be descriptive but concise
- Use prefixes for categorization (e.g., `ui-`, `api-`, `beta-`)

### Lifecycle Management

Feature flags should be temporary:

1. **Creation**: Create the flag and implement the feature behind it
2. **Rollout**: Gradually enable the feature for more users
3. **Cleanup**: Once the feature is stable, remove the flag and the old code

### Testing

Always test both states of a feature flag:

- Test with the feature flag enabled
- Test with the feature flag disabled
- Test the transition between states

### Documentation

Document all feature flags:

- What the feature flag controls
- Who should have it enabled
- When it was created
- When it should be removed
- Any dependencies on other feature flags

## Examples

### Gradual Rollout

```typescript
// Create a feature flag for a gradual rollout
await FeatureFlagService.createFlag({
  key: 'new-dashboard',
  name: 'New Dashboard',
  description: 'Redesigned dashboard with improved analytics',
  enabled: false,
  enabledForPercentage: 10, // Start with 10% of users
  tags: ['ui', 'dashboard'],
  createdBy: adminUserId,
});

// Increase the percentage over time
await FeatureFlagService.updateFlag('new-dashboard', {
  enabledForPercentage: 25, // Increase to 25%
});

// Eventually enable for everyone
await FeatureFlagService.updateFlag('new-dashboard', {
  enabled: true,
  enabledForPercentage: 100,
});
```

### Beta Feature

```typescript
// Create a feature flag for beta users
await FeatureFlagService.createFlag({
  key: 'ai-assistant',
  name: 'AI Assistant',
  description: 'AI-powered assistant for customer interactions',
  enabled: false,
  enabledForUsers: betaUserIds,
  tags: ['beta', 'ai'],
  createdBy: adminUserId,
});
```

### Time-Based Feature

```typescript
// Create a feature flag for a holiday promotion
await FeatureFlagService.createFlag({
  key: 'holiday-promotion',
  name: 'Holiday Promotion',
  description: 'Special holiday offers',
  enabled: false,
  rules: [
    {
      type: 'date',
      value: {
        startDate: '2023-12-01T00:00:00Z',
        endDate: '2023-12-31T23:59:59Z',
      },
    },
  ],
  tags: ['promotion', 'seasonal'],
  createdBy: adminUserId,
});
```
