# Repository Design Patterns

This guide explains the repository design patterns used in the Aizako CRM application. It provides an overview of the patterns, their benefits, and how they are implemented.

## Table of Contents

1. [Introduction](#introduction)
2. [Repository Pattern](#repository-pattern)
3. [Data Mapper Pattern](#data-mapper-pattern)
4. [Unit of Work Pattern](#unit-of-work-pattern)
5. [Query Object Pattern](#query-object-pattern)
6. [Specification Pattern](#specification-pattern)
7. [Factory Pattern](#factory-pattern)
8. [Singleton Pattern](#singleton-pattern)
9. [Decorator Pattern](#decorator-pattern)
10. [Conclusion](#conclusion)

## Introduction

Design patterns are reusable solutions to common problems in software design. They provide a way to structure code that is maintainable, scalable, and testable. The Aizako CRM application uses several design patterns for data access, each with its own benefits and use cases.

## Repository Pattern

The repository pattern is the primary pattern used for data access in the Aizako CRM application. It abstracts the data access layer from the business logic, providing a clean separation of concerns.

### Implementation

The repository pattern is implemented through the `IRepository` interface and its concrete implementations like `MongoDBRepository`. Each entity has its own repository that extends the base repository and adds specialized methods.

```typescript
export interface IRepository<T, K = string> {
  findById(id: K, tenantId: string): Promise<T | null>;
  findAll(tenantId: string, options?: QueryOptions<T>): Promise<T[]>;
  // ... other methods
}

export class MongoDBRepository<T, D extends Document> implements IRepository<T> {
  // ... implementation
}

export class ContactRepository extends MongoDBRepository<IContact, mongoose.Document & IContact> implements IContactRepository {
  // ... specialized methods
}
```

### Benefits

- **Separation of Concerns**: The repository pattern separates the data access logic from the business logic, making the code more maintainable.
- **Testability**: Repositories can be easily mocked for testing.
- **Reusability**: Common data access operations are implemented once and reused across the application.
- **Abstraction**: The repository pattern abstracts the underlying data storage, making it easier to change the data storage technology if needed.

## Data Mapper Pattern

The data mapper pattern is used to convert between domain entities and database models. It ensures that the domain model is not tied to the database schema.

### Implementation

The data mapper pattern is implemented through the `toEntity` and `toDocument` methods in the `MongoDBRepository` class.

```typescript
protected toEntity(doc: D | null): T | null {
  if (!doc) return null;
  
  try {
    // Convert database document to domain entity
    const entity = { ...doc.toObject() } as any;

    // Convert _id to id
    if (entity._id) {
      entity.id = entity._id.toString();
      delete entity._id;
    }

    // Remove __v
    delete entity.__v;

    return entity as T;
  } catch (error) {
    // ... error handling
  }
}

protected toDocument(entity: Partial<T>): Partial<D> {
  if (!entity) return {} as Partial<D>;
  
  try {
    // Convert domain entity to database document
    const doc = { ...entity } as any;

    // Convert id to _id if present
    if (doc?.id) {
      doc._id = doc.id;
      delete doc.id;
    }

    return doc as Partial<D>;
  } catch (error) {
    // ... error handling
  }
}
```

### Benefits

- **Separation of Concerns**: The data mapper pattern separates the domain model from the database schema, making the code more maintainable.
- **Flexibility**: The domain model can evolve independently of the database schema.
- **Testability**: Data mappers can be easily tested in isolation.

## Unit of Work Pattern

The unit of work pattern is used to ensure that multiple operations are treated as a single transaction. It tracks changes to objects and coordinates the writing of changes to the database.

### Implementation

The unit of work pattern is implemented through MongoDB transactions. The `mongoose.startSession()` method is used to start a transaction, and the `session.commitTransaction()` and `session.abortTransaction()` methods are used to commit or abort the transaction.

```typescript
async function transferOpportunity(opportunityId: string, fromContactId: string, toContactId: string, tenantId: string) {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    // Perform multiple operations
    // ...
    
    // Commit the transaction
    await session.commitTransaction();
    session.endSession();
    
    return result;
  } catch (error) {
    // Abort the transaction on error
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
}
```

### Benefits

- **Atomicity**: The unit of work pattern ensures that multiple operations are treated as a single transaction, either all succeeding or all failing.
- **Consistency**: The database remains in a consistent state even if errors occur.
- **Isolation**: Transactions are isolated from each other, preventing interference.
- **Durability**: Once a transaction is committed, its changes are permanent.

## Query Object Pattern

The query object pattern is used to encapsulate query parameters and logic. It separates the query construction from the query execution.

### Implementation

The query object pattern is implemented through the `QueryOptions` interface and its usage in repository methods.

```typescript
export interface QueryOptions<T> {
  filter?: TypedFilter<T>;
  sort?: TypedSort<T>;
  projection?: TypedProjection<T>;
  page?: number;
  limit?: number;
  // ... other options
}

async findAll(tenantId: string, options: QueryOptions<T> = {}): Promise<T[]> {
  // ... implementation
}
```

### Benefits

- **Separation of Concerns**: The query object pattern separates the query construction from the query execution, making the code more maintainable.
- **Reusability**: Query objects can be reused across the application.
- **Testability**: Query objects can be easily tested in isolation.
- **Flexibility**: Query objects can be composed and extended to create complex queries.

## Specification Pattern

The specification pattern is used to encapsulate business rules and criteria. It separates the criteria from the objects being evaluated.

### Implementation

The specification pattern is implemented through the `TypedFilter` type and its usage in repository methods.

```typescript
export type TypedFilter<T> = {
  [P in keyof T]?: T[P] | FilterOperators<T[P]>;
} & Record<string, any>;

async findAll(tenantId: string, options: QueryOptions<T> = {}): Promise<T[]> {
  const { filter = {} } = options;
  
  const query = this.model.find({
    tenantId,
    ...filter as FilterQuery<D>,
  });
  
  // ... implementation
}
```

### Benefits

- **Separation of Concerns**: The specification pattern separates the criteria from the objects being evaluated, making the code more maintainable.
- **Reusability**: Specifications can be reused across the application.
- **Testability**: Specifications can be easily tested in isolation.
- **Composability**: Specifications can be composed to create complex criteria.

## Factory Pattern

The factory pattern is used to create objects without specifying the exact class of object that will be created. It provides an interface for creating objects, but allows subclasses to alter the type of objects that will be created.

### Implementation

The factory pattern is implemented through the repository constructors and the `create` method.

```typescript
constructor(model: Model<D>, entityName: string) {
  this.model = model;
  this.entityName = entityName;
}

async create(entity: Partial<T>, tenantId: string): Promise<T> {
  // ... implementation
}
```

### Benefits

- **Encapsulation**: The factory pattern encapsulates the object creation logic, making the code more maintainable.
- **Flexibility**: The factory pattern allows for the creation of different types of objects based on the context.
- **Testability**: Factories can be easily mocked for testing.

## Singleton Pattern

The singleton pattern is used to ensure that a class has only one instance and provides a global point of access to it. It is useful when exactly one object is needed to coordinate actions across the system.

### Implementation

The singleton pattern is implemented through the repository exports.

```typescript
// Create and export a singleton instance
export const contactRepository = new ContactRepository();

export default contactRepository;
```

### Benefits

- **Global Access**: The singleton pattern provides a global point of access to the repository instance.
- **Resource Sharing**: The singleton pattern ensures that resources are shared across the application.
- **Consistency**: The singleton pattern ensures that the same instance is used throughout the application.

## Decorator Pattern

The decorator pattern is used to add new functionality to an object without altering its structure. It is a structural pattern that allows behavior to be added to an individual object, either statically or dynamically, without affecting the behavior of other objects from the same class.

### Implementation

The decorator pattern is implemented through the repository inheritance and method overriding.

```typescript
export class ContactRepository extends MongoDBRepository<IContact, mongoose.Document & IContact> implements IContactRepository {
  async findByEmail(email: string, tenantId: string): Promise<IContact | null> {
    // ... implementation
  }
}
```

### Benefits

- **Extensibility**: The decorator pattern allows for the addition of new functionality to an object without altering its structure.
- **Flexibility**: The decorator pattern allows for the dynamic addition of behavior to an object.
- **Reusability**: Decorators can be reused across the application.

## Conclusion

The Aizako CRM application uses a combination of design patterns for data access, each with its own benefits and use cases. These patterns work together to create a clean, maintainable, and testable codebase.

The repository pattern provides a clean separation of concerns between the data access layer and the business logic. The data mapper pattern ensures that the domain model is not tied to the database schema. The unit of work pattern ensures that multiple operations are treated as a single transaction. The query object pattern separates the query construction from the query execution. The specification pattern separates the criteria from the objects being evaluated. The factory pattern provides an interface for creating objects. The singleton pattern ensures that a class has only one instance. The decorator pattern adds new functionality to an object without altering its structure.

By understanding these patterns and how they are implemented in the Aizako CRM application, developers can write clean, maintainable, and testable code that follows best practices.
