# Aizako CRM Documentation

Welcome to the Aizako CRM documentation. This index provides links to all available documentation for the Aizako CRM system.

## User Guides

User guides provide instructions for using the Aizako CRM system.

- [Win/Loss Analyzer and Follow-up Coach](user-guides/win-loss-follow-up-features.md)
- [Proposal Generator](user-guides/proposal-generator-features.md)

## Technical Documentation

Technical documentation provides detailed information about the implementation of the Aizako CRM system.

- [Win/Loss Analyzer and Follow-up Coach Implementation](technical/win-loss-follow-up-implementation.md)
- [Proposal Generator Implementation](technical/proposal-generator-implementation.md)
- [Subscription System](subscription-system.md)
- [MongoDB Atlas Storage](mongodb-atlas-storage.md)

## Repository Documentation

Repository documentation provides information about the data access layer of the Aizako CRM system.

- [Repository Documentation](repositories.md)
- [Repository Usage Guide](repository-usage-guide.md)
- [Repository Testing Guide](repository-testing-guide.md)
- [Repository Design Patterns](repository-design-patterns.md)

## Quick Reference Guides

Quick reference guides provide concise information for common tasks.

- [Proposal Generator Quick Reference](quick-reference/proposal-generator-quick-reference.md)

## Release Notes

Release notes provide information about new features and improvements in each release.

- [Proposal Generator Enhancements](release-notes/proposal-generator-enhancements.md)

## Additional Resources

Additional resources provide supplementary information about the Aizako CRM system.

- [MongoDB Implementation](../README-MONGODB.md)
- [AI Service](../ai_service/README.md)

## API Documentation

API documentation provides information about the Aizako CRM API.

- [API Overview](api/overview.md)
- [Authentication](api/authentication.md)
- [Endpoints](api/endpoints.md)
- [Error Handling](api/error-handling.md)
- [Rate Limiting](api/rate-limiting.md)
- [Webhooks](api/webhooks.md)

## Architecture

Architecture documentation provides information about the overall architecture of the Aizako CRM system.

- [System Architecture](architecture/system-architecture.md)
- [Frontend Architecture](architecture/frontend-architecture.md)
- [Backend Architecture](architecture/backend-architecture.md)
- [AI Architecture](architecture/ai-architecture.md)
- [Database Architecture](architecture/database-architecture.md)
- [Security Architecture](architecture/security-architecture.md)

## Development Guides

Development guides provide information for developers working on the Aizako CRM system.

- [Development Environment Setup](development/environment-setup.md)
- [Coding Standards](development/coding-standards.md)
- [Testing](development/testing.md)
- [Continuous Integration](development/continuous-integration.md)
- [Deployment](development/deployment.md)
- [Contributing](development/contributing.md)

## Troubleshooting

Troubleshooting guides provide information for resolving common issues.

- [Common Issues](troubleshooting/common-issues.md)
- [Error Messages](troubleshooting/error-messages.md)
- [Support](troubleshooting/support.md)

## Glossary

The glossary provides definitions for terms used in the Aizako CRM system.

- [Glossary](glossary.md)

## Index

The index provides a comprehensive list of all documentation topics.

- [Index](index.md)
