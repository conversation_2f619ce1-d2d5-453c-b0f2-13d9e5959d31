# A/B Testing

This document outlines the A/B testing framework used in the Aizako CRM project.

## Overview

A/B testing (also known as split testing) allows you to compare two or more versions of a feature to determine which one performs better. This is useful for:

- Testing new UI designs
- Optimizing conversion rates
- Evaluating new features
- Making data-driven decisions

## Architecture

The A/B testing framework consists of the following components:

1. **MongoDB Models**: Store experiment definitions and results
2. **Experiment Service**: Core logic for assigning variants and tracking conversions
3. **API Routes**: Endpoints for managing experiments and tracking conversions
4. **React Hooks**: Client-side hooks for using experiments in components
5. **Admin UI**: Interface for managing experiments and viewing results
6. **Type System**: Shared types, type guards, and Zod schemas for type safety and validation

### Type System Integration

The A/B testing framework is fully integrated with the centralized type system:

- **Shared Types**: Defined in `shared/types/experiments.ts`
- **Type Guards**: Defined in `shared/types/guards/experiments.ts`
- **<PERSON><PERSON>**: Defined in `shared/schemas/experiments.ts`
- **Mapping Functions**: Defined in `server/models/mongoose/experiment-model.ts`

This integration ensures type safety and validation throughout the A/B testing framework. For more details, see the [Type System Guide](./type-system.md).

## Experiment Model

Each experiment has the following properties:

- `key`: Unique identifier for the experiment (e.g., `new-dashboard-design`)
- `name`: Human-readable name (e.g., "New Dashboard Design")
- `description`: Optional description of the experiment
- `status`: Current status (`draft`, `running`, `paused`, `completed`, `archived`)
- `startDate`: When the experiment starts
- `endDate`: When the experiment ends
- `variants`: Different versions to test (e.g., `control` and `treatment`)
- `audience`: Who to include in the experiment (percentage, specific users, etc.)
- `goals`: Metrics to track (e.g., `signup`, `purchase`, etc.)
- `createdBy`: User who created the experiment
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp

## Using A/B Testing

### Server-Side

Use the `ExperimentService` to get the variant for a user:

```typescript
import { ExperimentService } from '../services/experiment-service';

async function someFunction(req) {
  const context = ExperimentService.getContextFromRequest(req);

  const variant = await ExperimentService.getVariant('new-dashboard-design', context);

  if (variant === 'treatment') {
    // Show new dashboard
  } else {
    // Show old dashboard
  }
}
```

### Client-Side

#### React Hook

Use the `useExperiment` hook in React components:

```tsx
import { useExperiment } from '@/hooks/use-experiment';

function MyComponent() {
  const { variant, isLoading, trackConversion } = useExperiment('new-dashboard-design');

  // Track a conversion when user clicks a button
  const handleClick = () => {
    trackConversion('button_click');
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {variant === 'treatment' ? (
        <NewDashboard onButtonClick={handleClick} />
      ) : (
        <OldDashboard onButtonClick={handleClick} />
      )}
    </div>
  );
}
```

#### Component

Use the `ExperimentVariant` component for conditional rendering:

```tsx
import { ExperimentVariant } from '@/hooks/use-experiment';

function MyComponent() {
  return (
    <div>
      <ExperimentVariant
        experiment="new-dashboard-design"
        variant="treatment"
        onMount={() => console.log('Treatment variant shown')}
      >
        <NewDashboard />
      </ExperimentVariant>

      <ExperimentVariant
        experiment="new-dashboard-design"
        variant="control"
      >
        <OldDashboard />
      </ExperimentVariant>
    </div>
  );
}
```

#### Tracking Conversions

Use the `trackConversion` function to track goals:

```tsx
import { useExperiment } from '@/hooks/use-experiment';

function MyComponent() {
  const { variant, trackConversion } = useExperiment('new-dashboard-design');

  const handlePurchase = (amount) => {
    // Track a purchase conversion with a value
    trackConversion('purchase', amount, {
      productId: '123',
      category: 'electronics',
    });

    // Process the purchase
  };

  return (
    <div>
      <button onClick={() => handlePurchase(99.99)}>
        Buy Now
      </button>
    </div>
  );
}
```

## Managing Experiments

### Admin UI

The admin UI provides a user-friendly interface for managing experiments. It allows you to:

1. Create new experiments
2. Edit experiment settings
3. Start, pause, and stop experiments
4. View experiment results
5. Archive completed experiments

To access the admin UI, navigate to `/admin/experiments` (requires admin privileges).

### API Endpoints

The following API endpoints are available for managing experiments:

- `GET /api/experiments`: Get all experiments
- `POST /api/experiments`: Create a new experiment
- `GET /api/experiments/:key`: Get a specific experiment
- `PATCH /api/experiments/:key`: Update an experiment
- `DELETE /api/experiments/:key`: Delete an experiment
- `GET /api/experiments/:key/variant`: Get the variant for the current user
- `POST /api/experiments/:key/conversion`: Track a conversion
- `GET /api/experiments/:key/results`: Get experiment results

## Best Practices

### Experiment Design

1. **Clear Hypothesis**: Start with a clear hypothesis about what you're testing and why
2. **Single Variable**: Test one thing at a time to get clear results
3. **Sufficient Sample Size**: Ensure you have enough users to get statistically significant results
4. **Meaningful Metrics**: Choose goals that directly relate to your business objectives
5. **Control Group**: Always include a control group for comparison

### Implementation

1. **Consistent Experience**: Users should get the same variant across sessions
2. **Minimal Flicker**: Avoid showing the control briefly before switching to the treatment
3. **Graceful Fallback**: Have a fallback in case the experiment service fails
4. **Performance Impact**: Minimize the performance impact of the experiment code

### Analysis

1. **Statistical Significance**: Wait for statistically significant results before drawing conclusions
2. **Segmentation**: Analyze results across different user segments
3. **Secondary Metrics**: Look at secondary metrics to understand broader impact
4. **Correlation vs. Causation**: Be careful about attributing causation

## Examples

### Simple UI Test

```typescript
// Create a simple UI test experiment
await ExperimentService.createExperiment({
  key: 'button-color',
  name: 'Button Color Test',
  description: 'Testing different button colors for the signup page',
  variants: [
    { key: 'control', name: 'Blue Button', weight: 50 },
    { key: 'treatment', name: 'Green Button', weight: 50 },
  ],
  goals: [
    { key: 'click', name: 'Button Click', primary: true },
    { key: 'signup', name: 'Completed Signup' },
  ],
  createdBy: adminUserId,
});

// Start the experiment
await ExperimentService.updateExperiment('button-color', {
  status: 'running',
  startDate: new Date(),
  endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
});
```

### Feature Test with Multiple Variants

```typescript
// Create a feature test with multiple variants
await ExperimentService.createExperiment({
  key: 'onboarding-flow',
  name: 'Onboarding Flow Test',
  description: 'Testing different onboarding flows for new users',
  variants: [
    { key: 'control', name: 'Current Flow', weight: 34 },
    { key: 'minimal', name: 'Minimal Flow', weight: 33 },
    { key: 'guided', name: 'Guided Flow', weight: 33 },
  ],
  audience: {
    percentage: 100,
    filters: [
      {
        type: 'user',
        value: { isNew: true },
      },
    ],
  },
  goals: [
    { key: 'completion', name: 'Completed Onboarding', primary: true },
    { key: 'time_spent', name: 'Time Spent in Onboarding' },
    { key: 'feature_usage', name: 'Feature Usage After Onboarding' },
  ],
  createdBy: adminUserId,
});
```

### Targeted Experiment

```typescript
// Create an experiment for specific users
await ExperimentService.createExperiment({
  key: 'premium-features',
  name: 'Premium Features Test',
  description: 'Testing new premium features with enterprise customers',
  variants: [
    { key: 'control', name: 'Current Features', weight: 50 },
    { key: 'treatment', name: 'New Features', weight: 50 },
  ],
  audience: {
    tenantIds: enterpriseTenantIds,
  },
  goals: [
    { key: 'usage', name: 'Feature Usage', primary: true },
    { key: 'retention', name: 'Account Retention' },
    { key: 'upgrade', name: 'Plan Upgrade' },
  ],
  createdBy: adminUserId,
});
```
