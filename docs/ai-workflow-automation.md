# AI & Workflow Automation Module

The AI & Workflow Automation module for Aizako CRM provides powerful tools for creating automated workflows using natural language and generating data-driven insights from your CRM data.

## Table of Contents

1. [Overview](#overview)
2. [Visual NL Workflow Builder](#visual-nl-workflow-builder)
   - [Creating Workflows](#creating-workflows)
   - [Workflow Components](#workflow-components)
   - [Managing Workflows](#managing-workflows)
3. [Predictive Insights Hub](#predictive-insights-hub)
   - [Asking Questions](#asking-questions)
   - [Understanding Insights](#understanding-insights)
   - [Using Recommendations](#using-recommendations)
4. [Technical Reference](#technical-reference)
   - [API Endpoints](#api-endpoints)
   - [Data Models](#data-models)
5. [Troubleshooting](#troubleshooting)

## Overview

The AI & Workflow Automation module consists of two main components:

1. **Visual NL Workflow Builder (NL-WB)**: Create workflows by describing them in plain English. The system automatically converts your description into an executable workflow that can automate tasks across Aizako CRM.

2. **Predictive Insights Hub (PI-Hub)**: Ask business questions in natural language and receive AI-generated insights, including charts, narratives, and recommended actions.

## Visual NL Workflow Builder

### Creating Workflows

To create a new workflow:

1. Navigate to the **Workflow Builder** page from the sidebar.
2. Enter a name and description for your workflow.
3. Use the **Natural Language** tab to describe your workflow in plain English.
   - Example: "When a lead's score reaches 80 and there's no reply in 2 days, send a 'Nudge' email and assign to an SDR."
4. Click **Create Workflow** to generate the workflow.
5. Review the workflow in the **Visual Canvas** tab.
6. Make any necessary adjustments in the **DSL Editor** tab.
7. Click **Save Workflow** to save your workflow.

### Workflow Components

Workflows consist of the following components:

#### Triggers

Triggers are events that start a workflow:

**Basic Triggers**
- **lead_score_change**: Triggered when a lead's score changes
- **email_event**: Triggered by email events (open, click, reply)
- **form_submission**: Triggered when a form is submitted
- **record_update**: Triggered when a record is updated
- **webhook**: Triggered by an external webhook

**Time-Based Triggers**
- **scheduled**: Run at a specific time or on a recurring schedule
- **anniversary**: Run on anniversaries like customer sign-up date

**Behavioral Triggers**
- **website_activity**: Triggered by specific website activity patterns
- **product_usage**: Triggered by product usage metrics

**External System Triggers**
- **linkedin_event**: Triggered by LinkedIn events

**Advanced CRM Triggers**
- **relationship_change**: Triggered when a relationship between contacts changes
- **pipeline_velocity**: Triggered when deal movement slows or speeds up
- **team_performance**: Triggered based on team performance metrics

#### Actions

Actions are tasks that the workflow performs:

**Basic Actions**
- **send_email**: Send an email to a contact
- **update_record**: Update a record in the CRM
- **create_task**: Create a task for a user
- **call_webhook**: Call an external webhook

**Communication Actions**
- **send_sms**: Send text messages to contacts
- **schedule_meeting**: Automatically schedule meetings
- **create_video_message**: Generate personalized video messages

**Data Enrichment Actions**
- **enrich_contact**: Automatically enrich contact data
- **score_lead**: Update lead scoring based on custom algorithms
- **generate_ai_content**: Create personalized content using AI

**Integration Actions**
- **create_document**: Generate documents like proposals
- **update_external_system**: Update records in external systems
- **post_to_linkedin**: Create LinkedIn posts

**Advanced CRM Actions**
- **create_relationship**: Establish relationships between contacts
- **assign_territory**: Automatically assign territories
- **forecast_update**: Update sales forecasts based on deal changes

#### Conditions

Conditions control the flow of the workflow:

**Basic Conditions**
- **if**: Check if a condition is true
- **wait_until**: Wait until a condition is true
- **for_each**: Loop through a collection

**Advanced Conditions**
- **switch**: Branch based on multiple conditions
- **parallel**: Execute multiple paths in parallel
- **retry**: Retry an action if it fails
- **delay**: Wait for a specific amount of time

**Data Conditions**
- **data_exists**: Check if data exists
- **data_equals**: Check if data equals a value
- **data_greater_than**: Check if data is greater than a value
- **data_less_than**: Check if data is less than a value

**Time Conditions**
- **time_of_day**: Check if current time is within a range
- **day_of_week**: Check if current day is a specific day
- **business_hours**: Check if current time is within business hours

### Managing Workflows

From the Workflow Builder page, you can:

- **View** existing workflows
- **Edit** workflows
- **Activate/Deactivate** workflows
- **Delete** workflows
- **Duplicate** workflows
- **View run history** to see when workflows have executed

## Predictive Insights Hub

### Asking Questions

To generate insights:

1. Navigate to the **Predictive Insights** page from the sidebar.
2. Enter your business question in the input field.
   - Example: "Why is Q2 pipeline soft compared to last quarter?"
3. Click **Generate Insight** to analyze your data.
4. View the generated insight, including chart, narrative, and recommendations.

### Understanding Insights

Each insight includes:

- **Chart**: Visual representation of the data relevant to your question
  - **Basic Charts**: Bar, line, pie, funnel, scatter, table
  - **Advanced Visualizations**: Sankey, heatmap, radar, treemap, bubble
  - **Business-Specific Charts**: Pipeline waterfall, cohort analysis, win/loss analysis, sales velocity, forecast comparison
  - **Predictive Charts**: Forecast, anomaly detection, what-if analysis, trend prediction
  - **Interactive Features**: Drill-down capabilities, linked charts, animated transitions
- **Narrative**: Written explanation of the insight (up to 200 words)
- **Why It Matters**: Bullet points highlighting the business impact
- **Recommended Actions**: Suggested next steps based on the insight

### Using Recommendations

For each recommended action, you can:

- **Create Workflow**: Automatically create a workflow based on the recommendation
- **Take Action**: Follow a direct link to take the recommended action
- **Share Insight**: Share the insight with team members
- **Module Integrations**: Directly integrate with other Aizako CRM modules:
  - **Proposal Generator**: Create customized proposals based on insights
  - **Objection Handler**: Prepare for objections identified in the insight
  - **Follow-up Coach**: Create follow-up sequences based on insight recommendations
  - **Win/Loss Analyzer**: Analyze factors contributing to wins and losses
  - **Meeting Prep Wizard**: Prepare for meetings with insights-driven talking points

## Technical Reference

### API Endpoints

#### Workflow API

- `POST /api/workflows`: Create a new workflow
- `GET /api/workflows`: List workflows
- `GET /api/workflows/:id`: Get workflow by ID
- `PUT /api/workflows/:id`: Update workflow
- `DELETE /api/workflows/:id`: Delete workflow
- `GET /api/workflows/:id/versions`: Get workflow versions
- `POST /api/workflows/:id/simulate`: Simulate workflow execution
- `POST /api/workflows/:id/execute`: Execute workflow
- `GET /api/workflows/:id/runs`: Get workflow runs
- `POST /api/workflows/parse`: Parse natural language to workflow DSL
- `POST /api/workflows/compile`: Compile workflow DSL to graph

#### Insights API

- `POST /api/insights/generate`: Generate insight from natural language question
- `GET /api/insights/cached`: Get cached insights
- `GET /api/insights/:id`: Get insight by ID
- `POST /api/insights/:id/feedback`: Submit feedback for an insight

### Data Models

#### Workflow Models

- **Workflow**: Defines a workflow, including triggers, actions, and conditions
- **WorkflowRun**: Records the execution of a workflow
- **WorkflowVersion**: Stores versions of a workflow for rollback

#### Insights Models

- **InsightsCache**: Stores generated insights for reuse

## Troubleshooting

### Common Workflow Issues

- **Workflow not triggering**: Check that the trigger conditions are met and the workflow is active.
- **Actions not executing**: Verify that the workflow has the necessary permissions.
- **Compilation errors**: Check the DSL syntax in the DSL Editor tab.

### Common Insights Issues

- **No data available**: Ensure that your CRM has sufficient data to answer the question.
- **Unclear question**: Try rephrasing your question to be more specific.
- **Outdated insight**: Click the Refresh button to generate a new insight with the latest data.

---

For additional support, contact your Aizako CRM administrator or support team.
