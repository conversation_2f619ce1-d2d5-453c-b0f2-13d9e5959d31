# Aizako CRM Type System

This document outlines the type system used in the Aizako CRM project, explaining how types are organized, how Zod is used for validation, and best practices for working with types.

## Directory Structure

```
shared/
  ├── types/           # TypeScript type definitions
  │   ├── core.ts      # Core entity types
  │   ├── api.ts       # API request/response types
  │   ├── forms.ts     # Form-specific types
  │   ├── utils.ts     # Utility types
  │   ├── feature-flags.ts # Feature flag types
  │   ├── experiments.ts # A/B testing types
  │   ├── analytics.ts # Analytics types
  │   ├── guards/      # Type guards
  │   │   ├── core.ts  # Core entity type guards
  │   │   ├── feature-flags.ts # Feature flag type guards
  │   │   ├── experiments.ts # Experiment type guards
  │   │   ├── analytics.ts # Analytics type guards
  │   │   └── index.ts # Re-exports all type guards
  │   └── index.ts     # Re-exports all types
  │
  ├── schemas/         # Zod schemas for validation
  │   ├── core.ts      # Core entity schemas
  │   ├── api.ts       # API request/response schemas
  │   ├── forms.ts     # Form validation schemas
  │   ├── feature-flags.ts # Feature flag schemas
  │   ├── experiments.ts # A/B testing schemas
  │   ├── analytics.ts # Analytics schemas
  │   └── index.ts     # Re-exports all schemas
  │
  └── utils/           # Shared utilities
      └── validation.ts # Zod validation utilities
```

## Type Organization

### Core Types

Core types represent the fundamental data models used throughout the application. These are defined in `shared/types/core.ts` and include:

- `User`
- `Contact`
- `Company`
- `Opportunity`
- `Activity`
- `Relationship`
- `Tenant`
- `SubscriptionPlan`

### API Types

API types represent request and response structures for API endpoints. These are defined in `shared/types/api.ts` and include:

- Request types (e.g., `CreateContactRequest`, `UpdateContactRequest`)
- Response types (e.g., `ContactResponse`, `ContactsResponse`)
- Authentication types (e.g., `LoginRequest`, `AuthResponse`)

### Form Types

Form types represent form state and validation for UI components. These are defined in `shared/types/forms.ts` and include:

- Authentication forms (e.g., `LoginFormValues`, `RegisterFormValues`)
- Entity forms (e.g., `ContactFormValues`, `CompanyFormValues`)
- Settings forms (e.g., `ProfileFormValues`, `ApiKeyFormValues`)

### Utility Types

Utility types provide reusable type patterns. These are defined in `shared/types/utils.ts` and include:

- `Id` - Represents an ID which can be either a string or number
- `WithId<T>` - Adds an ID field to any type
- `Timestamps` - Standard timestamp fields for database records
- `WithTimestamps<T>` - Adds timestamp fields to any type
- `ApiResponse<T>` - Standard API response wrapper
- `PaginatedResponse<T>` - Paginated API response
- `UpdateType<T>` - Makes all properties optional and adds an ID field
- `TenantRecord` - Tenant-specific record
- `WithTenant<T>` - Adds tenant ID to any type
- `UserReference` - User reference for tracking who created/updated records
- `WithUserReference<T>` - Adds user reference fields to any type
- `CompleteRecord<T>` - Complete record with ID, timestamps, tenant, and user references

### Feature Flag Types

Feature flag types represent the feature flag system. These are defined in `shared/types/feature-flags.ts` and include:

- `FeatureFlag` - Represents a feature flag
- `FeatureFlagRule` - Represents a rule for a feature flag
- `FeatureFlagRuleType` - Enum of rule types
- `FeatureFlagContext` - Context for evaluating feature flags
- API request/response types (e.g., `CreateFeatureFlagRequest`, `CheckFeatureFlagResponse`)

### Experiment Types

Experiment types represent the A/B testing system. These are defined in `shared/types/experiments.ts` and include:

- `Experiment` - Represents an A/B test
- `ExperimentVariant` - Represents a variant in an experiment
- `ExperimentGoal` - Represents a goal for an experiment
- `ExperimentStatus` - Enum of experiment statuses
- `ExperimentContext` - Context for evaluating experiments
- `ExperimentResult` - Represents the result of an experiment
- API request/response types (e.g., `CreateExperimentRequest`, `GetVariantResponse`)

### Analytics Types

Analytics types represent the analytics system. These are defined in `shared/types/analytics.ts` and include:

- `AnalyticsEvent` - Represents an analytics event
- `AnalyticsContext` - Context for an analytics event
- `AnalyticsSession` - Represents a user session
- `AnalyticsCount` - Represents a count of events
- `FunnelStep` - Represents a step in a funnel analysis
- API request/response types (e.g., `TrackEventRequest`, `GetEventsResponse`)

### Type Guards

Type guards provide runtime type checking. These are defined in `shared/types/guards/` directory and include:

#### Core Type Guards (`guards/core.ts`)
- `isUser(obj)` - Checks if an object is a User
- `isContact(obj)` - Checks if an object is a Contact
- `isCompany(obj)` - Checks if an object is a Company
- `isOpportunity(obj)` - Checks if an object is an Opportunity
- `isActivity(obj)` - Checks if an object is an Activity
- `isTenant(obj)` - Checks if an object is a Tenant

#### Feature Flag Type Guards (`guards/feature-flags.ts`)
- `isFeatureFlag(obj)` - Checks if an object is a FeatureFlag
- `isFeatureFlagRule(obj)` - Checks if an object is a FeatureFlagRule
- `isFeatureFlagRuleType(value)` - Checks if a value is a FeatureFlagRuleType

#### Experiment Type Guards (`guards/experiments.ts`)
- `isExperiment(obj)` - Checks if an object is an Experiment
- `isExperimentVariant(obj)` - Checks if an object is an ExperimentVariant
- `isExperimentGoal(obj)` - Checks if an object is an ExperimentGoal
- `isExperimentStatus(value)` - Checks if a value is an ExperimentStatus

#### Analytics Type Guards (`guards/analytics.ts`)
- `isAnalyticsEvent(obj)` - Checks if an object is an AnalyticsEvent
- `isAnalyticsContext(obj)` - Checks if an object is an AnalyticsContext
- `isAnalyticsSession(obj)` - Checks if an object is an AnalyticsSession

#### Utility Type Guards
- `isArrayOf<T>(arr, guard)` - Checks if an array contains items of a specific type
- `hasProperties<T>(obj, properties)` - Checks if an object has specific properties

## Zod Schema Organization

### Core Schemas

Core schemas define validation rules for the core entities. These are defined in `shared/schemas/core.ts` and include:

- Base schemas (e.g., `userBaseSchema`, `contactBaseSchema`)
- Create schemas (e.g., `createUserSchema`, `createContactSchema`)
- Update schemas (e.g., `updateUserSchema`, `updateContactSchema`)

### API Schemas

API schemas define validation rules for API requests and responses. These are defined in `shared/schemas/api.ts` and include:

- Request schemas (e.g., `createContactRequestSchema`, `updateContactRequestSchema`)
- Response schemas (e.g., `contactResponseSchema`, `contactsResponseSchema`)

### Form Schemas

Form schemas define validation rules for forms in the UI. These are defined in `shared/schemas/forms.ts` and include:

- Authentication form schemas (e.g., `loginFormSchema`, `registerFormSchema`)
- Entity form schemas (e.g., `contactFormSchema`, `companyFormSchema`)
- Settings form schemas (e.g., `profileFormSchema`, `apiKeyFormSchema`)

### Feature Flag Schemas

Feature flag schemas define validation rules for feature flags. These are defined in `shared/schemas/feature-flags.ts` and include:

- `featureFlagSchema` - Validates a feature flag
- `featureFlagRuleSchema` - Validates a feature flag rule
- `featureFlagRuleTypeSchema` - Validates a feature flag rule type
- `featureFlagContextSchema` - Validates a feature flag context
- Request/response schemas (e.g., `createFeatureFlagRequestSchema`, `updateFeatureFlagRequestSchema`)

### Experiment Schemas

Experiment schemas define validation rules for A/B tests. These are defined in `shared/schemas/experiments.ts` and include:

- `experimentSchema` - Validates an experiment
- `experimentVariantSchema` - Validates an experiment variant
- `experimentGoalSchema` - Validates an experiment goal
- `experimentStatusSchema` - Validates an experiment status
- `experimentContextSchema` - Validates an experiment context
- Request/response schemas (e.g., `createExperimentRequestSchema`, `trackConversionRequestSchema`)

### Analytics Schemas

Analytics schemas define validation rules for analytics events. These are defined in `shared/schemas/analytics.ts` and include:

- `analyticsEventSchema` - Validates an analytics event
- `analyticsContextSchema` - Validates an analytics context
- `analyticsSessionSchema` - Validates an analytics session
- Request/response schemas (e.g., `trackEventRequestSchema`, `funnelAnalysisRequestSchema`)

## Validation Utilities

Validation utilities help with handling Zod validation errors. These are defined in `shared/utils/validation.ts` and include:

- `formatZodError(error)` - Converts a Zod error to a user-friendly error message
- `validateData(schema, data)` - Safely validates data against a Zod schema
- `validateOrThrow(schema, data)` - Validates data and throws a formatted error if validation fails
- `validateRequest(schema)` - Express middleware for validating request body
- `validateQuery(schema)` - Express middleware for validating query parameters

## Best Practices

### Importing Types

Use path aliases to import types:

```typescript
// Import from the central types module
import { Contact, Company } from '@types/core';
import { CreateContactRequest } from '@types/api';
import { ContactFormValues } from '@types/forms';

// Import from the central schemas module
import { contactBaseSchema } from '@schemas/core';
import { createContactRequestSchema } from '@schemas/api';
import { contactFormSchema } from '@schemas/forms';
```

### Form Validation with Zod

Use Zod schemas with React Hook Form:

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { contactFormSchema } from '@schemas/forms';
import type { ContactFormValues } from '@types/forms';

const form = useForm<ContactFormValues>({
  resolver: zodResolver(contactFormSchema),
  defaultValues: {
    firstName: '',
    lastName: '',
    status: 'lead',
  },
});
```

### API Validation with Zod

Use validation utilities for API endpoints:

```typescript
import { validateRequest } from '@shared/utils/validation';
import { createContactRequestSchema } from '@schemas/api';

// Express route with validation
router.post('/contacts', validateRequest(createContactRequestSchema), (req, res) => {
  // req.validatedBody is typed and validated
  const contact = createContact(req.validatedBody);
  res.json({ success: true, contact });
});
```

### Type Guards for Runtime Checking

Use type guards for runtime type checking:

```typescript
import { isContact, isContactArray } from '@types/guards';

function processContact(data: unknown) {
  if (isContact(data)) {
    // data is now typed as Contact
    console.log(data.firstName, data.lastName);
  } else {
    console.error('Invalid contact data');
  }
}

function processContacts(data: unknown) {
  if (isContactArray(data)) {
    // data is now typed as Contact[]
    data.forEach(contact => {
      console.log(contact.firstName, contact.lastName);
    });
  } else {
    console.error('Invalid contacts data');
  }
}
```

## Extending the Type System

When adding new entities or features:

1. Add core types to `shared/types/core.ts` or create a new file in `shared/types/` for feature-specific types
2. Add API types to `shared/types/api.ts` or include them in your feature-specific types file
3. Add form types to `shared/types/forms.ts` if applicable
4. Add type guards to `shared/types/guards/core.ts` or create a new file in `shared/types/guards/` for feature-specific guards
5. Add Zod schemas to the corresponding schema files or create a new file in `shared/schemas/` for feature-specific schemas
6. Update index files to export the new types, guards, and schemas
7. Create mapping functions in MongoDB models to convert between database models and shared types
8. Update services to use type guards for validation
9. Update API routes to use Zod schemas for validation
10. Update React components to use shared types
11. Add unit tests for new schemas and type guards

### Example: Adding a New Feature

For example, when adding a new feature like "Feature Flags":

1. Create `shared/types/feature-flags.ts` with types like `FeatureFlag`, `FeatureFlagRule`, etc.
2. Create `shared/types/guards/feature-flags.ts` with guards like `isFeatureFlag`, `isFeatureFlagRule`, etc.
3. Create `shared/schemas/feature-flags.ts` with schemas like `featureFlagSchema`, `createFeatureFlagRequestSchema`, etc.
4. Update `shared/types/index.ts` to export the new types
5. Update `shared/types/guards/index.ts` to export the new guards
6. Update `shared/schemas/index.ts` to export the new schemas
7. Add mapping functions in `server/models/mongoose/feature-flag-model.ts`
8. Update `server/services/feature-flag-service.ts` to use the new types and guards
9. Update `server/routes/feature-flag-routes.ts` to use the new schemas
10. Update React components to use the new types
11. Add unit tests for the new schemas and guards

## Testing the Type System

The type system is tested using Vitest for unit tests:

```bash
# Run all unit tests
npm run test:unit

# Run specific tests for schemas
npm run test:unit -- tests/unit/shared/schemas/forms.test.ts
```

### Testing Schemas

```typescript
import { describe, it, expect } from 'vitest';
import { contactFormSchema } from '@schemas/forms';

describe('contactFormSchema', () => {
  it('should validate valid contact data', () => {
    const validData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      status: 'lead',
    };

    const result = contactFormSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should reject invalid contact data', () => {
    const invalidData = {
      // Missing required lastName
      firstName: 'John',
      email: 'not-an-email',
    };

    const result = contactFormSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
  });
});
```

### Testing Type Guards

```typescript
import { describe, it, expect } from 'vitest';
import { isContact } from '@types/guards';

describe('isContact', () => {
  it('should return true for valid contact', () => {
    const contact = {
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      status: 'lead',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    expect(isContact(contact)).toBe(true);
  });

  it('should return false for invalid contact', () => {
    const invalidContact = {
      id: '1',
      // Missing required fields
    };

    expect(isContact(invalidContact)).toBe(false);
  });
});
```

### Testing Validation Utilities

```typescript
import { describe, it, expect } from 'vitest';
import { validateData } from '@shared/utils/validation';
import { contactFormSchema } from '@schemas/forms';

describe('validateData', () => {
  it('should validate valid data', () => {
    const data = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      status: 'lead',
    };

    const result = validateData(contactFormSchema, data);

    expect(result.success).toBe(true);
    expect(result.data).toEqual(data);
  });
});
```

For more information on testing, see the [Testing Guide](./testing-guide.md).
