# MongoDB Atlas Storage Integration for Aizako CRM

## Current Implementation

The current implementation of document storage in Aizako CRM uses a hybrid approach:
- Document metadata is stored in MongoDB (in the `Document` collection)
- Actual file content is stored on the local file system in an `uploads` directory
- API endpoints serve the files by reading them from the file system

## Future Improvement: MongoDB Atlas Integration

For production deployment, we plan to migrate to MongoDB Atlas for improved scalability, reliability, and security. Here's the planned approach:

### MongoDB Atlas Storage Options

1. **MongoDB Atlas with GridFS**
   - Use GridFS to store large files directly in MongoDB
   - Benefits: Everything stays within MongoDB ecosystem
   - Considerations: May impact database performance for very large files

2. **MongoDB Atlas with AWS S3 Integration**
   - Store file metadata in MongoDB
   - Store actual files in AWS S3 via MongoDB Atlas's S3 integration
   - Benefits: Optimized for large file storage, potentially lower costs

### Implementation Plan

1. **Setup MongoDB Atlas**
   - Create a MongoDB Atlas cluster
   - Configure network access and authentication
   - Migrate existing MongoDB data to Atlas

2. **Configure Storage Integration**
   - If using GridFS:
     - Update document service to use GridFS for file storage
     - Implement GridFS streaming for file retrieval
   - If using S3 Integration:
     - Configure Atlas-S3 integration
     - Update document service to use S3 for file storage
     - Implement S3 presigned URLs for secure file access

3. **Migration Strategy**
   - Develop a script to migrate existing files from the file system to the new storage solution
   - Implement a fallback mechanism during migration to check both storage locations
   - Update document URLs in the database after migration

### Code Changes Required

#### GridFS Implementation Example

```typescript
import { MongoClient, GridFSBucket } from 'mongodb';

// Initialize GridFS bucket
const client = new MongoClient(process.env.MONGODB_URI);
const db = client.db('aizako-crm');
const bucket = new GridFSBucket(db, { bucketName: 'documents' });

// Store a file
async function storeFile(buffer: Buffer, filename: string, metadata: any): Promise<string> {
  return new Promise((resolve, reject) => {
    const uploadStream = bucket.openUploadStream(filename, {
      metadata
    });
    
    uploadStream.on('error', reject);
    uploadStream.on('finish', () => {
      resolve(uploadStream.id.toString());
    });
    
    uploadStream.end(buffer);
  });
}

// Retrieve a file
async function retrieveFile(fileId: string): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    const downloadStream = bucket.openDownloadStream(new ObjectId(fileId));
    
    downloadStream.on('data', chunk => chunks.push(chunk));
    downloadStream.on('error', reject);
    downloadStream.on('end', () => resolve(Buffer.concat(chunks)));
  });
}
```

#### S3 Integration Example

```typescript
import { S3Client, PutObjectCommand, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Initialize S3 client
const s3Client = new S3Client({ 
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
  }
});

// Upload file to S3
async function uploadToS3(buffer: Buffer, key: string, contentType: string): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: key,
    Body: buffer,
    ContentType: contentType
  });
  
  await s3Client.send(command);
  
  // Return the key to the file
  return key;
}

// Generate a presigned URL for secure access
async function getPresignedUrl(key: string, expiresIn = 3600): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: key
  });
  
  return getSignedUrl(s3Client, command, { expiresIn });
}
```

## Security Considerations

1. **Access Control**
   - Implement proper access controls to ensure users can only access their authorized documents
   - Use MongoDB Atlas's security features for database-level protection
   - If using S3, use presigned URLs with short expiration times

2. **Data Encryption**
   - Enable encryption at rest for both MongoDB Atlas and S3
   - Use HTTPS for all data transfers

3. **Backup Strategy**
   - Configure automated backups in MongoDB Atlas
   - If using S3, enable versioning for document recovery

## Performance Optimization

1. **Caching**
   - Implement a caching layer for frequently accessed documents
   - Consider using a CDN for public documents

2. **Compression**
   - Compress documents before storage to reduce storage costs and improve retrieval speed

3. **Indexing**
   - Create appropriate indexes on document metadata for faster queries

## Cost Considerations

1. **Storage Costs**
   - MongoDB Atlas storage costs vs. S3 storage costs
   - Consider lifecycle policies for archiving older documents

2. **Data Transfer Costs**
   - Monitor and optimize data transfer between services
   - Implement compression to reduce transfer sizes

## Timeline

- **Phase 1 (Planning)**: Evaluate storage options and finalize approach
- **Phase 2 (Development)**: Implement the chosen storage solution
- **Phase 3 (Migration)**: Migrate existing documents to the new storage
- **Phase 4 (Optimization)**: Monitor and optimize performance and costs

This document serves as a guide for future implementation of MongoDB Atlas storage integration for the Aizako CRM system.
