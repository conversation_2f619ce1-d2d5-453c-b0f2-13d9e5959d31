# Aizako CRM Testing Guide

This document outlines the testing strategy for the Aizako CRM project, explaining the different types of tests, how to write them, and how to run them.

## Testing Strategy

Aizako CRM uses a hybrid testing approach with different tools for different types of tests:

1. **Unit Tests** (Vitest)
   - Test individual functions, utilities, and business logic
   - Fast execution, high coverage
   - Located in `tests/unit/`

2. **Integration Tests** (Jest)
   - Test API endpoints and database interactions
   - Verify that components work together correctly
   - Located in `tests/integration/`

3. **UI Tests** (Playwright)
   - Test user interfaces and user flows
   - Verify that the application works correctly from the user's perspective
   - Located in `tests/ui/`

## Running Tests

### Unit Tests (Vitest)

```bash
# Run all unit tests
npm run test:unit

# Run unit tests in watch mode
npm run test:unit:watch

# Run unit tests with coverage
npm run test:unit:coverage
```

### Integration Tests (Jest)

```bash
# Run all integration tests
npm run test:integration

# Run specific integration tests
npm run test:integration -- tests/integration/api/contacts.test.ts
```

### UI Tests (Playwright)

```bash
# Run all UI tests
npm run test:ui

# Run UI tests in headed mode (visible browser)
npm run test:ui:headed
```

## Writing Tests

### Unit Tests

Unit tests are written using Vitest and should focus on testing individual functions and utilities. They should be fast, isolated, and have high coverage.

```typescript
// Example unit test for a validation utility
import { describe, it, expect } from 'vitest';
import { validateData } from '@shared/utils/validation';
import { contactFormSchema } from '@schemas/forms';

describe('validateData', () => {
  it('should validate valid data', () => {
    const data = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      status: 'lead',
    };

    const result = validateData(contactFormSchema, data);
    
    expect(result.success).toBe(true);
    expect(result.data).toEqual(data);
    expect(result.error).toBeUndefined();
  });

  it('should return error for invalid data', () => {
    const data = {
      firstName: 'J', // Too short
      email: 'not-an-email',
    };

    const result = validateData(contactFormSchema, data);
    
    expect(result.success).toBe(false);
    expect(result.data).toBeUndefined();
    expect(result.error).toBeDefined();
  });
});
```

### Integration Tests

Integration tests are written using Jest and should focus on testing API endpoints and database interactions. They should verify that components work together correctly.

```typescript
// Example integration test for an API endpoint
import request from 'supertest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import express from 'express';
import session from 'express-session';
import { Contact } from '@models/mongoose';
import contactRoutes from '@server/routes/mongo-contacts-routes';

describe('Contacts API', () => {
  let app: express.Express;
  let mongoServer: MongoMemoryServer;
  let testUserId: string;

  beforeAll(async () => {
    // Set up MongoDB memory server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create test user ID
    testUserId = new mongoose.Types.ObjectId().toString();

    // Set up Express app
    app = express();
    app.use(express.json());
    
    // Set up session middleware with mock user
    app.use(session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false,
    }));
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.session.userId = testUserId;
      next();
    });
    
    // Add contact routes
    app.use('/api/contacts', contactRoutes);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear contacts collection before each test
    await Contact.deleteMany({});
  });

  it('should create a new contact', async () => {
    const contactData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      status: 'lead',
    };

    const response = await request(app)
      .post('/api/contacts')
      .send(contactData);
    
    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.contact.firstName).toBe('John');
  });
});
```

### UI Tests

UI tests are written using Playwright and should focus on testing user interfaces and user flows. They should verify that the application works correctly from the user's perspective.

```typescript
// Example UI test for a form
import { test, expect } from '@playwright/test';

test.describe('Contact Form', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
        }),
      });
    });

    // Navigate to contacts page
    await page.goto('/contacts');
  });

  test('should create a new contact', async ({ page }) => {
    // Mock create contact API
    await page.route('**/api/contacts', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          body: JSON.stringify({
            success: true,
            contact: {
              id: '1',
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              status: 'lead',
            },
          }),
        });
      }
    });

    // Click add contact button
    await page.getByRole('button', { name: /add contact/i }).click();
    
    // Fill out form
    await page.getByLabel('First Name').fill('John');
    await page.getByLabel('Last Name').fill('Doe');
    await page.getByLabel('Email').fill('<EMAIL>');
    
    // Submit form
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check for success message
    await expect(page.getByText('Contact created successfully')).toBeVisible();
  });
});
```

## Test Coverage

We aim for high test coverage, especially for critical parts of the application:

- **Unit Tests**: 80%+ coverage for utility functions and business logic
- **Integration Tests**: 70%+ coverage for API endpoints
- **UI Tests**: Cover all critical user flows

To check test coverage for unit tests, run:

```bash
npm run test:unit:coverage
```

## Best Practices

### General

- Write tests before or alongside code (TDD/BDD)
- Keep tests simple and focused
- Use descriptive test names
- Avoid test interdependence
- Clean up after tests

### Unit Tests

- Test one thing per test
- Mock external dependencies
- Use setup and teardown hooks
- Group related tests with `describe`
- Test edge cases and error conditions

### Integration Tests

- Use in-memory databases for testing
- Mock external services
- Test happy paths and error paths
- Verify database state after operations
- Clean up database between tests

### UI Tests

- Mock API responses for predictable tests
- Test user flows, not implementation details
- Use page objects for complex pages
- Test accessibility
- Test responsive behavior

## Continuous Integration

Tests are run automatically on CI/CD pipelines:

1. Unit tests run on every push
2. Integration tests run on every PR
3. UI tests run on every PR to main branch

## Debugging Tests

### Vitest

```bash
# Run specific test file
npm run test:unit -- tests/unit/shared/utils/validation.test.ts

# Run tests matching a pattern
npm run test:unit -- -t "validateData"
```

### Jest

```bash
# Run with verbose output
npm run test:integration -- --verbose

# Run with debugger
node --inspect-brk node_modules/.bin/jest --runInBand tests/integration/api/contacts.test.ts
```

### Playwright

```bash
# Run with debug mode
npm run test:ui -- --debug

# Generate and open HTML report
npx playwright show-report
```

## Mocking

### Mocking API Responses

```typescript
// In Playwright tests
await page.route('**/api/contacts', async (route) => {
  await route.fulfill({
    status: 200,
    body: JSON.stringify({
      success: true,
      contacts: [
        { id: '1', firstName: 'John', lastName: 'Doe' },
      ],
    }),
  });
});

// In Jest tests
jest.mock('@/lib/api', () => ({
  fetchContacts: jest.fn().mockResolvedValue({
    success: true,
    contacts: [
      { id: '1', firstName: 'John', lastName: 'Doe' },
    ],
  }),
}));
```

### Mocking Database

```typescript
// Use mongodb-memory-server for MongoDB
import { MongoMemoryServer } from 'mongodb-memory-server';

const mongoServer = await MongoMemoryServer.create();
const mongoUri = mongoServer.getUri();
await mongoose.connect(mongoUri);

// Clean up
await mongoose.disconnect();
await mongoServer.stop();
```
