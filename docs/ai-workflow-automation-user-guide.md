# AI & Workflow Automation User Guide

This guide provides step-by-step instructions for using the AI & Workflow Automation module in Aizako CRM.

## Visual NL Workflow Builder

### Creating Your First Workflow

1. **Access the Workflow Builder**
   - Click on "Workflow Builder" in the sidebar navigation.
   - You'll see the Workflow Builder interface with three tabs: Natural Language, DSL Editor, and Visual Canvas.

2. **Enter Workflow Details**
   - Enter a name for your workflow (e.g., "High-Value Lead Follow-up").
   - Optionally, add a description to help others understand the workflow's purpose.

3. **Describe Your Workflow in Natural Language**
   - In the Natural Language tab, describe what you want your workflow to do.
   - Be specific about:
     - What should trigger the workflow
     - Any conditions that should be checked
     - What actions should be taken
     - Any timing requirements

   **Example:**
   ```
   When a lead's score reaches 80 and there's no reply in 2 days, send a 'Nudge' email template to the lead, then create a follow-up task for the assigned sales rep with a due date of 1 day later.
   ```

4. **Generate the Workflow**
   - Click "Create Workflow" to generate the workflow from your description.
   - The system will process your description and create a workflow DSL (Domain Specific Language).

5. **Review the Visual Representation**
   - Switch to the "Visual Canvas" tab to see a graphical representation of your workflow.
   - You'll see nodes for the trigger, conditions, and actions, connected by edges.

6. **Fine-tune the Workflow (if needed)**
   - Switch to the "DSL Editor" tab to make detailed adjustments.
   - Edit the YAML code directly if you need to make specific changes.
   - Click "Compile DSL" to update the visual representation.

7. **Save Your Workflow**
   - Click "Save Workflow" to save your workflow.
   - Your workflow will be saved in draft status.

### Activating and Managing Workflows

1. **Activate a Workflow**
   - From the workflow list, find your workflow and click "Edit".
   - Change the status from "draft" to "active".
   - Click "Save" to activate the workflow.

2. **Test a Workflow**
   - From the workflow detail page, click "Simulate".
   - Enter test data for the trigger event.
   - Click "Run Simulation" to see how the workflow would execute.

3. **View Workflow Runs**
   - From the workflow detail page, click "Runs".
   - You'll see a list of all executions of this workflow.
   - Click on a run to see details of what happened.

4. **Manage Workflow Versions**
   - From the workflow detail page, click "Versions".
   - You'll see all saved versions of the workflow.
   - Click "Rollback" to revert to a previous version.

### Example Workflows

Here are some example workflows you can create:

#### Basic Workflows

1. **Lead Nurturing**
   ```
   When a lead visits our pricing page 3 times in a week, add them to the 'High Intent' tag, send the 'Pricing Follow-up' email template, and create a task for the sales rep to call them within 24 hours.
   ```

2. **Deal Progression**
   ```
   When a deal stays in the 'Proposal' stage for more than 7 days with no activity, send a reminder email to the assigned sales rep and create a task for their manager to review the deal.
   ```

3. **Customer Onboarding**
   ```
   When a deal is marked as 'Closed Won', create an onboarding task for the customer success team, send the welcome email to the customer, and schedule a kickoff call for the next business day.
   ```

#### Advanced Workflows Using New Features

4. **Scheduled Territory Review**
   ```
   Every Monday at 9 AM, analyze territory performance for the past week, generate an AI summary of key metrics, and send it to the sales manager with recommendations for territory adjustments.
   ```

5. **Anniversary Follow-up**
   ```
   On the anniversary of a customer's sign-up date, check their product usage, and if they're a power user, send them a personalized video message thanking them for their business and offering an exclusive upgrade.
   ```

6. **Website Behavior Targeting**
   ```
   When a lead visits the pricing page, then the features page, then returns to the pricing page within 24 hours, enrich their contact record with additional company data, score them as high-intent, and create a personalized proposal document.
   ```

7. **LinkedIn Integration**
   ```
   When a prospect accepts a LinkedIn connection request, wait 1 business day, then generate a personalized AI message based on their profile, and post it as a LinkedIn message.
   ```

8. **Pipeline Velocity Alert**
   ```
   When the velocity of deals moving through the pipeline drops by more than 20% compared to the previous month, analyze the bottleneck stages, create a forecast update, and schedule a pipeline review meeting with the sales team.
   ```

## Predictive Insights Hub

### Generating Insights

1. **Access the Predictive Insights Hub**
   - Click on "Predictive Insights" in the sidebar navigation.
   - You'll see the Insights interface with three tabs: Ask a Question, Insight, and History.

2. **Ask a Business Question**
   - In the "Ask a Question" tab, enter your business question.
   - Be specific and provide context in your question.

   **Example Questions:**
   - "Why is our Q2 pipeline soft compared to last quarter?"
   - "Which deals are most likely to close this month?"
   - "What's the trend in our average deal size over the past 6 months?"
   - "Which sales reps have the highest conversion rate?"

3. **Generate the Insight**
   - Click "Generate Insight" to analyze your data.
   - The system will process your question and generate an insight.

4. **Review the Insight**
   - Switch to the "Insight" tab to see the generated insight.
   - The insight includes:
     - A chart visualizing the relevant data
     - A narrative explaining the insight
     - "Why It Matters" bullet points
     - Recommended actions

5. **Use the Recommendations**
   - Review the recommended actions.
   - Click "Create Workflow" to automatically create a workflow based on a recommendation.
   - Click "Take Action" to follow a direct link to take the recommended action.

6. **Provide Feedback**
   - At the bottom of the insight, click "Yes, it was helpful" or "No, it wasn't helpful".
   - Your feedback helps improve future insights.

### Managing Insights

1. **View Insight History**
   - Switch to the "History" tab to see all previously generated insights.
   - Click on an insight to view it again.

2. **Refresh an Insight**
   - While viewing an insight, click "Refresh Insight" to regenerate it with the latest data.

3. **Share an Insight**
   - While viewing an insight, click "Share" to copy a link to the insight.
   - Share the link with team members to collaborate on the insight.

### Example Insights

Here are some example insights you can generate:

#### Basic Insights

1. **Pipeline Analysis**
   ```
   Why is our Q2 pipeline soft compared to last quarter?
   ```

2. **Deal Forecasting**
   ```
   Which deals are most likely to close this month?
   ```

3. **Performance Trends**
   ```
   How has our sales team's performance changed over the past year?
   ```

#### Advanced Insights Using New Features

4. **Pipeline Waterfall Analysis**
   ```
   Show me a waterfall analysis of how our pipeline has changed from the start of Q2 to now, breaking down additions, losses, and value changes.
   ```

5. **Cohort Analysis**
   ```
   Compare the performance of customers acquired in Q1 vs Q2 over their first 90 days, focusing on expansion revenue and product adoption.
   ```

6. **Win/Loss Pattern Detection**
   ```
   What patterns distinguish our won deals from lost deals in the technology sector over the past 6 months?
   ```

7. **Predictive Forecast**
   ```
   Based on current pipeline and historical conversion rates, what is our forecasted revenue for Q3 with confidence intervals?
   ```

8. **Cross-Module Integration**
   ```
   Analyze our lost deals from last quarter and recommend objection handling strategies we should prepare for similar deals this quarter.
   ```

## Best Practices

### For Workflows

1. **Start Simple**
   - Begin with straightforward workflows before creating complex ones.
   - Test each workflow thoroughly before activating it.

2. **Be Specific in Descriptions**
   - Clearly specify triggers, conditions, and actions.
   - Include timing information when relevant.

3. **Use Versioning**
   - Make incremental changes and save versions.
   - This allows you to roll back if needed.

### For Insights

1. **Ask Specific Questions**
   - The more specific your question, the more useful the insight.
   - Include time periods and specific metrics in your question.

2. **Provide Context**
   - Mention relevant background information in your question.
   - This helps the AI understand what you're looking for.

3. **Iterate on Questions**
   - If an insight doesn't answer your question fully, try rephrasing.
   - Ask follow-up questions to dig deeper.

## Troubleshooting

### Workflow Issues

- **Natural Language Not Parsing Correctly**
  - Try being more explicit in your description.
  - Break down complex workflows into simpler steps.

- **Workflow Not Triggering**
  - Check that the trigger conditions are correctly specified.
  - Verify that the workflow is active.

### Insight Issues

- **No Data Available**
  - Ensure your CRM has sufficient data to answer the question.
  - Try asking about a different time period or metric.

- **Unclear Insight**
  - Try rephrasing your question to be more specific.
  - Ask a simpler question first, then follow up with more complex ones.
