# Data Access Layer (DAL) Pattern

## Overview

The Data Access Layer (DAL) pattern is an architectural pattern that separates the business logic from the data access logic. It provides a consistent API for data access across the application, making it easier to maintain, test, and extend the codebase.

## Key Components

### Repository Interface

The repository interface defines the standard operations to be performed on a model. It provides a consistent API for data access across the application.

```typescript
interface IRepository<T> {
  findById(id: string, tenantId?: string): Promise<T | null>;
  findAll(filter?: Record<string, any>, tenantId?: string, options?: QueryOptions): Promise<T[]>;
  count(filter?: Record<string, any>, tenantId?: string): Promise<number>;
  create(data: Partial<T>): Promise<T>;
  update(id: string, data: Partial<T>, tenantId?: string): Promise<T | null>;
  delete(id: string, tenantId?: string): Promise<boolean>;
  findOne(filter: Record<string, any>, tenantId?: string): Promise<T | null>;
}
```

### Base Repository Implementation

The base repository implementation provides a common implementation for the repository interface. It handles the conversion between entities and documents, and provides a consistent error handling mechanism.

```typescript
abstract class BaseMongoRepository<T, D extends mongoose.Document> implements IRepository<T> {
  protected model: mongoose.Model<D>;
  protected entityName: string;

  constructor(model: mongoose.Model<D>, entityName: string) {
    this.model = model;
    this.entityName = entityName;
  }

  protected abstract toEntity(doc: D | null): T | null;
  protected abstract toDocument(entity: Partial<T>): Partial<D>;

  // Implementation of IRepository methods
}
```

### Entity-Specific Repository

Entity-specific repositories extend the base repository implementation and provide entity-specific operations. They implement the `toEntity` and `toDocument` methods to convert between entities and documents.

```typescript
class ObjectionRepository extends BaseMongoRepository<ObjectionEntity, ObjectionDocument> {
  constructor() {
    super(Objection, 'Objection');
  }

  protected toEntity(doc: ObjectionDocument | null): ObjectionEntity | null {
    // Convert document to entity
  }

  protected toDocument(entity: Partial<ObjectionEntity>): Partial<ObjectionDocument> {
    // Convert entity to document
  }

  // Entity-specific operations
}
```

## Benefits

### Improved Type Safety

- Strong type checking for MongoDB documents
- Proper type inference for MongoDB operations
- Better IDE autocompletion and error checking

### Better Code Organization

- Clear separation of concerns
- Consistent API for data access
- Easier to maintain and extend

### Improved Testability

- Easy to mock repositories for testing
- Clear boundaries for unit tests
- Consistent error handling

### Multi-Tenant Support

- Built-in support for multi-tenancy
- Consistent tenant isolation
- Secure data access

## Usage Examples

### Service Implementation

#### Basic Service Pattern

```typescript
class ObjectionHandlerService {
  async getObjectionById(id: string): Promise<ObjectionEntity | null> {
    try {
      return await objectionRepository.findById(id);
    } catch (error) {
      logger.error(`Error getting objection with ID ${id}:`, error);
      throw error;
    }
  }

  async createObjection(data: ObjectionData, userId: string): Promise<ObjectionEntity> {
    try {
      return await objectionRepository.create({
        ...data,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      logger.error('Error creating objection:', error);
      throw error;
    }
  }
}
```

#### Service with Multi-Tenant Support

```typescript
class ContactService {
  async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
    try {
      return await contactRepository.findById(id, tenantId);
    } catch (error) {
      logger.error(`Error getting contact with ID ${id}:`, error);
      throw error;
    }
  }

  async createContact(data: ContactCreateData, tenantId: string): Promise<ContactEntity> {
    try {
      return await contactRepository.create({
        ...data,
        status: data.status || 'new',
        tags: data.tags || [],
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      logger.error('Error creating contact:', error);
      throw error;
    }
  }
}
```

#### Service with Cross-Entity Validation

```typescript
class ContactService {
  async updateContact(
    id: string,
    data: ContactUpdateData,
    tenantId: string
  ): Promise<ContactEntity | null> {
    try {
      // Validate company ID if provided
      if (data.companyId) {
        const company = await companyRepository.findById(data.companyId, tenantId);
        if (!company) {
          throw new DocumentNotFoundError('Company', data.companyId);
        }
      }

      // Update the contact
      return await contactRepository.update(id, {
        ...data,
        updatedAt: new Date()
      }, tenantId);
    } catch (error) {
      logger.error(`Error updating contact with ID ${id}:`, error);
      throw error;
    }
  }
}
```

### Controller Implementation

#### Basic Controller Pattern

```typescript
router.get('/objections/:id', async (req, res, next) => {
  try {
    const objection = await objectionHandlerService.getObjectionById(req.params.id);

    if (!objection) {
      return res.status(404).json({ message: 'Objection not found' });
    }

    return res.json(objection);
  } catch (error) {
    next(error);
  }
});
```

#### Controller with Multi-Tenant Support

```typescript
router.get('/contacts/:id', async (req, res, next) => {
  try {
    const tenantId = req.user.tenantId;
    const contact = await contactService.getContactById(req.params.id, tenantId);

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    return res.json(contact);
  } catch (error) {
    next(error);
  }
});
```

#### Controller with Error Handling

```typescript
router.post('/contacts', async (req, res, next) => {
  try {
    const tenantId = req.user.tenantId;
    const contact = await contactService.createContact(req.body, tenantId);

    return res.status(201).json(contact);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    if (error instanceof DuplicateKeyError) {
      return res.status(409).json({
        message: `A contact with ${error.key} "${error.value}" already exists`
      });
    }

    next(error);
  }
});
```

## Best Practices

### 1. Use Entity Interfaces

Define clear entity interfaces that represent the business objects. This provides a consistent API for working with entities across the application.

```typescript
export interface ContactEntity {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  // Other properties...
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### 2. Implement Type Guards

Create type guards to check if an object is of a specific entity type. This helps with type narrowing and provides better type safety.

```typescript
export function isContactEntity(obj: any): obj is ContactEntity {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.firstName === 'string' &&
    typeof obj.lastName === 'string' &&
    typeof obj.email === 'string';
}
```

### 3. Handle ObjectId Conversion

Use utility functions to convert between string IDs and MongoDB ObjectIds. This ensures consistent handling of IDs across the application.

```typescript
export function toObjectId(id: string | mongoose.Types.ObjectId): mongoose.Types.ObjectId {
  if (typeof id === 'string') {
    return new mongoose.Types.ObjectId(id);
  }
  return id;
}
```

### 4. Implement Proper Error Handling

Add comprehensive error handling in repositories and services. Use custom error classes to provide more context for errors.

```typescript
try {
  // Perform operation
} catch (error) {
  logger.error(`Error performing operation:`, error);
  handleMongoDBError(error, 'EntityName');
}
```

### 5. Add Tenant Isolation

Ensure proper tenant isolation in all data access operations. This is critical for multi-tenant applications.

```typescript
async findAll(filter: Record<string, any> = {}, tenantId?: string): Promise<T[]> {
  const query = this.model.find(this.addTenantFilter(filter, tenantId));
  // ...
}

protected addTenantFilter(filter: Record<string, any>, tenantId?: string): Record<string, any> {
  if (!tenantId) return filter;
  return { ...filter, tenantId };
}
```

### 6. Write Unit Tests

Create comprehensive unit tests for repositories and services. Use mocks to isolate the unit being tested.

```typescript
describe('ContactService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a contact', async () => {
    const mockContact = { id: '1', firstName: 'John', lastName: 'Doe' };
    (contactRepository.create as jest.Mock).mockResolvedValue(mockContact);

    const result = await service.createContact(data, tenantId);

    expect(contactRepository.create).toHaveBeenCalledWith({
      ...data,
      tenantId,
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date)
    });
    expect(result).toEqual(mockContact);
  });
});
```

### 7. Document the API

Add clear documentation for repository and service methods. This helps other developers understand how to use the API.

```typescript
/**
 * Find contacts by company ID
 * @param companyId Company ID
 * @param tenantId Tenant ID
 * @returns Array of contacts
 */
async findByCompanyId(companyId: string, tenantId: string): Promise<ContactEntity[]> {
  try {
    return this.findAll({ companyId: toObjectId(companyId) }, tenantId);
  } catch (error) {
    logger.error(`Error finding contacts by company ID ${companyId}:`, error);
    throw error;
  }
}
```

### 8. Use Repository Patterns Consistently

Apply the repository pattern consistently across the application. This ensures a consistent API for data access.

```typescript
// In a service
async getContactById(id: string, tenantId: string): Promise<ContactEntity | null> {
  return await contactRepository.findById(id, tenantId);
}

// In another service
async getCompanyById(id: string, tenantId: string): Promise<CompanyEntity | null> {
  return await companyRepository.findById(id, tenantId);
}
```

### 9. Implement Pagination

Implement pagination for methods that return large result sets. This improves performance and reduces memory usage.

```typescript
async findAll(
  filter: Record<string, any> = {},
  tenantId?: string,
  options: {
    skip?: number;
    limit?: number;
    sort?: Record<string, 1 | -1>;
  } = {}
): Promise<T[]> {
  const query = this.model.find(this.addTenantFilter(filter, tenantId));

  if (options.skip !== undefined) {
    query.skip(options.skip);
  }

  if (options.limit !== undefined) {
    query.limit(options.limit);
  }

  // ...
}
```

### 10. Use Transactions for Multi-Document Operations

Use transactions for operations that modify multiple documents. This ensures data consistency.

```typescript
async transferContact(contactId: string, fromCompanyId: string, toCompanyId: string, tenantId: string): Promise<ContactEntity | null> {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Update contact
    const contact = await contactRepository.update(contactId, { companyId: toCompanyId }, tenantId);

    // Update company statistics
    await companyRepository.update(fromCompanyId, { $inc: { contactCount: -1 } }, tenantId);
    await companyRepository.update(toCompanyId, { $inc: { contactCount: 1 } }, tenantId);

    await session.commitTransaction();
    return contact;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
}
```

## Implementation Steps

1. Define entity interfaces
2. Create custom document types
3. Implement the repository interface
4. Create the base repository implementation
5. Implement entity-specific repositories
6. Update services to use repositories
7. Write unit tests
8. Update documentation

## Conclusion

The Data Access Layer pattern provides a clean and consistent way to access data in the application. It improves type safety, code organization, testability, and maintainability. By following this pattern, we can create a more robust and scalable application.
