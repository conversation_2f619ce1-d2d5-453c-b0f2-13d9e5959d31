# Win/Loss Analyzer and Follow-up Coach Technical Documentation

This document provides technical details about the implementation of the Win/Loss Analyzer and Follow-up Coach features in Aizako CRM.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Win/Loss Analyzer Implementation](#winloss-implementation)
   - [Data Models](#winloss-models)
   - [Service Layer](#winloss-service)
   - [API Routes](#winloss-routes)
   - [Client-side Implementation](#winloss-client)
   - [AI Integration](#winloss-ai)
3. [Follow-up Coach Implementation](#followup-implementation)
   - [Data Models](#followup-models)
   - [Service Layer](#followup-service)
   - [API Routes](#followup-routes)
   - [Client-side Implementation](#followup-client)
   - [AI Integration](#followup-ai)
4. [Subscription Integration](#subscription-integration)
5. [Testing](#testing)
6. [Future Enhancements](#future-enhancements)

---

## Architecture Overview <a name="architecture-overview"></a>

Both the Win/Loss Analyzer and Follow-up Coach features follow the standard Aizako CRM architecture:

- **MongoDB Models**: Define the data structure
- **Service Layer**: Implement business logic
- **API Routes**: Expose functionality via RESTful endpoints
- **Client-side API**: Interact with the backend
- **React Components**: Provide the user interface
- **AI Integration**: Leverage AI capabilities for intelligent features

The features integrate with the AI service through two mechanisms:
1. Direct API calls to the AI service for primary functionality
2. Fallback to Voyage AI when the AI service is unavailable

---

## Win/Loss Analyzer Implementation <a name="winloss-implementation"></a>

### Data Models <a name="winloss-models"></a>

The Win/Loss Analyzer uses two main data models:

1. **WinLossAnalysis**: Stores individual analyses of opportunities
   - Key fields: opportunity reference, outcome, key factors, recommendations
   - Located in `server/models/mongoose/win-loss-model.ts`

2. **WinLossFactor**: Tracks recurring factors across analyses
   - Key fields: name, category, impact, frequency, correlation
   - Used for statistical analysis and pattern recognition
   - Located in `server/models/mongoose/win-loss-model.ts`

### Service Layer <a name="winloss-service"></a>

The service layer is implemented in `server/services/win-loss-analyzer-service.ts` and provides:

- CRUD operations for analyses
- AI-powered analysis generation
- Statistical calculations
- Factor tracking and correlation analysis

Key methods:
- `getAnalyses()`: Retrieve analyses with filtering options
- `generateAnalysis()`: Create an AI-powered analysis for an opportunity
- `getStatistics()`: Calculate win rates and other metrics
- `updateFactorStatistics()`: Track and update factor correlations

### API Routes <a name="winloss-routes"></a>

API routes are defined in `server/routes/win-loss-analyzer-routes.ts` and registered in `mongo-routes.ts`.

Endpoints:
- `GET /api/win-loss/analyses`: List analyses with filtering
- `GET /api/win-loss/analyses/:id`: Get a specific analysis
- `POST /api/win-loss/analyses`: Create a manual analysis
- `PUT /api/win-loss/analyses/:id`: Update an analysis
- `DELETE /api/win-loss/analyses/:id`: Delete an analysis
- `POST /api/win-loss/analyses/generate`: Generate an AI analysis
- `GET /api/win-loss/factors`: Get win/loss factors
- `GET /api/win-loss/statistics`: Get statistical data

All routes are protected by authentication and feature entitlement middleware.

### Client-side Implementation <a name="winloss-client"></a>

The client-side implementation consists of:

1. **API Functions** (`client/src/api/win-loss-analyzer-api.ts`):
   - Functions to call the backend API endpoints
   - Uses the common `apiRequest` utility

2. **UI Components**:
   - `WinLossAnalyzerButton.tsx`: Entry point component
   - `WinLossAnalyzerDialog.tsx`: Main container with tabs
   - `WinLossAnalysisView.tsx`: List of analyses
   - `WinLossAnalysisDetail.tsx`: Detailed view of an analysis
   - `WinLossStatistics.tsx`: Statistical visualizations

3. **Integration Points**:
   - Added to OpportunityDetail page
   - Added to Dashboard page

### AI Integration <a name="winloss-ai"></a>

The Win/Loss Analyzer integrates with AI through:

1. **Primary AI Service**:
   - Uses the CrewAI task API at `${AI_SERVICE_URL}/crew`
   - Sends opportunity, contact, company, and activity data
   - Receives structured analysis with factors and recommendations

2. **Fallback to Voyage AI**:
   - Uses Voyage AI's chat completions API when the primary service is unavailable
   - Implements the same prompt structure with system and user messages
   - Specifies JSON response format for consistent parsing

---

## Follow-up Coach Implementation <a name="followup-implementation"></a>

### Data Models <a name="followup-models"></a>

The Follow-up Coach uses two main data models:

1. **FollowUp**: Stores individual follow-up tasks
   - Key fields: title, scheduled date, status, type, content
   - References to opportunity, contact, company
   - Located in `server/models/mongoose/follow-up-model.ts`

2. **FollowUpTemplate**: Stores reusable templates
   - Key fields: name, content, variables, category
   - Located in `server/models/mongoose/follow-up-model.ts`

### Service Layer <a name="followup-service"></a>

The service layer is implemented in `server/services/follow-up-coach-service.ts` and provides:

- CRUD operations for follow-ups
- AI-powered follow-up recommendations
- Template management

Key methods:
- `getFollowUps()`: Retrieve follow-ups with filtering options
- `createFollowUp()`: Create a new follow-up
- `generateFollowUpRecommendations()`: Generate AI-powered recommendations
- `generateFollowUpsWithAI()`: Internal method for AI integration

### API Routes <a name="followup-routes"></a>

API routes are defined in `server/routes/follow-up-coach-routes.ts` and registered in `mongo-routes.ts`.

Endpoints:
- `GET /api/follow-ups`: List follow-ups with filtering
- `GET /api/follow-ups/:id`: Get a specific follow-up
- `POST /api/follow-ups`: Create a follow-up
- `PUT /api/follow-ups/:id`: Update a follow-up
- `DELETE /api/follow-ups/:id`: Delete a follow-up
- `POST /api/follow-ups/generate`: Generate follow-up recommendations

All routes are protected by authentication and feature entitlement middleware.

### Client-side Implementation <a name="followup-client"></a>

The client-side implementation consists of:

1. **API Functions** (`client/src/api/follow-up-coach-api.ts`):
   - Functions to call the backend API endpoints
   - Uses the common `apiRequest` utility

2. **UI Components**:
   - `FollowUpCoachButton.tsx`: Entry point component
   - `FollowUpCoachDialog.tsx`: Main container with tabs and follow-up management

3. **Integration Points**:
   - Added to OpportunityDetail page
   - Added to ContactDetail page

### AI Integration <a name="followup-ai"></a>

The Follow-up Coach integrates with AI through:

1. **Primary AI Service**:
   - Uses the CrewAI task API at `${AI_SERVICE_URL}/crew`
   - Sends contact, company, opportunity, and activity data
   - Receives structured follow-up recommendations

2. **Fallback to Voyage AI**:
   - Uses Voyage AI's chat completions API when the primary service is unavailable
   - Implements the same prompt structure with system and user messages
   - Specifies JSON response format for consistent parsing

---

## Subscription Integration <a name="subscription-integration"></a>

Both features are integrated with the subscription system:

1. **Feature Entitlements**:
   - `ai.win-loss-analyzer`: Controls access to Win/Loss Analyzer
   - `ai.follow-up-coach`: Controls access to Follow-up Coach

2. **Middleware**:
   - All API routes use `checkFeatureEntitlement` middleware
   - Example: `checkFeatureEntitlement('ai.win-loss-analyzer')`

3. **Subscription Plans**:
   - Both features are available on Professional and Enterprise plans
   - Not available on Basic and Free plans

---

## Testing <a name="testing"></a>

To test these features:

1. **Unit Tests**:
   - Test service methods with mocked dependencies
   - Test API routes with mocked services
   - Test UI components with mocked API calls

2. **Integration Tests**:
   - Test the full flow from UI to database and back
   - Test AI integration with both primary and fallback services

3. **Manual Testing**:
   - Create test opportunities in different stages
   - Generate analyses and follow-ups
   - Verify AI-generated content quality

---

## Future Enhancements <a name="future-enhancements"></a>

Planned enhancements for these features:

### Win/Loss Analyzer

1. **Enhanced Visualizations**:
   - Trend analysis over time
   - Factor correlation matrix
   - Win rate by industry/company size

2. **Comparative Analysis**:
   - Compare factors across different segments
   - Benchmark against industry standards

3. **Predictive Win Probability**:
   - Use historical data to predict win probability for open opportunities

### Follow-up Coach

1. **Email Integration**:
   - Send follow-up emails directly from the interface
   - Track email opens and responses

2. **Calendar Integration**:
   - Schedule follow-up meetings with calendar integration
   - Automatic reminders for upcoming follow-ups

3. **Smart Scheduling**:
   - AI-powered optimal timing recommendations
   - Workload balancing across days
