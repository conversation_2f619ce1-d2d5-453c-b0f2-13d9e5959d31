# Proposal Generator Technical Implementation

## Overview

This document provides technical details about the implementation of the Proposal Generator feature in Aizako CRM, including the document generation, email integration, public sharing, and analytics components.

## Table of Contents

1. [Architecture](#architecture)
2. [Data Models](#data-models)
3. [Document Generation](#document-generation)
4. [Email Integration](#email-integration)
5. [Public Sharing](#public-sharing)
6. [Analytics Implementation](#analytics-implementation)
7. [Security Considerations](#security-considerations)
8. [Performance Optimizations](#performance-optimizations)
9. [Integration Points](#integration-points)

## Architecture <a name="architecture"></a>

The Proposal Generator feature follows a layered architecture:

1. **Presentation Layer**: React components for UI
2. **API Layer**: Express routes for handling requests
3. **Service Layer**: Business logic for proposal generation and management
4. **Data Layer**: MongoDB models for data storage
5. **Integration Layer**: External services for AI and email

### Key Components

- **ProposalGeneratorService**: Core service for proposal management
- **EmailService**: Handles email configuration and sending
- **ProposalAnalyticsService**: Tracks and analyzes proposal interactions
- **Document Generation**: Converts proposals to various formats
- **Public Sharing**: Secure public access to shared proposals

## Data Models <a name="data-models"></a>

### Proposal Model

```typescript
export interface IProposal extends Document {
  name: string;
  description?: string;
  content?: string;
  sections: Array<{
    name: string;
    type: string;
    content: string;
  }>;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected';
  value: number;
  currency: string;
  validUntil?: Date;
  opportunityId?: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  companyId?: mongoose.Types.ObjectId;
  documentId?: mongoose.Types.ObjectId;
  documentUrl?: string;
  format?: string;
  availableFormats?: string[];
  tags?: string[];
  notes?: string;
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  viewedAt?: Date;
  acceptedAt?: Date;
  rejectedAt?: Date;
  customFields?: Record<string, any>;
}
```

### Email Configuration Model

```typescript
export interface IEmailConfig extends Document {
  tenantId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses' | 'gmail' | 'outlook' | 'other';
  isEnabled: boolean;
  fromName: string;
  fromEmail: string;
  replyToEmail?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
  apiKey?: string;
  apiSecret?: string;
  region?: string;
  templates: {
    proposalShare?: {
      subject: string;
      body: string;
    };
    proposalAccepted?: {
      subject: string;
      body: string;
    };
    proposalRejected?: {
      subject: string;
      body: string;
    };
  };
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

### Proposal Analytics Model

```typescript
export interface IProposalAnalytics extends Document {
  proposalId: mongoose.Types.ObjectId;
  views: Array<{
    timestamp: Date;
    ip?: string;
    userAgent?: string;
    referrer?: string;
    viewDuration?: number;
    isUnique: boolean;
    shareToken?: string;
  }>;
  downloads: Array<{
    timestamp: Date;
    format: string;
    ip?: string;
    userAgent?: string;
    isUnique: boolean;
    shareToken?: string;
  }>;
  shares: Array<{
    timestamp: Date;
    method: 'link' | 'email' | 'social';
    platform?: string;
    recipientEmail?: string;
    shareToken: string;
    expiresAt: Date;
    sharedBy: mongoose.Types.ObjectId;
  }>;
  interactions: Array<{
    timestamp: Date;
    type: string;
    data: Record<string, any>;
    ip?: string;
    userAgent?: string;
    shareToken?: string;
  }>;
  totalViews: number;
  uniqueViews: number;
  totalDownloads: number;
  totalShares: number;
  averageViewDuration: number;
  lastViewedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

## Document Generation <a name="document-generation"></a>

The Proposal Generator supports multiple document formats:

### PDF Generation

PDF generation uses the HTML content and converts it to PDF:

```typescript
private async generatePdfDocument(
  proposal: IProposal,
  htmlContent: string,
  userId: string
): Promise<{
  documentUrl: string;
  format: string;
  isDownloadable: boolean;
}> {
  // Convert HTML to PDF using a library like puppeteer
  // Save the PDF to the document storage
  // Update the proposal with the document information
}
```

### DOCX Generation

DOCX generation uses the html-docx-js library:

```typescript
private async generateDocxDocument(
  proposal: IProposal,
  htmlContent: string,
  userId: string
): Promise<{
  documentUrl: string;
  format: string;
  isDownloadable: boolean;
}> {
  // Use html-docx-js to convert HTML to DOCX
  const htmlDocx = require('html-docx-js');
  const enhancedHtml = this.enhanceHtmlForDocx(htmlContent, proposal);
  const docxBuffer = htmlDocx.asBlob(enhancedHtml);
  
  // Save the DOCX to the document storage
  // Update the proposal with the document information
}
```

### Markdown Generation

Markdown generation uses the turndown library:

```typescript
private async generateMarkdownDocument(
  proposal: IProposal,
  htmlContent: string,
  userId: string
): Promise<{
  documentUrl: string;
  format: string;
  isDownloadable: boolean;
}> {
  // Use turndown to convert HTML to Markdown
  const TurndownService = require('turndown');
  const turndownService = new TurndownService({
    headingStyle: 'atx',
    codeBlockStyle: 'fenced',
    emDelimiter: '*'
  });
  
  // Add custom rules for better conversion
  // Convert HTML to Markdown
  // Save the Markdown to the document storage
  // Update the proposal with the document information
}
```

### Claude-Enhanced HTML

The Claude-Enhanced HTML format uses AI to create a visually appealing design:

```typescript
private async generateBeautifulHtmlWithClaude(
  proposal: IProposal,
  contact: IContact | null,
  company: ICompany | null,
  opportunity: IOpportunity | null
): Promise<string> {
  // Prepare data for Claude
  // Create prompt for Claude with design requirements
  // Try to use the AI service first
  // Fall back to Voyage AI if needed
  // Return the generated HTML
}
```

## Email Integration <a name="email-integration"></a>

### Email Service

The EmailService class handles email configuration and sending:

```typescript
export class EmailService {
  // Get email configuration for a user
  async getEmailConfig(userId: string): Promise<IEmailConfig | null> {
    // Find the user's email configuration
  }

  // Create or update email configuration
  async saveEmailConfig(userId: string, config: Partial<IEmailConfig>): Promise<IEmailConfig | null> {
    // Save the email configuration
  }

  // Send an email using the user's configuration
  async sendEmail(userId: string, options: EmailOptions): Promise<boolean> {
    // Get email configuration
    // Create transporter based on provider
    // Send email
  }

  // Send an email using a template
  async sendTemplateEmail(
    userId: string, 
    templateKey: 'proposalShare' | 'proposalAccepted' | 'proposalRejected',
    to: string | string[],
    data: TemplateData,
    attachments?: EmailOptions['attachments']
  ): Promise<boolean> {
    // Get email configuration
    // Get template
    // Replace template variables
    // Send email
  }
}
```

### Email Configuration Routes

```typescript
// Get email configuration
app.get('/api/email-config', authenticateUser, async (req, res) => {
  // Get the user's email configuration
});

// Save email configuration
app.post('/api/email-config', authenticateUser, checkFeatureEntitlement('email.configuration'), async (req, res) => {
  // Save the user's email configuration
});

// Test email configuration
app.post('/api/email-config/test', authenticateUser, checkFeatureEntitlement('email.configuration'), async (req, res) => {
  // Send a test email
});
```

## Public Sharing <a name="public-sharing"></a>

### Shareable Link Generation

```typescript
async createShareableLink(
  id: string,
  userId: string,
  options: {
    expirationDays: number;
    format: 'html' | 'pdf' | 'docx' | 'markdown' | 'claude-html';
  }
): Promise<{
  shareableUrl: string;
  expiresAt: Date;
  format: string;
} | null> {
  // Get the proposal
  // Generate a unique token
  // Calculate expiration date
  // Generate the document in the requested format if not already available
  // Create a shareable link record
  // Store the shareable link in the proposal
  // Track the share in analytics
  // Generate the shareable URL
}
```

### Public Routes

```typescript
// Get public proposal by token
app.get('/api/public/proposals/:token', async (req, res) => {
  // Find the proposal with the given token
  // Check if the link has expired
  // Track the view
  // Return the proposal data
});

// Get public proposal document content
app.get('/api/public/proposals/:token/content', async (req, res) => {
  // Find the proposal with the given token
  // Check if the link has expired
  // Track the view
  // Get the file content
  // Set the appropriate content type
  // Return the file content
});

// Track proposal interaction
app.post('/api/public/proposals/:token/track', async (req, res) => {
  // Find the proposal with the given token
  // Track the interaction based on type
});
```

## Analytics Implementation <a name="analytics-implementation"></a>

### Analytics Service

```typescript
export class ProposalAnalyticsService {
  // Get or create analytics for a proposal
  async getOrCreateAnalytics(proposalId: string): Promise<IProposalAnalytics> {
    // Check if analytics already exist
    // Create new analytics if needed
  }

  // Track a proposal view
  async trackView(
    proposalId: string,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    // Get client info
    // Check if this is a unique view
    // Add view
    // Update counters
    // Update proposal status if needed
  }

  // Track a proposal download
  async trackDownload(
    proposalId: string,
    format: string,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    // Get client info
    // Check if this is a unique download
    // Add download
    // Update counter
  }

  // Track a proposal share
  async trackShare(
    proposalId: string,
    userId: string,
    method: 'link' | 'email' | 'social',
    shareToken: string,
    expiresAt: Date,
    platform?: string,
    recipientEmail?: string
  ): Promise<void> {
    // Add share
    // Update counter
  }

  // Track a custom interaction
  async trackInteraction(
    proposalId: string,
    type: string,
    data: Record<string, any>,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    // Get client info
    // Add interaction
  }

  // Update view duration
  async updateViewDuration(
    proposalId: string,
    duration: number,
    req: Request,
    shareToken?: string
  ): Promise<void> {
    // Find the most recent view from this client
    // Update view duration
    // Recalculate average view duration
  }
}
```

### Analytics Routes

```typescript
// Get analytics for a proposal
app.get('/api/proposals/:id/analytics', authenticateUser, checkFeatureEntitlement('analytics.proposals'), async (req, res) => {
  // Get the analytics for the proposal
});

// Get aggregated analytics for all proposals
app.get('/api/proposals/analytics/summary', authenticateUser, checkFeatureEntitlement('analytics.proposals'), async (req, res) => {
  // Get all proposals for the user
  // Get analytics for each proposal
  // Calculate summary statistics
  // Prepare proposal summary data
});
```

## Security Considerations <a name="security-considerations"></a>

### Authentication and Authorization

- All API routes (except public routes) require authentication
- Feature entitlement checks for premium features
- Proposal ownership validation before access

### Secure Sharing

- Token-based authentication for shared proposals
- Expiration dates for shared links
- IP and user agent tracking for security monitoring

### Email Security

- Password encryption in the database
- Support for secure SMTP connections (TLS/SSL)
- API key protection for third-party email services

### Data Protection

- Input validation for all API endpoints
- XSS protection in HTML content
- CSRF protection for API requests

## Performance Optimizations <a name="performance-optimizations"></a>

### Document Generation

- Caching of generated documents
- Asynchronous document generation
- Format-specific optimizations

### Analytics

- Batch updates for analytics data
- Aggregation pipeline for efficient queries
- Indexing of frequently queried fields

### Email Sending

- Connection pooling for SMTP
- Retry mechanism for failed emails
- Rate limiting to prevent abuse

## Integration Points <a name="integration-points"></a>

### AI Integration

- Primary integration with AI service for Claude-enhanced HTML
- Fallback to Voyage AI for text generation
- Prompt engineering for consistent results

### Email Provider Integration

- SMTP for generic email servers
- Direct API integration for SendGrid, Mailgun, and Amazon SES
- OAuth integration for Gmail and Outlook

### Frontend Integration

- React components for proposal management
- Real-time analytics dashboard
- Responsive design for all device sizes

### MongoDB Integration

- Mongoose models for data storage
- Indexes for performance optimization
- Aggregation pipelines for analytics
