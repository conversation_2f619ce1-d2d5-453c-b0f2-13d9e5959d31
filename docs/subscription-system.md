# Aizako CRM Subscription System

This document provides an overview of the subscription system implemented in Aizako CRM.

## Overview

The subscription system is designed to be flexible, allowing for different subscription plans with varying features and resource limits. The system is built on these key principles:

- **Centralized Definition, Distributed Enforcement**: Plans are defined in one place but enforced by each module
- **Dynamic Configuration**: All subscription parameters can be modified through an admin UI without code changes
- **Real-time Propagation**: Changes to subscription plans are immediately propagated to all modules
- **Feature Flagging**: Individual features can be enabled/disabled per subscription plan
- **Usage Tracking**: Resource usage is tracked against subscription limits

## Core Components

### Data Model

The subscription system uses the following data model:

- **SubscriptionPlan**: Defines a subscription plan with features, limits, and pricing
- **TenantSubscription**: Links a tenant to a subscription plan
- **Feature**: Defines a feature that can be enabled/disabled in subscription plans
- **TenantUsage**: Tracks resource usage for a tenant
- **Tenant**: Represents an organization in the system
- **UserTenant**: Links a user to a tenant with a specific role

### Backend Services

The backend includes the following components:

- **SubscriptionService**: Core service for managing subscriptions, plans, features, and usage
- **Storage**: Persistence layer for subscription data
- **Middleware**: Express middleware for checking feature entitlements and resource limits
- **API Routes**: RESTful API for managing subscriptions

### Frontend Components

The frontend includes the following components:

- **useSubscription Hook**: React hook for accessing subscription data
- **SubscriptionContext**: React context for sharing subscription data across components
- **FeatureGate**: Component for conditionally rendering content based on feature entitlements
- **UsageDisplay**: Component for displaying resource usage
- **PlanSelector**: Component for selecting and changing subscription plans

## Subscription Plans

The system comes with four predefined subscription plans:

1. **Free**: Basic CRM features for individuals
   - 1 user, 50 contacts, 10 companies, 5 opportunities
   - Basic AI assistant

2. **Basic**: Essential CRM features for small teams
   - 5 users, 1,000 contacts, 100 companies, 50 opportunities
   - Basic AI assistant, AI insights, API access

3. **Professional**: Advanced CRM features for growing businesses
   - 20 users, 10,000 contacts, 1,000 companies, 500 opportunities
   - Advanced AI assistant, AI insights, document intelligence, API access

4. **Enterprise**: Custom CRM solution for large organizations
   - 100 users, 100,000 contacts, 10,000 companies, 5,000 opportunities
   - All features included

## Features

The system includes the following features that can be enabled/disabled per subscription plan:

- **core.contacts**: Contact management
- **core.companies**: Company management
- **core.opportunities**: Opportunity management
- **core.activities**: Activity tracking
- **core.relationships**: Relationship mapping
- **ai.assistant.basic**: Basic AI assistant
- **ai.assistant.advanced**: Advanced AI assistant
- **ai.insights**: AI-generated insights
- **ai.document**: Document intelligence
- **api.access**: API access

## Resource Limits

The system tracks and enforces limits on the following resources:

- **users**: Number of users in a tenant
- **contacts**: Number of contacts in the CRM
- **companies**: Number of companies in the CRM
- **opportunities**: Number of opportunities in the CRM
- **storage**: Storage space for documents and attachments (in MB)
- **apiRequests**: Number of API requests per day
- **ai.tokens**: Number of AI tokens used

## Usage

### Checking Feature Entitlements

On the backend, use the `requireFeature` middleware:

```typescript
app.post("/api/ai/chat", 
  authenticateUser, 
  requireFeature("ai.assistant.basic"),
  async (req, res) => {
    // Handler code
  }
);
```

On the frontend, use the `FeatureGate` component:

```tsx
<FeatureGate featureKey="ai.insights">
  <InsightsPanel />
</FeatureGate>
```

Or use the `useSubscription` hook directly:

```tsx
const { hasFeature } = useSubscription();

if (hasFeature("ai.assistant.advanced")) {
  // Render advanced features
}
```

### Checking Resource Limits

On the backend, use the `checkResourceLimit` middleware:

```typescript
app.post("/api/contacts", 
  authenticateUser, 
  checkResourceLimit("contacts"),
  async (req, res) => {
    // Handler code
  }
);
```

On the frontend, use the `useSubscription` hook:

```tsx
const { checkResourceLimit } = useSubscription();

if (checkResourceLimit("storage", fileSize)) {
  // Upload file
}
```

### Recording Usage

On the backend, usage is automatically recorded by the middleware, but you can also record it manually:

```typescript
await subscriptionService.recordUsage(tenantId, "storage", fileSize);
```

## Administration

Subscription plans can be managed through the admin UI at `/settings/subscription`. This interface allows administrators to:

- View and modify subscription plans
- Create new subscription plans
- Assign plans to tenants
- Monitor usage across tenants

## Integration with Other Modules

The subscription system is integrated with the following modules:

- **Core Backend**: Enforces limits on contacts, companies, opportunities, etc.
- **AI Service**: Enforces limits on AI features and token usage
- **Frontend**: Conditionally renders features based on entitlements

## Extending the System

To add a new feature to the subscription system:

1. Register the feature in the database
2. Add the feature to subscription plans
3. Add enforcement in the relevant module
4. Update the UI to conditionally render based on the feature

To add a new resource limit:

1. Add the limit to subscription plans
2. Add enforcement in the relevant module
3. Update the UI to display usage information
