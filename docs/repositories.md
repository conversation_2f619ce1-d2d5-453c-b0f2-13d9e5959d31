# Repository Documentation

## Overview

The repository pattern is a design pattern that abstracts the data access layer from the business logic. It provides a clean separation of concerns and makes the code more maintainable, testable, and scalable.

In the Aizako CRM system, repositories are used to access data from MongoDB. Each repository provides methods for common operations like finding, creating, updating, and deleting entities, as well as specialized methods for specific use cases.

## Base Repository

The base repository interface (`IRepository`) defines the common operations that all repositories should implement. It provides a consistent API for data access across the application.

### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `findById` | Find an entity by ID | `id: K, tenantId: string` | `Promise<T \| null>` |
| `findAll` | Find all entities that match the filter | `tenantId: string, options?: QueryOptions<T>` | `Promise<T[]>` |
| `findAllPaginated` | Find all entities that match the filter with pagination | `tenantId: string, options?: QueryOptions<T>` | `Promise<PaginatedResult<T>>` |
| `create` | Create a new entity | `entity: Partial<T>, tenantId: string` | `Promise<T>` |
| `createMany` | Create multiple entities | `entities: Partial<T>[], tenantId: string` | `Promise<T[]>` |
| `update` | Update an entity | `id: K, entity: Partial<T>, tenantId: string` | `Promise<T \| null>` |
| `updateMany` | Update multiple entities | `filter: TypedFilter<T>, update: Partial<T>, tenantId: string` | `Promise<number>` |
| `delete` | Delete an entity | `id: K, tenantId: string` | `Promise<boolean>` |
| `deleteMany` | Delete multiple entities | `filter: TypedFilter<T>, tenantId: string` | `Promise<number>` |
| `count` | Count entities that match the filter | `tenantId: string, filter?: TypedFilter<T>` | `Promise<number>` |
| `exists` | Check if an entity exists | `id: K, tenantId: string` | `Promise<boolean>` |
| `findOne` | Find one entity that matches the filter | `tenantId: string, filter: TypedFilter<T>` | `Promise<T \| null>` |
| `findOneAndUpdate` | Find and update an entity | `filter: TypedFilter<T>, update: Partial<T>, tenantId: string, options?: { upsert?: boolean; new?: boolean }` | `Promise<T \| null>` |
| `findOneAndDelete` | Find and delete an entity | `filter: TypedFilter<T>, tenantId: string` | `Promise<T \| null>` |

## MongoDB Repository

The MongoDB repository (`MongoDBRepository`) is a concrete implementation of the base repository interface for MongoDB. It uses Mongoose to interact with the database and provides a generic implementation that can be used for any entity.

### Features

- Implements all methods defined in the `IRepository` interface
- Uses data mappers to convert between domain entities and database models
- Handles errors consistently with custom error classes
- Enforces tenant isolation for all operations
- Provides logging for all database operations

## Contact Repository

The contact repository (`ContactRepository`) provides methods for accessing and manipulating contact data.

### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `findByCompanyId` | Find contacts by company ID | `companyId: string, tenantId: string, options?: QueryOptions<IContact>` | `Promise<IContact[]>` |
| `findByEmail` | Find a contact by email | `email: string, tenantId: string` | `Promise<IContact \| null>` |
| `search` | Search contacts by name or email | `query: string, tenantId: string, options?: QueryOptions<IContact>` | `Promise<PaginatedResult<IContact>>` |
| `generatePersona` | Generate a persona for a contact | `contactId: string, tenantId: string` | `Promise<IContact \| null>` |
| `enrich` | Enrich a contact with AI | `contactId: string, tenantId: string` | `Promise<IContact \| null>` |
| `findByTag` | Find contacts by tag | `tag: string, tenantId: string, options?: QueryOptions<IContact>` | `Promise<IContact[]>` |
| `findByStatus` | Find contacts by status | `status: string, tenantId: string, options?: QueryOptions<IContact>` | `Promise<IContact[]>` |
| `findByOwner` | Find contacts by owner | `ownerId: string, tenantId: string, options?: QueryOptions<IContact>` | `Promise<IContact[]>` |
| `findByScoreRange` | Find contacts by score range | `minScore: number, maxScore: number, tenantId: string, options?: QueryOptions<IContact>` | `Promise<IContact[]>` |

## Opportunity Repository

The opportunity repository (`OpportunityRepository`) provides methods for accessing and manipulating opportunity data.

### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `findByContactId` | Find opportunities by contact ID | `contactId: string, tenantId: string, options?: QueryOptions<IOpportunity>` | `Promise<IOpportunity[]>` |
| `findByCompanyId` | Find opportunities by company ID | `companyId: string, tenantId: string, options?: QueryOptions<IOpportunity>` | `Promise<IOpportunity[]>` |
| `getDealBrief` | Get deal brief for an opportunity | `opportunityId: string, tenantId: string` | `Promise<IDealBrief>` |
| `getStageAnalysis` | Get stage analysis for an opportunity | `opportunityId: string, tenantId: string` | `Promise<IStageAnalysis>` |
| `getStageTransitions` | Get stage transitions for an opportunity | `opportunityId: string, tenantId: string` | `Promise<IStageTransition[]>` |
| `findByStage` | Find opportunities by stage | `stage: string, tenantId: string, options?: QueryOptions<IOpportunity>` | `Promise<IOpportunity[]>` |
| `findByOwner` | Find opportunities by owner | `ownerId: string, tenantId: string, options?: QueryOptions<IOpportunity>` | `Promise<IOpportunity[]>` |
| `findByValueRange` | Find opportunities by value range | `minValue: number, maxValue: number, tenantId: string, options?: QueryOptions<IOpportunity>` | `Promise<IOpportunity[]>` |
| `findByExpectedCloseDateRange` | Find opportunities by expected close date range | `startDate: Date, endDate: Date, tenantId: string, options?: QueryOptions<IOpportunity>` | `Promise<IOpportunity[]>` |
| `search` | Search opportunities | `query: string, tenantId: string, options?: QueryOptions<IOpportunity>` | `Promise<PaginatedResult<IOpportunity>>` |

## Company Repository

The company repository (`CompanyRepository`) provides methods for accessing and manipulating company data.

### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `findByDomain` | Find companies by domain | `domain: string, tenantId: string, options?: QueryOptions<ICompany>` | `Promise<ICompany[]>` |
| `findByIndustry` | Find companies by industry | `industry: string, tenantId: string, options?: QueryOptions<ICompany>` | `Promise<ICompany[]>` |
| `findByStatus` | Find companies by status | `status: string, tenantId: string, options?: QueryOptions<ICompany>` | `Promise<ICompany[]>` |
| `findByOwner` | Find companies by owner | `ownerId: string, tenantId: string, options?: QueryOptions<ICompany>` | `Promise<ICompany[]>` |
| `findByTag` | Find companies by tag | `tag: string, tenantId: string, options?: QueryOptions<ICompany>` | `Promise<ICompany[]>` |
| `search` | Search companies | `query: string, tenantId: string, options?: QueryOptions<ICompany>` | `Promise<PaginatedResult<ICompany>>` |
| `enrich` | Enrich a company with AI | `companyId: string, tenantId: string` | `Promise<ICompany \| null>` |

## User Repository

The user repository (`UserRepository`) provides methods for accessing and manipulating user data.

### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `findByEmail` | Find a user by email | `email: string, tenantId: string` | `Promise<IUser \| null>` |
| `findByUsername` | Find a user by username | `username: string, tenantId: string` | `Promise<IUser \| null>` |
| `findByRole` | Find users by role | `role: string, tenantId: string, options?: QueryOptions<IUser>` | `Promise<IUser[]>` |
| `search` | Search users | `query: string, tenantId: string, options?: QueryOptions<IUser>` | `Promise<PaginatedResult<IUser>>` |
| `updatePassword` | Update user password | `userId: string, currentPassword: string, newPassword: string, tenantId: string` | `Promise<IUser \| null>` |
| `updatePreferences` | Update user preferences | `userId: string, preferences: Record<string, any>, tenantId: string` | `Promise<IUser \| null>` |
| `updateIntegrations` | Update user integrations | `userId: string, integrations: Record<string, any>, tenantId: string` | `Promise<IUser \| null>` |
| `updateLastLogin` | Update user last login | `userId: string, tenantId: string` | `Promise<IUser \| null>` |

## Task Repository

The task repository (`TaskRepository`) provides methods for accessing and manipulating task data.

### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `findByAssignee` | Find tasks by assignee | `assigneeId: string, tenantId: string, options?: QueryOptions<ITask>` | `Promise<ITask[]>` |
| `findByStatus` | Find tasks by status | `status: string, tenantId: string, options?: QueryOptions<ITask>` | `Promise<ITask[]>` |
| `findByPriority` | Find tasks by priority | `priority: string, tenantId: string, options?: QueryOptions<ITask>` | `Promise<ITask[]>` |
| `findByDueDateRange` | Find tasks by due date range | `startDate: Date, endDate: Date, tenantId: string, options?: QueryOptions<ITask>` | `Promise<ITask[]>` |
| `findByRelatedEntity` | Find tasks by related entity | `entityType: string, entityId: string, tenantId: string, options?: QueryOptions<ITask>` | `Promise<ITask[]>` |
| `findOverdueTasks` | Find overdue tasks | `tenantId: string, options?: QueryOptions<ITask>` | `Promise<ITask[]>` |
| `completeTask` | Complete a task | `taskId: string, tenantId: string` | `Promise<ITask \| null>` |
| `search` | Search tasks | `query: string, tenantId: string, options?: QueryOptions<ITask>` | `Promise<PaginatedResult<ITask>>` |

## Document Repository

The document repository (`DocumentRepository`) provides methods for accessing and manipulating document data.

### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `findByOwner` | Find documents by owner | `ownerId: string, tenantId: string, options?: QueryOptions<IDocument>` | `Promise<IDocument[]>` |
| `findByFileType` | Find documents by file type | `fileType: string, tenantId: string, options?: QueryOptions<IDocument>` | `Promise<IDocument[]>` |
| `findByRelatedEntity` | Find documents by related entity | `entityType: string, entityId: string, tenantId: string, options?: QueryOptions<IDocument>` | `Promise<IDocument[]>` |
| `findPublicDocuments` | Find public documents | `tenantId: string, options?: QueryOptions<IDocument>` | `Promise<IDocument[]>` |
| `findByTag` | Find documents by tag | `tag: string, tenantId: string, options?: QueryOptions<IDocument>` | `Promise<IDocument[]>` |
| `setDocumentPublic` | Make a document public or private | `documentId: string, isPublic: boolean, tenantId: string` | `Promise<IDocument \| null>` |
| `search` | Search documents | `query: string, tenantId: string, options?: QueryOptions<IDocument>` | `Promise<PaginatedResult<IDocument>>` |

## Query Options

The `QueryOptions` interface defines the options that can be passed to repository methods to control filtering, sorting, pagination, and projection.

### Properties

| Property | Type | Description |
|----------|------|-------------|
| `filter` | `TypedFilter<T>` | Filter criteria for selecting documents |
| `sort` | `TypedSort<T>` | Sort criteria for ordering documents |
| `projection` | `TypedProjection<T>` | Projection for including or excluding fields |
| `page` | `number` | Page number (1-based) for pagination |
| `limit` | `number` | Page size for pagination |
| `populate` | `string[] \| PopulateOptions[]` | Population options for related documents |
| `lean` | `boolean` | Whether to return plain JavaScript objects instead of Mongoose documents |
| `collation` | `object` | Collation options for string comparison |
| `skip` | `number` | Number of documents to skip |
| `timeout` | `number` | Timeout for the query in milliseconds |
| `explain` | `boolean` | Whether to return query plan information instead of results |

## Error Handling

All repositories use a consistent error handling approach with the `DatabaseError` class. This class extends the `RepositoryError` class and provides detailed error information, including:

- Error message
- Error code
- Original error
- Additional error details

This approach makes it easier to debug issues and provide meaningful error messages to users.

## Tenant Isolation

All repositories enforce tenant isolation by including the tenant ID in all queries. This ensures that users can only access data that belongs to their tenant, preventing data leakage between tenants.

## Usage Examples

### Finding a Contact by Email

```typescript
import { contactRepository } from '../repositories';

async function findContactByEmail(email: string, tenantId: string) {
  try {
    const contact = await contactRepository.findByEmail(email, tenantId);
    return contact;
  } catch (error) {
    console.error('Error finding contact by email:', error);
    throw error;
  }
}
```

### Creating a New Opportunity

```typescript
import { opportunityRepository } from '../repositories';

async function createOpportunity(opportunity: Partial<IOpportunity>, tenantId: string) {
  try {
    const newOpportunity = await opportunityRepository.create(opportunity, tenantId);
    return newOpportunity;
  } catch (error) {
    console.error('Error creating opportunity:', error);
    throw error;
  }
}
```

### Searching for Companies

```typescript
import { companyRepository } from '../repositories';

async function searchCompanies(query: string, tenantId: string, page: number = 1, limit: number = 20) {
  try {
    const result = await companyRepository.search(query, tenantId, { page, limit });
    return result;
  } catch (error) {
    console.error('Error searching companies:', error);
    throw error;
  }
}
```

### Completing a Task

```typescript
import { taskRepository } from '../repositories';

async function completeTask(taskId: string, tenantId: string) {
  try {
    const task = await taskRepository.completeTask(taskId, tenantId);
    return task;
  } catch (error) {
    console.error('Error completing task:', error);
    throw error;
  }
}
```

### Making a Document Public

```typescript
import { documentRepository } from '../repositories';

async function makeDocumentPublic(documentId: string, tenantId: string) {
  try {
    const document = await documentRepository.setDocumentPublic(documentId, true, tenantId);
    return document;
  } catch (error) {
    console.error('Error making document public:', error);
    throw error;
  }
}
```
