# Domain Setup Guide

This guide provides step-by-step instructions for setting up and verifying domains in Aizako CRM for email tracking.

## Prerequisites

Before you begin, you'll need:

- Admin access to your Aizako CRM tenant
- Access to your domain's DNS settings
- A domain you want to use for sending emails

## Step 1: Register Your Domain

1. Navigate to **Settings > Email > Domains** in Aizako CRM
2. Click the **Add Domain** button
3. Enter your domain name (e.g., `yourdomain.com`)
4. Click **Register Domain**

The system will register your domain with Resend and provide you with the necessary DNS records to add to your domain.

## Step 2: Add DNS Records

You'll need to add two types of DNS records to verify your domain:

### DKIM Record

The DKIM record allows email recipients to verify that emails sent from your domain were actually authorized by you.

1. Log in to your domain registrar or DNS provider
2. Navigate to the DNS management section
3. Add a new CNAME record with the following details:
   - **Host/Name**: The value provided by Aizako CRM (usually something like `dkim._domainkey`)
   - **Value/Target**: The value provided by Aizako CRM (points to Resend's servers)
   - **TTL**: 3600 (or default)

### SPF Record

The SPF record specifies which mail servers are authorized to send email on behalf of your domain.

1. Check if you already have an SPF record (TXT record with `v=spf1` at the beginning)
2. If you have an existing SPF record:
   - Add `include:spf.resend.com` before the final `all` mechanism
   - Example: `v=spf1 include:_spf.google.com include:spf.resend.com -all`
3. If you don't have an SPF record:
   - Add a new TXT record with the following details:
     - **Host/Name**: `@` (or leave empty, depending on your DNS provider)
     - **Value/Content**: `v=spf1 include:spf.resend.com -all`
     - **TTL**: 3600 (or default)

## Step 3: Verify Domain

After adding the DNS records, Aizako CRM will automatically check the verification status:

1. Wait for DNS propagation (can take up to 24 hours)
2. The system checks verification status every 30 seconds
3. You can manually check by clicking the **Check DNS Records** button
4. Once verified, the status will change to "Verified"

## Step 4: Set Up Tracking Domain (Optional but Recommended)

Setting up a tracking domain improves deliverability and provides a consistent brand experience:

1. After domain verification, you'll be prompted to set up a tracking domain
2. Choose a subdomain (default is `track`)
3. Add a CNAME record with the following details:
   - **Host/Name**: Your chosen subdomain (e.g., `track`)
   - **Value/Target**: `u.resend.net`
   - **TTL**: 3600 (or default)
4. Click **Set Up Tracking Domain**

## Step 5: Test Your Setup

After completing the setup:

1. Navigate to **Settings > Email > Configuration**
2. Ensure your verified domain is selected as the sending domain
3. Send a test email to yourself
4. Check that the email is delivered and that opens/clicks are tracked

## Troubleshooting

### Domain Verification Issues

If your domain remains in "Pending" status:

1. **Check DNS Records**: Verify that the records were added correctly
2. **Wait for Propagation**: DNS changes can take up to 24 hours to propagate
3. **Check for Conflicts**: Ensure there are no conflicting records
4. **DNS Lookup Tool**: Use a tool like [MXToolbox](https://mxtoolbox.com/) to verify your DNS records

### SPF Record Length

SPF records have a 255-character limit. If your SPF record is too long:

1. Use the `include:` mechanism instead of listing IP addresses
2. Consider using SPF flattening services
3. Prioritize essential sending services

### DKIM Verification Failures

If DKIM verification fails:

1. Ensure the CNAME record is correctly added
2. Check for typos in the host or value
3. Verify that your DNS provider supports CNAME records at the specified level

## Next Steps

After setting up your domain:

1. Configure your email signature in **Settings > Email > Configuration**
2. Create email templates in **Settings > Email > Templates**
3. Set up notification preferences for email tracking events

For additional assistance, contact Aizako CRM <NAME_EMAIL>.
