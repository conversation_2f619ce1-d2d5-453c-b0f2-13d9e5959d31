# Aizako CRM Email Tracking Documentation

This documentation covers the email tracking functionality in Aizako CRM, including domain setup, tracking capabilities, and integration with Resend.

## Table of Contents

1. [Overview](#overview)
2. [Domain Management](#domain-management)
3. [Email Tracking](#email-tracking)
4. [Webhook Integration](#webhook-integration)
5. [Multi-Tenant Architecture](#multi-tenant-architecture)
6. [API Reference](#api-reference)
7. [Troubleshooting](#troubleshooting)

## Overview

Aizako CRM's email tracking system allows users to:

- Send emails with tracking capabilities
- Monitor email opens and link clicks in real-time
- Receive notifications when recipients engage with emails
- Generate AI-powered reply drafts based on recipient behavior
- Analyze email performance with detailed metrics

The system is built on top of Resend's email delivery platform and supports multi-tenant architecture with domain verification and branded tracking domains.

## Domain Management

### Domain Verification

Before sending tracked emails, each tenant must verify domain ownership to ensure deliverability and maintain sender reputation.

#### Verification Process

1. **Register Domain**: Add your domain in Settings > Email > Domains
2. **Add DNS Records**: Add the provided DKIM and SPF records to your domain's DNS settings
3. **Verification Check**: The system automatically checks verification status every 30 seconds
4. **Manual Check**: You can manually check verification status using the "Check DNS Records" button

#### Required DNS Records

Two types of DNS records are required for domain verification:

1. **DKIM Record** (DomainKeys Identified Mail)
   - Type: `CNAME`
   - Host: Usually a subdomain like `dkim._domainkey`
   - Value: Provided by Resend

2. **SPF Record** (Sender Policy Framework)
   - Type: `TXT`
   - Host: Usually `@` (root domain)
   - Value: Includes `include:spf.resend.com` to authorize Resend to send on your behalf

### Tracking Domain Setup

Setting up a tracking domain improves deliverability and provides a consistent brand experience.

#### Setup Process

1. **Choose Subdomain**: Select a subdomain for tracking (default: `track`)
2. **Add DNS Record**: Add a CNAME record pointing to Resend's tracking servers
3. **Verification**: The system verifies the tracking domain setup

#### Required DNS Record

- Type: `CNAME`
- Host: Your chosen subdomain (e.g., `track`)
- Value: `u.resend.net`

## Email Tracking

### Tracking Capabilities

Aizako CRM's email tracking includes:

1. **Open Tracking**: Invisible pixel embedded in emails to detect when recipients open messages
2. **Link Tracking**: All links are rewritten to pass through tracking servers before redirecting to the destination
3. **Real-time Notifications**: Instant alerts when recipients engage with emails
4. **AI Reply Drafts**: Automatically generated reply suggestions based on recipient behavior

### How Tracking Works

1. **Pixel Tracking**: A 1x1 transparent pixel is added to each email. When loaded, it registers an open event.
2. **Link Rewriting**: Links are rewritten to go through your tracking domain before redirecting to the destination.
3. **Webhook Events**: Resend sends events to Aizako CRM's webhook endpoint when emails are opened or links are clicked.
4. **Activity Recording**: Events are stored as activities linked to contacts in the CRM.

### Privacy Considerations

- Recipients are not explicitly notified about tracking
- Consider adding a privacy notice in your email footer
- Comply with applicable privacy regulations (GDPR, CCPA, etc.)

## Webhook Integration

Aizako CRM uses webhooks to receive real-time email tracking events from Resend.

### Webhook Security

- Each tenant has a unique webhook secret for signature verification
- Webhook signatures are verified to prevent unauthorized events
- Secrets can be rotated for enhanced security

### Supported Events

- `email.delivered`: Email was successfully delivered
- `email.opened`: Recipient opened the email
- `email.clicked`: Recipient clicked a link in the email
- `email.bounced`: Email could not be delivered
- `email.complained`: Recipient marked the email as spam

### Event Processing

1. Webhook receives event from Resend
2. Signature is verified using tenant-specific secret
3. Event is processed and stored as an activity
4. Notifications are sent to relevant users
5. AI reply drafts are generated for opens and clicks

## Multi-Tenant Architecture

Aizako CRM supports multiple tenants (organizations) with isolated data and configurations.

### Tenant Isolation

- Each tenant has separate domains and tracking configurations
- Webhook events are routed to the correct tenant
- Email tracking data is isolated by tenant

### Tenant Domain Model

The `TenantDomain` model stores domain information for each tenant:

- Domain name
- Resend domain ID
- Verification status
- Verification records
- Tracking domain
- Webhook secret

## API Reference

### Domain Management API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/tenant/domains` | GET | Get all domains for a tenant |
| `/api/tenant/domains/:id` | GET | Get a specific domain |
| `/api/tenant/domains` | POST | Register a new domain |
| `/api/tenant/domains/:id/tracking` | POST | Set up tracking domain |
| `/api/tenant/domains/:id/verification` | GET | Check verification status |
| `/api/tenant/domains/:id/dns-check` | GET | Check DNS records |
| `/api/tenant/domains/:id/rotate-secret` | POST | Rotate webhook secret |
| `/api/tenant/domains/:id` | DELETE | Delete a domain |

### Email Tracking API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/email-tracking` | GET | Get all tracking records |
| `/api/email-tracking/:id` | GET | Get a specific tracking record |
| `/api/email-tracking` | POST | Create a tracking record |
| `/api/email-tracking/:id/reply-draft` | POST | Generate a reply draft |

### Webhook API

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/webhooks/resend` | POST | Receive Resend webhook events |

## Troubleshooting

### Common Issues

#### Domain Verification Issues

- **Problem**: Domain verification is stuck in pending status
- **Solution**: 
  - Verify DNS records are correctly added
  - Check for propagation delays (can take up to 24 hours)
  - Ensure there are no conflicting SPF records

#### Tracking Not Working

- **Problem**: Email opens or clicks are not being tracked
- **Solution**:
  - Verify tracking domain is set up correctly
  - Check if recipient's email client blocks tracking pixels
  - Ensure tracking is enabled for the email

#### Webhook Issues

- **Problem**: Not receiving webhook events
- **Solution**:
  - Verify webhook URL is accessible from the internet
  - Check webhook secret is correctly configured
  - Look for errors in webhook processing logs

### Support

For additional support, contact Aizako CRM <NAME_EMAIL> or open a ticket in the support portal.
