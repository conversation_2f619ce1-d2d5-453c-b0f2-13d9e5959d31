# Developer Guide: Email Tracking Integration

This guide provides technical details for developers working with Aizako CRM's email tracking system.

## Architecture Overview

The email tracking system consists of several components:

1. **Backend Services**:
   - `ResendService`: Handles interactions with Resend API
   - `EmailTrackingService`: Manages tracking records and events
   - `TenantDomainService`: Manages domain verification and configuration
   - `EmailService`: Sends emails with tracking capabilities

2. **Data Models**:
   - `EmailTracking`: Stores tracking information for sent emails
   - `TenantDomain`: Stores domain configuration for tenants
   - `Activity`: Records email engagement events
   - `Edge`: Establishes relationships between contacts and activities

3. **API Endpoints**:
   - Domain management endpoints
   - Email tracking endpoints
   - Webhook endpoint for Resend events

4. **Frontend Components**:
   - Domain verification wizard
   - Domain management interface
   - Email tracking dashboard

## Integration with Resend

### Authentication

Resend API authentication uses an API key:

```typescript
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const RESEND_API_URL = 'https://api.resend.com';

// Make authenticated request
const response = await axios({
  method: 'GET',
  url: `${RESEND_API_URL}/domains`,
  headers: {
    'Authorization': `Bearer ${RESEND_API_KEY}`,
    'Content-Type': 'application/json'
  }
});
```

### Domain Management

#### Creating a Domain

```typescript
// Register domain with Resend
const response = await axios({
  method: 'POST',
  url: `${RESEND_API_URL}/domains`,
  headers: {
    'Authorization': `Bearer ${RESEND_API_KEY}`,
    'Content-Type': 'application/json'
  },
  data: JSON.stringify({ name: 'example.com' })
});

// Response contains verification records
const { id, dkim, spf } = response.data;
```

#### Checking Domain Status

```typescript
// Check domain verification status
const response = await axios({
  method: 'GET',
  url: `${RESEND_API_URL}/domains/${domainId}`,
  headers: {
    'Authorization': `Bearer ${RESEND_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const { status, dkim_status, spf_status } = response.data;
```

### Sending Tracked Emails

```typescript
// Send email with tracking
const response = await axios({
  method: 'POST',
  url: `${RESEND_API_URL}/emails`,
  headers: {
    'Authorization': `Bearer ${RESEND_API_KEY}`,
    'Content-Type': 'application/json'
  },
  data: JSON.stringify({
    from: 'John Doe <<EMAIL>>',
    to: ['<EMAIL>'],
    subject: 'Hello World',
    html: '<p>Hello World</p>',
    text: 'Hello World',
    headers: {
      'X-Aizako-Message-ID': 'unique-message-id'
    },
    track_opens: true,
    track_clicks: true
  })
});
```

### Webhook Events

Resend sends webhook events to your configured endpoint. Events include:

- `email.delivered`: Email was successfully delivered
- `email.opened`: Recipient opened the email
- `email.clicked`: Recipient clicked a link in the email
- `email.bounced`: Email could not be delivered
- `email.complained`: Recipient marked the email as spam

Example webhook payload:

```json
{
  "type": "email.opened",
  "data": {
    "email_id": "email_123456789",
    "domain_id": "domain_123456789",
    "recipient": "<EMAIL>",
    "ip": "*********",
    "user_agent": "Mozilla/5.0...",
    "headers": {
      "X-Aizako-Message-ID": "unique-message-id"
    },
    "geo": {
      "city": "San Francisco",
      "country": "US",
      "region": "CA"
    },
    "created_at": "2023-05-01T12:00:00Z"
  }
}
```

## Webhook Security

### Signature Verification

Resend signs webhook payloads with a secret. Verify the signature to ensure authenticity:

```typescript
function verifyWebhookSignature(
  signature: string,
  payload: string,
  secret: string
): boolean {
  try {
    // Parse the signature
    const [timestamp, signatureHash] = signature.split(',');
    const timestampValue = timestamp.split('=')[1];
    const signatureValue = signatureHash.split('=')[1];

    // Check if the timestamp is within 5 minutes
    const timestampDate = new Date(parseInt(timestampValue) * 1000);
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    if (timestampDate < fiveMinutesAgo) {
      return false;
    }

    // Verify the signature
    const data = `${timestampValue}.${payload}`;
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(data)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(signatureValue),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    return false;
  }
}
```

### Multi-Tenant Webhook Handling

For multi-tenant setups, each tenant has a unique webhook secret:

1. Extract the `domain_id` from the webhook payload
2. Look up the tenant associated with that domain
3. Verify the signature using the tenant's webhook secret
4. Process the event in the tenant's context

## Data Models

### EmailTracking Model

```typescript
interface IEmailTracking extends Document {
  userId: mongoose.Types.ObjectId;
  tenantId?: mongoose.Types.ObjectId;
  contactId?: mongoose.Types.ObjectId;
  sequenceId?: mongoose.Types.ObjectId;
  sequenceStepId?: mongoose.Types.ObjectId;
  messageId: string;
  subject: string;
  recipient: string;
  sender: string;
  status: EmailEventType;
  pixelId: string;
  trackingEnabled: boolean;
  linkTrackingEnabled: boolean;
  attachmentTrackingEnabled: boolean;
  replyDraftGenerated?: boolean;
  replyDraftId?: string;
  events: IEmailEvent[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

### TenantDomain Model

```typescript
interface ITenantDomain extends Document {
  tenantId: mongoose.Types.ObjectId;
  domain: string;
  resendDomainId?: string;
  trackingDomain?: string;
  webhookSecret?: string;
  verificationStatus: DomainVerificationStatus;
  verificationRecords: IDNSRecord[];
  trackingRecords?: IDNSRecord[];
  lastVerificationCheck?: Date;
  isDefault: boolean;
  isActive: boolean;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

## API Reference

### Domain Management API

```typescript
// Register a new domain
const domain = await tenantDomainService.registerDomain(
  tenantId,
  'example.com',
  true // isDefault
);

// Set up tracking domain
const updatedDomain = await tenantDomainService.setupTrackingDomain(
  tenantId,
  'example.com',
  'track' // trackingSubdomain
);

// Check verification status
const status = await tenantDomainService.checkVerificationStatus(domainId);

// Check DNS records
const dnsCheck = await tenantDomainService.checkDNSRecords(domainId);

// Rotate webhook secret
const newSecret = await tenantDomainService.rotateWebhookSecret(domainId);
```

### Email Tracking API

```typescript
// Create tracking record
const tracking = await emailTrackingService.createTracking({
  userId,
  contactId,
  messageId: 'unique-message-id',
  subject: 'Hello World',
  recipient: '<EMAIL>',
  sender: '<EMAIL>'
});

// Process open event
await emailTrackingService.processOpenEvent(tracking.pixelId, {
  ip: '*********',
  userAgent: 'Mozilla/5.0...',
  device: 'desktop'
});

// Process click event
await emailTrackingService.processClickEvent('link-id', {
  ip: '*********',
  userAgent: 'Mozilla/5.0...',
  device: 'desktop',
  url: 'https://example.com'
});

// Generate reply draft
const draftId = await emailTrackingService.generateReplyDraft(trackingId);
```

## Frontend Integration

### API Client

```typescript
// Domain API
const domains = await api.get('/tenant/domains');
const domain = await api.get(`/tenant/domains/${domainId}`);
await api.post('/tenant/domains', { domain: 'example.com', isDefault: true });
await api.post(`/tenant/domains/${domainId}/tracking`, { trackingSubdomain: 'track' });

// Email Tracking API
const trackingRecords = await api.get('/email-tracking');
const trackingRecord = await api.get(`/email-tracking/${trackingId}`);
await api.post('/email-tracking', { /* tracking data */ });
await api.post(`/email-tracking/${trackingId}/reply-draft`);
```

## Testing

### Unit Testing

```typescript
// Test email tracking service
describe('Email Tracking Service', () => {
  it('should create a tracking record', async () => {
    const tracking = await emailTrackingService.createTracking({
      userId: 'user-id',
      messageId: 'test-message-id',
      subject: 'Test Subject',
      recipient: '<EMAIL>',
      sender: '<EMAIL>'
    });

    expect(tracking).toBeDefined();
    expect(tracking.messageId).toBe('test-message-id');
    expect(tracking.pixelId).toBeDefined();
  });

  it('should process an open event', async () => {
    const tracking = await emailTrackingService.createTracking({ /* ... */ });
    const result = await emailTrackingService.processOpenEvent(tracking.pixelId, {
      ip: '127.0.0.1',
      userAgent: 'test-user-agent'
    });

    expect(result).toBeDefined();
    expect(result?.status).toBe('opened');
    expect(result?.events.length).toBe(2);
  });
});
```

### Integration Testing

```typescript
// Test webhook endpoint
describe('Webhook Endpoint', () => {
  it('should process an open event', async () => {
    const payload = {
      type: 'email.opened',
      data: {
        email_id: 'email-id',
        domain_id: 'domain-id',
        recipient: '<EMAIL>',
        headers: {
          'X-Aizako-Message-ID': 'test-message-id'
        }
      }
    };

    const signature = generateTestSignature(payload, webhookSecret);

    const response = await request(app)
      .post('/api/webhooks/resend')
      .set('resend-signature', signature)
      .send(payload);

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

## Best Practices

1. **Secure Webhook Handling**:
   - Always verify webhook signatures
   - Use tenant-specific secrets
   - Implement rate limiting

2. **Error Handling**:
   - Log all API errors
   - Implement retry logic for transient failures
   - Provide clear error messages

3. **Performance Optimization**:
   - Use indexes for frequently queried fields
   - Implement caching for domain verification status
   - Process webhook events asynchronously

4. **Testing**:
   - Write unit tests for all services
   - Create integration tests for API endpoints
   - Test with real email clients

5. **Security**:
   - Store API keys securely
   - Rotate webhook secrets periodically
   - Implement proper access controls
