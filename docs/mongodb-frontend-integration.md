# MongoDB Frontend Integration

This document provides information about the MongoDB frontend integration for Aizako CRM.

## Overview

The frontend has been updated to work with both MongoDB and PostgreSQL data structures. The main changes include:

1. MongoDB-specific API client
2. API context provider for dynamic database selection
3. ID handling for MongoDB ObjectIds

## API Client

The API client has been updated to support both MongoDB and PostgreSQL:

```typescript
import { getApiClient } from '../api/api-client';

// Get the appropriate API client based on the database type
const apiClient = await getApiClient();

// Use the API client
const contacts = await apiClient.contacts.getAll();
```

## API Context Provider

A new API context provider has been created to dynamically select the appropriate API client:

```tsx
import { ApiProvider, useApi } from '../contexts/ApiContext';

// Wrap your application with the API provider
<ApiProvider>
  <App />
</ApiProvider>

// Use the API client in your components
function MyComponent() {
  const api = useApi();
  
  // Use the API client
  const fetchData = async () => {
    const contacts = await api.contacts.getAll();
    // ...
  };
  
  return (
    // ...
  );
}
```

## ID Handling

When working with MongoDB, IDs are strings (ObjectIds) instead of numbers. The API client handles this automatically, but you should be aware of this when working with IDs in your components:

```typescript
// PostgreSQL
const contact = await apiClient.contacts.getById(1);

// MongoDB
const contact = await apiClient.contacts.getById('60f1a5b3e6b3f32d4c8b4567');
```

## Data Structure Differences

MongoDB and PostgreSQL have some differences in data structure:

1. MongoDB uses `_id` instead of `id`
2. MongoDB uses string IDs instead of numeric IDs
3. MongoDB dates are ISO strings instead of timestamps

The API client normalizes these differences, but you should be aware of them when working with raw data.

## Testing

To test the MongoDB frontend integration:

1. Start the server with MongoDB enabled: `npm run db:mongo`
2. Open the application in your browser
3. Check the console for "Using MongoDB API client" message
4. Test all CRUD operations

## Troubleshooting

If you encounter issues with the MongoDB frontend integration:

1. Check the browser console for errors
2. Verify that the server is running with MongoDB enabled
3. Check that the MongoDB connection is successful
4. Verify that the API client is correctly detecting MongoDB

## Implementation Details

### MongoDB API Client

The MongoDB API client (`mongo-api-client.ts`) implements all the same endpoints as the PostgreSQL API client, but with MongoDB-specific data structures.

### API Context Provider

The API context provider (`ApiContext.tsx`) dynamically selects the appropriate API client based on the database type detected from the server.

### ID Handling

The MongoDB API client handles string IDs (ObjectIds) instead of numeric IDs, ensuring that all API calls work correctly with MongoDB.

### Data Normalization

The API client normalizes data structures between MongoDB and PostgreSQL, ensuring that components receive consistent data regardless of the database type.
