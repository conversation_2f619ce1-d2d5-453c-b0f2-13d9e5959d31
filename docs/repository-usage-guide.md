# Repository Usage Guide

This guide explains how to use the repositories in the Aizako CRM application. It provides examples and best practices for common operations.

## Table of Contents

1. [Introduction](#introduction)
2. [Importing Repositories](#importing-repositories)
3. [Basic Operations](#basic-operations)
4. [Advanced Queries](#advanced-queries)
5. [<PERSON>rror Handling](#error-handling)
6. [Pagination](#pagination)
7. [Transactions](#transactions)
8. [Best Practices](#best-practices)

## Introduction

Repositories provide a clean and consistent way to access data in the application. They abstract the data access layer from the business logic, making the code more maintainable, testable, and scalable.

All repositories in the Aizako CRM application follow the same pattern and provide similar methods for common operations. They also enforce tenant isolation, ensuring that users can only access data that belongs to their tenant.

## Importing Repositories

You can import repositories in two ways:

### Individual Import

```typescript
import { contactRepository } from '../repositories/contact-repository';
import { opportunityRepository } from '../repositories/opportunity-repository';
```

### Bulk Import

```typescript
import repositories from '../repositories';

const { contactRepository, opportunityRepository } = repositories;
```

## Basic Operations

### Finding an Entity by ID

```typescript
async function getContact(contactId: string, tenantId: string) {
  const contact = await contactRepository.findById(contactId, tenantId);
  return contact;
}
```

### Finding All Entities

```typescript
async function getAllContacts(tenantId: string) {
  const contacts = await contactRepository.findAll(tenantId);
  return contacts;
}
```

### Creating an Entity

```typescript
async function createContact(contactData: Partial<IContact>, tenantId: string) {
  const contact = await contactRepository.create(contactData, tenantId);
  return contact;
}
```

### Updating an Entity

```typescript
async function updateContact(contactId: string, contactData: Partial<IContact>, tenantId: string) {
  const contact = await contactRepository.update(contactId, contactData, tenantId);
  return contact;
}
```

### Deleting an Entity

```typescript
async function deleteContact(contactId: string, tenantId: string) {
  const success = await contactRepository.delete(contactId, tenantId);
  return success;
}
```

## Advanced Queries

### Filtering

You can use the `filter` option to specify conditions for selecting entities:

```typescript
async function getActiveContacts(tenantId: string) {
  const contacts = await contactRepository.findAll(tenantId, {
    filter: { status: 'active' }
  });
  return contacts;
}
```

### Sorting

You can use the `sort` option to specify the sort order:

```typescript
async function getContactsSortedByName(tenantId: string) {
  const contacts = await contactRepository.findAll(tenantId, {
    sort: { firstName: 1, lastName: 1 }
  });
  return contacts;
}
```

### Projection

You can use the `projection` option to include or exclude fields:

```typescript
async function getContactNames(tenantId: string) {
  const contacts = await contactRepository.findAll(tenantId, {
    projection: { firstName: 1, lastName: 1, _id: 0 }
  });
  return contacts;
}
```

### Population

You can use the `populate` option to include related entities:

```typescript
async function getContactsWithCompany(tenantId: string) {
  const contacts = await contactRepository.findAll(tenantId, {
    populate: ['company']
  });
  return contacts;
}
```

### Complex Queries

You can combine multiple options for complex queries:

```typescript
async function getActiveContactsSortedByNameWithCompany(tenantId: string) {
  const contacts = await contactRepository.findAll(tenantId, {
    filter: { status: 'active' },
    sort: { firstName: 1, lastName: 1 },
    populate: ['company']
  });
  return contacts;
}
```

## Error Handling

All repositories use a consistent error handling approach with the `DatabaseError` class. You should catch and handle these errors appropriately:

```typescript
async function getContact(contactId: string, tenantId: string) {
  try {
    const contact = await contactRepository.findById(contactId, tenantId);
    return contact;
  } catch (error) {
    if (error instanceof DatabaseError) {
      // Handle database error
      console.error('Database error:', error.message);
      // You can also access error.code, error.originalError, and error.details
    } else {
      // Handle other errors
      console.error('Unexpected error:', error);
    }
    throw error;
  }
}
```

## Pagination

You can use the `page` and `limit` options for pagination:

```typescript
async function getContactsPaginated(tenantId: string, page: number = 1, limit: number = 20) {
  const result = await contactRepository.findAllPaginated(tenantId, {
    page,
    limit
  });
  
  // result contains:
  // - items: Array of contacts
  // - total: Total number of contacts
  // - page: Current page number
  // - limit: Page size
  // - totalPages: Total number of pages
  // - hasPrevPage: Whether there is a previous page
  // - hasNextPage: Whether there is a next page
  // - prevPage: Previous page number or null
  // - nextPage: Next page number or null
  
  return result;
}
```

## Transactions

For operations that need to be atomic, you can use transactions:

```typescript
import mongoose from 'mongoose';

async function transferOpportunity(opportunityId: string, fromContactId: string, toContactId: string, tenantId: string) {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    // Update the opportunity
    const opportunity = await opportunityRepository.findOneAndUpdate(
      { _id: opportunityId, contactId: fromContactId },
      { contactId: toContactId },
      tenantId,
      { new: true }
    );
    
    if (!opportunity) {
      throw new Error('Opportunity not found or does not belong to the specified contact');
    }
    
    // Create a note about the transfer
    await noteRepository.create({
      title: 'Opportunity transferred',
      content: `Opportunity ${opportunity.name} transferred from contact ${fromContactId} to contact ${toContactId}`,
      relatedTo: {
        type: 'opportunity',
        id: opportunityId
      }
    }, tenantId);
    
    // Commit the transaction
    await session.commitTransaction();
    session.endSession();
    
    return opportunity;
  } catch (error) {
    // Abort the transaction on error
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
}
```

## Best Practices

### 1. Always Include Tenant ID

Always include the tenant ID in all repository method calls to ensure tenant isolation:

```typescript
// Good
const contacts = await contactRepository.findAll(tenantId);

// Bad - will throw an error
const contacts = await contactRepository.findAll();
```

### 2. Use Type-Safe Filters

Use type-safe filters to ensure that you're using the correct field names and types:

```typescript
// Good
const contacts = await contactRepository.findAll(tenantId, {
  filter: { status: 'active' }
});

// Bad - typo in field name
const contacts = await contactRepository.findAll(tenantId, {
  filter: { statuss: 'active' }
});
```

### 3. Handle Errors Properly

Always catch and handle errors from repository methods:

```typescript
// Good
try {
  const contact = await contactRepository.findById(contactId, tenantId);
  // Do something with the contact
} catch (error) {
  console.error('Error finding contact:', error);
  // Handle the error appropriately
}

// Bad - unhandled error
const contact = await contactRepository.findById(contactId, tenantId);
// Do something with the contact
```

### 4. Use Pagination for Large Result Sets

Use pagination for queries that might return a large number of results:

```typescript
// Good
const result = await contactRepository.findAllPaginated(tenantId, {
  page: 1,
  limit: 20
});

// Bad - might return too many results
const contacts = await contactRepository.findAll(tenantId);
```

### 5. Use Specialized Repository Methods

Use specialized repository methods when available instead of generic methods with filters:

```typescript
// Good
const contacts = await contactRepository.findByCompanyId(companyId, tenantId);

// Less good - using generic method with filter
const contacts = await contactRepository.findAll(tenantId, {
  filter: { companyId }
});
```

### 6. Avoid Unnecessary Database Calls

Avoid making unnecessary database calls by combining operations when possible:

```typescript
// Good - single database call
const contacts = await contactRepository.findAll(tenantId, {
  filter: { status: 'active' },
  sort: { firstName: 1 }
});

// Bad - multiple database calls
const contacts = await contactRepository.findAll(tenantId, {
  filter: { status: 'active' }
});
contacts.sort((a, b) => a.firstName.localeCompare(b.firstName));
```

### 7. Use Transactions for Related Operations

Use transactions when performing multiple related operations that need to be atomic:

```typescript
// Good - using transaction
const session = await mongoose.startSession();
session.startTransaction();

try {
  // Perform multiple operations
  await session.commitTransaction();
} catch (error) {
  await session.abortTransaction();
  throw error;
} finally {
  session.endSession();
}

// Bad - no transaction
// Perform multiple operations that might leave the database in an inconsistent state if one fails
```

### 8. Validate Input Data

Validate input data before passing it to repository methods:

```typescript
// Good - validating input
if (!contactId || typeof contactId !== 'string') {
  throw new Error('Invalid contact ID');
}
const contact = await contactRepository.findById(contactId, tenantId);

// Bad - no validation
const contact = await contactRepository.findById(contactId, tenantId);
```

### 9. Use Lean Queries for Read-Only Operations

Use lean queries for read-only operations to improve performance:

```typescript
// Good - using lean for read-only operation
const contacts = await contactRepository.findAll(tenantId, {
  lean: true
});

// Less good - not using lean for read-only operation
const contacts = await contactRepository.findAll(tenantId);
```

### 10. Document Your Repository Usage

Document your repository usage to make it easier for other developers to understand your code:

```typescript
/**
 * Get active contacts sorted by name
 * @param tenantId Tenant ID
 * @returns Array of active contacts sorted by name
 */
async function getActiveContactsSortedByName(tenantId: string) {
  return await contactRepository.findAll(tenantId, {
    filter: { status: 'active' },
    sort: { firstName: 1, lastName: 1 }
  });
}
```
