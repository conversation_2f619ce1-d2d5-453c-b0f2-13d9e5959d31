# Repository Testing Guide

This guide explains how to test repositories in the Aizako CRM application. It provides examples and best practices for writing unit tests for repositories.

## Table of Contents

1. [Introduction](#introduction)
2. [Setting Up the Test Environment](#setting-up-the-test-environment)
3. [Writing Unit Tests](#writing-unit-tests)
4. [Testing Error Handling](#testing-error-handling)
5. [Testing Edge Cases](#testing-edge-cases)
6. [Integration Testing](#integration-testing)
7. [Best Practices](#best-practices)

## Introduction

Testing repositories is essential to ensure that data access works correctly. The Aizako CRM application uses <PERSON><PERSON> as the testing framework and MongoDB Memory Server to create an in-memory MongoDB database for testing.

## Setting Up the Test Environment

### Test File Structure

Repository test files should be placed in the `server/tests/repositories` directory and named with the pattern `[repository-name].test.ts`.

### Basic Test Setup

Here's a basic setup for testing a repository:

```typescript
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { ContactRepository } from '../../repositories/contact-repository';
import { Contact, IContact } from '../../models/mongoose/contact-model';
import { DatabaseError } from '../../repositories/mongodb-repository';

// Mock the logger to prevent console output during tests
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('ContactRepository', () => {
  let mongoServer: MongoMemoryServer;
  let contactRepository: ContactRepository;
  let testTenantId: string;
  let testContactId: mongoose.Types.ObjectId;

  // Set up the in-memory database before all tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a new repository instance
    contactRepository = new ContactRepository();
    
    // Create test IDs
    testTenantId = 'test-tenant-id';
    testContactId = new mongoose.Types.ObjectId();
  });

  // Clean up after all tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up the database after each test
  afterEach(async () => {
    await Contact.deleteMany({});
  });

  // Helper function to create a test contact
  const createTestContact = async (overrides: Partial<IContact> = {}): Promise<IContact> => {
    const contact = new Contact({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      tenantId: testTenantId,
      ...overrides,
    });

    await contact.save();
    return contact.toObject();
  };

  // Tests go here
});
```

## Writing Unit Tests

### Testing Basic Operations

#### Testing findById

```typescript
describe('findById', () => {
  it('should find a contact by ID', async () => {
    // Create a test contact
    const testContact = await createTestContact();

    // Find the contact by ID
    const contact = await contactRepository.findById(
      testContact._id.toString(),
      testTenantId
    );

    // Verify the result
    expect(contact).not.toBeNull();
    expect(contact?.id).toBe(testContact._id.toString());
    expect(contact?.firstName).toBe('Test');
    expect(contact?.lastName).toBe('User');
  });

  it('should return null for non-existent ID', async () => {
    // Generate a non-existent ID
    const nonExistentId = new mongoose.Types.ObjectId();

    // Try to find a contact with the non-existent ID
    const contact = await contactRepository.findById(
      nonExistentId.toString(),
      testTenantId
    );

    // Verify the result
    expect(contact).toBeNull();
  });
});
```

#### Testing findAll

```typescript
describe('findAll', () => {
  it('should find all contacts', async () => {
    // Create test contacts
    await createTestContact();
    await createTestContact({ email: '<EMAIL>' });
    await createTestContact({ email: '<EMAIL>' });

    // Find all contacts
    const contacts = await contactRepository.findAll(testTenantId);

    // Verify the result
    expect(contacts).toHaveLength(3);
  });

  it('should find contacts with filter', async () => {
    // Create test contacts
    await createTestContact();
    await createTestContact({ firstName: 'Jane', email: '<EMAIL>' });
    await createTestContact({ firstName: 'John', email: '<EMAIL>' });

    // Find contacts with filter
    const contacts = await contactRepository.findAll(testTenantId, {
      filter: { firstName: 'Jane' }
    });

    // Verify the result
    expect(contacts).toHaveLength(1);
    expect(contacts[0].firstName).toBe('Jane');
  });
});
```

#### Testing create

```typescript
describe('create', () => {
  it('should create a new contact', async () => {
    // Create a new contact
    const contact = await contactRepository.create({
      firstName: 'New',
      lastName: 'Contact',
      email: '<EMAIL>',
    }, testTenantId);

    // Verify the result
    expect(contact).not.toBeNull();
    expect(contact.firstName).toBe('New');
    expect(contact.lastName).toBe('Contact');
    expect(contact.email).toBe('<EMAIL>');
    expect(contact.tenantId).toBe(testTenantId);

    // Verify that the contact was saved to the database
    const savedContact = await Contact.findById(contact.id);
    expect(savedContact).not.toBeNull();
    expect(savedContact?.firstName).toBe('New');
  });
});
```

#### Testing update

```typescript
describe('update', () => {
  it('should update a contact', async () => {
    // Create a test contact
    const testContact = await createTestContact();

    // Update the contact
    const updatedContact = await contactRepository.update(
      testContact._id.toString(),
      { firstName: 'Updated' },
      testTenantId
    );

    // Verify the result
    expect(updatedContact).not.toBeNull();
    expect(updatedContact?.firstName).toBe('Updated');
    expect(updatedContact?.lastName).toBe('User');

    // Verify that the contact was updated in the database
    const savedContact = await Contact.findById(testContact._id);
    expect(savedContact).not.toBeNull();
    expect(savedContact?.firstName).toBe('Updated');
  });

  it('should return null for non-existent ID', async () => {
    // Generate a non-existent ID
    const nonExistentId = new mongoose.Types.ObjectId();

    // Try to update a contact with the non-existent ID
    const updatedContact = await contactRepository.update(
      nonExistentId.toString(),
      { firstName: 'Updated' },
      testTenantId
    );

    // Verify the result
    expect(updatedContact).toBeNull();
  });
});
```

#### Testing delete

```typescript
describe('delete', () => {
  it('should delete a contact', async () => {
    // Create a test contact
    const testContact = await createTestContact();

    // Delete the contact
    const result = await contactRepository.delete(
      testContact._id.toString(),
      testTenantId
    );

    // Verify the result
    expect(result).toBe(true);

    // Verify that the contact was deleted from the database
    const deletedContact = await Contact.findById(testContact._id);
    expect(deletedContact).toBeNull();
  });

  it('should return false for non-existent ID', async () => {
    // Generate a non-existent ID
    const nonExistentId = new mongoose.Types.ObjectId();

    // Try to delete a contact with the non-existent ID
    const result = await contactRepository.delete(
      nonExistentId.toString(),
      testTenantId
    );

    // Verify the result
    expect(result).toBe(false);
  });
});
```

### Testing Specialized Methods

#### Testing findByEmail

```typescript
describe('findByEmail', () => {
  it('should find a contact by email', async () => {
    // Create a test contact
    await createTestContact();

    // Find the contact by email
    const contact = await contactRepository.findByEmail(
      '<EMAIL>',
      testTenantId
    );

    // Verify the result
    expect(contact).not.toBeNull();
    expect(contact?.email).toBe('<EMAIL>');
  });

  it('should return null for non-existent email', async () => {
    // Find a contact with a non-existent email
    const contact = await contactRepository.findByEmail(
      '<EMAIL>',
      testTenantId
    );

    // Verify the result
    expect(contact).toBeNull();
  });
});
```

## Testing Error Handling

```typescript
describe('error handling', () => {
  it('should throw DatabaseError for invalid ID format', async () => {
    // Try to find a contact with an invalid ID format
    await expect(
      contactRepository.findById('invalid-id', testTenantId)
    ).rejects.toThrow(DatabaseError);
  });

  it('should throw DatabaseError for missing tenant ID', async () => {
    // Try to find a contact without a tenant ID
    await expect(
      // @ts-ignore - Intentionally passing undefined for testing
      contactRepository.findById(testContactId.toString(), undefined)
    ).rejects.toThrow(DatabaseError);
  });
});
```

## Testing Edge Cases

```typescript
describe('edge cases', () => {
  it('should handle empty filter', async () => {
    // Create test contacts
    await createTestContact();
    await createTestContact({ email: '<EMAIL>' });

    // Find contacts with empty filter
    const contacts = await contactRepository.findAll(testTenantId, {
      filter: {}
    });

    // Verify the result
    expect(contacts).toHaveLength(2);
  });

  it('should handle non-existent tenant ID', async () => {
    // Create a test contact
    await createTestContact();

    // Find contacts with non-existent tenant ID
    const contacts = await contactRepository.findAll('non-existent-tenant');

    // Verify the result
    expect(contacts).toHaveLength(0);
  });

  it('should handle pagination with no results', async () => {
    // Find paginated contacts with no results
    const result = await contactRepository.findAllPaginated(testTenantId);

    // Verify the result
    expect(result.items).toHaveLength(0);
    expect(result.total).toBe(0);
    expect(result.totalPages).toBe(0);
  });
});
```

## Integration Testing

Integration tests verify that repositories work correctly with other components of the application. Here's an example of an integration test for a repository and a service:

```typescript
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { ContactRepository } from '../../repositories/contact-repository';
import { ContactService } from '../../services/contact-service';
import { Contact, IContact } from '../../models/mongoose/contact-model';

describe('ContactService with ContactRepository', () => {
  let mongoServer: MongoMemoryServer;
  let contactRepository: ContactRepository;
  let contactService: ContactService;
  let testTenantId: string;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    contactRepository = new ContactRepository();
    contactService = new ContactService(contactRepository);
    
    testTenantId = 'test-tenant-id';
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  afterEach(async () => {
    await Contact.deleteMany({});
  });

  it('should create and retrieve a contact', async () => {
    // Create a contact using the service
    const contact = await contactService.createContact({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
    }, testTenantId);

    // Retrieve the contact using the service
    const retrievedContact = await contactService.getContactById(
      contact.id,
      testTenantId
    );

    // Verify the result
    expect(retrievedContact).not.toBeNull();
    expect(retrievedContact?.firstName).toBe('Test');
    expect(retrievedContact?.lastName).toBe('User');
    expect(retrievedContact?.email).toBe('<EMAIL>');
  });
});
```

## Best Practices

### 1. Use In-Memory Database

Use MongoDB Memory Server to create an in-memory database for testing. This ensures that tests are isolated and don't affect the real database.

### 2. Clean Up After Tests

Clean up the database after each test to ensure that tests are isolated and don't affect each other.

### 3. Mock External Dependencies

Mock external dependencies like loggers to prevent side effects during testing.

### 4. Test Error Handling

Test error handling to ensure that repositories handle errors correctly and provide meaningful error messages.

### 5. Test Edge Cases

Test edge cases like empty filters, non-existent IDs, and pagination with no results to ensure that repositories handle them correctly.

### 6. Use Helper Functions

Use helper functions to create test data to avoid duplicating code.

### 7. Test Tenant Isolation

Test tenant isolation to ensure that repositories enforce tenant isolation correctly.

### 8. Test Type Safety

Test type safety to ensure that repositories handle different types of input correctly.

### 9. Test Performance

Test performance to ensure that repositories are efficient and don't cause performance issues.

### 10. Test Integration

Test integration with other components to ensure that repositories work correctly in the context of the application.
