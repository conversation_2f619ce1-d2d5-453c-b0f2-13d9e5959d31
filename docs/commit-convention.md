# Commit Message Convention

This project follows the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages. This leads to more readable messages that are easy to follow when looking through the project history and enables automatic versioning and changelog generation.

## Commit Message Format

Each commit message consists of a **header**, a **body**, and a **footer**. The header has a special format that includes a **type**, a **scope**, and a **subject**:

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

The **header** is mandatory and the **scope** of the header is optional.

### Type

The type must be one of the following:

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **build**: Changes that affect the build system or external dependencies
- **ci**: Changes to our CI configuration files and scripts
- **chore**: Other changes that don't modify src or test files
- **revert**: Reverts a previous commit

### Scope

The scope should be the name of the module affected (as perceived by the person reading the changelog generated from commit messages).

### Subject

The subject contains a succinct description of the change:

- Use the imperative, present tense: "change" not "changed" nor "changes"
- Don't capitalize the first letter
- No dot (.) at the end

### Body

The body should include the motivation for the change and contrast this with previous behavior.

### Footer

The footer should contain any information about **Breaking Changes** and is also the place to reference GitHub issues that this commit **Closes**.

**Breaking Changes** should start with the word `BREAKING CHANGE:` with a space or two newlines. The rest of the commit message is then used for this.

## Examples

### Commit message with a new feature

```
feat(auth): add ability to login with Google

This adds the ability for users to login with their Google account.
The Google OAuth2 flow is implemented using the Google API client library.

Closes #123
```

### Commit message with a bug fix

```
fix(contacts): prevent duplicate contacts from being created

Previously, the system would allow duplicate contacts to be created if they had the same email address.
Now, the system checks for existing contacts with the same email address before creating a new one.

Closes #456
```

### Commit message with breaking changes

```
feat(api): change authentication endpoint

BREAKING CHANGE: The authentication endpoint has been changed from /auth to /api/auth.
All clients need to be updated to use the new endpoint.

Closes #789
```

## Automatic Versioning

This project uses [semantic-release](https://github.com/semantic-release/semantic-release) to automatically determine the next version number based on the commit messages since the last release:

- **patch** version is incremented for `fix`, `perf`, `docs`, `style`, `refactor`, `test`, `build`, `ci`, and `chore` commits
- **minor** version is incremented for `feat` commits
- **major** version is incremented for commits with `BREAKING CHANGE` in the footer or commits with `breaking` scope

## Tools

To help with writing conventional commit messages, you can use the following tools:

- [commitizen](https://github.com/commitizen/cz-cli): A command line utility that helps you write conventional commit messages
- [commitlint](https://github.com/conventional-changelog/commitlint): A tool that lints your commit messages to ensure they follow the conventional commit format

## References

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Angular Commit Message Guidelines](https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit)
- [Semantic Versioning](https://semver.org/)
