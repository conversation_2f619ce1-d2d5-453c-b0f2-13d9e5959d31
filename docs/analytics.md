# Analytics

This document outlines the analytics system used in the Aizako CRM project.

## Overview

The analytics system allows you to track user behavior and application usage. This is useful for:

- Understanding how users interact with the application
- Identifying popular features and pain points
- Measuring conversion rates and engagement
- Making data-driven decisions

## Architecture

The analytics system consists of the following components:

1. **MongoDB Model**: Stores analytics events
2. **Analytics Service**: Core logic for tracking and querying events
3. **API Routes**: Endpoints for tracking events and retrieving analytics data
4. **React Hooks**: Client-side hooks for tracking events in components
5. **Admin UI**: Interface for viewing analytics data and reports
6. **Type System**: Shared types, type guards, and Zod schemas for type safety and validation

### Type System Integration

The analytics system is fully integrated with the centralized type system:

- **Shared Types**: Defined in `shared/types/analytics.ts`
- **Type Guards**: Defined in `shared/types/guards/analytics.ts`
- **<PERSON><PERSON>**: Defined in `shared/schemas/analytics.ts`
- **Mapping Functions**: Defined in `server/models/mongoose/analytics-event-model.ts`

This integration ensures type safety and validation throughout the analytics system. For more details, see the [Type System Guide](./type-system.md).

## Analytics Event Model

Each analytics event has the following properties:

- `userId`: User who performed the action (optional)
- `tenantId`: Tenant context for the action (optional)
- `sessionId`: Unique identifier for the user session
- `eventType`: Category of the event (e.g., `page`, `user`, `feature`)
- `eventName`: Specific action (e.g., `view`, `signup`, `click`)
- `properties`: Additional data about the event
- `context`: Information about the environment (page, device, location, etc.)
- `timestamp`: When the event occurred

## Using Analytics

### Client-Side

#### Basic Tracking

Use the `useAnalytics` hook to track events:

```tsx
import { useAnalytics } from '@/hooks/use-analytics';

function MyComponent() {
  const { track } = useAnalytics();

  const handleClick = () => {
    track('button', 'click', {
      buttonId: 'signup',
      location: 'header',
    });

    // Perform action
  };

  return (
    <button onClick={handleClick}>
      Sign Up
    </button>
  );
}
```

#### Form Analytics

Use the `useFormAnalytics` hook to track form interactions:

```tsx
import { useFormAnalytics } from '@/hooks/use-analytics';

function SignupForm() {
  const { trackSubmit, trackError, trackFieldChange } = useFormAnalytics('signup', 'Signup Form');

  const handleSubmit = (values) => {
    // Track form submission
    trackSubmit(values);

    // Submit form
    submitForm(values)
      .catch(errors => {
        // Track form errors
        trackError(errors);
      });
  };

  const handleFieldChange = (field, value) => {
    // Track field change
    trackFieldChange(field, value);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
}
```

#### Feature Analytics

Use the `useFeatureAnalytics` hook to track feature usage:

```tsx
import { useFeatureAnalytics } from '@/hooks/use-analytics';

function AdvancedSearch() {
  const { trackUse, trackImpression } = useFeatureAnalytics('advanced-search', 'Advanced Search');

  // Track impression when component mounts
  useEffect(() => {
    trackImpression();
  }, [trackImpression]);

  const handleSearch = (query) => {
    // Track feature use
    trackUse('search', {
      query,
      filters: activeFilters,
    });

    // Perform search
  };

  return (
    <div>
      {/* Search UI */}
    </div>
  );
}
```

#### Engagement Analytics

Use the `useEngagementAnalytics` hook to track user engagement:

```tsx
import { useEngagementAnalytics } from '@/hooks/use-analytics';

function ContentPage() {
  // This will automatically track:
  // - Time spent on page
  // - Scroll depth
  // - Page visibility changes
  useEngagementAnalytics();

  return (
    <div>
      {/* Page content */}
    </div>
  );
}
```

### Server-Side

Use the `AnalyticsService` to track events from the server:

```typescript
import { AnalyticsService } from '../services/analytics-service';

async function handleUserSignup(req, res) {
  try {
    // Create user
    const user = await createUser(req.body);

    // Track signup event
    await AnalyticsService.trackEventFromRequest(
      req,
      'user',
      'signup',
      {
        userId: user.id,
        plan: user.plan,
        referrer: req.body.referrer,
      }
    );

    res.json({ success: true, user });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
}
```

## Event Types and Names

To ensure consistent analytics data, use the following event types and names:

### Page Events

- `page:view`: User viewed a page
- `page:exit`: User left a page
- `page:error`: Page encountered an error

### User Events

- `user:signup`: User created an account
- `user:login`: User logged in
- `user:logout`: User logged out
- `user:update`: User updated their profile
- `user:delete`: User deleted their account

### Feature Events

- `feature:impression`: Feature was shown to the user
- `feature:use`: User used a feature
- `feature:complete`: User completed a feature flow
- `feature:abandon`: User abandoned a feature flow

### Form Events

- `form:submit`: User submitted a form
- `form:error`: Form validation failed
- `form:field_change`: User changed a form field

### Engagement Events

- `engagement:time_spent`: Time spent on a page
- `engagement:scroll_depth`: How far the user scrolled
- `engagement:click`: User clicked on an element
- `engagement:hover`: User hovered over an element

### Custom Events

For custom events, use a descriptive type and name:

- `email:open`: User opened an email
- `email:click`: User clicked a link in an email
- `notification:view`: User viewed a notification
- `notification:click`: User clicked a notification

## Analytics API

The following API endpoints are available for analytics:

- `POST /api/analytics/events`: Track an analytics event
- `GET /api/analytics/events`: Get analytics events (admin only)
- `GET /api/analytics/counts`: Get event counts (admin only)
- `GET /api/analytics/sessions`: Get user sessions (admin only)
- `POST /api/analytics/funnel`: Get funnel analysis (admin only)
- `GET /api/analytics/user`: Get current user's analytics

## Analytics Dashboard

The analytics dashboard provides visualizations and reports for the analytics data. It includes:

1. **Overview**: High-level metrics and trends
2. **User Activity**: User engagement and retention
3. **Feature Usage**: Which features are most popular
4. **Funnels**: Conversion rates for key flows
5. **Sessions**: User session details
6. **Events**: Raw event data

To access the analytics dashboard, navigate to `/admin/analytics` (requires admin privileges).

## Best Practices

### Event Naming

1. **Consistency**: Use consistent naming conventions
2. **Specificity**: Be specific about what the event represents
3. **Hierarchy**: Use types to group related events
4. **Clarity**: Use names that clearly describe the action

### Properties

1. **Relevance**: Include only relevant properties
2. **Consistency**: Use consistent property names
3. **Sanitization**: Don't include sensitive information
4. **Size**: Keep property values small

### Performance

1. **Batching**: Batch events when possible
2. **Async**: Track events asynchronously
3. **Throttling**: Throttle high-frequency events
4. **Fallbacks**: Handle tracking failures gracefully

## Examples

### Tracking Page Views

```typescript
// Automatically tracked by useAnalytics hook
useAnalytics();

// Manual tracking
track('page', 'view', {
  path: '/dashboard',
  title: 'Dashboard',
  referrer: document.referrer,
});
```

### Tracking User Actions

```typescript
// Track user login
track('user', 'login', {
  method: 'email',
  rememberMe: true,
});

// Track feature usage
track('feature', 'use', {
  featureId: 'export-data',
  format: 'csv',
  recordCount: 250,
});
```

### Tracking Conversion Funnel

```typescript
// Step 1: View signup page
track('funnel', 'signup_view', {
  step: 1,
  source: 'homepage_cta',
});

// Step 2: Start signup form
track('funnel', 'signup_start', {
  step: 2,
});

// Step 3: Submit signup form
track('funnel', 'signup_submit', {
  step: 3,
  plan: 'pro',
});

// Step 4: Complete onboarding
track('funnel', 'onboarding_complete', {
  step: 4,
  duration: 120, // seconds
});
```

### Analyzing Funnel Data

```typescript
// Server-side funnel analysis
const funnelResults = await AnalyticsService.getFunnelAnalysis({
  steps: [
    { eventType: 'funnel', eventName: 'signup_view' },
    { eventType: 'funnel', eventName: 'signup_start' },
    { eventType: 'funnel', eventName: 'signup_submit' },
    { eventType: 'funnel', eventName: 'onboarding_complete' },
  ],
  startDate: new Date('2023-01-01'),
  endDate: new Date('2023-01-31'),
  windowHours: 24, // Complete funnel within 24 hours
});
```
