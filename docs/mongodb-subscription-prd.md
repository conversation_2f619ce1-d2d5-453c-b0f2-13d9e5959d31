# Aizako CRM Subscription System - MongoDB Implementation PRD

## 1. Overview

The Aizako CRM Subscription System provides a flexible, centralized way to manage subscription plans, features, and resource limits across all modules of the application. This document outlines the requirements and implementation guidelines for integrating the MongoDB-based subscription system into each module.

## 2. Core Principles

1. **Centralized Definition**: All subscription plans, features, and limits are defined in a central location
2. **Distributed Enforcement**: Each module is responsible for enforcing its own features and limits
3. **Real-time Propagation**: Changes to subscription plans are immediately propagated to all modules
4. **Feature Flagging**: Individual features can be enabled/disabled per subscription plan
5. **Usage Tracking**: Resource usage is tracked against subscription limits

## 3. System Architecture

### 3.1 Data Model

The subscription system uses the following MongoDB collections:

- **SubscriptionPlans**: Defines subscription plans with features, limits, and pricing
- **Features**: Defines features that can be enabled/disabled in subscription plans
- **Tenants**: Represents organizations in the system
- **TenantSubscriptions**: Links tenants to subscription plans
- **TenantUsages**: Tracks resource usage for tenants
- **UserTenants**: Links users to tenants with specific roles
- **SubscriptionEvents**: Records subscription-related events for auditing and synchronization

### 3.2 Mongoose Schemas

```typescript
// Subscription Plan Schema
const SubscriptionPlanSchema = new Schema<ISubscriptionPlan>({
  name: { type: String, required: true },
  description: { type: String },
  status: { type: String, enum: ['active', 'inactive', 'archived'], default: 'active' },
  isDefault: { type: Boolean, default: false },
  sortOrder: { type: Number, default: 0 },
  price: { type: Number, required: true },
  currency: { type: String, default: 'USD' },
  billingPeriod: { type: String, enum: ['monthly', 'yearly', 'custom'], default: 'monthly' },
  trialDays: { type: Number, default: 0 },
  limits: { type: Schema.Types.Mixed, required: true },
  features: { type: Schema.Types.Mixed, required: true },
  moduleSettings: { type: Schema.Types.Mixed, default: {} },
}, { timestamps: true });

// Feature Schema
const FeatureSchema = new Schema<IFeature>({
  key: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String },
  category: { type: String, required: true },
  module: { type: String, required: true },
  defaultValue: { type: Boolean, default: false },
  requiresRestart: { type: Boolean, default: false },
  uiComponent: { type: String },
  dependencies: { type: Schema.Types.Mixed },
}, { timestamps: true });

// Tenant Subscription Schema
const TenantSubscriptionSchema = new Schema<ITenantSubscription>({
  tenantId: { type: String, required: true },
  planId: { type: Schema.Types.ObjectId, ref: 'SubscriptionPlan', required: true },
  status: { type: String, enum: ['active', 'trialing', 'past_due', 'canceled', 'expired'], default: 'active' },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  trialEndsAt: { type: Date },
  customLimits: { type: Schema.Types.Mixed },
  customFeatures: { type: Schema.Types.Mixed },
  billingDetails: { type: Schema.Types.Mixed },
  canceledAt: { type: Date },
}, { timestamps: true });
```

### 3.3 Component Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                     Subscription Service                     │
├─────────────────────────────────────────────────────────────┤
│ - Manages subscription plans, features, and limits          │
│ - Handles tenant subscriptions                              │
│ - Tracks resource usage                                     │
│ - Publishes subscription events                             │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                     Event Distribution                       │
├─────────────────────────────────────────────────────────────┤
│ - Propagates subscription changes to all modules            │
│ - Provides real-time updates                                │
└───┬───────────────────┬───────────────────┬────────────────┘
    │                   │                   │
    ▼                   ▼                   ▼
┌──────────┐      ┌──────────┐      ┌──────────┐
│ Module A │      │ Module B │      │ Module C │
└──────────┘      └──────────┘      └──────────┘
```

## 4. Module Integration Requirements

Each module must implement the following to integrate with the subscription system:

### 4.1 MongoDB Connection

Each module should use the shared MongoDB connection:

```typescript
// Example MongoDB connection setup
import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/aizako-crm';

let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function connectToDatabase() {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    };

    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose;
    });
  }
  cached.conn = await cached.promise;
  return cached.conn;
}

export default connectToDatabase;
```

### 4.2 Subscription Client

Each module must implement a client to communicate with the subscription service:

```typescript
// Example for JavaScript/TypeScript modules
class SubscriptionClient {
  constructor() {
    this.cache = {};
    this.cacheTimestamp = null;
    this.refreshCache();
  }

  async refreshCache() {
    try {
      // Get subscription plans
      const plans = await SubscriptionPlan.find().sort({ sortOrder: 1 });
      
      // Get features
      const features = await Feature.find();
      
      // Get tenant subscriptions
      const tenantSubscriptions = await TenantSubscription.find();
      
      this.cache = {
        plans,
        features,
        tenantSubscriptions
      };
      
      this.cacheTimestamp = Date.now();
    } catch (error) {
      console.error('Error refreshing subscription cache:', error);
    }
  }

  async checkEntitlement(tenantId, featureKey) {
    // Check if cache is stale (older than 5 minutes)
    if (!this.cacheTimestamp || (Date.now() - this.cacheTimestamp) > 300000) {
      await this.refreshCache();
    }
    
    // Find the tenant subscription
    const tenantSubscription = this.cache.tenantSubscriptions.find(
      sub => sub.tenantId === tenantId
    );
    
    if (!tenantSubscription) {
      return false;
    }
    
    // Check if subscription is active
    if (tenantSubscription.status !== 'active' && tenantSubscription.status !== 'trialing') {
      return false;
    }
    
    // Check if subscription is expired
    if (new Date(tenantSubscription.endDate) < new Date()) {
      return false;
    }
    
    // Check custom features first
    if (tenantSubscription.customFeatures && featureKey in tenantSubscription.customFeatures) {
      return tenantSubscription.customFeatures[featureKey];
    }
    
    // Find the subscription plan
    const plan = this.cache.plans.find(
      plan => plan._id.toString() === tenantSubscription.planId.toString()
    );
    
    if (!plan) {
      return false;
    }
    
    // Check plan features
    return plan.features[featureKey] || false;
  }

  // Other methods...
}
```

### 4.3 Event Listeners

Each module must implement event listeners to receive subscription change events:

```typescript
// Example for JavaScript/TypeScript modules using MongoDB change streams
async function setupSubscriptionEventListeners() {
  try {
    const db = mongoose.connection.db;
    
    // Watch for changes to subscription plans
    const planChangeStream = SubscriptionPlan.watch();
    planChangeStream.on('change', async (change) => {
      console.log('Subscription plan changed:', change);
      await subscriptionClient.refreshCache();
    });
    
    // Watch for changes to tenant subscriptions
    const subscriptionChangeStream = TenantSubscription.watch();
    subscriptionChangeStream.on('change', async (change) => {
      console.log('Tenant subscription changed:', change);
      await subscriptionClient.refreshCache();
    });
    
    // Watch for changes to features
    const featureChangeStream = Feature.watch();
    featureChangeStream.on('change', async (change) => {
      console.log('Feature changed:', change);
      await subscriptionClient.refreshCache();
    });
    
    console.log('Subscription event listeners set up successfully');
  } catch (error) {
    console.error('Error setting up subscription event listeners:', error);
  }
}
```

### 4.4 Feature Registration

Each module must register its features with the subscription system:

```typescript
// Example for JavaScript/TypeScript modules
async function registerFeatures() {
  const features = [
    {
      key: 'module.feature1',
      name: 'Feature 1',
      description: 'Description of Feature 1',
      category: 'category',
      module: 'module-name',
      defaultValue: false
    },
    // Other features...
  ];

  for (const feature of features) {
    try {
      // Check if feature already exists
      const existingFeature = await Feature.findOne({ key: feature.key });
      
      if (!existingFeature) {
        // Create new feature
        await Feature.create(feature);
        console.log(`Feature ${feature.key} registered successfully`);
      } else {
        console.log(`Feature ${feature.key} already exists`);
      }
    } catch (error) {
      console.error(`Error registering feature ${feature.key}:`, error);
    }
  }
}
```

### 4.5 Feature Enforcement

Each module must enforce feature entitlements:

```typescript
// Example for JavaScript/TypeScript modules
async function handleRequest(req, res, next) {
  const tenantId = req.headers['x-tenant-id'];
  const featureKey = 'module.feature1';
  
  if (!tenantId) {
    return res.status(401).json({ message: 'Tenant ID is required' });
  }
  
  const isEnabled = await subscriptionClient.checkEntitlement(tenantId, featureKey);
  
  if (!isEnabled) {
    return res.status(403).json({ 
      message: `Feature '${featureKey}' is not available in your subscription plan`
    });
  }
  
  // Record feature usage
  await recordFeatureUsage(tenantId, featureKey);
  
  // Continue with the request
  next();
}

async function recordFeatureUsage(tenantId, featureKey, amount = 1) {
  try {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    
    // Get current usage for this period
    let usage = await TenantUsage.findOne({ tenantId, period });
    
    if (!usage) {
      // Create new usage record if none exists
      usage = await TenantUsage.create({
        tenantId,
        period,
        usage: {},
        featureUsage: { [featureKey]: amount }
      });
    } else {
      // Update existing usage record
      const currentFeatureUsage = usage.featureUsage || {};
      const updatedFeatureUsage = {
        ...currentFeatureUsage,
        [featureKey]: (currentFeatureUsage[featureKey] || 0) + amount
      };
      
      await TenantUsage.updateOne(
        { _id: usage._id },
        { $set: { featureUsage: updatedFeatureUsage, updatedAt: new Date() } }
      );
    }
  } catch (error) {
    console.error(`Error recording feature usage for ${featureKey}:`, error);
  }
}
```

### 4.6 Resource Tracking

Each module must track resource usage:

```typescript
// Example for JavaScript/TypeScript modules
async function trackResourceUsage(tenantId, resourceType, amount) {
  try {
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    
    // Get current usage for this period
    let usage = await TenantUsage.findOne({ tenantId, period });
    
    if (!usage) {
      // Create new usage record if none exists
      usage = await TenantUsage.create({
        tenantId,
        period,
        usage: { [resourceType]: amount },
        featureUsage: {}
      });
    } else {
      // Update existing usage record
      const currentUsage = usage.usage || {};
      const updatedUsage = {
        ...currentUsage,
        [resourceType]: (currentUsage[resourceType] || 0) + amount
      };
      
      await TenantUsage.updateOne(
        { _id: usage._id },
        { $set: { usage: updatedUsage, updatedAt: new Date() } }
      );
    }
  } catch (error) {
    console.error(`Error tracking resource usage for ${resourceType}:`, error);
  }
}

// Example usage
async function createDocument(tenantId, document) {
  // Check if within storage limits
  const documentSize = calculateDocumentSize(document); // in MB
  const isWithinLimit = await checkResourceLimit(tenantId, 'storage', documentSize);
  
  if (!isWithinLimit) {
    throw new Error('Storage limit exceeded');
  }
  
  // Create the document
  const result = await documentService.createDocument(document);
  
  // Record usage
  await trackResourceUsage(tenantId, 'storage', documentSize);
  
  return result;
}

async function checkResourceLimit(tenantId, resourceType, additionalAmount = 0) {
  try {
    // Get tenant subscription
    const subscription = await TenantSubscription.findOne({ tenantId });
    
    if (!subscription) {
      return false;
    }
    
    // Check if subscription is active
    if (subscription.status !== 'active' && subscription.status !== 'trialing') {
      return false;
    }
    
    // Check if subscription is expired
    if (new Date(subscription.endDate) < new Date()) {
      return false;
    }
    
    // Get current usage
    const currentDate = new Date();
    const period = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    const usage = await TenantUsage.findOne({ tenantId, period });
    
    const currentUsage = usage?.usage?.[resourceType] || 0;
    const totalAmount = currentUsage + additionalAmount;
    
    // Check custom limits first
    if (subscription.customLimits && resourceType in subscription.customLimits) {
      return totalAmount <= subscription.customLimits[resourceType];
    }
    
    // Get subscription plan
    const plan = await SubscriptionPlan.findById(subscription.planId);
    
    if (!plan) {
      return false;
    }
    
    // Check plan limits
    return totalAmount <= (plan.limits[resourceType] || 0);
  } catch (error) {
    console.error(`Error checking resource limit for ${resourceType}:`, error);
    return false;
  }
}
```

## 5. Subscription Change Propagation

### 5.1 MongoDB Change Streams

MongoDB change streams provide a way to watch for changes to collections in real-time:

```typescript
// Example change stream setup
const changeStream = SubscriptionPlan.watch();

changeStream.on('change', (change) => {
  console.log('Change detected:', change);
  
  // Handle different types of changes
  if (change.operationType === 'insert') {
    handlePlanCreated(change.fullDocument);
  } else if (change.operationType === 'update') {
    handlePlanUpdated(change.documentKey._id);
  } else if (change.operationType === 'delete') {
    handlePlanDeleted(change.documentKey._id);
  }
});

async function handlePlanUpdated(planId) {
  // Refresh the plan in the cache
  const plan = await SubscriptionPlan.findById(planId);
  // Update cache and notify components
}
```

### 5.2 Event Distribution Methods

In addition to MongoDB change streams, the subscription system can use other methods for distributing events:

#### 5.2.1 Server-Sent Events (SSE)

```typescript
// Server implementation
app.get('/api/subscription/events', (req, res) => {
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  
  // Send initial data
  res.write(`data: ${JSON.stringify({ type: 'connected' })}\n\n`);
  
  // Add this client to the list of connected clients
  const clientId = Date.now();
  clients.set(clientId, res);
  
  // Remove client when connection is closed
  req.on('close', () => {
    clients.delete(clientId);
  });
});

// Function to publish events to all connected clients
function publishEvent(type, data) {
  const event = `event: ${type}\ndata: ${JSON.stringify(data)}\n\n`;
  for (const client of clients.values()) {
    client.write(event);
  }
}

// Publish events when subscription data changes
SubscriptionPlan.watch().on('change', (change) => {
  if (change.operationType === 'update') {
    SubscriptionPlan.findById(change.documentKey._id).then((plan) => {
      publishEvent('subscription.plan.updated', plan);
    });
  }
});
```

#### 5.2.2 WebSockets

```typescript
// Server implementation
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
  // Send initial data
  ws.send(JSON.stringify({ type: 'connected' }));
  
  // Add this client to the list of connected clients
  const clientId = Date.now();
  clients.set(clientId, ws);
  
  // Remove client when connection is closed
  ws.on('close', () => {
    clients.delete(clientId);
  });
});

// Function to publish events to all connected clients
function publishEvent(type, data) {
  const event = JSON.stringify({ type, data });
  for (const client of clients.values()) {
    if (client.readyState === WebSocket.OPEN) {
      client.send(event);
    }
  }
}

// Publish events when subscription data changes
SubscriptionPlan.watch().on('change', (change) => {
  if (change.operationType === 'update') {
    SubscriptionPlan.findById(change.documentKey._id).then((plan) => {
      publishEvent('subscription.plan.updated', plan);
    });
  }
});
```

## 6. Module-Specific Implementation Guidelines

### 6.1 Core Backend Module

1. **Features to Register**:
   - `core.contacts`
   - `core.companies`
   - `core.opportunities`
   - `core.activities`
   - `core.relationships`

2. **Resources to Track**:
   - `users`
   - `contacts`
   - `companies`
   - `opportunities`
   - `storage`

3. **Implementation Points**:
   - Add middleware to check feature entitlements for each API endpoint
   - Add middleware to check resource limits for creation endpoints
   - Record resource usage after successful creation operations
   - Listen for subscription events using MongoDB change streams

### 6.2 AI Service Module

1. **Features to Register**:
   - `ai.assistant.basic`
   - `ai.assistant.advanced`
   - `ai.insights`
   - `ai.document`

2. **Resources to Track**:
   - `ai.tokens`

3. **Implementation Points**:
   - Check feature entitlements before processing AI requests
   - Track token usage for each AI request
   - Enforce different capabilities based on subscription plan
   - Listen for subscription events using MongoDB change streams

### 6.3 Frontend Module

1. **Implementation Points**:
   - Use the `useSubscription` hook to access subscription data
   - Use the `FeatureGate` component to conditionally render features
   - Display usage information using the `UsageDisplay` component
   - Allow users to select plans using the `PlanSelector` component
   - Listen for subscription events using SSE or WebSockets

## 7. Testing Requirements

Each module must include tests for subscription integration:

1. **Unit Tests**:
   - Test feature entitlement checks
   - Test resource limit checks
   - Test usage tracking

2. **Integration Tests**:
   - Test subscription event handling
   - Test cache invalidation
   - Test subscription changes propagation

3. **End-to-End Tests**:
   - Test the complete subscription flow across modules
   - Test upgrading and downgrading subscription plans
   - Test feature access based on subscription plan

## 8. Deployment Considerations

1. **MongoDB Configuration**:
   - Set up MongoDB Atlas or a self-hosted MongoDB instance
   - Configure connection string in environment variables
   - Set up proper authentication and access controls

2. **Environment Configuration**:
   ```
   # MongoDB Configuration
   MONGODB_URI=mongodb+srv://username:<EMAIL>/aizako-crm?retryWrites=true&w=majority
   MONGODB_ENABLED=true # Set to true to use MongoDB, false to use PostgreSQL
   ```

3. **Monitoring**:
   - Set up MongoDB Atlas monitoring
   - Monitor database performance and usage
   - Set up alerts for subscription-related errors

## 9. Implementation Timeline

1. **Phase 1: MongoDB Setup** (Week 1)
   - Set up MongoDB Atlas or self-hosted MongoDB
   - Create Mongoose schemas for subscription data
   - Implement MongoDB storage adapter

2. **Phase 2: Module Integration** (Week 2-3)
   - Integrate with Core Backend Module
   - Integrate with AI Service Module
   - Integrate with Frontend Module

3. **Phase 3: Testing and Refinement** (Week 4)
   - Conduct unit, integration, and end-to-end tests
   - Refine implementation based on test results
   - Document the subscription system

## 10. Appendix

### 10.1 MongoDB Schema Examples

#### Subscription Plan Document Example

```json
{
  "_id": "60f1a5b3e6b3f32d4c8b4567",
  "name": "Professional",
  "description": "Advanced CRM features for growing businesses",
  "status": "active",
  "isDefault": false,
  "sortOrder": 3,
  "price": 49,
  "currency": "USD",
  "billingPeriod": "monthly",
  "trialDays": 14,
  "limits": {
    "users": 20,
    "contacts": 10000,
    "companies": 1000,
    "opportunities": 500,
    "storage": 10000,
    "apiRequests": 10000
  },
  "features": {
    "core.contacts": true,
    "core.companies": true,
    "core.opportunities": true,
    "core.activities": true,
    "core.relationships": true,
    "ai.assistant.basic": true,
    "ai.assistant.advanced": true,
    "ai.insights": true,
    "ai.document": true,
    "api.access": true
  },
  "moduleSettings": {},
  "createdAt": "2023-07-16T12:00:00.000Z",
  "updatedAt": "2023-07-16T12:00:00.000Z"
}
```

#### Feature Document Example

```json
{
  "_id": "60f1a5b3e6b3f32d4c8b4568",
  "key": "ai.insights",
  "name": "AI Insights",
  "description": "AI-generated insights from your CRM data",
  "category": "ai",
  "module": "ai",
  "defaultValue": false,
  "requiresRestart": false,
  "createdAt": "2023-07-16T12:00:00.000Z",
  "updatedAt": "2023-07-16T12:00:00.000Z"
}
```

#### Tenant Subscription Document Example

```json
{
  "_id": "60f1a5b3e6b3f32d4c8b4569",
  "tenantId": "1",
  "planId": "60f1a5b3e6b3f32d4c8b4567",
  "status": "active",
  "startDate": "2023-07-16T12:00:00.000Z",
  "endDate": "2024-07-16T12:00:00.000Z",
  "trialEndsAt": "2023-07-30T12:00:00.000Z",
  "customLimits": {
    "users": 25,
    "storage": 15000
  },
  "customFeatures": {
    "ai.document": true
  },
  "createdAt": "2023-07-16T12:00:00.000Z",
  "updatedAt": "2023-07-16T12:00:00.000Z"
}
```

### 10.2 MongoDB Indexes

To optimize query performance, create the following indexes:

```javascript
// Subscription Plan indexes
db.subscriptionPlans.createIndex({ "status": 1 });
db.subscriptionPlans.createIndex({ "isDefault": 1 });

// Feature indexes
db.features.createIndex({ "key": 1 }, { unique: true });
db.features.createIndex({ "module": 1 });

// Tenant Subscription indexes
db.tenantSubscriptions.createIndex({ "tenantId": 1 });
db.tenantSubscriptions.createIndex({ "planId": 1 });
db.tenantSubscriptions.createIndex({ "status": 1 });

// Tenant Usage indexes
db.tenantUsages.createIndex({ "tenantId": 1, "period": 1 }, { unique: true });

// User Tenant indexes
db.userTenants.createIndex({ "userId": 1 });
db.userTenants.createIndex({ "tenantId": 1 });
```

### 10.3 MongoDB Change Stream Example

```javascript
// Watch for changes to subscription plans
const planChangeStream = db.collection('subscriptionPlans').watch();

planChangeStream.on('change', (change) => {
  console.log('Subscription plan changed:', change);
  
  if (change.operationType === 'insert') {
    console.log('New plan created:', change.fullDocument);
  } else if (change.operationType === 'update') {
    console.log('Plan updated:', change.documentKey._id);
  } else if (change.operationType === 'delete') {
    console.log('Plan deleted:', change.documentKey._id);
  }
});
```
