# Analytics & Reporting Module Documentation

## Overview

The Analytics & Reporting module provides advanced data analysis capabilities through two main components:

1. **Conversational BI (C-BI)** — Natural language interface for querying data and generating insights
2. **Attribution AI (A-AI)** — Marketing attribution modeling and budget optimization

## Architecture

The module follows a layered architecture:

```
Client UI → API Routes → Services → Models → Data Sources (MongoDB, BigQuery, Neo4j)
```

### Data Flow

1. Events are collected through the Events Collection API
2. Data is stored in MongoDB and/or BigQuery (tenant-specific datasets)
3. Analytics queries are processed through the Conversational BI or Attribution services
4. Results are cached in MongoDB for performance

## Multi-Tenant Security

### Tenant Isolation

All data in the Analytics & Reporting module is strictly isolated by tenant. This is enforced at multiple levels:

#### 1. MongoDB Level

- All MongoDB models include a required `tenant_id` field
- All queries include tenant filtering
- Compound indexes include tenant_id for efficient querying
- All API routes validate tenant_id from the session or request

#### 2. BigQuery Level

- Each tenant has a dedicated dataset in BigQuery with naming convention `aizako_analytics_{tenant_id}`
- Dataset access is controlled through IAM permissions
- All queries include a tenant filter clause
- Query results are validated against the requesting tenant

#### 3. Application Level

- All service methods require and validate tenant_id
- Session management ensures users can only access their tenant's data
- Caching is tenant-specific with the tenant_id included in cache keys

### Security Measures

1. **Query Validation**: All natural language queries are validated and transformed into structured queries with tenant filters.

2. **Data Masking**: Sensitive data is masked in analytics results according to tenant-specific policies.

3. **Audit Logging**: All analytics queries are logged with tenant information for audit purposes.

4. **Access Control**: Fine-grained access control within a tenant determines which users can access which analytics features.

## BigQuery Integration

### Dataset Isolation

For BigQuery integration, we enforce tenant isolation through:

1. **Dedicated Datasets**: Each tenant has a dedicated BigQuery dataset.

```sql
-- Example of dataset creation with tenant isolation
CREATE SCHEMA IF NOT EXISTS `aizako_analytics_{tenant_id}`
OPTIONS(
  location="US"
);
```

2. **Table Partitioning**: Tables are partitioned by date for performance and easier data management.

3. **Row-Level Security**: All queries include a tenant filter.

```sql
-- Example of a query with tenant isolation
SELECT * FROM `aizako_analytics_{tenant_id}.events` 
WHERE tenant_id = '{tenant_id}'
AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
```

4. **Service Account Isolation**: For enterprise tenants, dedicated service accounts can be created.

### Data Synchronization

Data is synchronized between MongoDB and BigQuery through:

1. **Batch ETL**: Daily batch processing for historical data
2. **Streaming API**: Real-time event processing for critical events
3. **Change Data Capture**: MongoDB change streams for incremental updates

## API Security

All API endpoints enforce tenant isolation:

```typescript
// Example of tenant validation in API routes
router.get('/results',
  authenticateUser,
  checkFeatureEntitlement('analytics.attribution'),
  validateQuery(...),
  async (req: Request, res: Response) => {
    try {
      const tenantId = req.session?.tenantId;
      
      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: 'Tenant ID is required'
        });
      }
      
      // Proceed with tenant-specific data
    } catch (error) {
      // Error handling
    }
  }
);
```

## Implementation Guidelines

### Adding New Analytics Features

When adding new analytics features:

1. Always include tenant_id in all database models and queries
2. Validate tenant_id in all service methods
3. Use the tenant isolation patterns in the existing codebase
4. Add appropriate tests for tenant isolation

### BigQuery Best Practices

1. Always use parameterized queries to prevent SQL injection
2. Include tenant_id in all queries
3. Use the tenant-specific dataset
4. Implement appropriate error handling for BigQuery errors

## Testing Multi-Tenant Isolation

The module includes tests specifically for verifying tenant isolation:

1. **Unit Tests**: Verify that services properly filter by tenant_id
2. **Integration Tests**: Ensure that data from one tenant cannot be accessed by another
3. **Security Tests**: Attempt to bypass tenant isolation through various attack vectors

## Monitoring and Auditing

1. **Query Logging**: All analytics queries are logged with tenant information
2. **Usage Metrics**: Track usage by tenant for billing and capacity planning
3. **Error Monitoring**: Alert on potential tenant isolation issues
4. **Audit Reports**: Regular audit reports on cross-tenant access attempts

## Disaster Recovery

In case of a tenant isolation breach:

1. Immediately revoke access to the affected services
2. Investigate the root cause
3. Restore from clean backups if necessary
4. Implement additional security measures

## Compliance

The Analytics & Reporting module is designed to comply with:

1. GDPR
2. CCPA
3. SOC 2
4. HIPAA (for healthcare tenants)

Each tenant's data is processed according to their specific compliance requirements.
